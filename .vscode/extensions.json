{
  // See https://go.microsoft.com/fwlink/?LinkId=827846
  // for the documentation about the extensions.json format
  "recommendations": [
    "dart-code.dart-code",
    "dart-code.flutter",
    "felixangelov.bloc",
    "usernamehw.errorlens",
    "redhat.vscode-xml",
    "oderwat.indent-rainbow",
    "zainchen.json",
    "circlecodesolution.ccs-flutter-color",
    "google.arb-editor",
    "localizely.flutter-intl",
    "deblack.flutter-arb-editor",
    "miquelddg.dart-barrel-file-generator",
    "gaetschwartz.build-runner",
    "gmlewis-vscode.flutter-stylizer",
    "donjayamanne.githistory",
    "github.copilot",
    "github.copilot-chat",
    "github.vscode-pull-request-github",
    "redhat.vscode-yaml",
    "davidanson.vscode-markdownlint",
    "jock.svg"
  ]
}
