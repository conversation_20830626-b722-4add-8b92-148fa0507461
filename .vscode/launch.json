{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      // Launch the app using the development flavor.
      "name": "Flutter: Launch Production (Staging API)",
      "request": "launch",
      "type": "dart",
      "program": "lib/main_staging.dart",
      "args": [
        "--flavor",
        "production",
        "--target",
        "lib/main_staging.dart"
      ]
    },
    {
      // Launch the app using the development flavor.
      "name": "Flutter: Launch Development",
      "request": "launch",
      "type": "dart",
      "program": "lib/main_development.dart",
      "args": [
        "--flavor",
        "development",
        "--target",
        "lib/main_development.dart"
      ]
    },
    {
      // Launch the app using the staging flavor.
      "name": "Flutter: Launch Staging",
      "request": "launch",
      "type": "dart",
      "program": "lib/main_staging.dart",
      "args": [
        "--flavor",
        "staging",
        "--target",
        "lib/main_staging.dart"
      ]
    },
    {
      // Launch the app using the production flavor.
      "name": "Flutter: Launch Production",
      "request": "launch",
      "type": "dart",
      "program": "lib/main_production.dart",
      "args": [
        "--flavor",
        "production",
        "--target",
        "lib/main_production.dart"
      ]
    },
    {
      // Launch the production app in profile mode for performance analysis.
      "name": "Flutter: Launch Production (Profile Mode)",
      "request": "launch",
      "type": "dart",
      "program": "lib/main_production.dart",
      "args": [
        "--flavor",
        "production",
        "--target",
        "lib/main_production.dart",
        "--profile"
      ]
    },
    {
      // Launch the production app in profile mode for performance analysis.
      "name": "Flutter: Launch Production (Release Mode)",
      "request": "launch",
      "type": "dart",
      "program": "lib/main_production.dart",
      "args": [
        "--flavor",
        "production",
        "--target",
        "lib/main_production.dart",
        "--release"
      ]
    },
    {
      // Launch the development app in profile mode for performance analysis.
      "name": "Flutter: Launch Development (Profile Mode)",
      "request": "launch",
      "type": "dart",
      "program": "lib/main_development.dart",
      "args": [
        "--flavor",
        "development",
        "--target",
        "lib/main_development.dart",
        "--profile"
      ]
    },
    {
      // Launch the staging app in profile mode for performance analysis.
      "name": "Flutter: Launch Staging (Profile Mode)",
      "request": "launch",
      "type": "dart",
      "program": "lib/main_staging.dart",
      "args": [
        "--flavor",
        "staging",
        "--target",
        "lib/main_staging.dart",
        "--profile"
      ]
    },
    {
      // Preview the PreferredCallingUserView widget using the production flavor.
      "name": "Preview: PreferredCallingUserView (Production Flavor)",
      "request": "launch",
      "type": "dart",
      "program": "test/widget_previews/preferred_calling_user_view_preview.dart",
      "args": [
      "--flavor",
      "production"
      ]
    }
  ]
}