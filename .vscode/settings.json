{"files.exclude": {"**/*.freezed.dart": true, "**/*.g.dart": true, "**/.idea": true, "**/.http": true, "**/.flutter-plugins": true, "**/.flutter-plugins-dependencies": true, "**/.metadata": true, "**/.dart_tool": true, "**/build": true, "**/*.iml": true, "**/android/*.gradle": false, "**/android/*.kotlin": true, "**/android/local.properties": true, "**/android/gradlew*": true, "**/android/gradle/**/*.jar": true, "**/android/**/java": true, "**/ios/Pods": true, "**/ios/.symlinks": true, "**/ios/Flutter": true, "**/ios/RunnerTests": true, "**/ios/Runner.**": false, "**/ios/Runner/*GeneratedPlugin*": true, "**/ios/Runner/*.entitle*": false, "**/ios/Runner/*.h": false}, "editor.fontSize": 14, "editor.fontLigatures": true, "build-runner.commandToUse": "flutter", "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.sortMembers": "never", "source.organizeImports": "explicit"}, "files.autoSave": "off", "github.copilot.chat.scopeSelection": true, "cSpell.words": ["Affliate", "appbar", "callkeep", "dialerservice", "Dtmf", "Favourite", "froggytalk", "numpad", "SIPUA", "Splashscreen", "unawaited", "unfocus", "unmute", "Unmuted", "upgrader"], "IDX.aI.enableInlineCompletion": true, "IDX.aI.enableCodebaseIndexing": true, "github.copilot.chat.startDebugging.enabled": true, "github.copilot.chat.inlineChatCompletionTrigger.enabled": true, "github.copilot.chat.generateTests.codeLens": true, "github.copilot.chat.codeGeneration.useInstructionFiles": true, "workbench.colorTheme": "Default Light Modern", "dartBarrelFileGenerator.excludeFileList": ["**/*_event.dart", "**/*_state.dart"], "dartBarrelFileGenerator.excludeFreezed": true, "dartBarrelFileGenerator.excludeGenerated": true, "yaml.schemaStore.enable": false, "websearch.preferredEngine": "tavily", "workbench.tree.indent": 20, "dart-barrel-file-generator.excludeFreezed": true, "dart-barrel-file-generator.excludeGenerated": true, "dart-barrel-file-generator.excludeFileList": ["**/*_event.dart", "**/*_state.dart"], "github.copilot.chat.commitMessageGeneration.instructions": [{"text": "Always Make the commit messages extremely detailed. Make to state what changed, and what it was before. Add emojis to it."}], "dart.flutterSdkPath": ".fvm/versions/3.29.2"}