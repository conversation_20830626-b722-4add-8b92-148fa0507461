{
	"version": "2.0.0",
	"inputs": [
		{
			"id": "buildName",
			"type": "promptString",
			"description": "Enter the build name (version):",
			"default": "2.13.4"
		},
		{
			"id": "buildNumber",
			"type": "promptString",
			"description": "Enter the build number:",
			"default": "301"
		}
	],
	"tasks": [
		{
			"type": "flutter",
			"command": "flutter",
			"args": [
				"gen-l10n",
				"--arb-dir='lib/l10n/arb'"
			],
			"problemMatcher": [],
			"label": "flutter: flutter gen-l10n",
			"detail": ""
		},
		{
			"label": "Build Staging APK",
			"detail": "This task will build the staging APK",
			"type": "flutter",
			"command": "flutter",
			"args": [
				"build",
				"apk",
				"--flavor",
				"staging",
				"--target",
				"lib/main_staging.dart"
			],
			"problemMatcher": [],
		},
		{
			"label": "Build Production APK",
			"detail": "This task will build the Production APK",
			"type": "flutter",
			"command": "flutter",
			"args": [
				"build",
				"apk",
				"--flavor",
				"production",
				"--target",
				"lib/main_production.dart",
				"--build-name",
				"${input:buildName}",
				"--build-number",
				"${input:buildNumber}"
			],
			"problemMatcher": [],
		},
		{
			"label": "Build Production AppBundle",
			"detail": "This task will build the Production AppBundle",
			"type": "flutter",
			"command": "flutter",
			"args": [
				"build",
				"appbundle",
				"--flavor",
				"production",
				"--target",
				"lib/main_production.dart",
				"--build-name",
				"${input:buildName}",
				"--build-number",
				"${input:buildNumber}"
			],
			"problemMatcher": [],
		},
		{
			"label": "Build Production IPA",
			"detail": "This task will build the Production IPA",
			"type": "flutter",
			"command": "flutter",
			"args": [
				"build",
				"ipa",
				"--flavor",
				"production",
				"--target",
				"lib/main_production.dart",
				"--build-name",
				"${input:buildName}",
				"--build-number",
				"${input:buildNumber}"
			],
			"problemMatcher": [],
		},
		{
			"type": "dart",
			"command": "dart",
			"args": [
				"run",
				"${cwd}/svg2binary.dart"
			],
			"problemMatcher": [],
			"label": "flutter: convert svg to binary code",
			"detail": ""
		},
		{
			"label": "Run Production in Profile Mode",
			"detail": "Run the production app in profile mode",
			"type": "flutter",
			"command": "flutter",
			"args": [
				"run",
				"--profile",
				"--flavor",
				"production",
				"--target",
				"lib/main_production.dart"
			],
			"problemMatcher": []
		},
		{
			"label": "Run Development in Profile Mode",
			"detail": "Run the development app in profile mode",
			"type": "flutter",
			"command": "flutter",
			"args": [
				"run",
				"--profile",
				"--flavor",
				"development",
				"--target",
				"lib/main_development.dart"
			],
			"problemMatcher": []
		}
	]
}