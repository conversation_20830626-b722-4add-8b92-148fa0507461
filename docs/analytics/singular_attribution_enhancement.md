# Singular Revenue Attribution - Enhanced Implementation

## Overview

This document outlines the enhanced Singular revenue tracking implementation that provides superior attribution in the Singular dashboard. Based on the latest Singular SDK documentation and best practices, this implementation ensures maximum attribution accuracy for revenue events.

## Key Improvements

### 1. Standard Event Names
- **Before**: Using generic event names like `__REVENUE__`
- **After**: Using Singular standard event names like `sngPurchase`, `sngRevenue`, `sngSubscribe`
- **Benefit**: Better recognition in Singular dashboard and improved ad network compatibility

### 2. Multiple Revenue Tracking Methods
The new implementation uses a multi-layered approach for comprehensive attribution:

```dart
// 1. Custom revenue with standard event name
Singular.customRevenue(standardEventName, currency, price);

// 2. Detailed purchase event with attributes
Singular.eventWithArgs('sngPurchase', {
  'product_id': productId,
  'transaction_id': transactionId,
  'revenue': price,
  'currency': currency,
  // ... more attributes
});

// 3. Generic revenue event for attribution
Singular.customRevenue('sngRevenue', currency, price);
```

### 3. Enhanced Purchase Receipt Support
Added `trackPurchaseWithReceipt()` method for when purchase objects are available:
- Includes purchase tokens and signatures for fraud prevention
- Provides maximum attribution accuracy
- Enables Singular's receipt validation features

### 4. Improved Event Mapping
Intelligent mapping of custom event names to Singular standards:
- `purchase_successful` → `sngPurchase`
- `subscription_started` → `sngSubscribe`
- `trial_started` → `sngStartTrial`
- Custom events → `sngRevenue`

## Attribution Benefits

### Dashboard Recognition
- Events appear in Singular dashboard with proper categorization
- Better revenue attribution visualization
- Improved campaign performance insights

### Ad Network Optimization
- Standard events are recognized by ad networks (Google Ads, Facebook, TikTok)
- Better postback compatibility
- Enhanced campaign optimization capabilities

### Fraud Prevention
- Receipt validation when purchase objects are available
- Transaction verification through platform stores
- More accurate revenue attribution

## Usage Examples

### Basic Purchase Tracking
```dart
final attributionService = PurchaseAttributionService();
await attributionService.trackPurchase(
  eventName: 'purchase_successful',
  productId: 'credit_pack_10',
  price: 9.99,
  currency: 'USD',
  transactionId: 'txn_123456',
);
```

### Enhanced Purchase with Receipt
```dart
await attributionService.trackPurchaseWithReceipt(
  eventName: 'purchase_successful',
  productId: 'credit_pack_10',
  price: 9.99,
  currency: 'USD',
  transactionId: 'txn_123456',
  purchaseToken: 'purchase_token_from_store',
  signature: 'signature_from_store',
);
```

## PassKey Integration

Both methods automatically use the user's passkey for consistent attribution:
- PassKey is set as custom user ID for Singular
- Ensures attribution across sessions and devices
- Maintains user identity throughout the customer journey

## Singular SDK Version

Updated to Singular Flutter SDK v1.7.0 for:
- Latest attribution features
- Improved dashboard integration
- Enhanced revenue tracking capabilities

## Expected Dashboard Improvements

1. **Better Revenue Attribution**: Revenue events will be properly attributed to marketing campaigns
2. **Improved ROI Visibility**: Clear connection between ad spend and revenue
3. **Enhanced Reporting**: Standard events provide richer dashboard insights
4. **Campaign Optimization**: Ad networks can better optimize based on revenue events

## Implementation Status

✅ **Completed**:
- Enhanced PurchaseAttributionService with multiple tracking methods
- Standard event name mapping
- PassKey integration for user identification
- SDK version upgrade to 1.7.0

✅ **Ready for Use**:
- All existing purchase tracking will automatically benefit from improvements
- New receipt-based tracking available for enhanced accuracy
- Backward compatible with existing implementation

## Next Steps

1. **Monitor Dashboard**: Check Singular dashboard for improved revenue attribution
2. **Test Receipt Tracking**: Implement receipt-based tracking for highest accuracy
3. **Campaign Optimization**: Use enhanced attribution data for better campaign decisions
4. **Performance Analysis**: Compare attribution accuracy before and after implementation

## Dashboard Setup Companion

For complete dashboard configuration to maximize the benefits of this enhanced implementation, see the detailed setup guide:

📋 **[SINGULAR_DASHBOARD_SETUP_GUIDE.md](./singular_dashboard_setup.md)**

This companion guide covers:
- ✅ Step-by-step dashboard configuration
- ✅ Event and postback setup
- ✅ Attribution window optimization
- ✅ ROAS campaign configuration
- ✅ Advanced analytics setup
- ✅ Troubleshooting and monitoring

## Quick Implementation Checklist

### Code Implementation (Completed ✅)
- [x] Enhanced PurchaseAttributionService with standard event names
- [x] Multi-layered revenue tracking approach
- [x] Receipt-based tracking for maximum accuracy
- [x] PassKey integration for user identification
- [x] Singular SDK 1.7.0 upgrade

### Dashboard Configuration (Next Steps 📋)
- [ ] Configure standard events (`sngPurchase`, `sngRevenue`, `sngSubscribe`)
- [ ] Set up attribution windows (recommended: 7-day click, 1-day view)
- [ ] Enable custom user ID tracking for PassKey
- [ ] Configure postbacks for all active ad networks
- [ ] Set up revenue attribution reports and widgets
- [ ] Test and validate attribution accuracy

### Monitoring & Optimization (Ongoing 🔄)
- [ ] Daily revenue attribution monitoring
- [ ] Weekly campaign ROAS analysis
- [ ] Monthly LTV and cohort reviews
- [ ] Quarterly attribution model optimization

This enhanced implementation provides the most comprehensive revenue attribution possible with the Singular platform, ensuring maximum visibility into campaign performance and ROI.
