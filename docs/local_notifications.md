# Local Notification Service for FroggyTalk

A comprehensive local notification system built for Flutter with advanced features, localization support, and optimized performance using BLoC state management.

## Features

### Core Functionality
- **Immediate Notifications**: Send notifications instantly with rich content
- **Scheduled Notifications**: Schedule notifications for future delivery
- **Repeating Notifications**: Set up recurring notifications
- **Notification Categories**: Organize notifications by type (calls, promotions, security, etc.)
- **Custom Actions**: Add interactive buttons to notifications
- **Rich Content**: Support for images, custom sounds, and styled content

### Advanced Features
- **Localization Support**: Multi-language support using `intl`
- **Cross-Platform**: Works on both iOS and Android with platform-specific optimizations
- **Permission Management**: Automatic permission handling and status checking
- **Performance Optimized**: Efficient handling of large notification volumes
- **Navigation Integration**: Automatic navigation based on notification types
- **Error Handling**: Comprehensive error handling with recovery mechanisms

### Supported Languages
- English (en)
- Arabic (ar)
- French (fr)
- <PERSON><PERSON> (ha)
- <PERSON><PERSON><PERSON><PERSON> (ti)
- Amharic (am)
- <PERSON><PERSON> (din)

## Architecture

### Component Overview
```
Local Notification System
├── FroggyLocalNotifications (Core Service)
├── LocalNotificationService (Business Logic)
├── LocalNotificationBloc (State Management)
└── Demo Widgets (UI Examples)
```

### Key Components

#### 1. FroggyLocalNotifications
**Location**: `packages/local_notifications/lib/src/local_notifications.dart`

Core service handling platform-specific notification functionality:
- Platform-specific initialization
- Notification channel management (Android)
- Permission handling (iOS)
- Rich notification support
- Background notification handling

#### 2. LocalNotificationService
**Location**: `lib/shared/services/local_notification_service.dart`

High-level service with business logic:
- Localization integration
- Navigation handling
- Category-based management
- User-friendly API

#### 3. LocalNotificationBloc
**Location**: `lib/shared/bloc/local_notification_bloc.dart`

BLoC for reactive state management:
- Event-driven architecture
- State transitions
- Error handling
- Testing support

## Installation & Setup

### 1. Dependencies
The service uses the following packages:
- `flutter_local_notifications: ^18.0.1`
- `timezone: ^0.9.4`
- `flutter_bloc: ^9.1.1`
- `flutter_hooks: ^0.20.5`

### 2. Platform Configuration

#### Android Setup
Add to `android/app/src/main/AndroidManifest.xml`:
```xml
<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
<uses-permission android:name="android.permission.VIBRATE" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
<uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />

<receiver android:exported="false" android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationReceiver" />
<receiver android:exported="false" android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver">
    <intent-filter>
        <action android:name="android.intent.action.BOOT_COMPLETED"/>
        <action android:name="android.intent.action.MY_PACKAGE_REPLACED"/>
        <action android:name="android.intent.action.QUICKBOOT_POWERON" />
        <action android:name="com.htc.intent.action.QUICKBOOT_POWERON"/>
    </intent-filter>
</receiver>
```

#### iOS Setup
Add to `ios/Runner/Info.plist`:
```xml
<key>UIBackgroundModes</key>
<array>
    <string>background-processing</string>
</array>
```

### 3. Initialization
The service is automatically initialized in `lib/bootstrap.dart`:
```dart
// Initialize local notification service
await LocalNotificationService.instance.initialize();
```

## Usage Examples

### Basic Usage

#### Show Immediate Notification
```dart
await LocalNotificationService.instance.showNotification(
  id: 1,
  titleKey: 'notification_title',
  bodyKey: 'notification_body',
  category: NotificationCategory.general,
);
```

#### Schedule Notification
```dart
await LocalNotificationService.instance.scheduleNotification(
  id: 2,
  titleKey: 'scheduled_title',
  bodyKey: 'scheduled_body',
  scheduledDate: DateTime.now().add(Duration(hours: 1)),
  category: NotificationCategory.callReminder,
);
```

### Using Extension Methods

#### Call Reminder
```dart
await LocalNotificationService.instance.showCallReminder(
  phoneNumber: '+1234567890',
  contactName: 'John Doe',
);
```

#### Promotion Notification
```dart
await LocalNotificationService.instance.showPromotion(
  offerId: 'promo_001',
  offerTitle: '50% Off International Calls',
  offerDescription: 'Limited time offer!',
  imageUrl: 'https://example.com/promo.jpg',
);
```

#### Security Alert
```dart
await LocalNotificationService.instance.showSecurityAlert(
  alertType: 'Login Attempt',
  deviceInfo: 'Unknown Device',
);
```

### Using with BLoC

```dart
// Initialize BLoC
final bloc = LocalNotificationBloc(
  notificationService: LocalNotificationService.instance,
);

// Show notification
bloc.add(ShowLocalNotification(
  id: 1,
  titleKey: 'test_title',
  bodyKey: 'test_body',
  category: NotificationCategory.general,
));

// Listen to state changes
bloc.stream.listen((state) {
  if (state is LocalNotificationTapped) {
    // Handle notification tap
    print('Notification tapped: ${state.payload.category}');
  }
});
```

## Notification Categories

### Available Categories
1. **General** - Default notifications
2. **Call Reminder** - Call-related notifications with high priority
3. **Promotion** - Marketing and promotional content (low priority)
4. **Account Update** - Account-related notifications
5. **Security** - Security alerts with high priority and sound
6. **System Update** - App and system updates

### Category-Specific Behavior
- **Call Reminder**: High priority, custom ringtone, vibration
- **Promotion**: Low priority, no sound, no vibration
- **Security**: High priority, alert sound, vibration
- **General**: Default priority, standard notification sound

## Localization

### Adding New Strings
1. Add strings to `lib/l10n/arb/intl_en.arb`:
```json
{
  "notification_new_title": "New Notification",
  "@notification_new_title": {
    "description": "Title for new notification"
  },
  "notification_new_body": "You have a new message from {sender}",
  "@notification_new_body": {
    "description": "Body for new notification",
    "placeholders": {
      "sender": {
        "type": "String",
        "example": "John Doe"
      }
    }
  }
}
```

2. Generate localization files:
```bash
flutter gen-l10n
```

### Using Localized Notifications
```dart
await LocalNotificationService.instance.showNotification(
  id: 1,
  titleKey: 'notification_new_title',
  bodyKey: 'notification_new_body',
  titleArgs: {},
  bodyArgs: {'sender': 'John Doe'},
);
```

## Testing

### Demo Widget
Use the demo widget to test notification functionality:
```dart
import 'package:froggytalk/shared/widgets/local_notification_simple_demo.dart';

// In your app
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const LocalNotificationDemoWidget(),
  ),
);
```

### Unit Testing
```dart
void main() {
  group('LocalNotificationService', () {
    late LocalNotificationService service;
    
    setUp(() {
      service = LocalNotificationService.instance;
    });
    
    test('should initialize successfully', () async {
      await service.initialize();
      expect(service.isInitialized, true);
    });
    
    test('should show notification', () async {
      await service.showNotification(
        id: 1,
        titleKey: 'test_title',
        bodyKey: 'test_body',
      );
      // Verify notification was shown
    });
  });
}
```

## Performance Considerations

### Optimization Techniques
1. **Efficient ID Management**: Use hash codes for consistent IDs
2. **Category-Based Channels**: Separate channels for different notification types
3. **Background Processing**: Minimal processing in background handlers
4. **Memory Management**: Proper disposal of resources
5. **Batch Operations**: Group multiple operations when possible

### Best Practices
- Use unique IDs for each notification
- Cancel unnecessary scheduled notifications
- Implement proper error handling
- Test on both platforms
- Follow platform-specific guidelines

## Debugging

### Common Issues

#### Notifications Not Showing
1. Check permissions: `await service.areNotificationsEnabled()`
2. Verify initialization: `service.isInitialized`
3. Check notification channels (Android)
4. Verify app is not in Do Not Disturb mode

#### Scheduled Notifications Not Working
1. Ensure exact alarm permission (Android 12+)
2. Check battery optimization settings
3. Verify timezone configuration
4. Test with short delays first

#### Navigation Not Working
1. Verify router context is available
2. Check route definitions
3. Ensure proper payload structure

### Debug Tools
- Use `debugPaintSizeEnabled` for layout issues
- Enable notification logs in development
- Use platform-specific debugging tools
- Monitor memory usage during high-volume testing

## API Reference

### LocalNotificationService

#### Methods
- `initialize()` - Initialize the service
- `showNotification()` - Show immediate notification
- `scheduleNotification()` - Schedule future notification
- `cancelNotification(int id)` - Cancel specific notification
- `cancelAllNotifications()` - Cancel all notifications
- `areNotificationsEnabled()` - Check permission status
- `getPendingNotifications()` - Get scheduled notifications

#### Extension Methods
- `showCallReminder()` - Show call reminder
- `showPromotion()` - Show promotional notification
- `showSecurityAlert()` - Show security alert
- `scheduleDailyReminder()` - Schedule daily reminder

### NotificationPayload

#### Properties
- `category` - Notification category
- `data` - Custom data map
- `actionId` - Action identifier

#### Methods
- `toPayload()` - Serialize to string
- `fromPayload(String)` - Deserialize from string

## Contributing

### Development Setup
1. Clone the repository
2. Install dependencies: `flutter pub get`
3. Generate localization: `flutter gen-l10n`
4. Run tests: `flutter test`

### Code Style
- Follow Flutter style guide
- Use camelCase for variables and methods
- Add documentation comments for public APIs
- Include unit tests for new features

### Commit Messages
Follow conventional commits:
- `feat(notifications): add rich notification support`
- `fix(locale): correct RTL layout in Arabic`
- `perf(service): optimize notification processing`

## License

This project is part of the FroggyTalk mobile application.
