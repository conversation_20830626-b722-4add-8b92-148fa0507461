
# Storybook-Driven Development with `storybook_flutter`

This document outlines the process of using `storybook_flutter` to build, view, and test UI components in isolation. Adopting a storybook-driven approach accelerates development, improves component quality, and creates living documentation for your app's design system.

#### 1. What is Storybook?

A storybook is a tool that provides an isolated environment for building and showcasing UI components. Each component is captured in a "story," which represents a specific state or variation of that component. This allows developers and designers to work on components without needing to run the entire application or navigate to a specific screen.

#### 2. Why Use `storybook_flutter`?

* **Isolation:** Develop and test widgets independently from your app's business logic, navigation, and API calls.
* **Rapid Iteration:** Leverage Flutter's hot reload to see changes to your widgets instantly in multiple states.
* **Visual Documentation:** The storybook becomes a browsable gallery of your app's components, making it easy for the team to see what's available.
* **Collaboration:** Designers can review UI components and their variations without needing to run the app on a device.
* **Testability:** Stories make it easier to write screenshot tests and verify visual consistency.

#### 3. Setup and Installation

To get started, add `storybook_flutter` as a development dependency in your `pubspec.yaml` file.

```yaml
dev_dependencies:
  # ... other dependencies
  storybook_flutter: ^0.12.0 # Always check for the latest version
```

After adding the dependency, run the following command in your terminal to install it:

```sh
flutter pub get
```

#### 4. Creating Your First Story

Let's create a story for the `PreferredCallingUserView` widget.

**Step 1: Create a Storybook Entry Point**

It's good practice to keep your storybook files separate from your main application source. Create a new directory, `storybook/`, and a new entry file, `storybook/main.dart`.

**Step 2: Write the Story**

In `storybook/main.dart`, you will define the stories for your components. A story is essentially a function that returns a widget in a specific state. To make the `PreferredCallingUserView` work in isolation, we will provide it with mocked BLoCs and data.

```dart
// storybook/main.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:froggytalk/dialer/dialer.dart';
import 'package:froggytalk/dialer/views/preferred_calling_user_view.dart';
import 'package:mocktail/mocktail.dart';
import 'package:sip_ua/sip_ua.dart' as sip_ua;
import 'package:storybook_flutter/storybook_flutter.dart';

// Import or create your mock classes here
class MockDialerBloc extends Mock implements DialerBloc {}
class MockAuthBloc extends Mock implements AuthenticationBloc {}
class MockCallTimerBloc extends Mock implements CallTimerBloc {}
class MockCall extends Mock implements sip_ua.Call {}
class MockUser extends Mock implements User {} // Assuming User is a class

void main() => runApp(const StorybookApp());

class StorybookApp extends StatelessWidget {
  const StorybookApp({super.key});

  @override
  Widget build(BuildContext context) {
    // 1. Instantiate all necessary mock objects.
    final mockDialerBloc = MockDialerBloc();
    final mockAuthBloc = MockAuthBloc();
    final mockCallTimerBloc = MockCallTimerBloc();
    final mockCall = MockCall();

    // 2. Stub the states and properties of the mocks.
    // This is where you define the state you want to preview.
    when(() => mockDialerBloc.state).thenReturn(
      const DialerState(
        status: DialerStatus.connected,
        phoneNumber: 'Storybook Call',
        countryCode: 'US',
      ),
    );
    when(() => mockAuthBloc.state).thenReturn(AuthenticationState.authenticated(user: MockUser()));
    when(() => mockCallTimerBloc.state).thenReturn(
      const CallTimerState(elapsedTime: Duration(minutes: 3, seconds: 14)),
    );
    when(() => mockCall.id).thenReturn('storybook_call_id');
    when(() => mockCall.remote_identity).thenReturn('Storybook User');

    // 3. Define the stories.
    return Storybook(
      initialStory: 'Views/Calling/Connected',
      stories: [
        Story(
          name: 'Views/Calling/Connected',
          description: 'The standard view for an active, connected call.',
          builder: (context) => MultiBlocProvider(
            providers: [
              BlocProvider<DialerBloc>.value(value: mockDialerBloc),
              BlocProvider<AuthenticationBloc>.value(value: mockAuthBloc),
              BlocProvider<CallTimerBloc>.value(value: mockCallTimerBloc),
            ],
            child: PreferredCallingUserView(
              call: mockCall,
              phoneNumber: 'Storybook Call',
            ),
          ),
        ),
        // You can add more stories for the same widget in different states
        Story(
          name: 'Views/Calling/Ringing',
          builder: (context) {
            // Stub a different state for the ringing view
            when(() => mockDialerBloc.state).thenReturn(
              const DialerState(status: DialerStatus.ringing),
            );
            return MultiBlocProvider(
              // ... providers
              child: PreferredCallingUserView(call: mockCall),
            );
          },
        ),
      ],
    );
  }
}
```

#### 5. Running the Storybook

To view your storybook, run the entry point file from your terminal with your emulator or device active:

```sh
flutter run storybook/main.dart
```

This will launch a special version of your app that displays the storybook UI, allowing you to browse through your component stories.
