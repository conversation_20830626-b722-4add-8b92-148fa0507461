# Enhanced Call Notification Service Integration Plan

## Overview
This document outlines the integration steps required to implement the `EnhancedCallNotificationService` into the main call flow, specifically within the `DialerBloc`. The technical foundation is complete, but the actual integration into the call lifecycle is pending.

## Current State
- ✅ Native iOS CallKit service (`CallKitService.swift`) implemented
- ✅ Flutter-iOS bridge (`CallKitService`) created
- ✅ iOS call notification service (`IOSCallNotificationService`) implemented
- ✅ Cross-platform enhanced service (`EnhancedCallNotificationService`) created
- ✅ All services exported in `lib/app/app.dart`
- ✅ Documentation (`IOS_CALL_NOTIFICATION_FIX.md`) created
- ❌ Integration into `DialerBloc` and main call flow **NOT IMPLEMENTED**

## Integration Points Required

### 1. DialerBloc Constructor Updates
**File**: `lib/dialer/bloc/dialer_bloc.dart`

**Current Status**: The `EnhancedCallNotificationService` is already injected as a dependency but not actively used.

**Required Changes**:
```dart
// The service is already injected:
final EnhancedCallNotificationService _enhancedCallNotificationService;

// Need to initialize it in constructor:
// Initialize the enhanced call notification service
await _enhancedCallNotificationService.initialize();
```

### 2. Call Start Integration
**Location**: `_onCallStarted` method in `DialerBloc`

**Current Behavior**: Uses Android foreground service via `CallService.makeCall()`

**Required Changes**:
1. **Start call notification** before making the actual call:
```dart
// Add before the existing makeCall logic:
try {
  await _enhancedCallNotificationService.startCall(
    callId: 'call_${DateTime.now().millisecondsSinceEpoch}',
    phoneNumber: phoneNumber,
    contactName: await _getContactName(phoneNumber), // Helper method needed
  );
} catch (e) {
  FroggyLogger.error('Failed to start call notification: $e');
  // Continue with call anyway, but log the issue
}
```

2. **Store call ID** in state for later reference:
```dart
emit(
  state.copyWith(
    phoneNumber: phoneNumber,
    countryCode: _phoneNumberService.getCountryCode(phoneNumber),
    status: DialerStatus.initial,
    currentCallId: callId, // New field needed in DialerState
  ),
);
```

### 3. Call State Updates Integration
**Location**: `callStateChanged` method in `DialerBloc`

**Current Behavior**: Only updates internal state

**Required Changes**:
Add call notification updates for key state changes:

```dart
switch (callState.state) {
  case sip_ua.CallStateEnum.CONNECTING:
    // Existing logic...
    // Add:
    await _enhancedCallNotificationService.reportCallConnecting(
      callId: state.currentCallId,
    );
    
  case sip_ua.CallStateEnum.PROGRESS:
    // Existing logic...
    // Add:
    await _enhancedCallNotificationService.reportCallRinging(
      callId: state.currentCallId,
    );
    
  case sip_ua.CallStateEnum.ACCEPTED:
  case sip_ua.CallStateEnum.CONFIRMED:
    // Existing logic...
    // Add:
    await _enhancedCallNotificationService.reportCallConnected(
      callId: state.currentCallId,
    );
    
  case sip_ua.CallStateEnum.ENDED:
  case sip_ua.CallStateEnum.FAILED:
    // Existing logic...
    // Add:
    await _enhancedCallNotificationService.endCall(
      callId: state.currentCallId,
      reason: callState.state == sip_ua.CallStateEnum.FAILED 
        ? CallEndReason.failed 
        : CallEndReason.remoteEnded,
    );
}
```

### 4. Hangup Integration
**Location**: `_onHangedUp` method in `DialerBloc`

**Current Behavior**: Performs SIP hangup and state cleanup

**Required Changes**:
Add call notification cleanup:

```dart
// Add before or after the existing hangup logic:
try {
  await _enhancedCallNotificationService.endCall(
    callId: state.currentCallId,
    reason: CallEndReason.localEnded,
  );
} catch (e) {
  FroggyLogger.error('Failed to end call notification: $e');
  // Continue with cleanup regardless
}
```

### 5. State Model Updates
**File**: `lib/dialer/bloc/dialer_state.dart`

**Required Changes**:
Add new field to track current call notification ID:

```dart
@freezed
class DialerState with _$DialerState {
  const factory DialerState({
    // ...existing fields...
    String? currentCallId, // Add this field
  }) = _DialerState;
}
```

### 6. Helper Methods to Add
**Location**: `DialerBloc` class

**Required Methods**:

```dart
/// Gets the contact name for a phone number, if available
Future<String?> _getContactName(String phoneNumber) async {
  try {
    // This would integrate with the contacts service
    // Implementation depends on how contacts are handled in the app
    return null; // Placeholder
  } catch (e) {
    FroggyLogger.error('Error getting contact name: $e');
    return null;
  }
}

/// Handles errors from the enhanced call notification service
void _handleCallNotificationError(String operation, dynamic error) {
  FroggyLogger.error('Call notification $operation failed: $error');
  // Could add user-facing error handling here if needed
  // For now, we continue with the call flow regardless of notification issues
}
```

## iOS-Specific Integration Notes

### CallKit Event Handling
The iOS `CallKitService` will send events back to Flutter via method channels. These need to be handled in the `DialerBloc`:

**Required**: Listen to CallKit events and sync with SIP state:
```dart
// In constructor or initialization method:
_enhancedCallNotificationService.callKitEventStream?.listen((event) {
  switch (event.type) {
    case CallKitEventType.answerCall:
      // User answered via CallKit - ensure SIP call is connected
      break;
    case CallKitEventType.endCall:
      // User ended via CallKit - trigger SIP hangup
      add(const DialerEvent.hangedUp());
      break;
    case CallKitEventType.holdCall:
      // User held via CallKit - trigger SIP hold
      add(const DialerEvent.holdCall());
      break;
    case CallKitEventType.unholdCall:
      // User unhold via CallKit - trigger SIP unhold  
      add(const DialerEvent.unholdCall());
      break;
  }
});
```

## Error Handling Strategy

### Graceful Degradation
The integration should be designed to gracefully handle failures:

1. **Call notification failures should NOT prevent calls from proceeding**
2. **Log errors but continue with call flow**
3. **On iOS: If CallKit fails, continue with basic call functionality**
4. **On Android: If foreground service fails, continue with call but log the issue**

### Error Recovery
```dart
try {
  await _enhancedCallNotificationService.startCall(/*...*/);
} catch (e) {
  _handleCallNotificationError('start', e);
  // Continue with makeCall regardless
}
```

## Testing Strategy

### Unit Tests Required
1. **DialerBloc integration tests** with mocked `EnhancedCallNotificationService`
2. **State transition tests** ensuring call IDs are properly managed
3. **Error handling tests** ensuring failures don't break call flow

### Integration Tests Required
1. **iOS device testing** with actual CallKit integration
2. **Android device testing** with foreground service
3. **Cross-platform testing** ensuring consistent behavior

### Manual Testing Checklist
- [ ] Start call → CallKit/Notification appears
- [ ] Answer call → State syncs correctly
- [ ] Hold/Unhold → Both SIP and CallKit sync
- [ ] End call → Notification disappears
- [ ] Failed call → Proper cleanup
- [ ] Multiple calls → Proper call ID management
- [ ] Background/Foreground transitions
- [ ] Device rotation during calls
- [ ] Permission handling (iOS CallKit permissions)

## Dependencies and Prerequisites

### Required Services Available
- ✅ `PhoneNumberService` - for formatting phone numbers
- ✅ `CallService` - for SIP call management
- ✅ `EnhancedCallNotificationService` - for call notifications
- ❓ Contact service - for getting contact names (integration needed)

### Required Permissions
- ✅ iOS: Already configured in `Info.plist` (VoIP, audio background modes)
- ✅ Android: Foreground service permissions already configured

## Potential Issues and Mitigations

### Issue 1: Call ID Management
**Problem**: Need to track call IDs across SIP and notification systems
**Mitigation**: Use consistent UUID-based call IDs, store in state

### Issue 2: State Synchronization
**Problem**: CallKit and SIP state might get out of sync
**Mitigation**: Implement bidirectional event handling and state reconciliation

### Issue 3: Performance Impact
**Problem**: Additional async operations during call start/end
**Mitigation**: Use `unawaited()` for non-critical notification operations

### Issue 4: Platform-Specific Behavior
**Problem**: Different notification behavior on iOS vs Android
**Mitigation**: Comprehensive cross-platform testing and documentation

## Implementation Timeline

### Phase 1: Basic Integration (1-2 days)
- [ ] Add call notification service initialization
- [ ] Integrate start/end call notifications
- [ ] Add basic error handling

### Phase 2: State Synchronization (1-2 days)
- [ ] Implement CallKit event handling
- [ ] Add state model updates
- [ ] Implement bidirectional sync

### Phase 3: Polish and Testing (2-3 days)
- [ ] Add comprehensive error handling
- [ ] Implement helper methods
- [ ] Write unit and integration tests
- [ ] Manual testing on both platforms

### Phase 4: Documentation and Cleanup (1 day)
- [ ] Update existing documentation
- [ ] Add code comments
- [ ] Clean up any temporary code
- [ ] Final review and testing

## Files That Need Modification

### Core Integration Files
1. `lib/dialer/bloc/dialer_bloc.dart` - Main integration point
2. `lib/dialer/bloc/dialer_state.dart` - Add call ID field
3. `lib/dialer/bloc/dialer_event.dart` - Potentially add CallKit events

### Supporting Files
4. Contact service integration (file location TBD)
5. Any UI components that need to handle CallKit events

### Test Files
6. `test/dialer/bloc/dialer_bloc_test.dart` - Integration tests
7. New integration test files for cross-platform testing

## Success Criteria

### Functional Requirements
- [ ] Calls show proper system notifications on both platforms
- [ ] iOS calls integrate seamlessly with CallKit
- [ ] Android calls use foreground service appropriately
- [ ] Call state stays synchronized across all systems
- [ ] Error conditions are handled gracefully

### Non-Functional Requirements
- [ ] No performance degradation during call operations
- [ ] Memory usage remains stable
- [ ] Battery usage is optimized (especially for background operation)
- [ ] User experience is consistent across platforms

## Future Enhancements

### Potential Improvements
1. **Contact Integration**: Show contact photos in CallKit
2. **Call History**: Integrate with system call logs
3. **Multiple Calls**: Support for call waiting and multiple simultaneous calls
4. **VoIP Push**: Implement VoIP push notifications for incoming calls
5. **Analytics**: Track call notification system performance

---

**Note**: This integration should be approached incrementally, with thorough testing at each step to ensure the core calling functionality remains stable throughout the process.
