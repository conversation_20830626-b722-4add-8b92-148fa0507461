# Suggested Commit Messages (Following Conventional Commits)

## For this refactoring work:

```bash
# Main refactoring commit
feat(dialer): extract specialized blocs for maintainability

- Create AudioBloc for audio controls and routing
- Create RegistrationBloc for SIP registration management  
- Create CallTimerBloc for call duration tracking
- Add specialized blocs to app BlocProvider setup
- Resolve SIP UA type conflicts with namespace imports
- Add demo widget showing specialized bloc usage
- Generate Freezed files for all new event/state classes

Follows single responsibility principle and improves testability.
Original DialerBloc preserved for gradual migration approach.

# Individual commits (if breaking down):
feat(dialer): create AudioBloc for call audio management
feat(dialer): create RegistrationBloc for SIP registration
feat(dialer): create CallTimerBloc for call duration tracking
feat(dialer): integrate specialized blocs in app setup
feat(dialer): add demo widget for specialized blocs
fix(dialer): resolve SIP UA naming conflicts with imports
chore(dialer): generate Freezed files for new blocs
docs(dialer): add refactoring progress documentation
```

## Benefits of this approach:
- **Separation of Concerns**: Each bloc handles one specific responsibility
- **Better Testability**: Smaller, focused blocs are easier to unit test
- **Maintainability**: Changes to audio logic won't affect registration logic
- **Type Safety**: Proper SIP UA namespace prevents type conflicts
- **Incremental Migration**: Original DialerBloc preserved for gradual transition
