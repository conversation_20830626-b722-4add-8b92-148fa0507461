# FroggyTalk Documentation

This directory contains the comprehensive documentation for the FroggyTalk application.

## Table of Contents

### Analytics and Attribution
- [Singular Attribution Enhancement](analytics/singular_attribution_enhancement.md) - Technical implementation details for using passkey in analytics
- [Singular Dashboard Setup Guide](analytics/singular_dashboard_setup.md) - Step-by-step guide for Singular dashboard configuration

### API Documentation
- [API Implementation Guide](api/api_implementation_guide.md) - Guidelines for implementing and consuming APIs

### Legal Documents
- [Terms and Conditions](legal/terms-conditions.md)
- [Privacy Policy](legal/privacy-policy.md)

### Developer Guides
- [Pull Request Template](../.github/PULL_REQUEST_TEMPLATE.md)
- [Changelog](../CHANGELOG.md)

### Package Documentation
- [Dynamic App Icons](../packages/dynamic_app_icons/README.md)
- [Constants](../packages/constants/README.md)
- [Common](../packages/common/README.md)
- [Froggy Icons](../packages/froggy_icons/README.md)
- [Theme](../packages/theme/README.md)
- [Data](../packages/data/README.md)

## Contributing

Please ensure all documentation follows our standards:
1. Use clear, concise language
2. Include code examples where relevant
3. Keep documentation up-to-date with code changes
4. Add links to related documentation where appropriate
5. Follow the markdown style guide
