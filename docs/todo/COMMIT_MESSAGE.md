feat(call-notifications): create comprehensive integration TODO

- Add detailed INTEGRATION_TODO.md with complete checklist for Enhanced Call Notification Service integration
- Create QUICK_START_INTEGRATION.md for rapid 50-minute basic integration
- Document all pending work across 6 phases: core integration, state management, CallKit events, error handling, testing, and documentation
- Provide timeline estimates (~22.75 hours total) and risk mitigations
- Include troubleshooting guide and success criteria
- Ready for immediate DialerBloc integration to enable cross-platform call notifications

The technical foundation is complete - all infrastructure and services are implemented.
Integration into main call flow requires updating DialerState, DialerBloc constructor,
call start/end methods, and adding CallKit event handling.

Files:


- INTEGRATION_TODO.md (new): Complete integration checklist
- QUICK_START_INTEGRATION.md (new): 5-step quick start guide
- Builds on existing: CallKitService.swift, IOSCallNotificationService, EnhancedCallNotificationService
