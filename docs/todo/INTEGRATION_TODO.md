# Enhanced Call Notification Service - Integration TODO

## Overview

This document provides a complete checklist of all pending work required to integrate the `EnhancedCallNotificationService` into the main call flow. The technical foundation is complete, but the integration into the `DialerBloc` and call lifecycle is pending.

## 🎯 Integration Status

- ✅ **COMPLETE**: Native iOS CallKit service (`CallKitService.swift`)
- ✅ **COMPLETE**: Flutter-iOS bridge (`CallKitService`)
- ✅ **COMPLETE**: iOS call notification service (`IOSCallNotificationService`)
- ✅ **COMPLETE**: Cross-platform enhanced service (`EnhancedCallNotificationService`)
- ✅ **COMPLETE**: Services exported in `lib/app/app.dart`
- ✅ **COMPLETE**: Documentation (`IOS_CALL_NOTIFICATION_FIX.md` & `ENHANCED_CALL_NOTIFICATION_INTEGRATION.md`)
- ❌ **PENDING**: Integration into `DialerBloc` and main call flow

---

## 🚧 Phase 1: Core DialerBloc Integration

### 1.1 DialerBloc Constructor Updates

**File**: `lib/dialer/bloc/dialer_bloc.dart`  
**Status**: ❌ **NOT STARTED**

**Tasks**:

- [ ] Add service initialization call in constructor:

```dart
// Initialize the enhanced call notification service
await _enhancedCallNotificationService.initialize();
```

**Priority**: HIGH  
**Estimated Time**: 30 minutes

### 1.2 Call Start Integration

**File**: `lib/dialer/bloc/dialer_bloc.dart`  
**Method**: `_onCallStarted`  
**Status**: ❌ **NOT STARTED**

**Tasks**:

- [ ] Add call notification start before `CallService.makeCall()`:

```dart
try {
  final callId = 'call_${DateTime.now().millisecondsSinceEpoch}';
  await _enhancedCallNotificationService.startCall(
    callId: callId,
    phoneNumber: phoneNumber,
    contactName: await _getContactName(phoneNumber),
  );
} catch (e) {
  FroggyLogger.error('Failed to start call notification: $e');
}
```

- [ ] Store call ID in state emission

**Priority**: HIGH  
**Estimated Time**: 1 hour

### 1.3 Call Hangup Integration

**File**: `lib/dialer/bloc/dialer_bloc.dart`  
**Method**: `_onHangedUp`  
**Status**: ❌ **NOT STARTED**

**Tasks**:

- [ ] Add call notification cleanup:

```dart
try {
  await _enhancedCallNotificationService.endCall(
    callId: state.currentCallId,
    reason: CallEndReason.localEnded,
  );
} catch (e) {
  FroggyLogger.error('Failed to end call notification: $e');
}
```

**Priority**: HIGH  
**Estimated Time**: 30 minutes

---

## 🔄 Phase 2: State Management Updates

### 2.1 DialerState Model Updates

**File**: `lib/dialer/bloc/dialer_state.dart`  
**Status**: ❌ **NOT STARTED**

**Tasks**:

- [ ] Add `currentCallId` field to `DialerState`:

```dart
@freezed
class DialerState with _$DialerState {
  const factory DialerState({
    // ...existing fields...
    String? currentCallId, // NEW FIELD
  }) = _DialerState;
}
```

- [ ] Run `flutter packages pub run build_runner build` to regenerate freezed files

**Priority**: HIGH  
**Estimated Time**: 15 minutes

### 2.2 Call State Synchronization

**File**: `lib/dialer/bloc/dialer_bloc.dart`  
**Method**: `callStateChanged`  
**Status**: ❌ **NOT STARTED**

**Tasks**:

- [ ] Add notification updates for each SIP state:
  - [ ] `CONNECTING` → `reportCallConnecting`
  - [ ] `PROGRESS` → `reportCallRinging`
  - [ ] `ACCEPTED`/`CONFIRMED` → `reportCallConnected`
  - [ ] `ENDED`/`FAILED` → `endCall`

**Priority**: HIGH  
**Estimated Time**: 1 hour

---

## 🎛️ Phase 3: CallKit Event Handling (iOS)

### 3.1 CallKit Event Stream Integration

**File**: `lib/dialer/bloc/dialer_bloc.dart`  
**Status**: ❌ **NOT STARTED**

**Tasks**:

- [ ] Add CallKit event listener in constructor:

```dart
_enhancedCallNotificationService.callKitEventStream?.listen((event) {
  switch (event.type) {
    case CallKitEventType.answerCall:
      // Handle answer
      break;
    case CallKitEventType.endCall:
      add(const DialerEvent.hangedUp());
      break;
    // ... other events
  }
});
```

**Priority**: MEDIUM  
**Estimated Time**: 1 hour

### 3.2 Bidirectional State Sync

**File**: `lib/dialer/bloc/dialer_bloc.dart`  
**Status**: ❌ **NOT STARTED**

**Tasks**:

- [ ] Ensure SIP actions trigger CallKit updates
- [ ] Ensure CallKit actions trigger SIP state changes
- [ ] Add conflict resolution for competing state changes

**Priority**: MEDIUM  
**Estimated Time**: 2 hours

---

## 🛠️ Phase 4: Helper Methods and Error Handling

### 4.1 Contact Name Resolution

**File**: `lib/dialer/bloc/dialer_bloc.dart`  
**Status**: ❌ **NOT STARTED**

**Tasks**:

- [ ] Implement `_getContactName(String phoneNumber)` method
- [ ] Integrate with existing contact service (if available)
- [ ] Handle cases where contact service is unavailable

**Priority**: MEDIUM  
**Estimated Time**: 1 hour

### 4.2 Error Handling Infrastructure

**File**: `lib/dialer/bloc/dialer_bloc.dart`  
**Status**: ❌ **NOT STARTED**

**Tasks**:

- [ ] Implement `_handleCallNotificationError` method
- [ ] Add try-catch blocks around all service calls
- [ ] Ensure call flow continues even if notifications fail
- [ ] Add appropriate logging for debugging

**Priority**: HIGH  
**Estimated Time**: 30 minutes

---

## 🧪 Phase 5: Testing Implementation

### 5.1 Unit Tests

**File**: `test/dialer/bloc/dialer_bloc_test.dart`  
**Status**: ❌ **NOT STARTED**

**Tasks**:

- [ ] Mock `EnhancedCallNotificationService` in existing tests
- [ ] Add tests for call notification lifecycle
- [ ] Test error handling scenarios
- [ ] Test state transitions with call IDs

**Priority**: HIGH  
**Estimated Time**: 3 hours

### 5.2 Integration Tests

**Status**: ❌ **NOT STARTED**

**Tasks**:

- [ ] Create integration tests for iOS CallKit
- [ ] Create integration tests for Android foreground service
- [ ] Test cross-platform consistency
- [ ] Test background/foreground transitions

**Priority**: MEDIUM  
**Estimated Time**: 4 hours

### 5.3 Manual Testing Checklist

**Status**: ❌ **NOT STARTED**

**Tasks**:

- [ ] Test call start notifications
- [ ] Test call answer via CallKit (iOS)
- [ ] Test call end via CallKit (iOS)
- [ ] Test hold/unhold functionality
- [ ] Test failed call scenarios
- [ ] Test multiple calls (if supported)
- [ ] Test permission handling
- [ ] Test device rotation during calls
- [ ] Test background/foreground app transitions

**Priority**: HIGH  
**Estimated Time**: 4 hours

---

## 📚 Phase 6: Documentation and Cleanup

### 6.1 Code Documentation

**Status**: ❌ **NOT STARTED**

**Tasks**:

- [ ] Add comprehensive code comments to new integration points
- [ ] Document any platform-specific behavior
- [ ] Update existing method documentation
- [ ] Add examples for complex integration points

**Priority**: MEDIUM  
**Estimated Time**: 1 hour

### 6.2 Architecture Documentation

**Status**: ❌ **NOT STARTED**

**Tasks**:

- [ ] Update call flow diagrams
- [ ] Document state management patterns
- [ ] Create troubleshooting guide
- [ ] Document performance considerations

**Priority**: LOW  
**Estimated Time**: 2 hours

---

## 🔍 Dependencies and Prerequisites

### Required Services

- ✅ `PhoneNumberService` - Available
- ✅ `CallService` - Available  
- ✅ `EnhancedCallNotificationService` - Available
- ❓ Contact service - **NEEDS INVESTIGATION**

### Required Packages

- ✅ `uuid` - Available in pubspec.yaml
- ✅ `flutter_bloc` - Available
- ✅ `freezed` - Available

### Development Tools Needed

- ✅ iOS device for CallKit testing
- ✅ Android device for foreground service testing
- ✅ Flutter DevTools for debugging

---

## ⚠️ Known Risks and Mitigations

### Risk 1: State Synchronization Issues

**Problem**: CallKit and SIP state might diverge  
**Mitigation**: Implement robust bidirectional sync with conflict resolution  
**Priority**: HIGH

### Risk 2: Performance Impact

**Problem**: Additional async operations during call lifecycle  
**Mitigation**: Use `unawaited()` for non-critical operations, profile performance  
**Priority**: MEDIUM

### Risk 3: Platform-Specific Bugs

**Problem**: Different behavior on iOS vs Android  
**Mitigation**: Comprehensive cross-platform testing  
**Priority**: MEDIUM

### Risk 4: Contact Service Integration

**Problem**: Unknown contact service architecture  
**Mitigation**: Research existing contact handling patterns in the app  
**Priority**: MEDIUM

---

## 📊 Implementation Timeline

| Phase | Tasks | Estimated Time | Priority |
|-------|-------|---------------|----------|
| **Phase 1** | Core DialerBloc Integration | 2 hours | HIGH |
| **Phase 2** | State Management Updates | 1.25 hours | HIGH |
| **Phase 3** | CallKit Event Handling | 3 hours | MEDIUM |
| **Phase 4** | Helper Methods & Error Handling | 2.5 hours | HIGH |
| **Phase 5** | Testing Implementation | 11 hours | HIGH |
| **Phase 6** | Documentation & Cleanup | 3 hours | MEDIUM |
| **TOTAL** | **All Phases** | **~22.75 hours** | **~3-4 days** |

---

## 🎯 Success Criteria

### Functional Requirements

- [ ] iOS calls display in CallKit interface
- [ ] Android calls show foreground service notification
- [ ] Call state remains synchronized across all systems
- [ ] Users can answer/end calls from system interface
- [ ] Hold/unhold functionality works from CallKit
- [ ] Failed calls are handled gracefully
- [ ] Contact names display in notifications (when available)

### Technical Requirements

- [ ] No performance degradation during calls
- [ ] Memory usage remains stable
- [ ] All unit tests pass
- [ ] Integration tests pass on both platforms
- [ ] Error handling prevents app crashes
- [ ] Logging provides sufficient debugging information

### User Experience Requirements

- [ ] Seamless transition between app and system interfaces
- [ ] Consistent behavior across iOS and Android
- [ ] No unexpected call disconnections
- [ ] Clear visual feedback for all call states

---

## 🚀 Getting Started

### Immediate Next Steps

1. **Start with Phase 2.1** - Add `currentCallId` to `DialerState`
2. **Run code generation** - `flutter packages pub run build_runner build`
3. **Implement Phase 1.2** - Basic call start integration
4. **Test incrementally** - Verify each change doesn't break existing functionality

### Recommended Approach

- Work through phases sequentially
- Test after each major change
- Keep the existing call functionality working at all times
- Use feature flags if needed to toggle new functionality during development

---

**Created**: 2024-12-28  
**Status**: Ready for implementation  
**Next Action**: Begin Phase 2.1 - Add currentCallId to DialerState
