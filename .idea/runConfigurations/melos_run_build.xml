<!-- Generated by <PERSON><PERSON> -->
<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Melos Run -&gt; 'build'" type="ShConfigurationType">
    <option name="INDEPENDENT_SCRIPT_PATH" value="true" />
    <option name="SCRIPT_PATH" value="$USER_HOME$/.pub-cache/bin/melos" />
    <option name="SCRIPT_OPTIONS" value="run build" />
    <option name="INDEPENDENT_SCRIPT_WORKING_DIRECTORY" value="false" />
    <option name="SCRIPT_WORKING_DIRECTORY" value="$PROJECT_DIR$" />
    <method v="2" />
  </configuration>
</component>
