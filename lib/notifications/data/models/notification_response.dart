import 'package:freezed_annotation/freezed_annotation.dart';

part 'notification_response.freezed.dart';
part 'notification_response.g.dart';

@freezed
class NotificationResponse with _$NotificationResponse {
  factory NotificationResponse({
    dynamic id,
    String? title,
    dynamic body,
    dynamic bigPicture,
    dynamic largeIcon,
    dynamic smallIcon,
    @Json<PERSON>ey(name: 'read_at') String? readAt,
    @Json<PERSON>ey(name: 'created_at') String? createdAt,
  }) = _NotificationResponse;

  factory NotificationResponse.fromJson(Map<String, Object?> json) =>
      _$NotificationResponseFromJson(json);
}
