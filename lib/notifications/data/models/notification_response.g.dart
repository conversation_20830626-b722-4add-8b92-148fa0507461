// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$NotificationResponseImpl _$$NotificationResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$NotificationResponseImpl(
      id: json['id'],
      title: json['title'] as String?,
      body: json['body'],
      bigPicture: json['bigPicture'],
      largeIcon: json['largeIcon'],
      smallIcon: json['smallIcon'],
      readAt: json['read_at'] as String?,
      createdAt: json['created_at'] as String?,
    );

const _$$NotificationResponseImplFieldMap = <String, String>{
  'id': 'id',
  'title': 'title',
  'body': 'body',
  'bigPicture': 'bigPicture',
  'largeIcon': 'largeIcon',
  'smallIcon': 'smallIcon',
  'readAt': 'read_at',
  'createdAt': 'created_at',
};

Map<String, dynamic> _$$NotificationResponseImplToJson(
        _$NotificationResponseImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.title case final value?) 'title': value,
      if (instance.body case final value?) 'body': value,
      if (instance.bigPicture case final value?) 'bigPicture': value,
      if (instance.largeIcon case final value?) 'largeIcon': value,
      if (instance.smallIcon case final value?) 'smallIcon': value,
      if (instance.readAt case final value?) 'read_at': value,
      if (instance.createdAt case final value?) 'created_at': value,
    };
