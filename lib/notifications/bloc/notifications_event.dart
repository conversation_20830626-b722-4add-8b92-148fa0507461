part of 'notifications_bloc.dart';

@freezed
class NotificationsEvent with _$NotificationsEvent {
  const factory NotificationsEvent.started({@Default('all') String type}) =
      _Started;

  const factory NotificationsEvent.reloaded() = _Reload;

  const factory NotificationsEvent.deletedNotification({required dynamic id}) =
      _RemoveFromList;

  const factory NotificationsEvent.reset() = _Initial;
}
