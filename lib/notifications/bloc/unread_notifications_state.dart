part of 'unread_notifications_bloc.dart';

@freezed
class UnreadNotificationsState with _$UnreadNotificationsState {
  factory UnreadNotificationsState({
    @Default([]) List<NotificationResponse> notifications,
    @Default(FormzSubmissionStatus.initial) FormzSubmissionStatus status,
    String? errorMessage,
  }) = _UnreadNotificationsState;

  factory UnreadNotificationsState.initial() => UnreadNotificationsState();
}
