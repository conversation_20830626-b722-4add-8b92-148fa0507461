// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'single_notification_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SingleNotificationEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(NotificationResponse notification) started,
    required TResult Function(String? id) viewed,
    required TResult Function(String? id) deleted,
    required TResult Function(String? id) markAsRead,
    required TResult Function() initial,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(NotificationResponse notification)? started,
    TResult? Function(String? id)? viewed,
    TResult? Function(String? id)? deleted,
    TResult? Function(String? id)? markAsRead,
    TResult? Function()? initial,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(NotificationResponse notification)? started,
    TResult Function(String? id)? viewed,
    TResult Function(String? id)? deleted,
    TResult Function(String? id)? markAsRead,
    TResult Function()? initial,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Viewed value) viewed,
    required TResult Function(_Deleted value) deleted,
    required TResult Function(_MarkedAsRead value) markAsRead,
    required TResult Function(_Initial value) initial,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Viewed value)? viewed,
    TResult? Function(_Deleted value)? deleted,
    TResult? Function(_MarkedAsRead value)? markAsRead,
    TResult? Function(_Initial value)? initial,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Viewed value)? viewed,
    TResult Function(_Deleted value)? deleted,
    TResult Function(_MarkedAsRead value)? markAsRead,
    TResult Function(_Initial value)? initial,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SingleNotificationEventCopyWith<$Res> {
  factory $SingleNotificationEventCopyWith(SingleNotificationEvent value,
          $Res Function(SingleNotificationEvent) then) =
      _$SingleNotificationEventCopyWithImpl<$Res, SingleNotificationEvent>;
}

/// @nodoc
class _$SingleNotificationEventCopyWithImpl<$Res,
        $Val extends SingleNotificationEvent>
    implements $SingleNotificationEventCopyWith<$Res> {
  _$SingleNotificationEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SingleNotificationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$StartedImplCopyWith<$Res> {
  factory _$$StartedImplCopyWith(
          _$StartedImpl value, $Res Function(_$StartedImpl) then) =
      __$$StartedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({NotificationResponse notification});

  $NotificationResponseCopyWith<$Res> get notification;
}

/// @nodoc
class __$$StartedImplCopyWithImpl<$Res>
    extends _$SingleNotificationEventCopyWithImpl<$Res, _$StartedImpl>
    implements _$$StartedImplCopyWith<$Res> {
  __$$StartedImplCopyWithImpl(
      _$StartedImpl _value, $Res Function(_$StartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SingleNotificationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notification = null,
  }) {
    return _then(_$StartedImpl(
      notification: null == notification
          ? _value.notification
          : notification // ignore: cast_nullable_to_non_nullable
              as NotificationResponse,
    ));
  }

  /// Create a copy of SingleNotificationEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $NotificationResponseCopyWith<$Res> get notification {
    return $NotificationResponseCopyWith<$Res>(_value.notification, (value) {
      return _then(_value.copyWith(notification: value));
    });
  }
}

/// @nodoc

class _$StartedImpl implements _Started {
  const _$StartedImpl({required this.notification});

  @override
  final NotificationResponse notification;

  @override
  String toString() {
    return 'SingleNotificationEvent.started(notification: $notification)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StartedImpl &&
            (identical(other.notification, notification) ||
                other.notification == notification));
  }

  @override
  int get hashCode => Object.hash(runtimeType, notification);

  /// Create a copy of SingleNotificationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StartedImplCopyWith<_$StartedImpl> get copyWith =>
      __$$StartedImplCopyWithImpl<_$StartedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(NotificationResponse notification) started,
    required TResult Function(String? id) viewed,
    required TResult Function(String? id) deleted,
    required TResult Function(String? id) markAsRead,
    required TResult Function() initial,
  }) {
    return started(notification);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(NotificationResponse notification)? started,
    TResult? Function(String? id)? viewed,
    TResult? Function(String? id)? deleted,
    TResult? Function(String? id)? markAsRead,
    TResult? Function()? initial,
  }) {
    return started?.call(notification);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(NotificationResponse notification)? started,
    TResult Function(String? id)? viewed,
    TResult Function(String? id)? deleted,
    TResult Function(String? id)? markAsRead,
    TResult Function()? initial,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started(notification);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Viewed value) viewed,
    required TResult Function(_Deleted value) deleted,
    required TResult Function(_MarkedAsRead value) markAsRead,
    required TResult Function(_Initial value) initial,
  }) {
    return started(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Viewed value)? viewed,
    TResult? Function(_Deleted value)? deleted,
    TResult? Function(_MarkedAsRead value)? markAsRead,
    TResult? Function(_Initial value)? initial,
  }) {
    return started?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Viewed value)? viewed,
    TResult Function(_Deleted value)? deleted,
    TResult Function(_MarkedAsRead value)? markAsRead,
    TResult Function(_Initial value)? initial,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started(this);
    }
    return orElse();
  }
}

abstract class _Started implements SingleNotificationEvent {
  const factory _Started({required final NotificationResponse notification}) =
      _$StartedImpl;

  NotificationResponse get notification;

  /// Create a copy of SingleNotificationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StartedImplCopyWith<_$StartedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ViewedImplCopyWith<$Res> {
  factory _$$ViewedImplCopyWith(
          _$ViewedImpl value, $Res Function(_$ViewedImpl) then) =
      __$$ViewedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? id});
}

/// @nodoc
class __$$ViewedImplCopyWithImpl<$Res>
    extends _$SingleNotificationEventCopyWithImpl<$Res, _$ViewedImpl>
    implements _$$ViewedImplCopyWith<$Res> {
  __$$ViewedImplCopyWithImpl(
      _$ViewedImpl _value, $Res Function(_$ViewedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SingleNotificationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
  }) {
    return _then(_$ViewedImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$ViewedImpl implements _Viewed {
  const _$ViewedImpl({this.id});

  @override
  final String? id;

  @override
  String toString() {
    return 'SingleNotificationEvent.viewed(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ViewedImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id);

  /// Create a copy of SingleNotificationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ViewedImplCopyWith<_$ViewedImpl> get copyWith =>
      __$$ViewedImplCopyWithImpl<_$ViewedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(NotificationResponse notification) started,
    required TResult Function(String? id) viewed,
    required TResult Function(String? id) deleted,
    required TResult Function(String? id) markAsRead,
    required TResult Function() initial,
  }) {
    return viewed(id);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(NotificationResponse notification)? started,
    TResult? Function(String? id)? viewed,
    TResult? Function(String? id)? deleted,
    TResult? Function(String? id)? markAsRead,
    TResult? Function()? initial,
  }) {
    return viewed?.call(id);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(NotificationResponse notification)? started,
    TResult Function(String? id)? viewed,
    TResult Function(String? id)? deleted,
    TResult Function(String? id)? markAsRead,
    TResult Function()? initial,
    required TResult orElse(),
  }) {
    if (viewed != null) {
      return viewed(id);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Viewed value) viewed,
    required TResult Function(_Deleted value) deleted,
    required TResult Function(_MarkedAsRead value) markAsRead,
    required TResult Function(_Initial value) initial,
  }) {
    return viewed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Viewed value)? viewed,
    TResult? Function(_Deleted value)? deleted,
    TResult? Function(_MarkedAsRead value)? markAsRead,
    TResult? Function(_Initial value)? initial,
  }) {
    return viewed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Viewed value)? viewed,
    TResult Function(_Deleted value)? deleted,
    TResult Function(_MarkedAsRead value)? markAsRead,
    TResult Function(_Initial value)? initial,
    required TResult orElse(),
  }) {
    if (viewed != null) {
      return viewed(this);
    }
    return orElse();
  }
}

abstract class _Viewed implements SingleNotificationEvent {
  const factory _Viewed({final String? id}) = _$ViewedImpl;

  String? get id;

  /// Create a copy of SingleNotificationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ViewedImplCopyWith<_$ViewedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeletedImplCopyWith<$Res> {
  factory _$$DeletedImplCopyWith(
          _$DeletedImpl value, $Res Function(_$DeletedImpl) then) =
      __$$DeletedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? id});
}

/// @nodoc
class __$$DeletedImplCopyWithImpl<$Res>
    extends _$SingleNotificationEventCopyWithImpl<$Res, _$DeletedImpl>
    implements _$$DeletedImplCopyWith<$Res> {
  __$$DeletedImplCopyWithImpl(
      _$DeletedImpl _value, $Res Function(_$DeletedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SingleNotificationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
  }) {
    return _then(_$DeletedImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$DeletedImpl implements _Deleted {
  const _$DeletedImpl({this.id});

  @override
  final String? id;

  @override
  String toString() {
    return 'SingleNotificationEvent.deleted(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeletedImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id);

  /// Create a copy of SingleNotificationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeletedImplCopyWith<_$DeletedImpl> get copyWith =>
      __$$DeletedImplCopyWithImpl<_$DeletedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(NotificationResponse notification) started,
    required TResult Function(String? id) viewed,
    required TResult Function(String? id) deleted,
    required TResult Function(String? id) markAsRead,
    required TResult Function() initial,
  }) {
    return deleted(id);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(NotificationResponse notification)? started,
    TResult? Function(String? id)? viewed,
    TResult? Function(String? id)? deleted,
    TResult? Function(String? id)? markAsRead,
    TResult? Function()? initial,
  }) {
    return deleted?.call(id);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(NotificationResponse notification)? started,
    TResult Function(String? id)? viewed,
    TResult Function(String? id)? deleted,
    TResult Function(String? id)? markAsRead,
    TResult Function()? initial,
    required TResult orElse(),
  }) {
    if (deleted != null) {
      return deleted(id);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Viewed value) viewed,
    required TResult Function(_Deleted value) deleted,
    required TResult Function(_MarkedAsRead value) markAsRead,
    required TResult Function(_Initial value) initial,
  }) {
    return deleted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Viewed value)? viewed,
    TResult? Function(_Deleted value)? deleted,
    TResult? Function(_MarkedAsRead value)? markAsRead,
    TResult? Function(_Initial value)? initial,
  }) {
    return deleted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Viewed value)? viewed,
    TResult Function(_Deleted value)? deleted,
    TResult Function(_MarkedAsRead value)? markAsRead,
    TResult Function(_Initial value)? initial,
    required TResult orElse(),
  }) {
    if (deleted != null) {
      return deleted(this);
    }
    return orElse();
  }
}

abstract class _Deleted implements SingleNotificationEvent {
  const factory _Deleted({final String? id}) = _$DeletedImpl;

  String? get id;

  /// Create a copy of SingleNotificationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeletedImplCopyWith<_$DeletedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$MarkedAsReadImplCopyWith<$Res> {
  factory _$$MarkedAsReadImplCopyWith(
          _$MarkedAsReadImpl value, $Res Function(_$MarkedAsReadImpl) then) =
      __$$MarkedAsReadImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? id});
}

/// @nodoc
class __$$MarkedAsReadImplCopyWithImpl<$Res>
    extends _$SingleNotificationEventCopyWithImpl<$Res, _$MarkedAsReadImpl>
    implements _$$MarkedAsReadImplCopyWith<$Res> {
  __$$MarkedAsReadImplCopyWithImpl(
      _$MarkedAsReadImpl _value, $Res Function(_$MarkedAsReadImpl) _then)
      : super(_value, _then);

  /// Create a copy of SingleNotificationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
  }) {
    return _then(_$MarkedAsReadImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$MarkedAsReadImpl implements _MarkedAsRead {
  const _$MarkedAsReadImpl({this.id});

  @override
  final String? id;

  @override
  String toString() {
    return 'SingleNotificationEvent.markAsRead(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MarkedAsReadImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id);

  /// Create a copy of SingleNotificationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MarkedAsReadImplCopyWith<_$MarkedAsReadImpl> get copyWith =>
      __$$MarkedAsReadImplCopyWithImpl<_$MarkedAsReadImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(NotificationResponse notification) started,
    required TResult Function(String? id) viewed,
    required TResult Function(String? id) deleted,
    required TResult Function(String? id) markAsRead,
    required TResult Function() initial,
  }) {
    return markAsRead(id);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(NotificationResponse notification)? started,
    TResult? Function(String? id)? viewed,
    TResult? Function(String? id)? deleted,
    TResult? Function(String? id)? markAsRead,
    TResult? Function()? initial,
  }) {
    return markAsRead?.call(id);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(NotificationResponse notification)? started,
    TResult Function(String? id)? viewed,
    TResult Function(String? id)? deleted,
    TResult Function(String? id)? markAsRead,
    TResult Function()? initial,
    required TResult orElse(),
  }) {
    if (markAsRead != null) {
      return markAsRead(id);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Viewed value) viewed,
    required TResult Function(_Deleted value) deleted,
    required TResult Function(_MarkedAsRead value) markAsRead,
    required TResult Function(_Initial value) initial,
  }) {
    return markAsRead(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Viewed value)? viewed,
    TResult? Function(_Deleted value)? deleted,
    TResult? Function(_MarkedAsRead value)? markAsRead,
    TResult? Function(_Initial value)? initial,
  }) {
    return markAsRead?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Viewed value)? viewed,
    TResult Function(_Deleted value)? deleted,
    TResult Function(_MarkedAsRead value)? markAsRead,
    TResult Function(_Initial value)? initial,
    required TResult orElse(),
  }) {
    if (markAsRead != null) {
      return markAsRead(this);
    }
    return orElse();
  }
}

abstract class _MarkedAsRead implements SingleNotificationEvent {
  const factory _MarkedAsRead({final String? id}) = _$MarkedAsReadImpl;

  String? get id;

  /// Create a copy of SingleNotificationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MarkedAsReadImplCopyWith<_$MarkedAsReadImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$SingleNotificationEventCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of SingleNotificationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'SingleNotificationEvent.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(NotificationResponse notification) started,
    required TResult Function(String? id) viewed,
    required TResult Function(String? id) deleted,
    required TResult Function(String? id) markAsRead,
    required TResult Function() initial,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(NotificationResponse notification)? started,
    TResult? Function(String? id)? viewed,
    TResult? Function(String? id)? deleted,
    TResult? Function(String? id)? markAsRead,
    TResult? Function()? initial,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(NotificationResponse notification)? started,
    TResult Function(String? id)? viewed,
    TResult Function(String? id)? deleted,
    TResult Function(String? id)? markAsRead,
    TResult Function()? initial,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Viewed value) viewed,
    required TResult Function(_Deleted value) deleted,
    required TResult Function(_MarkedAsRead value) markAsRead,
    required TResult Function(_Initial value) initial,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Viewed value)? viewed,
    TResult? Function(_Deleted value)? deleted,
    TResult? Function(_MarkedAsRead value)? markAsRead,
    TResult? Function(_Initial value)? initial,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Viewed value)? viewed,
    TResult Function(_Deleted value)? deleted,
    TResult Function(_MarkedAsRead value)? markAsRead,
    TResult Function(_Initial value)? initial,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements SingleNotificationEvent {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
mixin _$SingleNotificationState {
  NotificationResponse? get notification => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;
  FormzSubmissionStatus get status => throw _privateConstructorUsedError;

  /// Create a copy of SingleNotificationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SingleNotificationStateCopyWith<SingleNotificationState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SingleNotificationStateCopyWith<$Res> {
  factory $SingleNotificationStateCopyWith(SingleNotificationState value,
          $Res Function(SingleNotificationState) then) =
      _$SingleNotificationStateCopyWithImpl<$Res, SingleNotificationState>;
  @useResult
  $Res call(
      {NotificationResponse? notification,
      String? errorMessage,
      FormzSubmissionStatus status});

  $NotificationResponseCopyWith<$Res>? get notification;
}

/// @nodoc
class _$SingleNotificationStateCopyWithImpl<$Res,
        $Val extends SingleNotificationState>
    implements $SingleNotificationStateCopyWith<$Res> {
  _$SingleNotificationStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SingleNotificationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notification = freezed,
    Object? errorMessage = freezed,
    Object? status = null,
  }) {
    return _then(_value.copyWith(
      notification: freezed == notification
          ? _value.notification
          : notification // ignore: cast_nullable_to_non_nullable
              as NotificationResponse?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as FormzSubmissionStatus,
    ) as $Val);
  }

  /// Create a copy of SingleNotificationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $NotificationResponseCopyWith<$Res>? get notification {
    if (_value.notification == null) {
      return null;
    }

    return $NotificationResponseCopyWith<$Res>(_value.notification!, (value) {
      return _then(_value.copyWith(notification: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SingleNotificationStateImplCopyWith<$Res>
    implements $SingleNotificationStateCopyWith<$Res> {
  factory _$$SingleNotificationStateImplCopyWith(
          _$SingleNotificationStateImpl value,
          $Res Function(_$SingleNotificationStateImpl) then) =
      __$$SingleNotificationStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {NotificationResponse? notification,
      String? errorMessage,
      FormzSubmissionStatus status});

  @override
  $NotificationResponseCopyWith<$Res>? get notification;
}

/// @nodoc
class __$$SingleNotificationStateImplCopyWithImpl<$Res>
    extends _$SingleNotificationStateCopyWithImpl<$Res,
        _$SingleNotificationStateImpl>
    implements _$$SingleNotificationStateImplCopyWith<$Res> {
  __$$SingleNotificationStateImplCopyWithImpl(
      _$SingleNotificationStateImpl _value,
      $Res Function(_$SingleNotificationStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of SingleNotificationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notification = freezed,
    Object? errorMessage = freezed,
    Object? status = null,
  }) {
    return _then(_$SingleNotificationStateImpl(
      notification: freezed == notification
          ? _value.notification
          : notification // ignore: cast_nullable_to_non_nullable
              as NotificationResponse?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as FormzSubmissionStatus,
    ));
  }
}

/// @nodoc

class _$SingleNotificationStateImpl implements _SingleNotificationState {
  const _$SingleNotificationStateImpl(
      {this.notification,
      this.errorMessage,
      this.status = FormzSubmissionStatus.initial});

  @override
  final NotificationResponse? notification;
  @override
  final String? errorMessage;
  @override
  @JsonKey()
  final FormzSubmissionStatus status;

  @override
  String toString() {
    return 'SingleNotificationState(notification: $notification, errorMessage: $errorMessage, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SingleNotificationStateImpl &&
            (identical(other.notification, notification) ||
                other.notification == notification) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.status, status) || other.status == status));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, notification, errorMessage, status);

  /// Create a copy of SingleNotificationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SingleNotificationStateImplCopyWith<_$SingleNotificationStateImpl>
      get copyWith => __$$SingleNotificationStateImplCopyWithImpl<
          _$SingleNotificationStateImpl>(this, _$identity);
}

abstract class _SingleNotificationState implements SingleNotificationState {
  const factory _SingleNotificationState(
      {final NotificationResponse? notification,
      final String? errorMessage,
      final FormzSubmissionStatus status}) = _$SingleNotificationStateImpl;

  @override
  NotificationResponse? get notification;
  @override
  String? get errorMessage;
  @override
  FormzSubmissionStatus get status;

  /// Create a copy of SingleNotificationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SingleNotificationStateImplCopyWith<_$SingleNotificationStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
