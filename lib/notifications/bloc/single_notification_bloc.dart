import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:formz/formz.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:froggytalk/notifications/notifications.dart';

part 'single_notification_bloc.freezed.dart';
part 'single_notification_event.dart';
part 'single_notification_state.dart';

class SingleNotificationBloc
    extends Bloc<SingleNotificationEvent, SingleNotificationState> {
  SingleNotificationBloc() : super(const SingleNotificationState()) {
    on<_Started>(_mapStartedToState);
    on<_Viewed>(_mapViewedToState);
    on<_Deleted>(_mapDeletedToState);
    on<_MarkedAsRead>(_mapMarkedAsReadToState);
    on<_Initial>(_onInitial);
  }

  final _markedAsReadRepository = MarkAsReadNotificationRepository();
  final _deletedRepository = DeleteNotificationRepository();

  FutureOr<void> _onInitial(
    _Initial event,
    Emitter<SingleNotificationState> emit,
  ) {
    emit(const SingleNotificationState());
  }

  FutureOr<void> _mapStartedToState(
    _Started event,
    Emitter<SingleNotificationState> emit,
  ) {
    emit(
      state.copyWith(
        notification: event.notification,
        status: FormzSubmissionStatus.initial,
      ),
    );
  }

  Future<void> _mapViewedToState(
    _Viewed event,
    Emitter<SingleNotificationState> emit,
  ) async {}

  FutureOr<void> _mapDeletedToState(
    _Deleted event,
    Emitter<SingleNotificationState> emit,
  ) async {
    emit(state.copyWith(status: FormzSubmissionStatus.inProgress));

    final id2 = state.notification?.id;
    final response = await _deletedRepository.execute(id2 ?? '');
    if (response.isRight) {
      emit(
        state.copyWith(
          // notifications: mappedNotifications ?? [],
          status: FormzSubmissionStatus.success,
        ),
      );
    } else {
      final message = response.left.message;
      final code = response.left.code;

      emit(
        state.copyWith(
          // notifications: [],
          status: FormzSubmissionStatus.failure,
          errorMessage: code == 'internet-out' ? 'internet-out' : message,
        ),
      );
    }
  }

  Future<void> _mapMarkedAsReadToState(
    _MarkedAsRead event,
    Emitter<SingleNotificationState> emit,
  ) async {
    final response = await _markedAsReadRepository.execute(
      event.id ?? state.notification?.id,
    );

    if (response.isRight) {
      emit(
        state.copyWith(
          errorMessage: null,
        ),
      );
    } else {
      emit(
        state.copyWith(
          errorMessage: response.left.message,
        ),
      );
    }
  }
}
