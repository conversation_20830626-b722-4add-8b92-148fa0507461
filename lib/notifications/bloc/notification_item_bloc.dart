import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'notification_item_event.dart';
part 'notification_item_state.dart';
part 'notification_item_bloc.freezed.dart';

class NotificationItemBloc
    extends Bloc<NotificationItemEvent, NotificationItemState> {
  NotificationItemBloc() : super(const _Initial()) {
    on<NotificationItemEvent>((event, emit) {
      // TODO(peterson): implement event handler
    });
  }
}
