// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'notifications_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$NotificationsEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String type) started,
    required TResult Function() reloaded,
    required TResult Function(dynamic id) deletedNotification,
    required TResult Function() reset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String type)? started,
    TResult? Function()? reloaded,
    TResult? Function(dynamic id)? deletedNotification,
    TResult? Function()? reset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String type)? started,
    TResult Function()? reloaded,
    TResult Function(dynamic id)? deletedNotification,
    TResult Function()? reset,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Reload value) reloaded,
    required TResult Function(_RemoveFromList value) deletedNotification,
    required TResult Function(_Initial value) reset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Reload value)? reloaded,
    TResult? Function(_RemoveFromList value)? deletedNotification,
    TResult? Function(_Initial value)? reset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Reload value)? reloaded,
    TResult Function(_RemoveFromList value)? deletedNotification,
    TResult Function(_Initial value)? reset,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationsEventCopyWith<$Res> {
  factory $NotificationsEventCopyWith(
          NotificationsEvent value, $Res Function(NotificationsEvent) then) =
      _$NotificationsEventCopyWithImpl<$Res, NotificationsEvent>;
}

/// @nodoc
class _$NotificationsEventCopyWithImpl<$Res, $Val extends NotificationsEvent>
    implements $NotificationsEventCopyWith<$Res> {
  _$NotificationsEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NotificationsEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$StartedImplCopyWith<$Res> {
  factory _$$StartedImplCopyWith(
          _$StartedImpl value, $Res Function(_$StartedImpl) then) =
      __$$StartedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String type});
}

/// @nodoc
class __$$StartedImplCopyWithImpl<$Res>
    extends _$NotificationsEventCopyWithImpl<$Res, _$StartedImpl>
    implements _$$StartedImplCopyWith<$Res> {
  __$$StartedImplCopyWithImpl(
      _$StartedImpl _value, $Res Function(_$StartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of NotificationsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
  }) {
    return _then(_$StartedImpl(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$StartedImpl implements _Started {
  const _$StartedImpl({this.type = 'all'});

  @override
  @JsonKey()
  final String type;

  @override
  String toString() {
    return 'NotificationsEvent.started(type: $type)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StartedImpl &&
            (identical(other.type, type) || other.type == type));
  }

  @override
  int get hashCode => Object.hash(runtimeType, type);

  /// Create a copy of NotificationsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StartedImplCopyWith<_$StartedImpl> get copyWith =>
      __$$StartedImplCopyWithImpl<_$StartedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String type) started,
    required TResult Function() reloaded,
    required TResult Function(dynamic id) deletedNotification,
    required TResult Function() reset,
  }) {
    return started(type);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String type)? started,
    TResult? Function()? reloaded,
    TResult? Function(dynamic id)? deletedNotification,
    TResult? Function()? reset,
  }) {
    return started?.call(type);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String type)? started,
    TResult Function()? reloaded,
    TResult Function(dynamic id)? deletedNotification,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started(type);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Reload value) reloaded,
    required TResult Function(_RemoveFromList value) deletedNotification,
    required TResult Function(_Initial value) reset,
  }) {
    return started(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Reload value)? reloaded,
    TResult? Function(_RemoveFromList value)? deletedNotification,
    TResult? Function(_Initial value)? reset,
  }) {
    return started?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Reload value)? reloaded,
    TResult Function(_RemoveFromList value)? deletedNotification,
    TResult Function(_Initial value)? reset,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started(this);
    }
    return orElse();
  }
}

abstract class _Started implements NotificationsEvent {
  const factory _Started({final String type}) = _$StartedImpl;

  String get type;

  /// Create a copy of NotificationsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StartedImplCopyWith<_$StartedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ReloadImplCopyWith<$Res> {
  factory _$$ReloadImplCopyWith(
          _$ReloadImpl value, $Res Function(_$ReloadImpl) then) =
      __$$ReloadImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ReloadImplCopyWithImpl<$Res>
    extends _$NotificationsEventCopyWithImpl<$Res, _$ReloadImpl>
    implements _$$ReloadImplCopyWith<$Res> {
  __$$ReloadImplCopyWithImpl(
      _$ReloadImpl _value, $Res Function(_$ReloadImpl) _then)
      : super(_value, _then);

  /// Create a copy of NotificationsEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ReloadImpl implements _Reload {
  const _$ReloadImpl();

  @override
  String toString() {
    return 'NotificationsEvent.reloaded()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ReloadImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String type) started,
    required TResult Function() reloaded,
    required TResult Function(dynamic id) deletedNotification,
    required TResult Function() reset,
  }) {
    return reloaded();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String type)? started,
    TResult? Function()? reloaded,
    TResult? Function(dynamic id)? deletedNotification,
    TResult? Function()? reset,
  }) {
    return reloaded?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String type)? started,
    TResult Function()? reloaded,
    TResult Function(dynamic id)? deletedNotification,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (reloaded != null) {
      return reloaded();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Reload value) reloaded,
    required TResult Function(_RemoveFromList value) deletedNotification,
    required TResult Function(_Initial value) reset,
  }) {
    return reloaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Reload value)? reloaded,
    TResult? Function(_RemoveFromList value)? deletedNotification,
    TResult? Function(_Initial value)? reset,
  }) {
    return reloaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Reload value)? reloaded,
    TResult Function(_RemoveFromList value)? deletedNotification,
    TResult Function(_Initial value)? reset,
    required TResult orElse(),
  }) {
    if (reloaded != null) {
      return reloaded(this);
    }
    return orElse();
  }
}

abstract class _Reload implements NotificationsEvent {
  const factory _Reload() = _$ReloadImpl;
}

/// @nodoc
abstract class _$$RemoveFromListImplCopyWith<$Res> {
  factory _$$RemoveFromListImplCopyWith(_$RemoveFromListImpl value,
          $Res Function(_$RemoveFromListImpl) then) =
      __$$RemoveFromListImplCopyWithImpl<$Res>;
  @useResult
  $Res call({dynamic id});
}

/// @nodoc
class __$$RemoveFromListImplCopyWithImpl<$Res>
    extends _$NotificationsEventCopyWithImpl<$Res, _$RemoveFromListImpl>
    implements _$$RemoveFromListImplCopyWith<$Res> {
  __$$RemoveFromListImplCopyWithImpl(
      _$RemoveFromListImpl _value, $Res Function(_$RemoveFromListImpl) _then)
      : super(_value, _then);

  /// Create a copy of NotificationsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
  }) {
    return _then(_$RemoveFromListImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ));
  }
}

/// @nodoc

class _$RemoveFromListImpl implements _RemoveFromList {
  const _$RemoveFromListImpl({required this.id});

  @override
  final dynamic id;

  @override
  String toString() {
    return 'NotificationsEvent.deletedNotification(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RemoveFromListImpl &&
            const DeepCollectionEquality().equals(other.id, id));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(id));

  /// Create a copy of NotificationsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RemoveFromListImplCopyWith<_$RemoveFromListImpl> get copyWith =>
      __$$RemoveFromListImplCopyWithImpl<_$RemoveFromListImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String type) started,
    required TResult Function() reloaded,
    required TResult Function(dynamic id) deletedNotification,
    required TResult Function() reset,
  }) {
    return deletedNotification(id);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String type)? started,
    TResult? Function()? reloaded,
    TResult? Function(dynamic id)? deletedNotification,
    TResult? Function()? reset,
  }) {
    return deletedNotification?.call(id);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String type)? started,
    TResult Function()? reloaded,
    TResult Function(dynamic id)? deletedNotification,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (deletedNotification != null) {
      return deletedNotification(id);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Reload value) reloaded,
    required TResult Function(_RemoveFromList value) deletedNotification,
    required TResult Function(_Initial value) reset,
  }) {
    return deletedNotification(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Reload value)? reloaded,
    TResult? Function(_RemoveFromList value)? deletedNotification,
    TResult? Function(_Initial value)? reset,
  }) {
    return deletedNotification?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Reload value)? reloaded,
    TResult Function(_RemoveFromList value)? deletedNotification,
    TResult Function(_Initial value)? reset,
    required TResult orElse(),
  }) {
    if (deletedNotification != null) {
      return deletedNotification(this);
    }
    return orElse();
  }
}

abstract class _RemoveFromList implements NotificationsEvent {
  const factory _RemoveFromList({required final dynamic id}) =
      _$RemoveFromListImpl;

  dynamic get id;

  /// Create a copy of NotificationsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RemoveFromListImplCopyWith<_$RemoveFromListImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$NotificationsEventCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of NotificationsEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'NotificationsEvent.reset()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String type) started,
    required TResult Function() reloaded,
    required TResult Function(dynamic id) deletedNotification,
    required TResult Function() reset,
  }) {
    return reset();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String type)? started,
    TResult? Function()? reloaded,
    TResult? Function(dynamic id)? deletedNotification,
    TResult? Function()? reset,
  }) {
    return reset?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String type)? started,
    TResult Function()? reloaded,
    TResult Function(dynamic id)? deletedNotification,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (reset != null) {
      return reset();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Reload value) reloaded,
    required TResult Function(_RemoveFromList value) deletedNotification,
    required TResult Function(_Initial value) reset,
  }) {
    return reset(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Reload value)? reloaded,
    TResult? Function(_RemoveFromList value)? deletedNotification,
    TResult? Function(_Initial value)? reset,
  }) {
    return reset?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Reload value)? reloaded,
    TResult Function(_RemoveFromList value)? deletedNotification,
    TResult Function(_Initial value)? reset,
    required TResult orElse(),
  }) {
    if (reset != null) {
      return reset(this);
    }
    return orElse();
  }
}

abstract class _Initial implements NotificationsEvent {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
mixin _$NotificationsState {
  String get notificationType => throw _privateConstructorUsedError;
  int get unreadNotificationCount => throw _privateConstructorUsedError;
  List<NotificationResponse> get notifications =>
      throw _privateConstructorUsedError;
  FormzSubmissionStatus get status => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;

  /// Create a copy of NotificationsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $NotificationsStateCopyWith<NotificationsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationsStateCopyWith<$Res> {
  factory $NotificationsStateCopyWith(
          NotificationsState value, $Res Function(NotificationsState) then) =
      _$NotificationsStateCopyWithImpl<$Res, NotificationsState>;
  @useResult
  $Res call(
      {String notificationType,
      int unreadNotificationCount,
      List<NotificationResponse> notifications,
      FormzSubmissionStatus status,
      String? errorMessage});
}

/// @nodoc
class _$NotificationsStateCopyWithImpl<$Res, $Val extends NotificationsState>
    implements $NotificationsStateCopyWith<$Res> {
  _$NotificationsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NotificationsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notificationType = null,
    Object? unreadNotificationCount = null,
    Object? notifications = null,
    Object? status = null,
    Object? errorMessage = freezed,
  }) {
    return _then(_value.copyWith(
      notificationType: null == notificationType
          ? _value.notificationType
          : notificationType // ignore: cast_nullable_to_non_nullable
              as String,
      unreadNotificationCount: null == unreadNotificationCount
          ? _value.unreadNotificationCount
          : unreadNotificationCount // ignore: cast_nullable_to_non_nullable
              as int,
      notifications: null == notifications
          ? _value.notifications
          : notifications // ignore: cast_nullable_to_non_nullable
              as List<NotificationResponse>,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as FormzSubmissionStatus,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NotificationsStateImplCopyWith<$Res>
    implements $NotificationsStateCopyWith<$Res> {
  factory _$$NotificationsStateImplCopyWith(_$NotificationsStateImpl value,
          $Res Function(_$NotificationsStateImpl) then) =
      __$$NotificationsStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String notificationType,
      int unreadNotificationCount,
      List<NotificationResponse> notifications,
      FormzSubmissionStatus status,
      String? errorMessage});
}

/// @nodoc
class __$$NotificationsStateImplCopyWithImpl<$Res>
    extends _$NotificationsStateCopyWithImpl<$Res, _$NotificationsStateImpl>
    implements _$$NotificationsStateImplCopyWith<$Res> {
  __$$NotificationsStateImplCopyWithImpl(_$NotificationsStateImpl _value,
      $Res Function(_$NotificationsStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of NotificationsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notificationType = null,
    Object? unreadNotificationCount = null,
    Object? notifications = null,
    Object? status = null,
    Object? errorMessage = freezed,
  }) {
    return _then(_$NotificationsStateImpl(
      notificationType: null == notificationType
          ? _value.notificationType
          : notificationType // ignore: cast_nullable_to_non_nullable
              as String,
      unreadNotificationCount: null == unreadNotificationCount
          ? _value.unreadNotificationCount
          : unreadNotificationCount // ignore: cast_nullable_to_non_nullable
              as int,
      notifications: null == notifications
          ? _value._notifications
          : notifications // ignore: cast_nullable_to_non_nullable
              as List<NotificationResponse>,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as FormzSubmissionStatus,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$NotificationsStateImpl implements _NotificationsState {
  const _$NotificationsStateImpl(
      {this.notificationType = 'all',
      this.unreadNotificationCount = 0,
      final List<NotificationResponse> notifications = const [],
      this.status = FormzSubmissionStatus.initial,
      this.errorMessage})
      : _notifications = notifications;

  @override
  @JsonKey()
  final String notificationType;
  @override
  @JsonKey()
  final int unreadNotificationCount;
  final List<NotificationResponse> _notifications;
  @override
  @JsonKey()
  List<NotificationResponse> get notifications {
    if (_notifications is EqualUnmodifiableListView) return _notifications;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_notifications);
  }

  @override
  @JsonKey()
  final FormzSubmissionStatus status;
  @override
  final String? errorMessage;

  @override
  String toString() {
    return 'NotificationsState(notificationType: $notificationType, unreadNotificationCount: $unreadNotificationCount, notifications: $notifications, status: $status, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotificationsStateImpl &&
            (identical(other.notificationType, notificationType) ||
                other.notificationType == notificationType) &&
            (identical(
                    other.unreadNotificationCount, unreadNotificationCount) ||
                other.unreadNotificationCount == unreadNotificationCount) &&
            const DeepCollectionEquality()
                .equals(other._notifications, _notifications) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      notificationType,
      unreadNotificationCount,
      const DeepCollectionEquality().hash(_notifications),
      status,
      errorMessage);

  /// Create a copy of NotificationsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NotificationsStateImplCopyWith<_$NotificationsStateImpl> get copyWith =>
      __$$NotificationsStateImplCopyWithImpl<_$NotificationsStateImpl>(
          this, _$identity);
}

abstract class _NotificationsState implements NotificationsState {
  const factory _NotificationsState(
      {final String notificationType,
      final int unreadNotificationCount,
      final List<NotificationResponse> notifications,
      final FormzSubmissionStatus status,
      final String? errorMessage}) = _$NotificationsStateImpl;

  @override
  String get notificationType;
  @override
  int get unreadNotificationCount;
  @override
  List<NotificationResponse> get notifications;
  @override
  FormzSubmissionStatus get status;
  @override
  String? get errorMessage;

  /// Create a copy of NotificationsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NotificationsStateImplCopyWith<_$NotificationsStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
