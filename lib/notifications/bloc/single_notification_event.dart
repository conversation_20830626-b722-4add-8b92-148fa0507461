part of 'single_notification_bloc.dart';

@freezed
class SingleNotificationEvent with _$SingleNotificationEvent {
  const factory SingleNotificationEvent.started({
    required NotificationResponse notification,
  }) = _Started;

  const factory SingleNotificationEvent.viewed({String? id}) = _Viewed;

  const factory SingleNotificationEvent.deleted({String? id}) = _Deleted;

  const factory SingleNotificationEvent.markAsRead({String? id}) =
      _MarkedAsRead;

  const factory SingleNotificationEvent.initial() = _Initial;
}
