import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:formz/formz.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:froggytalk/notifications/notifications.dart';

part 'notifications_bloc.freezed.dart';
part 'notifications_event.dart';
part 'notifications_state.dart';

class NotificationsBloc extends Bloc<NotificationsEvent, NotificationsState> {
  NotificationsBloc() : super(const NotificationsState()) {
    on<_Started>(_mapStartedToState);
    on<_RemoveFromList>(_mapRemoveFromListToState);
    on<_Reload>(_mapReloadToState);
    on<_Initial>(_onInitial);
  }

  final _repository = ListAllNotificationsRepository();

  FutureOr<void> _onInitial(
    _Initial event,
    Emitter<NotificationsState> emit,
  ) {
    emit(const NotificationsState());
  }

  FutureOr<void> _mapStartedToState(
    _Started event,
    Emitter<NotificationsState> emit,
  ) async {
    final type = event.type;
    emit(
      state.copyWith(
        status: FormzSubmissionStatus.inProgress,
        notificationType: type,
      ),
    );

    await _loadNotifications(emit, type: type);
  }

  Future<void> _loadNotifications(
    Emitter<NotificationsState> emit, {
    String type = 'all',
  }) async {
    final response = await _repository.execute(type: type);
    if (response.isRight) {
      final mappedNotifications =
          response.right.data?.map((item) => item.attributes!).toList();

      if (type == 'unread') {
        emit(
          state.copyWith(
            unreadNotificationCount: (mappedNotifications ?? []).length,
          ),
        );
      }

      emit(
        state.copyWith(
          notifications: mappedNotifications ?? [],
          status: FormzSubmissionStatus.success,
        ),
      );
    } else {
      final message = response.left.message;
      final code = response.left.code;

      emit(
        state.copyWith(
          notifications: [],
          status: code == 'internet-out'
              ? FormzSubmissionStatus.canceled
              : FormzSubmissionStatus.failure,
          errorMessage: code == 'internet-out' ? 'internet-out' : message,
        ),
      );
    }
  }

  Future<void> _mapReloadToState(
    _Reload event,
    Emitter<NotificationsState> emit,
  ) async {
    await _loadNotifications(emit);
  }

  FutureOr<void> _mapRemoveFromListToState(
    _RemoveFromList event,
    Emitter<NotificationsState> emit,
  ) {
    emit(
      state.copyWith(
        notifications:
            state.notifications.where((item) => item.id != event.id).toList(),
        status: FormzSubmissionStatus.success,
      ),
    );
  }
}
