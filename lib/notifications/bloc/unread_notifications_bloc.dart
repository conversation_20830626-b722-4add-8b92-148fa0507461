import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:formz/formz.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:froggytalk/notifications/notifications.dart';

part 'unread_notifications_event.dart';
part 'unread_notifications_state.dart';
part 'unread_notifications_bloc.freezed.dart';

class UnreadNotificationsBloc
    extends Bloc<UnreadNotificationsEvent, UnreadNotificationsState> {
  UnreadNotificationsBloc() : super(UnreadNotificationsState.initial()) {
    on<_Started>(_mapStartedToState);
    on<_Reload>(_mapReloadToState);
    on<_Reset>(_mapResetToState);
  }

  final _repository = ListAllNotificationsRepository();

  Future<void> _mapStartedToState(
    _Started event,
    Emitter<UnreadNotificationsState> emit,
  ) async {
    emit(
      state.copyWith(
        status: FormzSubmissionStatus.inProgress,
      ),
    );

    await _loadNotifications(emit);
  }

  Future<void> _loadNotifications(
    Emitter<UnreadNotificationsState> emit,
  ) async {
    final response = await _repository.execute(type: 'unread');
    if (response.isRight) {
      final mappedNotifications =
          response.right.data?.map((item) => item.attributes!).toList();

      emit(
        state.copyWith(
          notifications: mappedNotifications ?? [],
          status: FormzSubmissionStatus.success,
        ),
      );
    } else {
      final message = response.left.message;
      final code = response.left.code;

      emit(
        state.copyWith(
          notifications: [],
          status: code == 'internet-out'
              ? FormzSubmissionStatus.canceled
              : FormzSubmissionStatus.failure,
          errorMessage: code == 'internet-out' ? 'internet-out' : message,
        ),
      );
    }
  }

  Future<void> _mapReloadToState(
    _Reload event,
    Emitter<UnreadNotificationsState> emit,
  ) async {
    await _loadNotifications(emit);
  }

  FutureOr<void> _mapResetToState(
    _Reset event,
    Emitter<UnreadNotificationsState> emit,
  ) {
    emit(UnreadNotificationsState.initial());
  }
}
