part of 'all_notifications_bloc.dart';

@freezed
class AllNotificationsState with _$AllNotificationsState {
  factory AllNotificationsState({
    @Default([]) List<NotificationResponse> notifications,
    @Default(FormzSubmissionStatus.initial) FormzSubmissionStatus status,
    String? errorMessage,
  }) = _AllNotificationsState;

  factory AllNotificationsState.initial() => AllNotificationsState();
}
