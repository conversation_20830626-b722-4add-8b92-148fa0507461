import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:formz/formz.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:froggytalk/notifications/notifications.dart';

part 'all_notifications_bloc.freezed.dart';
part 'all_notifications_event.dart';
part 'all_notifications_state.dart';

class AllNotificationsBloc
    extends Bloc<AllNotificationsEvent, AllNotificationsState> {
  AllNotificationsBloc() : super(AllNotificationsState.initial()) {
    on<_Started>(_mapStartedToState);
    on<_Reload>(_mapReloadToState);
    on<_Reset>(_mapResetToState);
  }

  final _repository = ListAllNotificationsRepository();

  Future<void> _mapStartedToState(
    _Started event,
    Emitter<AllNotificationsState> emit,
  ) async {
    emit(
      state.copyWith(
        status: FormzSubmissionStatus.inProgress,
      ),
    );

    await _loadNotifications(emit);
  }

  Future<void> _loadNotifications(Emitter<AllNotificationsState> emit) async {
    final response = await _repository.execute();
    if (response.isRight) {
      final mappedNotifications =
          response.right.data?.map((item) => item.attributes!).toList();

      emit(
        state.copyWith(
          notifications: mappedNotifications ?? [],
          status: FormzSubmissionStatus.success,
        ),
      );
    } else {
      final message = response.left.message;
      final code = response.left.code;

      emit(
        state.copyWith(
          notifications: [],
          status: code == 'internet-out'
              ? FormzSubmissionStatus.canceled
              : FormzSubmissionStatus.failure,
          errorMessage: code == 'internet-out' ? 'internet-out' : message,
        ),
      );
    }
  }

  Future<void> _mapReloadToState(
    _Reload event,
    Emitter<AllNotificationsState> emit,
  ) async {
    await _loadNotifications(emit);
  }

  FutureOr<void> _mapResetToState(
    _Reset event,
    Emitter<AllNotificationsState> emit,
  ) {
    emit(AllNotificationsState.initial());
  }
}
