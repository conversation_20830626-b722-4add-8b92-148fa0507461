part of 'notifications_bloc.dart';

@freezed
class NotificationsState with _$NotificationsState {
  const factory NotificationsState({
    @Default('all') String notificationType,
    @Default(0) int unreadNotificationCount,
    @Default([]) List<NotificationResponse> notifications,
    @Default(FormzSubmissionStatus.initial) FormzSubmissionStatus status,
    String? errorMessage,
  }) = _NotificationsState;
}
