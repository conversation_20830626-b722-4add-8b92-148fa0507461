import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:formz/formz.dart';
import 'package:froggy_icons/froggy_icons.dart';
import 'package:froggytalk/notifications/notifications.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:timeago/timeago.dart' as timeago;

class NotificationItem extends HookWidget {
  const NotificationItem({
    required this.parentBloc,
    required this.notification,
    super.key,
  });

  final dynamic parentBloc;
  final NotificationResponse notification;

  @override
  Widget build(BuildContext context) {
    final allNotificationsBloc = context.read<AllNotificationsBloc>();
    final unreadNotificationsBloc = context.read<UnreadNotificationsBloc>();
    final languageCode = Localizations.localeOf(context).languageCode;
    final createdAtInSecs = useMemoized(
      () {
        final createdAt = notification.createdAt;
        if (createdAt != null) {
          return formatTimeAgo(
            createdAt,
            locale: languageCode,
          );
          // return formatTimeAgo('2024-10-28T21:43:04.000000Z');
        }
      },
      [notification],
    );
    final isRead = useMemoized(() => notification.readAt == null);

    return BlocProvider(
      create: (context) => SingleNotificationBloc()
        ..add(SingleNotificationEvent.started(notification: notification)),
      child: BlocConsumer<SingleNotificationBloc, SingleNotificationState>(
        listener: (context, state) {
          if (state.status.isSuccess) {
            // if (parentBloc is AllNotificationsBloc) {
            // } else if (parentBloc is UnreadNotificationsBloc) {}
            allNotificationsBloc.add(
              const AllNotificationsEvent.started(),
            );
            unreadNotificationsBloc.add(
              const UnreadNotificationsEvent.started(),
            );
          }
        },
        builder: (context, notificationState) {
          final singleNotificationBloc = context.read<SingleNotificationBloc>();

          return Skeletonizer(
            enabled: notificationState.status.isInProgress,
            child: Dismissible(
              key: Key(notification.id.toString()),
              onDismissed: (direction) {
                if (DismissDirection.endToStart == direction) {
                  // swiping from right to left to delete
                  singleNotificationBloc
                      .add(const SingleNotificationEvent.deleted());
                }
              },
              direction: DismissDirection.endToStart,
              background: Container(
                alignment: Alignment.centerRight,
                color: FroggyColors.error,
                child: FroggyIconsList.delete.toWidget(),
              ),
              child: ListTile(
                tileColor: !isRead ? null : FroggyColors.froggyLighterGreen,
                leading: FroggyIconsList.notificationBell.toWidget(
                  color: FroggyColors.primary,
                ),
                onTap: () => onTapSingleNotification(
                  context,
                  context.read<SingleNotificationBloc>(),
                ),
                title: Text(notificationState.notification?.title ?? ''),
                trailing: Text('$createdAtInSecs'),
              ),
            ),
          );
        },
      ),
    );
  }

  void onTapSingleNotification(
    BuildContext context,
    SingleNotificationBloc bloc,
  ) {
    final id = (notification.id ?? '') as String;
    bloc.add(SingleNotificationEvent.markAsRead(id: id));
    Navigator.of(context).push(NotificationDetailsPage.route(notification));
  }
}

String formatTimeAgo(String isoString, {String locale = 'en'}) {
  final inputTime = DateTime.parse(isoString).toUtc(); // Ensure input is in UTC
  // timeago.setDefaultLocale('am');

  return timeago.format(
    inputTime,
    // locale: 'am',
  );

  // final dateFormatter = DateFormat("d'th'/MMM/yyyy 'at' h:mma", 'ti');
  // final dateFormatter = DateFormat("d'th'/MMM/yyyy", 'ti');
  // final formattedDate = dateFormatter.format(inputTime.toLocal());
  // return formattedDate;
}
