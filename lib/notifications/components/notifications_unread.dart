import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';
import 'package:froggy_icons/froggy_icons.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:froggytalk/notifications/notifications.dart';

class UnreadNotifications extends StatefulWidget {
  const UnreadNotifications({super.key});

  @override
  State<UnreadNotifications> createState() => _UnreadNotificationsState();
}

class _UnreadNotificationsState extends State<UnreadNotifications> {
  UnreadNotificationsBloc? _bloc;

  @override
  void initState() {
    super.initState();
    _bloc = context.read<UnreadNotificationsBloc>();

    _bloc?.add(const UnreadNotificationsEvent.started());
  }

  @override
  Widget build(BuildContext context) {
    // return ListView.separated(
    //   itemCount: 3,
    //   itemBuilder: (context, index) => Dismissible(
    //     key: Key(index.toString()),
    //     onDismissed: (direction) {
    //       if (DismissDirection.endToStart == direction) {
    //         // swiping from right to left
    //       }
    //     },
    //     direction: DismissDirection.endToStart,
    //     background: Container(
    //       alignment: Alignment.centerRight,
    //       color: FroggyColors.error,
    //       child: FroggyIconsList.delete.toWidget(),
    //     ),
    //     child: ListTile(
    //       leading: FroggyIconsList.notificationBell.toWidget(
    //         color: FroggyColors.primary,
    //       ),
    //       onTap: () {},
    //       title: const Text('Nothing to show you !!!'),
    //     ),
    //   ),
    //   physics: const BouncingScrollPhysics(),
    //   separatorBuilder: (context, index) =>
    //       FroggyIconsList.dividerHorizontal.toWidget(),
    // );

    return BlocBuilder<UnreadNotificationsBloc, UnreadNotificationsState>(
      builder: (context, state) {
        if (state.status.isInProgress) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state.notifications.isEmpty) {
          return Center(
            child: Column(
              children: [
                const Spacer(),
                Center(
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 15),
                    child: FroggyIconsList.notificationBell.toWidget(),
                  ),
                ),
                // const Spacer(),
                Center(
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 15),
                    child: Text(
                      context.l10n.noNotificationsYet,
                      style: const TextStyle(
                        fontSize: 20,
                        color: FroggyColors.black,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                const Spacer(),
              ],
            ),
          );
        }

        return ListView.separated(
          padding: EdgeInsets.zero,
          itemCount: state.notifications.length,
          itemBuilder: (context, index) {
            final notification = state.notifications[index];

            return Column(
              children: [
                NotificationItem(
                  parentBloc: _bloc,
                  notification: notification,
                ),
                FroggyIconsList.dividerHorizontal.toWidget(),
              ],
            );
          },
          physics: const BouncingScrollPhysics(),
          separatorBuilder: (context, index) => const SizedBox.shrink(),
          // separatorBuilder: (context, index) =>
          //     FroggyIconsList.dividerHorizontal.toWidget(),
        );
      },
    );
  }
}
