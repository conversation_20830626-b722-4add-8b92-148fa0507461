import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';
import 'package:froggy_icons/froggy_icons.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:froggytalk/notifications/notifications.dart';

class AllNotifications extends StatefulWidget {
  const AllNotifications({super.key});

  @override
  State<AllNotifications> createState() => _AllNotificationsState();
}

class _AllNotificationsState extends State<AllNotifications> {
  AllNotificationsBloc? _bloc;

  @override
  void initState() {
    super.initState();
    _bloc = context.read<AllNotificationsBloc>();

    _bloc?.add(const AllNotificationsEvent.started());
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AllNotificationsBloc, AllNotificationsState>(
      builder: (context, state) {
        if (state.status.isInProgress) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state.notifications.isEmpty) {
          return Center(
            child: Column(
              children: [
                const Spacer(),
                Center(
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 15),
                    child: FroggyIconsList.notificationBell.toWidget(),
                  ),
                ),
                // const Spacer(),
                Center(
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 15),
                    child: Text(
                      context.l10n.noNotificationsYet,
                      style: const TextStyle(
                        fontSize: 20,
                        color: FroggyColors.black,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                const Spacer(),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () async {
            _bloc?.add(const AllNotificationsEvent.started());
          },
          child: ListView.separated(
            padding: EdgeInsets.zero,
            itemCount: state.notifications.length,
            itemBuilder: (context, index) {
              final notification = state.notifications[index];

              // return Container(
              //   color: Colors.green,
              //   child: Text('${notification.title}'),
              // );
              return Column(
                children: [
                  NotificationItem(
                    parentBloc: _bloc,
                    notification: notification,
                  ),
                  FroggyIconsList.dividerHorizontal.toWidget(),
                ],
              );
            },
            physics: const BouncingScrollPhysics(),
            separatorBuilder: (context, index) => const SizedBox.shrink(),
            // separatorBuilder: (context, index) =>
            //     FroggyIconsList.dividerHorizontal.toWidget(),
            // separatorBuilder: (context, index) => const Divider(),
          ),
        );
      },
    );
  }
}
