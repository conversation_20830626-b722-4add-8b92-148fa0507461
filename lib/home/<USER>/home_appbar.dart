import 'package:countries/countries.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:froggytalk/notifications/notifications.dart';

class HomePageAppBar extends HookWidget implements PreferredSizeWidget {
  const HomePageAppBar({
    super.key,
  });

  double get height => 70;

  @override
  Widget build(BuildContext context) {
    final authUser = useAuthUser();
    final libphonenumber = context.read<PhoneNumberService>();
    final profilePhoneNumber =
        libphonenumber.format(authUser?.formattedPhoneNumber ?? '');
    var profileCountryFlag = authUser?.country?.attributes?.code ?? 'nl';
    profileCountryFlag = 'flags/$profileCountryFlag.svg';
    final hasInternetConnection = useHasConnectivity();

    return AppBar(
      toolbarHeight: height,
      title: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(3),
            child: FroggyCountries.showCountryFlag(
              profileCountryFlag,
              width: 18,
              height: 20,
              margin: 0,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(bottom: 1, left: 5, right: 5),
            child: Text(
              profilePhoneNumber ?? '',
              textDirection: TextDirection.ltr,
            ),
          ),
        ],
      ),
      actions: [
        Builder(
          builder: (context) {
            if (!hasInternetConnection) {
              const SizedBox.shrink();
            }

            return Padding(
              padding: const EdgeInsets.only(top: 1, right: 20, bottom: 1),
              child: BlocSelector<UnreadNotificationsBloc,
                  UnreadNotificationsState, int>(
                selector: (state) {
                  return state.notifications.length;
                },
                builder: (context, notificationCount) {
                  return AppNotificationBell(
                    notificationCount: notificationCount.toDouble(),
                  );
                },
              ),
            );
          },
        ),
      ],
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(height);
}
