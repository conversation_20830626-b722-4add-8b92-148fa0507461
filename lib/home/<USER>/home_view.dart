import 'package:common/common.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/app/hooks/use_settings.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:froggytalk/buy_credit/buy_credit.dart';
import 'package:froggytalk/contact_list/contact_list.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:froggytalk/dialer/dialer.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:froggytalk/language/language.dart';
import 'package:froggytalk/radio_stations/radio_stations.dart';
import 'package:froggytalk/referral/referral.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:utils/utils.dart';

class HomePage extends StatefulHookWidget {
  const HomePage({super.key});

  static Route<Object?> route() {
    return MaterialPageRoute<void>(
      builder: (_) => const HomePage(),
    );
  }

  static String routeName = '/home';

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // Localization and Connectivity
    final l10n = useLocale();
    final isIOSEnabled =
        useSettings().getSetting<bool>('ios_hide_buy_credit_button') ?? true;

    // User and Currency
    final authUser = useAuthUser();
    final currencySymbol = useAuthUserCurrencyCode() ?? 'USD';

    // Contact Tabs
    final tabs = useContactTabs();
    final selectedTab =
        context.select((ContactTabsBloc bloc) => bloc.state.selectedIndex);

    // Authentication State
    final isAuthProfileRefreshing =
        context.select((AuthenticationBloc bloc) => bloc.state.isLoading);

    // Navigation
    final onBuyCreditButtonPressed = useCallback(
      () {
        BuyCreditEvents.clickedOnBuyCreditButton();

        return Navigator.push(
          context,
          BuyCreditPage.route(bloc: context.read<CheckoutBloc>()),
        );
      },
      [],
    );

    // Blocs
    final authBloc = context.read<AuthenticationBloc>();
    final dialerBloc = context.read<DialerBloc>();
    final favContactsBloc = context.read<FavouriteContactsBloc>();
    final contactTabsBloc = context.read<ContactTabsBloc>();
    final recentCallsBloc = context.read<RecentCallsBloc>();

    final onRefreshed = useCallback(
      () async {
        authBloc.add(const AuthenticationEvent.refreshProfile());

        final status = await Permission.contacts.isGranted;

        if (status) {
          favContactsBloc.add(const FavouriteContactsEvent.started());
          // contactListBloc.add(const ContactListEvent.started());
          recentCallsBloc.add(const RecentCallsEvent.started());
        } else {
          favContactsBloc.add(const FavouriteContactsEvent.started());
          recentCallsBloc.add(const RecentCallsEvent.started());
        }

        if (authUser != null) {
          dialerBloc.add(
            DialerEvent.register(
              sipUsername: authUser.formattedPhoneNumber,
              sipPassword: authUser.asteriskPassword,
            ),
          );
        }
      },
      [],
    );

    // Check if language modal should be shown on first build
    final oldLanguageValue = FroggyLocalStorage.getInstance()
        .get<String>(LanguageRepository.isLanguageUpdatedKey);

    final needsUpdateLang = oldLanguageValue == null;

    // final updateOneSignalTags = useCallback(
    //   () async {
    //     final version = await getAppVersion();
    //     final buildNumber = await getAppBuildNumber();

    //     final tags = flattenMap({
    //       'user_name': authUser?.name ?? '',
    //       'user_currency': currencySymbol,
    //       'app_build_number': buildNumber,
    //       'app_version': version,
    //     });

    //     await OneSignal.User.addTags(tags);
    //     await OneSignal.User.addTagWithKey(
    //       'user_language',
    //       LanguageRepository.getSavedLanguage()?.code,
    //     );
    //     await OneSignal.User.addTagWithKey(
    //       'user_phone',
    //       authUser?.formattedPhoneNumber ?? '',
    //     );
    //     await OneSignal.User.addSms(authUser?.formattedPhoneNumber ?? '');
    //     await OneSignal.User.addEmail(authUser?.email ?? '');

    //     await OneSignal.User.removeTags(
    //       ['user_phone', 'user_id', 'user_country', 'user_email'],
    //     );

    //     // CustomerIO.instance.identify(
    //     //   userId: authUser?.oneSignalPassKey ?? '',
    //     //   traits: {
    //     //     'name': authUser?.name ?? '',
    //     //     'email': authUser?.email ?? '',
    //     //     'country_name': authUser?.country?.attributes?.name ?? '',
    //     //     'country_code': authUser?.country?.attributes?.code ?? '',
    //     //     'user_currency': currencySymbol,
    //     //     'app_build_number': buildNumber,
    //     //     'app_version': version,
    //     //     'app_platform': Platform.operatingSystem,
    //     //     'app_language': LanguageRepository.getSavedLanguage()?.code,
    //     //   },
    //     // );

    //     // await OneSignal.LiveActivities.s
    //   },
    //   [authUser, currencySymbol],
    // );

    useEffect(
      () {
        // updateOneSignalTags();

        if (needsUpdateLang) {
          // Start the language bloc
          context.read<LanguageBloc>().add(const LanguageEvent.started());

          // Show the language selection modal after the frame is built
          WidgetsBinding.instance.addPostFrameCallback((_) {
            // LanguageSelectionModal.show(context);
            SelectPreferredLanguageModal.show(context);
          });
        }

        return;
      },
      [],
    );

    return Stack(
      children: [
        RefreshIndicator(
          onRefresh: onRefreshed,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 15),
            child: CustomScrollView(
              slivers: [
                SliverToBoxAdapter(child: FroggySpacer.y16()),
                const SliverToBoxAdapter(child: ReferEarnAdvertWidget()),
                SliverToBoxAdapter(child: FroggySpacer.y16()),
                SliverToBoxAdapter(
                  child: Skeletonizer(
                    enabled: isAuthProfileRefreshing,
                    enableSwitchAnimation: true,
                    child: FroggyAccountBalance(
                      balanceTitleText: l10n.accountBalanceCardTitle,
                      buyCreditText: l10n.buyCreditAppBarTitle,
                      balance: authUser?.accountBalance ?? '0',
                      onBuyCredit: onBuyCreditButtonPressed,
                      currencyCode: currencySymbol,
                      hideBuyCreditButton: isIOS() && isIOSEnabled,
                    ),
                  ),
                ),
                SliverToBoxAdapter(child: FroggySpacer.y16()),
                SliverToBoxAdapter(
                  child: CallsTabBar(
                    tabs: tabs,
                    tabValue: selectedTab,
                    onTabChanged: (int index) =>
                        contactTabsBloc.add(ContactTabsEvent.tabChanged(index)),
                  ),
                ),
                if (selectedTab == 0)
                  const SliverToBoxAdapter(child: RecentCallsSearch()),
                if (selectedTab == 1)
                  // const SliverToBoxAdapter(child: ContactListSearch()),
                  const SliverToBoxAdapter(child: ContactListSearchBarV2()),
                if (selectedTab == 2)
                  const SliverToBoxAdapter(child: FavouriteContactsSearch()),
                SliverToBoxAdapter(child: FroggySpacer.y16()),
                SliverToBoxAdapter(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: MediaQuery.sizeOf(context).height * 0.45,
                    ),
                    child: Builder(
                      builder: (context) {
                        if (selectedTab == 0) {
                          return const RecentCallsComponentView();
                        }

                        if (selectedTab == 1) {
                          return const ContactListPv2age();
                          // return const ContactListComponentView();
                        }

                        return const FavouriteContactsComponentView();
                      },
                    ),
                  ),
                ),
                // const SliverToBoxAdapter(child: ContactsOverviewPage()),
                SliverToBoxAdapter(child: FroggySpacer.y16()),
              ],
            ),
          ),
        ),
        if (!needsUpdateLang)
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: const EdgeInsets.only(left: 8, right: 8, bottom: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
              ),
              child: const MiniRadioPlayerControl(),
            ),
          ),
      ],
    );
  }
}
