// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'call_rates_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CallRatesRequest _$CallRatesRequestFromJson(Map<String, dynamic> json) {
  return _CallRatesRequest.fromJson(json);
}

/// @nodoc
mixin _$CallRatesRequest {
  @JsonKey(name: 'country_code')
  String get countryCode => throw _privateConstructorUsedError;

  /// Serializes this CallRatesRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CallRatesRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CallRatesRequestCopyWith<CallRatesRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CallRatesRequestCopyWith<$Res> {
  factory $CallRatesRequestCopyWith(
          CallRatesRequest value, $Res Function(CallRatesRequest) then) =
      _$CallRatesRequestCopyWithImpl<$Res, CallRatesRequest>;
  @useResult
  $Res call({@JsonKey(name: 'country_code') String countryCode});
}

/// @nodoc
class _$CallRatesRequestCopyWithImpl<$Res, $Val extends CallRatesRequest>
    implements $CallRatesRequestCopyWith<$Res> {
  _$CallRatesRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CallRatesRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? countryCode = null,
  }) {
    return _then(_value.copyWith(
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CallRatesRequestImplCopyWith<$Res>
    implements $CallRatesRequestCopyWith<$Res> {
  factory _$$CallRatesRequestImplCopyWith(_$CallRatesRequestImpl value,
          $Res Function(_$CallRatesRequestImpl) then) =
      __$$CallRatesRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({@JsonKey(name: 'country_code') String countryCode});
}

/// @nodoc
class __$$CallRatesRequestImplCopyWithImpl<$Res>
    extends _$CallRatesRequestCopyWithImpl<$Res, _$CallRatesRequestImpl>
    implements _$$CallRatesRequestImplCopyWith<$Res> {
  __$$CallRatesRequestImplCopyWithImpl(_$CallRatesRequestImpl _value,
      $Res Function(_$CallRatesRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of CallRatesRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? countryCode = null,
  }) {
    return _then(_$CallRatesRequestImpl(
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CallRatesRequestImpl implements _CallRatesRequest {
  _$CallRatesRequestImpl(
      {@JsonKey(name: 'country_code') required this.countryCode});

  factory _$CallRatesRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$CallRatesRequestImplFromJson(json);

  @override
  @JsonKey(name: 'country_code')
  final String countryCode;

  @override
  String toString() {
    return 'CallRatesRequest(countryCode: $countryCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CallRatesRequestImpl &&
            (identical(other.countryCode, countryCode) ||
                other.countryCode == countryCode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, countryCode);

  /// Create a copy of CallRatesRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CallRatesRequestImplCopyWith<_$CallRatesRequestImpl> get copyWith =>
      __$$CallRatesRequestImplCopyWithImpl<_$CallRatesRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CallRatesRequestImplToJson(
      this,
    );
  }
}

abstract class _CallRatesRequest implements CallRatesRequest {
  factory _CallRatesRequest(
          {@JsonKey(name: 'country_code') required final String countryCode}) =
      _$CallRatesRequestImpl;

  factory _CallRatesRequest.fromJson(Map<String, dynamic> json) =
      _$CallRatesRequestImpl.fromJson;

  @override
  @JsonKey(name: 'country_code')
  String get countryCode;

  /// Create a copy of CallRatesRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CallRatesRequestImplCopyWith<_$CallRatesRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
