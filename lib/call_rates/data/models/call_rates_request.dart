import 'package:freezed_annotation/freezed_annotation.dart';

part 'call_rates_request.freezed.dart';
part 'call_rates_request.g.dart';

@freezed
class CallRatesRequest with _$CallRatesRequest {
  factory CallRatesRequest({
    @JsonKey(name: 'country_code') required String countryCode,
  }) = _CallRatesRequest;

  factory CallRatesRequest.fromJson(Map<String, Object?> json) =>
      _$CallRatesRequestFromJson(json);
}
