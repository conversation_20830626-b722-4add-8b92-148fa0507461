import 'package:countries/countries.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'call_rates_response.freezed.dart';
part 'call_rates_response.g.dart';

@freezed
class CallRatesResponse with _$CallRatesResponse {
  factory CallRatesResponse({
    required String id,
    @Default('call_rates') String? type,
    CallRatesAttributesModel? attributes,
    @JsonKey(name: 'call_frequency') CallFrequencyModel? callFrequency,
  }) = _CallRatesResponse;

  factory CallRatesResponse.fromJson(Map<String, Object?> json) =>
      _$CallRatesResponseFromJson(json);
}

@freezed
class CallRatesAttributesModel with _$CallRatesAttributesModel {
  factory CallRatesAttributesModel({
    @JsonKey(name: 'rate_mobile_per_minute')
    CallRatesAmountModel? rateMobilePerMinute,
    @JsonKey(name: 'rate_landline_per_minute')
    CallRatesAmountModel? rateLandlinePerMinute,
    CountryModel? country,
  }) = _CallRatesAttributesModel;

  factory CallRatesAttributesModel.fromJson(Map<String, Object?> json) =>
      _$CallRatesAttributesModelFromJson(json);
}

@freezed
class CallFrequencyModel with _$CallFrequencyModel {
  factory CallFrequencyModel({
    @JsonKey(name: '45_minutes') CallRatesAmountModel? for45Minutes,
    @JsonKey(name: '91_minutes') CallRatesAmountModel? for91Minutes,
    @JsonKey(name: '182_minutes') CallRatesAmountModel? for182Minutes,
  }) = _CallFrequencyModel;

  factory CallFrequencyModel.fromJson(Map<String, Object?> json) =>
      _$CallFrequencyModelFromJson(json);
}

@freezed
class CallRatesAmountModel with _$CallRatesAmountModel {
  factory CallRatesAmountModel({
    String? amount,
    String? currency,
    @JsonKey(name: 'currency_code') String? currencyCode,
  }) = _CallRatesAmountModel;

  factory CallRatesAmountModel.fromJson(Map<String, Object?> json) =>
      _$CallRatesAmountModelFromJson(json);
}
