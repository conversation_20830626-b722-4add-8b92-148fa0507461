// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'call_rates_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CallRatesResponse _$CallRatesResponseFromJson(Map<String, dynamic> json) {
  return _CallRatesResponse.fromJson(json);
}

/// @nodoc
mixin _$CallRatesResponse {
  String get id => throw _privateConstructorUsedError;
  String? get type => throw _privateConstructorUsedError;
  CallRatesAttributesModel? get attributes =>
      throw _privateConstructorUsedError;
  @JsonKey(name: 'call_frequency')
  CallFrequencyModel? get callFrequency => throw _privateConstructorUsedError;

  /// Serializes this CallRatesResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CallRatesResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CallRatesResponseCopyWith<CallRatesResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CallRatesResponseCopyWith<$Res> {
  factory $CallRatesResponseCopyWith(
          CallRatesResponse value, $Res Function(CallRatesResponse) then) =
      _$CallRatesResponseCopyWithImpl<$Res, CallRatesResponse>;
  @useResult
  $Res call(
      {String id,
      String? type,
      CallRatesAttributesModel? attributes,
      @JsonKey(name: 'call_frequency') CallFrequencyModel? callFrequency});

  $CallRatesAttributesModelCopyWith<$Res>? get attributes;
  $CallFrequencyModelCopyWith<$Res>? get callFrequency;
}

/// @nodoc
class _$CallRatesResponseCopyWithImpl<$Res, $Val extends CallRatesResponse>
    implements $CallRatesResponseCopyWith<$Res> {
  _$CallRatesResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CallRatesResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? type = freezed,
    Object? attributes = freezed,
    Object? callFrequency = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      attributes: freezed == attributes
          ? _value.attributes
          : attributes // ignore: cast_nullable_to_non_nullable
              as CallRatesAttributesModel?,
      callFrequency: freezed == callFrequency
          ? _value.callFrequency
          : callFrequency // ignore: cast_nullable_to_non_nullable
              as CallFrequencyModel?,
    ) as $Val);
  }

  /// Create a copy of CallRatesResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CallRatesAttributesModelCopyWith<$Res>? get attributes {
    if (_value.attributes == null) {
      return null;
    }

    return $CallRatesAttributesModelCopyWith<$Res>(_value.attributes!, (value) {
      return _then(_value.copyWith(attributes: value) as $Val);
    });
  }

  /// Create a copy of CallRatesResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CallFrequencyModelCopyWith<$Res>? get callFrequency {
    if (_value.callFrequency == null) {
      return null;
    }

    return $CallFrequencyModelCopyWith<$Res>(_value.callFrequency!, (value) {
      return _then(_value.copyWith(callFrequency: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CallRatesResponseImplCopyWith<$Res>
    implements $CallRatesResponseCopyWith<$Res> {
  factory _$$CallRatesResponseImplCopyWith(_$CallRatesResponseImpl value,
          $Res Function(_$CallRatesResponseImpl) then) =
      __$$CallRatesResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String? type,
      CallRatesAttributesModel? attributes,
      @JsonKey(name: 'call_frequency') CallFrequencyModel? callFrequency});

  @override
  $CallRatesAttributesModelCopyWith<$Res>? get attributes;
  @override
  $CallFrequencyModelCopyWith<$Res>? get callFrequency;
}

/// @nodoc
class __$$CallRatesResponseImplCopyWithImpl<$Res>
    extends _$CallRatesResponseCopyWithImpl<$Res, _$CallRatesResponseImpl>
    implements _$$CallRatesResponseImplCopyWith<$Res> {
  __$$CallRatesResponseImplCopyWithImpl(_$CallRatesResponseImpl _value,
      $Res Function(_$CallRatesResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of CallRatesResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? type = freezed,
    Object? attributes = freezed,
    Object? callFrequency = freezed,
  }) {
    return _then(_$CallRatesResponseImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      attributes: freezed == attributes
          ? _value.attributes
          : attributes // ignore: cast_nullable_to_non_nullable
              as CallRatesAttributesModel?,
      callFrequency: freezed == callFrequency
          ? _value.callFrequency
          : callFrequency // ignore: cast_nullable_to_non_nullable
              as CallFrequencyModel?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CallRatesResponseImpl implements _CallRatesResponse {
  _$CallRatesResponseImpl(
      {required this.id,
      this.type = 'call_rates',
      this.attributes,
      @JsonKey(name: 'call_frequency') this.callFrequency});

  factory _$CallRatesResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$CallRatesResponseImplFromJson(json);

  @override
  final String id;
  @override
  @JsonKey()
  final String? type;
  @override
  final CallRatesAttributesModel? attributes;
  @override
  @JsonKey(name: 'call_frequency')
  final CallFrequencyModel? callFrequency;

  @override
  String toString() {
    return 'CallRatesResponse(id: $id, type: $type, attributes: $attributes, callFrequency: $callFrequency)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CallRatesResponseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.attributes, attributes) ||
                other.attributes == attributes) &&
            (identical(other.callFrequency, callFrequency) ||
                other.callFrequency == callFrequency));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, type, attributes, callFrequency);

  /// Create a copy of CallRatesResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CallRatesResponseImplCopyWith<_$CallRatesResponseImpl> get copyWith =>
      __$$CallRatesResponseImplCopyWithImpl<_$CallRatesResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CallRatesResponseImplToJson(
      this,
    );
  }
}

abstract class _CallRatesResponse implements CallRatesResponse {
  factory _CallRatesResponse(
      {required final String id,
      final String? type,
      final CallRatesAttributesModel? attributes,
      @JsonKey(name: 'call_frequency')
      final CallFrequencyModel? callFrequency}) = _$CallRatesResponseImpl;

  factory _CallRatesResponse.fromJson(Map<String, dynamic> json) =
      _$CallRatesResponseImpl.fromJson;

  @override
  String get id;
  @override
  String? get type;
  @override
  CallRatesAttributesModel? get attributes;
  @override
  @JsonKey(name: 'call_frequency')
  CallFrequencyModel? get callFrequency;

  /// Create a copy of CallRatesResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CallRatesResponseImplCopyWith<_$CallRatesResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CallRatesAttributesModel _$CallRatesAttributesModelFromJson(
    Map<String, dynamic> json) {
  return _CallRatesAttributesModel.fromJson(json);
}

/// @nodoc
mixin _$CallRatesAttributesModel {
  @JsonKey(name: 'rate_mobile_per_minute')
  CallRatesAmountModel? get rateMobilePerMinute =>
      throw _privateConstructorUsedError;
  @JsonKey(name: 'rate_landline_per_minute')
  CallRatesAmountModel? get rateLandlinePerMinute =>
      throw _privateConstructorUsedError;
  CountryModel? get country => throw _privateConstructorUsedError;

  /// Serializes this CallRatesAttributesModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CallRatesAttributesModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CallRatesAttributesModelCopyWith<CallRatesAttributesModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CallRatesAttributesModelCopyWith<$Res> {
  factory $CallRatesAttributesModelCopyWith(CallRatesAttributesModel value,
          $Res Function(CallRatesAttributesModel) then) =
      _$CallRatesAttributesModelCopyWithImpl<$Res, CallRatesAttributesModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'rate_mobile_per_minute')
      CallRatesAmountModel? rateMobilePerMinute,
      @JsonKey(name: 'rate_landline_per_minute')
      CallRatesAmountModel? rateLandlinePerMinute,
      CountryModel? country});

  $CallRatesAmountModelCopyWith<$Res>? get rateMobilePerMinute;
  $CallRatesAmountModelCopyWith<$Res>? get rateLandlinePerMinute;
  $CountryModelCopyWith<$Res>? get country;
}

/// @nodoc
class _$CallRatesAttributesModelCopyWithImpl<$Res,
        $Val extends CallRatesAttributesModel>
    implements $CallRatesAttributesModelCopyWith<$Res> {
  _$CallRatesAttributesModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CallRatesAttributesModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? rateMobilePerMinute = freezed,
    Object? rateLandlinePerMinute = freezed,
    Object? country = freezed,
  }) {
    return _then(_value.copyWith(
      rateMobilePerMinute: freezed == rateMobilePerMinute
          ? _value.rateMobilePerMinute
          : rateMobilePerMinute // ignore: cast_nullable_to_non_nullable
              as CallRatesAmountModel?,
      rateLandlinePerMinute: freezed == rateLandlinePerMinute
          ? _value.rateLandlinePerMinute
          : rateLandlinePerMinute // ignore: cast_nullable_to_non_nullable
              as CallRatesAmountModel?,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as CountryModel?,
    ) as $Val);
  }

  /// Create a copy of CallRatesAttributesModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CallRatesAmountModelCopyWith<$Res>? get rateMobilePerMinute {
    if (_value.rateMobilePerMinute == null) {
      return null;
    }

    return $CallRatesAmountModelCopyWith<$Res>(_value.rateMobilePerMinute!,
        (value) {
      return _then(_value.copyWith(rateMobilePerMinute: value) as $Val);
    });
  }

  /// Create a copy of CallRatesAttributesModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CallRatesAmountModelCopyWith<$Res>? get rateLandlinePerMinute {
    if (_value.rateLandlinePerMinute == null) {
      return null;
    }

    return $CallRatesAmountModelCopyWith<$Res>(_value.rateLandlinePerMinute!,
        (value) {
      return _then(_value.copyWith(rateLandlinePerMinute: value) as $Val);
    });
  }

  /// Create a copy of CallRatesAttributesModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CountryModelCopyWith<$Res>? get country {
    if (_value.country == null) {
      return null;
    }

    return $CountryModelCopyWith<$Res>(_value.country!, (value) {
      return _then(_value.copyWith(country: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CallRatesAttributesModelImplCopyWith<$Res>
    implements $CallRatesAttributesModelCopyWith<$Res> {
  factory _$$CallRatesAttributesModelImplCopyWith(
          _$CallRatesAttributesModelImpl value,
          $Res Function(_$CallRatesAttributesModelImpl) then) =
      __$$CallRatesAttributesModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'rate_mobile_per_minute')
      CallRatesAmountModel? rateMobilePerMinute,
      @JsonKey(name: 'rate_landline_per_minute')
      CallRatesAmountModel? rateLandlinePerMinute,
      CountryModel? country});

  @override
  $CallRatesAmountModelCopyWith<$Res>? get rateMobilePerMinute;
  @override
  $CallRatesAmountModelCopyWith<$Res>? get rateLandlinePerMinute;
  @override
  $CountryModelCopyWith<$Res>? get country;
}

/// @nodoc
class __$$CallRatesAttributesModelImplCopyWithImpl<$Res>
    extends _$CallRatesAttributesModelCopyWithImpl<$Res,
        _$CallRatesAttributesModelImpl>
    implements _$$CallRatesAttributesModelImplCopyWith<$Res> {
  __$$CallRatesAttributesModelImplCopyWithImpl(
      _$CallRatesAttributesModelImpl _value,
      $Res Function(_$CallRatesAttributesModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of CallRatesAttributesModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? rateMobilePerMinute = freezed,
    Object? rateLandlinePerMinute = freezed,
    Object? country = freezed,
  }) {
    return _then(_$CallRatesAttributesModelImpl(
      rateMobilePerMinute: freezed == rateMobilePerMinute
          ? _value.rateMobilePerMinute
          : rateMobilePerMinute // ignore: cast_nullable_to_non_nullable
              as CallRatesAmountModel?,
      rateLandlinePerMinute: freezed == rateLandlinePerMinute
          ? _value.rateLandlinePerMinute
          : rateLandlinePerMinute // ignore: cast_nullable_to_non_nullable
              as CallRatesAmountModel?,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as CountryModel?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CallRatesAttributesModelImpl implements _CallRatesAttributesModel {
  _$CallRatesAttributesModelImpl(
      {@JsonKey(name: 'rate_mobile_per_minute') this.rateMobilePerMinute,
      @JsonKey(name: 'rate_landline_per_minute') this.rateLandlinePerMinute,
      this.country});

  factory _$CallRatesAttributesModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$CallRatesAttributesModelImplFromJson(json);

  @override
  @JsonKey(name: 'rate_mobile_per_minute')
  final CallRatesAmountModel? rateMobilePerMinute;
  @override
  @JsonKey(name: 'rate_landline_per_minute')
  final CallRatesAmountModel? rateLandlinePerMinute;
  @override
  final CountryModel? country;

  @override
  String toString() {
    return 'CallRatesAttributesModel(rateMobilePerMinute: $rateMobilePerMinute, rateLandlinePerMinute: $rateLandlinePerMinute, country: $country)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CallRatesAttributesModelImpl &&
            (identical(other.rateMobilePerMinute, rateMobilePerMinute) ||
                other.rateMobilePerMinute == rateMobilePerMinute) &&
            (identical(other.rateLandlinePerMinute, rateLandlinePerMinute) ||
                other.rateLandlinePerMinute == rateLandlinePerMinute) &&
            (identical(other.country, country) || other.country == country));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, rateMobilePerMinute, rateLandlinePerMinute, country);

  /// Create a copy of CallRatesAttributesModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CallRatesAttributesModelImplCopyWith<_$CallRatesAttributesModelImpl>
      get copyWith => __$$CallRatesAttributesModelImplCopyWithImpl<
          _$CallRatesAttributesModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CallRatesAttributesModelImplToJson(
      this,
    );
  }
}

abstract class _CallRatesAttributesModel implements CallRatesAttributesModel {
  factory _CallRatesAttributesModel(
      {@JsonKey(name: 'rate_mobile_per_minute')
      final CallRatesAmountModel? rateMobilePerMinute,
      @JsonKey(name: 'rate_landline_per_minute')
      final CallRatesAmountModel? rateLandlinePerMinute,
      final CountryModel? country}) = _$CallRatesAttributesModelImpl;

  factory _CallRatesAttributesModel.fromJson(Map<String, dynamic> json) =
      _$CallRatesAttributesModelImpl.fromJson;

  @override
  @JsonKey(name: 'rate_mobile_per_minute')
  CallRatesAmountModel? get rateMobilePerMinute;
  @override
  @JsonKey(name: 'rate_landline_per_minute')
  CallRatesAmountModel? get rateLandlinePerMinute;
  @override
  CountryModel? get country;

  /// Create a copy of CallRatesAttributesModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CallRatesAttributesModelImplCopyWith<_$CallRatesAttributesModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

CallFrequencyModel _$CallFrequencyModelFromJson(Map<String, dynamic> json) {
  return _CallFrequencyModel.fromJson(json);
}

/// @nodoc
mixin _$CallFrequencyModel {
  @JsonKey(name: '45_minutes')
  CallRatesAmountModel? get for45Minutes => throw _privateConstructorUsedError;
  @JsonKey(name: '91_minutes')
  CallRatesAmountModel? get for91Minutes => throw _privateConstructorUsedError;
  @JsonKey(name: '182_minutes')
  CallRatesAmountModel? get for182Minutes => throw _privateConstructorUsedError;

  /// Serializes this CallFrequencyModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CallFrequencyModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CallFrequencyModelCopyWith<CallFrequencyModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CallFrequencyModelCopyWith<$Res> {
  factory $CallFrequencyModelCopyWith(
          CallFrequencyModel value, $Res Function(CallFrequencyModel) then) =
      _$CallFrequencyModelCopyWithImpl<$Res, CallFrequencyModel>;
  @useResult
  $Res call(
      {@JsonKey(name: '45_minutes') CallRatesAmountModel? for45Minutes,
      @JsonKey(name: '91_minutes') CallRatesAmountModel? for91Minutes,
      @JsonKey(name: '182_minutes') CallRatesAmountModel? for182Minutes});

  $CallRatesAmountModelCopyWith<$Res>? get for45Minutes;
  $CallRatesAmountModelCopyWith<$Res>? get for91Minutes;
  $CallRatesAmountModelCopyWith<$Res>? get for182Minutes;
}

/// @nodoc
class _$CallFrequencyModelCopyWithImpl<$Res, $Val extends CallFrequencyModel>
    implements $CallFrequencyModelCopyWith<$Res> {
  _$CallFrequencyModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CallFrequencyModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? for45Minutes = freezed,
    Object? for91Minutes = freezed,
    Object? for182Minutes = freezed,
  }) {
    return _then(_value.copyWith(
      for45Minutes: freezed == for45Minutes
          ? _value.for45Minutes
          : for45Minutes // ignore: cast_nullable_to_non_nullable
              as CallRatesAmountModel?,
      for91Minutes: freezed == for91Minutes
          ? _value.for91Minutes
          : for91Minutes // ignore: cast_nullable_to_non_nullable
              as CallRatesAmountModel?,
      for182Minutes: freezed == for182Minutes
          ? _value.for182Minutes
          : for182Minutes // ignore: cast_nullable_to_non_nullable
              as CallRatesAmountModel?,
    ) as $Val);
  }

  /// Create a copy of CallFrequencyModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CallRatesAmountModelCopyWith<$Res>? get for45Minutes {
    if (_value.for45Minutes == null) {
      return null;
    }

    return $CallRatesAmountModelCopyWith<$Res>(_value.for45Minutes!, (value) {
      return _then(_value.copyWith(for45Minutes: value) as $Val);
    });
  }

  /// Create a copy of CallFrequencyModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CallRatesAmountModelCopyWith<$Res>? get for91Minutes {
    if (_value.for91Minutes == null) {
      return null;
    }

    return $CallRatesAmountModelCopyWith<$Res>(_value.for91Minutes!, (value) {
      return _then(_value.copyWith(for91Minutes: value) as $Val);
    });
  }

  /// Create a copy of CallFrequencyModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CallRatesAmountModelCopyWith<$Res>? get for182Minutes {
    if (_value.for182Minutes == null) {
      return null;
    }

    return $CallRatesAmountModelCopyWith<$Res>(_value.for182Minutes!, (value) {
      return _then(_value.copyWith(for182Minutes: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CallFrequencyModelImplCopyWith<$Res>
    implements $CallFrequencyModelCopyWith<$Res> {
  factory _$$CallFrequencyModelImplCopyWith(_$CallFrequencyModelImpl value,
          $Res Function(_$CallFrequencyModelImpl) then) =
      __$$CallFrequencyModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: '45_minutes') CallRatesAmountModel? for45Minutes,
      @JsonKey(name: '91_minutes') CallRatesAmountModel? for91Minutes,
      @JsonKey(name: '182_minutes') CallRatesAmountModel? for182Minutes});

  @override
  $CallRatesAmountModelCopyWith<$Res>? get for45Minutes;
  @override
  $CallRatesAmountModelCopyWith<$Res>? get for91Minutes;
  @override
  $CallRatesAmountModelCopyWith<$Res>? get for182Minutes;
}

/// @nodoc
class __$$CallFrequencyModelImplCopyWithImpl<$Res>
    extends _$CallFrequencyModelCopyWithImpl<$Res, _$CallFrequencyModelImpl>
    implements _$$CallFrequencyModelImplCopyWith<$Res> {
  __$$CallFrequencyModelImplCopyWithImpl(_$CallFrequencyModelImpl _value,
      $Res Function(_$CallFrequencyModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of CallFrequencyModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? for45Minutes = freezed,
    Object? for91Minutes = freezed,
    Object? for182Minutes = freezed,
  }) {
    return _then(_$CallFrequencyModelImpl(
      for45Minutes: freezed == for45Minutes
          ? _value.for45Minutes
          : for45Minutes // ignore: cast_nullable_to_non_nullable
              as CallRatesAmountModel?,
      for91Minutes: freezed == for91Minutes
          ? _value.for91Minutes
          : for91Minutes // ignore: cast_nullable_to_non_nullable
              as CallRatesAmountModel?,
      for182Minutes: freezed == for182Minutes
          ? _value.for182Minutes
          : for182Minutes // ignore: cast_nullable_to_non_nullable
              as CallRatesAmountModel?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CallFrequencyModelImpl implements _CallFrequencyModel {
  _$CallFrequencyModelImpl(
      {@JsonKey(name: '45_minutes') this.for45Minutes,
      @JsonKey(name: '91_minutes') this.for91Minutes,
      @JsonKey(name: '182_minutes') this.for182Minutes});

  factory _$CallFrequencyModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$CallFrequencyModelImplFromJson(json);

  @override
  @JsonKey(name: '45_minutes')
  final CallRatesAmountModel? for45Minutes;
  @override
  @JsonKey(name: '91_minutes')
  final CallRatesAmountModel? for91Minutes;
  @override
  @JsonKey(name: '182_minutes')
  final CallRatesAmountModel? for182Minutes;

  @override
  String toString() {
    return 'CallFrequencyModel(for45Minutes: $for45Minutes, for91Minutes: $for91Minutes, for182Minutes: $for182Minutes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CallFrequencyModelImpl &&
            (identical(other.for45Minutes, for45Minutes) ||
                other.for45Minutes == for45Minutes) &&
            (identical(other.for91Minutes, for91Minutes) ||
                other.for91Minutes == for91Minutes) &&
            (identical(other.for182Minutes, for182Minutes) ||
                other.for182Minutes == for182Minutes));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, for45Minutes, for91Minutes, for182Minutes);

  /// Create a copy of CallFrequencyModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CallFrequencyModelImplCopyWith<_$CallFrequencyModelImpl> get copyWith =>
      __$$CallFrequencyModelImplCopyWithImpl<_$CallFrequencyModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CallFrequencyModelImplToJson(
      this,
    );
  }
}

abstract class _CallFrequencyModel implements CallFrequencyModel {
  factory _CallFrequencyModel(
      {@JsonKey(name: '45_minutes') final CallRatesAmountModel? for45Minutes,
      @JsonKey(name: '91_minutes') final CallRatesAmountModel? for91Minutes,
      @JsonKey(name: '182_minutes')
      final CallRatesAmountModel? for182Minutes}) = _$CallFrequencyModelImpl;

  factory _CallFrequencyModel.fromJson(Map<String, dynamic> json) =
      _$CallFrequencyModelImpl.fromJson;

  @override
  @JsonKey(name: '45_minutes')
  CallRatesAmountModel? get for45Minutes;
  @override
  @JsonKey(name: '91_minutes')
  CallRatesAmountModel? get for91Minutes;
  @override
  @JsonKey(name: '182_minutes')
  CallRatesAmountModel? get for182Minutes;

  /// Create a copy of CallFrequencyModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CallFrequencyModelImplCopyWith<_$CallFrequencyModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CallRatesAmountModel _$CallRatesAmountModelFromJson(Map<String, dynamic> json) {
  return _CallRatesAmountModel.fromJson(json);
}

/// @nodoc
mixin _$CallRatesAmountModel {
  String? get amount => throw _privateConstructorUsedError;
  String? get currency => throw _privateConstructorUsedError;
  @JsonKey(name: 'currency_code')
  String? get currencyCode => throw _privateConstructorUsedError;

  /// Serializes this CallRatesAmountModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CallRatesAmountModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CallRatesAmountModelCopyWith<CallRatesAmountModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CallRatesAmountModelCopyWith<$Res> {
  factory $CallRatesAmountModelCopyWith(CallRatesAmountModel value,
          $Res Function(CallRatesAmountModel) then) =
      _$CallRatesAmountModelCopyWithImpl<$Res, CallRatesAmountModel>;
  @useResult
  $Res call(
      {String? amount,
      String? currency,
      @JsonKey(name: 'currency_code') String? currencyCode});
}

/// @nodoc
class _$CallRatesAmountModelCopyWithImpl<$Res,
        $Val extends CallRatesAmountModel>
    implements $CallRatesAmountModelCopyWith<$Res> {
  _$CallRatesAmountModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CallRatesAmountModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = freezed,
    Object? currency = freezed,
    Object? currencyCode = freezed,
  }) {
    return _then(_value.copyWith(
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as String?,
      currency: freezed == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String?,
      currencyCode: freezed == currencyCode
          ? _value.currencyCode
          : currencyCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CallRatesAmountModelImplCopyWith<$Res>
    implements $CallRatesAmountModelCopyWith<$Res> {
  factory _$$CallRatesAmountModelImplCopyWith(_$CallRatesAmountModelImpl value,
          $Res Function(_$CallRatesAmountModelImpl) then) =
      __$$CallRatesAmountModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? amount,
      String? currency,
      @JsonKey(name: 'currency_code') String? currencyCode});
}

/// @nodoc
class __$$CallRatesAmountModelImplCopyWithImpl<$Res>
    extends _$CallRatesAmountModelCopyWithImpl<$Res, _$CallRatesAmountModelImpl>
    implements _$$CallRatesAmountModelImplCopyWith<$Res> {
  __$$CallRatesAmountModelImplCopyWithImpl(_$CallRatesAmountModelImpl _value,
      $Res Function(_$CallRatesAmountModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of CallRatesAmountModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = freezed,
    Object? currency = freezed,
    Object? currencyCode = freezed,
  }) {
    return _then(_$CallRatesAmountModelImpl(
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as String?,
      currency: freezed == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String?,
      currencyCode: freezed == currencyCode
          ? _value.currencyCode
          : currencyCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CallRatesAmountModelImpl implements _CallRatesAmountModel {
  _$CallRatesAmountModelImpl(
      {this.amount,
      this.currency,
      @JsonKey(name: 'currency_code') this.currencyCode});

  factory _$CallRatesAmountModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$CallRatesAmountModelImplFromJson(json);

  @override
  final String? amount;
  @override
  final String? currency;
  @override
  @JsonKey(name: 'currency_code')
  final String? currencyCode;

  @override
  String toString() {
    return 'CallRatesAmountModel(amount: $amount, currency: $currency, currencyCode: $currencyCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CallRatesAmountModelImpl &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.currencyCode, currencyCode) ||
                other.currencyCode == currencyCode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, amount, currency, currencyCode);

  /// Create a copy of CallRatesAmountModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CallRatesAmountModelImplCopyWith<_$CallRatesAmountModelImpl>
      get copyWith =>
          __$$CallRatesAmountModelImplCopyWithImpl<_$CallRatesAmountModelImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CallRatesAmountModelImplToJson(
      this,
    );
  }
}

abstract class _CallRatesAmountModel implements CallRatesAmountModel {
  factory _CallRatesAmountModel(
          {final String? amount,
          final String? currency,
          @JsonKey(name: 'currency_code') final String? currencyCode}) =
      _$CallRatesAmountModelImpl;

  factory _CallRatesAmountModel.fromJson(Map<String, dynamic> json) =
      _$CallRatesAmountModelImpl.fromJson;

  @override
  String? get amount;
  @override
  String? get currency;
  @override
  @JsonKey(name: 'currency_code')
  String? get currencyCode;

  /// Create a copy of CallRatesAmountModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CallRatesAmountModelImplCopyWith<_$CallRatesAmountModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
