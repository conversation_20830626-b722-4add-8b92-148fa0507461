// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'call_rates_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CallRatesResponseImpl _$$CallRatesResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$CallRatesResponseImpl(
      id: json['id'] as String,
      type: json['type'] as String? ?? 'call_rates',
      attributes: json['attributes'] == null
          ? null
          : CallRatesAttributesModel.fromJson(
              json['attributes'] as Map<String, dynamic>),
      callFrequency: json['call_frequency'] == null
          ? null
          : CallFrequencyModel.fromJson(
              json['call_frequency'] as Map<String, dynamic>),
    );

const _$$CallRatesResponseImplFieldMap = <String, String>{
  'id': 'id',
  'type': 'type',
  'attributes': 'attributes',
  'callFrequency': 'call_frequency',
};

Map<String, dynamic> _$$CallRatesResponseImplToJson(
        _$CallRatesResponseImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      if (instance.type case final value?) 'type': value,
      if (instance.attributes?.toJson() case final value?) 'attributes': value,
      if (instance.callFrequency?.toJson() case final value?)
        'call_frequency': value,
    };

_$CallRatesAttributesModelImpl _$$CallRatesAttributesModelImplFromJson(
        Map<String, dynamic> json) =>
    _$CallRatesAttributesModelImpl(
      rateMobilePerMinute: json['rate_mobile_per_minute'] == null
          ? null
          : CallRatesAmountModel.fromJson(
              json['rate_mobile_per_minute'] as Map<String, dynamic>),
      rateLandlinePerMinute: json['rate_landline_per_minute'] == null
          ? null
          : CallRatesAmountModel.fromJson(
              json['rate_landline_per_minute'] as Map<String, dynamic>),
      country: json['country'] == null
          ? null
          : CountryModel.fromJson(json['country'] as Map<String, dynamic>),
    );

const _$$CallRatesAttributesModelImplFieldMap = <String, String>{
  'rateMobilePerMinute': 'rate_mobile_per_minute',
  'rateLandlinePerMinute': 'rate_landline_per_minute',
  'country': 'country',
};

Map<String, dynamic> _$$CallRatesAttributesModelImplToJson(
        _$CallRatesAttributesModelImpl instance) =>
    <String, dynamic>{
      if (instance.rateMobilePerMinute?.toJson() case final value?)
        'rate_mobile_per_minute': value,
      if (instance.rateLandlinePerMinute?.toJson() case final value?)
        'rate_landline_per_minute': value,
      if (instance.country?.toJson() case final value?) 'country': value,
    };

_$CallFrequencyModelImpl _$$CallFrequencyModelImplFromJson(
        Map<String, dynamic> json) =>
    _$CallFrequencyModelImpl(
      for45Minutes: json['45_minutes'] == null
          ? null
          : CallRatesAmountModel.fromJson(
              json['45_minutes'] as Map<String, dynamic>),
      for91Minutes: json['91_minutes'] == null
          ? null
          : CallRatesAmountModel.fromJson(
              json['91_minutes'] as Map<String, dynamic>),
      for182Minutes: json['182_minutes'] == null
          ? null
          : CallRatesAmountModel.fromJson(
              json['182_minutes'] as Map<String, dynamic>),
    );

const _$$CallFrequencyModelImplFieldMap = <String, String>{
  'for45Minutes': '45_minutes',
  'for91Minutes': '91_minutes',
  'for182Minutes': '182_minutes',
};

Map<String, dynamic> _$$CallFrequencyModelImplToJson(
        _$CallFrequencyModelImpl instance) =>
    <String, dynamic>{
      if (instance.for45Minutes?.toJson() case final value?)
        '45_minutes': value,
      if (instance.for91Minutes?.toJson() case final value?)
        '91_minutes': value,
      if (instance.for182Minutes?.toJson() case final value?)
        '182_minutes': value,
    };

_$CallRatesAmountModelImpl _$$CallRatesAmountModelImplFromJson(
        Map<String, dynamic> json) =>
    _$CallRatesAmountModelImpl(
      amount: json['amount'] as String?,
      currency: json['currency'] as String?,
      currencyCode: json['currency_code'] as String?,
    );

const _$$CallRatesAmountModelImplFieldMap = <String, String>{
  'amount': 'amount',
  'currency': 'currency',
  'currencyCode': 'currency_code',
};

Map<String, dynamic> _$$CallRatesAmountModelImplToJson(
        _$CallRatesAmountModelImpl instance) =>
    <String, dynamic>{
      if (instance.amount case final value?) 'amount': value,
      if (instance.currency case final value?) 'currency': value,
      if (instance.currencyCode case final value?) 'currency_code': value,
    };
