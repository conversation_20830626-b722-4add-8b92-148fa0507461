import 'dart:io';

import 'package:data/data.dart';
import 'package:either_dart/either.dart';
import 'package:froggytalk/call_rates/call_rates.dart';
import 'package:http/http.dart';
import 'package:utils/utils.dart';

class CallRatesRepository {
  Future<Either<GenericErrorResponse, PaginatedResponse<CallRatesResponse>>>
      execute({
    required String countryCode,
  }) async {
    try {
      final request = ApiRequest(
        route:
            '/v1/mobile/call-rates?data[attributes][country_code]=${countryCode.toLowerCase()}',
        requestType: RequestType.get,
      );

      final response =
          await ApiClient.getInstance().executeRequest(request: request);

      if (response.isSuccess) {
        final fromJson = CallRatesResponse.fromJson(response.data);
        final paginatedResponse = PaginatedResponse<CallRatesResponse>(
          status: response.statusToString,
          message: response.successOrErrorMessage,
          data: from<PERSON><PERSON>,
        );

        FroggyLogger.info('Response: $paginatedResponse');

        return Right(paginatedResponse);
      } else {
        final message = response.fromJson['message'];
        FroggyLogger.error(
          'Error Occurred: $message',
        );

        return Left(
          GenericErrorResponse(
            message: (message as String?) ?? 'Unknown error',
          ),
        );
      }
    } on ClientException catch (e) {
      return Left(
        GenericErrorResponse(
          code: 'internet-out',
          message: 'No internet connection: ${e.message}',
        ),
      );
    } on SocketException catch (e) {
      return Left(
        GenericErrorResponse(
          code: 'internet-out',
          message: 'No internet connection: ${e.message}',
        ),
      );
    } catch (e) {
      FroggyLogger.error('Failure Occurred: $e');

      return Left(GenericErrorResponse(message: e.toString()));
    }
  }
}
