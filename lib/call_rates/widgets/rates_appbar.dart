import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:froggytalk/l10n/l10n.dart';

class CallRatesPageAppBar extends StatelessWidget
    implements PreferredSizeWidget {
  const CallRatesPageAppBar({
    super.key,
    this.onNotificationTap,
    this.onProfileTap,
    this.profileCountryFlag,
    this.profilePhoneNumber,
  });

  double get height => 60;

  final VoidCallback? onNotificationTap;
  final VoidCallback? onProfileTap;
  final String? profileCountryFlag;
  final String? profilePhoneNumber;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 1),
      child: AppBar(
        toolbarHeight: height,
        title: Text(
          l10n.callRatesAppBarTitle,
          style: const TextStyle(
            color: FroggyColors.black,
            fontSize: 24,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: const [
          // AppNotificationBell(),
        ],
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(height);
}
