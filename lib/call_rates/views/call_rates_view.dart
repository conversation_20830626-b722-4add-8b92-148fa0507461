import 'package:common/common.dart';
import 'package:constants/constants.dart';
import 'package:countries/countries.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggy_icons/froggy_icons.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/app/data/services/event_tracker.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:froggytalk/buy_credit/buy_credit.dart';
import 'package:froggytalk/call_rates/call_rates.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:froggytalk/radio_stations/radio_stations.dart';
import 'package:froggytalk/referral/referral.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:utils/utils.dart';

class CallRatesPage extends HookWidget {
  const CallRatesPage({super.key});

  static Route<Object?> route() {
    return MaterialPageRoute<Object?>(
      builder: (_) => const CallRatesPage(),
    );
  }

  static Route<Object?> routeFromMorePage() {
    return MaterialPageRoute<Object?>(
      builder: (_) =>
          const Scaffold(appBar: CallRatesPageAppBar(), body: CallRatesPage()),
    );
  }

  static String routeName = '/call_rates';

  @override
  Widget build(BuildContext context) {
    final l10n = useLocale();
    final hasInternet = useHasConnectivity();
    // final internetState = useHasInternet();
    final authUserCountry = useAuthUserCountry();
    final selectedCountry =
        context.select((RatesBloc bloc) => bloc.state.country);
    final callRatesBloc = context.read<RatesBloc>();
    final isIOSEnabled =
        useSettings().getSetting<bool>('ios_hide_buy_credit_button') ?? true;

    // FroggyLogger.info('internet state: $internetState || $hasInternet');

    final buyCreditIcon = useMemoized(
      () {
        return FroggyIconsList.buyCredit.toWidget(
          color: FroggyColors.white,
        );
      },
    );

    final onBuyCreditButtonPressed = useCallback(() {
      Navigator.of(context)
          .push(BuyCreditPage.route(bloc: context.read<CheckoutBloc>()));
    });

    final initCountryCode =
        useCallback(({bool reset = false, bool hasInternet = true}) async {
      if (authUserCountry == null) return;

      if (reset && hasInternet) {
        callRatesBloc.add(
          RatesEvent.getNationalRates(nationalCountry: authUserCountry),
        );
        return;
      }

      if (!hasInternet) {
        callRatesBloc.add(
          RatesEvent.enabledOfflineMode(country: authUserCountry),
        );
        return;
      }

      if (selectedCountry == null) {
        callRatesBloc.add(
          RatesEvent.getNationalRates(nationalCountry: authUserCountry),
        );
      }
    });

    final addViewCallRatesAttribution = useCallback(() {
      context.read<EventTrackerService>().logEvent(
            schema: 'Check_call_rates',
            description: 'User views call rates',
          );
    });

    useEffect(
      () {
        initCountryCode();
        addViewCallRatesAttribution();

        return () {};
      },
      const [],
    );

    return Stack(
      children: [
        RefreshIndicator(
          onRefresh: () async => initCountryCode(reset: true),
          child: ListView(
            padding: const EdgeInsets.symmetric(horizontal: 15),
            children: [
              const _CountrySearch(),
              FroggySpacer.y32(),
              const _FeaturedCallRates(),
              FroggySpacer.y32(),
              Builder(
                builder: (context) {
                  if (isIOS() && isIOSEnabled) {
                    return const SizedBox.shrink();
                  }

                  return FractionallySizedBox(
                    widthFactor: .5,
                    child: ElevatedButton.icon(
                      onPressed: !hasInternet ? null : onBuyCreditButtonPressed,
                      icon: buyCreditIcon,
                      label: Text(
                        l10n.buyCreditAppBarTitle,
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                      style: ElevatedButton.styleFrom(
                        elevation: 0,
                        fixedSize: const Size(100, 50),
                        side: BorderSide.none,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 18,
                          vertical: 10,
                        ),
                      ),
                    ),
                  );
                },
              ),
              FroggySpacer.y32(),
              const ReferEarnAdvertWidget(),
              // const FroggySendCreditToOtherCountriesAdvert(),
              FroggySpacer.y16(),
            ],
          ),
        ),
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Container(
            padding: const EdgeInsets.only(left: 8, right: 8, bottom: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
            ),
            child: const MiniRadioPlayerControl(),
          ),
        ),
      ],
    );
  }
}

class _CountrySearch extends StatefulHookWidget {
  const _CountrySearch();

  @override
  State<_CountrySearch> createState() => _CountrySearchState();
}

class _CountrySearchState extends State<_CountrySearch> {
  RatesBloc? bloc;

  @override
  void initState() {
    bloc = context.read<RatesBloc>();
    super.initState();
  }

  void onCountrySelected(CountryModel country) {
    bloc?.add(
      RatesEvent.started(
        country: country,
      ),
    );
  }

  void onSearchFormTapped() {
    FroggyCountries.showPicker(
      context: context,
      height: MediaQuery.of(context).size.height * 0.75,
      onCountrySelected: onCountrySelected,
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = useLocale();
    final hasInternet = useHasConnectivity();

    return BlocConsumer<RatesBloc, RatesState>(
      listener: (context, state) {
        if (state.hasError) {
          FroggyToast.showErrorToast(
            context,
            state.message ?? l10n.somethingWentWrongMessage,
          );
        }
      },
      builder: (context, state) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          margin: const EdgeInsets.only(top: 10),
          height: 50,
          decoration: BoxDecoration(
            color: FroggyColors.froggyGrey5,
            borderRadius: BorderRadius.circular(300),
          ),
          child: InkWell(
            onTap: !hasInternet ? null : onSearchFormTapped,
            child: Row(
              children: [
                const SizedBox(width: 10),
                Text(
                  state.country == null
                      ? l10n.searchCountryPlaceholder
                      : (state.country?.name ?? ''),
                  style: TextStyle(
                    color: state.country == null
                        ? FroggyColors.froggyGrey35
                        : FroggyColors.black,
                  ),
                ),
                const Spacer(),
                FroggyIconsList.countrySearchDropdownArrow.toWidget(
                  color: FroggyColors.froggyGrey2,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class _FeaturedCallRates extends HookWidget {
  const _FeaturedCallRates();

  @override
  Widget build(BuildContext context) {
    final l10n = useLocalizationsWithoutContext();

    return BlocBuilder<RatesBloc, RatesState>(
      builder: (context, state) {
        final rate = state.rates;
        final selectedCountry = state.country;
        final country = rate?.attributes?.country;

        if (state.message != null && state.country != null && state.hasError) {
          return Builder(
            builder: (context) {
              final country = state.country;

              return Column(
                children: [
                  FroggyCountries.showCountryFlag(
                    country == null
                        ? 'flags/nl.svg'
                        : 'flags/${country.code?.toLowerCase()}.svg',
                    width: 80,
                    margin: 0,
                  ),
                  Container(
                    margin: const EdgeInsets.symmetric(vertical: 6),
                    child: Text(
                      country == null
                          ? 'Netherlands'
                          : '${country.name} (${selectedCountry?.dialingCode})',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  Center(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 15),
                      child: Text(
                        state.message ?? '',
                        style: const TextStyle(
                          color: FroggyColors.error,
                          fontSize: 12,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ],
              );
            },
          );
        }

        return Skeletonizer(
          enabled: state.isLoading,
          child: Column(
            children: [
              FroggyCountries.showCountryFlag(
                country == null
                    ? 'flags/nl.svg'
                    : 'flags/${country.code?.toLowerCase()}.svg',
                width: 80,
                margin: 0,
              ),
              Container(
                margin: const EdgeInsets.symmetric(vertical: 6),
                child: Text(
                  country == null
                      ? 'Netherlands'
                      : '${country.name} (${selectedCountry?.dialingCode})',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              SizedBox(
                // height: 100,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _FeaturedCallRatePrice(
                      label: l10n.mobileText,
                      price:
                          rate?.attributes?.rateMobilePerMinute?.amount ?? '0',
                      icon: FroggyIconsList.phoneSolid.toWidget(
                        color: FroggyColors.froggyGrey2,
                      ),
                      alignment: CrossAxisAlignment.end,
                      currencyCode:
                          rate?.attributes?.rateLandlinePerMinute?.currency ??
                              '',
                    ),
                    SizedBox(
                      height: 80,
                      child: FroggyIconsList.callRatesDivider.toWidget(),
                    ),
                    _FeaturedCallRatePrice(
                      label: l10n.landlineText,
                      price: rate?.attributes?.rateLandlinePerMinute?.amount ??
                          '0',
                      icon: FroggyIconsList.landline.toWidget(),
                      alignment: CrossAxisAlignment.start,
                      currencyCode:
                          rate?.attributes?.rateLandlinePerMinute?.currency ??
                              '',
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

class _FeaturedCallRatePrice extends HookWidget {
  const _FeaturedCallRatePrice({
    required this.alignment,
    required this.label,
    required this.price,
    required this.icon,
    this.currencyCode = 'USD',
  });

  final CrossAxisAlignment alignment;
  final String label;
  final String price;
  final String currencyCode;
  final Widget icon;

  @override
  Widget build(BuildContext context) {
    final l10n = useLocale();
    // final currencyCode = useState<String?>(null);

    return BlocBuilder<RatesBloc, RatesState>(
      builder: (context, state) {
        // final currencyCode =
        //     state.rates?.attributes?.country?.currency?.code ?? 'NGN';

        return Container(
          margin: const EdgeInsets.symmetric(
            horizontal: 10,
            // vertical: 5,
          ),
          // height: 100,
          child: Column(
            crossAxisAlignment: alignment,
            // mainAxisSize: MainAxisSize.min,
            // textBaseline: TextBaseline.ideographic,
            children: [
              RichText(
                text: TextSpan(
                  style: const TextStyle(
                      // textBaseline: TextBaseline.alphabetic,
                      ),
                  children: [
                    WidgetSpan(
                      child: icon,
                    ),
                    WidgetSpan(
                      child: Container(
                        margin: const EdgeInsets.only(right: 8),
                      ),
                    ),
                    WidgetSpan(
                      child: Text(
                        label,
                      ),
                    ),
                    // TextSpan(
                    //   text: label,
                    // ),
                  ],
                ),
                strutStyle: const StrutStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                  forceStrutHeight: true,
                  height: 1.6,
                  // : FroggyColors.froggyGrey2,
                ),
              ),
              Text.rich(
                TextSpan(
                  text: price.toCurrency(
                    context,
                    symbol: currencyCode,
                    // decimalDigits: 3,
                  ),
                  children: [
                    TextSpan(
                      text: l10n.perMinRateSingle,
                      style: const TextStyle(
                        fontSize: 14,
                        height: 3.7,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
                style: const TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: FroggyColors.primary,
                ),
                textScaler: const TextScaler.linear(.8),
              ),
            ],
          ),
        );
      },
    );
  }
}
