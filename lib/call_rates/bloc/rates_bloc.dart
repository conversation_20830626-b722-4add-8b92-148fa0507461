import 'dart:async';

import 'package:countries/countries.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:froggytalk/call_rates/call_rates.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';

// import 'package:utils/utils.dart';

part 'rates_bloc.freezed.dart';
part 'rates_bloc.g.dart';
part 'rates_event.dart';
part 'rates_state.dart';

class RatesBloc extends HydratedBloc<RatesEvent, RatesState> {
  RatesBloc() : super(RatesState.initial()) {
    on<_Reset>(_onReset);
    on<_Started>(_onStarted);
    on<_GetNationalRates>(_onGetNationalRates);
    on<_EnabledOfflineMode>(_onEnabledOfflineMode);
  }

  final _callRatesRepository = CallRatesRepository();

  @override
  RatesState? fromJson(Map<String, dynamic> json) {
    try {
      return RatesState.fromJson(json);
    } catch (_) {
      return RatesState.initial();
    }
  }

  @override
  Map<String, dynamic>? toJson(RatesState state) {
    try {
      // return state.toJson();
      return {
        // 'country': state.country?.toJson(),
        'nationalRates': state.nationalRates?.toJson(),
        // 'rates': state.rates?.toJson(),
        // 'isLoading': state.isLoading,
        // 'hasError': state.hasError,
        // 'message': state.message,
        // 'hasInternetAccess': state.hasInternetAccess,
      };
    } catch (_) {
      return null;
    }
  }

  FutureOr<void> _onReset(
    _Reset event,
    Emitter<RatesState> emit,
  ) {
    emit(RatesState.initial());
  }

  FutureOr<void> _onStarted(_Started event, Emitter<RatesState> emit) async {
    if (isClosed) return;

    final country = event.country;
    final code2 = country?.code;
    if (code2 == null) return;

    emit(
      state.copyWith(
        isLoading: true,
        hasError: false,
      ),
    );

    final result = await _callRatesRepository.execute(
      countryCode: code2,
    );

    if (result.isRight) {
      final rates = result.right.data;
      emit(
        state.copyWith(
          country: country,
          rates: rates,
          isLoading: false,
          hasError: false,
        ),
      );
    } else {
      final errorResponse = result.left;

      switch (errorResponse.code) {
        case 'internet-out':
          emit(
            state.copyWith(
              isLoading: false,
              message: 'internet-out',
              // fallback rates
              rates: state.nationalRates,
            ),
          );
        case 'unauthorized':
          emit(
            state.copyWith(
              isLoading: false,
              hasError: true,
              message: 'Unauthorized',
            ),
          );
        default:
          emit(
            state.copyWith(
              isLoading: false,
              hasError: true,
              message: errorResponse.message,
            ),
          );
      }
    }
  }

  FutureOr<void> _onGetNationalRates(
    _GetNationalRates event,
    Emitter<RatesState> emit,
  ) async {
    final nationalCountry = event.nationalCountry;
    final code2 = nationalCountry.code;

    if (code2 == null) return;

    emit(
      state.copyWith(
        isLoading: true,
        hasError: false,
      ),
    );

    final result = await _callRatesRepository.execute(
      countryCode: code2,
    );

    if (result.isRight) {
      final rates = result.right.data;
      emit(
        state.copyWith(
          country: nationalCountry,
          rates: rates,
          nationalRates: rates,
          isLoading: false,
          hasError: false,
        ),
      );
    } else {
      final errorResponse = result.left;

      switch (errorResponse.code) {
        case 'internet-out':
          emit(
            state.copyWith(
              isLoading: false,
              message: 'internet-out',
              rates: state.nationalRates,
            ),
          );
        case 'unauthorized':
          emit(
            state.copyWith(
              isLoading: false,
              hasError: true,
              message: 'Unauthorized',
            ),
          );
        default:
          emit(
            state.copyWith(
              isLoading: false,
              hasError: true,
              message: errorResponse.message,
            ),
          );
      }
    }
  }

  FutureOr<void> _onEnabledOfflineMode(
    _EnabledOfflineMode event,
    Emitter<RatesState> emit,
  ) {
    emit(
      state.copyWith(
        isLoading: false,
        hasError: false,
        rates: state.nationalRates,
      ),
    );
  }
}
