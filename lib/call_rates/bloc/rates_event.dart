part of 'rates_bloc.dart';

@freezed
class RatesEvent with _$RatesEvent {
  const factory RatesEvent.started({
    CountryModel? country,
    @Default(false) bool isNationalRate,
  }) = _Started;

  const factory RatesEvent.getNationalRates({
    required CountryModel nationalCountry,
  }) = _GetNationalRates;

  const factory RatesEvent.enabledOfflineMode({
    required CountryModel country,
  }) = _EnabledOfflineMode;

  const factory RatesEvent.reset() = _Reset;
}
