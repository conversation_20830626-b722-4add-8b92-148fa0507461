part of 'rates_bloc.dart';

@freezed
class RatesState with _$RatesState {
  factory RatesState({
    CallRatesResponse? nationalRates,
    CallRatesResponse? rates,
    CountryModel? country,
    @Default(false) bool isLoading,
    @Default(false) bool hasError,
    String? message,
  }) = _RatesState;

  RatesState._();

  // initial state
  factory RatesState.initial() => RatesState();

  factory RatesState.fromJson(Map<String, dynamic> json) =>
      _$RatesStateFromJson(json);
}
