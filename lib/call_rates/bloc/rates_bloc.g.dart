// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'rates_bloc.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RatesStateImpl _$$RatesStateImplFromJson(Map<String, dynamic> json) =>
    _$RatesStateImpl(
      nationalRates: json['nationalRates'] == null
          ? null
          : CallRatesResponse.fromJson(
              json['nationalRates'] as Map<String, dynamic>),
      rates: json['rates'] == null
          ? null
          : CallRatesResponse.fromJson(json['rates'] as Map<String, dynamic>),
      country: json['country'] == null
          ? null
          : CountryModel.fromJson(json['country'] as Map<String, dynamic>),
      isLoading: json['isLoading'] as bool? ?? false,
      hasError: json['hasError'] as bool? ?? false,
      message: json['message'] as String?,
    );

const _$$RatesStateImplFieldMap = <String, String>{
  'nationalRates': 'nationalRates',
  'rates': 'rates',
  'country': 'country',
  'isLoading': 'isLoading',
  'hasError': 'hasError',
  'message': 'message',
};

Map<String, dynamic> _$$RatesStateImplToJson(_$RatesStateImpl instance) =>
    <String, dynamic>{
      if (instance.nationalRates?.toJson() case final value?)
        'nationalRates': value,
      if (instance.rates?.toJson() case final value?) 'rates': value,
      if (instance.country?.toJson() case final value?) 'country': value,
      'isLoading': instance.isLoading,
      'hasError': instance.hasError,
      if (instance.message case final value?) 'message': value,
    };
