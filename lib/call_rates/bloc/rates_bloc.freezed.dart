// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'rates_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$RatesEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(CountryModel? country, bool isNationalRate)
        started,
    required TResult Function(CountryModel nationalCountry) getNationalRates,
    required TResult Function(CountryModel country) enabledOfflineMode,
    required TResult Function() reset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(CountryModel? country, bool isNationalRate)? started,
    TResult? Function(CountryModel nationalCountry)? getNationalRates,
    TResult? Function(CountryModel country)? enabledOfflineMode,
    TResult? Function()? reset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(CountryModel? country, bool isNationalRate)? started,
    TResult Function(CountryModel nationalCountry)? getNationalRates,
    TResult Function(CountryModel country)? enabledOfflineMode,
    TResult Function()? reset,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_GetNationalRates value) getNationalRates,
    required TResult Function(_EnabledOfflineMode value) enabledOfflineMode,
    required TResult Function(_Reset value) reset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_GetNationalRates value)? getNationalRates,
    TResult? Function(_EnabledOfflineMode value)? enabledOfflineMode,
    TResult? Function(_Reset value)? reset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_GetNationalRates value)? getNationalRates,
    TResult Function(_EnabledOfflineMode value)? enabledOfflineMode,
    TResult Function(_Reset value)? reset,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RatesEventCopyWith<$Res> {
  factory $RatesEventCopyWith(
          RatesEvent value, $Res Function(RatesEvent) then) =
      _$RatesEventCopyWithImpl<$Res, RatesEvent>;
}

/// @nodoc
class _$RatesEventCopyWithImpl<$Res, $Val extends RatesEvent>
    implements $RatesEventCopyWith<$Res> {
  _$RatesEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RatesEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$StartedImplCopyWith<$Res> {
  factory _$$StartedImplCopyWith(
          _$StartedImpl value, $Res Function(_$StartedImpl) then) =
      __$$StartedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({CountryModel? country, bool isNationalRate});

  $CountryModelCopyWith<$Res>? get country;
}

/// @nodoc
class __$$StartedImplCopyWithImpl<$Res>
    extends _$RatesEventCopyWithImpl<$Res, _$StartedImpl>
    implements _$$StartedImplCopyWith<$Res> {
  __$$StartedImplCopyWithImpl(
      _$StartedImpl _value, $Res Function(_$StartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of RatesEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? country = freezed,
    Object? isNationalRate = null,
  }) {
    return _then(_$StartedImpl(
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as CountryModel?,
      isNationalRate: null == isNationalRate
          ? _value.isNationalRate
          : isNationalRate // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of RatesEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CountryModelCopyWith<$Res>? get country {
    if (_value.country == null) {
      return null;
    }

    return $CountryModelCopyWith<$Res>(_value.country!, (value) {
      return _then(_value.copyWith(country: value));
    });
  }
}

/// @nodoc

class _$StartedImpl implements _Started {
  const _$StartedImpl({this.country, this.isNationalRate = false});

  @override
  final CountryModel? country;
  @override
  @JsonKey()
  final bool isNationalRate;

  @override
  String toString() {
    return 'RatesEvent.started(country: $country, isNationalRate: $isNationalRate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StartedImpl &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.isNationalRate, isNationalRate) ||
                other.isNationalRate == isNationalRate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, country, isNationalRate);

  /// Create a copy of RatesEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StartedImplCopyWith<_$StartedImpl> get copyWith =>
      __$$StartedImplCopyWithImpl<_$StartedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(CountryModel? country, bool isNationalRate)
        started,
    required TResult Function(CountryModel nationalCountry) getNationalRates,
    required TResult Function(CountryModel country) enabledOfflineMode,
    required TResult Function() reset,
  }) {
    return started(country, isNationalRate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(CountryModel? country, bool isNationalRate)? started,
    TResult? Function(CountryModel nationalCountry)? getNationalRates,
    TResult? Function(CountryModel country)? enabledOfflineMode,
    TResult? Function()? reset,
  }) {
    return started?.call(country, isNationalRate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(CountryModel? country, bool isNationalRate)? started,
    TResult Function(CountryModel nationalCountry)? getNationalRates,
    TResult Function(CountryModel country)? enabledOfflineMode,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started(country, isNationalRate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_GetNationalRates value) getNationalRates,
    required TResult Function(_EnabledOfflineMode value) enabledOfflineMode,
    required TResult Function(_Reset value) reset,
  }) {
    return started(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_GetNationalRates value)? getNationalRates,
    TResult? Function(_EnabledOfflineMode value)? enabledOfflineMode,
    TResult? Function(_Reset value)? reset,
  }) {
    return started?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_GetNationalRates value)? getNationalRates,
    TResult Function(_EnabledOfflineMode value)? enabledOfflineMode,
    TResult Function(_Reset value)? reset,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started(this);
    }
    return orElse();
  }
}

abstract class _Started implements RatesEvent {
  const factory _Started(
      {final CountryModel? country, final bool isNationalRate}) = _$StartedImpl;

  CountryModel? get country;
  bool get isNationalRate;

  /// Create a copy of RatesEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StartedImplCopyWith<_$StartedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$GetNationalRatesImplCopyWith<$Res> {
  factory _$$GetNationalRatesImplCopyWith(_$GetNationalRatesImpl value,
          $Res Function(_$GetNationalRatesImpl) then) =
      __$$GetNationalRatesImplCopyWithImpl<$Res>;
  @useResult
  $Res call({CountryModel nationalCountry});

  $CountryModelCopyWith<$Res> get nationalCountry;
}

/// @nodoc
class __$$GetNationalRatesImplCopyWithImpl<$Res>
    extends _$RatesEventCopyWithImpl<$Res, _$GetNationalRatesImpl>
    implements _$$GetNationalRatesImplCopyWith<$Res> {
  __$$GetNationalRatesImplCopyWithImpl(_$GetNationalRatesImpl _value,
      $Res Function(_$GetNationalRatesImpl) _then)
      : super(_value, _then);

  /// Create a copy of RatesEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nationalCountry = null,
  }) {
    return _then(_$GetNationalRatesImpl(
      nationalCountry: null == nationalCountry
          ? _value.nationalCountry
          : nationalCountry // ignore: cast_nullable_to_non_nullable
              as CountryModel,
    ));
  }

  /// Create a copy of RatesEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CountryModelCopyWith<$Res> get nationalCountry {
    return $CountryModelCopyWith<$Res>(_value.nationalCountry, (value) {
      return _then(_value.copyWith(nationalCountry: value));
    });
  }
}

/// @nodoc

class _$GetNationalRatesImpl implements _GetNationalRates {
  const _$GetNationalRatesImpl({required this.nationalCountry});

  @override
  final CountryModel nationalCountry;

  @override
  String toString() {
    return 'RatesEvent.getNationalRates(nationalCountry: $nationalCountry)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GetNationalRatesImpl &&
            (identical(other.nationalCountry, nationalCountry) ||
                other.nationalCountry == nationalCountry));
  }

  @override
  int get hashCode => Object.hash(runtimeType, nationalCountry);

  /// Create a copy of RatesEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GetNationalRatesImplCopyWith<_$GetNationalRatesImpl> get copyWith =>
      __$$GetNationalRatesImplCopyWithImpl<_$GetNationalRatesImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(CountryModel? country, bool isNationalRate)
        started,
    required TResult Function(CountryModel nationalCountry) getNationalRates,
    required TResult Function(CountryModel country) enabledOfflineMode,
    required TResult Function() reset,
  }) {
    return getNationalRates(nationalCountry);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(CountryModel? country, bool isNationalRate)? started,
    TResult? Function(CountryModel nationalCountry)? getNationalRates,
    TResult? Function(CountryModel country)? enabledOfflineMode,
    TResult? Function()? reset,
  }) {
    return getNationalRates?.call(nationalCountry);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(CountryModel? country, bool isNationalRate)? started,
    TResult Function(CountryModel nationalCountry)? getNationalRates,
    TResult Function(CountryModel country)? enabledOfflineMode,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (getNationalRates != null) {
      return getNationalRates(nationalCountry);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_GetNationalRates value) getNationalRates,
    required TResult Function(_EnabledOfflineMode value) enabledOfflineMode,
    required TResult Function(_Reset value) reset,
  }) {
    return getNationalRates(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_GetNationalRates value)? getNationalRates,
    TResult? Function(_EnabledOfflineMode value)? enabledOfflineMode,
    TResult? Function(_Reset value)? reset,
  }) {
    return getNationalRates?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_GetNationalRates value)? getNationalRates,
    TResult Function(_EnabledOfflineMode value)? enabledOfflineMode,
    TResult Function(_Reset value)? reset,
    required TResult orElse(),
  }) {
    if (getNationalRates != null) {
      return getNationalRates(this);
    }
    return orElse();
  }
}

abstract class _GetNationalRates implements RatesEvent {
  const factory _GetNationalRates(
      {required final CountryModel nationalCountry}) = _$GetNationalRatesImpl;

  CountryModel get nationalCountry;

  /// Create a copy of RatesEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GetNationalRatesImplCopyWith<_$GetNationalRatesImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$EnabledOfflineModeImplCopyWith<$Res> {
  factory _$$EnabledOfflineModeImplCopyWith(_$EnabledOfflineModeImpl value,
          $Res Function(_$EnabledOfflineModeImpl) then) =
      __$$EnabledOfflineModeImplCopyWithImpl<$Res>;
  @useResult
  $Res call({CountryModel country});

  $CountryModelCopyWith<$Res> get country;
}

/// @nodoc
class __$$EnabledOfflineModeImplCopyWithImpl<$Res>
    extends _$RatesEventCopyWithImpl<$Res, _$EnabledOfflineModeImpl>
    implements _$$EnabledOfflineModeImplCopyWith<$Res> {
  __$$EnabledOfflineModeImplCopyWithImpl(_$EnabledOfflineModeImpl _value,
      $Res Function(_$EnabledOfflineModeImpl) _then)
      : super(_value, _then);

  /// Create a copy of RatesEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? country = null,
  }) {
    return _then(_$EnabledOfflineModeImpl(
      country: null == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as CountryModel,
    ));
  }

  /// Create a copy of RatesEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CountryModelCopyWith<$Res> get country {
    return $CountryModelCopyWith<$Res>(_value.country, (value) {
      return _then(_value.copyWith(country: value));
    });
  }
}

/// @nodoc

class _$EnabledOfflineModeImpl implements _EnabledOfflineMode {
  const _$EnabledOfflineModeImpl({required this.country});

  @override
  final CountryModel country;

  @override
  String toString() {
    return 'RatesEvent.enabledOfflineMode(country: $country)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EnabledOfflineModeImpl &&
            (identical(other.country, country) || other.country == country));
  }

  @override
  int get hashCode => Object.hash(runtimeType, country);

  /// Create a copy of RatesEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EnabledOfflineModeImplCopyWith<_$EnabledOfflineModeImpl> get copyWith =>
      __$$EnabledOfflineModeImplCopyWithImpl<_$EnabledOfflineModeImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(CountryModel? country, bool isNationalRate)
        started,
    required TResult Function(CountryModel nationalCountry) getNationalRates,
    required TResult Function(CountryModel country) enabledOfflineMode,
    required TResult Function() reset,
  }) {
    return enabledOfflineMode(country);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(CountryModel? country, bool isNationalRate)? started,
    TResult? Function(CountryModel nationalCountry)? getNationalRates,
    TResult? Function(CountryModel country)? enabledOfflineMode,
    TResult? Function()? reset,
  }) {
    return enabledOfflineMode?.call(country);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(CountryModel? country, bool isNationalRate)? started,
    TResult Function(CountryModel nationalCountry)? getNationalRates,
    TResult Function(CountryModel country)? enabledOfflineMode,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (enabledOfflineMode != null) {
      return enabledOfflineMode(country);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_GetNationalRates value) getNationalRates,
    required TResult Function(_EnabledOfflineMode value) enabledOfflineMode,
    required TResult Function(_Reset value) reset,
  }) {
    return enabledOfflineMode(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_GetNationalRates value)? getNationalRates,
    TResult? Function(_EnabledOfflineMode value)? enabledOfflineMode,
    TResult? Function(_Reset value)? reset,
  }) {
    return enabledOfflineMode?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_GetNationalRates value)? getNationalRates,
    TResult Function(_EnabledOfflineMode value)? enabledOfflineMode,
    TResult Function(_Reset value)? reset,
    required TResult orElse(),
  }) {
    if (enabledOfflineMode != null) {
      return enabledOfflineMode(this);
    }
    return orElse();
  }
}

abstract class _EnabledOfflineMode implements RatesEvent {
  const factory _EnabledOfflineMode({required final CountryModel country}) =
      _$EnabledOfflineModeImpl;

  CountryModel get country;

  /// Create a copy of RatesEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EnabledOfflineModeImplCopyWith<_$EnabledOfflineModeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ResetImplCopyWith<$Res> {
  factory _$$ResetImplCopyWith(
          _$ResetImpl value, $Res Function(_$ResetImpl) then) =
      __$$ResetImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ResetImplCopyWithImpl<$Res>
    extends _$RatesEventCopyWithImpl<$Res, _$ResetImpl>
    implements _$$ResetImplCopyWith<$Res> {
  __$$ResetImplCopyWithImpl(
      _$ResetImpl _value, $Res Function(_$ResetImpl) _then)
      : super(_value, _then);

  /// Create a copy of RatesEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ResetImpl implements _Reset {
  const _$ResetImpl();

  @override
  String toString() {
    return 'RatesEvent.reset()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ResetImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(CountryModel? country, bool isNationalRate)
        started,
    required TResult Function(CountryModel nationalCountry) getNationalRates,
    required TResult Function(CountryModel country) enabledOfflineMode,
    required TResult Function() reset,
  }) {
    return reset();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(CountryModel? country, bool isNationalRate)? started,
    TResult? Function(CountryModel nationalCountry)? getNationalRates,
    TResult? Function(CountryModel country)? enabledOfflineMode,
    TResult? Function()? reset,
  }) {
    return reset?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(CountryModel? country, bool isNationalRate)? started,
    TResult Function(CountryModel nationalCountry)? getNationalRates,
    TResult Function(CountryModel country)? enabledOfflineMode,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (reset != null) {
      return reset();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_GetNationalRates value) getNationalRates,
    required TResult Function(_EnabledOfflineMode value) enabledOfflineMode,
    required TResult Function(_Reset value) reset,
  }) {
    return reset(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_GetNationalRates value)? getNationalRates,
    TResult? Function(_EnabledOfflineMode value)? enabledOfflineMode,
    TResult? Function(_Reset value)? reset,
  }) {
    return reset?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_GetNationalRates value)? getNationalRates,
    TResult Function(_EnabledOfflineMode value)? enabledOfflineMode,
    TResult Function(_Reset value)? reset,
    required TResult orElse(),
  }) {
    if (reset != null) {
      return reset(this);
    }
    return orElse();
  }
}

abstract class _Reset implements RatesEvent {
  const factory _Reset() = _$ResetImpl;
}

RatesState _$RatesStateFromJson(Map<String, dynamic> json) {
  return _RatesState.fromJson(json);
}

/// @nodoc
mixin _$RatesState {
  CallRatesResponse? get nationalRates => throw _privateConstructorUsedError;
  CallRatesResponse? get rates => throw _privateConstructorUsedError;
  CountryModel? get country => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  bool get hasError => throw _privateConstructorUsedError;
  String? get message => throw _privateConstructorUsedError;

  /// Serializes this RatesState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RatesState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RatesStateCopyWith<RatesState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RatesStateCopyWith<$Res> {
  factory $RatesStateCopyWith(
          RatesState value, $Res Function(RatesState) then) =
      _$RatesStateCopyWithImpl<$Res, RatesState>;
  @useResult
  $Res call(
      {CallRatesResponse? nationalRates,
      CallRatesResponse? rates,
      CountryModel? country,
      bool isLoading,
      bool hasError,
      String? message});

  $CallRatesResponseCopyWith<$Res>? get nationalRates;
  $CallRatesResponseCopyWith<$Res>? get rates;
  $CountryModelCopyWith<$Res>? get country;
}

/// @nodoc
class _$RatesStateCopyWithImpl<$Res, $Val extends RatesState>
    implements $RatesStateCopyWith<$Res> {
  _$RatesStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RatesState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nationalRates = freezed,
    Object? rates = freezed,
    Object? country = freezed,
    Object? isLoading = null,
    Object? hasError = null,
    Object? message = freezed,
  }) {
    return _then(_value.copyWith(
      nationalRates: freezed == nationalRates
          ? _value.nationalRates
          : nationalRates // ignore: cast_nullable_to_non_nullable
              as CallRatesResponse?,
      rates: freezed == rates
          ? _value.rates
          : rates // ignore: cast_nullable_to_non_nullable
              as CallRatesResponse?,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as CountryModel?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      hasError: null == hasError
          ? _value.hasError
          : hasError // ignore: cast_nullable_to_non_nullable
              as bool,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of RatesState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CallRatesResponseCopyWith<$Res>? get nationalRates {
    if (_value.nationalRates == null) {
      return null;
    }

    return $CallRatesResponseCopyWith<$Res>(_value.nationalRates!, (value) {
      return _then(_value.copyWith(nationalRates: value) as $Val);
    });
  }

  /// Create a copy of RatesState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CallRatesResponseCopyWith<$Res>? get rates {
    if (_value.rates == null) {
      return null;
    }

    return $CallRatesResponseCopyWith<$Res>(_value.rates!, (value) {
      return _then(_value.copyWith(rates: value) as $Val);
    });
  }

  /// Create a copy of RatesState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CountryModelCopyWith<$Res>? get country {
    if (_value.country == null) {
      return null;
    }

    return $CountryModelCopyWith<$Res>(_value.country!, (value) {
      return _then(_value.copyWith(country: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RatesStateImplCopyWith<$Res>
    implements $RatesStateCopyWith<$Res> {
  factory _$$RatesStateImplCopyWith(
          _$RatesStateImpl value, $Res Function(_$RatesStateImpl) then) =
      __$$RatesStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {CallRatesResponse? nationalRates,
      CallRatesResponse? rates,
      CountryModel? country,
      bool isLoading,
      bool hasError,
      String? message});

  @override
  $CallRatesResponseCopyWith<$Res>? get nationalRates;
  @override
  $CallRatesResponseCopyWith<$Res>? get rates;
  @override
  $CountryModelCopyWith<$Res>? get country;
}

/// @nodoc
class __$$RatesStateImplCopyWithImpl<$Res>
    extends _$RatesStateCopyWithImpl<$Res, _$RatesStateImpl>
    implements _$$RatesStateImplCopyWith<$Res> {
  __$$RatesStateImplCopyWithImpl(
      _$RatesStateImpl _value, $Res Function(_$RatesStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of RatesState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nationalRates = freezed,
    Object? rates = freezed,
    Object? country = freezed,
    Object? isLoading = null,
    Object? hasError = null,
    Object? message = freezed,
  }) {
    return _then(_$RatesStateImpl(
      nationalRates: freezed == nationalRates
          ? _value.nationalRates
          : nationalRates // ignore: cast_nullable_to_non_nullable
              as CallRatesResponse?,
      rates: freezed == rates
          ? _value.rates
          : rates // ignore: cast_nullable_to_non_nullable
              as CallRatesResponse?,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as CountryModel?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      hasError: null == hasError
          ? _value.hasError
          : hasError // ignore: cast_nullable_to_non_nullable
              as bool,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RatesStateImpl extends _RatesState {
  _$RatesStateImpl(
      {this.nationalRates,
      this.rates,
      this.country,
      this.isLoading = false,
      this.hasError = false,
      this.message})
      : super._();

  factory _$RatesStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$RatesStateImplFromJson(json);

  @override
  final CallRatesResponse? nationalRates;
  @override
  final CallRatesResponse? rates;
  @override
  final CountryModel? country;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool hasError;
  @override
  final String? message;

  @override
  String toString() {
    return 'RatesState(nationalRates: $nationalRates, rates: $rates, country: $country, isLoading: $isLoading, hasError: $hasError, message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RatesStateImpl &&
            (identical(other.nationalRates, nationalRates) ||
                other.nationalRates == nationalRates) &&
            (identical(other.rates, rates) || other.rates == rates) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.hasError, hasError) ||
                other.hasError == hasError) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, nationalRates, rates, country, isLoading, hasError, message);

  /// Create a copy of RatesState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RatesStateImplCopyWith<_$RatesStateImpl> get copyWith =>
      __$$RatesStateImplCopyWithImpl<_$RatesStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RatesStateImplToJson(
      this,
    );
  }
}

abstract class _RatesState extends RatesState {
  factory _RatesState(
      {final CallRatesResponse? nationalRates,
      final CallRatesResponse? rates,
      final CountryModel? country,
      final bool isLoading,
      final bool hasError,
      final String? message}) = _$RatesStateImpl;
  _RatesState._() : super._();

  factory _RatesState.fromJson(Map<String, dynamic> json) =
      _$RatesStateImpl.fromJson;

  @override
  CallRatesResponse? get nationalRates;
  @override
  CallRatesResponse? get rates;
  @override
  CountryModel? get country;
  @override
  bool get isLoading;
  @override
  bool get hasError;
  @override
  String? get message;

  /// Create a copy of RatesState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RatesStateImplCopyWith<_$RatesStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
