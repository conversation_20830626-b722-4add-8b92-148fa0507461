import 'dart:async';

import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggy_icons/froggy_icons.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/app/data/services/event_tracker.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:froggytalk/buy_credit/buy_credit.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:navigation/navigation.dart';
import 'package:utils/utils.dart';

class PaymentSuccessfulPage extends StatefulHookWidget {
  const PaymentSuccessfulPage({this.amount, super.key});

  final String? amount;

  static Route<Object?> route({
    required String amount,
  }) {
    return MaterialPageRoute<void>(
      builder: (_) => PaymentSuccessfulPage(
        amount: amount,
      ),
    );
  }

  static String routeName = '/payment_confirmation';

  @override
  State<PaymentSuccessfulPage> createState() => _PaymentSuccessfulPageState();
}

class _PaymentSuccessfulPageState extends State<PaymentSuccessfulPage> {
  // ignore: unused_field
  // String _platformVersion = 'Unknown';
  // final inAppReview = InAppReview.instance;

  @override
  void initState() {
    super.initState();
    // initPlatformState();
    initAdvancedAppReview();
    // initAppReview();

    // context
    //     .read<EventTrackerService>()
    //     .trackEvent('Payment of ${widget.amount} was successful');
  }

  Future<void> initAdvancedAppReview() async {
    // AdvancedInAppReview()
    //     .setMinDaysBeforeRemind(14)
    //     .setMinDaysAfterInstall(0)
    //     .setMinLaunchTimes(1)
    //     .setMinSecondsBeforeShowDialog(2)
    //     .monitor();

    final inAppReview = InAppReview.instance;

    if (await inAppReview.isAvailable()) {
      await inAppReview.requestReview();
    }
  }

  // Future<void> initAppReview() async {
  //   if (await inAppReview.isAvailable()) {
  //     await inAppReview.requestReview();
  //   }
  // }

  // // Platform messages are asynchronous, so we initialize in an async method.
  // Future<void> initPlatformState() async {
  //   String platformVersion;
  //   // Platform messages may fail, so we use a try/catch PlatformException.
  //   // We also handle the message potentially returning null.
  //   try {
  //     platformVersion = await AdvancedInAppReview.platformVersion ??
  //         'Unknown platform version';
  //   } on PlatformException {
  //     platformVersion = 'Failed to get platform version.';
  //   }

  //   // If the widget was removed from the tree while the asynchronous platform
  //   // message was in flight, we want to discard the reply rather than calling
  //   // setState to update our non-existent appearance.
  //   if (!mounted) return;

  //   setState(() {
  //     _platformVersion = platformVersion;
  //   });
  // }

  @override
  Widget build(BuildContext context) {
    final authBloc = context.read<AuthenticationBloc>();
    final authUserCurrency = useAuthUserCurrencyCode() ?? 'USD';
    final l10n = useLocalizations();

    final onConfirmPaymentButtonPressed = useCallback(() {
      vibrate(const Duration(milliseconds: 100));

      authBloc.add(const AuthenticationEvent.refreshProfile());

      FroggyRouter.pushAndRemoveUntil(const AppDashboardPage());
    });

    return Scaffold(
      appBar: FroggyAppBar(
        label: l10n.confirmationAppBarTitle,
        labelFontSize: 20,
      ),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Spacer(),
          Center(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 15),
              child: FroggyIconsList.paymentConfirmed.toWidget(),
            ),
          ),
          // const Spacer(),
          Center(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 15),
              child: Text(
                l10n.confirmationSuccessTitle,
                style: const TextStyle(
                  fontSize: 20,
                  color: FroggyColors.black,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          // const Spacer(
          //   flex: 2,
          // ),
          BlocConsumer<CheckoutBloc, CheckoutState>(
            listener: (context, state) {
              if (state.session?.totalPayment != null) {
                context.read<EventTrackerService>().logEvent(
                      schema: 'payment_success',
                      description: 'Payment of ${state.session?.totalPayment} '
                          'was successful',
                    );

                BuyCreditEvents.onPaymentSuccess(
                  currency: state.currencyCode,
                  value: (state.buyCreditOption?.totalPayment ?? '0').toDouble,
                  sessionId: state.session?.id ?? '',
                  couponCode: state.couponCode.value,
                );
              }
            },
            builder: (context, state) {
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 5),
                child: Text.rich(
                  TextSpan(
                    text: l10n.confirmationSuccessDescription(
                      (widget.amount ?? state.session?.amountToBeCredited)
                              ?.toCurrency(
                            context,
                            decimalDigits: 3,
                            symbol: authUserCurrency,
                          ) ??
                          '0',
                    ),
                  ),
                  style: const TextStyle(
                    fontSize: 12,
                    color: FroggyColors.black,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              );
            },
          ),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
            child: OutlinedButton(
              onPressed: onConfirmPaymentButtonPressed,
              style: OutlinedButton.styleFrom(
                elevation: 0,
                iconColor: FroggyColors.primary,
                backgroundColor: FroggyColors.froggyLighterGreen,
                side: BorderSide.none,
                padding:
                    const EdgeInsets.symmetric(horizontal: 18, vertical: 10),
              ),
              // child: const Text('Go back to Dashboard'),
              child: Text(l10n.paymentFailureHomeButton),
            ),
          ),
          const Spacer(
            flex: 3,
          ),
        ],
      ),
    );
  }
}
