import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:formz/formz.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:froggytalk/buy_credit/buy_credit.dart';
import 'package:froggytalk/buy_credit/components/components.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:navigation/navigation.dart';
import 'package:utils/utils.dart';

/// A page that displays checkout options for purchasing credits.
///
/// This page allows users to select payment methods, apply coupons,
/// and complete the checkout process.
class CheckoutPage extends HookWidget {
  /// Creates an instance of [CheckoutPage].
  const CheckoutPage({super.key});

  /// Generates a route for this page.
  ///
  /// If a [bloc] is provided, it will be used instead of creating a new one.
  static Route<Object?> route({CheckoutBloc? bloc}) {
    return MaterialPageRoute<Object?>(
      builder: (_) {
        if (bloc != null) {
          return BlocProvider.value(
            value: bloc..add(const CheckoutEvent.checkoutInitiated()),
            child: const CheckoutPage(),
          );
        }

        return const CheckoutPage();
      },
    );
  }

  /// The route name for this page.
  static String routeName = '/payment_options';

  @override
  Widget build(BuildContext context) {
    final isAutoCreditEnabled = useState(false);
    final bloc = context.read<CheckoutBloc>();
    final authUser = useAuthUser();
    final showRestorePurchasesButton =
        useSettings().getSetting<bool>('showRestorePurchasesButton') ?? false;
    final shouldShowRestorePurchasesButton = showRestorePurchasesButton;

    final onPayWithCard = useCallback(
      () {
        bloc.add(CheckoutEvent.cardPaymentProcessed(user: authUser!));
      },
      [bloc, authUser],
    );

    final onPayWithIapStore = useCallback(
      () {
        bloc.add(
          isAndroid()
              ? const CheckoutEvent.googlePayPaymentProcessed()
              : const CheckoutEvent.applePayPaymentProcessed(),
        );
      },
      [bloc],
    );

    final onAutoCreditButtonPressed = useCallback(
      () {
        isAutoCreditEnabled.value = !isAutoCreditEnabled.value;

        bloc.add(
          CheckoutEvent.automaticCreditToggled(
            value: isAutoCreditEnabled.value,
          ),
        );
      },
      [isAutoCreditEnabled, bloc],
    );

    final onRestorePurchasesPressed = useCallback(
      () {
        bloc.add(const CheckoutEvent.restorePurchases());
      },
      [bloc],
    );

    final l10n = context.l10n;

    return Scaffold(
      appBar: FroggyAppBar(
        label: l10n.paymentOptionsAppBarTitle,
        labelFontSize: 20,
      ),
      body: BlocConsumer<CheckoutBloc, CheckoutState>(
        listener: (context, state) {
          if (state.paymentSheetStatus.isSuccessful) {
            FroggyRouter.pushAndRemoveUntil(
              BlocProvider.value(
                value: bloc,
                child: const PaymentSuccessfulPage(),
              ),
            );
          }

          if (state.paymentSheetStatus.isFailure) {
            FroggyRouter.push(
              BlocProvider.value(
                value: bloc,
                child: const PaymentFailedPage(),
              ),
            );
          }

          if (state.paymentSheetStatus.isCancelled) {
            // stop loading
          }
        },
        builder: (context, state) {
          return Column(
            children: [
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 25),
                child: Text(
                  l10n.paymentOptionsSelectPaymentMethod,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              // const Expanded(child: PaymentButtonsComponent()),
              const PaymentButtonsComponent(),
              // FroggySpacer.y2(),
              AutocreditCheckboxComponent(
                onAutoCreditButtonPressed: onAutoCreditButtonPressed,
              ),
              const Spacer(flex: 2),
              const CouponCodeFieldComponent(),
              Container(
                margin:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 3),
                decoration: BoxDecoration(
                  color: FroggyColors.froggyGrey5,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: const PaymentSummaryComponent(),
              ),
              Container(
                margin: const EdgeInsets.only(top: 10, bottom: 10),
                padding: const EdgeInsets.symmetric(horizontal: 20),
                width: double.infinity,
                height: 48,
                child: ElevatedButton(
                  onPressed: state.status.isInProgress
                      ? null
                      : (state.paymentMethod.isIapStore
                          ? onPayWithIapStore
                          : onPayWithCard),
                  style: OutlinedButton.styleFrom(
                    side: BorderSide.none,
                  ),
                  child: state.status.isInProgress
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            color: FroggyColors.froggyGrey35,
                          ),
                        )
                      : Text(
                          l10n.proceedToPaymentButtonText,
                          style: const TextStyle(
                            color: FroggyColors.white,
                            fontSize: 16,
                          ),
                        ),
                ),
              ),
              // Add restore purchases button
              // Only show if store is available
              if (state.isStoreAvailable && shouldShowRestorePurchasesButton)
                Container(
                  margin: const EdgeInsets.only(bottom: 5),
                  child: TextButton(
                    onPressed: state.status.isInProgress
                        ? null
                        : onRestorePurchasesPressed,
                    child: Text(
                      context.l10n.restorePurchases,
                      style: const TextStyle(
                        color: FroggyColors.grey,
                        fontSize: 14,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                ),
              const Spacer(),
            ],
          );
        },
      ),
    );
  }
}
