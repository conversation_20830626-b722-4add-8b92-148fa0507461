import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/app/data/services/event_tracker.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:froggytalk/buy_credit/buy_credit.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:navigation/navigation.dart';
import 'package:utils/utils.dart';

class PaymentFailedPage extends HookWidget {
  const PaymentFailedPage({this.amount, super.key});

  final String? amount;

  static Route<Object?> route({
    required String amount,
  }) {
    return MaterialPageRoute<void>(
      builder: (_) => PaymentFailedPage(
        amount: amount,
      ),
    );
  }

  static String routeName = '/payment_failed';

  @override
  Widget build(BuildContext context) {
    final authUserCurrency = useAuthUserCurrencyCode() ?? 'USD';
    final bloc = context.read<CheckoutBloc>();
    final authBloc = context.read<AuthenticationBloc>();
    final l10n = useLocale();

    final goToDashboard = useCallback(() {
      authBloc.add(const AuthenticationEvent.refreshProfile());
      FroggyRouter.pushAndRemoveUntil(const AppDashboardPage());
    });

    final tryPaymentAgain = useCallback(() {
      vibrate(const Duration(milliseconds: 100));

      // bloc.add(CheckoutEvent.payWithCard(user: authUser!));
      // FroggyRouter.pushAndRemoveUntil(const AppDashboardPage());
      Navigator.of(context).pop(CheckoutPage.route(bloc: bloc));
      // Navigator.of(context).push(CheckoutPage.route(bloc: bloc));

      context.read<EventTrackerService>().logEvent(
            schema: 'retry_payment',
            description: 'User is Retrying Payment of $amount again',
          );
    });

    useEffect(() {
      context.read<EventTrackerService>().logEvent(
            schema: 'payment_failure',
            description: 'Payment of $amount failed',
          );

      return null;
    });

    return BlocListener<CheckoutBloc, CheckoutState>(
      listener: (context, state) {
        BuyCreditEvents.onPaymentFailure(
          currency: state.currencyCode,
          value: (state.buyCreditOption?.amountToBeCredited?.amount ?? '0')
              .toDouble,
          sessionId: state.session?.id ?? '',
          couponCode: state.couponCode.value,
        );
      },
      child: Scaffold(
        appBar: FroggyAppBar(
          label: l10n.paymentFailureAppBarTitle,
          labelFontSize: 20,
        ),
        body: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Spacer(),
            Center(
              child: Container(
                margin:
                    const EdgeInsets.symmetric(horizontal: 15, vertical: 25),
                child: Image.asset(
                  'assets/logos/froggy-sad.png',
                  width: 180,
                  height: 180,
                ),
              ),
            ),
            // const Spacer(),
            Center(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 15),
                child: Text(
                  l10n.paymentFailureTitle,
                  style: const TextStyle(
                    fontSize: 20,
                    color: FroggyColors.black,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
            // const Spacer(
            //   flex: 2,
            // ),
            Visibility(
              visible: amount == null,
              replacement: _BuildTextWidget(
                amount: amount,
                currencySymbol: authUserCurrency,
              ),
              child: BlocBuilder<CheckoutBloc, CheckoutState>(
                builder: (context, state) {
                  return _BuildTextWidget(
                    amount: state.session?.amountToBeCredited,
                    currencySymbol: authUserCurrency,
                  );
                },
              ),
            ),
            Container(
              margin: const EdgeInsets.only(top: 20),
              padding: const EdgeInsets.symmetric(horizontal: 20),
              width: MediaQuery.sizeOf(context).width * 0.7,
              height: 48,
              child: ElevatedButton(
                onPressed: tryPaymentAgain,
                style: ElevatedButton.styleFrom(
                  side: BorderSide.none,
                  backgroundColor: FroggyColors.froggyGreen,
                ),
                child: Text(
                  l10n.paymentFailureTryAgainButton,
                  style: const TextStyle(
                    color: FroggyColors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
            Container(
              margin: const EdgeInsets.only(top: 20),
              padding: const EdgeInsets.symmetric(horizontal: 20),
              width: MediaQuery.sizeOf(context).width * 0.45,
              height: 48,
              child: OutlinedButton(
                onPressed: goToDashboard,
                // style: OutlinedButton.styleFrom(
                //   side: BorderSide.none,
                //   // backgroundColor: FroggyColors.froggyRed,
                // ),
                child: Text(
                  l10n.homeNavigationBarText,
                  style: const TextStyle(
                    color: FroggyColors.primary,
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
            const Spacer(
              flex: 2,
            ),
          ],
        ),
      ),
    );
  }
}

class _BuildTextWidget extends HookWidget {
  const _BuildTextWidget({
    this.currencySymbol = 'USD',
    this.amount = '100',
  });

  final String? amount;
  final String currencySymbol;

  @override
  Widget build(BuildContext context) {
    final l10n = useLocale();
    final amountMemz = useMemoized(
      () => amount?.toCurrency(
        context,
        symbol: currencySymbol,
      ),
    );

    // return Container(
    //   margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 5),
    //   child: Text.rich(
    //     TextSpan(
    //       children: [
    //         const TextSpan(
    //           text: 'Your payment of ',
    //           style: TextStyle(
    //             color: FroggyColors.black,
    //             fontWeight: FontWeight.w400,
    //           ),
    //         ),
    //         TextSpan(
    //           text: amountMemz ?? '',
    //           style: const TextStyle(
    //             color: FroggyColors.black,
    //             fontWeight: FontWeight.bold,
    //           ),
    //         ),
    //         const TextSpan(
    //           text: ' was unsuccessful',
    //           style: TextStyle(
    //             color: FroggyColors.black,
    //             fontWeight: FontWeight.w400,
    //           ),
    //         ),
    //       ],
    //     ),
    //     style: const TextStyle(
    //       fontSize: 12,
    //       color: FroggyColors.black,
    //       fontWeight: FontWeight.bold,
    //     ),
    //   ),
    // );

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 5),
      child: Text(
        l10n.paymentFailureMessage(amountMemz ?? ''),
        style: const TextStyle(
          fontSize: 12,
          color: FroggyColors.black,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }
}
