import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:formz/formz.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/app/data/services/event_tracker.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:froggytalk/buy_credit/buy_credit.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:money2/money2.dart';
import 'package:utils/utils.dart';

class BuyCreditPage extends HookWidget {
  const BuyCreditPage({super.key});

  static Route<Object?> route({required CheckoutBloc bloc}) {
    return MaterialPageRoute<Object?>(
      builder: (_) => MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) =>
                BuyCreditBloc()..add(const BuyCreditEvent.started()),
          ),
          BlocProvider.value(
            value: bloc..add(const CheckoutEvent.checkoutInitiated()),
          ),
        ],
        child: const BuyCreditPage(),
      ),
    );
  }

  static String routeName = '/buy_credit';

  @override
  Widget build(BuildContext context) {
    final currencyCode = useAuthUserCurrencyCode();
    final checkoutBloc = context.read<CheckoutBloc>();
    final l10n = useLocale();

    final isProductsLoading = context.select<BuyCreditBloc, bool>(
      (bloc) => bloc.state.status.isInProgress,
    );

    final productsCount = context.select<BuyCreditBloc, int>(
      (bloc) =>
          bloc.state.status.isSuccess ? bloc.state.buyCreditOptions.length : 0,
    );

    final isProductSelected = context.select<CheckoutBloc, bool>(
      (bloc) => bloc.state.buyCreditOption != null,
    );

    final onProceedPressed = useCallback(() {
      context.read<EventTrackerService>().logEvent(
            schema: 'Navigating to Checkout Page',
            description: 'User is navigating to checkout page',
          );

      Navigator.push(
        context,
        CheckoutPage.route(bloc: context.read<CheckoutBloc>()),
      );
    });

    final initCheckoutCurrencyCode = useCallback(({String? iCurrencyCode}) {
      checkoutBloc.add(
        CheckoutEvent.currencyCodeUpdated(
          value: iCurrencyCode ?? (currencyCode ?? 'BDT'),
          // value: 'EUR',
        ),
      );
    });

    useEffect(() {
      initCheckoutCurrencyCode();

      context.read<EventTrackerService>().logEvent(
            schema: 'Enter Buy Credit Page',
            description: 'User entered buy credit page',
          );

      BuyCreditEvents.buyCreditPageEntered();

      return null;
    });

    return Scaffold(
      appBar: FroggyAppBar(
        label: l10n.buyCreditAppBarTitle,
        labelFontSize: 20,
      ),
      body: Builder(
        builder: (context) {
          if (isProductsLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (productsCount <= 0) {
            return const Center(child: _BuildEmptyProductItems());
          }

          if (productsCount > 0) {
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 5),
                  Container(
                    margin: const EdgeInsets.only(
                      left: 10,
                      right: 10,
                    ),
                    child: Text(
                      l10n.buyCreditPageTitle,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(height: 10),
                  const Expanded(child: _BuildDynamicProductItems()),
                  // EnterCustomAmount(),
                  Container(
                    margin: const EdgeInsets.only(top: 30, bottom: 30),
                    width: double.infinity,
                    height: 48,
                    child: ElevatedButton(
                      onPressed: !isProductSelected ? null : onProceedPressed,
                      style: OutlinedButton.styleFrom(
                        side: BorderSide.none,
                      ),
                      child: Text(
                        l10n.proceedToPaymentButtonText,
                        style: const TextStyle(
                          color: FroggyColors.white,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          }

          return const SizedBox.shrink();
        },
      ),
    );
  }
}

class _BuildEmptyProductItems extends StatelessWidget {
  const _BuildEmptyProductItems();

  @override
  Widget build(BuildContext context) {
    final bloc = context.read<BuyCreditBloc>();
    final l10n = context.l10n;
    final hasErrorOptions =
        context.select((BuyCreditBloc bloc) => bloc.state.status.isCanceled);

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Spacer(),
        // Center(
        //   child: Container(
        //     margin: const EdgeInsets.symmetric(horizontal: 15),
        //     child: const Icon(
        //       Icons.error_outline,
        //       color: FroggyColors.froggyRed,
        //       size: 200,
        //     ),
        //   ),
        // ),
        Center(
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 25),
            child: Image.asset(
              'assets/logos/froggy-sad.png',
              width: 180,
              height: 180,
            ),
          ),
        ),
        // const Spacer(),
        Center(
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 15),
            child: Text(
              hasErrorOptions
                  ? l10n.internetConnectionAlertTextError
                  : l10n.noBuyCreditOptionsAvailable,
              style: const TextStyle(
                fontSize: 20,
                color: FroggyColors.black,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        BlocSelector<ConnectivityBloc, ConnectivityState, bool>(
          selector: (state) {
            return state.isConnected;
          },
          builder: (context, isConnected) {
            return Container(
              margin: const EdgeInsets.only(top: 30, bottom: 30),
              padding: const EdgeInsets.symmetric(horizontal: 20),
              width: double.infinity,
              height: 48,
              child: ElevatedButton(
                onPressed: !isConnected
                    ? null
                    : () {
                        bloc.add(const BuyCreditEvent.started());
                      },
                style: OutlinedButton.styleFrom(
                  side: BorderSide.none,
                ),
                child: const Text(
                  'Refresh',
                  style: TextStyle(
                    color: FroggyColors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
              ),
            );
          },
        ),
        const Spacer(
          flex: 3,
        ),
      ],
    );
  }
}

class _BuildDynamicProductItems extends StatefulHookWidget {
  const _BuildDynamicProductItems();

  @override
  State<_BuildDynamicProductItems> createState() =>
      _BuildDynamicProductItemsState();
}

class _BuildDynamicProductItemsState extends State<_BuildDynamicProductItems> {
  CheckoutBloc? bloc;
  BuyCreditBloc? buyCreditBloc;

  @override
  void initState() {
    super.initState();
    bloc = context.read<CheckoutBloc>();
    buyCreditBloc = context.read<BuyCreditBloc>();
    addRecommendedProductToCartOnFirstPageLoad();
  }

  void addRecommendedProductToCartOnFirstPageLoad() {
    final options = buyCreditBloc?.state.buyCreditOptions;
    if (options != null && options.isNotEmpty) {
      final recommendedProduct =
          options.firstWhere((product) => product.recommended);

      addToCart(recommendedProduct);
    }
  }

  void addToCart(BuyCreditResponse product) {
    bloc
      ?..add(
        CheckoutEvent.productAddedToCart(buyCreditOption: product),
      )
      ..add(const CheckoutEvent.checkoutRefreshed());
  }

  @override
  Widget build(BuildContext context) {
    final currencySymbol = useAuthUserCurrencyCode() ?? 'USD';

    final selectedProduct = context.select<CheckoutBloc, BuyCreditResponse?>(
      (bloc) => bloc.state.buyCreditOption,
    );

    return BlocBuilder<BuyCreditBloc, BuyCreditState>(
      builder: (context, state) {
        return ListView.builder(
          itemCount: state.buyCreditOptions.length,
          itemBuilder: (context, index) {
            final buyCreditOption = state.buyCreditOptions[index];
            // Show VAT-inclusive price for iOS if available
            var displayAmount =
                (buyCreditOption.amountToBeCredited?.amount ?? '0').toCurrency(
              context,
              symbol: currencySymbol,
            );
            var isIOS = false;
            // Platform check for iOS (robust for web/desktop)
            try {
              isIOS = Theme.of(context).platform == TargetPlatform.iOS;
            } catch (_) {}
            // Show VAT-inclusive price for iOS if available
            if (isIOS &&
                buyCreditOption.appStoreVatPrice != null &&
                buyCreditOption.appStoreVatPrice!.isNotEmpty) {
              displayAmount =
                  (buyCreditOption.amountToBeCredited?.amount ?? '0')
                      .toCurrency(
                context,
                symbol: currencySymbol,
              );
            }

            return _BuildProductItem(
              amount: displayAmount,
              isRecommended: buyCreditOption.recommended,
              isSelected: selectedProduct != null &&
                  selectedProduct.id == buyCreditOption.id,
              onPressed: () => addToCart(buyCreditOption),
              percentageAdded:
                  Fixed.parse(buyCreditOption.percentageAdded ?? '').toInt(),
            );
          },
        );
      },
    );
  }
}

class _BuildProductItem extends HookWidget {
  const _BuildProductItem({
    required this.amount,
    required this.onPressed,
    this.percentageAdded,
    this.isSelected = false,
    this.isRecommended = false,
  });

  final String amount;
  final bool isSelected;
  final bool isRecommended;
  final VoidCallback onPressed;
  final int? percentageAdded;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        // horizontal: 10,
        vertical: 3,
      ),
      width: MediaQuery.of(context).size.width * 0.9,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor:
              isSelected ? FroggyColors.froggyGreen : FroggyColors.froggyCream,
          fixedSize: const Size.fromHeight(50),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(50),
            side: const BorderSide(
              color: FroggyColors.black,
            ),
          ),
          alignment: Alignment.centerLeft,
        ),
        onPressed: onPressed,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              amount,
              textAlign: TextAlign.left,
              style: TextStyle(
                color: isSelected ? FroggyColors.white : FroggyColors.black,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            PriceDiscountBadge(
              fontSize: 10,
              size: 50,
              percentageAdded: percentageAdded,
              isInverted: isSelected,
              isRecommended: isRecommended,
            ),
          ],
        ),
      ),
    );
  }
}
