import 'package:constants/constants.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:froggytalk/l10n/hooks/use_locale.dart';
import 'package:utils/utils.dart';

class PaymentSheetService {
  // Method to initiate the payment process
  Future<void> makePayment({
    required String clientSecret,
    required BillingDetails billingDetails,
    required VoidCallback onPaymentSuccess,
    required void Function(StripeException e) onPaymentFailure,
    required void Function(StripeException e) onPaymentCancelled,
    required void Function(StripeException e) onPaymentTimedout,
    required void Function(Object e) onPaymentError,
  }) async {
    final l10n = useLocalizationsWithoutContext();

    try {
      // Create a Payment Method (card details will be collected via UI)
      await Stripe.instance.initPaymentSheet(
        paymentSheetParameters: SetupPaymentSheetParameters(
          paymentIntentClientSecret: clientSecret,
          merchantDisplayName: l10n.appTitle,
          primaryButtonLabel: l10n.buyCreditAppBarTitle,
          billingDetails: billingDetails,
          billingDetailsCollectionConfiguration:
              const BillingDetailsCollectionConfiguration(
            email: CollectionMode.always,
            name: CollectionMode.always,
            phone: CollectionMode.automatic,
            address: AddressCollectionMode.automatic,
          ),
          returnURL: 'froggtalk://redirect',
          appearance: const PaymentSheetAppearance(
            primaryButton: PaymentSheetPrimaryButtonAppearance(
              shapes: PaymentSheetPrimaryButtonShape(blurRadius: 8),
              colors: PaymentSheetPrimaryButtonTheme(
                light: PaymentSheetPrimaryButtonThemeColors(
                  background: FroggyColors.primary,
                  text: FroggyColors.white,
                  border: FroggyColors.froggyLightGreen,
                ),
              ),
            ),
            colors: PaymentSheetAppearanceColors(
              background: FroggyColors.froggyCream,
              primary: FroggyColors.primary,
              // componentBorder: Colors.red,
            ),
            shapes: PaymentSheetShape(
              borderRadius: 10,
              borderWidth: 2,
            ),
          ),
        ),
      );

      // Display the payment sheet
      await Stripe.instance.presentPaymentSheet();

      onPaymentSuccess.call(); // If payment is successful
    } on StripeException catch (e) {
      FroggyLogger.error('Payment Cancelled: $e');

      final error = e.error;
      final errorMessage = error.message ?? '';

      if (error.code == FailureCode.Canceled) {
        FroggyLogger.error('Payment Cancelled: $errorMessage');

        onPaymentCancelled.call(e);
      } else if (error.code == FailureCode.Failed) {
        FroggyLogger.error('Payment Failed: $errorMessage');

        onPaymentFailure.call(e);
      } else if (error.code == FailureCode.Timeout) {
        FroggyLogger.error('Payment Timedout: $errorMessage');

        onPaymentTimedout.call(e);
      }
    } catch (e, stackTrace) {
      // Catch any other exceptions and log them
      FroggyLogger.error('Unexpected error: $e');
      FroggyLogger.error('Stack trace: $stackTrace');

      onPaymentError.call(e);
    }
  }
}
