import 'package:customer_io/customer_io.dart';
import 'package:facebook_app_events/facebook_app_events.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:froggytalk/shared/services/purchase_attribution_service.dart';
// import 'package:onesignal_flutter/onesignal_flutter.dart';

class BuyCreditEvents {
  static void clickedOnBuyCreditButton() {
    const eventName = 'clicked_on_buy_credit_button';
    const eventDescription = 'User clicked on buy credit button';

    _firebaseAnalytics.logEvent(
      name: eventName,
      parameters: {
        'schema': eventName,
        'description': eventDescription,
      },
    );

    _facebookAppEvents.logEvent(
      name: eventName,
      parameters: {
        'schema': eventName,
        'description': eventDescription,
      },
    );

    // OneSignal.User.addTagWithKey('Buy_Credit_Phase', eventName);

    _customerIO.track(
      name: eventName,
      properties: {
        'schema': eventName,
        'description': eventDescription,
      },
    );
  }

  // on buy credit page
  static void buyCreditPageEntered() {
    const eventName = 'buy_credit_page_entered';
    const eventDescription = 'User entered buy credit page';

    _firebaseAnalytics.logEvent(
      name: eventName,
      parameters: {
        'schema': eventName,
        'description': eventDescription,
      },
    );

    _facebookAppEvents.logEvent(
      name: eventName,
      parameters: {
        'schema': eventName,
        'description': eventDescription,
      },
    );

    // OneSignal.User.addTagWithKey('Buy_Credit_Phase', eventName);
    // OneSignal.User.removeTag('Buy_Credit_Amount');
    // OneSignal.User.removeTag('Buy_Credit_Currency');

    _customerIO.track(
      name: eventName,
      properties: {
        'schema': eventName,
        'description': eventDescription,
      },
    );
  }

  // selected a buy credit option
  static void selectedBuyCreditOption({
    required String optionName,
    required String currency,
    required double value,
    required String sessionId,
    String? couponCode,
  }) {
    const eventName = 'selected_buy_credit_option';
    const eventDescription = 'User selected a buy credit option';

    _firebaseAnalytics
      ..logRemoveFromCart()
      ..logAddToCart(
        currency: currency,
        value: value,
        items: [
          AnalyticsEventItem(
            currency: currency,
            coupon: couponCode,
            itemName: optionName,
            quantity: 1,
            itemId: sessionId,
            price: value,
          ),
        ],
        parameters: {
          'schema': eventName,
          'description': eventDescription,
        },
      );

    _facebookAppEvents.logAddToCart(
      id: sessionId,
      price: value,
      currency: currency,
      type: 'buy_credit',
      content: {
        'schema': eventName,
        'description': eventDescription,
      },
    );

    // OneSignal.User.addTagWithKey('Buy_Credit_Phase', eventName);
    // OneSignal.User.addTagWithKey('Buy_Credit_Amount', value);
    // OneSignal.User.addTagWithKey('Buy_Credit_Currency', currency);

    _customerIO.track(
      name: eventName,
      properties: {
        'schema': eventName,
        'description': eventDescription,
        'name': optionName,
        'currency': currency,
        'value': value,
        'sessionId': sessionId,
        'couponCode': couponCode,
      },
    );
  }

  // on proceed to checkout button click
  static void clickedOnProceedToCheckoutButton({
    required String currency,
    required double value,
    required String sessionId,
    String? couponCode,
  }) {
    const eventName = 'clicked_on_proceed_to_checkout_button';
    const eventDescription = 'User clicked on proceed to checkout button';

    _firebaseAnalytics.logBeginCheckout(
      currency: currency,
      value: value,
      coupon: couponCode,
      items: [
        AnalyticsEventItem(
          currency: currency,
          coupon: couponCode,
          itemName: 'buy_credit',
          quantity: 1,
          itemId: sessionId,
          price: value,
        ),
      ],
      parameters: {
        'schema': eventName,
        'description': eventDescription,
      },
    );

    _facebookAppEvents.logInitiatedCheckout(
      totalPrice: value,
      currency: currency,
      numItems: 1,
    );

    // OneSignal.User.addTagWithKey('Buy_Credit_Phase', eventName);
    // OneSignal.User.addTagWithKey('Buy_Credit_Amount', value);
    // OneSignal.User.addTagWithKey('Buy_Credit_Currency', currency);

    _customerIO.track(
      name: eventName,
      properties: {
        'schema': eventName,
        'description': eventDescription,
        'currency': currency,
        'value': value,
        'sessionId': sessionId,
        'couponCode': couponCode,
      },
    );
  }

  // on proceed to payment button click
  static void clickedOnProceedToPaymentButton({
    required String currency,
    required double value,
    required String sessionId,
    String? couponCode,
  }) {
    const eventName = 'clicked_on_proceed_to_payment_button';
    const eventDescription = 'User clicked on proceed to payment button';

    _firebaseAnalytics.logEvent(
      name: eventName,
      parameters: {
        'schema': eventName,
        'description': eventDescription,
      },
    );

    _facebookAppEvents.logEvent(
      name: eventName,
      parameters: {
        'schema': eventName,
        'description': eventDescription,
      },
    );

    // OneSignal.User.addTagWithKey('Buy_Credit_Phase', eventName);
    // OneSignal.User.addTagWithKey('Buy_Credit_Amount', value);
    // OneSignal.User.addTagWithKey('Buy_Credit_Currency', currency);

    _customerIO.track(
      name: eventName,
      properties: {
        'schema': eventName,
        'description': eventDescription,
        'currency': currency,
        'value': value,
        'sessionId': sessionId,
        'couponCode': couponCode,
      },
    );
  }

  // on payment success
  static void onPaymentSuccess({
    required String currency,
    required double value,
    required String sessionId,
    String? couponCode,
  }) {
    const eventName = 'payment_successful';
    const eventDescription = 'User payment was successful';

    // Use PurchaseAttributionService to track purchases consistently
    // across all platforms. This ensures proper attribution with
    // tracking services
    PurchaseAttributionService().trackPurchase(
      eventName: eventName,
      productId: 'buy_credit',
      price: value,
      currency: currency,
      transactionId: sessionId,
      couponCode: couponCode,
      additionalParams: {
        'description': eventDescription,
        'schema': eventName,
      },
    );

    // Track with Customer.io separately since it's not in the attribution
    // service
    _customerIO.track(
      name: eventName,
      properties: {
        'schema': eventName,
        'description': eventDescription,
        'currency': currency,
        'value': value,
        'sessionId': sessionId,
        'couponCode': couponCode,
      },
    );

    // // Track with OneSignal for user segmentation
    // OneSignal.User.addTagWithKey('Buy_Credit_Phase', eventName);
    // OneSignal.User.addTagWithKey('Buy_Credit_Amount', value);

    // OneSignal.User.addTagWithKey('Buy_Credit_Currency', currency);
  }

  // on payment failure
  static void onPaymentFailure({
    required String currency,
    required double value,
    required String sessionId,
    String? couponCode,
  }) {
    const eventName = 'payment_failure';
    const eventDescription = 'User payment failed';

    _firebaseAnalytics.logEvent(
      name: eventName,
      parameters: {
        'schema': eventName,
        'description': eventDescription,
      },
    );

    _facebookAppEvents.logEvent(
      name: eventName,
      parameters: {
        'schema': eventName,
        'description': eventDescription,
      },
    );

    _customerIO.track(
      name: eventName,
      properties: {
        'schema': eventName,
        'description': eventDescription,
      },
    );

    // // one signal
    // OneSignal.User.addTagWithKey('Buy_Credit_Phase', eventName);
    // OneSignal.User.addTagWithKey('Buy_Credit_Amount', value);
    // OneSignal.User.addTagWithKey('Buy_Credit_Currency', currency);
  }

  // on payment cancelled
  static void onPaymentCancelled({
    required String currency,
    required double value,
    required String sessionId,
    String? couponCode,
  }) {
    const eventName = 'payment_cancelled';
    const eventDescription = 'User payment was cancelled';

    _firebaseAnalytics.logEvent(
      name: eventName,
      parameters: {
        'schema': eventName,
        'description': eventDescription,
      },
    );

    _facebookAppEvents.logEvent(
      name: eventName,
      parameters: {
        'schema': eventName,
        'description': eventDescription,
      },
    );

    // // one signal
    // OneSignal.User.addTagWithKey('Buy_Credit_Phase', eventName);
    // OneSignal.User.addTagWithKey('Buy_Credit_Amount', value);
    // OneSignal.User.addTagWithKey('Buy_Credit_Currency', currency);

    _customerIO.track(
      name: eventName,
      properties: {
        'schema': eventName,
        'description': eventDescription,
      },
    );
  }

  static FacebookAppEvents get _facebookAppEvents => FacebookAppEvents();

  static FirebaseAnalytics get _firebaseAnalytics => FirebaseAnalytics.instance;

  static CustomerIO get _customerIO => CustomerIO.instance;
}
