// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'buy_credit_iap.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BuyCreditInAppPurchaseImpl _$$BuyCreditInAppPurchaseImplFromJson(
        Map<String, dynamic> json) =>
    _$BuyCreditInAppPurchaseImpl(
      purchaseId: json['purchaseId'] as String,
      productId: json['productId'] as String,
      purchaseDate: DateTime.parse(json['purchaseDate'] as String),
      status:
          $enumDecode(_$BuyCreditInAppPurchaseStatusEnumMap, json['status']),
      errorMessage: json['errorMessage'] as String?,
      verificationData: json['verificationData'] as Map<String, dynamic>?,
      isSubscription: json['isSubscription'] as bool? ?? false,
    );

const _$$BuyCreditInAppPurchaseImplFieldMap = <String, String>{
  'purchaseId': 'purchaseId',
  'productId': 'productId',
  'purchaseDate': 'purchaseDate',
  'status': 'status',
  'errorMessage': 'errorMessage',
  'verificationData': 'verificationData',
  'isSubscription': 'isSubscription',
};

Map<String, dynamic> _$$BuyCreditInAppPurchaseImplToJson(
        _$BuyCreditInAppPurchaseImpl instance) =>
    <String, dynamic>{
      'purchaseId': instance.purchaseId,
      'productId': instance.productId,
      'purchaseDate': instance.purchaseDate.toIso8601String(),
      'status': _$BuyCreditInAppPurchaseStatusEnumMap[instance.status]!,
      if (instance.errorMessage case final value?) 'errorMessage': value,
      if (instance.verificationData case final value?)
        'verificationData': value,
      'isSubscription': instance.isSubscription,
    };

const _$BuyCreditInAppPurchaseStatusEnumMap = {
  BuyCreditInAppPurchaseStatus.pending: 'pending',
  BuyCreditInAppPurchaseStatus.completed: 'completed',
  BuyCreditInAppPurchaseStatus.failed: 'failed',
  BuyCreditInAppPurchaseStatus.verifying: 'verifying',
  BuyCreditInAppPurchaseStatus.verified: 'verified',
  BuyCreditInAppPurchaseStatus.consumed: 'consumed',
  BuyCreditInAppPurchaseStatus.restored: 'restored',
};

_$BuyCreditInAppProductImpl _$$BuyCreditInAppProductImplFromJson(
        Map<String, dynamic> json) =>
    _$BuyCreditInAppProductImpl(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      price: json['price'] as String,
      rawPrice: (json['rawPrice'] as num).toDouble(),
      currencyCode: json['currencyCode'] as String,
      credits: (json['credits'] as num).toInt(),
      isSubscription: json['isSubscription'] as bool? ?? false,
    );

const _$$BuyCreditInAppProductImplFieldMap = <String, String>{
  'id': 'id',
  'title': 'title',
  'description': 'description',
  'price': 'price',
  'rawPrice': 'rawPrice',
  'currencyCode': 'currencyCode',
  'credits': 'credits',
  'isSubscription': 'isSubscription',
};

Map<String, dynamic> _$$BuyCreditInAppProductImplToJson(
        _$BuyCreditInAppProductImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'price': instance.price,
      'rawPrice': instance.rawPrice,
      'currencyCode': instance.currencyCode,
      'credits': instance.credits,
      'isSubscription': instance.isSubscription,
    };

_$CreditResponseImpl _$$CreditResponseImplFromJson(Map<String, dynamic> json) =>
    _$CreditResponseImpl(
      success: json['success'] as bool,
      newBalance: json['newBalance'] as String,
      addedCredit: json['addedCredit'] as String,
      message: json['message'] as String?,
    );

const _$$CreditResponseImplFieldMap = <String, String>{
  'success': 'success',
  'newBalance': 'newBalance',
  'addedCredit': 'addedCredit',
  'message': 'message',
};

Map<String, dynamic> _$$CreditResponseImplToJson(
        _$CreditResponseImpl instance) =>
    <String, dynamic>{
      'success': instance.success,
      'newBalance': instance.newBalance,
      'addedCredit': instance.addedCredit,
      if (instance.message case final value?) 'message': value,
    };

_$VerificationResponseImpl _$$VerificationResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$VerificationResponseImpl(
      isValid: json['isValid'] as bool,
      message: json['message'] as String,
      errorCode: json['errorCode'] as String?,
    );

const _$$VerificationResponseImplFieldMap = <String, String>{
  'isValid': 'isValid',
  'message': 'message',
  'errorCode': 'errorCode',
};

Map<String, dynamic> _$$VerificationResponseImplToJson(
        _$VerificationResponseImpl instance) =>
    <String, dynamic>{
      'isValid': instance.isValid,
      'message': instance.message,
      if (instance.errorCode case final value?) 'errorCode': value,
    };
