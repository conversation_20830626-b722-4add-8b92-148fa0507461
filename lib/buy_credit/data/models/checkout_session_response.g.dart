// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'checkout_session_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CheckoutSessionResponseImpl _$$CheckoutSessionResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$CheckoutSessionResponseImpl(
      id: json['id'] as String?,
      amountToBeCredited: json['amount_to_be_credited'] as String?,
      vatPlusFees: json['vat_plus_fees'] as String?,
      discount: json['discount'] as String?,
      totalPayment: json['total_payment'] as String?,
    );

const _$$CheckoutSessionResponseImplFieldMap = <String, String>{
  'id': 'id',
  'amountToBeCredited': 'amount_to_be_credited',
  'vatPlusFees': 'vat_plus_fees',
  'discount': 'discount',
  'totalPayment': 'total_payment',
};

Map<String, dynamic> _$$CheckoutSessionResponseImplToJson(
        _$CheckoutSessionResponseImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.amountToBeCredited case final value?)
        'amount_to_be_credited': value,
      if (instance.vatPlusFees case final value?) 'vat_plus_fees': value,
      if (instance.discount case final value?) 'discount': value,
      if (instance.totalPayment case final value?) 'total_payment': value,
    };
