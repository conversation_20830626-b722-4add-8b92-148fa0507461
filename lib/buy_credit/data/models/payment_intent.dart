import 'package:freezed_annotation/freezed_annotation.dart';

part 'payment_intent.freezed.dart';
part 'payment_intent.g.dart';

@freezed
class CheckoutPaymentIntent with _$CheckoutPaymentIntent {
  factory CheckoutPaymentIntent({
    dynamic id,
    @JsonKey(name: 'client_secret') String? clientSecret,
    // String? paymentMethodId,
    // String? paymentMethodNonce,
    dynamic amount,
    String? currency,
    // String? paymentIntentId,
    // String? status,
  }) = _CheckoutPaymentIntent;

  factory CheckoutPaymentIntent.fromJson(Map<String, Object?> json) =>
      _$CheckoutPaymentIntentFromJson(json);
}
