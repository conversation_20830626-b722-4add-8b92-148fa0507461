import 'package:flutter/material.dart';
import 'package:formz/formz.dart';
import 'package:froggytalk/l10n/l10n.dart';

// Define input validation errors
enum CouponCodeInputError { invalid, invalidLength }

extension CouponCodeInputErrorX on CouponCodeInputError {
  bool get isInvalid => this == CouponCodeInputError.invalid;
  bool get isInvalidLength => this == CouponCodeInputError.invalidLength;

  // get the localized error messages
  String? getLocalizedErrorMessage(BuildContext context) {
    switch (this) {
      case CouponCodeInputError.invalid:
        return context.l10n.validationCouponInvalid;
      case CouponCodeInputError.invalidLength:
        return context.l10n.validationCouponCodeIncomplete;
    }
  }
}

// Extend FormzInput and provide the input type and error type.
class CouponCodeInput extends FormzInput<String, CouponCodeInputError> {
  // Call super.pure to represent an unmodified form input.
  const CouponCodeInput.pure() : super.pure('');

  // Call super.dirty to represent a modified form input.
  const CouponCodeInput.dirty(super.value) : super.dirty();

  // Override validator to handle validating a given input value.
  @override
  CouponCodeInputError? validator(String? value) {
    final length = (value ?? '').length;
    final regex = RegExp('[a-zA-Z0-9]');

    if ((length >= 1 && length < 5) || length > 10) {
      return CouponCodeInputError.invalidLength;
    }

    if (!regex.hasMatch(value ?? '') && length >= 5) {
      return CouponCodeInputError.invalid;
    }

    return null;
  }

  // get the error message
  String? get errorMessage {
    if (error == CouponCodeInputError.invalid) {
      return 'Please enter a valid coupon code';
    } else if (error == CouponCodeInputError.invalidLength) {
      return 'Coupon code is incomplete or invalid';
    } else {
      return null;
    }
  }
}
