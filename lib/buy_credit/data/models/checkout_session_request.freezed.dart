// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'checkout_session_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CheckoutSessionRequest _$CheckoutSessionRequestFromJson(
    Map<String, dynamic> json) {
  return _CheckoutSessionRequest.fromJson(json);
}

/// @nodoc
mixin _$CheckoutSessionRequest {
  String get amount => throw _privateConstructorUsedError;
  @JsonKey(name: 'payment_method')
  String get paymentMethod => throw _privateConstructorUsedError;
  @JsonKey(name: 'e_payment_log_id')
  String? get sessionId => throw _privateConstructorUsedError;
  @JsonKey(name: 'coupon_code')
  String? get couponCode => throw _privateConstructorUsedError;
  @JsonKey(name: 'auto_recharge')
  bool get enableAutoRecharge => throw _privateConstructorUsedError;
  @JsonKey(name: 'currency')
  String get currencyCode => throw _privateConstructorUsedError;

  /// Serializes this CheckoutSessionRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CheckoutSessionRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CheckoutSessionRequestCopyWith<CheckoutSessionRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CheckoutSessionRequestCopyWith<$Res> {
  factory $CheckoutSessionRequestCopyWith(CheckoutSessionRequest value,
          $Res Function(CheckoutSessionRequest) then) =
      _$CheckoutSessionRequestCopyWithImpl<$Res, CheckoutSessionRequest>;
  @useResult
  $Res call(
      {String amount,
      @JsonKey(name: 'payment_method') String paymentMethod,
      @JsonKey(name: 'e_payment_log_id') String? sessionId,
      @JsonKey(name: 'coupon_code') String? couponCode,
      @JsonKey(name: 'auto_recharge') bool enableAutoRecharge,
      @JsonKey(name: 'currency') String currencyCode});
}

/// @nodoc
class _$CheckoutSessionRequestCopyWithImpl<$Res,
        $Val extends CheckoutSessionRequest>
    implements $CheckoutSessionRequestCopyWith<$Res> {
  _$CheckoutSessionRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CheckoutSessionRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = null,
    Object? paymentMethod = null,
    Object? sessionId = freezed,
    Object? couponCode = freezed,
    Object? enableAutoRecharge = null,
    Object? currencyCode = null,
  }) {
    return _then(_value.copyWith(
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as String,
      paymentMethod: null == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as String,
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      couponCode: freezed == couponCode
          ? _value.couponCode
          : couponCode // ignore: cast_nullable_to_non_nullable
              as String?,
      enableAutoRecharge: null == enableAutoRecharge
          ? _value.enableAutoRecharge
          : enableAutoRecharge // ignore: cast_nullable_to_non_nullable
              as bool,
      currencyCode: null == currencyCode
          ? _value.currencyCode
          : currencyCode // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CheckoutSessionRequestImplCopyWith<$Res>
    implements $CheckoutSessionRequestCopyWith<$Res> {
  factory _$$CheckoutSessionRequestImplCopyWith(
          _$CheckoutSessionRequestImpl value,
          $Res Function(_$CheckoutSessionRequestImpl) then) =
      __$$CheckoutSessionRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String amount,
      @JsonKey(name: 'payment_method') String paymentMethod,
      @JsonKey(name: 'e_payment_log_id') String? sessionId,
      @JsonKey(name: 'coupon_code') String? couponCode,
      @JsonKey(name: 'auto_recharge') bool enableAutoRecharge,
      @JsonKey(name: 'currency') String currencyCode});
}

/// @nodoc
class __$$CheckoutSessionRequestImplCopyWithImpl<$Res>
    extends _$CheckoutSessionRequestCopyWithImpl<$Res,
        _$CheckoutSessionRequestImpl>
    implements _$$CheckoutSessionRequestImplCopyWith<$Res> {
  __$$CheckoutSessionRequestImplCopyWithImpl(
      _$CheckoutSessionRequestImpl _value,
      $Res Function(_$CheckoutSessionRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of CheckoutSessionRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = null,
    Object? paymentMethod = null,
    Object? sessionId = freezed,
    Object? couponCode = freezed,
    Object? enableAutoRecharge = null,
    Object? currencyCode = null,
  }) {
    return _then(_$CheckoutSessionRequestImpl(
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as String,
      paymentMethod: null == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as String,
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      couponCode: freezed == couponCode
          ? _value.couponCode
          : couponCode // ignore: cast_nullable_to_non_nullable
              as String?,
      enableAutoRecharge: null == enableAutoRecharge
          ? _value.enableAutoRecharge
          : enableAutoRecharge // ignore: cast_nullable_to_non_nullable
              as bool,
      currencyCode: null == currencyCode
          ? _value.currencyCode
          : currencyCode // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CheckoutSessionRequestImpl implements _CheckoutSessionRequest {
  _$CheckoutSessionRequestImpl(
      {required this.amount,
      @JsonKey(name: 'payment_method') required this.paymentMethod,
      @JsonKey(name: 'e_payment_log_id') this.sessionId,
      @JsonKey(name: 'coupon_code') this.couponCode,
      @JsonKey(name: 'auto_recharge') this.enableAutoRecharge = true,
      @JsonKey(name: 'currency') this.currencyCode = 'BDT'});

  factory _$CheckoutSessionRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$CheckoutSessionRequestImplFromJson(json);

  @override
  final String amount;
  @override
  @JsonKey(name: 'payment_method')
  final String paymentMethod;
  @override
  @JsonKey(name: 'e_payment_log_id')
  final String? sessionId;
  @override
  @JsonKey(name: 'coupon_code')
  final String? couponCode;
  @override
  @JsonKey(name: 'auto_recharge')
  final bool enableAutoRecharge;
  @override
  @JsonKey(name: 'currency')
  final String currencyCode;

  @override
  String toString() {
    return 'CheckoutSessionRequest(amount: $amount, paymentMethod: $paymentMethod, sessionId: $sessionId, couponCode: $couponCode, enableAutoRecharge: $enableAutoRecharge, currencyCode: $currencyCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CheckoutSessionRequestImpl &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.paymentMethod, paymentMethod) ||
                other.paymentMethod == paymentMethod) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.couponCode, couponCode) ||
                other.couponCode == couponCode) &&
            (identical(other.enableAutoRecharge, enableAutoRecharge) ||
                other.enableAutoRecharge == enableAutoRecharge) &&
            (identical(other.currencyCode, currencyCode) ||
                other.currencyCode == currencyCode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, amount, paymentMethod, sessionId,
      couponCode, enableAutoRecharge, currencyCode);

  /// Create a copy of CheckoutSessionRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CheckoutSessionRequestImplCopyWith<_$CheckoutSessionRequestImpl>
      get copyWith => __$$CheckoutSessionRequestImplCopyWithImpl<
          _$CheckoutSessionRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CheckoutSessionRequestImplToJson(
      this,
    );
  }
}

abstract class _CheckoutSessionRequest implements CheckoutSessionRequest {
  factory _CheckoutSessionRequest(
          {required final String amount,
          @JsonKey(name: 'payment_method') required final String paymentMethod,
          @JsonKey(name: 'e_payment_log_id') final String? sessionId,
          @JsonKey(name: 'coupon_code') final String? couponCode,
          @JsonKey(name: 'auto_recharge') final bool enableAutoRecharge,
          @JsonKey(name: 'currency') final String currencyCode}) =
      _$CheckoutSessionRequestImpl;

  factory _CheckoutSessionRequest.fromJson(Map<String, dynamic> json) =
      _$CheckoutSessionRequestImpl.fromJson;

  @override
  String get amount;
  @override
  @JsonKey(name: 'payment_method')
  String get paymentMethod;
  @override
  @JsonKey(name: 'e_payment_log_id')
  String? get sessionId;
  @override
  @JsonKey(name: 'coupon_code')
  String? get couponCode;
  @override
  @JsonKey(name: 'auto_recharge')
  bool get enableAutoRecharge;
  @override
  @JsonKey(name: 'currency')
  String get currencyCode;

  /// Create a copy of CheckoutSessionRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CheckoutSessionRequestImplCopyWith<_$CheckoutSessionRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
