enum CouponCodeInputStatus {
  initial,
  applied,
  reset,
  expired,
  readonly,
}

// write extensions for the enum
extension CouponCodeInputStatusExtension on CouponCodeInputStatus {
  String get statusText {
    switch (this) {
      case CouponCodeInputStatus.initial:
        return 'Enter your coupon code';
      case CouponCodeInputStatus.applied:
        return 'Coupon code applied';
      case CouponCodeInputStatus.reset:
        return 'Coupon code reset';
      case CouponCodeInputStatus.expired:
        return 'Coupon code has expired';
      case CouponCodeInputStatus.readonly:
        return 'Coupon code is readonly';
    }
  }

  bool get isApplied {
    return this == CouponCodeInputStatus.applied;
  }

  bool get isReset {
    return this == CouponCodeInputStatus.reset;
  }

  bool get isInitial {
    return this == CouponCodeInputStatus.initial;
  }

  bool get isExpired {
    return this == CouponCodeInputStatus.expired;
  }

  bool get isReadonly {
    return this == CouponCodeInputStatus.readonly;
  }
}
