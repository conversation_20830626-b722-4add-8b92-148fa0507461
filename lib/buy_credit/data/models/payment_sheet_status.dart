enum PaymentSheetStatus {
  pending,
  approved,
  failure,
  initial,
  cancelled,
  timedout,
}

extension PaymentSheetStatusExtension on PaymentSheetStatus {
  String get value {
    switch (this) {
      case PaymentSheetStatus.pending:
        return 'pending';
      case PaymentSheetStatus.approved:
        return 'approved';
      case PaymentSheetStatus.failure:
        return 'rejected';
      case PaymentSheetStatus.initial:
        return 'initial';
      case PaymentSheetStatus.cancelled:
        return 'cancelled';
      case PaymentSheetStatus.timedout:
        return 'timedout';
    }
  }

  bool get isInitial => this == PaymentSheetStatus.initial;
  bool get isPending => this == PaymentSheetStatus.pending;
  bool get isSuccessful => this == PaymentSheetStatus.approved;
  bool get isFailure => this == PaymentSheetStatus.failure;
  bool get isCancelled => this == PaymentSheetStatus.cancelled;
  bool get isTimedout => this == PaymentSheetStatus.timedout;
}
