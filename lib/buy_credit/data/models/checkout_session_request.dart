import 'package:freezed_annotation/freezed_annotation.dart';

part 'checkout_session_request.freezed.dart';
part 'checkout_session_request.g.dart';

@freezed
class CheckoutSessionRequest with _$CheckoutSessionRequest {
  factory CheckoutSessionRequest({
    required String amount,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'payment_method') required String paymentMethod,
    @Json<PERSON>ey(name: 'e_payment_log_id') String? sessionId,
    @JsonKey(name: 'coupon_code') String? couponCode,
    @Default(true) @<PERSON>sonKey(name: 'auto_recharge') bool enableAutoRecharge,
    @JsonKey(name: 'currency') @Default('BDT') String currencyCode,
  }) = _CheckoutSessionRequest;

  factory CheckoutSessionRequest.fromJson(Map<String, Object?> json) =>
      _$CheckoutSessionRequestFromJson(json);
}
