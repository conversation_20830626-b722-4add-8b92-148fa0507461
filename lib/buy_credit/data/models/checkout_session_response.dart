import 'package:freezed_annotation/freezed_annotation.dart';

part 'checkout_session_response.freezed.dart';
part 'checkout_session_response.g.dart';

@freezed
class CheckoutSessionResponse with _$CheckoutSessionResponse {
  factory CheckoutSessionResponse({
    String? id,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'amount_to_be_credited') String? amountToBeCredited,
    @<PERSON><PERSON><PERSON>ey(name: 'vat_plus_fees') String? vatPlusFees,
    String? discount,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'total_payment') String? totalPayment,
  }) = _CheckoutSessionResponse;

  factory CheckoutSessionResponse.fromJson(Map<String, Object?> json) =>
      _$CheckoutSessionResponseFromJson(json);
}
