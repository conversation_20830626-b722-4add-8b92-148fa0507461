// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'discounted_checkout_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

DiscountedCheckoutResponse _$DiscountedCheckoutResponseFromJson(
    Map<String, dynamic> json) {
  return _DiscountedCheckoutResponse.fromJson(json);
}

/// @nodoc
mixin _$DiscountedCheckoutResponse {
  double get amount => throw _privateConstructorUsedError;
  String get currency => throw _privateConstructorUsedError;
  bool get enabled => throw _privateConstructorUsedError;
  @JsonKey(name: 'coupon_code')
  String? get couponCode => throw _privateConstructorUsedError;

  /// Serializes this DiscountedCheckoutResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DiscountedCheckoutResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DiscountedCheckoutResponseCopyWith<DiscountedCheckoutResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DiscountedCheckoutResponseCopyWith<$Res> {
  factory $DiscountedCheckoutResponseCopyWith(DiscountedCheckoutResponse value,
          $Res Function(DiscountedCheckoutResponse) then) =
      _$DiscountedCheckoutResponseCopyWithImpl<$Res,
          DiscountedCheckoutResponse>;
  @useResult
  $Res call(
      {double amount,
      String currency,
      bool enabled,
      @JsonKey(name: 'coupon_code') String? couponCode});
}

/// @nodoc
class _$DiscountedCheckoutResponseCopyWithImpl<$Res,
        $Val extends DiscountedCheckoutResponse>
    implements $DiscountedCheckoutResponseCopyWith<$Res> {
  _$DiscountedCheckoutResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DiscountedCheckoutResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = null,
    Object? currency = null,
    Object? enabled = null,
    Object? couponCode = freezed,
  }) {
    return _then(_value.copyWith(
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      enabled: null == enabled
          ? _value.enabled
          : enabled // ignore: cast_nullable_to_non_nullable
              as bool,
      couponCode: freezed == couponCode
          ? _value.couponCode
          : couponCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DiscountedCheckoutResponseImplCopyWith<$Res>
    implements $DiscountedCheckoutResponseCopyWith<$Res> {
  factory _$$DiscountedCheckoutResponseImplCopyWith(
          _$DiscountedCheckoutResponseImpl value,
          $Res Function(_$DiscountedCheckoutResponseImpl) then) =
      __$$DiscountedCheckoutResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double amount,
      String currency,
      bool enabled,
      @JsonKey(name: 'coupon_code') String? couponCode});
}

/// @nodoc
class __$$DiscountedCheckoutResponseImplCopyWithImpl<$Res>
    extends _$DiscountedCheckoutResponseCopyWithImpl<$Res,
        _$DiscountedCheckoutResponseImpl>
    implements _$$DiscountedCheckoutResponseImplCopyWith<$Res> {
  __$$DiscountedCheckoutResponseImplCopyWithImpl(
      _$DiscountedCheckoutResponseImpl _value,
      $Res Function(_$DiscountedCheckoutResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of DiscountedCheckoutResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = null,
    Object? currency = null,
    Object? enabled = null,
    Object? couponCode = freezed,
  }) {
    return _then(_$DiscountedCheckoutResponseImpl(
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
      enabled: null == enabled
          ? _value.enabled
          : enabled // ignore: cast_nullable_to_non_nullable
              as bool,
      couponCode: freezed == couponCode
          ? _value.couponCode
          : couponCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DiscountedCheckoutResponseImpl implements _DiscountedCheckoutResponse {
  const _$DiscountedCheckoutResponseImpl(
      {required this.amount,
      required this.currency,
      required this.enabled,
      @JsonKey(name: 'coupon_code') this.couponCode});

  factory _$DiscountedCheckoutResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$DiscountedCheckoutResponseImplFromJson(json);

  @override
  final double amount;
  @override
  final String currency;
  @override
  final bool enabled;
  @override
  @JsonKey(name: 'coupon_code')
  final String? couponCode;

  @override
  String toString() {
    return 'DiscountedCheckoutResponse(amount: $amount, currency: $currency, enabled: $enabled, couponCode: $couponCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DiscountedCheckoutResponseImpl &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.enabled, enabled) || other.enabled == enabled) &&
            (identical(other.couponCode, couponCode) ||
                other.couponCode == couponCode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, amount, currency, enabled, couponCode);

  /// Create a copy of DiscountedCheckoutResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DiscountedCheckoutResponseImplCopyWith<_$DiscountedCheckoutResponseImpl>
      get copyWith => __$$DiscountedCheckoutResponseImplCopyWithImpl<
          _$DiscountedCheckoutResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DiscountedCheckoutResponseImplToJson(
      this,
    );
  }
}

abstract class _DiscountedCheckoutResponse
    implements DiscountedCheckoutResponse {
  const factory _DiscountedCheckoutResponse(
          {required final double amount,
          required final String currency,
          required final bool enabled,
          @JsonKey(name: 'coupon_code') final String? couponCode}) =
      _$DiscountedCheckoutResponseImpl;

  factory _DiscountedCheckoutResponse.fromJson(Map<String, dynamic> json) =
      _$DiscountedCheckoutResponseImpl.fromJson;

  @override
  double get amount;
  @override
  String get currency;
  @override
  bool get enabled;
  @override
  @JsonKey(name: 'coupon_code')
  String? get couponCode;

  /// Create a copy of DiscountedCheckoutResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DiscountedCheckoutResponseImplCopyWith<_$DiscountedCheckoutResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
