// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'buy_credit_iap.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

BuyCreditInAppPurchase _$BuyCreditInAppPurchaseFromJson(
    Map<String, dynamic> json) {
  return _BuyCreditInAppPurchase.fromJson(json);
}

/// @nodoc
mixin _$BuyCreditInAppPurchase {
  /// Unique purchase identifier
  String get purchaseId => throw _privateConstructorUsedError;

  /// ID of the purchased product
  String get productId => throw _privateConstructorUsedError;

  /// Transaction date
  DateTime get purchaseDate => throw _privateConstructorUsedError;

  /// Current status of the purchase
  BuyCreditInAppPurchaseStatus get status => throw _privateConstructorUsedError;

  /// Error message if purchase failed
  String? get errorMessage => throw _privateConstructorUsedError;

  /// Transaction verification data
  Map<String, dynamic>? get verificationData =>
      throw _privateConstructorUsedError;

  /// Whether this purchase is a subscription
  bool get isSubscription => throw _privateConstructorUsedError;

  /// Serializes this BuyCreditInAppPurchase to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BuyCreditInAppPurchase
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BuyCreditInAppPurchaseCopyWith<BuyCreditInAppPurchase> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BuyCreditInAppPurchaseCopyWith<$Res> {
  factory $BuyCreditInAppPurchaseCopyWith(BuyCreditInAppPurchase value,
          $Res Function(BuyCreditInAppPurchase) then) =
      _$BuyCreditInAppPurchaseCopyWithImpl<$Res, BuyCreditInAppPurchase>;
  @useResult
  $Res call(
      {String purchaseId,
      String productId,
      DateTime purchaseDate,
      BuyCreditInAppPurchaseStatus status,
      String? errorMessage,
      Map<String, dynamic>? verificationData,
      bool isSubscription});
}

/// @nodoc
class _$BuyCreditInAppPurchaseCopyWithImpl<$Res,
        $Val extends BuyCreditInAppPurchase>
    implements $BuyCreditInAppPurchaseCopyWith<$Res> {
  _$BuyCreditInAppPurchaseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BuyCreditInAppPurchase
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? purchaseId = null,
    Object? productId = null,
    Object? purchaseDate = null,
    Object? status = null,
    Object? errorMessage = freezed,
    Object? verificationData = freezed,
    Object? isSubscription = null,
  }) {
    return _then(_value.copyWith(
      purchaseId: null == purchaseId
          ? _value.purchaseId
          : purchaseId // ignore: cast_nullable_to_non_nullable
              as String,
      productId: null == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String,
      purchaseDate: null == purchaseDate
          ? _value.purchaseDate
          : purchaseDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as BuyCreditInAppPurchaseStatus,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      verificationData: freezed == verificationData
          ? _value.verificationData
          : verificationData // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      isSubscription: null == isSubscription
          ? _value.isSubscription
          : isSubscription // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BuyCreditInAppPurchaseImplCopyWith<$Res>
    implements $BuyCreditInAppPurchaseCopyWith<$Res> {
  factory _$$BuyCreditInAppPurchaseImplCopyWith(
          _$BuyCreditInAppPurchaseImpl value,
          $Res Function(_$BuyCreditInAppPurchaseImpl) then) =
      __$$BuyCreditInAppPurchaseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String purchaseId,
      String productId,
      DateTime purchaseDate,
      BuyCreditInAppPurchaseStatus status,
      String? errorMessage,
      Map<String, dynamic>? verificationData,
      bool isSubscription});
}

/// @nodoc
class __$$BuyCreditInAppPurchaseImplCopyWithImpl<$Res>
    extends _$BuyCreditInAppPurchaseCopyWithImpl<$Res,
        _$BuyCreditInAppPurchaseImpl>
    implements _$$BuyCreditInAppPurchaseImplCopyWith<$Res> {
  __$$BuyCreditInAppPurchaseImplCopyWithImpl(
      _$BuyCreditInAppPurchaseImpl _value,
      $Res Function(_$BuyCreditInAppPurchaseImpl) _then)
      : super(_value, _then);

  /// Create a copy of BuyCreditInAppPurchase
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? purchaseId = null,
    Object? productId = null,
    Object? purchaseDate = null,
    Object? status = null,
    Object? errorMessage = freezed,
    Object? verificationData = freezed,
    Object? isSubscription = null,
  }) {
    return _then(_$BuyCreditInAppPurchaseImpl(
      purchaseId: null == purchaseId
          ? _value.purchaseId
          : purchaseId // ignore: cast_nullable_to_non_nullable
              as String,
      productId: null == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String,
      purchaseDate: null == purchaseDate
          ? _value.purchaseDate
          : purchaseDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as BuyCreditInAppPurchaseStatus,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      verificationData: freezed == verificationData
          ? _value._verificationData
          : verificationData // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      isSubscription: null == isSubscription
          ? _value.isSubscription
          : isSubscription // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BuyCreditInAppPurchaseImpl implements _BuyCreditInAppPurchase {
  const _$BuyCreditInAppPurchaseImpl(
      {required this.purchaseId,
      required this.productId,
      required this.purchaseDate,
      required this.status,
      this.errorMessage,
      final Map<String, dynamic>? verificationData,
      this.isSubscription = false})
      : _verificationData = verificationData;

  factory _$BuyCreditInAppPurchaseImpl.fromJson(Map<String, dynamic> json) =>
      _$$BuyCreditInAppPurchaseImplFromJson(json);

  /// Unique purchase identifier
  @override
  final String purchaseId;

  /// ID of the purchased product
  @override
  final String productId;

  /// Transaction date
  @override
  final DateTime purchaseDate;

  /// Current status of the purchase
  @override
  final BuyCreditInAppPurchaseStatus status;

  /// Error message if purchase failed
  @override
  final String? errorMessage;

  /// Transaction verification data
  final Map<String, dynamic>? _verificationData;

  /// Transaction verification data
  @override
  Map<String, dynamic>? get verificationData {
    final value = _verificationData;
    if (value == null) return null;
    if (_verificationData is EqualUnmodifiableMapView) return _verificationData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  /// Whether this purchase is a subscription
  @override
  @JsonKey()
  final bool isSubscription;

  @override
  String toString() {
    return 'BuyCreditInAppPurchase(purchaseId: $purchaseId, productId: $productId, purchaseDate: $purchaseDate, status: $status, errorMessage: $errorMessage, verificationData: $verificationData, isSubscription: $isSubscription)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BuyCreditInAppPurchaseImpl &&
            (identical(other.purchaseId, purchaseId) ||
                other.purchaseId == purchaseId) &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.purchaseDate, purchaseDate) ||
                other.purchaseDate == purchaseDate) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            const DeepCollectionEquality()
                .equals(other._verificationData, _verificationData) &&
            (identical(other.isSubscription, isSubscription) ||
                other.isSubscription == isSubscription));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      purchaseId,
      productId,
      purchaseDate,
      status,
      errorMessage,
      const DeepCollectionEquality().hash(_verificationData),
      isSubscription);

  /// Create a copy of BuyCreditInAppPurchase
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BuyCreditInAppPurchaseImplCopyWith<_$BuyCreditInAppPurchaseImpl>
      get copyWith => __$$BuyCreditInAppPurchaseImplCopyWithImpl<
          _$BuyCreditInAppPurchaseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BuyCreditInAppPurchaseImplToJson(
      this,
    );
  }
}

abstract class _BuyCreditInAppPurchase implements BuyCreditInAppPurchase {
  const factory _BuyCreditInAppPurchase(
      {required final String purchaseId,
      required final String productId,
      required final DateTime purchaseDate,
      required final BuyCreditInAppPurchaseStatus status,
      final String? errorMessage,
      final Map<String, dynamic>? verificationData,
      final bool isSubscription}) = _$BuyCreditInAppPurchaseImpl;

  factory _BuyCreditInAppPurchase.fromJson(Map<String, dynamic> json) =
      _$BuyCreditInAppPurchaseImpl.fromJson;

  /// Unique purchase identifier
  @override
  String get purchaseId;

  /// ID of the purchased product
  @override
  String get productId;

  /// Transaction date
  @override
  DateTime get purchaseDate;

  /// Current status of the purchase
  @override
  BuyCreditInAppPurchaseStatus get status;

  /// Error message if purchase failed
  @override
  String? get errorMessage;

  /// Transaction verification data
  @override
  Map<String, dynamic>? get verificationData;

  /// Whether this purchase is a subscription
  @override
  bool get isSubscription;

  /// Create a copy of BuyCreditInAppPurchase
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BuyCreditInAppPurchaseImplCopyWith<_$BuyCreditInAppPurchaseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

BuyCreditInAppProduct _$BuyCreditInAppProductFromJson(
    Map<String, dynamic> json) {
  return _BuyCreditInAppProduct.fromJson(json);
}

/// @nodoc
mixin _$BuyCreditInAppProduct {
  /// Unique identifier for the product
  String get id => throw _privateConstructorUsedError;

  /// Product title displayed to users
  String get title => throw _privateConstructorUsedError;

  /// Product description
  String get description => throw _privateConstructorUsedError;

  /// Formatted price (e.g., "$9.99")
  String get price => throw _privateConstructorUsedError;

  /// Raw price value
  double get rawPrice => throw _privateConstructorUsedError;

  /// Currency code (e.g., "USD")
  String get currencyCode => throw _privateConstructorUsedError;

  /// Amount of credits this product provides
  int get credits => throw _privateConstructorUsedError;

  /// Whether this product is a subscription
  bool get isSubscription => throw _privateConstructorUsedError;

  /// Serializes this BuyCreditInAppProduct to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BuyCreditInAppProduct
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BuyCreditInAppProductCopyWith<BuyCreditInAppProduct> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BuyCreditInAppProductCopyWith<$Res> {
  factory $BuyCreditInAppProductCopyWith(BuyCreditInAppProduct value,
          $Res Function(BuyCreditInAppProduct) then) =
      _$BuyCreditInAppProductCopyWithImpl<$Res, BuyCreditInAppProduct>;
  @useResult
  $Res call(
      {String id,
      String title,
      String description,
      String price,
      double rawPrice,
      String currencyCode,
      int credits,
      bool isSubscription});
}

/// @nodoc
class _$BuyCreditInAppProductCopyWithImpl<$Res,
        $Val extends BuyCreditInAppProduct>
    implements $BuyCreditInAppProductCopyWith<$Res> {
  _$BuyCreditInAppProductCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BuyCreditInAppProduct
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? price = null,
    Object? rawPrice = null,
    Object? currencyCode = null,
    Object? credits = null,
    Object? isSubscription = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as String,
      rawPrice: null == rawPrice
          ? _value.rawPrice
          : rawPrice // ignore: cast_nullable_to_non_nullable
              as double,
      currencyCode: null == currencyCode
          ? _value.currencyCode
          : currencyCode // ignore: cast_nullable_to_non_nullable
              as String,
      credits: null == credits
          ? _value.credits
          : credits // ignore: cast_nullable_to_non_nullable
              as int,
      isSubscription: null == isSubscription
          ? _value.isSubscription
          : isSubscription // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BuyCreditInAppProductImplCopyWith<$Res>
    implements $BuyCreditInAppProductCopyWith<$Res> {
  factory _$$BuyCreditInAppProductImplCopyWith(
          _$BuyCreditInAppProductImpl value,
          $Res Function(_$BuyCreditInAppProductImpl) then) =
      __$$BuyCreditInAppProductImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String title,
      String description,
      String price,
      double rawPrice,
      String currencyCode,
      int credits,
      bool isSubscription});
}

/// @nodoc
class __$$BuyCreditInAppProductImplCopyWithImpl<$Res>
    extends _$BuyCreditInAppProductCopyWithImpl<$Res,
        _$BuyCreditInAppProductImpl>
    implements _$$BuyCreditInAppProductImplCopyWith<$Res> {
  __$$BuyCreditInAppProductImplCopyWithImpl(_$BuyCreditInAppProductImpl _value,
      $Res Function(_$BuyCreditInAppProductImpl) _then)
      : super(_value, _then);

  /// Create a copy of BuyCreditInAppProduct
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = null,
    Object? price = null,
    Object? rawPrice = null,
    Object? currencyCode = null,
    Object? credits = null,
    Object? isSubscription = null,
  }) {
    return _then(_$BuyCreditInAppProductImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as String,
      rawPrice: null == rawPrice
          ? _value.rawPrice
          : rawPrice // ignore: cast_nullable_to_non_nullable
              as double,
      currencyCode: null == currencyCode
          ? _value.currencyCode
          : currencyCode // ignore: cast_nullable_to_non_nullable
              as String,
      credits: null == credits
          ? _value.credits
          : credits // ignore: cast_nullable_to_non_nullable
              as int,
      isSubscription: null == isSubscription
          ? _value.isSubscription
          : isSubscription // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BuyCreditInAppProductImpl implements _BuyCreditInAppProduct {
  _$BuyCreditInAppProductImpl(
      {required this.id,
      required this.title,
      required this.description,
      required this.price,
      required this.rawPrice,
      required this.currencyCode,
      required this.credits,
      this.isSubscription = false});

  factory _$BuyCreditInAppProductImpl.fromJson(Map<String, dynamic> json) =>
      _$$BuyCreditInAppProductImplFromJson(json);

  /// Unique identifier for the product
  @override
  final String id;

  /// Product title displayed to users
  @override
  final String title;

  /// Product description
  @override
  final String description;

  /// Formatted price (e.g., "$9.99")
  @override
  final String price;

  /// Raw price value
  @override
  final double rawPrice;

  /// Currency code (e.g., "USD")
  @override
  final String currencyCode;

  /// Amount of credits this product provides
  @override
  final int credits;

  /// Whether this product is a subscription
  @override
  @JsonKey()
  final bool isSubscription;

  @override
  String toString() {
    return 'BuyCreditInAppProduct(id: $id, title: $title, description: $description, price: $price, rawPrice: $rawPrice, currencyCode: $currencyCode, credits: $credits, isSubscription: $isSubscription)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BuyCreditInAppProductImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.rawPrice, rawPrice) ||
                other.rawPrice == rawPrice) &&
            (identical(other.currencyCode, currencyCode) ||
                other.currencyCode == currencyCode) &&
            (identical(other.credits, credits) || other.credits == credits) &&
            (identical(other.isSubscription, isSubscription) ||
                other.isSubscription == isSubscription));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, title, description, price,
      rawPrice, currencyCode, credits, isSubscription);

  /// Create a copy of BuyCreditInAppProduct
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BuyCreditInAppProductImplCopyWith<_$BuyCreditInAppProductImpl>
      get copyWith => __$$BuyCreditInAppProductImplCopyWithImpl<
          _$BuyCreditInAppProductImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BuyCreditInAppProductImplToJson(
      this,
    );
  }
}

abstract class _BuyCreditInAppProduct implements BuyCreditInAppProduct {
  factory _BuyCreditInAppProduct(
      {required final String id,
      required final String title,
      required final String description,
      required final String price,
      required final double rawPrice,
      required final String currencyCode,
      required final int credits,
      final bool isSubscription}) = _$BuyCreditInAppProductImpl;

  factory _BuyCreditInAppProduct.fromJson(Map<String, dynamic> json) =
      _$BuyCreditInAppProductImpl.fromJson;

  /// Unique identifier for the product
  @override
  String get id;

  /// Product title displayed to users
  @override
  String get title;

  /// Product description
  @override
  String get description;

  /// Formatted price (e.g., "$9.99")
  @override
  String get price;

  /// Raw price value
  @override
  double get rawPrice;

  /// Currency code (e.g., "USD")
  @override
  String get currencyCode;

  /// Amount of credits this product provides
  @override
  int get credits;

  /// Whether this product is a subscription
  @override
  bool get isSubscription;

  /// Create a copy of BuyCreditInAppProduct
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BuyCreditInAppProductImplCopyWith<_$BuyCreditInAppProductImpl>
      get copyWith => throw _privateConstructorUsedError;
}

CreditResponse _$CreditResponseFromJson(Map<String, dynamic> json) {
  return _CreditResponse.fromJson(json);
}

/// @nodoc
mixin _$CreditResponse {
  bool get success => throw _privateConstructorUsedError;
  String get newBalance => throw _privateConstructorUsedError;
  String get addedCredit => throw _privateConstructorUsedError;
  String? get message => throw _privateConstructorUsedError;

  /// Serializes this CreditResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CreditResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreditResponseCopyWith<CreditResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreditResponseCopyWith<$Res> {
  factory $CreditResponseCopyWith(
          CreditResponse value, $Res Function(CreditResponse) then) =
      _$CreditResponseCopyWithImpl<$Res, CreditResponse>;
  @useResult
  $Res call(
      {bool success, String newBalance, String addedCredit, String? message});
}

/// @nodoc
class _$CreditResponseCopyWithImpl<$Res, $Val extends CreditResponse>
    implements $CreditResponseCopyWith<$Res> {
  _$CreditResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreditResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? newBalance = null,
    Object? addedCredit = null,
    Object? message = freezed,
  }) {
    return _then(_value.copyWith(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      newBalance: null == newBalance
          ? _value.newBalance
          : newBalance // ignore: cast_nullable_to_non_nullable
              as String,
      addedCredit: null == addedCredit
          ? _value.addedCredit
          : addedCredit // ignore: cast_nullable_to_non_nullable
              as String,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreditResponseImplCopyWith<$Res>
    implements $CreditResponseCopyWith<$Res> {
  factory _$$CreditResponseImplCopyWith(_$CreditResponseImpl value,
          $Res Function(_$CreditResponseImpl) then) =
      __$$CreditResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool success, String newBalance, String addedCredit, String? message});
}

/// @nodoc
class __$$CreditResponseImplCopyWithImpl<$Res>
    extends _$CreditResponseCopyWithImpl<$Res, _$CreditResponseImpl>
    implements _$$CreditResponseImplCopyWith<$Res> {
  __$$CreditResponseImplCopyWithImpl(
      _$CreditResponseImpl _value, $Res Function(_$CreditResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreditResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? newBalance = null,
    Object? addedCredit = null,
    Object? message = freezed,
  }) {
    return _then(_$CreditResponseImpl(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      newBalance: null == newBalance
          ? _value.newBalance
          : newBalance // ignore: cast_nullable_to_non_nullable
              as String,
      addedCredit: null == addedCredit
          ? _value.addedCredit
          : addedCredit // ignore: cast_nullable_to_non_nullable
              as String,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreditResponseImpl implements _CreditResponse {
  _$CreditResponseImpl(
      {required this.success,
      required this.newBalance,
      required this.addedCredit,
      this.message});

  factory _$CreditResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreditResponseImplFromJson(json);

  @override
  final bool success;
  @override
  final String newBalance;
  @override
  final String addedCredit;
  @override
  final String? message;

  @override
  String toString() {
    return 'CreditResponse(success: $success, newBalance: $newBalance, addedCredit: $addedCredit, message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreditResponseImpl &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.newBalance, newBalance) ||
                other.newBalance == newBalance) &&
            (identical(other.addedCredit, addedCredit) ||
                other.addedCredit == addedCredit) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, success, newBalance, addedCredit, message);

  /// Create a copy of CreditResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreditResponseImplCopyWith<_$CreditResponseImpl> get copyWith =>
      __$$CreditResponseImplCopyWithImpl<_$CreditResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreditResponseImplToJson(
      this,
    );
  }
}

abstract class _CreditResponse implements CreditResponse {
  factory _CreditResponse(
      {required final bool success,
      required final String newBalance,
      required final String addedCredit,
      final String? message}) = _$CreditResponseImpl;

  factory _CreditResponse.fromJson(Map<String, dynamic> json) =
      _$CreditResponseImpl.fromJson;

  @override
  bool get success;
  @override
  String get newBalance;
  @override
  String get addedCredit;
  @override
  String? get message;

  /// Create a copy of CreditResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreditResponseImplCopyWith<_$CreditResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

VerificationResponse _$VerificationResponseFromJson(Map<String, dynamic> json) {
  return _VerificationResponse.fromJson(json);
}

/// @nodoc
mixin _$VerificationResponse {
  bool get isValid => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;
  String? get errorCode => throw _privateConstructorUsedError;

  /// Serializes this VerificationResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VerificationResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VerificationResponseCopyWith<VerificationResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VerificationResponseCopyWith<$Res> {
  factory $VerificationResponseCopyWith(VerificationResponse value,
          $Res Function(VerificationResponse) then) =
      _$VerificationResponseCopyWithImpl<$Res, VerificationResponse>;
  @useResult
  $Res call({bool isValid, String message, String? errorCode});
}

/// @nodoc
class _$VerificationResponseCopyWithImpl<$Res,
        $Val extends VerificationResponse>
    implements $VerificationResponseCopyWith<$Res> {
  _$VerificationResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VerificationResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isValid = null,
    Object? message = null,
    Object? errorCode = freezed,
  }) {
    return _then(_value.copyWith(
      isValid: null == isValid
          ? _value.isValid
          : isValid // ignore: cast_nullable_to_non_nullable
              as bool,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      errorCode: freezed == errorCode
          ? _value.errorCode
          : errorCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VerificationResponseImplCopyWith<$Res>
    implements $VerificationResponseCopyWith<$Res> {
  factory _$$VerificationResponseImplCopyWith(_$VerificationResponseImpl value,
          $Res Function(_$VerificationResponseImpl) then) =
      __$$VerificationResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isValid, String message, String? errorCode});
}

/// @nodoc
class __$$VerificationResponseImplCopyWithImpl<$Res>
    extends _$VerificationResponseCopyWithImpl<$Res, _$VerificationResponseImpl>
    implements _$$VerificationResponseImplCopyWith<$Res> {
  __$$VerificationResponseImplCopyWithImpl(_$VerificationResponseImpl _value,
      $Res Function(_$VerificationResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of VerificationResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isValid = null,
    Object? message = null,
    Object? errorCode = freezed,
  }) {
    return _then(_$VerificationResponseImpl(
      isValid: null == isValid
          ? _value.isValid
          : isValid // ignore: cast_nullable_to_non_nullable
              as bool,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      errorCode: freezed == errorCode
          ? _value.errorCode
          : errorCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VerificationResponseImpl implements _VerificationResponse {
  _$VerificationResponseImpl(
      {required this.isValid, required this.message, this.errorCode});

  factory _$VerificationResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$VerificationResponseImplFromJson(json);

  @override
  final bool isValid;
  @override
  final String message;
  @override
  final String? errorCode;

  @override
  String toString() {
    return 'VerificationResponse(isValid: $isValid, message: $message, errorCode: $errorCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerificationResponseImpl &&
            (identical(other.isValid, isValid) || other.isValid == isValid) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.errorCode, errorCode) ||
                other.errorCode == errorCode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, isValid, message, errorCode);

  /// Create a copy of VerificationResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VerificationResponseImplCopyWith<_$VerificationResponseImpl>
      get copyWith =>
          __$$VerificationResponseImplCopyWithImpl<_$VerificationResponseImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VerificationResponseImplToJson(
      this,
    );
  }
}

abstract class _VerificationResponse implements VerificationResponse {
  factory _VerificationResponse(
      {required final bool isValid,
      required final String message,
      final String? errorCode}) = _$VerificationResponseImpl;

  factory _VerificationResponse.fromJson(Map<String, dynamic> json) =
      _$VerificationResponseImpl.fromJson;

  @override
  bool get isValid;
  @override
  String get message;
  @override
  String? get errorCode;

  /// Create a copy of VerificationResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VerificationResponseImplCopyWith<_$VerificationResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
