// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'payment_intent.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CheckoutPaymentIntent _$CheckoutPaymentIntentFromJson(
    Map<String, dynamic> json) {
  return _CheckoutPaymentIntent.fromJson(json);
}

/// @nodoc
mixin _$CheckoutPaymentIntent {
  dynamic get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'client_secret')
  String? get clientSecret =>
      throw _privateConstructorUsedError; // String? paymentMethodId,
// String? paymentMethodNonce,
  dynamic get amount => throw _privateConstructorUsedError;
  String? get currency => throw _privateConstructorUsedError;

  /// Serializes this CheckoutPaymentIntent to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CheckoutPaymentIntent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CheckoutPaymentIntentCopyWith<CheckoutPaymentIntent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CheckoutPaymentIntentCopyWith<$Res> {
  factory $CheckoutPaymentIntentCopyWith(CheckoutPaymentIntent value,
          $Res Function(CheckoutPaymentIntent) then) =
      _$CheckoutPaymentIntentCopyWithImpl<$Res, CheckoutPaymentIntent>;
  @useResult
  $Res call(
      {dynamic id,
      @JsonKey(name: 'client_secret') String? clientSecret,
      dynamic amount,
      String? currency});
}

/// @nodoc
class _$CheckoutPaymentIntentCopyWithImpl<$Res,
        $Val extends CheckoutPaymentIntent>
    implements $CheckoutPaymentIntentCopyWith<$Res> {
  _$CheckoutPaymentIntentCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CheckoutPaymentIntent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? clientSecret = freezed,
    Object? amount = freezed,
    Object? currency = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as dynamic,
      clientSecret: freezed == clientSecret
          ? _value.clientSecret
          : clientSecret // ignore: cast_nullable_to_non_nullable
              as String?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as dynamic,
      currency: freezed == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CheckoutPaymentIntentImplCopyWith<$Res>
    implements $CheckoutPaymentIntentCopyWith<$Res> {
  factory _$$CheckoutPaymentIntentImplCopyWith(
          _$CheckoutPaymentIntentImpl value,
          $Res Function(_$CheckoutPaymentIntentImpl) then) =
      __$$CheckoutPaymentIntentImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {dynamic id,
      @JsonKey(name: 'client_secret') String? clientSecret,
      dynamic amount,
      String? currency});
}

/// @nodoc
class __$$CheckoutPaymentIntentImplCopyWithImpl<$Res>
    extends _$CheckoutPaymentIntentCopyWithImpl<$Res,
        _$CheckoutPaymentIntentImpl>
    implements _$$CheckoutPaymentIntentImplCopyWith<$Res> {
  __$$CheckoutPaymentIntentImplCopyWithImpl(_$CheckoutPaymentIntentImpl _value,
      $Res Function(_$CheckoutPaymentIntentImpl) _then)
      : super(_value, _then);

  /// Create a copy of CheckoutPaymentIntent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? clientSecret = freezed,
    Object? amount = freezed,
    Object? currency = freezed,
  }) {
    return _then(_$CheckoutPaymentIntentImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as dynamic,
      clientSecret: freezed == clientSecret
          ? _value.clientSecret
          : clientSecret // ignore: cast_nullable_to_non_nullable
              as String?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as dynamic,
      currency: freezed == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CheckoutPaymentIntentImpl implements _CheckoutPaymentIntent {
  _$CheckoutPaymentIntentImpl(
      {this.id,
      @JsonKey(name: 'client_secret') this.clientSecret,
      this.amount,
      this.currency});

  factory _$CheckoutPaymentIntentImpl.fromJson(Map<String, dynamic> json) =>
      _$$CheckoutPaymentIntentImplFromJson(json);

  @override
  final dynamic id;
  @override
  @JsonKey(name: 'client_secret')
  final String? clientSecret;
// String? paymentMethodId,
// String? paymentMethodNonce,
  @override
  final dynamic amount;
  @override
  final String? currency;

  @override
  String toString() {
    return 'CheckoutPaymentIntent(id: $id, clientSecret: $clientSecret, amount: $amount, currency: $currency)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CheckoutPaymentIntentImpl &&
            const DeepCollectionEquality().equals(other.id, id) &&
            (identical(other.clientSecret, clientSecret) ||
                other.clientSecret == clientSecret) &&
            const DeepCollectionEquality().equals(other.amount, amount) &&
            (identical(other.currency, currency) ||
                other.currency == currency));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(id),
      clientSecret,
      const DeepCollectionEquality().hash(amount),
      currency);

  /// Create a copy of CheckoutPaymentIntent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CheckoutPaymentIntentImplCopyWith<_$CheckoutPaymentIntentImpl>
      get copyWith => __$$CheckoutPaymentIntentImplCopyWithImpl<
          _$CheckoutPaymentIntentImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CheckoutPaymentIntentImplToJson(
      this,
    );
  }
}

abstract class _CheckoutPaymentIntent implements CheckoutPaymentIntent {
  factory _CheckoutPaymentIntent(
      {final dynamic id,
      @JsonKey(name: 'client_secret') final String? clientSecret,
      final dynamic amount,
      final String? currency}) = _$CheckoutPaymentIntentImpl;

  factory _CheckoutPaymentIntent.fromJson(Map<String, dynamic> json) =
      _$CheckoutPaymentIntentImpl.fromJson;

  @override
  dynamic get id;
  @override
  @JsonKey(name: 'client_secret')
  String? get clientSecret; // String? paymentMethodId,
// String? paymentMethodNonce,
  @override
  dynamic get amount;
  @override
  String? get currency;

  /// Create a copy of CheckoutPaymentIntent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CheckoutPaymentIntentImplCopyWith<_$CheckoutPaymentIntentImpl>
      get copyWith => throw _privateConstructorUsedError;
}
