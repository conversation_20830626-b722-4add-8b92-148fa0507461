import 'package:freezed_annotation/freezed_annotation.dart';

part 'buy_credit_response.freezed.dart';
part 'buy_credit_response.g.dart';

@freezed
class BuyCreditResponse with _$BuyCreditResponse {
  /// Represents a credit purchase option,
  /// including VAT-inclusive price for iOS users.
  factory BuyCreditResponse({
    String? id,
    @JsonKey(name: 'credit_amount') BuyCreditAmountModel? amountToBeCredited,
    @Json<PERSON>ey(name: 'percentage_discount') String? discountInPercentage,
    @JsonKey(name: 'percentage_added') String? percentageAdded,
    String? status,
    @Default(false) bool recommended,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'total_payment') String? totalPayment,
    @Json<PERSON>ey(name: 'playstore_iap_id') String? playStoreIapId,
    @JsonKey(name: 'appstore_iap_id') String? appStoreIapId,

    /// VAT-inclusive price for App Store (iOS), nullable.
    @<PERSON><PERSON><PERSON><PERSON>(name: 'appstore_vat_price') String? appStoreVatPrice,
  }) = _BuyCreditResponse;

  factory BuyCreditResponse.fromJson(Map<String, Object?> json) =>
      _$BuyCreditResponseFromJson(json);
}

@freezed
class BuyCreditAmountModel with _$BuyCreditAmountModel {
  factory BuyCreditAmountModel({
    String? amount,
    String? currency,
    @JsonKey(name: 'currency_code') String? currencyCode,
  }) = _BuyCreditAmountModel;

  factory BuyCreditAmountModel.fromJson(Map<String, Object?> json) =>
      _$BuyCreditAmountModelFromJson(json);
}
