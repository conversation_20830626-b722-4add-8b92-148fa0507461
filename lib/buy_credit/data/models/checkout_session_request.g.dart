// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'checkout_session_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CheckoutSessionRequestImpl _$$CheckoutSessionRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$CheckoutSessionRequestImpl(
      amount: json['amount'] as String,
      paymentMethod: json['payment_method'] as String,
      sessionId: json['e_payment_log_id'] as String?,
      couponCode: json['coupon_code'] as String?,
      enableAutoRecharge: json['auto_recharge'] as bool? ?? true,
      currencyCode: json['currency'] as String? ?? 'BDT',
    );

const _$$CheckoutSessionRequestImplFieldMap = <String, String>{
  'amount': 'amount',
  'paymentMethod': 'payment_method',
  'sessionId': 'e_payment_log_id',
  'couponCode': 'coupon_code',
  'enableAutoRecharge': 'auto_recharge',
  'currencyCode': 'currency',
};

Map<String, dynamic> _$$CheckoutSessionRequestImplToJson(
        _$CheckoutSessionRequestImpl instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'payment_method': instance.paymentMethod,
      if (instance.sessionId case final value?) 'e_payment_log_id': value,
      if (instance.couponCode case final value?) 'coupon_code': value,
      'auto_recharge': instance.enableAutoRecharge,
      'currency': instance.currencyCode,
    };
