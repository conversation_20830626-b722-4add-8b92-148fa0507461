// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'checkout_session_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CheckoutSessionResponse _$CheckoutSessionResponseFromJson(
    Map<String, dynamic> json) {
  return _CheckoutSessionResponse.fromJson(json);
}

/// @nodoc
mixin _$CheckoutSessionResponse {
  String? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'amount_to_be_credited')
  String? get amountToBeCredited => throw _privateConstructorUsedError;
  @JsonKey(name: 'vat_plus_fees')
  String? get vatPlusFees => throw _privateConstructorUsedError;
  String? get discount => throw _privateConstructorUsedError;
  @JsonKey(name: 'total_payment')
  String? get totalPayment => throw _privateConstructorUsedError;

  /// Serializes this CheckoutSessionResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CheckoutSessionResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CheckoutSessionResponseCopyWith<CheckoutSessionResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CheckoutSessionResponseCopyWith<$Res> {
  factory $CheckoutSessionResponseCopyWith(CheckoutSessionResponse value,
          $Res Function(CheckoutSessionResponse) then) =
      _$CheckoutSessionResponseCopyWithImpl<$Res, CheckoutSessionResponse>;
  @useResult
  $Res call(
      {String? id,
      @JsonKey(name: 'amount_to_be_credited') String? amountToBeCredited,
      @JsonKey(name: 'vat_plus_fees') String? vatPlusFees,
      String? discount,
      @JsonKey(name: 'total_payment') String? totalPayment});
}

/// @nodoc
class _$CheckoutSessionResponseCopyWithImpl<$Res,
        $Val extends CheckoutSessionResponse>
    implements $CheckoutSessionResponseCopyWith<$Res> {
  _$CheckoutSessionResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CheckoutSessionResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? amountToBeCredited = freezed,
    Object? vatPlusFees = freezed,
    Object? discount = freezed,
    Object? totalPayment = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      amountToBeCredited: freezed == amountToBeCredited
          ? _value.amountToBeCredited
          : amountToBeCredited // ignore: cast_nullable_to_non_nullable
              as String?,
      vatPlusFees: freezed == vatPlusFees
          ? _value.vatPlusFees
          : vatPlusFees // ignore: cast_nullable_to_non_nullable
              as String?,
      discount: freezed == discount
          ? _value.discount
          : discount // ignore: cast_nullable_to_non_nullable
              as String?,
      totalPayment: freezed == totalPayment
          ? _value.totalPayment
          : totalPayment // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CheckoutSessionResponseImplCopyWith<$Res>
    implements $CheckoutSessionResponseCopyWith<$Res> {
  factory _$$CheckoutSessionResponseImplCopyWith(
          _$CheckoutSessionResponseImpl value,
          $Res Function(_$CheckoutSessionResponseImpl) then) =
      __$$CheckoutSessionResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? id,
      @JsonKey(name: 'amount_to_be_credited') String? amountToBeCredited,
      @JsonKey(name: 'vat_plus_fees') String? vatPlusFees,
      String? discount,
      @JsonKey(name: 'total_payment') String? totalPayment});
}

/// @nodoc
class __$$CheckoutSessionResponseImplCopyWithImpl<$Res>
    extends _$CheckoutSessionResponseCopyWithImpl<$Res,
        _$CheckoutSessionResponseImpl>
    implements _$$CheckoutSessionResponseImplCopyWith<$Res> {
  __$$CheckoutSessionResponseImplCopyWithImpl(
      _$CheckoutSessionResponseImpl _value,
      $Res Function(_$CheckoutSessionResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of CheckoutSessionResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? amountToBeCredited = freezed,
    Object? vatPlusFees = freezed,
    Object? discount = freezed,
    Object? totalPayment = freezed,
  }) {
    return _then(_$CheckoutSessionResponseImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      amountToBeCredited: freezed == amountToBeCredited
          ? _value.amountToBeCredited
          : amountToBeCredited // ignore: cast_nullable_to_non_nullable
              as String?,
      vatPlusFees: freezed == vatPlusFees
          ? _value.vatPlusFees
          : vatPlusFees // ignore: cast_nullable_to_non_nullable
              as String?,
      discount: freezed == discount
          ? _value.discount
          : discount // ignore: cast_nullable_to_non_nullable
              as String?,
      totalPayment: freezed == totalPayment
          ? _value.totalPayment
          : totalPayment // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CheckoutSessionResponseImpl implements _CheckoutSessionResponse {
  _$CheckoutSessionResponseImpl(
      {this.id,
      @JsonKey(name: 'amount_to_be_credited') this.amountToBeCredited,
      @JsonKey(name: 'vat_plus_fees') this.vatPlusFees,
      this.discount,
      @JsonKey(name: 'total_payment') this.totalPayment});

  factory _$CheckoutSessionResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$CheckoutSessionResponseImplFromJson(json);

  @override
  final String? id;
  @override
  @JsonKey(name: 'amount_to_be_credited')
  final String? amountToBeCredited;
  @override
  @JsonKey(name: 'vat_plus_fees')
  final String? vatPlusFees;
  @override
  final String? discount;
  @override
  @JsonKey(name: 'total_payment')
  final String? totalPayment;

  @override
  String toString() {
    return 'CheckoutSessionResponse(id: $id, amountToBeCredited: $amountToBeCredited, vatPlusFees: $vatPlusFees, discount: $discount, totalPayment: $totalPayment)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CheckoutSessionResponseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.amountToBeCredited, amountToBeCredited) ||
                other.amountToBeCredited == amountToBeCredited) &&
            (identical(other.vatPlusFees, vatPlusFees) ||
                other.vatPlusFees == vatPlusFees) &&
            (identical(other.discount, discount) ||
                other.discount == discount) &&
            (identical(other.totalPayment, totalPayment) ||
                other.totalPayment == totalPayment));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, amountToBeCredited, vatPlusFees, discount, totalPayment);

  /// Create a copy of CheckoutSessionResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CheckoutSessionResponseImplCopyWith<_$CheckoutSessionResponseImpl>
      get copyWith => __$$CheckoutSessionResponseImplCopyWithImpl<
          _$CheckoutSessionResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CheckoutSessionResponseImplToJson(
      this,
    );
  }
}

abstract class _CheckoutSessionResponse implements CheckoutSessionResponse {
  factory _CheckoutSessionResponse(
      {final String? id,
      @JsonKey(name: 'amount_to_be_credited') final String? amountToBeCredited,
      @JsonKey(name: 'vat_plus_fees') final String? vatPlusFees,
      final String? discount,
      @JsonKey(name: 'total_payment')
      final String? totalPayment}) = _$CheckoutSessionResponseImpl;

  factory _CheckoutSessionResponse.fromJson(Map<String, dynamic> json) =
      _$CheckoutSessionResponseImpl.fromJson;

  @override
  String? get id;
  @override
  @JsonKey(name: 'amount_to_be_credited')
  String? get amountToBeCredited;
  @override
  @JsonKey(name: 'vat_plus_fees')
  String? get vatPlusFees;
  @override
  String? get discount;
  @override
  @JsonKey(name: 'total_payment')
  String? get totalPayment;

  /// Create a copy of CheckoutSessionResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CheckoutSessionResponseImplCopyWith<_$CheckoutSessionResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
