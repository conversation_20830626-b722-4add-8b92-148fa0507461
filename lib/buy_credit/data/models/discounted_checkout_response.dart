import 'package:freezed_annotation/freezed_annotation.dart';

part 'discounted_checkout_response.freezed.dart';
part 'discounted_checkout_response.g.dart';

@freezed
class DiscountedCheckoutResponse with _$DiscountedCheckoutResponse {
  const factory DiscountedCheckoutResponse({
    required double amount,
    required String currency,
    required bool enabled,
    @JsonKey(name: 'coupon_code') String? couponCode,
  }) = _DiscountedCheckoutResponse;

  factory DiscountedCheckoutResponse.fromJson(Map<String, dynamic> json) =>
      _$DiscountedCheckoutResponseFromJson(json);
}
