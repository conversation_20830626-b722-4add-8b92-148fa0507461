// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'buy_credit_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

BuyCreditResponse _$BuyCreditResponseFromJson(Map<String, dynamic> json) {
  return _BuyCreditResponse.fromJson(json);
}

/// @nodoc
mixin _$BuyCreditResponse {
  String? get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'credit_amount')
  BuyCreditAmountModel? get amountToBeCredited =>
      throw _privateConstructorUsedError;
  @JsonKey(name: 'percentage_discount')
  String? get discountInPercentage => throw _privateConstructorUsedError;
  @JsonKey(name: 'percentage_added')
  String? get percentageAdded => throw _privateConstructorUsedError;
  String? get status => throw _privateConstructorUsedError;
  bool get recommended => throw _privateConstructorUsedError;
  @JsonKey(name: 'total_payment')
  String? get totalPayment => throw _privateConstructorUsedError;
  @JsonKey(name: 'playstore_iap_id')
  String? get playStoreIapId => throw _privateConstructorUsedError;
  @JsonKey(name: 'appstore_iap_id')
  String? get appStoreIapId => throw _privateConstructorUsedError;

  /// VAT-inclusive price for App Store (iOS), nullable.
  @JsonKey(name: 'appstore_vat_price')
  String? get appStoreVatPrice => throw _privateConstructorUsedError;

  /// Serializes this BuyCreditResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BuyCreditResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BuyCreditResponseCopyWith<BuyCreditResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BuyCreditResponseCopyWith<$Res> {
  factory $BuyCreditResponseCopyWith(
          BuyCreditResponse value, $Res Function(BuyCreditResponse) then) =
      _$BuyCreditResponseCopyWithImpl<$Res, BuyCreditResponse>;
  @useResult
  $Res call(
      {String? id,
      @JsonKey(name: 'credit_amount') BuyCreditAmountModel? amountToBeCredited,
      @JsonKey(name: 'percentage_discount') String? discountInPercentage,
      @JsonKey(name: 'percentage_added') String? percentageAdded,
      String? status,
      bool recommended,
      @JsonKey(name: 'total_payment') String? totalPayment,
      @JsonKey(name: 'playstore_iap_id') String? playStoreIapId,
      @JsonKey(name: 'appstore_iap_id') String? appStoreIapId,
      @JsonKey(name: 'appstore_vat_price') String? appStoreVatPrice});

  $BuyCreditAmountModelCopyWith<$Res>? get amountToBeCredited;
}

/// @nodoc
class _$BuyCreditResponseCopyWithImpl<$Res, $Val extends BuyCreditResponse>
    implements $BuyCreditResponseCopyWith<$Res> {
  _$BuyCreditResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BuyCreditResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? amountToBeCredited = freezed,
    Object? discountInPercentage = freezed,
    Object? percentageAdded = freezed,
    Object? status = freezed,
    Object? recommended = null,
    Object? totalPayment = freezed,
    Object? playStoreIapId = freezed,
    Object? appStoreIapId = freezed,
    Object? appStoreVatPrice = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      amountToBeCredited: freezed == amountToBeCredited
          ? _value.amountToBeCredited
          : amountToBeCredited // ignore: cast_nullable_to_non_nullable
              as BuyCreditAmountModel?,
      discountInPercentage: freezed == discountInPercentage
          ? _value.discountInPercentage
          : discountInPercentage // ignore: cast_nullable_to_non_nullable
              as String?,
      percentageAdded: freezed == percentageAdded
          ? _value.percentageAdded
          : percentageAdded // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      recommended: null == recommended
          ? _value.recommended
          : recommended // ignore: cast_nullable_to_non_nullable
              as bool,
      totalPayment: freezed == totalPayment
          ? _value.totalPayment
          : totalPayment // ignore: cast_nullable_to_non_nullable
              as String?,
      playStoreIapId: freezed == playStoreIapId
          ? _value.playStoreIapId
          : playStoreIapId // ignore: cast_nullable_to_non_nullable
              as String?,
      appStoreIapId: freezed == appStoreIapId
          ? _value.appStoreIapId
          : appStoreIapId // ignore: cast_nullable_to_non_nullable
              as String?,
      appStoreVatPrice: freezed == appStoreVatPrice
          ? _value.appStoreVatPrice
          : appStoreVatPrice // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of BuyCreditResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BuyCreditAmountModelCopyWith<$Res>? get amountToBeCredited {
    if (_value.amountToBeCredited == null) {
      return null;
    }

    return $BuyCreditAmountModelCopyWith<$Res>(_value.amountToBeCredited!,
        (value) {
      return _then(_value.copyWith(amountToBeCredited: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$BuyCreditResponseImplCopyWith<$Res>
    implements $BuyCreditResponseCopyWith<$Res> {
  factory _$$BuyCreditResponseImplCopyWith(_$BuyCreditResponseImpl value,
          $Res Function(_$BuyCreditResponseImpl) then) =
      __$$BuyCreditResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? id,
      @JsonKey(name: 'credit_amount') BuyCreditAmountModel? amountToBeCredited,
      @JsonKey(name: 'percentage_discount') String? discountInPercentage,
      @JsonKey(name: 'percentage_added') String? percentageAdded,
      String? status,
      bool recommended,
      @JsonKey(name: 'total_payment') String? totalPayment,
      @JsonKey(name: 'playstore_iap_id') String? playStoreIapId,
      @JsonKey(name: 'appstore_iap_id') String? appStoreIapId,
      @JsonKey(name: 'appstore_vat_price') String? appStoreVatPrice});

  @override
  $BuyCreditAmountModelCopyWith<$Res>? get amountToBeCredited;
}

/// @nodoc
class __$$BuyCreditResponseImplCopyWithImpl<$Res>
    extends _$BuyCreditResponseCopyWithImpl<$Res, _$BuyCreditResponseImpl>
    implements _$$BuyCreditResponseImplCopyWith<$Res> {
  __$$BuyCreditResponseImplCopyWithImpl(_$BuyCreditResponseImpl _value,
      $Res Function(_$BuyCreditResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of BuyCreditResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? amountToBeCredited = freezed,
    Object? discountInPercentage = freezed,
    Object? percentageAdded = freezed,
    Object? status = freezed,
    Object? recommended = null,
    Object? totalPayment = freezed,
    Object? playStoreIapId = freezed,
    Object? appStoreIapId = freezed,
    Object? appStoreVatPrice = freezed,
  }) {
    return _then(_$BuyCreditResponseImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      amountToBeCredited: freezed == amountToBeCredited
          ? _value.amountToBeCredited
          : amountToBeCredited // ignore: cast_nullable_to_non_nullable
              as BuyCreditAmountModel?,
      discountInPercentage: freezed == discountInPercentage
          ? _value.discountInPercentage
          : discountInPercentage // ignore: cast_nullable_to_non_nullable
              as String?,
      percentageAdded: freezed == percentageAdded
          ? _value.percentageAdded
          : percentageAdded // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      recommended: null == recommended
          ? _value.recommended
          : recommended // ignore: cast_nullable_to_non_nullable
              as bool,
      totalPayment: freezed == totalPayment
          ? _value.totalPayment
          : totalPayment // ignore: cast_nullable_to_non_nullable
              as String?,
      playStoreIapId: freezed == playStoreIapId
          ? _value.playStoreIapId
          : playStoreIapId // ignore: cast_nullable_to_non_nullable
              as String?,
      appStoreIapId: freezed == appStoreIapId
          ? _value.appStoreIapId
          : appStoreIapId // ignore: cast_nullable_to_non_nullable
              as String?,
      appStoreVatPrice: freezed == appStoreVatPrice
          ? _value.appStoreVatPrice
          : appStoreVatPrice // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BuyCreditResponseImpl implements _BuyCreditResponse {
  _$BuyCreditResponseImpl(
      {this.id,
      @JsonKey(name: 'credit_amount') this.amountToBeCredited,
      @JsonKey(name: 'percentage_discount') this.discountInPercentage,
      @JsonKey(name: 'percentage_added') this.percentageAdded,
      this.status,
      this.recommended = false,
      @JsonKey(name: 'total_payment') this.totalPayment,
      @JsonKey(name: 'playstore_iap_id') this.playStoreIapId,
      @JsonKey(name: 'appstore_iap_id') this.appStoreIapId,
      @JsonKey(name: 'appstore_vat_price') this.appStoreVatPrice});

  factory _$BuyCreditResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$BuyCreditResponseImplFromJson(json);

  @override
  final String? id;
  @override
  @JsonKey(name: 'credit_amount')
  final BuyCreditAmountModel? amountToBeCredited;
  @override
  @JsonKey(name: 'percentage_discount')
  final String? discountInPercentage;
  @override
  @JsonKey(name: 'percentage_added')
  final String? percentageAdded;
  @override
  final String? status;
  @override
  @JsonKey()
  final bool recommended;
  @override
  @JsonKey(name: 'total_payment')
  final String? totalPayment;
  @override
  @JsonKey(name: 'playstore_iap_id')
  final String? playStoreIapId;
  @override
  @JsonKey(name: 'appstore_iap_id')
  final String? appStoreIapId;

  /// VAT-inclusive price for App Store (iOS), nullable.
  @override
  @JsonKey(name: 'appstore_vat_price')
  final String? appStoreVatPrice;

  @override
  String toString() {
    return 'BuyCreditResponse(id: $id, amountToBeCredited: $amountToBeCredited, discountInPercentage: $discountInPercentage, percentageAdded: $percentageAdded, status: $status, recommended: $recommended, totalPayment: $totalPayment, playStoreIapId: $playStoreIapId, appStoreIapId: $appStoreIapId, appStoreVatPrice: $appStoreVatPrice)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BuyCreditResponseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.amountToBeCredited, amountToBeCredited) ||
                other.amountToBeCredited == amountToBeCredited) &&
            (identical(other.discountInPercentage, discountInPercentage) ||
                other.discountInPercentage == discountInPercentage) &&
            (identical(other.percentageAdded, percentageAdded) ||
                other.percentageAdded == percentageAdded) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.recommended, recommended) ||
                other.recommended == recommended) &&
            (identical(other.totalPayment, totalPayment) ||
                other.totalPayment == totalPayment) &&
            (identical(other.playStoreIapId, playStoreIapId) ||
                other.playStoreIapId == playStoreIapId) &&
            (identical(other.appStoreIapId, appStoreIapId) ||
                other.appStoreIapId == appStoreIapId) &&
            (identical(other.appStoreVatPrice, appStoreVatPrice) ||
                other.appStoreVatPrice == appStoreVatPrice));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      amountToBeCredited,
      discountInPercentage,
      percentageAdded,
      status,
      recommended,
      totalPayment,
      playStoreIapId,
      appStoreIapId,
      appStoreVatPrice);

  /// Create a copy of BuyCreditResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BuyCreditResponseImplCopyWith<_$BuyCreditResponseImpl> get copyWith =>
      __$$BuyCreditResponseImplCopyWithImpl<_$BuyCreditResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BuyCreditResponseImplToJson(
      this,
    );
  }
}

abstract class _BuyCreditResponse implements BuyCreditResponse {
  factory _BuyCreditResponse(
      {final String? id,
      @JsonKey(name: 'credit_amount')
      final BuyCreditAmountModel? amountToBeCredited,
      @JsonKey(name: 'percentage_discount') final String? discountInPercentage,
      @JsonKey(name: 'percentage_added') final String? percentageAdded,
      final String? status,
      final bool recommended,
      @JsonKey(name: 'total_payment') final String? totalPayment,
      @JsonKey(name: 'playstore_iap_id') final String? playStoreIapId,
      @JsonKey(name: 'appstore_iap_id') final String? appStoreIapId,
      @JsonKey(name: 'appstore_vat_price')
      final String? appStoreVatPrice}) = _$BuyCreditResponseImpl;

  factory _BuyCreditResponse.fromJson(Map<String, dynamic> json) =
      _$BuyCreditResponseImpl.fromJson;

  @override
  String? get id;
  @override
  @JsonKey(name: 'credit_amount')
  BuyCreditAmountModel? get amountToBeCredited;
  @override
  @JsonKey(name: 'percentage_discount')
  String? get discountInPercentage;
  @override
  @JsonKey(name: 'percentage_added')
  String? get percentageAdded;
  @override
  String? get status;
  @override
  bool get recommended;
  @override
  @JsonKey(name: 'total_payment')
  String? get totalPayment;
  @override
  @JsonKey(name: 'playstore_iap_id')
  String? get playStoreIapId;
  @override
  @JsonKey(name: 'appstore_iap_id')
  String? get appStoreIapId;

  /// VAT-inclusive price for App Store (iOS), nullable.
  @override
  @JsonKey(name: 'appstore_vat_price')
  String? get appStoreVatPrice;

  /// Create a copy of BuyCreditResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BuyCreditResponseImplCopyWith<_$BuyCreditResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

BuyCreditAmountModel _$BuyCreditAmountModelFromJson(Map<String, dynamic> json) {
  return _BuyCreditAmountModel.fromJson(json);
}

/// @nodoc
mixin _$BuyCreditAmountModel {
  String? get amount => throw _privateConstructorUsedError;
  String? get currency => throw _privateConstructorUsedError;
  @JsonKey(name: 'currency_code')
  String? get currencyCode => throw _privateConstructorUsedError;

  /// Serializes this BuyCreditAmountModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BuyCreditAmountModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BuyCreditAmountModelCopyWith<BuyCreditAmountModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BuyCreditAmountModelCopyWith<$Res> {
  factory $BuyCreditAmountModelCopyWith(BuyCreditAmountModel value,
          $Res Function(BuyCreditAmountModel) then) =
      _$BuyCreditAmountModelCopyWithImpl<$Res, BuyCreditAmountModel>;
  @useResult
  $Res call(
      {String? amount,
      String? currency,
      @JsonKey(name: 'currency_code') String? currencyCode});
}

/// @nodoc
class _$BuyCreditAmountModelCopyWithImpl<$Res,
        $Val extends BuyCreditAmountModel>
    implements $BuyCreditAmountModelCopyWith<$Res> {
  _$BuyCreditAmountModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BuyCreditAmountModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = freezed,
    Object? currency = freezed,
    Object? currencyCode = freezed,
  }) {
    return _then(_value.copyWith(
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as String?,
      currency: freezed == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String?,
      currencyCode: freezed == currencyCode
          ? _value.currencyCode
          : currencyCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BuyCreditAmountModelImplCopyWith<$Res>
    implements $BuyCreditAmountModelCopyWith<$Res> {
  factory _$$BuyCreditAmountModelImplCopyWith(_$BuyCreditAmountModelImpl value,
          $Res Function(_$BuyCreditAmountModelImpl) then) =
      __$$BuyCreditAmountModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? amount,
      String? currency,
      @JsonKey(name: 'currency_code') String? currencyCode});
}

/// @nodoc
class __$$BuyCreditAmountModelImplCopyWithImpl<$Res>
    extends _$BuyCreditAmountModelCopyWithImpl<$Res, _$BuyCreditAmountModelImpl>
    implements _$$BuyCreditAmountModelImplCopyWith<$Res> {
  __$$BuyCreditAmountModelImplCopyWithImpl(_$BuyCreditAmountModelImpl _value,
      $Res Function(_$BuyCreditAmountModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of BuyCreditAmountModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = freezed,
    Object? currency = freezed,
    Object? currencyCode = freezed,
  }) {
    return _then(_$BuyCreditAmountModelImpl(
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as String?,
      currency: freezed == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String?,
      currencyCode: freezed == currencyCode
          ? _value.currencyCode
          : currencyCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BuyCreditAmountModelImpl implements _BuyCreditAmountModel {
  _$BuyCreditAmountModelImpl(
      {this.amount,
      this.currency,
      @JsonKey(name: 'currency_code') this.currencyCode});

  factory _$BuyCreditAmountModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$BuyCreditAmountModelImplFromJson(json);

  @override
  final String? amount;
  @override
  final String? currency;
  @override
  @JsonKey(name: 'currency_code')
  final String? currencyCode;

  @override
  String toString() {
    return 'BuyCreditAmountModel(amount: $amount, currency: $currency, currencyCode: $currencyCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BuyCreditAmountModelImpl &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.currencyCode, currencyCode) ||
                other.currencyCode == currencyCode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, amount, currency, currencyCode);

  /// Create a copy of BuyCreditAmountModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BuyCreditAmountModelImplCopyWith<_$BuyCreditAmountModelImpl>
      get copyWith =>
          __$$BuyCreditAmountModelImplCopyWithImpl<_$BuyCreditAmountModelImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BuyCreditAmountModelImplToJson(
      this,
    );
  }
}

abstract class _BuyCreditAmountModel implements BuyCreditAmountModel {
  factory _BuyCreditAmountModel(
          {final String? amount,
          final String? currency,
          @JsonKey(name: 'currency_code') final String? currencyCode}) =
      _$BuyCreditAmountModelImpl;

  factory _BuyCreditAmountModel.fromJson(Map<String, dynamic> json) =
      _$BuyCreditAmountModelImpl.fromJson;

  @override
  String? get amount;
  @override
  String? get currency;
  @override
  @JsonKey(name: 'currency_code')
  String? get currencyCode;

  /// Create a copy of BuyCreditAmountModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BuyCreditAmountModelImplCopyWith<_$BuyCreditAmountModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
