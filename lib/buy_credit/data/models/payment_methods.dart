enum BuyCreditPaymentMethods {
  applePay,
  googlePay,
  others,
}

extension PaymentMethodsExtension on BuyCreditPaymentMethods {
  String get name {
    switch (this) {
      case BuyCreditPaymentMethods.applePay:
        return 'apple_pay';
      case BuyCreditPaymentMethods.googlePay:
        return 'google_pay';
      case BuyCreditPaymentMethods.others:
        return 'others';
    }
  }

  bool get isApplePay => this == BuyCreditPaymentMethods.applePay;
  bool get isGooglePay => this == BuyCreditPaymentMethods.googlePay;
  bool get isOthers => this == BuyCreditPaymentMethods.others;
  bool get isIapStore =>
      this == BuyCreditPaymentMethods.applePay ||
      this == BuyCreditPaymentMethods.googlePay;
}

extension PaymentMethodsFromString on String {
  BuyCreditPaymentMethods get toPaymentMethods {
    switch (this) {
      case 'Apple Pay':
      case 'apple_pay':
      case 'apple':
        return BuyCreditPaymentMethods.applePay;
      case 'Google Pay':
      case 'google_pay':
      case 'google':
        return BuyCreditPaymentMethods.googlePay;
      case 'Stripe':
      case 'card':
      case 'others':
        return BuyCreditPaymentMethods.others;
      default:
        return BuyCreditPaymentMethods.others;
    }
  }
}
