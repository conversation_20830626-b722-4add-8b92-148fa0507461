import 'package:freezed_annotation/freezed_annotation.dart';

part 'buy_credit_iap.freezed.dart';
part 'buy_credit_iap.g.dart';

/// Status of a purchase in BuyCreditInAppTalk
enum BuyCreditInAppPurchaseStatus {
  /// Purchase is pending
  pending,

  /// Purchase completed successfully
  completed,

  /// Purchase failed
  failed,

  /// Purchase is being verified
  verifying,

  /// Purchase has been verified
  verified,

  /// Purchase has been consumed (for consumables)
  consumed,

  /// Purchase has been restored
  restored,
}

extension BuyCreditInAppPurchaseStatusX on BuyCreditInAppPurchaseStatus {
  /// Converts the enum value to a string
  String get value => toString().split('.').last;

  /// Converts a string to the corresponding enum value
  static BuyCreditInAppPurchaseStatus fromString(String status) {
    return BuyCreditInAppPurchaseStatus.values
        .firstWhere((e) => e.value == status);
  }

  /// Converts the enum value to a string for display
  String get displayValue {
    switch (this) {
      case BuyCreditInAppPurchaseStatus.pending:
        return 'Pending';
      case BuyCreditInAppPurchaseStatus.completed:
        return 'Completed';
      case BuyCreditInAppPurchaseStatus.failed:
        return 'Failed';
      case BuyCreditInAppPurchaseStatus.verifying:
        return 'Verifying';
      case BuyCreditInAppPurchaseStatus.verified:
        return 'Verified';
      case BuyCreditInAppPurchaseStatus.consumed:
        return 'Consumed';
      case BuyCreditInAppPurchaseStatus.restored:
        return 'Restored';
    }
  }

  /// handy helper
  bool get isPending => this == BuyCreditInAppPurchaseStatus.pending;
  bool get isCompleted => this == BuyCreditInAppPurchaseStatus.completed;
  bool get isFailed => this == BuyCreditInAppPurchaseStatus.failed;
  bool get isVerifying => this == BuyCreditInAppPurchaseStatus.verifying;
  bool get isVerified => this == BuyCreditInAppPurchaseStatus.verified;
  bool get isConsumed => this == BuyCreditInAppPurchaseStatus.consumed;
  bool get isRestored => this == BuyCreditInAppPurchaseStatus.restored;
  bool get isUnsubscribed => this != BuyCreditInAppPurchaseStatus.restored;
  bool get isSuccess =>
      this == BuyCreditInAppPurchaseStatus.completed ||
      this == BuyCreditInAppPurchaseStatus.restored;
}

/// Model representing a purchase transaction
@freezed
class BuyCreditInAppPurchase with _$BuyCreditInAppPurchase {
  /// Creates a BuyCreditInAppPurchase instance
  const factory BuyCreditInAppPurchase({
    /// Unique purchase identifier
    required String purchaseId,

    /// ID of the purchased product
    required String productId,

    /// Transaction date
    required DateTime purchaseDate,

    /// Current status of the purchase
    required BuyCreditInAppPurchaseStatus status,

    /// Error message if purchase failed
    String? errorMessage,

    /// Transaction verification data
    Map<String, dynamic>? verificationData,

    /// Whether this purchase is a subscription
    @Default(false) bool isSubscription,
  }) = _BuyCreditInAppPurchase;

  /// Creates a BuyCreditInAppPurchase from JSON map
  factory BuyCreditInAppPurchase.fromJson(Map<String, dynamic> json) =>
      _$BuyCreditInAppPurchaseFromJson(json);
}

@freezed
class BuyCreditInAppProduct with _$BuyCreditInAppProduct {
  factory BuyCreditInAppProduct({
    /// Unique identifier for the product
    required String id,

    /// Product title displayed to users
    required String title,

    /// Product description
    required String description,

    /// Formatted price (e.g., "$9.99")
    required String price,

    /// Raw price value
    required double rawPrice,

    /// Currency code (e.g., "USD")
    required String currencyCode,

    /// Amount of credits this product provides
    required int credits,

    /// Whether this product is a subscription
    @Default(false) bool isSubscription,
  }) = _BuyCreditInAppProduct;

  factory BuyCreditInAppProduct.fromJson(Map<String, dynamic> json) =>
      _$BuyCreditInAppProductFromJson(json);
}

@freezed
class CreditResponse with _$CreditResponse {
  factory CreditResponse({
    required bool success,
    required String newBalance,
    required String addedCredit,
    String? message,
  }) = _CreditResponse;

  factory CreditResponse.fromJson(Map<String, dynamic> json) =>
      _$CreditResponseFromJson(json);
}

@freezed
class VerificationResponse with _$VerificationResponse {
  factory VerificationResponse({
    required bool isValid,
    required String message,
    String? errorCode,
  }) = _VerificationResponse;

  factory VerificationResponse.fromJson(Map<String, dynamic> json) =>
      _$VerificationResponseFromJson(json);
}
