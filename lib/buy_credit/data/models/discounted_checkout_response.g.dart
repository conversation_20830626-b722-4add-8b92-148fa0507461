// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'discounted_checkout_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DiscountedCheckoutResponseImpl _$$DiscountedCheckoutResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$DiscountedCheckoutResponseImpl(
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String,
      enabled: json['enabled'] as bool,
      couponCode: json['coupon_code'] as String?,
    );

const _$$DiscountedCheckoutResponseImplFieldMap = <String, String>{
  'amount': 'amount',
  'currency': 'currency',
  'enabled': 'enabled',
  'couponCode': 'coupon_code',
};

Map<String, dynamic> _$$DiscountedCheckoutResponseImplToJson(
        _$DiscountedCheckoutResponseImpl instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'currency': instance.currency,
      'enabled': instance.enabled,
      if (instance.couponCode case final value?) 'coupon_code': value,
    };
