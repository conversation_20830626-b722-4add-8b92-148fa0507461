// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_intent.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CheckoutPaymentIntentImpl _$$CheckoutPaymentIntentImplFromJson(
        Map<String, dynamic> json) =>
    _$CheckoutPaymentIntentImpl(
      id: json['id'],
      clientSecret: json['client_secret'] as String?,
      amount: json['amount'],
      currency: json['currency'] as String?,
    );

const _$$CheckoutPaymentIntentImplFieldMap = <String, String>{
  'id': 'id',
  'clientSecret': 'client_secret',
  'amount': 'amount',
  'currency': 'currency',
};

Map<String, dynamic> _$$CheckoutPaymentIntentImplToJson(
        _$CheckoutPaymentIntentImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.clientSecret case final value?) 'client_secret': value,
      if (instance.amount case final value?) 'amount': value,
      if (instance.currency case final value?) 'currency': value,
    };
