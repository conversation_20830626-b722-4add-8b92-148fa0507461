// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'buy_credit_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BuyCreditResponseImpl _$$BuyCreditResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$BuyCreditResponseImpl(
      id: json['id'] as String?,
      amountToBeCredited: json['credit_amount'] == null
          ? null
          : BuyCreditAmountModel.fromJson(
              json['credit_amount'] as Map<String, dynamic>),
      discountInPercentage: json['percentage_discount'] as String?,
      percentageAdded: json['percentage_added'] as String?,
      status: json['status'] as String?,
      recommended: json['recommended'] as bool? ?? false,
      totalPayment: json['total_payment'] as String?,
      playStoreIapId: json['playstore_iap_id'] as String?,
      appStoreIapId: json['appstore_iap_id'] as String?,
      appStoreVatPrice: json['appstore_vat_price'] as String?,
    );

const _$$BuyCreditResponseImplFieldMap = <String, String>{
  'id': 'id',
  'amountToBeCredited': 'credit_amount',
  'discountInPercentage': 'percentage_discount',
  'percentageAdded': 'percentage_added',
  'status': 'status',
  'recommended': 'recommended',
  'totalPayment': 'total_payment',
  'playStoreIapId': 'playstore_iap_id',
  'appStoreIapId': 'appstore_iap_id',
  'appStoreVatPrice': 'appstore_vat_price',
};

Map<String, dynamic> _$$BuyCreditResponseImplToJson(
        _$BuyCreditResponseImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.amountToBeCredited?.toJson() case final value?)
        'credit_amount': value,
      if (instance.discountInPercentage case final value?)
        'percentage_discount': value,
      if (instance.percentageAdded case final value?) 'percentage_added': value,
      if (instance.status case final value?) 'status': value,
      'recommended': instance.recommended,
      if (instance.totalPayment case final value?) 'total_payment': value,
      if (instance.playStoreIapId case final value?) 'playstore_iap_id': value,
      if (instance.appStoreIapId case final value?) 'appstore_iap_id': value,
      if (instance.appStoreVatPrice case final value?)
        'appstore_vat_price': value,
    };

_$BuyCreditAmountModelImpl _$$BuyCreditAmountModelImplFromJson(
        Map<String, dynamic> json) =>
    _$BuyCreditAmountModelImpl(
      amount: json['amount'] as String?,
      currency: json['currency'] as String?,
      currencyCode: json['currency_code'] as String?,
    );

const _$$BuyCreditAmountModelImplFieldMap = <String, String>{
  'amount': 'amount',
  'currency': 'currency',
  'currencyCode': 'currency_code',
};

Map<String, dynamic> _$$BuyCreditAmountModelImplToJson(
        _$BuyCreditAmountModelImpl instance) =>
    <String, dynamic>{
      if (instance.amount case final value?) 'amount': value,
      if (instance.currency case final value?) 'currency': value,
      if (instance.currencyCode case final value?) 'currency_code': value,
    };
