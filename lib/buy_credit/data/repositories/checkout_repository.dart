import 'package:data/data.dart';
import 'package:either_dart/either.dart';
import 'package:froggytalk/buy_credit/buy_credit.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:utils/utils.dart';

class CheckoutRepository {
  Future<
      Either<FormValidatedErrorResponse,
          PaginatedResponse<CheckoutPaymentIntent>>> execute({
    required String paymentId,
    String? couponCode,
  }) async {
    try {
      final request = ApiRequest(
        route: '/v1/mobile/proceed-payment/$paymentId',
        requestType: RequestType.post,
        params: {},
      );

      final response =
          await ApiClient.getInstance().executeRequest(request: request);

      if (response.isSuccess) {
        final fromJson = response.data;
        final paymentIntentObj =
            fromJson['payment_intent'] as Map<String, dynamic>;
        final paginatedResponse = PaginatedResponse<CheckoutPaymentIntent>(
          status: response.statusToString,
          message: response.successOrErrorMessage,
          data: CheckoutPaymentIntent.fromJson(paymentIntentObj),
        );

        FroggyLogger.info('Response: $paginatedResponse');

        return Right(paginatedResponse);
      } else if (response.hasFormErrors) {
        final formErrors = FormValidatedErrorResponse.fromJson(response.data);

        final formattedErrors = {
          ...formErrors.setErrorAttribute(key: 'payment_method'),
          ...formErrors.setErrorAttribute(key: 'amount'),
          ...formErrors.setErrorAttribute(key: 'coupon_code'),
          ...formErrors.setErrorAttribute(key: 'currency'),
        };

        FroggyLogger.warning('Form errors: $formattedErrors');

        return Left(
          formErrors.copyWith(
            formValidationErrors: formattedErrors,
          ),
        );
      } else {
        FroggyLogger.error('Error Occurred: ${response.successOrErrorMessage}');

        return Left(
          FormValidatedErrorResponse(
            message: response.successOrErrorMessage ?? 'Unknown error',
          ),
        );
      }
    } catch (e) {
      FroggyLogger.error('Failure Occurred: $e');

      return Left(FormValidatedErrorResponse(message: e.toString()));
    }
  }

  /// Verifies a store receipt with the backend
  Future<Either<GenericErrorResponse, GenericResponse<VerificationResponse>>>
      verifyStoreReceipt({
    required String receipt,
    required String productId,
    required String ePaymentLogId,
    required String source,
    required String transactionId,
    bool isRestored = false,
  }) async {
    try {
      final route = source == 'apple'
          ? '/v1/mobile/in-app-purchases/verify/apple'
          : '/v1/mobile/in-app-purchases/verify/google';

      final request = ApiRequest(
        route: route,
        requestType: RequestType.post,
        params: {
          'receipt': receipt,
          'product_id': productId,
          'e_payment_log_id': ePaymentLogId,
          'transaction_id': transactionId,
        },
      );

      final response =
          await ApiClient.getInstance().executeRequest(request: request);

      if (response.isSuccess) {
        final responseData = response.data;
        final verificationResponse =
            VerificationResponse.fromJson(responseData);

        return Right(
          GenericResponse<VerificationResponse>(
            type: 'verification_response',
            attributes: verificationResponse,
          ),
        );
      } else {
        FroggyLogger.error(
          'Verification failed: ${response.successOrErrorMessage}',
        );

        return Left(
          GenericErrorResponse(
            message:
                response.successOrErrorMessage ?? 'Receipt verification failed',
          ),
        );
      }
    } catch (e) {
      FroggyLogger.error('Exception during receipt verification: $e');

      return Left(GenericErrorResponse(message: e.toString()));
    }
  }

  /// Records a failed transaction for customer support to handle
  Future<Either<GenericErrorResponse, GenericResponse<void>>>
      recordFailedTransaction({
    required String productId,
    required String ePaymentLogId,
    required String transactionId,
    required String receiptData,
    required String platform,
    required String attemptedAmount,
    required String currencyCode,
  }) async {
    try {
      final request = ApiRequest(
        route: '/v1/mobile/in-app-purchases/failed-transaction',
        requestType: RequestType.post,
        params: {
          'product_id': productId,
          'transaction_id': transactionId,
          'receipt_data': receiptData,
          'platform': platform,
          'attempted_amount': attemptedAmount,
          'currency_code': currencyCode,
          'e_payment_log_id': ePaymentLogId,
        },
      );

      final response =
          await ApiClient.getInstance().executeRequest(request: request);

      if (response.isSuccess) {
        return Right(
          GenericResponse<void>(
            type: 'failed_transaction_recorded',
          ),
        );
      } else {
        FroggyLogger.error(
          'Failed to record transaction: ${response.successOrErrorMessage}',
        );

        return Left(
          GenericErrorResponse(
            message: response.successOrErrorMessage ??
                'Failed to record failed transaction',
          ),
        );
      }
    } catch (e) {
      FroggyLogger.error('Exception recording failed transaction: $e');

      return Left(GenericErrorResponse(message: e.toString()));
    }
  }

  /// Credits a user's account after purchase is verified
  ///
  /// If isRestored is true, this indicates the transaction is from a restored
  /// purchase and may require different handling by the backend
  Future<Either<GenericErrorResponse, GenericResponse<CreditResponse>>>
      creditUserAccount({
    required String amount,
    required String productId,
    required String transactionId,
    required String currencyCode,
    required String ePaymentLogId,
    bool isRestored = false,
  }) async {
    try {
      final request = ApiRequest(
        route: '/v1/mobile/in-app-purchases/credit',
        requestType: RequestType.post,
        params: {
          'amount': amount,
          'product_id': productId,
          'transaction_id': transactionId,
          'currency_code': currencyCode,
          'is_restored': isRestored,
          'e_payment_log_id': ePaymentLogId,
        },
      );

      final response =
          await ApiClient.getInstance().executeRequest(request: request);

      if (response.isSuccess) {
        final responseData = response.data;
        final creditResponse = CreditResponse.fromJson(responseData);

        return Right(
          GenericResponse<CreditResponse>(
            type: 'credit_response',
            attributes: creditResponse,
          ),
        );
      } else {
        FroggyLogger.error(
          'Failed to credit account: ${response.successOrErrorMessage}',
        );

        return Left(
          GenericErrorResponse(
            message:
                response.successOrErrorMessage ?? 'Failed to credit account',
          ),
        );
      }
    } catch (e) {
      FroggyLogger.error('Exception crediting account: $e');

      return Left(GenericErrorResponse(message: e.toString()));
    }
  }

  /// Restores purchases from the app store (iOS) or Google Play (Android)
  ///
  /// This method queries the platform's app store for previous purchases and
  /// initiates verification with the backend. It returns either success or
  /// an error response.
  Future<Either<GenericErrorResponse, GenericResponse<void>>>
      restorePurchases() async {
    try {
      FroggyLogger.info('Starting purchase restoration process');

      // This will query the store and return previously purchased products
      // The results will come through the purchaseStream that's being
      // listened to in the CheckoutBloc constructor
      // Note: restorePurchases() doesn't actually return success/failure
      // status
      // It just initiates the restoration process and results come via
      // the purchaseStream
      await InAppPurchase.instance.restorePurchases();

      // Restoration request was successfully initiated
      // The actual results will be delivered through the purchase stream
      return Right(
        GenericResponse<void>(
          type: 'purchases_restoration_initiated',
          // message: 'Purchase restoration initiated successfully.'
          // 'Processing results...',
        ),
      );
    } catch (e) {
      FroggyLogger.error('Exception during purchase restoration: $e');

      return Left(GenericErrorResponse(message: e.toString()));
    }
  }
}
