import 'dart:io';

import 'package:data/data.dart';
import 'package:either_dart/either.dart';
import 'package:froggytalk/buy_credit/buy_credit.dart';
import 'package:http/http.dart';
import 'package:utils/utils.dart';

class BuyCreditOptionsRepository {
  Future<
          Either<GenericErrorResponse,
              PaginatedResponse<List<GenericResponse<BuyCreditResponse>>>>>
      execute() async {
    try {
      final request = ApiRequest(
        route: '/v1/mobile/preset-purchase-credits',
        requestType: RequestType.get,
      );

      final response =
          await ApiClient.getInstance().executeRequest(request: request);

      if (response.isSuccess) {
        final data = response.collection<dynamic>();
        final paginatedResponse =
            PaginatedResponse<List<GenericResponse<BuyCreditResponse>>>(
          status: response.statusToString,
          message: response.successOrErrorMessage,
          data: data
              .map(
                (p01) => GenericResponse<BuyCreditResponse>.fromJson(
                    p01 as Map<String, dynamic>, (json) {
                  final mappedData = json as Map<String, dynamic>?;
                  return BuyCreditResponse.fromJson(
                    mappedData ?? {},
                  ).copyWith(id: json?['id'] as String?);
                }),
              )
              .toList(),
        );

        FroggyLogger.info('Response: $paginatedResponse');

        return Right(paginatedResponse);
      } else {
        FroggyLogger.error('Error Occurred: ${response.successOrErrorMessage}');

        return Left(
          GenericErrorResponse(
            message: response.successOrErrorMessage ?? 'Unknown error',
          ),
        );
      }
    } on ClientException catch (e) {
      // high level exception
      return Left(
        GenericErrorResponse(
          code: 'internet-out',
          message: 'Malformed Request: ${e.message}',
        ),
      );
    } on SocketException catch (e) {
      // low level exception
      return Left(
        GenericErrorResponse(
          code: 'internet-out',
          message: 'No internet connection: ${e.message}',
        ),
      );
    } catch (e) {
      FroggyLogger.error('Failure Occurred: $e');

      return Left(GenericErrorResponse(message: e.toString()));
    }
  }
}
