import 'package:data/data.dart';
import 'package:either_dart/either.dart';
import 'package:froggytalk/buy_credit/buy_credit.dart';
import 'package:utils/utils.dart';

class AddToCartRepository {
  Future<
      Either<FormValidatedErrorResponse,
          PaginatedResponse<CheckoutSessionResponse>>> execute({
    required String amount,
    required String currencyCode,
    String paymentMethod = 'card',
    String? couponCode,
    String? sessionId,
    bool isAutoRechargeable = false,
  }) async {
    try {
      final body = CheckoutSessionRequest(
        amount: amount,
        paymentMethod: paymentMethod,
        couponCode:
            couponCode == null || couponCode.isEmpty ? null : couponCode,
        currencyCode: currencyCode,
        enableAutoRecharge: isAutoRechargeable,
        sessionId: sessionId,
      );

      // FroggyLogger.info('Add to Cart Request: ${body.toJson()}');

      final request = ApiRequest(
        route: '/v1/mobile/buy-credit',
        requestType: RequestType.post,
        params: PaginatedRequest<CheckoutSessionRequest>(body: body).toJson(
          (p0) => p0.toJson(),
        ),
      );

      final response =
          await ApiClient.getInstance().executeRequest(request: request);

      if (response.isSuccess) {
        final data = response.data;

        final paymentDetails = data['payment_details'] as Map<String, dynamic>;

        final fromJson = CheckoutSessionResponse.fromJson(
          paymentDetails['attributes'] as Map<String, dynamic>,
        ).copyWith(id: paymentDetails['id'] as String?);

        final paginatedResponse = PaginatedResponse<CheckoutSessionResponse>(
          status: response.statusToString,
          message: response.successOrErrorMessage,
          data: fromJson,
        );

        FroggyLogger.info('Response: $paginatedResponse');

        return Right(paginatedResponse);
      } else if (response.hasFormErrors) {
        final formErrors = FormValidatedErrorResponse.fromJson(response.data);

        // final formattedErrors = {
        //   ...formErrors.setErrorAttribute(key: 'payment_method'),
        //   // ...formErrors.setErrorAttribute(key: 'amount'),
        //   ...formErrors.setErrorAttribute(key: 'coupon_code'),
        //   // ...formErrors.setErrorAttribute(key: 'currency'),
        // };

        final message =
            (response.fromJson['message'] as List<dynamic>)[0] as String;
        final value = formErrors.copyWith(
          message: message,
          // formValidationErrors: formattedErrors,
        );
        FroggyLogger.warning('Form errors: ${value.toJson()}');
        return Left(value);
      } else {
        final message =
            (response.fromJson['message'] as List<dynamic>)[0] as String;
        FroggyLogger.error('Error Occurred: $message');

        return Left(
          FormValidatedErrorResponse(
            code: 'error-occurred',
            message: message,
          ),
        );
      }
    } catch (e) {
      FroggyLogger.error('Failure Occurred: $e');

      return Left(
        FormValidatedErrorResponse(
          code: 'error-occurred',
          message: e.toString(),
        ),
      );
    }
  }
}
