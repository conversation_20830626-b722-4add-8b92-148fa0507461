import 'dart:convert';

import 'package:http/http.dart' as http;
import 'package:in_app_purchase/in_app_purchase.dart';

/// Repository for store operations
class StoreRepository {
  /// Creates a StoreRepository
  StoreRepository({
    required this.apiUrl,
    this.authToken,
  });

  /// Backend API URL for verifying purchases
  final String apiUrl;

  /// Authentication token for API calls
  final String? authToken;

  /// Verify purchase with backend server
  Future<bool> verifyPurchase({
    required String productId,
    required String purchaseId,
    required PurchaseVerificationData verificationData,
    required bool isAndroid,
    required bool isIOS,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$apiUrl/api/v1/verify-purchase'),
        headers: {
          'Content-Type': 'application/json',
          if (authToken != null) 'Authorization': 'Bearer $authToken',
        },
        body: jsonEncode({
          'productId': productId,
          'purchaseId': purchaseId,
          'platform': isAndroid ? 'android' : (isIOS ? 'ios' : 'unknown'),
          'serverVerificationData': verificationData.serverVerificationData,
          'source': verificationData.source,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        return data['isValid'] == true;
      }

      return false;
    } catch (e) {
      throw Exception('Failed to verify purchase: $e');
    }
  }

  /// Fetch purchase history from backend
  Future<List<Map<String, dynamic>>> fetchPurchaseHistory() async {
    try {
      final response = await http.get(
        Uri.parse('$apiUrl/api/v1/purchase-history'),
        headers: {
          if (authToken != null) 'Authorization': 'Bearer $authToken',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as List;
        return data.cast<Map<String, dynamic>>();
      }

      return [];
    } catch (e) {
      throw Exception('Failed to fetch purchase history: $e');
    }
  }
}
