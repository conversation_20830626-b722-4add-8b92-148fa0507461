import 'package:common/common.dart';
import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

class EnterCustomAmount extends HookWidget {
  const EnterCustomAmount({super.key});

  @override
  Widget build(BuildContext context) {
    final isSelected = useState(2);
    final isCustomAmount = useState<bool>(false);
    final isCustomAmountCheckboxVisible = useState<bool>(false);
    final customAmountController = useTextEditingController();

    return Column(
      children: [
        if (isCustomAmountCheckboxVisible.value)
          Container(
            margin: const EdgeInsets.only(
              top: 10,
              left: 10,
              right: 10,
            ),
            child: FroggyCheckbox(
              isChecked: isCustomAmount.value,
              onCheckboxChanged: (value) {
                if (value == null) return;

                isCustomAmount.value = !value;

                if (!value) {
                  isSelected.value = 9999;
                } else {
                  isSelected.value = 0;
                }
              },
              label: 'Enter custom amount',
            ),
          ),
        if (isCustomAmount.value == true)
          Container(
            margin: const EdgeInsets.symmetric(vertical: 10),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 3),
            decoration: BoxDecoration(
              color: FroggyColors.froggyGrey5,
              borderRadius: BorderRadius.circular(25),
            ),
            child: TextField(
              controller: customAmountController,
              keyboardType: const TextInputType.numberWithOptions(
                decimal: true,
              ),
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(
                  4,
                ), // Adjust length as per requirement
              ],
              style: const TextStyle(
                color: FroggyColors.black,
              ),
              decoration: const InputDecoration(
                border: InputBorder.none,
                hintText: r'min $50 ~ max $500',
                hintStyle: TextStyle(color: Colors.black38),
                focusedBorder: InputBorder.none,
              ),
            ),
          ),
        const Spacer(),
      ],
    );
  }
}
