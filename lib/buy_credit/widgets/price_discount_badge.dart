import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/svg.dart';
import 'package:froggytalk/l10n/l10n.dart';

class PriceDiscountBadge extends HookWidget {
  const PriceDiscountBadge({
    this.percentageAdded,
    this.fontSize = 15,
    this.fontWeight = FontWeight.w700,
    this.size = 80,
    this.isInverted = false,
    this.isRecommended = false,
    super.key,
  });

  final int? percentageAdded;
  final double fontSize;
  final FontWeight fontWeight;
  final double size;
  final bool isInverted;
  final bool isRecommended;

  @override
  Widget build(BuildContext context) {
    final formattedPercentageDiscount = useMemoized<int>(() {
      return percentageAdded ?? 0;
    });
    final l10n = useLocale();

    return Row(
      children: [
        if (isRecommended)
          Text(
            l10n.buyCreditAmountRecommended,
            style: TextStyle(
              color: !isInverted ? FroggyColors.black : Colors.white,
              fontSize: fontSize + 5,
              fontWeight: fontWeight,
              height: 0,
              letterSpacing: -0.14,
            ),
          ),
        if (formattedPercentageDiscount > 0)
          SizedBox(
            height: size,
            width: size,
            child: Stack(
              alignment: Alignment.center,
              children: [
                Positioned.fill(
                  child: SvgPicture.asset(
                    'packages/froggy_icons/icons/ic_badge.svg', // Path to your SVG file
                    fit: BoxFit.cover, //
                    colorFilter: isInverted == false
                        ? null
                        : ColorFilter.mode(
                            !isInverted ? FroggyColors.primary : Colors.white,
                            BlendMode.srcIn,
                          ),
                  ),
                ),
                Text(
                  '$formattedPercentageDiscount%',
                  style: TextStyle(
                    color: isInverted ? FroggyColors.primary : Colors.white,
                    fontSize: fontSize,
                    fontWeight: fontWeight,
                    height: 0,
                    letterSpacing: -0.14,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }
}
