import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:formz/formz.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:froggytalk/buy_credit/buy_credit.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:utils/utils.dart';

/// A component that displays the payment summary details.
///
/// This widget shows a breakdown of the payment including:
/// - Amount to be credited
/// - Fees and taxes
/// - Applied discounts
/// - Total payment amount
///
/// For in-app purchases (IAP), it displays the store-formatted price along
/// with a calculated breakdown of what the user will receive.
class PaymentSummaryComponent extends HookWidget {
  /// Creates an instance of [PaymentSummaryComponent].
  const PaymentSummaryComponent({super.key});

  @override
  Widget build(BuildContext context) {
    // Cache the currency code for better performance
    final currencySymbol = useAuthUserCurrencyCode() ?? 'USD';

    return BlocBuilder<CheckoutBloc, CheckoutState>(
      builder: (context, state) {
        final checkoutSession = state.session;
        final isIapStore = state.paymentMethod.isIapStore;
        final iapProduct = state.iapProductDetails;

        // Use Skeletonizer for loading state
        return Skeletonizer(
          enabled: state.status == FormzSubmissionStatus.inProgress,
          enableSwitchAnimation: true,
          child: isIapStore && iapProduct != null
              ? _buildIapSummary(
                  context,
                  iapProduct,
                  checkoutSession,
                  currencySymbol,
                )
              : _buildRegularSummary(context, checkoutSession, currencySymbol),
        );
      },
    );
  }

  /// Builds the summary view for in-app purchases, showing store pricing
  ///
  /// Displays formatted price from the store along with our calculated total
  /// payment amount with a clear indication this is an in-app purchase
  Widget _buildIapSummary(
    BuildContext context,
    ProductDetails iapProduct,
    CheckoutSessionResponse? checkoutSession,
    String currencySymbol,
  ) {
    final l10n = context.l10n;

    // Cache the container decoration to prevent rebuilds
    final containerDecoration = BoxDecoration(
      color: FroggyColors.froggyLighterGreen,
      borderRadius: BorderRadius.circular(12),
      border: Border.all(
        color: FroggyColors.froggyGreen.withAlpha((255.0 * 0.3).round()),
      ),
    );

    // Get localized strings with fallbacks to constants
    final inAppPurchaseLabel = l10n.inAppPurchaseLabel;
    final storePriceLabel = l10n.storePrice;

    return ListView(
      shrinkWrap: true,
      padding: const EdgeInsets.symmetric(vertical: 10),
      physics: const NeverScrollableScrollPhysics(),
      children: [
        // Store purchase indicator with store name
        Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          margin: const EdgeInsets.only(bottom: 12),
          decoration: containerDecoration,
          child: Row(
            children: [
              const Icon(
                Icons.shopping_bag_outlined,
                size: 20,
                color: FroggyColors.froggyGreen,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  inAppPurchaseLabel,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: FroggyColors.froggyGreen,
                  ),
                ),
              ),
            ],
          ),
        ),

        // Store price
        PaymentSummaryItemComponent(
          label: storePriceLabel,
          value: iapProduct.price,
          isRowBold: true,
        ),

        const Divider(height: 16),

        // Amount to be credited
        PaymentSummaryItemComponent(
          label: l10n.paymentSummaryAmountToCreditLabel,
          value: iapProduct.price,
        ),

        // Total from our calculation
        PaymentSummaryItemComponent(
          label: l10n.paymentSummaryTotalLabel,
          value: iapProduct.price,
          isRowBold: true,
        ),
      ],
    );
  }

  /// Builds the summary view for regular (non-IAP) payments
  ///
  /// Shows a detailed breakdown of payment including fees, VAT, and discounts
  Widget _buildRegularSummary(
    BuildContext context,
    CheckoutSessionResponse? checkoutSession,
    String currencySymbol,
  ) {
    final l10n = context.l10n;

    // Fallbacks for null safety
    final amountToBeCredited = (checkoutSession?.amountToBeCredited ?? '0')
        .toCurrency(context, symbol: currencySymbol);
    // final vatPlusFees = (checkoutSession?.vatPlusFees ?? '0')
    //     .toCurrency(context, symbol: currencySymbol);
    final discount = (checkoutSession?.discount ?? '0')
        .toCurrency(context, symbol: currencySymbol);
    final totalPayment = (checkoutSession?.totalPayment ?? '0')
        .toCurrency(context, symbol: currencySymbol);

    return ListView(
      shrinkWrap: true,
      padding: const EdgeInsets.symmetric(vertical: 10),
      physics: const NeverScrollableScrollPhysics(),
      children: [
        PaymentSummaryItemComponent(
          label: l10n.paymentSummaryAmountToCreditLabel,
          value: amountToBeCredited,
        ),
        PaymentSummaryItemComponent(
          label: l10n.paymentSummaryVatFeesLabel,
          value: (checkoutSession?.vatPlusFees ?? '0')
              .toCurrency(context, symbol: currencySymbol),
        ),
        PaymentSummaryItemComponent(
          label: l10n.paymentSummaryDiscountLabel,
          value: discount,
        ),
        PaymentSummaryItemComponent(
          label: l10n.paymentSummaryTotalLabel,
          value: totalPayment,
          isRowBold: true,
        ),
      ],
    );
  }
}

/// A component that displays a single line item in the payment summary.
///
/// This widget shows a label and value pair used in the payment summary
/// breakdown.
/// It's optimized for performance with constant constructors and cached
/// styling.
class PaymentSummaryItemComponent extends StatelessWidget {
  /// Creates an instance of [PaymentSummaryItemComponent].
  ///
  /// [label] is the description text for the payment item.
  /// [value] is the monetary amount for the payment item.
  /// [isRowBold] determines if the text should be displayed in bold.
  const PaymentSummaryItemComponent({
    required this.label,
    required this.value,
    super.key,
    this.isRowBold = false,
  });

  /// The descriptive label for this payment item.
  final String label;

  /// The monetary value of this payment item.
  final String value;

  /// Whether to display this row in bold text.
  /// Typically used for the total amount.
  final bool isRowBold;

  @override
  Widget build(BuildContext context) {
    // Derive text styles once per build to avoid creating
    // multiple style objects
    final textStyle = TextStyle(
      fontSize: 12,
      fontWeight: isRowBold ? FontWeight.w700 : FontWeight.w400,
      height: 0,
      letterSpacing: -0.29,
    );

    return ListTile(
      horizontalTitleGap: 0,
      contentPadding: EdgeInsets.zero,
      minVerticalPadding: 0,
      minTileHeight: 30,
      title: Text(
        label,
        style: textStyle,
      ),
      trailing: Text(
        value,
        textAlign: TextAlign.right,
        style: textStyle,
      ),
    );
  }
}
