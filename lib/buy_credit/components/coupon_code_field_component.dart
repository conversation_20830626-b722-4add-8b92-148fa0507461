import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/buy_credit/buy_credit.dart';
import 'package:froggytalk/l10n/l10n.dart';

/// A component that displays a coupon code input field with apply button.
///
/// This widget allows users to enter and apply coupon codes during checkout,
/// with validation and feedback on coupon validity.
class CouponCodeFieldComponent extends HookWidget {
  /// Creates an instance of [CouponCodeFieldComponent].
  const CouponCodeFieldComponent({super.key});

  @override
  Widget build(BuildContext context) {
    final focusNode = useFocusNode();
    final defaultCouponValue = context
        .select<CheckoutBloc, String?>((state) => state.state.couponCode.value);
    final couponController = useTextEditingController(text: defaultCouponValue);
    final bloc = context.read<CheckoutBloc>();

    final onApplyCouponButtonPressed = useCallback(
      () {
        bloc.add(const CheckoutEvent.couponApplied());
        focusNode.unfocus();
      },
      [bloc],
    );

    useEffect(
      () {
        void onCouponChanged() {
          bloc.add(
            CheckoutEvent.couponCodeUpdated(
              value: couponController.text,
            ),
          );
        }

        couponController.addListener(onCouponChanged);

        return () {
          couponController.removeListener(onCouponChanged);
        };
      },
      [bloc, couponController],
    );

    return BlocBuilder<CheckoutBloc, CheckoutState>(
      builder: (context, state) {
        final couponStatus = state.couponStatus;

        if (state.paymentMethod.isIapStore) {
          return const SizedBox.shrink();
        }

        if (couponStatus.isReadonly && state.couponCode.value.isEmpty) {
          return const SizedBox.shrink();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCouponField(
              couponController,
              focusNode,
              couponStatus,
              state,
              context,
              onApplyCouponButtonPressed,
            ),
            BlocSelector<CheckoutBloc, CheckoutState, String?>(
              selector: (state) {
                if (state.couponMessage != null) {
                  return state.couponMessage;
                }

                return state.couponCode.displayError
                    ?.getLocalizedErrorMessage(context);
              },
              builder: (context, errorMessage) {
                if (errorMessage == null) {
                  return const SizedBox.shrink();
                }

                return Container(
                  margin:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 1),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 5, vertical: 1),
                  child: Text(
                    errorMessage,
                    style: const TextStyle(
                      color: FroggyColors.error,
                      fontSize: 11,
                    ),
                    textAlign: TextAlign.center,
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }

  /// Builds the coupon input field with apply button
  ///
  /// This method creates a styled text field with an apply button that
  /// changes state based on whether the coupon has been applied.
  Container _buildCouponField(
    TextEditingController couponController,
    FocusNode focusNode,
    CouponCodeInputStatus couponStatus,
    CheckoutState state,
    BuildContext context,
    VoidCallback onApplyCouponButtonPressed,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 3),
      decoration: BoxDecoration(
        color: FroggyColors.froggyGrey5,
        borderRadius: BorderRadius.circular(25),
      ),
      child: TextField(
        controller: couponController,
        focusNode: focusNode,
        keyboardType: TextInputType.text,
        style: const TextStyle(
          color: FroggyColors.black,
        ),
        inputFormatters: [
          FilteringTextInputFormatter.allow(
            RegExp('[a-zA-Z0-9]'),
          ),
          LengthLimitingTextInputFormatter(10),
        ],
        decoration: InputDecoration(
          enabled:
              !(couponStatus.isReadonly && state.couponCode.value.isNotEmpty),
          border: InputBorder.none,
          hintText: context.l10n.enterCouponOptionalPlaceholder,
          hintStyle: const TextStyle(color: Colors.black38),
          focusedBorder: InputBorder.none,
          suffixIconConstraints: const BoxConstraints(
            maxWidth: 150,
            maxHeight: 40,
          ),
          suffixIcon: Builder(
            builder: (context) {
              return ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: FroggyColors.froggyBlack,
                  elevation: 0,
                ),
                onPressed: couponStatus.isReadonly
                    ? null
                    : (couponStatus.isApplied
                        ? null
                        : onApplyCouponButtonPressed),
                child: Text(
                  couponStatus.isApplied || couponStatus.isReadonly
                      ? context.l10n.couponAppliedButtonText
                      : context.l10n.couponApplyButtonText,
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
