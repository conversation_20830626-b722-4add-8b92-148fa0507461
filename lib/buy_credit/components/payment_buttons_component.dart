import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/app/hooks/use_settings.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:froggytalk/buy_credit/buy_credit.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:utils/utils.dart';

class PaymentButtonsComponent extends HookWidget {
  const PaymentButtonsComponent({super.key});

  @override
  Widget build(BuildContext context) {
    final selectedPaymentOption = useState(0);
    final bloc = context.read<CheckoutBloc>();
    final onPaymentOptionsSelected = useCallback((int value) {
      selectedPaymentOption.value = value;

      switch (value) {
        case 1:
          if (isIOS()) {
            bloc.add(
              const CheckoutEvent.paymentMethodUpdated(
                purchaseMethod: BuyCreditPaymentMethods.applePay,
              ),
            );
          } else {
            bloc.add(
              const CheckoutEvent.paymentMethodUpdated(
                purchaseMethod: BuyCreditPaymentMethods.googlePay,
              ),
            );
          }

          bloc.add(
            const CheckoutEvent.checkoutRefreshed(),
          );
          return;
        default:
          bloc
            ..add(
              const CheckoutEvent.paymentMethodUpdated(),
            )
            ..add(
              const CheckoutEvent.checkoutRefreshed(),
            );
          return;
      }
    });

    final playstorePercentage =
        useSettings().getSetting<int?>('android_iap_percentage') ?? 15;
    final hideAppstorePay =
        useSettings().getSetting<bool>('show_inapp_purchase_ios') ?? true;
    final hidePlaystorePay =
        useSettings().getSetting<bool>('show_inapp_purchase_android') ?? false;
    final hideIosStripePay =
        useSettings().getSetting<bool>('show_purchase_card_ios') ?? true;
    final hideAndroidStripePay =
        useSettings().getSetting<bool>('show_purchase_card_android') ?? true;
    final currencySymbol = useAuthUserCurrencyCode() ?? 'USD';

    useEffect(
      () {
        // Automatically select Google/Apple Pay if Stripe/Card is hidden
        if (!hideIosStripePay) {
          selectedPaymentOption.value = 1;
          onPaymentOptionsSelected(1);
        }

        return null;
      },
      [],
    );

    return BlocBuilder<CheckoutBloc, CheckoutState>(
      builder: (context, state) {
        return Column(
          children: [
            Builder(
              builder: (context) {
                // Show Stripe/Card payment option based on platform and settings
                final shouldShowStripePay = (isIOS() && !hideIosStripePay) ||
                    (isAndroid() && !hideAndroidStripePay);

                if (shouldShowStripePay) {
                  return const SizedBox.shrink();
                }

                return Container(
                  margin:
                      const EdgeInsets.symmetric(horizontal: 15, vertical: 5),
                  child: ListTile(
                    title:
                        Image.asset('assets/images/pay_with_credit_card.png'),
                    minLeadingWidth: 50,
                    contentPadding: EdgeInsets.zero,
                    minVerticalPadding: 0,
                    tileColor: FroggyColors.froggyLighterGreen,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(50),
                    ),
                    horizontalTitleGap: 0,
                    onTap: () {
                      onPaymentOptionsSelected(0);
                    },
                    leading: Radio(
                      value: 0,
                      groupValue: selectedPaymentOption.value,
                      onChanged: (value) {
                        onPaymentOptionsSelected(0);
                      },
                    ),
                  ),
                );
              },
            ),
            if (isIOS() && state.isStoreAvailable)
              Builder(
                builder: (context) {
                  if (!hideAppstorePay) {
                    return const SizedBox.shrink();
                  }

                  final appStoreVatPrice =
                      state.buyCreditOption?.appStoreVatPrice;

                  return ApplePayButton(
                    context.l10n.payWithApplePay,
                    context.l10n
                        .appStoreCost(
                          appStoreVatPrice.toString().toCurrency(
                                context,
                                symbol: currencySymbol,
                              ),
                        )
                        .replaceAll('%', ''),
                    () => onPaymentOptionsSelected(1),
                    selectedPaymentOption.value == 1,
                  );
                },
              ),
            if (isAndroid() && state.isStoreAvailable)
              Builder(
                builder: (context) {
                  if (!hidePlaystorePay) {
                    return const SizedBox.shrink();
                  }

                  return GooglePayButton(
                    context.l10n.payWithGooglePay,
                    context.l10n.paymentSummaryPlayStoreFeeLabel(
                      playstorePercentage.toString(),
                    ),
                    () => onPaymentOptionsSelected(1),
                    selectedPaymentOption.value == 1,
                  );
                },
              ),
          ],
        );
      },
    );
  }
}

/// ApplePayButton displays an Apple Pay payment option with selection state.
/// Mirrors the GooglePayButton for UI consistency and localization support.
///
/// [title] - Localized title for Apple Pay.
/// [subtitle] - Localized subtitle or fee label.
/// [onPressed] - Callback when the button is tapped or selected.
/// [isSelected] - Whether this payment option is currently selected.
class ApplePayButton extends HookWidget {
  const ApplePayButton(
    this.title,
    this.subtitle,
    this.onPressed,
    this.isSelected, {
    super.key,
  });

  final String title;
  final String subtitle;
  final VoidCallback? onPressed;
  final bool isSelected;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 5),
        child: ListTile(
          title: Row(
            children: [
              Container(
                width: 25,
                height: 25,
                margin: const EdgeInsets.only(right: 4),
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage('assets/images/image_11.png'),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: Text(
                  title,
                  style: const TextStyle(
                    color: Color(0xFF161616),
                    fontSize: 13,
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w600,
                    letterSpacing: -0.34,
                  ),
                ),
              ),
              Text(
                subtitle,
                style: const TextStyle(
                  color: Color(0xFF167047),
                  fontSize: 8,
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w500,
                  letterSpacing: -0.24,
                ),
              ),
              const Spacer(),
            ],
          ),
          minLeadingWidth: 50,
          contentPadding: EdgeInsets.zero,
          minVerticalPadding: 0,
          tileColor: const Color(0xFFDCECE5),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
          leading: Radio(
            value: 1,
            groupValue: isSelected ? 1 : 0,
            onChanged: (_) => onPressed?.call(),
          ),
        ),
      ),
    );
  }
}

class GooglePayButton extends HookWidget {
  const GooglePayButton(
    this.title,
    this.subtitle,
    this.onPressed,
    this.isSelected, {
    super.key,
  });

  final String title;
  final String subtitle;
  final VoidCallback? onPressed;
  final bool isSelected;

  @override
  Widget build(BuildContext context) {
    final playstorePercentage =
        useSettings().getSetting<int?>('android_iap_percentage') ?? 15;

    return Builder(
      builder: (context) {
        return InkWell(
          onTap: onPressed,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 5),
            child: ListTile(
              title: Row(
                children: [
                  Container(
                    width: 25,
                    height: 25,
                    margin: const EdgeInsets.only(right: 4),
                    decoration: const BoxDecoration(
                      image: DecorationImage(
                        image: AssetImage('assets/images/google_pay_icon.png'),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 10, right: 5),
                    child: Text(
                      AppLocalizations.of(context).googlePay,
                      style: const TextStyle(
                        color: Color(0xFF161616),
                        fontSize: 15,
                        fontFamily: 'Inter',
                        fontWeight: FontWeight.w600,
                        letterSpacing: -0.34,
                      ),
                    ),
                  ),
                  Text(
                    context.l10n.paymentSummaryPlayStoreFeeLabel(
                      playstorePercentage.toString(),
                    ),
                    style: const TextStyle(
                      color: Color(0xFF167047),
                      fontSize: 12,
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w500,
                      letterSpacing: -0.24,
                    ),
                  ),
                  const Spacer(),
                ],
              ),
              minLeadingWidth: 50,
              contentPadding: EdgeInsets.zero,
              minVerticalPadding: 0,
              tileColor: const Color(0xFFDCECE5),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(24),
              ),
              leading: Radio(
                value: 1,
                groupValue: isSelected ? 1 : 0,
                onChanged: (_) => onPressed?.call(),
              ),
            ),
          ),
        );
      },
    );
  }
}
