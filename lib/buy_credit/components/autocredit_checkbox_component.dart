import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggy_icons/froggy_icons.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:froggytalk/buy_credit/buy_credit.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:utils/utils.dart';

/// A component that displays a checkbox to enable/disable automatic credit refill.
///
/// This widget allows users to toggle the auto-credit feature which
/// automatically credits their account when balance falls below a certain
/// threshold to prevent call interruptions.
class AutocreditCheckboxComponent extends HookWidget {
  /// Creates an instance of [AutocreditCheckboxComponent].
  ///
  /// The [onAutoCreditButtonPressed] callback is required and is called when
  /// the user toggles the auto-credit feature.
  const AutocreditCheckboxComponent({
    required this.onAutoCreditButtonPressed,
    super.key,
  });

  /// Callback to be called when the auto-credit toggle button is pressed.
  final VoidCallback onAutoCreditButtonPressed;

  @override
  Widget build(BuildContext context) {
    final currencySymbol = useAuthUserCurrencyCode() ?? 'USD';
    final l10n = useLocale();
    final minAmount =
        useSettings().getSetting<double>('min_autocredit_amount') ?? 1;
    final showAutocreditCheckbox =
        useSettings().getSetting<bool>('show_autocredit_checkbox') ?? false;

    return Builder(
      builder: (context) {
        if (!showAutocreditCheckbox) {
          return const SizedBox.shrink();
        }
        
        return BlocBuilder<CheckoutBloc, CheckoutState>(
          builder: (context, state) {
            if (state.paymentMethod.isOthers) {
              final checkoutSession = state.session;
              final amountToBeCredited =
                  (checkoutSession?.amountToBeCredited ?? '0').toCurrency(
                context,
                symbol: currencySymbol,
              );
        
              return Container(
                margin: const EdgeInsets.symmetric(
                  horizontal: 15,
                  vertical: 5,
                ),
                child: ListTile(
                  title: Text(
                    l10n.activateAutoCredit,
                    style: const TextStyle(
                      fontSize: 14,
                    ),
                  ),
                  subtitle: Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                          text: '$amountToBeCredited ',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        TextSpan(
                          text: l10n.autoAccountCreditHelp,
                        ),
                        TextSpan(
                          text: ' ${minAmount.toString().toCurrency(
                                context,
                                symbol: currencySymbol,
                              )}',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    style: const TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: FroggyColors.froggyGrey2,
                    ),
                  ),
                  subtitleTextStyle: const TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: FroggyColors.froggyGrey2,
                  ),
                  tileColor: FroggyColors.froggyCream,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(50),
                  ),
                  onTap: onAutoCreditButtonPressed,
                  trailing: Builder(
                    builder: (context) {
                      const size = 35.0;
        
                      return Visibility(
                        visible: !state.autoCredit,
                        replacement: FroggyIconsList.toggleOn.toWidget(
                          height: size,
                        ),
                        child: FroggyIconsList.toggleOff.toWidget(
                          height: size,
                        ),
                      );
                    },
                  ),
                ),
              );
            }
        
            return const SizedBox.shrink();
          },
        );
      },
    );
  }
}
