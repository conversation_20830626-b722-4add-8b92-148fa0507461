part of 'checkout_bloc.dart';

@freezed
class CheckoutState with _$CheckoutState, FormzMixin {
  factory CheckoutState({
    CheckoutSessionResponse? session,
    String? checkoutUrl,
    @Default(CouponCodeInput.pure()) CouponCodeInput couponCode,
    @Default(false) bool autoCredit,
    @Default(false) bool isStoreAvailable,
    BuyCreditResponse? buyCreditOption,
    @Default('USD') String currencyCode,
    @Default(BuyCreditPaymentMethods.others)
    BuyCreditPaymentMethods paymentMethod,
    @Default(FormzSubmissionStatus.initial) FormzSubmissionStatus status,
    @Default(PaymentSheetStatus.initial) PaymentSheetStatus paymentSheetStatus,
    @Default(CouponCodeInputStatus.initial) CouponCodeInputStatus couponStatus,
    String? message,
    String? couponMessage,
    ProductDetails? iapProductDetails,
  }) = _CheckoutState;

  factory CheckoutState.initial() => CheckoutState();

  CheckoutState._();

  CheckoutState reset() {
    return copyWith(
      checkoutUrl: null,
      // buyCreditOption: null,
      // autoCredit: false,
      couponCode: const CouponCodeInput.pure(),
      couponMessage: null,
      couponStatus: CouponCodeInputStatus.initial,
      // message: null,
      paymentSheetStatus: PaymentSheetStatus.initial,
      iapProductDetails: null,
      // autoCredit: false,
      isStoreAvailable: false,
      paymentMethod: BuyCreditPaymentMethods.others,
      // status: FormzSubmissionStatus.initial,
      // session: null,
      // status: FormzSubmissionStatus.initial,
    );
  }

  @override
  List<FormzInput<String, dynamic>> get inputs => [couponCode];
}
