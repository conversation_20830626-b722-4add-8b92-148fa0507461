// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'checkout_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CheckoutEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkoutInitiated,
    required TResult Function() checkoutReset,
    required TResult Function() checkoutRefreshed,
    required TResult Function(UserAttributesModel user) cardPaymentProcessed,
    required TResult Function() applePayPaymentProcessed,
    required TResult Function() googlePayPaymentProcessed,
    required TResult Function(String? value, CouponCodeInputStatus status)
        couponCodeUpdated,
    required TResult Function() couponApplied,
    required TResult Function(bool value) automaticCreditToggled,
    required TResult Function(String value) currencyCodeUpdated,
    required TResult Function(BuyCreditPaymentMethods purchaseMethod)
        paymentMethodUpdated,
    required TResult Function(BuyCreditResponse buyCreditOption)
        productAddedToCart,
    required TResult Function(BuyCreditResponse buyCreditOption,
            String currencyCode, String? couponCode)
        discountedCheckoutStarted,
    required TResult Function(String storeProductId) iapDetailsRefreshed,
    required TResult Function(List<PurchaseDetails> purchaseDetailsList)
        purchaseDetailsUpdated,
    required TResult Function() restorePurchases,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkoutInitiated,
    TResult? Function()? checkoutReset,
    TResult? Function()? checkoutRefreshed,
    TResult? Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult? Function()? applePayPaymentProcessed,
    TResult? Function()? googlePayPaymentProcessed,
    TResult? Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult? Function()? couponApplied,
    TResult? Function(bool value)? automaticCreditToggled,
    TResult? Function(String value)? currencyCodeUpdated,
    TResult? Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult? Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult? Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult? Function(String storeProductId)? iapDetailsRefreshed,
    TResult? Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult? Function()? restorePurchases,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkoutInitiated,
    TResult Function()? checkoutReset,
    TResult Function()? checkoutRefreshed,
    TResult Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult Function()? applePayPaymentProcessed,
    TResult Function()? googlePayPaymentProcessed,
    TResult Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult Function()? couponApplied,
    TResult Function(bool value)? automaticCreditToggled,
    TResult Function(String value)? currencyCodeUpdated,
    TResult Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult Function(String storeProductId)? iapDetailsRefreshed,
    TResult Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult Function()? restorePurchases,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CheckoutInitiated value) checkoutInitiated,
    required TResult Function(_CheckoutReset value) checkoutReset,
    required TResult Function(_CheckoutRefreshed value) checkoutRefreshed,
    required TResult Function(_CardPaymentProcessed value) cardPaymentProcessed,
    required TResult Function(_ApplePayPaymentProcessed value)
        applePayPaymentProcessed,
    required TResult Function(_GooglePayPaymentProcessed value)
        googlePayPaymentProcessed,
    required TResult Function(_CouponCodeUpdated value) couponCodeUpdated,
    required TResult Function(_CouponApplied value) couponApplied,
    required TResult Function(_AutomaticCreditToggled value)
        automaticCreditToggled,
    required TResult Function(_CurrencyCodeUpdated value) currencyCodeUpdated,
    required TResult Function(_PaymentMethodUpdated value) paymentMethodUpdated,
    required TResult Function(_ProductAddedToCart value) productAddedToCart,
    required TResult Function(_DiscountedCheckoutStarted value)
        discountedCheckoutStarted,
    required TResult Function(_IapDetailsRefreshed value) iapDetailsRefreshed,
    required TResult Function(_PurchaseDetailsUpdated value)
        purchaseDetailsUpdated,
    required TResult Function(_RestorePurchases value) restorePurchases,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult? Function(_CheckoutReset value)? checkoutReset,
    TResult? Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult? Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult? Function(_ApplePayPaymentProcessed value)?
        applePayPaymentProcessed,
    TResult? Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult? Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult? Function(_CouponApplied value)? couponApplied,
    TResult? Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult? Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult? Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult? Function(_ProductAddedToCart value)? productAddedToCart,
    TResult? Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult? Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult? Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult? Function(_RestorePurchases value)? restorePurchases,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult Function(_CheckoutReset value)? checkoutReset,
    TResult Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult Function(_ApplePayPaymentProcessed value)? applePayPaymentProcessed,
    TResult Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult Function(_CouponApplied value)? couponApplied,
    TResult Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult Function(_ProductAddedToCart value)? productAddedToCart,
    TResult Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult Function(_RestorePurchases value)? restorePurchases,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CheckoutEventCopyWith<$Res> {
  factory $CheckoutEventCopyWith(
          CheckoutEvent value, $Res Function(CheckoutEvent) then) =
      _$CheckoutEventCopyWithImpl<$Res, CheckoutEvent>;
}

/// @nodoc
class _$CheckoutEventCopyWithImpl<$Res, $Val extends CheckoutEvent>
    implements $CheckoutEventCopyWith<$Res> {
  _$CheckoutEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$CheckoutInitiatedImplCopyWith<$Res> {
  factory _$$CheckoutInitiatedImplCopyWith(_$CheckoutInitiatedImpl value,
          $Res Function(_$CheckoutInitiatedImpl) then) =
      __$$CheckoutInitiatedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CheckoutInitiatedImplCopyWithImpl<$Res>
    extends _$CheckoutEventCopyWithImpl<$Res, _$CheckoutInitiatedImpl>
    implements _$$CheckoutInitiatedImplCopyWith<$Res> {
  __$$CheckoutInitiatedImplCopyWithImpl(_$CheckoutInitiatedImpl _value,
      $Res Function(_$CheckoutInitiatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CheckoutInitiatedImpl implements _CheckoutInitiated {
  const _$CheckoutInitiatedImpl();

  @override
  String toString() {
    return 'CheckoutEvent.checkoutInitiated()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CheckoutInitiatedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkoutInitiated,
    required TResult Function() checkoutReset,
    required TResult Function() checkoutRefreshed,
    required TResult Function(UserAttributesModel user) cardPaymentProcessed,
    required TResult Function() applePayPaymentProcessed,
    required TResult Function() googlePayPaymentProcessed,
    required TResult Function(String? value, CouponCodeInputStatus status)
        couponCodeUpdated,
    required TResult Function() couponApplied,
    required TResult Function(bool value) automaticCreditToggled,
    required TResult Function(String value) currencyCodeUpdated,
    required TResult Function(BuyCreditPaymentMethods purchaseMethod)
        paymentMethodUpdated,
    required TResult Function(BuyCreditResponse buyCreditOption)
        productAddedToCart,
    required TResult Function(BuyCreditResponse buyCreditOption,
            String currencyCode, String? couponCode)
        discountedCheckoutStarted,
    required TResult Function(String storeProductId) iapDetailsRefreshed,
    required TResult Function(List<PurchaseDetails> purchaseDetailsList)
        purchaseDetailsUpdated,
    required TResult Function() restorePurchases,
  }) {
    return checkoutInitiated();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkoutInitiated,
    TResult? Function()? checkoutReset,
    TResult? Function()? checkoutRefreshed,
    TResult? Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult? Function()? applePayPaymentProcessed,
    TResult? Function()? googlePayPaymentProcessed,
    TResult? Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult? Function()? couponApplied,
    TResult? Function(bool value)? automaticCreditToggled,
    TResult? Function(String value)? currencyCodeUpdated,
    TResult? Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult? Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult? Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult? Function(String storeProductId)? iapDetailsRefreshed,
    TResult? Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult? Function()? restorePurchases,
  }) {
    return checkoutInitiated?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkoutInitiated,
    TResult Function()? checkoutReset,
    TResult Function()? checkoutRefreshed,
    TResult Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult Function()? applePayPaymentProcessed,
    TResult Function()? googlePayPaymentProcessed,
    TResult Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult Function()? couponApplied,
    TResult Function(bool value)? automaticCreditToggled,
    TResult Function(String value)? currencyCodeUpdated,
    TResult Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult Function(String storeProductId)? iapDetailsRefreshed,
    TResult Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult Function()? restorePurchases,
    required TResult orElse(),
  }) {
    if (checkoutInitiated != null) {
      return checkoutInitiated();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CheckoutInitiated value) checkoutInitiated,
    required TResult Function(_CheckoutReset value) checkoutReset,
    required TResult Function(_CheckoutRefreshed value) checkoutRefreshed,
    required TResult Function(_CardPaymentProcessed value) cardPaymentProcessed,
    required TResult Function(_ApplePayPaymentProcessed value)
        applePayPaymentProcessed,
    required TResult Function(_GooglePayPaymentProcessed value)
        googlePayPaymentProcessed,
    required TResult Function(_CouponCodeUpdated value) couponCodeUpdated,
    required TResult Function(_CouponApplied value) couponApplied,
    required TResult Function(_AutomaticCreditToggled value)
        automaticCreditToggled,
    required TResult Function(_CurrencyCodeUpdated value) currencyCodeUpdated,
    required TResult Function(_PaymentMethodUpdated value) paymentMethodUpdated,
    required TResult Function(_ProductAddedToCart value) productAddedToCart,
    required TResult Function(_DiscountedCheckoutStarted value)
        discountedCheckoutStarted,
    required TResult Function(_IapDetailsRefreshed value) iapDetailsRefreshed,
    required TResult Function(_PurchaseDetailsUpdated value)
        purchaseDetailsUpdated,
    required TResult Function(_RestorePurchases value) restorePurchases,
  }) {
    return checkoutInitiated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult? Function(_CheckoutReset value)? checkoutReset,
    TResult? Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult? Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult? Function(_ApplePayPaymentProcessed value)?
        applePayPaymentProcessed,
    TResult? Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult? Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult? Function(_CouponApplied value)? couponApplied,
    TResult? Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult? Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult? Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult? Function(_ProductAddedToCart value)? productAddedToCart,
    TResult? Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult? Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult? Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult? Function(_RestorePurchases value)? restorePurchases,
  }) {
    return checkoutInitiated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult Function(_CheckoutReset value)? checkoutReset,
    TResult Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult Function(_ApplePayPaymentProcessed value)? applePayPaymentProcessed,
    TResult Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult Function(_CouponApplied value)? couponApplied,
    TResult Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult Function(_ProductAddedToCart value)? productAddedToCart,
    TResult Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult Function(_RestorePurchases value)? restorePurchases,
    required TResult orElse(),
  }) {
    if (checkoutInitiated != null) {
      return checkoutInitiated(this);
    }
    return orElse();
  }
}

abstract class _CheckoutInitiated implements CheckoutEvent {
  const factory _CheckoutInitiated() = _$CheckoutInitiatedImpl;
}

/// @nodoc
abstract class _$$CheckoutResetImplCopyWith<$Res> {
  factory _$$CheckoutResetImplCopyWith(
          _$CheckoutResetImpl value, $Res Function(_$CheckoutResetImpl) then) =
      __$$CheckoutResetImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CheckoutResetImplCopyWithImpl<$Res>
    extends _$CheckoutEventCopyWithImpl<$Res, _$CheckoutResetImpl>
    implements _$$CheckoutResetImplCopyWith<$Res> {
  __$$CheckoutResetImplCopyWithImpl(
      _$CheckoutResetImpl _value, $Res Function(_$CheckoutResetImpl) _then)
      : super(_value, _then);

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CheckoutResetImpl implements _CheckoutReset {
  const _$CheckoutResetImpl();

  @override
  String toString() {
    return 'CheckoutEvent.checkoutReset()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CheckoutResetImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkoutInitiated,
    required TResult Function() checkoutReset,
    required TResult Function() checkoutRefreshed,
    required TResult Function(UserAttributesModel user) cardPaymentProcessed,
    required TResult Function() applePayPaymentProcessed,
    required TResult Function() googlePayPaymentProcessed,
    required TResult Function(String? value, CouponCodeInputStatus status)
        couponCodeUpdated,
    required TResult Function() couponApplied,
    required TResult Function(bool value) automaticCreditToggled,
    required TResult Function(String value) currencyCodeUpdated,
    required TResult Function(BuyCreditPaymentMethods purchaseMethod)
        paymentMethodUpdated,
    required TResult Function(BuyCreditResponse buyCreditOption)
        productAddedToCart,
    required TResult Function(BuyCreditResponse buyCreditOption,
            String currencyCode, String? couponCode)
        discountedCheckoutStarted,
    required TResult Function(String storeProductId) iapDetailsRefreshed,
    required TResult Function(List<PurchaseDetails> purchaseDetailsList)
        purchaseDetailsUpdated,
    required TResult Function() restorePurchases,
  }) {
    return checkoutReset();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkoutInitiated,
    TResult? Function()? checkoutReset,
    TResult? Function()? checkoutRefreshed,
    TResult? Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult? Function()? applePayPaymentProcessed,
    TResult? Function()? googlePayPaymentProcessed,
    TResult? Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult? Function()? couponApplied,
    TResult? Function(bool value)? automaticCreditToggled,
    TResult? Function(String value)? currencyCodeUpdated,
    TResult? Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult? Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult? Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult? Function(String storeProductId)? iapDetailsRefreshed,
    TResult? Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult? Function()? restorePurchases,
  }) {
    return checkoutReset?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkoutInitiated,
    TResult Function()? checkoutReset,
    TResult Function()? checkoutRefreshed,
    TResult Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult Function()? applePayPaymentProcessed,
    TResult Function()? googlePayPaymentProcessed,
    TResult Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult Function()? couponApplied,
    TResult Function(bool value)? automaticCreditToggled,
    TResult Function(String value)? currencyCodeUpdated,
    TResult Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult Function(String storeProductId)? iapDetailsRefreshed,
    TResult Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult Function()? restorePurchases,
    required TResult orElse(),
  }) {
    if (checkoutReset != null) {
      return checkoutReset();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CheckoutInitiated value) checkoutInitiated,
    required TResult Function(_CheckoutReset value) checkoutReset,
    required TResult Function(_CheckoutRefreshed value) checkoutRefreshed,
    required TResult Function(_CardPaymentProcessed value) cardPaymentProcessed,
    required TResult Function(_ApplePayPaymentProcessed value)
        applePayPaymentProcessed,
    required TResult Function(_GooglePayPaymentProcessed value)
        googlePayPaymentProcessed,
    required TResult Function(_CouponCodeUpdated value) couponCodeUpdated,
    required TResult Function(_CouponApplied value) couponApplied,
    required TResult Function(_AutomaticCreditToggled value)
        automaticCreditToggled,
    required TResult Function(_CurrencyCodeUpdated value) currencyCodeUpdated,
    required TResult Function(_PaymentMethodUpdated value) paymentMethodUpdated,
    required TResult Function(_ProductAddedToCart value) productAddedToCart,
    required TResult Function(_DiscountedCheckoutStarted value)
        discountedCheckoutStarted,
    required TResult Function(_IapDetailsRefreshed value) iapDetailsRefreshed,
    required TResult Function(_PurchaseDetailsUpdated value)
        purchaseDetailsUpdated,
    required TResult Function(_RestorePurchases value) restorePurchases,
  }) {
    return checkoutReset(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult? Function(_CheckoutReset value)? checkoutReset,
    TResult? Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult? Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult? Function(_ApplePayPaymentProcessed value)?
        applePayPaymentProcessed,
    TResult? Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult? Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult? Function(_CouponApplied value)? couponApplied,
    TResult? Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult? Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult? Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult? Function(_ProductAddedToCart value)? productAddedToCart,
    TResult? Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult? Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult? Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult? Function(_RestorePurchases value)? restorePurchases,
  }) {
    return checkoutReset?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult Function(_CheckoutReset value)? checkoutReset,
    TResult Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult Function(_ApplePayPaymentProcessed value)? applePayPaymentProcessed,
    TResult Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult Function(_CouponApplied value)? couponApplied,
    TResult Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult Function(_ProductAddedToCart value)? productAddedToCart,
    TResult Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult Function(_RestorePurchases value)? restorePurchases,
    required TResult orElse(),
  }) {
    if (checkoutReset != null) {
      return checkoutReset(this);
    }
    return orElse();
  }
}

abstract class _CheckoutReset implements CheckoutEvent {
  const factory _CheckoutReset() = _$CheckoutResetImpl;
}

/// @nodoc
abstract class _$$CheckoutRefreshedImplCopyWith<$Res> {
  factory _$$CheckoutRefreshedImplCopyWith(_$CheckoutRefreshedImpl value,
          $Res Function(_$CheckoutRefreshedImpl) then) =
      __$$CheckoutRefreshedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CheckoutRefreshedImplCopyWithImpl<$Res>
    extends _$CheckoutEventCopyWithImpl<$Res, _$CheckoutRefreshedImpl>
    implements _$$CheckoutRefreshedImplCopyWith<$Res> {
  __$$CheckoutRefreshedImplCopyWithImpl(_$CheckoutRefreshedImpl _value,
      $Res Function(_$CheckoutRefreshedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CheckoutRefreshedImpl implements _CheckoutRefreshed {
  const _$CheckoutRefreshedImpl();

  @override
  String toString() {
    return 'CheckoutEvent.checkoutRefreshed()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CheckoutRefreshedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkoutInitiated,
    required TResult Function() checkoutReset,
    required TResult Function() checkoutRefreshed,
    required TResult Function(UserAttributesModel user) cardPaymentProcessed,
    required TResult Function() applePayPaymentProcessed,
    required TResult Function() googlePayPaymentProcessed,
    required TResult Function(String? value, CouponCodeInputStatus status)
        couponCodeUpdated,
    required TResult Function() couponApplied,
    required TResult Function(bool value) automaticCreditToggled,
    required TResult Function(String value) currencyCodeUpdated,
    required TResult Function(BuyCreditPaymentMethods purchaseMethod)
        paymentMethodUpdated,
    required TResult Function(BuyCreditResponse buyCreditOption)
        productAddedToCart,
    required TResult Function(BuyCreditResponse buyCreditOption,
            String currencyCode, String? couponCode)
        discountedCheckoutStarted,
    required TResult Function(String storeProductId) iapDetailsRefreshed,
    required TResult Function(List<PurchaseDetails> purchaseDetailsList)
        purchaseDetailsUpdated,
    required TResult Function() restorePurchases,
  }) {
    return checkoutRefreshed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkoutInitiated,
    TResult? Function()? checkoutReset,
    TResult? Function()? checkoutRefreshed,
    TResult? Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult? Function()? applePayPaymentProcessed,
    TResult? Function()? googlePayPaymentProcessed,
    TResult? Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult? Function()? couponApplied,
    TResult? Function(bool value)? automaticCreditToggled,
    TResult? Function(String value)? currencyCodeUpdated,
    TResult? Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult? Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult? Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult? Function(String storeProductId)? iapDetailsRefreshed,
    TResult? Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult? Function()? restorePurchases,
  }) {
    return checkoutRefreshed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkoutInitiated,
    TResult Function()? checkoutReset,
    TResult Function()? checkoutRefreshed,
    TResult Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult Function()? applePayPaymentProcessed,
    TResult Function()? googlePayPaymentProcessed,
    TResult Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult Function()? couponApplied,
    TResult Function(bool value)? automaticCreditToggled,
    TResult Function(String value)? currencyCodeUpdated,
    TResult Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult Function(String storeProductId)? iapDetailsRefreshed,
    TResult Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult Function()? restorePurchases,
    required TResult orElse(),
  }) {
    if (checkoutRefreshed != null) {
      return checkoutRefreshed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CheckoutInitiated value) checkoutInitiated,
    required TResult Function(_CheckoutReset value) checkoutReset,
    required TResult Function(_CheckoutRefreshed value) checkoutRefreshed,
    required TResult Function(_CardPaymentProcessed value) cardPaymentProcessed,
    required TResult Function(_ApplePayPaymentProcessed value)
        applePayPaymentProcessed,
    required TResult Function(_GooglePayPaymentProcessed value)
        googlePayPaymentProcessed,
    required TResult Function(_CouponCodeUpdated value) couponCodeUpdated,
    required TResult Function(_CouponApplied value) couponApplied,
    required TResult Function(_AutomaticCreditToggled value)
        automaticCreditToggled,
    required TResult Function(_CurrencyCodeUpdated value) currencyCodeUpdated,
    required TResult Function(_PaymentMethodUpdated value) paymentMethodUpdated,
    required TResult Function(_ProductAddedToCart value) productAddedToCart,
    required TResult Function(_DiscountedCheckoutStarted value)
        discountedCheckoutStarted,
    required TResult Function(_IapDetailsRefreshed value) iapDetailsRefreshed,
    required TResult Function(_PurchaseDetailsUpdated value)
        purchaseDetailsUpdated,
    required TResult Function(_RestorePurchases value) restorePurchases,
  }) {
    return checkoutRefreshed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult? Function(_CheckoutReset value)? checkoutReset,
    TResult? Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult? Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult? Function(_ApplePayPaymentProcessed value)?
        applePayPaymentProcessed,
    TResult? Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult? Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult? Function(_CouponApplied value)? couponApplied,
    TResult? Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult? Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult? Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult? Function(_ProductAddedToCart value)? productAddedToCart,
    TResult? Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult? Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult? Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult? Function(_RestorePurchases value)? restorePurchases,
  }) {
    return checkoutRefreshed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult Function(_CheckoutReset value)? checkoutReset,
    TResult Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult Function(_ApplePayPaymentProcessed value)? applePayPaymentProcessed,
    TResult Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult Function(_CouponApplied value)? couponApplied,
    TResult Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult Function(_ProductAddedToCart value)? productAddedToCart,
    TResult Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult Function(_RestorePurchases value)? restorePurchases,
    required TResult orElse(),
  }) {
    if (checkoutRefreshed != null) {
      return checkoutRefreshed(this);
    }
    return orElse();
  }
}

abstract class _CheckoutRefreshed implements CheckoutEvent {
  const factory _CheckoutRefreshed() = _$CheckoutRefreshedImpl;
}

/// @nodoc
abstract class _$$CardPaymentProcessedImplCopyWith<$Res> {
  factory _$$CardPaymentProcessedImplCopyWith(_$CardPaymentProcessedImpl value,
          $Res Function(_$CardPaymentProcessedImpl) then) =
      __$$CardPaymentProcessedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({UserAttributesModel user});

  $UserAttributesModelCopyWith<$Res> get user;
}

/// @nodoc
class __$$CardPaymentProcessedImplCopyWithImpl<$Res>
    extends _$CheckoutEventCopyWithImpl<$Res, _$CardPaymentProcessedImpl>
    implements _$$CardPaymentProcessedImplCopyWith<$Res> {
  __$$CardPaymentProcessedImplCopyWithImpl(_$CardPaymentProcessedImpl _value,
      $Res Function(_$CardPaymentProcessedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? user = null,
  }) {
    return _then(_$CardPaymentProcessedImpl(
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as UserAttributesModel,
    ));
  }

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserAttributesModelCopyWith<$Res> get user {
    return $UserAttributesModelCopyWith<$Res>(_value.user, (value) {
      return _then(_value.copyWith(user: value));
    });
  }
}

/// @nodoc

class _$CardPaymentProcessedImpl implements _CardPaymentProcessed {
  const _$CardPaymentProcessedImpl({required this.user});

  @override
  final UserAttributesModel user;

  @override
  String toString() {
    return 'CheckoutEvent.cardPaymentProcessed(user: $user)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CardPaymentProcessedImpl &&
            (identical(other.user, user) || other.user == user));
  }

  @override
  int get hashCode => Object.hash(runtimeType, user);

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CardPaymentProcessedImplCopyWith<_$CardPaymentProcessedImpl>
      get copyWith =>
          __$$CardPaymentProcessedImplCopyWithImpl<_$CardPaymentProcessedImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkoutInitiated,
    required TResult Function() checkoutReset,
    required TResult Function() checkoutRefreshed,
    required TResult Function(UserAttributesModel user) cardPaymentProcessed,
    required TResult Function() applePayPaymentProcessed,
    required TResult Function() googlePayPaymentProcessed,
    required TResult Function(String? value, CouponCodeInputStatus status)
        couponCodeUpdated,
    required TResult Function() couponApplied,
    required TResult Function(bool value) automaticCreditToggled,
    required TResult Function(String value) currencyCodeUpdated,
    required TResult Function(BuyCreditPaymentMethods purchaseMethod)
        paymentMethodUpdated,
    required TResult Function(BuyCreditResponse buyCreditOption)
        productAddedToCart,
    required TResult Function(BuyCreditResponse buyCreditOption,
            String currencyCode, String? couponCode)
        discountedCheckoutStarted,
    required TResult Function(String storeProductId) iapDetailsRefreshed,
    required TResult Function(List<PurchaseDetails> purchaseDetailsList)
        purchaseDetailsUpdated,
    required TResult Function() restorePurchases,
  }) {
    return cardPaymentProcessed(user);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkoutInitiated,
    TResult? Function()? checkoutReset,
    TResult? Function()? checkoutRefreshed,
    TResult? Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult? Function()? applePayPaymentProcessed,
    TResult? Function()? googlePayPaymentProcessed,
    TResult? Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult? Function()? couponApplied,
    TResult? Function(bool value)? automaticCreditToggled,
    TResult? Function(String value)? currencyCodeUpdated,
    TResult? Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult? Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult? Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult? Function(String storeProductId)? iapDetailsRefreshed,
    TResult? Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult? Function()? restorePurchases,
  }) {
    return cardPaymentProcessed?.call(user);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkoutInitiated,
    TResult Function()? checkoutReset,
    TResult Function()? checkoutRefreshed,
    TResult Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult Function()? applePayPaymentProcessed,
    TResult Function()? googlePayPaymentProcessed,
    TResult Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult Function()? couponApplied,
    TResult Function(bool value)? automaticCreditToggled,
    TResult Function(String value)? currencyCodeUpdated,
    TResult Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult Function(String storeProductId)? iapDetailsRefreshed,
    TResult Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult Function()? restorePurchases,
    required TResult orElse(),
  }) {
    if (cardPaymentProcessed != null) {
      return cardPaymentProcessed(user);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CheckoutInitiated value) checkoutInitiated,
    required TResult Function(_CheckoutReset value) checkoutReset,
    required TResult Function(_CheckoutRefreshed value) checkoutRefreshed,
    required TResult Function(_CardPaymentProcessed value) cardPaymentProcessed,
    required TResult Function(_ApplePayPaymentProcessed value)
        applePayPaymentProcessed,
    required TResult Function(_GooglePayPaymentProcessed value)
        googlePayPaymentProcessed,
    required TResult Function(_CouponCodeUpdated value) couponCodeUpdated,
    required TResult Function(_CouponApplied value) couponApplied,
    required TResult Function(_AutomaticCreditToggled value)
        automaticCreditToggled,
    required TResult Function(_CurrencyCodeUpdated value) currencyCodeUpdated,
    required TResult Function(_PaymentMethodUpdated value) paymentMethodUpdated,
    required TResult Function(_ProductAddedToCart value) productAddedToCart,
    required TResult Function(_DiscountedCheckoutStarted value)
        discountedCheckoutStarted,
    required TResult Function(_IapDetailsRefreshed value) iapDetailsRefreshed,
    required TResult Function(_PurchaseDetailsUpdated value)
        purchaseDetailsUpdated,
    required TResult Function(_RestorePurchases value) restorePurchases,
  }) {
    return cardPaymentProcessed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult? Function(_CheckoutReset value)? checkoutReset,
    TResult? Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult? Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult? Function(_ApplePayPaymentProcessed value)?
        applePayPaymentProcessed,
    TResult? Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult? Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult? Function(_CouponApplied value)? couponApplied,
    TResult? Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult? Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult? Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult? Function(_ProductAddedToCart value)? productAddedToCart,
    TResult? Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult? Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult? Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult? Function(_RestorePurchases value)? restorePurchases,
  }) {
    return cardPaymentProcessed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult Function(_CheckoutReset value)? checkoutReset,
    TResult Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult Function(_ApplePayPaymentProcessed value)? applePayPaymentProcessed,
    TResult Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult Function(_CouponApplied value)? couponApplied,
    TResult Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult Function(_ProductAddedToCart value)? productAddedToCart,
    TResult Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult Function(_RestorePurchases value)? restorePurchases,
    required TResult orElse(),
  }) {
    if (cardPaymentProcessed != null) {
      return cardPaymentProcessed(this);
    }
    return orElse();
  }
}

abstract class _CardPaymentProcessed implements CheckoutEvent {
  const factory _CardPaymentProcessed(
      {required final UserAttributesModel user}) = _$CardPaymentProcessedImpl;

  UserAttributesModel get user;

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CardPaymentProcessedImplCopyWith<_$CardPaymentProcessedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ApplePayPaymentProcessedImplCopyWith<$Res> {
  factory _$$ApplePayPaymentProcessedImplCopyWith(
          _$ApplePayPaymentProcessedImpl value,
          $Res Function(_$ApplePayPaymentProcessedImpl) then) =
      __$$ApplePayPaymentProcessedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ApplePayPaymentProcessedImplCopyWithImpl<$Res>
    extends _$CheckoutEventCopyWithImpl<$Res, _$ApplePayPaymentProcessedImpl>
    implements _$$ApplePayPaymentProcessedImplCopyWith<$Res> {
  __$$ApplePayPaymentProcessedImplCopyWithImpl(
      _$ApplePayPaymentProcessedImpl _value,
      $Res Function(_$ApplePayPaymentProcessedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ApplePayPaymentProcessedImpl implements _ApplePayPaymentProcessed {
  const _$ApplePayPaymentProcessedImpl();

  @override
  String toString() {
    return 'CheckoutEvent.applePayPaymentProcessed()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ApplePayPaymentProcessedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkoutInitiated,
    required TResult Function() checkoutReset,
    required TResult Function() checkoutRefreshed,
    required TResult Function(UserAttributesModel user) cardPaymentProcessed,
    required TResult Function() applePayPaymentProcessed,
    required TResult Function() googlePayPaymentProcessed,
    required TResult Function(String? value, CouponCodeInputStatus status)
        couponCodeUpdated,
    required TResult Function() couponApplied,
    required TResult Function(bool value) automaticCreditToggled,
    required TResult Function(String value) currencyCodeUpdated,
    required TResult Function(BuyCreditPaymentMethods purchaseMethod)
        paymentMethodUpdated,
    required TResult Function(BuyCreditResponse buyCreditOption)
        productAddedToCart,
    required TResult Function(BuyCreditResponse buyCreditOption,
            String currencyCode, String? couponCode)
        discountedCheckoutStarted,
    required TResult Function(String storeProductId) iapDetailsRefreshed,
    required TResult Function(List<PurchaseDetails> purchaseDetailsList)
        purchaseDetailsUpdated,
    required TResult Function() restorePurchases,
  }) {
    return applePayPaymentProcessed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkoutInitiated,
    TResult? Function()? checkoutReset,
    TResult? Function()? checkoutRefreshed,
    TResult? Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult? Function()? applePayPaymentProcessed,
    TResult? Function()? googlePayPaymentProcessed,
    TResult? Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult? Function()? couponApplied,
    TResult? Function(bool value)? automaticCreditToggled,
    TResult? Function(String value)? currencyCodeUpdated,
    TResult? Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult? Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult? Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult? Function(String storeProductId)? iapDetailsRefreshed,
    TResult? Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult? Function()? restorePurchases,
  }) {
    return applePayPaymentProcessed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkoutInitiated,
    TResult Function()? checkoutReset,
    TResult Function()? checkoutRefreshed,
    TResult Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult Function()? applePayPaymentProcessed,
    TResult Function()? googlePayPaymentProcessed,
    TResult Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult Function()? couponApplied,
    TResult Function(bool value)? automaticCreditToggled,
    TResult Function(String value)? currencyCodeUpdated,
    TResult Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult Function(String storeProductId)? iapDetailsRefreshed,
    TResult Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult Function()? restorePurchases,
    required TResult orElse(),
  }) {
    if (applePayPaymentProcessed != null) {
      return applePayPaymentProcessed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CheckoutInitiated value) checkoutInitiated,
    required TResult Function(_CheckoutReset value) checkoutReset,
    required TResult Function(_CheckoutRefreshed value) checkoutRefreshed,
    required TResult Function(_CardPaymentProcessed value) cardPaymentProcessed,
    required TResult Function(_ApplePayPaymentProcessed value)
        applePayPaymentProcessed,
    required TResult Function(_GooglePayPaymentProcessed value)
        googlePayPaymentProcessed,
    required TResult Function(_CouponCodeUpdated value) couponCodeUpdated,
    required TResult Function(_CouponApplied value) couponApplied,
    required TResult Function(_AutomaticCreditToggled value)
        automaticCreditToggled,
    required TResult Function(_CurrencyCodeUpdated value) currencyCodeUpdated,
    required TResult Function(_PaymentMethodUpdated value) paymentMethodUpdated,
    required TResult Function(_ProductAddedToCart value) productAddedToCart,
    required TResult Function(_DiscountedCheckoutStarted value)
        discountedCheckoutStarted,
    required TResult Function(_IapDetailsRefreshed value) iapDetailsRefreshed,
    required TResult Function(_PurchaseDetailsUpdated value)
        purchaseDetailsUpdated,
    required TResult Function(_RestorePurchases value) restorePurchases,
  }) {
    return applePayPaymentProcessed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult? Function(_CheckoutReset value)? checkoutReset,
    TResult? Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult? Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult? Function(_ApplePayPaymentProcessed value)?
        applePayPaymentProcessed,
    TResult? Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult? Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult? Function(_CouponApplied value)? couponApplied,
    TResult? Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult? Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult? Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult? Function(_ProductAddedToCart value)? productAddedToCart,
    TResult? Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult? Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult? Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult? Function(_RestorePurchases value)? restorePurchases,
  }) {
    return applePayPaymentProcessed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult Function(_CheckoutReset value)? checkoutReset,
    TResult Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult Function(_ApplePayPaymentProcessed value)? applePayPaymentProcessed,
    TResult Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult Function(_CouponApplied value)? couponApplied,
    TResult Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult Function(_ProductAddedToCart value)? productAddedToCart,
    TResult Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult Function(_RestorePurchases value)? restorePurchases,
    required TResult orElse(),
  }) {
    if (applePayPaymentProcessed != null) {
      return applePayPaymentProcessed(this);
    }
    return orElse();
  }
}

abstract class _ApplePayPaymentProcessed implements CheckoutEvent {
  const factory _ApplePayPaymentProcessed() = _$ApplePayPaymentProcessedImpl;
}

/// @nodoc
abstract class _$$GooglePayPaymentProcessedImplCopyWith<$Res> {
  factory _$$GooglePayPaymentProcessedImplCopyWith(
          _$GooglePayPaymentProcessedImpl value,
          $Res Function(_$GooglePayPaymentProcessedImpl) then) =
      __$$GooglePayPaymentProcessedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GooglePayPaymentProcessedImplCopyWithImpl<$Res>
    extends _$CheckoutEventCopyWithImpl<$Res, _$GooglePayPaymentProcessedImpl>
    implements _$$GooglePayPaymentProcessedImplCopyWith<$Res> {
  __$$GooglePayPaymentProcessedImplCopyWithImpl(
      _$GooglePayPaymentProcessedImpl _value,
      $Res Function(_$GooglePayPaymentProcessedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$GooglePayPaymentProcessedImpl implements _GooglePayPaymentProcessed {
  const _$GooglePayPaymentProcessedImpl();

  @override
  String toString() {
    return 'CheckoutEvent.googlePayPaymentProcessed()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GooglePayPaymentProcessedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkoutInitiated,
    required TResult Function() checkoutReset,
    required TResult Function() checkoutRefreshed,
    required TResult Function(UserAttributesModel user) cardPaymentProcessed,
    required TResult Function() applePayPaymentProcessed,
    required TResult Function() googlePayPaymentProcessed,
    required TResult Function(String? value, CouponCodeInputStatus status)
        couponCodeUpdated,
    required TResult Function() couponApplied,
    required TResult Function(bool value) automaticCreditToggled,
    required TResult Function(String value) currencyCodeUpdated,
    required TResult Function(BuyCreditPaymentMethods purchaseMethod)
        paymentMethodUpdated,
    required TResult Function(BuyCreditResponse buyCreditOption)
        productAddedToCart,
    required TResult Function(BuyCreditResponse buyCreditOption,
            String currencyCode, String? couponCode)
        discountedCheckoutStarted,
    required TResult Function(String storeProductId) iapDetailsRefreshed,
    required TResult Function(List<PurchaseDetails> purchaseDetailsList)
        purchaseDetailsUpdated,
    required TResult Function() restorePurchases,
  }) {
    return googlePayPaymentProcessed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkoutInitiated,
    TResult? Function()? checkoutReset,
    TResult? Function()? checkoutRefreshed,
    TResult? Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult? Function()? applePayPaymentProcessed,
    TResult? Function()? googlePayPaymentProcessed,
    TResult? Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult? Function()? couponApplied,
    TResult? Function(bool value)? automaticCreditToggled,
    TResult? Function(String value)? currencyCodeUpdated,
    TResult? Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult? Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult? Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult? Function(String storeProductId)? iapDetailsRefreshed,
    TResult? Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult? Function()? restorePurchases,
  }) {
    return googlePayPaymentProcessed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkoutInitiated,
    TResult Function()? checkoutReset,
    TResult Function()? checkoutRefreshed,
    TResult Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult Function()? applePayPaymentProcessed,
    TResult Function()? googlePayPaymentProcessed,
    TResult Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult Function()? couponApplied,
    TResult Function(bool value)? automaticCreditToggled,
    TResult Function(String value)? currencyCodeUpdated,
    TResult Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult Function(String storeProductId)? iapDetailsRefreshed,
    TResult Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult Function()? restorePurchases,
    required TResult orElse(),
  }) {
    if (googlePayPaymentProcessed != null) {
      return googlePayPaymentProcessed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CheckoutInitiated value) checkoutInitiated,
    required TResult Function(_CheckoutReset value) checkoutReset,
    required TResult Function(_CheckoutRefreshed value) checkoutRefreshed,
    required TResult Function(_CardPaymentProcessed value) cardPaymentProcessed,
    required TResult Function(_ApplePayPaymentProcessed value)
        applePayPaymentProcessed,
    required TResult Function(_GooglePayPaymentProcessed value)
        googlePayPaymentProcessed,
    required TResult Function(_CouponCodeUpdated value) couponCodeUpdated,
    required TResult Function(_CouponApplied value) couponApplied,
    required TResult Function(_AutomaticCreditToggled value)
        automaticCreditToggled,
    required TResult Function(_CurrencyCodeUpdated value) currencyCodeUpdated,
    required TResult Function(_PaymentMethodUpdated value) paymentMethodUpdated,
    required TResult Function(_ProductAddedToCart value) productAddedToCart,
    required TResult Function(_DiscountedCheckoutStarted value)
        discountedCheckoutStarted,
    required TResult Function(_IapDetailsRefreshed value) iapDetailsRefreshed,
    required TResult Function(_PurchaseDetailsUpdated value)
        purchaseDetailsUpdated,
    required TResult Function(_RestorePurchases value) restorePurchases,
  }) {
    return googlePayPaymentProcessed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult? Function(_CheckoutReset value)? checkoutReset,
    TResult? Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult? Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult? Function(_ApplePayPaymentProcessed value)?
        applePayPaymentProcessed,
    TResult? Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult? Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult? Function(_CouponApplied value)? couponApplied,
    TResult? Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult? Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult? Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult? Function(_ProductAddedToCart value)? productAddedToCart,
    TResult? Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult? Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult? Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult? Function(_RestorePurchases value)? restorePurchases,
  }) {
    return googlePayPaymentProcessed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult Function(_CheckoutReset value)? checkoutReset,
    TResult Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult Function(_ApplePayPaymentProcessed value)? applePayPaymentProcessed,
    TResult Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult Function(_CouponApplied value)? couponApplied,
    TResult Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult Function(_ProductAddedToCart value)? productAddedToCart,
    TResult Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult Function(_RestorePurchases value)? restorePurchases,
    required TResult orElse(),
  }) {
    if (googlePayPaymentProcessed != null) {
      return googlePayPaymentProcessed(this);
    }
    return orElse();
  }
}

abstract class _GooglePayPaymentProcessed implements CheckoutEvent {
  const factory _GooglePayPaymentProcessed() = _$GooglePayPaymentProcessedImpl;
}

/// @nodoc
abstract class _$$CouponCodeUpdatedImplCopyWith<$Res> {
  factory _$$CouponCodeUpdatedImplCopyWith(_$CouponCodeUpdatedImpl value,
          $Res Function(_$CouponCodeUpdatedImpl) then) =
      __$$CouponCodeUpdatedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? value, CouponCodeInputStatus status});
}

/// @nodoc
class __$$CouponCodeUpdatedImplCopyWithImpl<$Res>
    extends _$CheckoutEventCopyWithImpl<$Res, _$CouponCodeUpdatedImpl>
    implements _$$CouponCodeUpdatedImplCopyWith<$Res> {
  __$$CouponCodeUpdatedImplCopyWithImpl(_$CouponCodeUpdatedImpl _value,
      $Res Function(_$CouponCodeUpdatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = freezed,
    Object? status = null,
  }) {
    return _then(_$CouponCodeUpdatedImpl(
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as CouponCodeInputStatus,
    ));
  }
}

/// @nodoc

class _$CouponCodeUpdatedImpl implements _CouponCodeUpdated {
  const _$CouponCodeUpdatedImpl(
      {this.value, this.status = CouponCodeInputStatus.initial});

  @override
  final String? value;
  @override
  @JsonKey()
  final CouponCodeInputStatus status;

  @override
  String toString() {
    return 'CheckoutEvent.couponCodeUpdated(value: $value, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CouponCodeUpdatedImpl &&
            (identical(other.value, value) || other.value == value) &&
            (identical(other.status, status) || other.status == status));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value, status);

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CouponCodeUpdatedImplCopyWith<_$CouponCodeUpdatedImpl> get copyWith =>
      __$$CouponCodeUpdatedImplCopyWithImpl<_$CouponCodeUpdatedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkoutInitiated,
    required TResult Function() checkoutReset,
    required TResult Function() checkoutRefreshed,
    required TResult Function(UserAttributesModel user) cardPaymentProcessed,
    required TResult Function() applePayPaymentProcessed,
    required TResult Function() googlePayPaymentProcessed,
    required TResult Function(String? value, CouponCodeInputStatus status)
        couponCodeUpdated,
    required TResult Function() couponApplied,
    required TResult Function(bool value) automaticCreditToggled,
    required TResult Function(String value) currencyCodeUpdated,
    required TResult Function(BuyCreditPaymentMethods purchaseMethod)
        paymentMethodUpdated,
    required TResult Function(BuyCreditResponse buyCreditOption)
        productAddedToCart,
    required TResult Function(BuyCreditResponse buyCreditOption,
            String currencyCode, String? couponCode)
        discountedCheckoutStarted,
    required TResult Function(String storeProductId) iapDetailsRefreshed,
    required TResult Function(List<PurchaseDetails> purchaseDetailsList)
        purchaseDetailsUpdated,
    required TResult Function() restorePurchases,
  }) {
    return couponCodeUpdated(value, status);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkoutInitiated,
    TResult? Function()? checkoutReset,
    TResult? Function()? checkoutRefreshed,
    TResult? Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult? Function()? applePayPaymentProcessed,
    TResult? Function()? googlePayPaymentProcessed,
    TResult? Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult? Function()? couponApplied,
    TResult? Function(bool value)? automaticCreditToggled,
    TResult? Function(String value)? currencyCodeUpdated,
    TResult? Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult? Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult? Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult? Function(String storeProductId)? iapDetailsRefreshed,
    TResult? Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult? Function()? restorePurchases,
  }) {
    return couponCodeUpdated?.call(value, status);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkoutInitiated,
    TResult Function()? checkoutReset,
    TResult Function()? checkoutRefreshed,
    TResult Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult Function()? applePayPaymentProcessed,
    TResult Function()? googlePayPaymentProcessed,
    TResult Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult Function()? couponApplied,
    TResult Function(bool value)? automaticCreditToggled,
    TResult Function(String value)? currencyCodeUpdated,
    TResult Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult Function(String storeProductId)? iapDetailsRefreshed,
    TResult Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult Function()? restorePurchases,
    required TResult orElse(),
  }) {
    if (couponCodeUpdated != null) {
      return couponCodeUpdated(value, status);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CheckoutInitiated value) checkoutInitiated,
    required TResult Function(_CheckoutReset value) checkoutReset,
    required TResult Function(_CheckoutRefreshed value) checkoutRefreshed,
    required TResult Function(_CardPaymentProcessed value) cardPaymentProcessed,
    required TResult Function(_ApplePayPaymentProcessed value)
        applePayPaymentProcessed,
    required TResult Function(_GooglePayPaymentProcessed value)
        googlePayPaymentProcessed,
    required TResult Function(_CouponCodeUpdated value) couponCodeUpdated,
    required TResult Function(_CouponApplied value) couponApplied,
    required TResult Function(_AutomaticCreditToggled value)
        automaticCreditToggled,
    required TResult Function(_CurrencyCodeUpdated value) currencyCodeUpdated,
    required TResult Function(_PaymentMethodUpdated value) paymentMethodUpdated,
    required TResult Function(_ProductAddedToCart value) productAddedToCart,
    required TResult Function(_DiscountedCheckoutStarted value)
        discountedCheckoutStarted,
    required TResult Function(_IapDetailsRefreshed value) iapDetailsRefreshed,
    required TResult Function(_PurchaseDetailsUpdated value)
        purchaseDetailsUpdated,
    required TResult Function(_RestorePurchases value) restorePurchases,
  }) {
    return couponCodeUpdated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult? Function(_CheckoutReset value)? checkoutReset,
    TResult? Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult? Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult? Function(_ApplePayPaymentProcessed value)?
        applePayPaymentProcessed,
    TResult? Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult? Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult? Function(_CouponApplied value)? couponApplied,
    TResult? Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult? Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult? Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult? Function(_ProductAddedToCart value)? productAddedToCart,
    TResult? Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult? Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult? Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult? Function(_RestorePurchases value)? restorePurchases,
  }) {
    return couponCodeUpdated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult Function(_CheckoutReset value)? checkoutReset,
    TResult Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult Function(_ApplePayPaymentProcessed value)? applePayPaymentProcessed,
    TResult Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult Function(_CouponApplied value)? couponApplied,
    TResult Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult Function(_ProductAddedToCart value)? productAddedToCart,
    TResult Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult Function(_RestorePurchases value)? restorePurchases,
    required TResult orElse(),
  }) {
    if (couponCodeUpdated != null) {
      return couponCodeUpdated(this);
    }
    return orElse();
  }
}

abstract class _CouponCodeUpdated implements CheckoutEvent {
  const factory _CouponCodeUpdated(
      {final String? value,
      final CouponCodeInputStatus status}) = _$CouponCodeUpdatedImpl;

  String? get value;
  CouponCodeInputStatus get status;

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CouponCodeUpdatedImplCopyWith<_$CouponCodeUpdatedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CouponAppliedImplCopyWith<$Res> {
  factory _$$CouponAppliedImplCopyWith(
          _$CouponAppliedImpl value, $Res Function(_$CouponAppliedImpl) then) =
      __$$CouponAppliedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CouponAppliedImplCopyWithImpl<$Res>
    extends _$CheckoutEventCopyWithImpl<$Res, _$CouponAppliedImpl>
    implements _$$CouponAppliedImplCopyWith<$Res> {
  __$$CouponAppliedImplCopyWithImpl(
      _$CouponAppliedImpl _value, $Res Function(_$CouponAppliedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CouponAppliedImpl implements _CouponApplied {
  const _$CouponAppliedImpl();

  @override
  String toString() {
    return 'CheckoutEvent.couponApplied()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CouponAppliedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkoutInitiated,
    required TResult Function() checkoutReset,
    required TResult Function() checkoutRefreshed,
    required TResult Function(UserAttributesModel user) cardPaymentProcessed,
    required TResult Function() applePayPaymentProcessed,
    required TResult Function() googlePayPaymentProcessed,
    required TResult Function(String? value, CouponCodeInputStatus status)
        couponCodeUpdated,
    required TResult Function() couponApplied,
    required TResult Function(bool value) automaticCreditToggled,
    required TResult Function(String value) currencyCodeUpdated,
    required TResult Function(BuyCreditPaymentMethods purchaseMethod)
        paymentMethodUpdated,
    required TResult Function(BuyCreditResponse buyCreditOption)
        productAddedToCart,
    required TResult Function(BuyCreditResponse buyCreditOption,
            String currencyCode, String? couponCode)
        discountedCheckoutStarted,
    required TResult Function(String storeProductId) iapDetailsRefreshed,
    required TResult Function(List<PurchaseDetails> purchaseDetailsList)
        purchaseDetailsUpdated,
    required TResult Function() restorePurchases,
  }) {
    return couponApplied();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkoutInitiated,
    TResult? Function()? checkoutReset,
    TResult? Function()? checkoutRefreshed,
    TResult? Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult? Function()? applePayPaymentProcessed,
    TResult? Function()? googlePayPaymentProcessed,
    TResult? Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult? Function()? couponApplied,
    TResult? Function(bool value)? automaticCreditToggled,
    TResult? Function(String value)? currencyCodeUpdated,
    TResult? Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult? Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult? Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult? Function(String storeProductId)? iapDetailsRefreshed,
    TResult? Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult? Function()? restorePurchases,
  }) {
    return couponApplied?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkoutInitiated,
    TResult Function()? checkoutReset,
    TResult Function()? checkoutRefreshed,
    TResult Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult Function()? applePayPaymentProcessed,
    TResult Function()? googlePayPaymentProcessed,
    TResult Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult Function()? couponApplied,
    TResult Function(bool value)? automaticCreditToggled,
    TResult Function(String value)? currencyCodeUpdated,
    TResult Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult Function(String storeProductId)? iapDetailsRefreshed,
    TResult Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult Function()? restorePurchases,
    required TResult orElse(),
  }) {
    if (couponApplied != null) {
      return couponApplied();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CheckoutInitiated value) checkoutInitiated,
    required TResult Function(_CheckoutReset value) checkoutReset,
    required TResult Function(_CheckoutRefreshed value) checkoutRefreshed,
    required TResult Function(_CardPaymentProcessed value) cardPaymentProcessed,
    required TResult Function(_ApplePayPaymentProcessed value)
        applePayPaymentProcessed,
    required TResult Function(_GooglePayPaymentProcessed value)
        googlePayPaymentProcessed,
    required TResult Function(_CouponCodeUpdated value) couponCodeUpdated,
    required TResult Function(_CouponApplied value) couponApplied,
    required TResult Function(_AutomaticCreditToggled value)
        automaticCreditToggled,
    required TResult Function(_CurrencyCodeUpdated value) currencyCodeUpdated,
    required TResult Function(_PaymentMethodUpdated value) paymentMethodUpdated,
    required TResult Function(_ProductAddedToCart value) productAddedToCart,
    required TResult Function(_DiscountedCheckoutStarted value)
        discountedCheckoutStarted,
    required TResult Function(_IapDetailsRefreshed value) iapDetailsRefreshed,
    required TResult Function(_PurchaseDetailsUpdated value)
        purchaseDetailsUpdated,
    required TResult Function(_RestorePurchases value) restorePurchases,
  }) {
    return couponApplied(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult? Function(_CheckoutReset value)? checkoutReset,
    TResult? Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult? Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult? Function(_ApplePayPaymentProcessed value)?
        applePayPaymentProcessed,
    TResult? Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult? Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult? Function(_CouponApplied value)? couponApplied,
    TResult? Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult? Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult? Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult? Function(_ProductAddedToCart value)? productAddedToCart,
    TResult? Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult? Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult? Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult? Function(_RestorePurchases value)? restorePurchases,
  }) {
    return couponApplied?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult Function(_CheckoutReset value)? checkoutReset,
    TResult Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult Function(_ApplePayPaymentProcessed value)? applePayPaymentProcessed,
    TResult Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult Function(_CouponApplied value)? couponApplied,
    TResult Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult Function(_ProductAddedToCart value)? productAddedToCart,
    TResult Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult Function(_RestorePurchases value)? restorePurchases,
    required TResult orElse(),
  }) {
    if (couponApplied != null) {
      return couponApplied(this);
    }
    return orElse();
  }
}

abstract class _CouponApplied implements CheckoutEvent {
  const factory _CouponApplied() = _$CouponAppliedImpl;
}

/// @nodoc
abstract class _$$AutomaticCreditToggledImplCopyWith<$Res> {
  factory _$$AutomaticCreditToggledImplCopyWith(
          _$AutomaticCreditToggledImpl value,
          $Res Function(_$AutomaticCreditToggledImpl) then) =
      __$$AutomaticCreditToggledImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool value});
}

/// @nodoc
class __$$AutomaticCreditToggledImplCopyWithImpl<$Res>
    extends _$CheckoutEventCopyWithImpl<$Res, _$AutomaticCreditToggledImpl>
    implements _$$AutomaticCreditToggledImplCopyWith<$Res> {
  __$$AutomaticCreditToggledImplCopyWithImpl(
      _$AutomaticCreditToggledImpl _value,
      $Res Function(_$AutomaticCreditToggledImpl) _then)
      : super(_value, _then);

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$AutomaticCreditToggledImpl(
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$AutomaticCreditToggledImpl implements _AutomaticCreditToggled {
  const _$AutomaticCreditToggledImpl({this.value = false});

  @override
  @JsonKey()
  final bool value;

  @override
  String toString() {
    return 'CheckoutEvent.automaticCreditToggled(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AutomaticCreditToggledImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AutomaticCreditToggledImplCopyWith<_$AutomaticCreditToggledImpl>
      get copyWith => __$$AutomaticCreditToggledImplCopyWithImpl<
          _$AutomaticCreditToggledImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkoutInitiated,
    required TResult Function() checkoutReset,
    required TResult Function() checkoutRefreshed,
    required TResult Function(UserAttributesModel user) cardPaymentProcessed,
    required TResult Function() applePayPaymentProcessed,
    required TResult Function() googlePayPaymentProcessed,
    required TResult Function(String? value, CouponCodeInputStatus status)
        couponCodeUpdated,
    required TResult Function() couponApplied,
    required TResult Function(bool value) automaticCreditToggled,
    required TResult Function(String value) currencyCodeUpdated,
    required TResult Function(BuyCreditPaymentMethods purchaseMethod)
        paymentMethodUpdated,
    required TResult Function(BuyCreditResponse buyCreditOption)
        productAddedToCart,
    required TResult Function(BuyCreditResponse buyCreditOption,
            String currencyCode, String? couponCode)
        discountedCheckoutStarted,
    required TResult Function(String storeProductId) iapDetailsRefreshed,
    required TResult Function(List<PurchaseDetails> purchaseDetailsList)
        purchaseDetailsUpdated,
    required TResult Function() restorePurchases,
  }) {
    return automaticCreditToggled(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkoutInitiated,
    TResult? Function()? checkoutReset,
    TResult? Function()? checkoutRefreshed,
    TResult? Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult? Function()? applePayPaymentProcessed,
    TResult? Function()? googlePayPaymentProcessed,
    TResult? Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult? Function()? couponApplied,
    TResult? Function(bool value)? automaticCreditToggled,
    TResult? Function(String value)? currencyCodeUpdated,
    TResult? Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult? Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult? Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult? Function(String storeProductId)? iapDetailsRefreshed,
    TResult? Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult? Function()? restorePurchases,
  }) {
    return automaticCreditToggled?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkoutInitiated,
    TResult Function()? checkoutReset,
    TResult Function()? checkoutRefreshed,
    TResult Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult Function()? applePayPaymentProcessed,
    TResult Function()? googlePayPaymentProcessed,
    TResult Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult Function()? couponApplied,
    TResult Function(bool value)? automaticCreditToggled,
    TResult Function(String value)? currencyCodeUpdated,
    TResult Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult Function(String storeProductId)? iapDetailsRefreshed,
    TResult Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult Function()? restorePurchases,
    required TResult orElse(),
  }) {
    if (automaticCreditToggled != null) {
      return automaticCreditToggled(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CheckoutInitiated value) checkoutInitiated,
    required TResult Function(_CheckoutReset value) checkoutReset,
    required TResult Function(_CheckoutRefreshed value) checkoutRefreshed,
    required TResult Function(_CardPaymentProcessed value) cardPaymentProcessed,
    required TResult Function(_ApplePayPaymentProcessed value)
        applePayPaymentProcessed,
    required TResult Function(_GooglePayPaymentProcessed value)
        googlePayPaymentProcessed,
    required TResult Function(_CouponCodeUpdated value) couponCodeUpdated,
    required TResult Function(_CouponApplied value) couponApplied,
    required TResult Function(_AutomaticCreditToggled value)
        automaticCreditToggled,
    required TResult Function(_CurrencyCodeUpdated value) currencyCodeUpdated,
    required TResult Function(_PaymentMethodUpdated value) paymentMethodUpdated,
    required TResult Function(_ProductAddedToCart value) productAddedToCart,
    required TResult Function(_DiscountedCheckoutStarted value)
        discountedCheckoutStarted,
    required TResult Function(_IapDetailsRefreshed value) iapDetailsRefreshed,
    required TResult Function(_PurchaseDetailsUpdated value)
        purchaseDetailsUpdated,
    required TResult Function(_RestorePurchases value) restorePurchases,
  }) {
    return automaticCreditToggled(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult? Function(_CheckoutReset value)? checkoutReset,
    TResult? Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult? Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult? Function(_ApplePayPaymentProcessed value)?
        applePayPaymentProcessed,
    TResult? Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult? Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult? Function(_CouponApplied value)? couponApplied,
    TResult? Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult? Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult? Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult? Function(_ProductAddedToCart value)? productAddedToCart,
    TResult? Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult? Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult? Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult? Function(_RestorePurchases value)? restorePurchases,
  }) {
    return automaticCreditToggled?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult Function(_CheckoutReset value)? checkoutReset,
    TResult Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult Function(_ApplePayPaymentProcessed value)? applePayPaymentProcessed,
    TResult Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult Function(_CouponApplied value)? couponApplied,
    TResult Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult Function(_ProductAddedToCart value)? productAddedToCart,
    TResult Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult Function(_RestorePurchases value)? restorePurchases,
    required TResult orElse(),
  }) {
    if (automaticCreditToggled != null) {
      return automaticCreditToggled(this);
    }
    return orElse();
  }
}

abstract class _AutomaticCreditToggled implements CheckoutEvent {
  const factory _AutomaticCreditToggled({final bool value}) =
      _$AutomaticCreditToggledImpl;

  bool get value;

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AutomaticCreditToggledImplCopyWith<_$AutomaticCreditToggledImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CurrencyCodeUpdatedImplCopyWith<$Res> {
  factory _$$CurrencyCodeUpdatedImplCopyWith(_$CurrencyCodeUpdatedImpl value,
          $Res Function(_$CurrencyCodeUpdatedImpl) then) =
      __$$CurrencyCodeUpdatedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String value});
}

/// @nodoc
class __$$CurrencyCodeUpdatedImplCopyWithImpl<$Res>
    extends _$CheckoutEventCopyWithImpl<$Res, _$CurrencyCodeUpdatedImpl>
    implements _$$CurrencyCodeUpdatedImplCopyWith<$Res> {
  __$$CurrencyCodeUpdatedImplCopyWithImpl(_$CurrencyCodeUpdatedImpl _value,
      $Res Function(_$CurrencyCodeUpdatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$CurrencyCodeUpdatedImpl(
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$CurrencyCodeUpdatedImpl implements _CurrencyCodeUpdated {
  const _$CurrencyCodeUpdatedImpl({this.value = 'usd'});

  @override
  @JsonKey()
  final String value;

  @override
  String toString() {
    return 'CheckoutEvent.currencyCodeUpdated(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CurrencyCodeUpdatedImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CurrencyCodeUpdatedImplCopyWith<_$CurrencyCodeUpdatedImpl> get copyWith =>
      __$$CurrencyCodeUpdatedImplCopyWithImpl<_$CurrencyCodeUpdatedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkoutInitiated,
    required TResult Function() checkoutReset,
    required TResult Function() checkoutRefreshed,
    required TResult Function(UserAttributesModel user) cardPaymentProcessed,
    required TResult Function() applePayPaymentProcessed,
    required TResult Function() googlePayPaymentProcessed,
    required TResult Function(String? value, CouponCodeInputStatus status)
        couponCodeUpdated,
    required TResult Function() couponApplied,
    required TResult Function(bool value) automaticCreditToggled,
    required TResult Function(String value) currencyCodeUpdated,
    required TResult Function(BuyCreditPaymentMethods purchaseMethod)
        paymentMethodUpdated,
    required TResult Function(BuyCreditResponse buyCreditOption)
        productAddedToCart,
    required TResult Function(BuyCreditResponse buyCreditOption,
            String currencyCode, String? couponCode)
        discountedCheckoutStarted,
    required TResult Function(String storeProductId) iapDetailsRefreshed,
    required TResult Function(List<PurchaseDetails> purchaseDetailsList)
        purchaseDetailsUpdated,
    required TResult Function() restorePurchases,
  }) {
    return currencyCodeUpdated(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkoutInitiated,
    TResult? Function()? checkoutReset,
    TResult? Function()? checkoutRefreshed,
    TResult? Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult? Function()? applePayPaymentProcessed,
    TResult? Function()? googlePayPaymentProcessed,
    TResult? Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult? Function()? couponApplied,
    TResult? Function(bool value)? automaticCreditToggled,
    TResult? Function(String value)? currencyCodeUpdated,
    TResult? Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult? Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult? Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult? Function(String storeProductId)? iapDetailsRefreshed,
    TResult? Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult? Function()? restorePurchases,
  }) {
    return currencyCodeUpdated?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkoutInitiated,
    TResult Function()? checkoutReset,
    TResult Function()? checkoutRefreshed,
    TResult Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult Function()? applePayPaymentProcessed,
    TResult Function()? googlePayPaymentProcessed,
    TResult Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult Function()? couponApplied,
    TResult Function(bool value)? automaticCreditToggled,
    TResult Function(String value)? currencyCodeUpdated,
    TResult Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult Function(String storeProductId)? iapDetailsRefreshed,
    TResult Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult Function()? restorePurchases,
    required TResult orElse(),
  }) {
    if (currencyCodeUpdated != null) {
      return currencyCodeUpdated(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CheckoutInitiated value) checkoutInitiated,
    required TResult Function(_CheckoutReset value) checkoutReset,
    required TResult Function(_CheckoutRefreshed value) checkoutRefreshed,
    required TResult Function(_CardPaymentProcessed value) cardPaymentProcessed,
    required TResult Function(_ApplePayPaymentProcessed value)
        applePayPaymentProcessed,
    required TResult Function(_GooglePayPaymentProcessed value)
        googlePayPaymentProcessed,
    required TResult Function(_CouponCodeUpdated value) couponCodeUpdated,
    required TResult Function(_CouponApplied value) couponApplied,
    required TResult Function(_AutomaticCreditToggled value)
        automaticCreditToggled,
    required TResult Function(_CurrencyCodeUpdated value) currencyCodeUpdated,
    required TResult Function(_PaymentMethodUpdated value) paymentMethodUpdated,
    required TResult Function(_ProductAddedToCart value) productAddedToCart,
    required TResult Function(_DiscountedCheckoutStarted value)
        discountedCheckoutStarted,
    required TResult Function(_IapDetailsRefreshed value) iapDetailsRefreshed,
    required TResult Function(_PurchaseDetailsUpdated value)
        purchaseDetailsUpdated,
    required TResult Function(_RestorePurchases value) restorePurchases,
  }) {
    return currencyCodeUpdated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult? Function(_CheckoutReset value)? checkoutReset,
    TResult? Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult? Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult? Function(_ApplePayPaymentProcessed value)?
        applePayPaymentProcessed,
    TResult? Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult? Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult? Function(_CouponApplied value)? couponApplied,
    TResult? Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult? Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult? Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult? Function(_ProductAddedToCart value)? productAddedToCart,
    TResult? Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult? Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult? Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult? Function(_RestorePurchases value)? restorePurchases,
  }) {
    return currencyCodeUpdated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult Function(_CheckoutReset value)? checkoutReset,
    TResult Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult Function(_ApplePayPaymentProcessed value)? applePayPaymentProcessed,
    TResult Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult Function(_CouponApplied value)? couponApplied,
    TResult Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult Function(_ProductAddedToCart value)? productAddedToCart,
    TResult Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult Function(_RestorePurchases value)? restorePurchases,
    required TResult orElse(),
  }) {
    if (currencyCodeUpdated != null) {
      return currencyCodeUpdated(this);
    }
    return orElse();
  }
}

abstract class _CurrencyCodeUpdated implements CheckoutEvent {
  const factory _CurrencyCodeUpdated({final String value}) =
      _$CurrencyCodeUpdatedImpl;

  String get value;

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CurrencyCodeUpdatedImplCopyWith<_$CurrencyCodeUpdatedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PaymentMethodUpdatedImplCopyWith<$Res> {
  factory _$$PaymentMethodUpdatedImplCopyWith(_$PaymentMethodUpdatedImpl value,
          $Res Function(_$PaymentMethodUpdatedImpl) then) =
      __$$PaymentMethodUpdatedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BuyCreditPaymentMethods purchaseMethod});
}

/// @nodoc
class __$$PaymentMethodUpdatedImplCopyWithImpl<$Res>
    extends _$CheckoutEventCopyWithImpl<$Res, _$PaymentMethodUpdatedImpl>
    implements _$$PaymentMethodUpdatedImplCopyWith<$Res> {
  __$$PaymentMethodUpdatedImplCopyWithImpl(_$PaymentMethodUpdatedImpl _value,
      $Res Function(_$PaymentMethodUpdatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? purchaseMethod = null,
  }) {
    return _then(_$PaymentMethodUpdatedImpl(
      purchaseMethod: null == purchaseMethod
          ? _value.purchaseMethod
          : purchaseMethod // ignore: cast_nullable_to_non_nullable
              as BuyCreditPaymentMethods,
    ));
  }
}

/// @nodoc

class _$PaymentMethodUpdatedImpl implements _PaymentMethodUpdated {
  const _$PaymentMethodUpdatedImpl(
      {this.purchaseMethod = BuyCreditPaymentMethods.others});

  @override
  @JsonKey()
  final BuyCreditPaymentMethods purchaseMethod;

  @override
  String toString() {
    return 'CheckoutEvent.paymentMethodUpdated(purchaseMethod: $purchaseMethod)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentMethodUpdatedImpl &&
            (identical(other.purchaseMethod, purchaseMethod) ||
                other.purchaseMethod == purchaseMethod));
  }

  @override
  int get hashCode => Object.hash(runtimeType, purchaseMethod);

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaymentMethodUpdatedImplCopyWith<_$PaymentMethodUpdatedImpl>
      get copyWith =>
          __$$PaymentMethodUpdatedImplCopyWithImpl<_$PaymentMethodUpdatedImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkoutInitiated,
    required TResult Function() checkoutReset,
    required TResult Function() checkoutRefreshed,
    required TResult Function(UserAttributesModel user) cardPaymentProcessed,
    required TResult Function() applePayPaymentProcessed,
    required TResult Function() googlePayPaymentProcessed,
    required TResult Function(String? value, CouponCodeInputStatus status)
        couponCodeUpdated,
    required TResult Function() couponApplied,
    required TResult Function(bool value) automaticCreditToggled,
    required TResult Function(String value) currencyCodeUpdated,
    required TResult Function(BuyCreditPaymentMethods purchaseMethod)
        paymentMethodUpdated,
    required TResult Function(BuyCreditResponse buyCreditOption)
        productAddedToCart,
    required TResult Function(BuyCreditResponse buyCreditOption,
            String currencyCode, String? couponCode)
        discountedCheckoutStarted,
    required TResult Function(String storeProductId) iapDetailsRefreshed,
    required TResult Function(List<PurchaseDetails> purchaseDetailsList)
        purchaseDetailsUpdated,
    required TResult Function() restorePurchases,
  }) {
    return paymentMethodUpdated(purchaseMethod);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkoutInitiated,
    TResult? Function()? checkoutReset,
    TResult? Function()? checkoutRefreshed,
    TResult? Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult? Function()? applePayPaymentProcessed,
    TResult? Function()? googlePayPaymentProcessed,
    TResult? Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult? Function()? couponApplied,
    TResult? Function(bool value)? automaticCreditToggled,
    TResult? Function(String value)? currencyCodeUpdated,
    TResult? Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult? Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult? Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult? Function(String storeProductId)? iapDetailsRefreshed,
    TResult? Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult? Function()? restorePurchases,
  }) {
    return paymentMethodUpdated?.call(purchaseMethod);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkoutInitiated,
    TResult Function()? checkoutReset,
    TResult Function()? checkoutRefreshed,
    TResult Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult Function()? applePayPaymentProcessed,
    TResult Function()? googlePayPaymentProcessed,
    TResult Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult Function()? couponApplied,
    TResult Function(bool value)? automaticCreditToggled,
    TResult Function(String value)? currencyCodeUpdated,
    TResult Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult Function(String storeProductId)? iapDetailsRefreshed,
    TResult Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult Function()? restorePurchases,
    required TResult orElse(),
  }) {
    if (paymentMethodUpdated != null) {
      return paymentMethodUpdated(purchaseMethod);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CheckoutInitiated value) checkoutInitiated,
    required TResult Function(_CheckoutReset value) checkoutReset,
    required TResult Function(_CheckoutRefreshed value) checkoutRefreshed,
    required TResult Function(_CardPaymentProcessed value) cardPaymentProcessed,
    required TResult Function(_ApplePayPaymentProcessed value)
        applePayPaymentProcessed,
    required TResult Function(_GooglePayPaymentProcessed value)
        googlePayPaymentProcessed,
    required TResult Function(_CouponCodeUpdated value) couponCodeUpdated,
    required TResult Function(_CouponApplied value) couponApplied,
    required TResult Function(_AutomaticCreditToggled value)
        automaticCreditToggled,
    required TResult Function(_CurrencyCodeUpdated value) currencyCodeUpdated,
    required TResult Function(_PaymentMethodUpdated value) paymentMethodUpdated,
    required TResult Function(_ProductAddedToCart value) productAddedToCart,
    required TResult Function(_DiscountedCheckoutStarted value)
        discountedCheckoutStarted,
    required TResult Function(_IapDetailsRefreshed value) iapDetailsRefreshed,
    required TResult Function(_PurchaseDetailsUpdated value)
        purchaseDetailsUpdated,
    required TResult Function(_RestorePurchases value) restorePurchases,
  }) {
    return paymentMethodUpdated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult? Function(_CheckoutReset value)? checkoutReset,
    TResult? Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult? Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult? Function(_ApplePayPaymentProcessed value)?
        applePayPaymentProcessed,
    TResult? Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult? Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult? Function(_CouponApplied value)? couponApplied,
    TResult? Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult? Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult? Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult? Function(_ProductAddedToCart value)? productAddedToCart,
    TResult? Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult? Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult? Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult? Function(_RestorePurchases value)? restorePurchases,
  }) {
    return paymentMethodUpdated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult Function(_CheckoutReset value)? checkoutReset,
    TResult Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult Function(_ApplePayPaymentProcessed value)? applePayPaymentProcessed,
    TResult Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult Function(_CouponApplied value)? couponApplied,
    TResult Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult Function(_ProductAddedToCart value)? productAddedToCart,
    TResult Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult Function(_RestorePurchases value)? restorePurchases,
    required TResult orElse(),
  }) {
    if (paymentMethodUpdated != null) {
      return paymentMethodUpdated(this);
    }
    return orElse();
  }
}

abstract class _PaymentMethodUpdated implements CheckoutEvent {
  const factory _PaymentMethodUpdated(
          {final BuyCreditPaymentMethods purchaseMethod}) =
      _$PaymentMethodUpdatedImpl;

  BuyCreditPaymentMethods get purchaseMethod;

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaymentMethodUpdatedImplCopyWith<_$PaymentMethodUpdatedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ProductAddedToCartImplCopyWith<$Res> {
  factory _$$ProductAddedToCartImplCopyWith(_$ProductAddedToCartImpl value,
          $Res Function(_$ProductAddedToCartImpl) then) =
      __$$ProductAddedToCartImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BuyCreditResponse buyCreditOption});

  $BuyCreditResponseCopyWith<$Res> get buyCreditOption;
}

/// @nodoc
class __$$ProductAddedToCartImplCopyWithImpl<$Res>
    extends _$CheckoutEventCopyWithImpl<$Res, _$ProductAddedToCartImpl>
    implements _$$ProductAddedToCartImplCopyWith<$Res> {
  __$$ProductAddedToCartImplCopyWithImpl(_$ProductAddedToCartImpl _value,
      $Res Function(_$ProductAddedToCartImpl) _then)
      : super(_value, _then);

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? buyCreditOption = null,
  }) {
    return _then(_$ProductAddedToCartImpl(
      buyCreditOption: null == buyCreditOption
          ? _value.buyCreditOption
          : buyCreditOption // ignore: cast_nullable_to_non_nullable
              as BuyCreditResponse,
    ));
  }

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BuyCreditResponseCopyWith<$Res> get buyCreditOption {
    return $BuyCreditResponseCopyWith<$Res>(_value.buyCreditOption, (value) {
      return _then(_value.copyWith(buyCreditOption: value));
    });
  }
}

/// @nodoc

class _$ProductAddedToCartImpl implements _ProductAddedToCart {
  const _$ProductAddedToCartImpl({required this.buyCreditOption});

  @override
  final BuyCreditResponse buyCreditOption;

  @override
  String toString() {
    return 'CheckoutEvent.productAddedToCart(buyCreditOption: $buyCreditOption)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductAddedToCartImpl &&
            (identical(other.buyCreditOption, buyCreditOption) ||
                other.buyCreditOption == buyCreditOption));
  }

  @override
  int get hashCode => Object.hash(runtimeType, buyCreditOption);

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductAddedToCartImplCopyWith<_$ProductAddedToCartImpl> get copyWith =>
      __$$ProductAddedToCartImplCopyWithImpl<_$ProductAddedToCartImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkoutInitiated,
    required TResult Function() checkoutReset,
    required TResult Function() checkoutRefreshed,
    required TResult Function(UserAttributesModel user) cardPaymentProcessed,
    required TResult Function() applePayPaymentProcessed,
    required TResult Function() googlePayPaymentProcessed,
    required TResult Function(String? value, CouponCodeInputStatus status)
        couponCodeUpdated,
    required TResult Function() couponApplied,
    required TResult Function(bool value) automaticCreditToggled,
    required TResult Function(String value) currencyCodeUpdated,
    required TResult Function(BuyCreditPaymentMethods purchaseMethod)
        paymentMethodUpdated,
    required TResult Function(BuyCreditResponse buyCreditOption)
        productAddedToCart,
    required TResult Function(BuyCreditResponse buyCreditOption,
            String currencyCode, String? couponCode)
        discountedCheckoutStarted,
    required TResult Function(String storeProductId) iapDetailsRefreshed,
    required TResult Function(List<PurchaseDetails> purchaseDetailsList)
        purchaseDetailsUpdated,
    required TResult Function() restorePurchases,
  }) {
    return productAddedToCart(buyCreditOption);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkoutInitiated,
    TResult? Function()? checkoutReset,
    TResult? Function()? checkoutRefreshed,
    TResult? Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult? Function()? applePayPaymentProcessed,
    TResult? Function()? googlePayPaymentProcessed,
    TResult? Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult? Function()? couponApplied,
    TResult? Function(bool value)? automaticCreditToggled,
    TResult? Function(String value)? currencyCodeUpdated,
    TResult? Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult? Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult? Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult? Function(String storeProductId)? iapDetailsRefreshed,
    TResult? Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult? Function()? restorePurchases,
  }) {
    return productAddedToCart?.call(buyCreditOption);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkoutInitiated,
    TResult Function()? checkoutReset,
    TResult Function()? checkoutRefreshed,
    TResult Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult Function()? applePayPaymentProcessed,
    TResult Function()? googlePayPaymentProcessed,
    TResult Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult Function()? couponApplied,
    TResult Function(bool value)? automaticCreditToggled,
    TResult Function(String value)? currencyCodeUpdated,
    TResult Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult Function(String storeProductId)? iapDetailsRefreshed,
    TResult Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult Function()? restorePurchases,
    required TResult orElse(),
  }) {
    if (productAddedToCart != null) {
      return productAddedToCart(buyCreditOption);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CheckoutInitiated value) checkoutInitiated,
    required TResult Function(_CheckoutReset value) checkoutReset,
    required TResult Function(_CheckoutRefreshed value) checkoutRefreshed,
    required TResult Function(_CardPaymentProcessed value) cardPaymentProcessed,
    required TResult Function(_ApplePayPaymentProcessed value)
        applePayPaymentProcessed,
    required TResult Function(_GooglePayPaymentProcessed value)
        googlePayPaymentProcessed,
    required TResult Function(_CouponCodeUpdated value) couponCodeUpdated,
    required TResult Function(_CouponApplied value) couponApplied,
    required TResult Function(_AutomaticCreditToggled value)
        automaticCreditToggled,
    required TResult Function(_CurrencyCodeUpdated value) currencyCodeUpdated,
    required TResult Function(_PaymentMethodUpdated value) paymentMethodUpdated,
    required TResult Function(_ProductAddedToCart value) productAddedToCart,
    required TResult Function(_DiscountedCheckoutStarted value)
        discountedCheckoutStarted,
    required TResult Function(_IapDetailsRefreshed value) iapDetailsRefreshed,
    required TResult Function(_PurchaseDetailsUpdated value)
        purchaseDetailsUpdated,
    required TResult Function(_RestorePurchases value) restorePurchases,
  }) {
    return productAddedToCart(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult? Function(_CheckoutReset value)? checkoutReset,
    TResult? Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult? Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult? Function(_ApplePayPaymentProcessed value)?
        applePayPaymentProcessed,
    TResult? Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult? Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult? Function(_CouponApplied value)? couponApplied,
    TResult? Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult? Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult? Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult? Function(_ProductAddedToCart value)? productAddedToCart,
    TResult? Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult? Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult? Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult? Function(_RestorePurchases value)? restorePurchases,
  }) {
    return productAddedToCart?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult Function(_CheckoutReset value)? checkoutReset,
    TResult Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult Function(_ApplePayPaymentProcessed value)? applePayPaymentProcessed,
    TResult Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult Function(_CouponApplied value)? couponApplied,
    TResult Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult Function(_ProductAddedToCart value)? productAddedToCart,
    TResult Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult Function(_RestorePurchases value)? restorePurchases,
    required TResult orElse(),
  }) {
    if (productAddedToCart != null) {
      return productAddedToCart(this);
    }
    return orElse();
  }
}

abstract class _ProductAddedToCart implements CheckoutEvent {
  const factory _ProductAddedToCart(
          {required final BuyCreditResponse buyCreditOption}) =
      _$ProductAddedToCartImpl;

  BuyCreditResponse get buyCreditOption;

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductAddedToCartImplCopyWith<_$ProductAddedToCartImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DiscountedCheckoutStartedImplCopyWith<$Res> {
  factory _$$DiscountedCheckoutStartedImplCopyWith(
          _$DiscountedCheckoutStartedImpl value,
          $Res Function(_$DiscountedCheckoutStartedImpl) then) =
      __$$DiscountedCheckoutStartedImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {BuyCreditResponse buyCreditOption,
      String currencyCode,
      String? couponCode});

  $BuyCreditResponseCopyWith<$Res> get buyCreditOption;
}

/// @nodoc
class __$$DiscountedCheckoutStartedImplCopyWithImpl<$Res>
    extends _$CheckoutEventCopyWithImpl<$Res, _$DiscountedCheckoutStartedImpl>
    implements _$$DiscountedCheckoutStartedImplCopyWith<$Res> {
  __$$DiscountedCheckoutStartedImplCopyWithImpl(
      _$DiscountedCheckoutStartedImpl _value,
      $Res Function(_$DiscountedCheckoutStartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? buyCreditOption = null,
    Object? currencyCode = null,
    Object? couponCode = freezed,
  }) {
    return _then(_$DiscountedCheckoutStartedImpl(
      buyCreditOption: null == buyCreditOption
          ? _value.buyCreditOption
          : buyCreditOption // ignore: cast_nullable_to_non_nullable
              as BuyCreditResponse,
      currencyCode: null == currencyCode
          ? _value.currencyCode
          : currencyCode // ignore: cast_nullable_to_non_nullable
              as String,
      couponCode: freezed == couponCode
          ? _value.couponCode
          : couponCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BuyCreditResponseCopyWith<$Res> get buyCreditOption {
    return $BuyCreditResponseCopyWith<$Res>(_value.buyCreditOption, (value) {
      return _then(_value.copyWith(buyCreditOption: value));
    });
  }
}

/// @nodoc

class _$DiscountedCheckoutStartedImpl implements _DiscountedCheckoutStarted {
  const _$DiscountedCheckoutStartedImpl(
      {required this.buyCreditOption,
      this.currencyCode = 'usd',
      this.couponCode});

  @override
  final BuyCreditResponse buyCreditOption;
  @override
  @JsonKey()
  final String currencyCode;
  @override
  final String? couponCode;

  @override
  String toString() {
    return 'CheckoutEvent.discountedCheckoutStarted(buyCreditOption: $buyCreditOption, currencyCode: $currencyCode, couponCode: $couponCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DiscountedCheckoutStartedImpl &&
            (identical(other.buyCreditOption, buyCreditOption) ||
                other.buyCreditOption == buyCreditOption) &&
            (identical(other.currencyCode, currencyCode) ||
                other.currencyCode == currencyCode) &&
            (identical(other.couponCode, couponCode) ||
                other.couponCode == couponCode));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, buyCreditOption, currencyCode, couponCode);

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DiscountedCheckoutStartedImplCopyWith<_$DiscountedCheckoutStartedImpl>
      get copyWith => __$$DiscountedCheckoutStartedImplCopyWithImpl<
          _$DiscountedCheckoutStartedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkoutInitiated,
    required TResult Function() checkoutReset,
    required TResult Function() checkoutRefreshed,
    required TResult Function(UserAttributesModel user) cardPaymentProcessed,
    required TResult Function() applePayPaymentProcessed,
    required TResult Function() googlePayPaymentProcessed,
    required TResult Function(String? value, CouponCodeInputStatus status)
        couponCodeUpdated,
    required TResult Function() couponApplied,
    required TResult Function(bool value) automaticCreditToggled,
    required TResult Function(String value) currencyCodeUpdated,
    required TResult Function(BuyCreditPaymentMethods purchaseMethod)
        paymentMethodUpdated,
    required TResult Function(BuyCreditResponse buyCreditOption)
        productAddedToCart,
    required TResult Function(BuyCreditResponse buyCreditOption,
            String currencyCode, String? couponCode)
        discountedCheckoutStarted,
    required TResult Function(String storeProductId) iapDetailsRefreshed,
    required TResult Function(List<PurchaseDetails> purchaseDetailsList)
        purchaseDetailsUpdated,
    required TResult Function() restorePurchases,
  }) {
    return discountedCheckoutStarted(buyCreditOption, currencyCode, couponCode);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkoutInitiated,
    TResult? Function()? checkoutReset,
    TResult? Function()? checkoutRefreshed,
    TResult? Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult? Function()? applePayPaymentProcessed,
    TResult? Function()? googlePayPaymentProcessed,
    TResult? Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult? Function()? couponApplied,
    TResult? Function(bool value)? automaticCreditToggled,
    TResult? Function(String value)? currencyCodeUpdated,
    TResult? Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult? Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult? Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult? Function(String storeProductId)? iapDetailsRefreshed,
    TResult? Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult? Function()? restorePurchases,
  }) {
    return discountedCheckoutStarted?.call(
        buyCreditOption, currencyCode, couponCode);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkoutInitiated,
    TResult Function()? checkoutReset,
    TResult Function()? checkoutRefreshed,
    TResult Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult Function()? applePayPaymentProcessed,
    TResult Function()? googlePayPaymentProcessed,
    TResult Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult Function()? couponApplied,
    TResult Function(bool value)? automaticCreditToggled,
    TResult Function(String value)? currencyCodeUpdated,
    TResult Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult Function(String storeProductId)? iapDetailsRefreshed,
    TResult Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult Function()? restorePurchases,
    required TResult orElse(),
  }) {
    if (discountedCheckoutStarted != null) {
      return discountedCheckoutStarted(
          buyCreditOption, currencyCode, couponCode);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CheckoutInitiated value) checkoutInitiated,
    required TResult Function(_CheckoutReset value) checkoutReset,
    required TResult Function(_CheckoutRefreshed value) checkoutRefreshed,
    required TResult Function(_CardPaymentProcessed value) cardPaymentProcessed,
    required TResult Function(_ApplePayPaymentProcessed value)
        applePayPaymentProcessed,
    required TResult Function(_GooglePayPaymentProcessed value)
        googlePayPaymentProcessed,
    required TResult Function(_CouponCodeUpdated value) couponCodeUpdated,
    required TResult Function(_CouponApplied value) couponApplied,
    required TResult Function(_AutomaticCreditToggled value)
        automaticCreditToggled,
    required TResult Function(_CurrencyCodeUpdated value) currencyCodeUpdated,
    required TResult Function(_PaymentMethodUpdated value) paymentMethodUpdated,
    required TResult Function(_ProductAddedToCart value) productAddedToCart,
    required TResult Function(_DiscountedCheckoutStarted value)
        discountedCheckoutStarted,
    required TResult Function(_IapDetailsRefreshed value) iapDetailsRefreshed,
    required TResult Function(_PurchaseDetailsUpdated value)
        purchaseDetailsUpdated,
    required TResult Function(_RestorePurchases value) restorePurchases,
  }) {
    return discountedCheckoutStarted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult? Function(_CheckoutReset value)? checkoutReset,
    TResult? Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult? Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult? Function(_ApplePayPaymentProcessed value)?
        applePayPaymentProcessed,
    TResult? Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult? Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult? Function(_CouponApplied value)? couponApplied,
    TResult? Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult? Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult? Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult? Function(_ProductAddedToCart value)? productAddedToCart,
    TResult? Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult? Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult? Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult? Function(_RestorePurchases value)? restorePurchases,
  }) {
    return discountedCheckoutStarted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult Function(_CheckoutReset value)? checkoutReset,
    TResult Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult Function(_ApplePayPaymentProcessed value)? applePayPaymentProcessed,
    TResult Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult Function(_CouponApplied value)? couponApplied,
    TResult Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult Function(_ProductAddedToCart value)? productAddedToCart,
    TResult Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult Function(_RestorePurchases value)? restorePurchases,
    required TResult orElse(),
  }) {
    if (discountedCheckoutStarted != null) {
      return discountedCheckoutStarted(this);
    }
    return orElse();
  }
}

abstract class _DiscountedCheckoutStarted implements CheckoutEvent {
  const factory _DiscountedCheckoutStarted(
      {required final BuyCreditResponse buyCreditOption,
      final String currencyCode,
      final String? couponCode}) = _$DiscountedCheckoutStartedImpl;

  BuyCreditResponse get buyCreditOption;
  String get currencyCode;
  String? get couponCode;

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DiscountedCheckoutStartedImplCopyWith<_$DiscountedCheckoutStartedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$IapDetailsRefreshedImplCopyWith<$Res> {
  factory _$$IapDetailsRefreshedImplCopyWith(_$IapDetailsRefreshedImpl value,
          $Res Function(_$IapDetailsRefreshedImpl) then) =
      __$$IapDetailsRefreshedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String storeProductId});
}

/// @nodoc
class __$$IapDetailsRefreshedImplCopyWithImpl<$Res>
    extends _$CheckoutEventCopyWithImpl<$Res, _$IapDetailsRefreshedImpl>
    implements _$$IapDetailsRefreshedImplCopyWith<$Res> {
  __$$IapDetailsRefreshedImplCopyWithImpl(_$IapDetailsRefreshedImpl _value,
      $Res Function(_$IapDetailsRefreshedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? storeProductId = null,
  }) {
    return _then(_$IapDetailsRefreshedImpl(
      null == storeProductId
          ? _value.storeProductId
          : storeProductId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$IapDetailsRefreshedImpl implements _IapDetailsRefreshed {
  const _$IapDetailsRefreshedImpl(this.storeProductId);

  @override
  final String storeProductId;

  @override
  String toString() {
    return 'CheckoutEvent.iapDetailsRefreshed(storeProductId: $storeProductId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IapDetailsRefreshedImpl &&
            (identical(other.storeProductId, storeProductId) ||
                other.storeProductId == storeProductId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, storeProductId);

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$IapDetailsRefreshedImplCopyWith<_$IapDetailsRefreshedImpl> get copyWith =>
      __$$IapDetailsRefreshedImplCopyWithImpl<_$IapDetailsRefreshedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkoutInitiated,
    required TResult Function() checkoutReset,
    required TResult Function() checkoutRefreshed,
    required TResult Function(UserAttributesModel user) cardPaymentProcessed,
    required TResult Function() applePayPaymentProcessed,
    required TResult Function() googlePayPaymentProcessed,
    required TResult Function(String? value, CouponCodeInputStatus status)
        couponCodeUpdated,
    required TResult Function() couponApplied,
    required TResult Function(bool value) automaticCreditToggled,
    required TResult Function(String value) currencyCodeUpdated,
    required TResult Function(BuyCreditPaymentMethods purchaseMethod)
        paymentMethodUpdated,
    required TResult Function(BuyCreditResponse buyCreditOption)
        productAddedToCart,
    required TResult Function(BuyCreditResponse buyCreditOption,
            String currencyCode, String? couponCode)
        discountedCheckoutStarted,
    required TResult Function(String storeProductId) iapDetailsRefreshed,
    required TResult Function(List<PurchaseDetails> purchaseDetailsList)
        purchaseDetailsUpdated,
    required TResult Function() restorePurchases,
  }) {
    return iapDetailsRefreshed(storeProductId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkoutInitiated,
    TResult? Function()? checkoutReset,
    TResult? Function()? checkoutRefreshed,
    TResult? Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult? Function()? applePayPaymentProcessed,
    TResult? Function()? googlePayPaymentProcessed,
    TResult? Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult? Function()? couponApplied,
    TResult? Function(bool value)? automaticCreditToggled,
    TResult? Function(String value)? currencyCodeUpdated,
    TResult? Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult? Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult? Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult? Function(String storeProductId)? iapDetailsRefreshed,
    TResult? Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult? Function()? restorePurchases,
  }) {
    return iapDetailsRefreshed?.call(storeProductId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkoutInitiated,
    TResult Function()? checkoutReset,
    TResult Function()? checkoutRefreshed,
    TResult Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult Function()? applePayPaymentProcessed,
    TResult Function()? googlePayPaymentProcessed,
    TResult Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult Function()? couponApplied,
    TResult Function(bool value)? automaticCreditToggled,
    TResult Function(String value)? currencyCodeUpdated,
    TResult Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult Function(String storeProductId)? iapDetailsRefreshed,
    TResult Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult Function()? restorePurchases,
    required TResult orElse(),
  }) {
    if (iapDetailsRefreshed != null) {
      return iapDetailsRefreshed(storeProductId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CheckoutInitiated value) checkoutInitiated,
    required TResult Function(_CheckoutReset value) checkoutReset,
    required TResult Function(_CheckoutRefreshed value) checkoutRefreshed,
    required TResult Function(_CardPaymentProcessed value) cardPaymentProcessed,
    required TResult Function(_ApplePayPaymentProcessed value)
        applePayPaymentProcessed,
    required TResult Function(_GooglePayPaymentProcessed value)
        googlePayPaymentProcessed,
    required TResult Function(_CouponCodeUpdated value) couponCodeUpdated,
    required TResult Function(_CouponApplied value) couponApplied,
    required TResult Function(_AutomaticCreditToggled value)
        automaticCreditToggled,
    required TResult Function(_CurrencyCodeUpdated value) currencyCodeUpdated,
    required TResult Function(_PaymentMethodUpdated value) paymentMethodUpdated,
    required TResult Function(_ProductAddedToCart value) productAddedToCart,
    required TResult Function(_DiscountedCheckoutStarted value)
        discountedCheckoutStarted,
    required TResult Function(_IapDetailsRefreshed value) iapDetailsRefreshed,
    required TResult Function(_PurchaseDetailsUpdated value)
        purchaseDetailsUpdated,
    required TResult Function(_RestorePurchases value) restorePurchases,
  }) {
    return iapDetailsRefreshed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult? Function(_CheckoutReset value)? checkoutReset,
    TResult? Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult? Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult? Function(_ApplePayPaymentProcessed value)?
        applePayPaymentProcessed,
    TResult? Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult? Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult? Function(_CouponApplied value)? couponApplied,
    TResult? Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult? Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult? Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult? Function(_ProductAddedToCart value)? productAddedToCart,
    TResult? Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult? Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult? Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult? Function(_RestorePurchases value)? restorePurchases,
  }) {
    return iapDetailsRefreshed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult Function(_CheckoutReset value)? checkoutReset,
    TResult Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult Function(_ApplePayPaymentProcessed value)? applePayPaymentProcessed,
    TResult Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult Function(_CouponApplied value)? couponApplied,
    TResult Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult Function(_ProductAddedToCart value)? productAddedToCart,
    TResult Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult Function(_RestorePurchases value)? restorePurchases,
    required TResult orElse(),
  }) {
    if (iapDetailsRefreshed != null) {
      return iapDetailsRefreshed(this);
    }
    return orElse();
  }
}

abstract class _IapDetailsRefreshed implements CheckoutEvent {
  const factory _IapDetailsRefreshed(final String storeProductId) =
      _$IapDetailsRefreshedImpl;

  String get storeProductId;

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$IapDetailsRefreshedImplCopyWith<_$IapDetailsRefreshedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PurchaseDetailsUpdatedImplCopyWith<$Res> {
  factory _$$PurchaseDetailsUpdatedImplCopyWith(
          _$PurchaseDetailsUpdatedImpl value,
          $Res Function(_$PurchaseDetailsUpdatedImpl) then) =
      __$$PurchaseDetailsUpdatedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<PurchaseDetails> purchaseDetailsList});
}

/// @nodoc
class __$$PurchaseDetailsUpdatedImplCopyWithImpl<$Res>
    extends _$CheckoutEventCopyWithImpl<$Res, _$PurchaseDetailsUpdatedImpl>
    implements _$$PurchaseDetailsUpdatedImplCopyWith<$Res> {
  __$$PurchaseDetailsUpdatedImplCopyWithImpl(
      _$PurchaseDetailsUpdatedImpl _value,
      $Res Function(_$PurchaseDetailsUpdatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? purchaseDetailsList = null,
  }) {
    return _then(_$PurchaseDetailsUpdatedImpl(
      null == purchaseDetailsList
          ? _value._purchaseDetailsList
          : purchaseDetailsList // ignore: cast_nullable_to_non_nullable
              as List<PurchaseDetails>,
    ));
  }
}

/// @nodoc

class _$PurchaseDetailsUpdatedImpl implements _PurchaseDetailsUpdated {
  const _$PurchaseDetailsUpdatedImpl(
      final List<PurchaseDetails> purchaseDetailsList)
      : _purchaseDetailsList = purchaseDetailsList;

  final List<PurchaseDetails> _purchaseDetailsList;
  @override
  List<PurchaseDetails> get purchaseDetailsList {
    if (_purchaseDetailsList is EqualUnmodifiableListView)
      return _purchaseDetailsList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_purchaseDetailsList);
  }

  @override
  String toString() {
    return 'CheckoutEvent.purchaseDetailsUpdated(purchaseDetailsList: $purchaseDetailsList)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PurchaseDetailsUpdatedImpl &&
            const DeepCollectionEquality()
                .equals(other._purchaseDetailsList, _purchaseDetailsList));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_purchaseDetailsList));

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PurchaseDetailsUpdatedImplCopyWith<_$PurchaseDetailsUpdatedImpl>
      get copyWith => __$$PurchaseDetailsUpdatedImplCopyWithImpl<
          _$PurchaseDetailsUpdatedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkoutInitiated,
    required TResult Function() checkoutReset,
    required TResult Function() checkoutRefreshed,
    required TResult Function(UserAttributesModel user) cardPaymentProcessed,
    required TResult Function() applePayPaymentProcessed,
    required TResult Function() googlePayPaymentProcessed,
    required TResult Function(String? value, CouponCodeInputStatus status)
        couponCodeUpdated,
    required TResult Function() couponApplied,
    required TResult Function(bool value) automaticCreditToggled,
    required TResult Function(String value) currencyCodeUpdated,
    required TResult Function(BuyCreditPaymentMethods purchaseMethod)
        paymentMethodUpdated,
    required TResult Function(BuyCreditResponse buyCreditOption)
        productAddedToCart,
    required TResult Function(BuyCreditResponse buyCreditOption,
            String currencyCode, String? couponCode)
        discountedCheckoutStarted,
    required TResult Function(String storeProductId) iapDetailsRefreshed,
    required TResult Function(List<PurchaseDetails> purchaseDetailsList)
        purchaseDetailsUpdated,
    required TResult Function() restorePurchases,
  }) {
    return purchaseDetailsUpdated(purchaseDetailsList);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkoutInitiated,
    TResult? Function()? checkoutReset,
    TResult? Function()? checkoutRefreshed,
    TResult? Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult? Function()? applePayPaymentProcessed,
    TResult? Function()? googlePayPaymentProcessed,
    TResult? Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult? Function()? couponApplied,
    TResult? Function(bool value)? automaticCreditToggled,
    TResult? Function(String value)? currencyCodeUpdated,
    TResult? Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult? Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult? Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult? Function(String storeProductId)? iapDetailsRefreshed,
    TResult? Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult? Function()? restorePurchases,
  }) {
    return purchaseDetailsUpdated?.call(purchaseDetailsList);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkoutInitiated,
    TResult Function()? checkoutReset,
    TResult Function()? checkoutRefreshed,
    TResult Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult Function()? applePayPaymentProcessed,
    TResult Function()? googlePayPaymentProcessed,
    TResult Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult Function()? couponApplied,
    TResult Function(bool value)? automaticCreditToggled,
    TResult Function(String value)? currencyCodeUpdated,
    TResult Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult Function(String storeProductId)? iapDetailsRefreshed,
    TResult Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult Function()? restorePurchases,
    required TResult orElse(),
  }) {
    if (purchaseDetailsUpdated != null) {
      return purchaseDetailsUpdated(purchaseDetailsList);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CheckoutInitiated value) checkoutInitiated,
    required TResult Function(_CheckoutReset value) checkoutReset,
    required TResult Function(_CheckoutRefreshed value) checkoutRefreshed,
    required TResult Function(_CardPaymentProcessed value) cardPaymentProcessed,
    required TResult Function(_ApplePayPaymentProcessed value)
        applePayPaymentProcessed,
    required TResult Function(_GooglePayPaymentProcessed value)
        googlePayPaymentProcessed,
    required TResult Function(_CouponCodeUpdated value) couponCodeUpdated,
    required TResult Function(_CouponApplied value) couponApplied,
    required TResult Function(_AutomaticCreditToggled value)
        automaticCreditToggled,
    required TResult Function(_CurrencyCodeUpdated value) currencyCodeUpdated,
    required TResult Function(_PaymentMethodUpdated value) paymentMethodUpdated,
    required TResult Function(_ProductAddedToCart value) productAddedToCart,
    required TResult Function(_DiscountedCheckoutStarted value)
        discountedCheckoutStarted,
    required TResult Function(_IapDetailsRefreshed value) iapDetailsRefreshed,
    required TResult Function(_PurchaseDetailsUpdated value)
        purchaseDetailsUpdated,
    required TResult Function(_RestorePurchases value) restorePurchases,
  }) {
    return purchaseDetailsUpdated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult? Function(_CheckoutReset value)? checkoutReset,
    TResult? Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult? Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult? Function(_ApplePayPaymentProcessed value)?
        applePayPaymentProcessed,
    TResult? Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult? Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult? Function(_CouponApplied value)? couponApplied,
    TResult? Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult? Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult? Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult? Function(_ProductAddedToCart value)? productAddedToCart,
    TResult? Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult? Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult? Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult? Function(_RestorePurchases value)? restorePurchases,
  }) {
    return purchaseDetailsUpdated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult Function(_CheckoutReset value)? checkoutReset,
    TResult Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult Function(_ApplePayPaymentProcessed value)? applePayPaymentProcessed,
    TResult Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult Function(_CouponApplied value)? couponApplied,
    TResult Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult Function(_ProductAddedToCart value)? productAddedToCart,
    TResult Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult Function(_RestorePurchases value)? restorePurchases,
    required TResult orElse(),
  }) {
    if (purchaseDetailsUpdated != null) {
      return purchaseDetailsUpdated(this);
    }
    return orElse();
  }
}

abstract class _PurchaseDetailsUpdated implements CheckoutEvent {
  const factory _PurchaseDetailsUpdated(
          final List<PurchaseDetails> purchaseDetailsList) =
      _$PurchaseDetailsUpdatedImpl;

  List<PurchaseDetails> get purchaseDetailsList;

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PurchaseDetailsUpdatedImplCopyWith<_$PurchaseDetailsUpdatedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RestorePurchasesImplCopyWith<$Res> {
  factory _$$RestorePurchasesImplCopyWith(_$RestorePurchasesImpl value,
          $Res Function(_$RestorePurchasesImpl) then) =
      __$$RestorePurchasesImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$RestorePurchasesImplCopyWithImpl<$Res>
    extends _$CheckoutEventCopyWithImpl<$Res, _$RestorePurchasesImpl>
    implements _$$RestorePurchasesImplCopyWith<$Res> {
  __$$RestorePurchasesImplCopyWithImpl(_$RestorePurchasesImpl _value,
      $Res Function(_$RestorePurchasesImpl) _then)
      : super(_value, _then);

  /// Create a copy of CheckoutEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$RestorePurchasesImpl implements _RestorePurchases {
  const _$RestorePurchasesImpl();

  @override
  String toString() {
    return 'CheckoutEvent.restorePurchases()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$RestorePurchasesImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() checkoutInitiated,
    required TResult Function() checkoutReset,
    required TResult Function() checkoutRefreshed,
    required TResult Function(UserAttributesModel user) cardPaymentProcessed,
    required TResult Function() applePayPaymentProcessed,
    required TResult Function() googlePayPaymentProcessed,
    required TResult Function(String? value, CouponCodeInputStatus status)
        couponCodeUpdated,
    required TResult Function() couponApplied,
    required TResult Function(bool value) automaticCreditToggled,
    required TResult Function(String value) currencyCodeUpdated,
    required TResult Function(BuyCreditPaymentMethods purchaseMethod)
        paymentMethodUpdated,
    required TResult Function(BuyCreditResponse buyCreditOption)
        productAddedToCart,
    required TResult Function(BuyCreditResponse buyCreditOption,
            String currencyCode, String? couponCode)
        discountedCheckoutStarted,
    required TResult Function(String storeProductId) iapDetailsRefreshed,
    required TResult Function(List<PurchaseDetails> purchaseDetailsList)
        purchaseDetailsUpdated,
    required TResult Function() restorePurchases,
  }) {
    return restorePurchases();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? checkoutInitiated,
    TResult? Function()? checkoutReset,
    TResult? Function()? checkoutRefreshed,
    TResult? Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult? Function()? applePayPaymentProcessed,
    TResult? Function()? googlePayPaymentProcessed,
    TResult? Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult? Function()? couponApplied,
    TResult? Function(bool value)? automaticCreditToggled,
    TResult? Function(String value)? currencyCodeUpdated,
    TResult? Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult? Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult? Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult? Function(String storeProductId)? iapDetailsRefreshed,
    TResult? Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult? Function()? restorePurchases,
  }) {
    return restorePurchases?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? checkoutInitiated,
    TResult Function()? checkoutReset,
    TResult Function()? checkoutRefreshed,
    TResult Function(UserAttributesModel user)? cardPaymentProcessed,
    TResult Function()? applePayPaymentProcessed,
    TResult Function()? googlePayPaymentProcessed,
    TResult Function(String? value, CouponCodeInputStatus status)?
        couponCodeUpdated,
    TResult Function()? couponApplied,
    TResult Function(bool value)? automaticCreditToggled,
    TResult Function(String value)? currencyCodeUpdated,
    TResult Function(BuyCreditPaymentMethods purchaseMethod)?
        paymentMethodUpdated,
    TResult Function(BuyCreditResponse buyCreditOption)? productAddedToCart,
    TResult Function(BuyCreditResponse buyCreditOption, String currencyCode,
            String? couponCode)?
        discountedCheckoutStarted,
    TResult Function(String storeProductId)? iapDetailsRefreshed,
    TResult Function(List<PurchaseDetails> purchaseDetailsList)?
        purchaseDetailsUpdated,
    TResult Function()? restorePurchases,
    required TResult orElse(),
  }) {
    if (restorePurchases != null) {
      return restorePurchases();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CheckoutInitiated value) checkoutInitiated,
    required TResult Function(_CheckoutReset value) checkoutReset,
    required TResult Function(_CheckoutRefreshed value) checkoutRefreshed,
    required TResult Function(_CardPaymentProcessed value) cardPaymentProcessed,
    required TResult Function(_ApplePayPaymentProcessed value)
        applePayPaymentProcessed,
    required TResult Function(_GooglePayPaymentProcessed value)
        googlePayPaymentProcessed,
    required TResult Function(_CouponCodeUpdated value) couponCodeUpdated,
    required TResult Function(_CouponApplied value) couponApplied,
    required TResult Function(_AutomaticCreditToggled value)
        automaticCreditToggled,
    required TResult Function(_CurrencyCodeUpdated value) currencyCodeUpdated,
    required TResult Function(_PaymentMethodUpdated value) paymentMethodUpdated,
    required TResult Function(_ProductAddedToCart value) productAddedToCart,
    required TResult Function(_DiscountedCheckoutStarted value)
        discountedCheckoutStarted,
    required TResult Function(_IapDetailsRefreshed value) iapDetailsRefreshed,
    required TResult Function(_PurchaseDetailsUpdated value)
        purchaseDetailsUpdated,
    required TResult Function(_RestorePurchases value) restorePurchases,
  }) {
    return restorePurchases(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult? Function(_CheckoutReset value)? checkoutReset,
    TResult? Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult? Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult? Function(_ApplePayPaymentProcessed value)?
        applePayPaymentProcessed,
    TResult? Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult? Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult? Function(_CouponApplied value)? couponApplied,
    TResult? Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult? Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult? Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult? Function(_ProductAddedToCart value)? productAddedToCart,
    TResult? Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult? Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult? Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult? Function(_RestorePurchases value)? restorePurchases,
  }) {
    return restorePurchases?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CheckoutInitiated value)? checkoutInitiated,
    TResult Function(_CheckoutReset value)? checkoutReset,
    TResult Function(_CheckoutRefreshed value)? checkoutRefreshed,
    TResult Function(_CardPaymentProcessed value)? cardPaymentProcessed,
    TResult Function(_ApplePayPaymentProcessed value)? applePayPaymentProcessed,
    TResult Function(_GooglePayPaymentProcessed value)?
        googlePayPaymentProcessed,
    TResult Function(_CouponCodeUpdated value)? couponCodeUpdated,
    TResult Function(_CouponApplied value)? couponApplied,
    TResult Function(_AutomaticCreditToggled value)? automaticCreditToggled,
    TResult Function(_CurrencyCodeUpdated value)? currencyCodeUpdated,
    TResult Function(_PaymentMethodUpdated value)? paymentMethodUpdated,
    TResult Function(_ProductAddedToCart value)? productAddedToCart,
    TResult Function(_DiscountedCheckoutStarted value)?
        discountedCheckoutStarted,
    TResult Function(_IapDetailsRefreshed value)? iapDetailsRefreshed,
    TResult Function(_PurchaseDetailsUpdated value)? purchaseDetailsUpdated,
    TResult Function(_RestorePurchases value)? restorePurchases,
    required TResult orElse(),
  }) {
    if (restorePurchases != null) {
      return restorePurchases(this);
    }
    return orElse();
  }
}

abstract class _RestorePurchases implements CheckoutEvent {
  const factory _RestorePurchases() = _$RestorePurchasesImpl;
}

/// @nodoc
mixin _$CheckoutState {
  CheckoutSessionResponse? get session => throw _privateConstructorUsedError;
  String? get checkoutUrl => throw _privateConstructorUsedError;
  CouponCodeInput get couponCode => throw _privateConstructorUsedError;
  bool get autoCredit => throw _privateConstructorUsedError;
  bool get isStoreAvailable => throw _privateConstructorUsedError;
  BuyCreditResponse? get buyCreditOption => throw _privateConstructorUsedError;
  String get currencyCode => throw _privateConstructorUsedError;
  BuyCreditPaymentMethods get paymentMethod =>
      throw _privateConstructorUsedError;
  FormzSubmissionStatus get status => throw _privateConstructorUsedError;
  PaymentSheetStatus get paymentSheetStatus =>
      throw _privateConstructorUsedError;
  CouponCodeInputStatus get couponStatus => throw _privateConstructorUsedError;
  String? get message => throw _privateConstructorUsedError;
  String? get couponMessage => throw _privateConstructorUsedError;
  ProductDetails? get iapProductDetails => throw _privateConstructorUsedError;

  /// Create a copy of CheckoutState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CheckoutStateCopyWith<CheckoutState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CheckoutStateCopyWith<$Res> {
  factory $CheckoutStateCopyWith(
          CheckoutState value, $Res Function(CheckoutState) then) =
      _$CheckoutStateCopyWithImpl<$Res, CheckoutState>;
  @useResult
  $Res call(
      {CheckoutSessionResponse? session,
      String? checkoutUrl,
      CouponCodeInput couponCode,
      bool autoCredit,
      bool isStoreAvailable,
      BuyCreditResponse? buyCreditOption,
      String currencyCode,
      BuyCreditPaymentMethods paymentMethod,
      FormzSubmissionStatus status,
      PaymentSheetStatus paymentSheetStatus,
      CouponCodeInputStatus couponStatus,
      String? message,
      String? couponMessage,
      ProductDetails? iapProductDetails});

  $CheckoutSessionResponseCopyWith<$Res>? get session;
  $BuyCreditResponseCopyWith<$Res>? get buyCreditOption;
}

/// @nodoc
class _$CheckoutStateCopyWithImpl<$Res, $Val extends CheckoutState>
    implements $CheckoutStateCopyWith<$Res> {
  _$CheckoutStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CheckoutState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? session = freezed,
    Object? checkoutUrl = freezed,
    Object? couponCode = null,
    Object? autoCredit = null,
    Object? isStoreAvailable = null,
    Object? buyCreditOption = freezed,
    Object? currencyCode = null,
    Object? paymentMethod = null,
    Object? status = null,
    Object? paymentSheetStatus = null,
    Object? couponStatus = null,
    Object? message = freezed,
    Object? couponMessage = freezed,
    Object? iapProductDetails = freezed,
  }) {
    return _then(_value.copyWith(
      session: freezed == session
          ? _value.session
          : session // ignore: cast_nullable_to_non_nullable
              as CheckoutSessionResponse?,
      checkoutUrl: freezed == checkoutUrl
          ? _value.checkoutUrl
          : checkoutUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      couponCode: null == couponCode
          ? _value.couponCode
          : couponCode // ignore: cast_nullable_to_non_nullable
              as CouponCodeInput,
      autoCredit: null == autoCredit
          ? _value.autoCredit
          : autoCredit // ignore: cast_nullable_to_non_nullable
              as bool,
      isStoreAvailable: null == isStoreAvailable
          ? _value.isStoreAvailable
          : isStoreAvailable // ignore: cast_nullable_to_non_nullable
              as bool,
      buyCreditOption: freezed == buyCreditOption
          ? _value.buyCreditOption
          : buyCreditOption // ignore: cast_nullable_to_non_nullable
              as BuyCreditResponse?,
      currencyCode: null == currencyCode
          ? _value.currencyCode
          : currencyCode // ignore: cast_nullable_to_non_nullable
              as String,
      paymentMethod: null == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as BuyCreditPaymentMethods,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as FormzSubmissionStatus,
      paymentSheetStatus: null == paymentSheetStatus
          ? _value.paymentSheetStatus
          : paymentSheetStatus // ignore: cast_nullable_to_non_nullable
              as PaymentSheetStatus,
      couponStatus: null == couponStatus
          ? _value.couponStatus
          : couponStatus // ignore: cast_nullable_to_non_nullable
              as CouponCodeInputStatus,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      couponMessage: freezed == couponMessage
          ? _value.couponMessage
          : couponMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      iapProductDetails: freezed == iapProductDetails
          ? _value.iapProductDetails
          : iapProductDetails // ignore: cast_nullable_to_non_nullable
              as ProductDetails?,
    ) as $Val);
  }

  /// Create a copy of CheckoutState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CheckoutSessionResponseCopyWith<$Res>? get session {
    if (_value.session == null) {
      return null;
    }

    return $CheckoutSessionResponseCopyWith<$Res>(_value.session!, (value) {
      return _then(_value.copyWith(session: value) as $Val);
    });
  }

  /// Create a copy of CheckoutState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BuyCreditResponseCopyWith<$Res>? get buyCreditOption {
    if (_value.buyCreditOption == null) {
      return null;
    }

    return $BuyCreditResponseCopyWith<$Res>(_value.buyCreditOption!, (value) {
      return _then(_value.copyWith(buyCreditOption: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CheckoutStateImplCopyWith<$Res>
    implements $CheckoutStateCopyWith<$Res> {
  factory _$$CheckoutStateImplCopyWith(
          _$CheckoutStateImpl value, $Res Function(_$CheckoutStateImpl) then) =
      __$$CheckoutStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {CheckoutSessionResponse? session,
      String? checkoutUrl,
      CouponCodeInput couponCode,
      bool autoCredit,
      bool isStoreAvailable,
      BuyCreditResponse? buyCreditOption,
      String currencyCode,
      BuyCreditPaymentMethods paymentMethod,
      FormzSubmissionStatus status,
      PaymentSheetStatus paymentSheetStatus,
      CouponCodeInputStatus couponStatus,
      String? message,
      String? couponMessage,
      ProductDetails? iapProductDetails});

  @override
  $CheckoutSessionResponseCopyWith<$Res>? get session;
  @override
  $BuyCreditResponseCopyWith<$Res>? get buyCreditOption;
}

/// @nodoc
class __$$CheckoutStateImplCopyWithImpl<$Res>
    extends _$CheckoutStateCopyWithImpl<$Res, _$CheckoutStateImpl>
    implements _$$CheckoutStateImplCopyWith<$Res> {
  __$$CheckoutStateImplCopyWithImpl(
      _$CheckoutStateImpl _value, $Res Function(_$CheckoutStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CheckoutState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? session = freezed,
    Object? checkoutUrl = freezed,
    Object? couponCode = null,
    Object? autoCredit = null,
    Object? isStoreAvailable = null,
    Object? buyCreditOption = freezed,
    Object? currencyCode = null,
    Object? paymentMethod = null,
    Object? status = null,
    Object? paymentSheetStatus = null,
    Object? couponStatus = null,
    Object? message = freezed,
    Object? couponMessage = freezed,
    Object? iapProductDetails = freezed,
  }) {
    return _then(_$CheckoutStateImpl(
      session: freezed == session
          ? _value.session
          : session // ignore: cast_nullable_to_non_nullable
              as CheckoutSessionResponse?,
      checkoutUrl: freezed == checkoutUrl
          ? _value.checkoutUrl
          : checkoutUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      couponCode: null == couponCode
          ? _value.couponCode
          : couponCode // ignore: cast_nullable_to_non_nullable
              as CouponCodeInput,
      autoCredit: null == autoCredit
          ? _value.autoCredit
          : autoCredit // ignore: cast_nullable_to_non_nullable
              as bool,
      isStoreAvailable: null == isStoreAvailable
          ? _value.isStoreAvailable
          : isStoreAvailable // ignore: cast_nullable_to_non_nullable
              as bool,
      buyCreditOption: freezed == buyCreditOption
          ? _value.buyCreditOption
          : buyCreditOption // ignore: cast_nullable_to_non_nullable
              as BuyCreditResponse?,
      currencyCode: null == currencyCode
          ? _value.currencyCode
          : currencyCode // ignore: cast_nullable_to_non_nullable
              as String,
      paymentMethod: null == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as BuyCreditPaymentMethods,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as FormzSubmissionStatus,
      paymentSheetStatus: null == paymentSheetStatus
          ? _value.paymentSheetStatus
          : paymentSheetStatus // ignore: cast_nullable_to_non_nullable
              as PaymentSheetStatus,
      couponStatus: null == couponStatus
          ? _value.couponStatus
          : couponStatus // ignore: cast_nullable_to_non_nullable
              as CouponCodeInputStatus,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      couponMessage: freezed == couponMessage
          ? _value.couponMessage
          : couponMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      iapProductDetails: freezed == iapProductDetails
          ? _value.iapProductDetails
          : iapProductDetails // ignore: cast_nullable_to_non_nullable
              as ProductDetails?,
    ));
  }
}

/// @nodoc

class _$CheckoutStateImpl extends _CheckoutState {
  _$CheckoutStateImpl(
      {this.session,
      this.checkoutUrl,
      this.couponCode = const CouponCodeInput.pure(),
      this.autoCredit = false,
      this.isStoreAvailable = false,
      this.buyCreditOption,
      this.currencyCode = 'USD',
      this.paymentMethod = BuyCreditPaymentMethods.others,
      this.status = FormzSubmissionStatus.initial,
      this.paymentSheetStatus = PaymentSheetStatus.initial,
      this.couponStatus = CouponCodeInputStatus.initial,
      this.message,
      this.couponMessage,
      this.iapProductDetails})
      : super._();

  @override
  final CheckoutSessionResponse? session;
  @override
  final String? checkoutUrl;
  @override
  @JsonKey()
  final CouponCodeInput couponCode;
  @override
  @JsonKey()
  final bool autoCredit;
  @override
  @JsonKey()
  final bool isStoreAvailable;
  @override
  final BuyCreditResponse? buyCreditOption;
  @override
  @JsonKey()
  final String currencyCode;
  @override
  @JsonKey()
  final BuyCreditPaymentMethods paymentMethod;
  @override
  @JsonKey()
  final FormzSubmissionStatus status;
  @override
  @JsonKey()
  final PaymentSheetStatus paymentSheetStatus;
  @override
  @JsonKey()
  final CouponCodeInputStatus couponStatus;
  @override
  final String? message;
  @override
  final String? couponMessage;
  @override
  final ProductDetails? iapProductDetails;

  @override
  String toString() {
    return 'CheckoutState(session: $session, checkoutUrl: $checkoutUrl, couponCode: $couponCode, autoCredit: $autoCredit, isStoreAvailable: $isStoreAvailable, buyCreditOption: $buyCreditOption, currencyCode: $currencyCode, paymentMethod: $paymentMethod, status: $status, paymentSheetStatus: $paymentSheetStatus, couponStatus: $couponStatus, message: $message, couponMessage: $couponMessage, iapProductDetails: $iapProductDetails)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CheckoutStateImpl &&
            (identical(other.session, session) || other.session == session) &&
            (identical(other.checkoutUrl, checkoutUrl) ||
                other.checkoutUrl == checkoutUrl) &&
            (identical(other.couponCode, couponCode) ||
                other.couponCode == couponCode) &&
            (identical(other.autoCredit, autoCredit) ||
                other.autoCredit == autoCredit) &&
            (identical(other.isStoreAvailable, isStoreAvailable) ||
                other.isStoreAvailable == isStoreAvailable) &&
            (identical(other.buyCreditOption, buyCreditOption) ||
                other.buyCreditOption == buyCreditOption) &&
            (identical(other.currencyCode, currencyCode) ||
                other.currencyCode == currencyCode) &&
            (identical(other.paymentMethod, paymentMethod) ||
                other.paymentMethod == paymentMethod) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.paymentSheetStatus, paymentSheetStatus) ||
                other.paymentSheetStatus == paymentSheetStatus) &&
            (identical(other.couponStatus, couponStatus) ||
                other.couponStatus == couponStatus) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.couponMessage, couponMessage) ||
                other.couponMessage == couponMessage) &&
            (identical(other.iapProductDetails, iapProductDetails) ||
                other.iapProductDetails == iapProductDetails));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      session,
      checkoutUrl,
      couponCode,
      autoCredit,
      isStoreAvailable,
      buyCreditOption,
      currencyCode,
      paymentMethod,
      status,
      paymentSheetStatus,
      couponStatus,
      message,
      couponMessage,
      iapProductDetails);

  /// Create a copy of CheckoutState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CheckoutStateImplCopyWith<_$CheckoutStateImpl> get copyWith =>
      __$$CheckoutStateImplCopyWithImpl<_$CheckoutStateImpl>(this, _$identity);
}

abstract class _CheckoutState extends CheckoutState {
  factory _CheckoutState(
      {final CheckoutSessionResponse? session,
      final String? checkoutUrl,
      final CouponCodeInput couponCode,
      final bool autoCredit,
      final bool isStoreAvailable,
      final BuyCreditResponse? buyCreditOption,
      final String currencyCode,
      final BuyCreditPaymentMethods paymentMethod,
      final FormzSubmissionStatus status,
      final PaymentSheetStatus paymentSheetStatus,
      final CouponCodeInputStatus couponStatus,
      final String? message,
      final String? couponMessage,
      final ProductDetails? iapProductDetails}) = _$CheckoutStateImpl;
  _CheckoutState._() : super._();

  @override
  CheckoutSessionResponse? get session;
  @override
  String? get checkoutUrl;
  @override
  CouponCodeInput get couponCode;
  @override
  bool get autoCredit;
  @override
  bool get isStoreAvailable;
  @override
  BuyCreditResponse? get buyCreditOption;
  @override
  String get currencyCode;
  @override
  BuyCreditPaymentMethods get paymentMethod;
  @override
  FormzSubmissionStatus get status;
  @override
  PaymentSheetStatus get paymentSheetStatus;
  @override
  CouponCodeInputStatus get couponStatus;
  @override
  String? get message;
  @override
  String? get couponMessage;
  @override
  ProductDetails? get iapProductDetails;

  /// Create a copy of CheckoutState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CheckoutStateImplCopyWith<_$CheckoutStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
