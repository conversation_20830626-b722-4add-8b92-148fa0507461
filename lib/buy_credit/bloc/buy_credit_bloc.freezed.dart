// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'buy_credit_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$BuyCreditEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() initial,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? initial,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? initial,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Initial value) initial,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Initial value)? initial,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Initial value)? initial,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BuyCreditEventCopyWith<$Res> {
  factory $BuyCreditEventCopyWith(
          BuyCreditEvent value, $Res Function(BuyCreditEvent) then) =
      _$BuyCreditEventCopyWithImpl<$Res, BuyCreditEvent>;
}

/// @nodoc
class _$BuyCreditEventCopyWithImpl<$Res, $Val extends BuyCreditEvent>
    implements $BuyCreditEventCopyWith<$Res> {
  _$BuyCreditEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BuyCreditEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$StartedImplCopyWith<$Res> {
  factory _$$StartedImplCopyWith(
          _$StartedImpl value, $Res Function(_$StartedImpl) then) =
      __$$StartedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$StartedImplCopyWithImpl<$Res>
    extends _$BuyCreditEventCopyWithImpl<$Res, _$StartedImpl>
    implements _$$StartedImplCopyWith<$Res> {
  __$$StartedImplCopyWithImpl(
      _$StartedImpl _value, $Res Function(_$StartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of BuyCreditEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$StartedImpl implements _Started {
  const _$StartedImpl();

  @override
  String toString() {
    return 'BuyCreditEvent.started()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$StartedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() initial,
  }) {
    return started();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? initial,
  }) {
    return started?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? initial,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Initial value) initial,
  }) {
    return started(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Initial value)? initial,
  }) {
    return started?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Initial value)? initial,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started(this);
    }
    return orElse();
  }
}

abstract class _Started implements BuyCreditEvent {
  const factory _Started() = _$StartedImpl;
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$BuyCreditEventCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of BuyCreditEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'BuyCreditEvent.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() initial,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? initial,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? initial,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Initial value) initial,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Initial value)? initial,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Initial value)? initial,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements BuyCreditEvent {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
mixin _$BuyCreditState {
  String get currencySymbol => throw _privateConstructorUsedError;
  List<BuyCreditResponse> get buyCreditOptions =>
      throw _privateConstructorUsedError;
  FormzSubmissionStatus get status => throw _privateConstructorUsedError;
  String? get message => throw _privateConstructorUsedError;

  /// Create a copy of BuyCreditState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BuyCreditStateCopyWith<BuyCreditState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BuyCreditStateCopyWith<$Res> {
  factory $BuyCreditStateCopyWith(
          BuyCreditState value, $Res Function(BuyCreditState) then) =
      _$BuyCreditStateCopyWithImpl<$Res, BuyCreditState>;
  @useResult
  $Res call(
      {String currencySymbol,
      List<BuyCreditResponse> buyCreditOptions,
      FormzSubmissionStatus status,
      String? message});
}

/// @nodoc
class _$BuyCreditStateCopyWithImpl<$Res, $Val extends BuyCreditState>
    implements $BuyCreditStateCopyWith<$Res> {
  _$BuyCreditStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BuyCreditState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currencySymbol = null,
    Object? buyCreditOptions = null,
    Object? status = null,
    Object? message = freezed,
  }) {
    return _then(_value.copyWith(
      currencySymbol: null == currencySymbol
          ? _value.currencySymbol
          : currencySymbol // ignore: cast_nullable_to_non_nullable
              as String,
      buyCreditOptions: null == buyCreditOptions
          ? _value.buyCreditOptions
          : buyCreditOptions // ignore: cast_nullable_to_non_nullable
              as List<BuyCreditResponse>,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as FormzSubmissionStatus,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BuyCreditStateImplCopyWith<$Res>
    implements $BuyCreditStateCopyWith<$Res> {
  factory _$$BuyCreditStateImplCopyWith(_$BuyCreditStateImpl value,
          $Res Function(_$BuyCreditStateImpl) then) =
      __$$BuyCreditStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String currencySymbol,
      List<BuyCreditResponse> buyCreditOptions,
      FormzSubmissionStatus status,
      String? message});
}

/// @nodoc
class __$$BuyCreditStateImplCopyWithImpl<$Res>
    extends _$BuyCreditStateCopyWithImpl<$Res, _$BuyCreditStateImpl>
    implements _$$BuyCreditStateImplCopyWith<$Res> {
  __$$BuyCreditStateImplCopyWithImpl(
      _$BuyCreditStateImpl _value, $Res Function(_$BuyCreditStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of BuyCreditState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currencySymbol = null,
    Object? buyCreditOptions = null,
    Object? status = null,
    Object? message = freezed,
  }) {
    return _then(_$BuyCreditStateImpl(
      currencySymbol: null == currencySymbol
          ? _value.currencySymbol
          : currencySymbol // ignore: cast_nullable_to_non_nullable
              as String,
      buyCreditOptions: null == buyCreditOptions
          ? _value._buyCreditOptions
          : buyCreditOptions // ignore: cast_nullable_to_non_nullable
              as List<BuyCreditResponse>,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as FormzSubmissionStatus,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$BuyCreditStateImpl extends _BuyCreditState {
  _$BuyCreditStateImpl(
      {this.currencySymbol = 'USD',
      final List<BuyCreditResponse> buyCreditOptions = const [],
      this.status = FormzSubmissionStatus.inProgress,
      this.message})
      : _buyCreditOptions = buyCreditOptions,
        super._();

  @override
  @JsonKey()
  final String currencySymbol;
  final List<BuyCreditResponse> _buyCreditOptions;
  @override
  @JsonKey()
  List<BuyCreditResponse> get buyCreditOptions {
    if (_buyCreditOptions is EqualUnmodifiableListView)
      return _buyCreditOptions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_buyCreditOptions);
  }

  @override
  @JsonKey()
  final FormzSubmissionStatus status;
  @override
  final String? message;

  @override
  String toString() {
    return 'BuyCreditState(currencySymbol: $currencySymbol, buyCreditOptions: $buyCreditOptions, status: $status, message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BuyCreditStateImpl &&
            (identical(other.currencySymbol, currencySymbol) ||
                other.currencySymbol == currencySymbol) &&
            const DeepCollectionEquality()
                .equals(other._buyCreditOptions, _buyCreditOptions) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, currencySymbol,
      const DeepCollectionEquality().hash(_buyCreditOptions), status, message);

  /// Create a copy of BuyCreditState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BuyCreditStateImplCopyWith<_$BuyCreditStateImpl> get copyWith =>
      __$$BuyCreditStateImplCopyWithImpl<_$BuyCreditStateImpl>(
          this, _$identity);
}

abstract class _BuyCreditState extends BuyCreditState {
  factory _BuyCreditState(
      {final String currencySymbol,
      final List<BuyCreditResponse> buyCreditOptions,
      final FormzSubmissionStatus status,
      final String? message}) = _$BuyCreditStateImpl;
  _BuyCreditState._() : super._();

  @override
  String get currencySymbol;
  @override
  List<BuyCreditResponse> get buyCreditOptions;
  @override
  FormzSubmissionStatus get status;
  @override
  String? get message;

  /// Create a copy of BuyCreditState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BuyCreditStateImplCopyWith<_$BuyCreditStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
