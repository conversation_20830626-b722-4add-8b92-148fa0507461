part of 'checkout_bloc.dart';

@freezed
class CheckoutEvent with _$CheckoutEvent {
  const factory CheckoutEvent.checkoutInitiated() = _CheckoutInitiated;

  const factory CheckoutEvent.checkoutReset() = _CheckoutReset;

  const factory CheckoutEvent.checkoutRefreshed() = _CheckoutRefreshed;

  const factory CheckoutEvent.cardPaymentProcessed({
    required UserAttributesModel user,
  }) = _CardPaymentProcessed;

  const factory CheckoutEvent.applePayPaymentProcessed() =
      _ApplePayPaymentProcessed;

  const factory CheckoutEvent.googlePayPaymentProcessed() =
      _GooglePayPaymentProcessed;

  const factory CheckoutEvent.couponCodeUpdated({
    String? value,
    @Default(CouponCodeInputStatus.initial) CouponCodeInputStatus status,
  }) = _CouponCodeUpdated;

  const factory CheckoutEvent.couponApplied() = _CouponApplied;

  const factory CheckoutEvent.automaticCreditToggled({
    @Default(false) bool value,
  }) = _AutomaticCreditToggled;

  const factory CheckoutEvent.currencyCodeUpdated({
    @Default('usd') String value,
  }) = _CurrencyCodeUpdated;

  const factory CheckoutEvent.paymentMethodUpdated({
    @Default(BuyCreditPaymentMethods.others)
    BuyCreditPaymentMethods purchaseMethod,
  }) = _PaymentMethodUpdated;

  const factory CheckoutEvent.productAddedToCart({
    required BuyCreditResponse buyCreditOption,
  }) = _ProductAddedToCart;

  const factory CheckoutEvent.discountedCheckoutStarted({
    required BuyCreditResponse buyCreditOption,
    @Default('usd') String currencyCode,
    String? couponCode,
  }) = _DiscountedCheckoutStarted;

  const factory CheckoutEvent.iapDetailsRefreshed(
    String storeProductId,
  ) = _IapDetailsRefreshed;

  const factory CheckoutEvent.purchaseDetailsUpdated(
    List<PurchaseDetails> purchaseDetailsList,
  ) = _PurchaseDetailsUpdated;
  
  const factory CheckoutEvent.restorePurchases() = _RestorePurchases;
}
