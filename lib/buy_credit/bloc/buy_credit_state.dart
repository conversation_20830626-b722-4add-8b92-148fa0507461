part of 'buy_credit_bloc.dart';

@freezed
class BuyCreditState with _$BuyCreditState {
  factory BuyCreditState({
    @Default('USD') String currencySymbol,
    @Default([]) List<BuyCreditResponse> buyCreditOptions,
    @Default(FormzSubmissionStatus.inProgress) FormzSubmissionStatus status,
    String? message,
  }) = _BuyCreditState;

  factory BuyCreditState.initial() => BuyCreditState();

  BuyCreditState._();
}
