import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:formz/formz.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:froggytalk/app/data/services/event_tracker.dart';
import 'package:froggytalk/buy_credit/buy_credit.dart';

part 'buy_credit_bloc.freezed.dart';
part 'buy_credit_event.dart';
part 'buy_credit_state.dart';

class BuyCreditBloc extends Bloc<BuyCreditEvent, BuyCreditState> {
  BuyCreditBloc() : super(BuyCreditState()) {
    on<_Started>(_onStarted);
  }

  final _productsRepository = BuyCreditOptionsRepository();
  final _eventTracker = EventTrackerService.getInstance();

  FutureOr<void> _onStarted(
    _Started event,
    Emitter<BuyCreditState> emit,
  ) async {
    await _eventTracker.logEvent(
      schema: 'Purchase_Initiated',
      description: 'A User wants to make a purchase on the App',
    );

    emit(
      state.copyWith(
        status: FormzSubmissionStatus.inProgress,
      ),
    );

    final result = await _productsRepository.execute();

    if (result.isRight) {
      emit(
        state.copyWith(
          status: FormzSubmissionStatus.success,
          buyCreditOptions: result.right.data?.map((e) {
                return e.attributes!.copyWith(id: e.id);
              }).toList() ??
              [],
        ),
      );
    } else {
      final left = result.left;

      if (left.code == 'internet-out') {
        emit(
          state.copyWith(
            status: FormzSubmissionStatus.canceled,
            message: left.message,
          ),
        );
      } else {
        emit(
          state.copyWith(
            status: FormzSubmissionStatus.failure,
            message: left.message,
          ),
        );
      }
    }

    return null;
  }
}
