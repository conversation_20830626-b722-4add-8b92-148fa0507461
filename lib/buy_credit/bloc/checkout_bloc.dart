import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:formz/formz.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:froggytalk/app/data/services/event_tracker.dart';
import 'package:froggytalk/buy_credit/buy_credit.dart';
import 'package:froggytalk/profile/profile.dart';
import 'package:froggytalk/shared/services/purchase_attribution_service.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_android/in_app_purchase_android.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import 'package:utils/utils.dart';

part 'checkout_bloc.freezed.dart';
part 'checkout_event.dart';
part 'checkout_state.dart';

class CheckoutBloc extends Bloc<CheckoutEvent, CheckoutState> {
  CheckoutBloc() : super(CheckoutState()) {
    on<_CheckoutInitiated>(_onCheckoutInitiated);
    on<_CheckoutReset>(_onCheckoutReset);
    on<_ProductAddedToCart>(_onProductAddedToCart);
    on<_AutomaticCreditToggled>(_onAutomaticCreditToggled);
    on<_PaymentMethodUpdated>(_onPaymentMethodUpdated);
    on<_CurrencyCodeUpdated>(_onCurrencyCodeUpdated);
    on<_CheckoutRefreshed>(_onCheckoutRefreshed);
    on<_CardPaymentProcessed>(_onCardPaymentProcessed);
    on<_ApplePayPaymentProcessed>(_onApplePayPaymentProcessed);
    on<_GooglePayPaymentProcessed>(_onGooglePayPaymentProcessed);
    on<_CouponApplied>(_onCouponApplied);
    on<_DiscountedCheckoutStarted>(_onDiscountedCheckoutStarted);
    on<_RestorePurchases>(_onRestorePurchases);
    on<_CouponCodeUpdated>(
      _onCouponCodeUpdated,
      transformer: debounce(),
    );
    on<_PurchaseDetailsUpdated>(_onPurchaseDetailsUpdated);

    _subscription = InAppPurchase.instance.purchaseStream.listen(
      (purchaseDetailsList) {
        add(CheckoutEvent.purchaseDetailsUpdated(purchaseDetailsList));
      },
      onDone: () {
        _subscription?.cancel();
      },
      onError: (Object error) {
        FroggyLogger.error('Error in purchase stream: $error');
      },
    );
  }

  final _addToCartRepo = AddToCartRepository();
  final _checkoutRepo = CheckoutRepository();
  final _checkoutService = PaymentSheetService();
  final _eventTracker = EventTrackerService.getInstance();

  StreamSubscription<List<PurchaseDetails>>? _subscription;

  FutureOr<void> _onCheckoutReset(
    _CheckoutReset event,
    Emitter<CheckoutState> emit,
  ) {
    emit(state.reset());
  }

  Future<void> _onCheckoutInitiated(
    _CheckoutInitiated event,
    Emitter<CheckoutState> emit,
  ) async {
    emit(state.reset());

    BuyCreditEvents.clickedOnProceedToCheckoutButton(
      currency: state.currencyCode,
      value: double.tryParse(
            state.buyCreditOption?.amountToBeCredited?.amount ?? '0',
          ) ??
          0.0,
      sessionId: state.session?.id ?? '',
      couponCode: state.couponCode.value,
    );

    try {
      final isAvailable = await InAppPurchase.instance.isAvailable();
      FroggyLogger.info('IAP Store availability: $isAvailable');
      emit(
        state.copyWith(
          isStoreAvailable: isAvailable,
        ),
      );

      if (isAvailable) {
        FroggyLogger.info('IAP Store is available');
      }
    } catch (e) {
      FroggyLogger.error('Error checking IAP Store availability: $e');
      emit(
        state.copyWith(
          isStoreAvailable: false,
          status: FormzSubmissionStatus.failure,
          message: 'Error checking IAP Store availability: $e',
        ),
      );
    }
  }

  FutureOr<void> _onCardPaymentProcessed(
    _CardPaymentProcessed event,
    Emitter<CheckoutState> emit,
  ) async {
    emit(
      state.copyWith(
        status: FormzSubmissionStatus.inProgress,
        paymentSheetStatus: PaymentSheetStatus.pending,
      ),
    );

    await _eventTracker.logEvent(
      schema: 'Proceed_to_purchase',
      description: 'User proceeds after clicking on amount',
    );

    BuyCreditEvents.clickedOnProceedToPaymentButton(
      currency: state.currencyCode,
      value:
          (state.buyCreditOption?.amountToBeCredited?.amount ?? '0').toDouble,
      sessionId: state.session?.id ?? '',
      couponCode: state.couponCode.value,
    );

    final result =
        await _checkoutRepo.execute(paymentId: state.session?.id ?? '');

    if (result.isRight) {
      FroggyLogger.info(
        'the checkout result contains: ${result.right.data}',
      );
      final paymentIntentData = result.right.data;
      final clientSecret = paymentIntentData?.clientSecret;

      final billingDetails = BillingDetails(
        name: event.user.name,
        email: event.user.email,
        phone: event.user.formattedPhoneNumber,
        address: Address(
          country: event.user.country?.attributes?.code,
          line1: '',
          line2: '',
          city: event.user.country?.attributes?.capital,
          state: event.user.country?.attributes?.region,
          postalCode: '',
        ),
      );

      await _checkoutService.makePayment(
        clientSecret: clientSecret ?? '',
        billingDetails: billingDetails,
        onPaymentSuccess: () async {
          emit(
            state.copyWith(
              status: FormzSubmissionStatus.success,
              paymentSheetStatus: PaymentSheetStatus.approved,
            ),
          );

          await _eventTracker.logEvent(
            schema: 'Purchase successful',
            description: 'User purchased '
                '${state.session?.amountToBeCredited} top up successful',
          );

          BuyCreditEvents.onPaymentSuccess(
            currency: state.currencyCode,
            value: (state.buyCreditOption?.amountToBeCredited?.amount ?? '0')
                .toDouble,
            sessionId: state.session?.id ?? '',
            couponCode: state.couponCode.value,
          );
        },
        onPaymentCancelled: (e) async {
          FroggyLogger.error('payment cancelled: $e');

          emit(
            state.copyWith(
              status: FormzSubmissionStatus.canceled,
              paymentSheetStatus: PaymentSheetStatus.cancelled,
              message: e.error.localizedMessage.toString(),
            ),
          );

          await _eventTracker.logEvent(
            schema: 'Purchase cancelled',
            description: 'User cancelled purchase '
                '${state.session?.amountToBeCredited}',
          );

          BuyCreditEvents.onPaymentCancelled(
            currency: state.currencyCode,
            value: (state.buyCreditOption?.amountToBeCredited?.amount ?? '0')
                .toDouble,
            sessionId: state.session?.id ?? '',
            couponCode: state.couponCode.value,
          );
        },
        onPaymentTimedout: (e) async {
          FroggyLogger.error('payment timed out: $e');

          emit(
            state.copyWith(
              status: FormzSubmissionStatus.failure,
              paymentSheetStatus: PaymentSheetStatus.timedout,
              message: e.error.localizedMessage.toString(),
            ),
          );

          await _eventTracker.logEvent(
            schema: 'Purchase timeout',
            description: 'User purchase timedout '
                '${state.session?.amountToBeCredited}',
          );
        },
        onPaymentError: (e) {
          FroggyLogger.error('payment error: $e');

          emit(
            state.copyWith(
              status: FormzSubmissionStatus.failure,
              paymentSheetStatus: PaymentSheetStatus.failure,
              message: e.toString(),
            ),
          );

          _eventTracker.logEvent(
            schema: 'Purchase error',
            description: 'User purchase errored: '
                '${state.session?.amountToBeCredited}',
          );
        },
        onPaymentFailure: (e) {
          FroggyLogger.error('payment failure: $e');

          emit(
            state.copyWith(
              status: FormzSubmissionStatus.failure,
              paymentSheetStatus: PaymentSheetStatus.failure,
              message: e.error.localizedMessage.toString(),
            ),
          );

          _eventTracker.logEvent(
            schema: 'Purchase failed',
            description: 'User purchase failed: '
                '${state.session?.amountToBeCredited}',
          );

          BuyCreditEvents.onPaymentFailure(
            currency: state.currencyCode,
            value: (state.buyCreditOption?.amountToBeCredited?.amount ?? '0')
                .toDouble,
            sessionId: state.session?.id ?? '',
            couponCode: state.couponCode.value,
          );
        },
      );
    } else {
      emit(
        state.copyWith(
          status: FormzSubmissionStatus.failure,
          message: result.left.message,
        ),
      );

      BuyCreditEvents.onPaymentFailure(
        currency: state.currencyCode,
        value:
            (state.buyCreditOption?.amountToBeCredited?.amount ?? '0').toDouble,
        sessionId: state.session?.id ?? '',
        couponCode: state.couponCode.value,
      );
    }
  }

  FutureOr<void> _onCheckoutRefreshed(
    _CheckoutRefreshed event,
    Emitter<CheckoutState> emit,
  ) async {
    emit(
      state.copyWith(
        status: FormzSubmissionStatus.inProgress,
        message: null,
      ),
    );

    FroggyLogger.info(
      'refreshing checkout: ${state.buyCreditOption?.toJson()}',
    );

    final response = await _addToCartRepo.execute(
      amount: state.buyCreditOption?.amountToBeCredited?.amount ?? '0',
      paymentMethod: state.paymentMethod.name,
      currencyCode: state.currencyCode,
      isAutoRechargeable: state.autoCredit,
      sessionId: state.session?.id,
      couponCode: state.couponCode.value,
    );

    if (response.isRight) {
      emit(
        state.copyWith(
          status: FormzSubmissionStatus.initial,
          session: response.right.data,
          message: null,
          couponMessage: null,
          buyCreditOption: state.buyCreditOption,
          currencyCode: state.currencyCode,
          couponCode: state.couponCode,
          autoCredit: state.autoCredit,
        ),
      );
    } else {
      final message2 = response.left.message ?? '';
      emit(
        state.copyWith(
          status: FormzSubmissionStatus.failure,
          message: message2.contains('oupon') ? null : message2,
          couponMessage: message2.contains('oupon') ? message2 : null,
        ),
      );
    }
  }

  FutureOr<void> _onAutomaticCreditToggled(
    _AutomaticCreditToggled event,
    Emitter<CheckoutState> emit,
  ) {
    emit(state.copyWith(autoCredit: event.value));

    add(const CheckoutEvent.checkoutRefreshed());
  }

  Future<void> _onPaymentMethodUpdated(
    _PaymentMethodUpdated event,
    Emitter<CheckoutState> emit,
  ) async {
    emit(state.copyWith(paymentMethod: event.purchaseMethod));

    if (event.purchaseMethod.isOthers) return;

    try {
      final productId = isAndroid()
          ? state.buyCreditOption?.playStoreIapId
          : state.buyCreditOption?.appStoreIapId;
      if (productId != null && state.isStoreAvailable) {
        final response =
            await InAppPurchase.instance.queryProductDetails({productId});

        if (response.notFoundIDs.isNotEmpty) {
          FroggyLogger.warning('Product not found: ${response.notFoundIDs}');
          emit(
            state.copyWith(
              status: FormzSubmissionStatus.failure,
              paymentSheetStatus: PaymentSheetStatus.failure,
              message: 'Failed to fetch product details',
            ),
          );
        }

        if (response.productDetails.isNotEmpty) {
          FroggyLogger.info('Found ${response.productDetails.length} products');
          // final wip = response.productDetails.first.;

          emit(
            state.copyWith(
              iapProductDetails: response.productDetails.first,
            ),
          );

          if (response.productDetails.first is GooglePlayProductDetails) {
            final googleProductDetails =
                response.productDetails.first as GooglePlayProductDetails;
            FroggyLogger.info(
              'Google Play Product Details: $googleProductDetails',
            );
          } else if (response.productDetails.first is AppStoreProductDetails) {
            final iosProductDetails =
                response.productDetails.first as AppStoreProductDetails;
            FroggyLogger.info(
              'iOS Product Details: $iosProductDetails',
            );
          }
        }
      }
    } catch (e) {
      FroggyLogger.error('Failed to query product details: $e');
      emit(
        state.copyWith(
          status: FormzSubmissionStatus.failure,
          message: 'Failed to fetch product details: $e',
        ),
      );
    }
  }

  FutureOr<void> _onCurrencyCodeUpdated(
    _CurrencyCodeUpdated event,
    Emitter<CheckoutState> emit,
  ) {
    emit(state.copyWith(currencyCode: event.value));
  }

  Future<void> _onProductAddedToCart(
    _ProductAddedToCart event,
    Emitter<CheckoutState> emit,
  ) async {
    emit(
      state.copyWith(
        buyCreditOption: event.buyCreditOption,
      ),
    );

    await _eventTracker.logEvent(
      schema: 'Select_Amount',
      description: 'User selects amount to top up',
    );

    BuyCreditEvents.selectedBuyCreditOption(
      optionName: 'User selects '
          '${state.buyCreditOption?.amountToBeCredited?.amount ?? '0'} '
          'to top up',
      currency: state.currencyCode,
      value:
          (state.buyCreditOption?.amountToBeCredited?.amount ?? '0').toDouble,
      sessionId: state.session?.id ?? '',
      couponCode: state.couponCode.value,
    );
  }

  Future<void> _onApplePayPaymentProcessed(
    _ApplePayPaymentProcessed event,
    Emitter<CheckoutState> emit,
  ) async {
    try {
      emit(
        state.copyWith(
          status: FormzSubmissionStatus.inProgress,
          paymentSheetStatus: PaymentSheetStatus.pending,
        ),
      );

      final productDetails = state.iapProductDetails! as AppStoreProductDetails;
      final purchaseParam = PurchaseParam(
        productDetails: productDetails,
      );

      final result = await InAppPurchase.instance.buyConsumable(
        purchaseParam: purchaseParam,
      );

      FroggyLogger.info('Apple Pay purchase initiated: $result');
    } catch (e) {
      FroggyLogger.error('Failed to initiate Apple Pay purchase: $e');
      emit(
        state.copyWith(
          status: FormzSubmissionStatus.failure,
          message: 'Failed to initiate payment: $e',
          paymentSheetStatus: PaymentSheetStatus.failure,
        ),
      );
    }
  }

  Future<void> _onGooglePayPaymentProcessed(
    _GooglePayPaymentProcessed event,
    Emitter<CheckoutState> emit,
  ) async {
    try {
      emit(
        state.copyWith(
          status: FormzSubmissionStatus.inProgress,
          paymentSheetStatus: PaymentSheetStatus.pending,
        ),
      );

      final productDetails =
          state.iapProductDetails! as GooglePlayProductDetails;
      final purchaseParam = PurchaseParam(
        productDetails: productDetails,
      );

      final result = await InAppPurchase.instance.buyConsumable(
        purchaseParam: purchaseParam,
      );

      FroggyLogger.info('Google Pay purchase initiated: $result');
    } catch (e) {
      FroggyLogger.error('Failed to initiate Google Pay purchase: $e');
      emit(
        state.copyWith(
          status: FormzSubmissionStatus.failure,
          message: 'Failed to initiate payment: $e',
          paymentSheetStatus: PaymentSheetStatus.failure,
        ),
      );
    }
  }

  FutureOr<void> _onCouponApplied(
    _CouponApplied event,
    Emitter<CheckoutState> emit,
  ) {
    emit(
      state.copyWith(
        couponStatus: CouponCodeInputStatus.applied,
        couponMessage: null,
      ),
    );

    add(const CheckoutEvent.checkoutRefreshed());

    BuyCreditEvents.selectedBuyCreditOption(
      optionName: 'User selects '
          '${state.buyCreditOption?.amountToBeCredited?.amount ?? '0'} '
          'to top up',
      currency: state.currencyCode,
      value:
          (state.buyCreditOption?.amountToBeCredited?.amount ?? '0').toDouble,
      sessionId: state.session?.id ?? '',
      couponCode: state.couponCode.value,
    );
  }

  FutureOr<void> _onCouponCodeUpdated(
    _CouponCodeUpdated event,
    Emitter<CheckoutState> emit,
  ) {
    final formInput = CouponCodeInput.dirty(event.value ?? '');
    emit(
      state.copyWith(
        status: FormzSubmissionStatus.initial,
        couponCode: formInput,
        couponStatus: formInput.value.length >= 4
            ? CouponCodeInputStatus.reset
            : CouponCodeInputStatus.initial,
      ),
    );
  }

  FutureOr<void> _onDiscountedCheckoutStarted(
    _DiscountedCheckoutStarted event,
    Emitter<CheckoutState> emit,
  ) async {
    emit(
      state.copyWith(
        status: FormzSubmissionStatus.inProgress,
        message: null,
        couponCode: const CouponCodeInput.pure(),
        couponMessage: null,
        couponStatus: CouponCodeInputStatus.readonly,
        buyCreditOption: event.buyCreditOption,
        currencyCode: event.currencyCode,
        autoCredit: false,
      ),
    );

    add(const CheckoutEvent.checkoutRefreshed());
    emit(
      state.copyWith(
        status: FormzSubmissionStatus.inProgress,
      ),
    );

    final formInput = CouponCodeInput.dirty(event.couponCode ?? '');
    await setTimeout(
      computation: () {
        emit(
          state.copyWith(
            message: null,
            couponCode: formInput,
            couponStatus: CouponCodeInputStatus.readonly,
            couponMessage: null,
          ),
        );

        add(const CheckoutEvent.checkoutRefreshed());
      },
    );
  }

  FutureOr<void> _onPurchaseDetailsUpdated(
    _PurchaseDetailsUpdated event,
    Emitter<CheckoutState> emit,
  ) async {
    for (final purchaseDetails in event.purchaseDetailsList) {
      if (purchaseDetails.status == PurchaseStatus.pending) {
        FroggyLogger.info('IAP Purchase pending: ${purchaseDetails.productID}');

        emit(
          state.copyWith(
            status: FormzSubmissionStatus.inProgress,
            paymentSheetStatus: PaymentSheetStatus.pending,
            // message: e.error.localizedMessage.toString(),
          ),
        );

        continue;
      }

      if (purchaseDetails.status == PurchaseStatus.canceled) {
        FroggyLogger.info(
          'IAP Purchase cancelled: ${purchaseDetails.status}',
        );

        emit(
          state.copyWith(
            status: FormzSubmissionStatus.canceled,
            paymentSheetStatus: PaymentSheetStatus.cancelled,
            // message: e.error.localizedMessage.toString(),
          ),
        );

        continue;
      }

      if (purchaseDetails.status == PurchaseStatus.error) {
        FroggyLogger.error('IAP Purchase error: ${purchaseDetails.error}');
        emit(
          state.copyWith(
            status: FormzSubmissionStatus.failure,
            paymentSheetStatus: PaymentSheetStatus.failure,
            message: 'Purchase failed: '
                '${purchaseDetails.error?.message ?? 'Unknown error'}',
          ),
        );
        continue;
      }

      if (purchaseDetails.status == PurchaseStatus.purchased ||
          purchaseDetails.status == PurchaseStatus.restored) {
        // Log differently for restored vs new purchases
        if (purchaseDetails.status == PurchaseStatus.restored) {
          FroggyLogger.info(
            'Purchase restored: ${purchaseDetails.productID}, '
            'purchaseID: ${purchaseDetails.purchaseID}',
          );
          // Update UI to show progress of restored purchase
          emit(
            state.copyWith(
              message:
                  'Processing restored purchase: ${purchaseDetails.productID}',
            ),
          );
        } else {
          FroggyLogger.info('Purchase completed: ${purchaseDetails.productID}');
        }

        final verified =
            await _verifyPurchase(purchaseDetails, state.session?.id ?? '');

        if (verified) {
          await _creditUserAccount(purchaseDetails, state.session?.id ?? '');

          /// Tracks a purchase with receipt information using the
          /// PurchaseAttributionService.
          ///
          /// The event name is set to either 'purchase_restored' for restored
          /// purchases or 'purchase_completed' for new purchases.
          ///
          /// Parameters tracked include:
          /// - Product ID from purchase details
          /// - Price from IAP product details (defaults to '0' if null)
          /// - Currency code from IAP product details (defaults to 'USD'
          ///   if null)
          /// - Transaction ID from purchase details
          /// - Purchase token from verification data
          await PurchaseAttributionService().trackPurchaseWithReceipt(
            eventName: purchaseDetails.status == PurchaseStatus.restored
                ? 'purchase_restored'
                : 'purchase_completed',
            productId: purchaseDetails.productID,
            price: double.parse(state.iapProductDetails?.price ?? '0'),
            currency: state.iapProductDetails?.currencyCode ?? 'USD',
            transactionId: purchaseDetails.purchaseID ?? '',
            purchaseToken:
                purchaseDetails.verificationData.serverVerificationData,
          );

          // Important: After verifying and reporting the purchase, you must
          // call InAppPurchase.instance.completePurchase(purchaseDetails)
          // to finalize the transaction with the store.
          if (purchaseDetails.pendingCompletePurchase) {
            await InAppPurchase.instance.completePurchase(purchaseDetails);
          }

          emit(
            state.copyWith(
              status: FormzSubmissionStatus.success,
              paymentSheetStatus: PaymentSheetStatus.approved,
              message: purchaseDetails.status == PurchaseStatus.restored
                  ? 'Purchase restored successfully'
                  : 'Payment was successful',
            ),
          );

          await _eventTracker.logEvent(
            schema: purchaseDetails.status == PurchaseStatus.restored
                ? 'Purchase_Restored'
                : 'Purchase_Successful',
            description: purchaseDetails.status == PurchaseStatus.restored
                ? 'User restored purchase ${purchaseDetails.productID}'
                : 'User purchased '
                    '${state.session?.amountToBeCredited} '
                    'top up successful',
          );
        } else {
          FroggyLogger.error(
            'IAP Purchase verification failed for: '
            '${purchaseDetails.productID}',
          );
          emit(
            state.copyWith(
              status: FormzSubmissionStatus.failure,
              paymentSheetStatus: PaymentSheetStatus.failure,
              message: 'Purchase verification failed',
            ),
          );
        }
      }
    }
  }

  Future<bool> _verifyPurchase(
    PurchaseDetails purchaseDetails,
    String ePaymentLogId,
  ) async {
    try {
      final isRestored = purchaseDetails.status == PurchaseStatus.restored;
      final logMessage = isRestored
          ? 'Verifying restored purchase: ${purchaseDetails.productID}'
          : 'Verifying purchase: ${purchaseDetails.productID}';

      FroggyLogger.info(logMessage);

      final receiptData =
          purchaseDetails.verificationData.serverVerificationData;
      final source = isAndroid() ? 'google' : 'apple';

      final response = await _checkoutRepo.verifyStoreReceipt(
        receipt: receiptData,
        productId: purchaseDetails.productID,
        source: source,
        ePaymentLogId: ePaymentLogId,
        transactionId: purchaseDetails.purchaseID ?? '',
        isRestored: isRestored,
      );

      if (response.isRight) {
        FroggyLogger.info(
          'Purchase verified successfully: ${purchaseDetails.purchaseID}',
        );

        await _eventTracker.logEvent(
          schema: 'Purchase_Verified',
          description: 'Purchase receipt verified successfully',
        );

        return true;
      } else {
        FroggyLogger.error(
          'Purchase verification failed: ${response.left.message}',
        );

        await _eventTracker.logEvent(
          schema: 'Purchase_Verification_Failed',
          description:
              'Purchase receipt verification failed: ${response.left.message}',
        );

        return false;
      }
    } catch (e) {
      FroggyLogger.error('Exception during purchase verification: $e');

      await _eventTracker.logEvent(
        schema: 'Purchase_Verification_Error',
        description: 'Exception during purchase verification: $e',
      );

      return false;
    }
  }

  Future<void> _creditUserAccount(
    PurchaseDetails purchaseDetails,
    String ePaymentLogId,
  ) async {
    try {
      final isRestored = purchaseDetails.status == PurchaseStatus.restored;
      final logMessage = isRestored
          ? 'Crediting user account for restored purchase: '
              '${purchaseDetails.purchaseID}'
          : 'Crediting user account for purchase: '
              '${purchaseDetails.purchaseID}';

      FroggyLogger.info(logMessage);

      final productId = purchaseDetails.productID;
      final transactionId = purchaseDetails.purchaseID ?? '';

      var creditAmount = '';

      if (state.iapProductDetails != null &&
          state.iapProductDetails!.id == productId) {
        creditAmount = state.buyCreditOption?.amountToBeCredited?.amount ?? '0';
      } else {
        final idParts = productId.split('.');
        if (idParts.length > 2) {
          creditAmount = idParts.last;
        }
      }

      final response = await _checkoutRepo.creditUserAccount(
        amount: creditAmount,
        productId: productId,
        transactionId: transactionId,
        currencyCode: state.currencyCode,
        isRestored: isRestored,
        ePaymentLogId: state.session?.id ?? '',
      );

      if (response.isRight) {
        FroggyLogger.info(
          'User account credited successfully: $creditAmount '
          '${state.currencyCode}',
        );

        BuyCreditEvents.onPaymentSuccess(
          currency: state.currencyCode,
          value: double.tryParse(creditAmount) ?? 0.0,
          sessionId: state.session?.id ?? '',
          couponCode: state.couponCode.value,
        );

        await _eventTracker.logEvent(
          schema: 'Account_Credited',
          description: 'User account credited with $creditAmount '
              '${state.currencyCode}',
        );
      } else {
        final errorMsg = response.left.message;
        final errorDisplay = errorMsg;
        FroggyLogger.error('Failed to credit user account: $errorDisplay');

        await _eventTracker.logEvent(
          schema: 'Account_Credit_Failed',
          description: 'Failed to credit user account: $errorDisplay',
        );

        await _recordFailedCreditTransaction(
          purchaseDetails,
          creditAmount,
          ePaymentLogId,
        );
      }
    } catch (e) {
      FroggyLogger.error('Exception during account crediting: $e');

      await _eventTracker.logEvent(
        schema: 'Account_Credit_Error',
        description: 'Exception during account crediting: $e',
      );

      await _recordFailedCreditTransaction(
        purchaseDetails,
        'unknown',
        ePaymentLogId,
      );
    }
  }

  Future<void> _recordFailedCreditTransaction(
    PurchaseDetails purchaseDetails,
    String attemptedCreditAmount,
    String ePaymentLogId,
  ) async {
    try {
      await _checkoutRepo.recordFailedTransaction(
        productId: purchaseDetails.productID,
        transactionId: purchaseDetails.purchaseID ?? '',
        receiptData: purchaseDetails.verificationData.serverVerificationData,
        platform: isAndroid() ? 'android' : 'ios',
        attemptedAmount: attemptedCreditAmount,
        currencyCode: state.currencyCode,
        ePaymentLogId: ePaymentLogId,
      );

      FroggyLogger.info('Failed transaction recorded for customer support');
    } catch (e) {
      FroggyLogger.error(
        'CRITICAL: Failed to record failed transaction. '
        'Manual intervention required. Details: '
        'Product: ${purchaseDetails.productID}, '
        'Transaction: ${purchaseDetails.purchaseID}, '
        'Error: $e',
      );
    }
  }

  /// Handles the restoration of past purchases
  /// It will emit loading state, then attempt to restore purchases from
  /// Purchase details will be processed by the _onPurchaseDetailsUpdated
  /// handler once returned.
  /// This method initiates the process of querying past purchases from the
  /// Apple/Google stores
  /// It will emit loading state, then attempt to restore purchases from the
  /// platform's store.
  /// Purchase details will be processed by the _onPurchaseDetailsUpdated
  /// handler once returned.
  Future<void> _onRestorePurchases(
    _RestorePurchases event,
    Emitter<CheckoutState> emit,
  ) async {
    try {
      emit(
        state.copyWith(
          status: FormzSubmissionStatus.inProgress,
          message: null,
        ),
      );

      FroggyLogger.info('Attempting to restore purchases');

      await _eventTracker.logEvent(
        schema: 'Restore_Purchases',
        description: 'User initiated restore purchases',
      );

      final available = await InAppPurchase.instance.isAvailable();
      if (!available) {
        emit(
          state.copyWith(
            status: FormzSubmissionStatus.failure,
            message: 'Store is not available for purchase restoration',
            isStoreAvailable: false,
          ),
        );
        return;
      }

      // On iOS, this will query the App Store for previous purchases
      // On Android, this will query Google Play for previous purchases
      final response = await _checkoutRepo.restorePurchases();

      if (response.isRight) {
        emit(
          state.copyWith(
            status: FormzSubmissionStatus.success,
            message: 'Purchase restoration process initiated. '
                'Any restored purchases will be processed automatically.',
          ),
        );

        FroggyLogger.info(
          'Purchase restoration process initiated successfully',
        );
      } else {
        emit(
          state.copyWith(
            status: FormzSubmissionStatus.failure,
            message: response.left.message,
          ),
        );

        FroggyLogger.error(
          'Failed to restore purchases: ${response.left.message}',
        );
      }
    } catch (e) {
      FroggyLogger.error('Error during purchase restoration: $e');
      emit(
        state.copyWith(
          status: FormzSubmissionStatus.failure,
          message: 'Error during purchase restoration: $e',
        ),
      );
    }
  }
}
