import 'package:flutter/material.dart';
import 'package:flutter_tawk_to_chat/flutter_tawk_to_chat.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/l10n/l10n.dart';

class ChatWithLiveAgentPage extends StatelessWidget {
  const ChatWithLiveAgentPage({super.key});

  static Route<Object?> route() {
    return MaterialPageRoute<Object?>(
      builder: (_) => const ChatWithLiveAgentPage(),
    );
  }

  static String routeName = '/chat_with_live_agent';

  @override
  Widget build(BuildContext context) {
    const directChatLink =
        'https://tawk.to/chat/647b14747957702c744b8bd2/1h20cfqc6';
    final l10n = context.l10n;

    return Scaffold(
      appBar: FroggyAppBar(
        label: l10n.helpCenterPageMenu_2,
        labelFontSize: 20,
      ),
      // body: Tawk(
      //   directChatLink: directChatLink,
      //   visitor: TawkVisitor(
      //     name: 'Ayoub AMINE',
      //     email: '<EMAIL>',
      //   ),
      // ),
      body: Tawk(
        directChatLink: directChatLink,
        visitor: TawkVisitor(
          name: 'Ayoub AMINE',
          email: '<EMAIL>',
        ),
      ),
      // body: const WhatsAppChatPage(
      //   phoneNumber: '31657848469',
      // ),
    );
  }
}
