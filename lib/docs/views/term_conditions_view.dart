import 'package:flutter/material.dart';
import 'package:froggytalk/docs/docs.dart';

class TermConditionsView extends StatelessWidget {
  const TermConditionsView({super.key});

  static void showModal(BuildContext context) {
    showDialog<void>(
      context: context,
      builder: (context) => const TermConditionsView(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return MarkdownReaderWidget(
      markdownFile: 'assets/docs/terms-conditions.md',
    );
  }
}
