import 'package:flutter/material.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/docs/docs.dart';
import 'package:froggytalk/l10n/l10n.dart';

class FaqsView extends StatelessWidget {
  const FaqsView({super.key});

  // return route()
  static Route<Object?> route() {
    return MaterialPageRoute(builder: (context) => const FaqsView());
  }

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return Scaffold(
      appBar: FroggyAppBar(label: l10n.helpCenterPageMenu_3),
      body: MarkdownReaderWidget(
        markdownFile: 'assets/docs/faqs-reviewed.md',
        isDialog: false,
      ),
    );
  }
}
