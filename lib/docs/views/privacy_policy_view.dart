import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/app/data/services/event_tracker.dart';
import 'package:froggytalk/docs/docs.dart';
import 'package:froggytalk/l10n/extensions/l10n.dart';

class PrivacyPolicyView extends HookWidget {
  const PrivacyPolicyView({super.key, this.isDialog = true});

  static void showModal(BuildContext context) {
    showDialog<void>(
      context: context,
      builder: (context) => const PrivacyPolicyView(),
    );
  }

  final bool isDialog;

  @override
  Widget build(BuildContext context) {
    final init = useCallback(
      () {
        context.read<EventTrackerService>().logEvent(
              schema: 'Privacy Policy Viewed',
              description: 'user viewed privacy policy',
            );
      },
      [],
    );

    useEffect(
      () {
        init();
        return null;
      },
      [],
    );

    if (!isDialog) {
      final l10n = context.l10n;
      return Scaffold(
        appBar: FroggyAppBar(label: l10n.onboardingPageFooterPrivacy),
        body: MarkdownReaderWidget(
          markdownFile: 'assets/docs/privacy-policy.md',
          isDialog: false,
        ),
      );
    }

    return MarkdownReaderWidget(
      markdownFile: 'assets/docs/privacy-policy.md',
      isDialog: isDialog,
    );
  }
}
