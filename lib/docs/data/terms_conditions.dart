import 'package:froggytalk/docs/data/document_model.dart';

const termsAndConditions = DocumentModel(
  items: [
    DocumentItemModel(
      title: '1. Applicability',
      content: '''
These general terms and conditions (the "Terms") apply to every offer
made by Applebeam Technology doing business as ("FroggyTalk") and to
every contract that has been realized between FroggyTalk and a user (the
"User") using the mobile application FroggyTalk (the "Software"),
purchasing FroggyTalk Call Credits or Vouchers (the "Products") or with
regard to the mobile services provided by FroggyTalk (the "Services").
These services only include charged calls and free directory listings.

FroggyTalk may make changes to these Terms from time to time. FroggyTalk
will publish the changes at FroggyTalk.com/terms-and-conditions. Changes
to these Terms will be effective when published. We recommend reviewing
the Terms on a regular basis.

By accepting our Terms and conditions, you also accept our privacy
policy as published at www.FroggyTalk.com/privacy. Changes to this
privacy policy will be effective when published. We recommend reviewing
the privacy policy on a regular basis. Our privacy spolicy is in line
with the General Data Protection Regulation (GDPR) act which came into
effect in the European Economic Area from May 2018.

If you do not agree to the Terms of Service, you may not activate or use
the Services nor Software. Once accepted the Terms of Service constitute
a binding agreement between you and FroggyTalk. In addition, by
downloading the App from Appstore or Google Play you agree to the
applicable terms of the license of Apple and Google which are binding to
you.
''',
    ),
    DocumentItemModel(
      title: '2.  Identity of FroggyTalk',
      content: '''
Name: Applebeam Technology

Registered address: Vergiliushof, 24A, 6215GG, Maastricht, Netherlands

Email address: <EMAIL>

Chamber of Commerce number: 90386183
''',
    ),
  ],
);
