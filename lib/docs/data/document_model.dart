import 'package:freezed_annotation/freezed_annotation.dart';

part 'document_model.freezed.dart';

@freezed
class DocumentModel with _$DocumentModel {
  const factory DocumentModel({
    @Default([]) List<DocumentItemModel> items,
  }) = _DocumentModel;
}

@freezed
class DocumentItemModel with _$DocumentItemModel {
  const factory DocumentItemModel({
    String? title,
    String? content,
  }) = _DocumentItemModel;
}
