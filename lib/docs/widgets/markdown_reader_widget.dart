import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:utils/utils.dart';

class MarkdownReaderWidget extends StatelessWidget {
  MarkdownReaderWidget({
    required this.markdownFile,
    this.isDialog = true,
    super.key,
  }) : assert(
          markdownFile.contains('.md'),
          "The markdown file must have a '.md' extension.",
        );

  final String markdownFile;
  final bool isDialog;

  @override
  Widget build(BuildContext context) {
    if (!isDialog) {
      return ColoredBox(
        color: Colors.white,
        child: _BuildMarkdownContent(
          markdownFile: markdownFile,
          isDialog: isDialog,
        ),
      );
    }

    return Dialog(
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(5)),
      ),
      child: _BuildMarkdownContent(
        markdownFile: markdownFile,
        isDialog: isDialog,
      ),
    );
  }
}

class _BuildMarkdownContent extends StatelessWidget {
  const _BuildMarkdownContent({
    required this.markdownFile,
    this.isDialog = true,
  });

  final bool isDialog;
  final String markdownFile;

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;

    return Column(
      children: [
        Expanded(
          child: FutureBuilder<String>(
            future: rootBundle.loadString(markdownFile),
            builder: (context, snapshot) {
              if (snapshot.hasData) {
                final mdContent = snapshot.data ?? '';
                final splitContent = mdContent.split('<br/>');

                return ListView.builder(
                  shrinkWrap: true,
                  itemCount: splitContent.length,
                  itemBuilder: (context, index) {
                    final content = splitContent[index];
                    final split = content.split('\n');
                    final heading = split[0];
                    final cleanedHeading =
                        heading == '' && split.length > 1 ? split[1] : heading;
                    // print('heading is ${content.split('\n')}');

                    return Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 5,
                        horizontal: 10,
                      ),
                      child: ExpansionTile(
                        backgroundColor: FroggyColors.fromHexToColor('#f4f4f4'),
                        shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.all(Radius.circular(5)),
                          side: BorderSide(
                            color: FroggyColors.froggyGrey4,
                          ),
                        ),
                        collapsedShape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.all(Radius.circular(5)),
                          side: BorderSide(
                            color: FroggyColors.froggyGrey4,
                          ),
                        ),
                        title: Text(
                          cleanedHeading.replaceAll('**', ''),
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            color: FroggyColors.froggyGreen,
                          ),
                        ),
                        children: [
                          Markdown(
                            data: content.replaceAll(cleanedHeading, ''),
                            selectable: true,
                            shrinkWrap: true,
                            onTapLink: (text, href, title) {
                              final url = href;
                              if (url != null) {
                                FroggyUrl.open(url);
                              }
                            },
                            styleSheet: MarkdownStyleSheet(
                              a: const TextStyle(
                                color: FroggyColors.froggyRed,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );

                    // return Markdown(
                    //   data: content,
                    //   selectable: true,
                    //   shrinkWrap: true,
                    //   onTapLink: (text, href, title) {
                    //     final url = href;
                    //     if (url != null) {
                    //       FroggyUrl.open(url);
                    //     }
                    //   },
                    //   styleSheet: MarkdownStyleSheet(
                    //     a: const TextStyle(
                    //       color: FroggyColors.froggyRed,
                    //     ),
                    //   ),
                    // );
                  },
                );
              }

              return const Center(child: CircularProgressIndicator());
            },
          ),
        ),
        if (isDialog)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 3, horizontal: 15),
            child: ElevatedButton(
              onPressed: () => Navigator.pop(context),
              child: Text(l10n.closeButtonText),
            ),
          ),
      ],
    );
  }
}
