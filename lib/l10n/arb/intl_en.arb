{"@@locale": "en", "accountBalanceCardTitle": "Balance", "@accountBalanceCardTitle": {"description": "Title of the account balance page"}, "all": "All", "@all": {"description": "Text shown for all notifications"}, "allowAccessButtonText": "Allow Access to Perform this Required Action", "@allowAccessButtonText": {"description": "Text shown when requesting the user to allow access to device features"}, "appBootingTextBelow": "Helping communities stay connected", "@appBootingTextBelow": {"description": "Text that shows below the app title on the booting screen"}, "appLanguageAppbarTitle": "App Language", "@appLanguageAppbarTitle": {"description": "This is a title for the app bar"}, "appTitle": "<PERSON><PERSON>T<PERSON>", "@appTitle": {"description": "The name of the app"}, "buyCreditAmountCustomPlaceholder": "min {min} - max {max}", "@buyCreditAmountCustomPlaceholder": {"description": "Placeholder text for the custom amount input field on the buy credit page", "placeholders": {"min": {"type": "String", "example": "$50"}, "max": {"type": "String", "example": "$500"}}}, "buyCreditAmountRecommended": "Recommended", "@buyCreditAmountRecommended": {"description": "Text that shows above the recommended amount on the buy credit page"}, "buyCreditAppBarTitle": "Buy Credit", "@buyCreditAppBarTitle": {"description": "The app page title for buy credit"}, "buyCreditButtonText": "Buy credit", "@buyCreditButtonText": {"description": "Text for the buy credit button on the account balance page"}, "buyCreditEnterCustomAmountLabel": "Enter custom amount", "@buyCreditEnterCustomAmountLabel": {"description": "Label for the custom amount input field on the buy credit page"}, "buyCreditPageTitle": "Preferred amount", "@buyCreditPageTitle": {"description": "Title of the buy credit page"}, "callButtonText": "Call", "@callButtonText": {"description": "Text for the call action button on a page"}, "callingAppBarTitle": "Calling", "@callingAppBarTitle": {"description": "Title of the calling page"}, "callingPageBluetoothOptionsText": "Bluetooth", "@callingPageBluetoothOptionsText": {"description": "Text that shows to toggle the bluetooth on the calling page"}, "callingPageCreditErrorCardTitle": "Out of call credit", "@callingPageCreditErrorCardTitle": {}, "callingPagePhoneOptionsText": "Phone", "@callingPagePhoneOptionsText": {"description": "Text that shows to toggle the phone on the calling page"}, "callingPageSpeakerOptionsText": "Speaker", "@callingPageSpeakerOptionsText": {"description": "Text that shows to toggle the speaker on the calling page"}, "callingPageTimeLeft": "{time} left", "@callingPageTimeLeft": {"description": "Text that shows the time left for the call to end", "placeholders": {"time": {"type": "String", "example": "2"}}}, "callLogTypeAnswered": "Answered", "@callLogTypeAnswered": {"description": "Text that shows when the call log type is answered"}, "callLogTypeBusy": "Busy", "@callLogTypeBusy": {"description": "Text that shows when the call log type is busy"}, "callLogTypeCancel": "Cancelled", "@callLogTypeCancel": {"description": "Text that shows when the call log type is cancelled"}, "callLogTypeIncoming": "Incoming", "@callLogTypeIncoming": {"description": "Text that shows when the call log type is incoming"}, "callLogTypeMissed": "Missed", "@callLogTypeMissed": {"description": "Text that shows when the call log type is missed"}, "callLogTypeOutgoing": "Outgoing", "@callLogTypeOutgoing": {"description": "Text that shows when the call log type is outgoing"}, "callLogTypeUnavailable": "Unavailable", "@callLogTypeUnavailable": {"description": "Text that shows when the call log type is unavailable"}, "callRatesAppBarTitle": "Call rates", "@callRatesAppBarTitle": {"description": "Title of the call rates page"}, "callTypeAnswered": "Outgoing call", "@callTypeAnswered": {"description": "Text that shows when the call is answered"}, "callTypeBusy": "Busy", "@callTypeBusy": {"description": "Text that shows when the call is busy"}, "callTypeCancel": "Cancelled call", "@callTypeCancel": {"description": "Text that shows when the call is cancelled"}, "callTypeIncoming": "Incoming call", "@callTypeIncoming": {"description": "Text that shows when the call is incoming"}, "callTypeMissed": "Missed call", "@callTypeMissed": {"description": "Text that shows when the call is missed"}, "callTypeOutgoing": "Outgoing call", "@callTypeOutgoing": {"description": "Text that shows when the call is outgoing"}, "callTypeUnavailable": "User Unavailable", "@callTypeUnavailable": {"description": "Text that shows when the user is unavailable"}, "cancelButtonText": "Cancel", "@cancelButtonText": {}, "changeChannelButtonText": "Change Channel", "@changeChannelButtonText": {"description": "Text shown on button to change radio channel"}, "chatWithLiveAgentAppbarTitle": "Live agent", "@chatWithLiveAgentAppbarTitle": {}, "closeButtonText": "Close", "@closeButtonText": {"description": "Text shown on buttons that close views or dialogs"}, "confirmationAppBarTitle": "Confirmation", "@confirmationAppBarTitle": {"description": "Title of the confirmation page"}, "confirmationFailedButtonText": "Try Again", "@confirmationFailedButtonText": {"description": "Text for the try again button on the confirmation page"}, "confirmationFailedContactSupportDescription": "If you have any questions or need help, please contact our support team", "@confirmationFailedContactSupportDescription": {"description": "Description that shows when the payment fails"}, "confirmationFailedContactSupportText": "Contact Support", "@confirmationFailedContactSupportText": {"description": "Text for the contact support button on the confirmation page"}, "confirmationFailedDescription": "Your payment was NOT successful. Please try again", "@confirmationFailedDescription": {"description": "Description that shows when the payment fails"}, "confirmationFailedTitle": "Payment Failed", "@confirmationFailedTitle": {"description": "Title that shows when the payment fails"}, "confirmationSuccessButtonText": "Done", "@confirmationSuccessButtonText": {"description": "Text for the success button on the confirmation page"}, "confirmationSuccessDescription": "{amount} has been successfully added to your balance", "@confirmationSuccessDescription": {"description": "Description that shows when the payment is successful", "placeholders": {"amount": {"type": "String", "example": "$10"}}}, "confirmationSuccessTitle": "Payment Successful", "@confirmationSuccessTitle": {"description": "Title that shows when the payment is successful"}, "contactsAllContactsTabText": "Contacts", "@contactsAllContactsTabText": {"description": "Text that shows above the contacts list on the home page"}, "contactsCallRateText": "{min} mins {secs} secs", "@contactsCallRateText": {"description": "Text that shows the duration of a call", "placeholders": {"min": {"type": "String", "example": "10"}, "secs": {"type": "String", "example": "3"}}}, "contactsFavouriteCallsTabText": "Favourite", "@contactsFavouriteCallsTabText": {"description": "Text that shows above the Favourite calls list on the home page"}, "contactsNoCallsEmptyMessage": "You do not have any recent calls", "@contactsNoCallsEmptyMessage": {"description": "Message that shows when the user has no recent calls"}, "contactsNoContactsButtonText": "View Contacts", "@contactsNoContactsButtonText": {"description": "Text for the view contacts button on the home page"}, "contactsSearchContactsPlaceholder": "Search  contact name", "@contactsSearchContactsPlaceholder": {"description": "Placeholder text for the search contact name"}, "contactsSearchForContactsPlaceholder": "Search for contact name", "@contactsSearchForContactsPlaceholder": {"description": "Placeholder text for the search input field on the contacts page"}, "couponAppliedButtonText": "Applied", "@couponAppliedButtonText": {"description": "Text shown when the coupon is successfully applied"}, "couponApplyButtonText": "Apply", "@couponApplyButtonText": {"description": "Text for the apply coupon button"}, "credit": "Credit", "@credit": {"description": "Text shown for credit"}, "deleteAccountWarning": "The action you are about to take cannot be reversed. Please confirm you are deleting your account.", "@deleteAccountWarning": {"description": "Warning message shown when user attempts to delete their account"}, "dialerAppBarTitle": "<PERSON><PERSON><PERSON>", "@dialerAppBarTitle": {"description": "Title of the dialer page"}, "dialerCopyActionText": "Copy", "@dialerCopyActionText": {"description": "Text for the copy action on the dialer page"}, "dialerErrorInvalidNumber": "Invalid number", "@dialerErrorInvalidNumber": {"description": "Error message that shows when the user enters an invalid number"}, "dialerErrorWithoutCountryCode": "Add country code (e.g +1)", "@dialerErrorWithoutCountryCode": {"description": "Error message that shows when the user does not add a country code"}, "dialerKeypadABC": "ABC", "@dialerKeypadABC": {"description": "Text that shows on the ABC button on the dialer page"}, "dialerKeypadDEF": "DEF", "@dialerKeypadDEF": {"description": "Text that shows on the DEF button on the dialer page"}, "dialerKeypadGHI": "GHI", "@dialerKeypadGHI": {"description": "Text that shows on the GHI button on the dialer page"}, "dialerKeypadJKL": "JKL", "@dialerKeypadJKL": {"description": "Text that shows on the JKL button on the dialer page"}, "dialerKeypadMNO": "MNO", "@dialerKeypadMNO": {"description": "Text that shows on the MNO button on the dialer page"}, "dialerKeypadPQRS": "PQRS", "@dialerKeypadPQRS": {"description": "Text that shows on the PQRS button on the dialer page"}, "dialerKeypadTUV": "TUV", "@dialerKeypadTUV": {"description": "Text that shows on the TUV button on the dialer page"}, "dialerKeypadWXYZ": "WXYZ", "@dialerKeypadWXYZ": {"description": "Text that shows on the WXYZ button on the dialer page"}, "dialerMuteStatusMuted": "Muted", "@dialerMuteStatusMuted": {"description": "Text that shows when the dialer is muted"}, "dialerMuteStatusUnmuted": "Un-Muted", "@dialerMuteStatusUnmuted": {"description": "Text that shows when the dialer is unmuted"}, "dialerPasteActionText": "Paste", "@dialerPasteActionText": {"description": "Text for the paste action on the dialer page"}, "dialerSearchResultsHeader": "Results", "@dialerSearchResultsHeader": {"description": "Header text for the search results on the dialer page"}, "dialerSearchResultsNoResults": "No results found", "@dialerSearchResultsNoResults": {"description": "Text that shows when no results are found for the search input field on the dialer page"}, "dialerStatusConnected": "Connected", "@dialerStatusConnected": {"description": "Text that shows when the dialer status is connected"}, "dialerStatusConnecting": "Calling", "@dialerStatusConnecting": {"description": "Text that shows when the dialer status is connecting"}, "dialerStatusEnded": "Ended", "@dialerStatusEnded": {"description": "Text that shows when the dialer status is ended"}, "dialerStatusError": "<PERSON><PERSON><PERSON>urred", "@dialerStatusError": {"description": "Text that shows when an error occurs in the dialer"}, "dialerStatusFailedToConnect": "Connect Failed", "@dialerStatusFailedToConnect": {"description": "Text that shows when the dialer fails to connect"}, "dialerStatusHold": "On Hold", "@dialerStatusHold": {"description": "Text that shows when the dialer status is on hold"}, "dialerStatusInitial": "Calling", "@dialerStatusInitial": {"description": "Text that shows when the dialer status is initial"}, "dialerStatusInitiating": "Calling", "@dialerStatusInitiating": {"description": "Text that shows when the dialer status is initiating"}, "dialerStatusRinging": "Ringing", "@dialerStatusRinging": {"description": "Text that shows when the dialer status is ringing"}, "dialerStatusUnknown": "unknown", "@dialerStatusUnknown": {"description": "Text that shows when the dialer status is unknown"}, "emptyContactList": "You have no contact list.", "@emptyContactList": {"description": "Message that shows when the user has no contact list"}, "emptyFavouriteContactList": "You have no favourite contact list.", "@emptyFavouriteContactList": {"description": "Message that shows when the user has no favourite contact list"}, "enterCouponOptionalPlaceholder": "Enter coupon (Optional)", "@enterCouponOptionalPlaceholder": {"description": "Placeholder text for entering an optional coupon code"}, "enterOtpPageAppBarSubtitle": "Step 2 of 3", "@enterOtpPageAppBarSubtitle": {}, "enterOtpPageAppBarTitle": "Getting Started", "@enterOtpPageAppBarTitle": {}, "enterOtpPageEditButton": "Wrong number? Please Edit", "@enterOtpPageEditButton": {"description": "Text that shows on the edit phone number button"}, "enterOtpPageErrorMessage": "Incorrect code", "@enterOtpPageErrorMessage": {"description": "Error message that shows when the user enters an incorrect OTP"}, "enterOtpPagePhoneLabel": "Enter your verification code", "@enterOtpPagePhoneLabel": {}, "enterOtpPagePhoneLabelDescription": "A 4 digit code has been sent to your number {phoneNumber}", "@enterOtpPagePhoneLabelDescription": {"description": "Description text for the OTP input field on the OTP entry page", "placeholders": {"phoneNumber": {"type": "String", "example": "+1234567890"}}}, "enterOtpPageResendOtpButton": "Resend Code", "@enterOtpPageResendOtpButton": {"description": "Text that shows on the confirm verificatio button"}, "enterOtpPageResendOtpRetries": "{count, plural, =0{No tries left} =1{1 final try left} other{{count} more tries}}", "@enterOtpPageResendOtpRetries": {"description": "Message showing the number of retries a user has left", "placeholders": {"count": {"type": "int", "example": "3"}}}, "enterOtpPageResendOtpRetriesError": "You cannot receive verification codes on this number for the next 24 hours", "@enterOtpPageResendOtpRetriesError": {"description": "Error message that shows when the user has exhausted the number of retries"}, "enterOtpPageSubmitButton": "Confirm OTP", "@enterOtpPageSubmitButton": {"description": "Text that shows on the confirm verificatio button"}, "enterVoucherCodeLabel": "Enter voucher code", "@enterVoucherCodeLabel": {"description": "Label for voucher code input field"}, "free": "FREE", "@free": {"description": "Text shown for free promotional content"}, "freeCreditAppbarTitle": "Free Credit", "@freeCreditAppbarTitle": {}, "freeCreditPageContent_1": "Share your referral code with your friends & family.", "@freeCreditPageContent_1": {}, "freeCreditPageContent_2": "They download FroggyTalk and register with your referral code.", "@freeCreditPageContent_2": {}, "freeCreditPageContent_3": "You earn {amount} when the person buys credit the first time.", "@freeCreditPageContent_3": {"description": "Text that shows the amount the user gets when the person they referred buys credit for the first time", "placeholders": {"amount": {"type": "String", "example": "$10"}}}, "freeCreditPageHeadingText": "Refer a friend & earn {amount}", "@freeCreditPageHeadingText": {"description": "Text that shows the amount the user gets when they refer a friend", "placeholders": {"amount": {"type": "String", "example": "$10"}}}, "freeCreditPageShareReferralLinkButtonText": "Share referral link", "@freeCreditPageShareReferralLinkButtonText": {}, "freeCreditPageSubHeadingText": "My referrals ({amount})", "@freeCreditPageSubHeadingText": {"description": "Text that shows the amount the user gets when they refer a friend", "placeholders": {"amount": {"type": "String", "example": "$10"}}}, "froggytalkCustomerLabel": "Froggytalk Customer", "@froggytalkCustomerLabel": {"description": "Default label shown when user name is not available"}, "helpCenterAppbarTitle": "Help Center", "@helpCenterAppbarTitle": {"description": "Title of the help center page"}, "helpCenterPageMenu_1": "Make a Complaint or Suggestion", "@helpCenterPageMenu_1": {"description": "Text that shows on the make a complaint or suggestion button on the help center page"}, "helpCenterPageMenu_2": "Chat with Live agent", "@helpCenterPageMenu_2": {"description": "Text that shows on the chat with live agent button on the help center page"}, "helpCenterPageMenu_3": "FAQ", "@helpCenterPageMenu_3": {"description": "Text that shows on the FAQ button on the help center page"}, "homeNavigationBarText": "Home", "@homeNavigationBarText": {"description": "navbar text for home page"}, "internetConnectionAlertTextError": "Oops! It looks like you''re offline", "@internetConnectionAlertTextError": {}, "internetConnectionAlertTextSuccess": "All set! You''re connected again.", "@internetConnectionAlertTextSuccess": {}, "invalidInternationalPhoneFormat": "Invalid International Phone Number Format", "@invalidInternationalPhoneFormat": {"description": "Error message shown when phone number format is invalid"}, "invalidPhoneNumber": "Please enter a valid Phone number", "@invalidPhoneNumber": {"description": "Error message shown when phone number is invalid"}, "keypadNavigationBarText": "Keypad", "@keypadNavigationBarText": {"description": "navbar text for keypad page"}, "landlineText": "Landline", "@landlineText": {"description": "Text that shows when the number is a landline number"}, "loadVoucherCardButtonText": "Recharge", "@loadVoucherCardButtonText": {}, "loadVoucherCardErrorText": "Wrong code, Try again", "@loadVoucherCardErrorText": {}, "loadVoucherCardLabelText": "Enter 10 digit voucher code", "@loadVoucherCardLabelText": {}, "loadVoucherCardTitle": "Load Voucher Code", "@loadVoucherCardTitle": {}, "loadVoucherCodeTitle": "Load Voucher Code", "@loadVoucherCodeTitle": {"description": "Title shown on the load voucher code dialog"}, "loginPageAppBarSubtitle": "Step 1 of 3", "@loginPageAppBarSubtitle": {"description": "Subtitle of the login page"}, "loginPageAppBarTitle": "Getting Started", "@loginPageAppBarTitle": {"description": "Title of the login page"}, "loginPageCheckboxLabel": "I have a referral code", "@loginPageCheckboxLabel": {}, "loginPageErrorMessage": "The number is not linked to a account", "@loginPageErrorMessage": {}, "loginPagePhoneLabel": "Enter your number.", "@loginPagePhoneLabel": {}, "loginPagePhoneLabelDescription": "A One-Time verification code will be sent to you via WhatsApp or SMS.", "@loginPagePhoneLabelDescription": {}, "loginPagePhoneNumberError": "Your phone number is incomplete, please complete it", "@loginPagePhoneNumberError": {}, "loginPageReferralLabel": "Enter referral code", "@loginPageReferralLabel": {}, "loginPageSubmitButton": "Request Verification", "@loginPageSubmitButton": {}, "minsOrMin": "{count, plural, zero{} one{1 min} other{{count} mins}}", "@minsOrMin": {"description": "Text that shows either '1 min' or '{count} mins' based on the count", "placeholders": {"count": {"type": "int", "example": "5"}}}, "mobileText": "Mobile", "@mobileText": {"description": "Text that shows when the number is a mobile number"}, "moreAppbarTitle": "Account", "@moreAppbarTitle": {}, "moreNavigationBarText": "More", "@moreNavigationBarText": {"description": "navbar text for more page"}, "morePageAccountBalanceCardTitle": "Balance", "@morePageAccountBalanceCardTitle": {}, "morePageAppVersionMenuText": "App version", "@morePageAppVersionMenuText": {}, "morePageCallRatesMenuText": "Call rates", "@morePageCallRatesMenuText": {}, "morePageHelpCenterMenuText": "Help Center", "@morePageHelpCenterMenuText": {}, "morePageLanguageMenuText": "Language", "@morePageLanguageMenuText": {}, "morePageLoadVoucherMenuText": "<PERSON><PERSON>", "@morePageLoadVoucherMenuText": {}, "morePageLogoutMenuText": "Log out", "@morePageLogoutMenuText": {}, "morePageProfileMenuText": "Profile", "@morePageProfileMenuText": {}, "morePageRadioMenuText": "Radio", "@morePageRadioMenuText": {}, "morePageReferralCodeCardButtonText": "More info", "@morePageReferralCodeCardButtonText": {}, "morePageReferralCodeCardContent": "{numberOfPeople} people have used your referral code", "@morePageReferralCodeCardContent": {"description": "Text that shows the number of people that have used the referral code", "placeholders": {"numberOfPeople": {"type": "int", "example": "5"}}}, "morePageReferralCodeCardDescription": "Get {amount} when you refer your friends & family", "@morePageReferralCodeCardDescription": {"description": "Text that shows the amount the user gets when they refer their friends and family", "placeholders": {"amount": {"type": "String", "example": "$10"}}}, "morePageReferralCodeCardTitle": "Referral code", "@morePageReferralCodeCardTitle": {}, "noBuyCreditOptionsAvailable": "No Buy Credit Options Available", "@noBuyCreditOptionsAvailable": {"description": "Message that shows when there are no buy credit options available"}, "noFavoriteContactsMessage": "No Favourite Contacts", "@noFavoriteContactsMessage": {"description": "Message shown when there are no favorite contacts to display"}, "noNotificationsYet": "No notifications yet", "@noNotificationsYet": {"description": "Text shown when there are no notifications"}, "notEnoughCreditMessage": "You don''t have enough credit", "@notEnoughCreditMessage": {"description": "Message shown when user has insufficient credit for a call"}, "notification": "Notification", "@notification": {"description": "Text shown for a single notification"}, "notificationsAppBarTitle": "Notifications", "@notificationsAppBarTitle": {"description": "Title of the notifications page"}, "notificationsEmptyMessage": "You do not have any notifications", "@notificationsEmptyMessage": {"description": "Message that shows when the user has no notifications"}, "notificationSettingsAppBarTitle": "Notification Settings", "@notificationSettingsAppBarTitle": {"description": "Title of the notification settings page"}, "notificationSettingsAutoDeleteNotificationText": "Auto delete notification", "@notificationSettingsAutoDeleteNotificationText": {"description": "Text that shows to toggle the auto delete notification on the notification settings page"}, "notificationSettingsNotificationSoundText": "Notification sound", "@notificationSettingsNotificationSoundText": {"description": "Text that shows to toggle the notification sound on the notification settings page"}, "notificationsTabAllText": "All", "@notificationsTabAllText": {"description": "Text that shows on the all tab on the notifications page"}, "notificationsTabUnreadText": "Unread", "@notificationsTabUnreadText": {"description": "Text that shows on the unread tab on the notifications page"}, "notificationsTimeAgo": "{time} {time, plural, one{{duration}} other{{duration}s}} ago", "@notificationsTimeAgo": {"description": "Text that shows the time since the notification was received", "placeholders": {"time": {"type": "int", "example": "2"}, "duration": {"type": "String", "example": "hours"}}}, "okButtonText": "Ok", "@okButtonText": {"description": "Text shown on OK confirmation button"}, "onboardingPageFooterAndText": "and", "@onboardingPageFooterAndText": {"description": "Text that shows between the terms and conditions link and the privacy policy link on the onboarding page"}, "onboardingPageFooterPrivacy": "Privacy Policy", "@onboardingPageFooterPrivacy": {"description": "Text that shows below the submit button on the onboarding page"}, "onboardingPageFooterTermsConditionsText": "Terms and Conditions", "@onboardingPageFooterTermsConditionsText": {"description": "Text that shows below the submit button on the onboarding page"}, "onboardingPageFooterText": "By Clicking Get Started, you are accepting the", "@onboardingPageFooterText": {"description": "Text that shows below the submit button on the onboarding page"}, "onboardingPageSliderHeader1": "Multi-Language", "@onboardingPageSliderHeader1": {"description": "Header text for the first slide on the onboarding page"}, "onboardingPageSliderHeader2": "Call mobile or landline numbers worldwide", "@onboardingPageSliderHeader2": {"description": "Header text for the second slide on the onboarding page"}, "onboardingPageSliderHeader3": "Easy payment", "@onboardingPageSliderHeader3": {"description": "Header text for the third slide on the onboarding page"}, "onboardingPageSliderText1": "FroggyTalk allows you select your preferred language e.g Tignrigna, Amharic, Hausa etc.", "@onboardingPageSliderText1": {"description": "Text for the first slide on the onboarding page"}, "onboardingPageSliderText2": "Make calls to any mobile or landline anywhere in the world. The receiver does not need a smartphone or internet connection.", "@onboardingPageSliderText2": {"description": "Text for the second slide on the onboarding page"}, "onboardingPageSliderText3": "Buy call credit in your local currency with your preferred payment method.", "@onboardingPageSliderText3": {"description": "Text for the third slide on the onboarding page"}, "onboardingPageSubmitButtonText": "Get Started", "@onboardingPageSubmitButtonText": {"description": "Text for the submit button on the onboarding page"}, "outOfCreditLabel": "Out of credit", "@outOfCreditLabel": {"description": "Label shown when user has no remaining credit"}, "paymentFailureAppBarTitle": "Failed", "@paymentFailureAppBarTitle": {"description": "Title shown in app bar of payment failure page"}, "paymentFailureHomeButton": "Home", "@paymentFailureHomeButton": {"description": "Text for button to return to home page"}, "paymentFailureMessage": "Your payment of {amount} was unsuccessful", "@paymentFailureMessage": {"description": "Message shown when payment fails, includes amount", "placeholders": {"amount": {"type": "String", "example": "$50.00"}}}, "paymentFailureTitle": "Payment Unsuccessful", "@paymentFailureTitle": {"description": "Main title shown when payment fails"}, "paymentFailureTryAgainButton": "Try Again", "@paymentFailureTryAgainButton": {"description": "Text for button to retry payment"}, "paymentOptionsAppBarTitle": "Payment Options", "@paymentOptionsAppBarTitle": {"description": "Title of the payment options page"}, "paymentOptionsAutoCreditDescription": "This will automatically credit your account with {amount} when your balance is below  {minAmount}.", "@paymentOptionsAutoCreditDescription": {"description": "Description of the auto credit payment method", "placeholders": {"amount": {"type": "String", "example": "$10"}, "minAmount": {"type": "String", "example": "$5"}}}, "paymentOptionsAutoCreditTitle": "Auto Credit", "@paymentOptionsAutoCreditTitle": {"description": "Title of the auto credit payment method"}, "paymentOptionsSelectPaymentMethod": "Choose preferred method of payment", "@paymentOptionsSelectPaymentMethod": {"description": "Text that shows above the payment method selection dropdown"}, "paymentOptionsSummaryAmountToCreditText": "Amount to be credited", "@paymentOptionsSummaryAmountToCreditText": {"description": "Text that shows the amount to be credited on the payment options page"}, "paymentOptionsSummaryDiscountButtonText": "Apply", "@paymentOptionsSummaryDiscountButtonText": {"description": "Text for the apply discount button on the payment options page"}, "paymentOptionsSummaryDiscountPlaceholder": "Enter discount code(optional)", "@paymentOptionsSummaryDiscountPlaceholder": {"description": "Placeholder text for the discount code input field on the payment options page"}, "paymentOptionsSummaryDiscountText": "Discount", "@paymentOptionsSummaryDiscountText": {"description": "Text that shows the discount on the payment options page"}, "paymentOptionsSummaryTotalPaymentText": "Total payment", "@paymentOptionsSummaryTotalPaymentText": {"description": "Text that shows the total payment on the payment options page"}, "paymentOptionsSummaryVatFeesText": "VAT + fees", "@paymentOptionsSummaryVatFeesText": {"description": "Text that shows the VAT and fees on the payment options page"}, "paymentSummaryAmountToCreditLabel": "Amount to be Credited", "@paymentSummaryAmountToCreditLabel": {"description": "Label for the amount to be credited in the payment summary"}, "paymentSummaryDiscountLabel": "Discount", "@paymentSummaryDiscountLabel": {"description": "Label for the discount in the payment summary"}, "paymentSummaryTotalLabel": "Total payment", "@paymentSummaryTotalLabel": {"description": "Label for the total payment in the payment summary"}, "paymentSummaryVatFeesLabel": "VAT + Fees", "@paymentSummaryVatFeesLabel": {"description": "Label for the VAT and additional fees in the payment summary"}, "perMinRate": "{rate}/min", "@perMinRate": {"description": "Rate per minute for a call", "placeholders": {"rate": {"type": "String", "example": "$10"}}}, "perMinRateSingle": "/min", "@perMinRateSingle": {"description": "Rate per minute for a call in short form"}, "perMinuteRate": "{rate}/minute", "@perMinuteRate": {"description": "Rate per minute for a call", "placeholders": {"rate": {"type": "String", "example": "$10"}}}, "perMinuteSlashLabel": "{min}/minutes", "@perMinuteSlashLabel": {"description": "Label showing '/minutes' for call rate displays", "placeholders": {"min": {"type": "String", "example": "5"}}}, "permissionButtonAccept": "Allow Access", "@permissionButtonAccept": {"description": "Text for the accept button on the permission request dialog"}, "permissionButtonSkip": "<PERSON><PERSON>", "@permissionButtonSkip": {"description": "Text for the skip button on the permission request dialog"}, "permissionForCameraTitle": "We need permission to capture photos and videos", "@permissionForCameraTitle": {"description": "Title for the camera permission request dialog"}, "permissionForContactListTitle": "Give FroggyTalk permission to access your Contact list / Phonebook", "@permissionForContactListTitle": {"description": "Title for the contact list permission request dialog"}, "permissionForMicrophoneTitle": "Give FroggyTalk access to your Microphone to process calls", "@permissionForMicrophoneTitle": {"description": "Title for the microphone permission request dialog"}, "permissionForNotificationTitle": "Give FroggyTalk permission to send you Updates", "@permissionForNotificationTitle": {"description": "Title for the notification permission request dialog"}, "permissionForStorageTitle": "We need permission to store temporary data on your device", "@permissionForStorageTitle": {"description": "Title for the storage permission request dialog"}, "permissionPermanentlyDeniedMessage": "Permission is permanently denied, Please enable it in the app settings", "@permissionPermanentlyDeniedMessage": {"description": "Message shown when a permission is permanently denied by the user"}, "permissionDeniedMessage": "Permission is denied, Please enable it in the app settings", "@permissionDeniedMessage": {"description": "Message shown when a permission is denied by the user"}, "perM_Rate": "{rate}/m", "@perM_Rate": {"description": "Rate per minute for a call", "placeholders": {"rate": {"type": "String", "example": "$10"}}}, "phoneNumberStartRule": "Please start your entry with either + or 00", "@phoneNumberStartRule": {"description": "Error message shown when phone number doesn't start with + or 00"}, "proceedToPaymentButtonText": "Proceed", "@proceedToPaymentButtonText": {"description": "Text for the proceed to payment button on the buy credit page"}, "profileAppbarTitle": "Profile", "@profileAppbarTitle": {}, "profileDeleteAccountButtonText": "Delete account", "@profileDeleteAccountButtonText": {}, "profileDeleteAccountCardContent": "Please confirm you want your account deleted. You will lose any balance on your account.", "@profileDeleteAccountCardContent": {}, "profileDeleteAccountCardTitle": "Delete account", "@profileDeleteAccountCardTitle": {}, "profileLabel": "Profile", "@profileLabel": {"description": "Label used for profile sections"}, "profileLabelChangeLocation": "Change location", "@profileLabelChangeLocation": {}, "profileLabelEmailAddress": "Enter email address", "@profileLabelEmailAddress": {}, "profileLabelFullName": "Enter full name", "@profileLabelFullName": {"description": "profile<PERSON>abelFull<PERSON>ame"}, "profileUpdatedSuccessfullyMessage": "Profile updated successfully", "@profileUpdatedSuccessfullyMessage": {"description": "Message shown when profile is updated successfully"}, "profileUpdateFailedMessage": "Profile update failed", "@profileUpdateFailedMessage": {"description": "Message shown when profile update fails"}, "quickAdvertSubtitle": "Buy airtime for your family worldwide", "@quickAdvertSubtitle": {"description": "Subtitle for the quick advert on the home page"}, "quickAdvertTitle": "Send credit to 140+ countries", "@quickAdvertTitle": {"description": "Title for the quick advert on the home page"}, "radioAppBarTitle": "Radio", "@radioAppBarTitle": {}, "radioComingSoonText": "Radio Coming soon !!!", "@radioComingSoonText": {"description": "Text shown when radio feature is not yet available"}, "radioPageChooseChannelButtonText": "Choose Channel", "@radioPageChooseChannelButtonText": {}, "radioPageNoChannelsText": "No channels found", "@radioPageNoChannelsText": {}, "radioPageSearchPlaceholder": "Choose Channel", "@radioPageSearchPlaceholder": {}, "ratesNavigationBarText": "Rates", "@ratesNavigationBarText": {"description": "navbar text for rates page"}, "recentCallsText": "Recent Calls", "@recentCallsText": {"description": "Text that shows above the recent calls list on the home page"}, "rechargeButtonText": "Recharge", "@rechargeButtonText": {"description": "Text shown on recharge button"}, "referAndEarn": "Refer & Earn", "@referAndEarn": {"description": "Text shown for refer and earn promotional text"}, "referralCardButtonText": "Free Credit", "@referralCardButtonText": {"description": "Text for the referral card button"}, "referralCardDescription1": "Refer someone and get {percentageAmount} free credit.", "@referralCardDescription1": {"description": "Message showing the referral benefit", "placeholders": {"percentageAmount": {"type": "String", "example": "10%"}}}, "referralCardDescription2": "{numberOfPeople, plural, zero{No one has used your referral code.} one{One person has used your referral code.} other{{numberOfPeople} people have used your referral code.}}", "@referralCardDescription2": {"description": "Message showing how many people have used the referral code, with pluralization.", "placeholders": {"numberOfPeople": {"type": "int", "example": "2"}}}, "referralCardTitle": "Referral Code", "@referralCardTitle": {"description": "Title for the referral card"}, "referralCodeOnCopyActionResponse": "FroggyTalk pasted to clipboard", "@referralCodeOnCopyActionResponse": {}, "saveAndProceedToPaymentButtonText": "Save & Proceed to Payment", "@saveAndProceedToPaymentButtonText": {"description": "Text shown on button to save profile changes and proceed to payment"}, "saveChangesButtonText": "Save changes", "@saveChangesButtonText": {}, "savedSuccessfullyMessage": "Saved successfully", "@savedSuccessfullyMessage": {"description": "Message shown when changes are saved successfully"}, "searchCountryPlaceholder": "Search country", "@searchCountryPlaceholder": {"description": "Placeholder text for the search input field on the country selection page"}, "searchFavouriteContactMessage": "Search For Favourite Contact", "@searchFavouriteContactMessage": {"description": "Placeholder text for searching favorite contacts"}, "searchForCountryNoResults": "No results found", "@searchForCountryNoResults": {"description": "Text that shows when no results are found for the search input field on the country selection page"}, "searchForCountryPlaceholder": "Search for Country", "@searchForCountryPlaceholder": {"description": "Placeholder text for the search input field on the country selection page"}, "searchRecentCallsMessage": "Search Recent Calls", "@searchRecentCallsMessage": {"description": "Placeholder text for searching recent calls"}, "secsOrSec": "{count, plural, zero{} one{1 sec} other{{count} secs}}", "@secsOrSec": {"description": "Text that shows either '1 sec' or '{count} secs' based on the count", "placeholders": {"count": {"type": "int", "example": "10"}}}, "selectCountryPlaceholder": "Select country", "@selectCountryPlaceholder": {"description": "Placeholder text for the country selection dropdown"}, "sendCreditButtonText": "Send Credit", "@sendCreditButtonText": {"description": "Text for the send credit button on the home page"}, "settings": "Settings", "@settings": {"description": "Text shown for settings"}, "shareReferralText": "Download FroggyTalk App {appLink} and register with my referral code {referralCode}. It has the best rates and international calling experience.", "@shareReferralText": {"description": "Text shown when sharing referral code", "placeholders": {"appLink": {"type": "String", "example": "https://link-to.app/AHA423kdwG"}, "referralCode": {"type": "String", "example": "ABC123"}}}, "somethingWentWrongMessage": "Something went wrong. Please try again.", "@somethingWentWrongMessage": {"description": "Message that shows when an error occurs"}, "unread": "Unread", "@unread": {"description": "Text shown for unread notifications"}, "updateProfileButtonText": "Update Profile", "@updateProfileButtonText": {"description": "Text shown on button to update user profile"}, "upgradeDialogButtonNo": "Not Now", "@upgradeDialogButtonNo": {"description": "Text for the button to dismiss the update dialog"}, "upgradeDialogButtonYes": "Update", "@upgradeDialogButtonYes": {"description": "Text for the button to proceed with the update"}, "upgradeDialogMessage": "A new version of FroggyTalk is available. Would you like to update now?", "@upgradeDialogMessage": {"description": "Message prompting the user to update to the latest version"}, "upgradeDialogTitle": "Update Available", "@upgradeDialogTitle": {"description": "Title shown when a new update is available"}, "validationCouponCodeIncomplete": "Discount code is incomplete or invalid", "@validationCouponCodeIncomplete": {"description": "Error message shown when coupon code length or format is invalid"}, "validationCouponInvalid": "Please enter a valid discount code", "@validationCouponInvalid": {"description": "Error message shown when coupon code is invalid"}, "validationEmailInvalid": "Invalid email address.", "@validationEmailInvalid": {"description": "Error message shown when email format is invalid"}, "validationFieldIsRequired": "{fieldName} is required", "@validationFieldIsRequired": {"description": "Generic required field error message", "placeholders": {"fieldName": {"type": "String", "example": "Email"}}}, "validationMinLengthError": "{fieldName} must be at least {minLength} characters long", "@validationMinLengthError": {"description": "Error message for fields that don't meet minimum length requirement", "placeholders": {"fieldName": {"type": "String", "example": "Password"}, "minLength": {"type": "int", "example": "8"}}}, "validationPhoneNumberIncomplete": "Incomplete phone number.", "@validationPhoneNumberIncomplete": {"description": "Error message shown when phone number is incomplete"}, "viewButtonText": "View", "@viewButtonText": {"description": "Text for the view action button on page"}, "viewContactDetailAppBarTitle": "Contact Details", "@viewContactDetailAppBarTitle": {"description": "Title of the contact detail page"}, "voucherLoadedSuccessMessage": "Voucher Loaded Successfully", "@voucherLoadedSuccessMessage": {"description": "Message shown when voucher is successfully loaded"}, "securedByStripe": "Secured by <PERSON><PERSON>", "@securedByStripe": {"description": "Text showing that payment is secured by Stripe payment processor"}, "nextTrack": "Next Track", "@nextTrack": {"description": "Text shown on button to play the next radio track or station"}, "stopRadio": "Stop Radio", "@stopRadio": {"description": "Text shown on button to stop the currently playing radio"}, "playRadio": "Play Radio", "@playRadio": {"description": "Text shown on button to start playing the radio"}, "previousTrack": "Previous Track", "@previousTrack": {"description": "Text shown on button to play the previous radio track or station"}, "channel": "Channel", "@channel": {"description": "Text shown to represent a radio channel or station"}, "allChannels": "All Channels", "@allChannels": {"description": "Text shown to represent all available radio channels or stations"}, "acceptButtonText": "Accept", "@acceptButtonText": {"description": "Text shown on buttons that accept or confirm an action"}, "declineButtonText": "Decline", "@declineButtonText": {"description": "Text shown on buttons that decline or reject an action"}, "allowShareInfoWithRadio": "Allow us share your information with {radioName}", "@allowShareInfoWithRadio": {"description": "Text shown when requesting permission to share user information with a radio station", "placeholders": {"radioName": {"type": "String", "example": "KEXP Radio"}}}, "nowPlaying": "Now Playing", "@nowPlaying": {"description": "Text shown to indicate the currently playing radio track or station"}, "checkBackLaterText": "Please check back later", "@checkBackLaterText": {"description": "Message shown when a feature or content is temporarily unavailable"}, "refreshText": "Refresh", "@refreshText": {"description": "Text shown on button to refresh or reload content"}, "noRadioStationsAvailableText": "No radio stations available", "@noRadioStationsAvailableText": {"description": "Message shown when there are no radio stations to display"}, "tryAgainText": "Try Again", "@tryAgainText": {"description": "Text shown on button to retry an action that failed"}, "unknownErrorText": "An unknown error occurred", "@unknownErrorText": {"description": "Generic message shown when an unspecified error occurs"}, "errorLoadingRadioStationsText": "Error loading radio stations", "@errorLoadingRadioStationsText": {"description": "Message shown when radio stations fail to load"}, "loadingRadioStationsText": "Loading radio stations...", "@loadingRadioStationsText": {"description": "Message shown while radio stations are being loaded"}, "helpCenterPageMenu_4": "WhatsApp", "@helpCenterPageMenu_4": {"description": "Text that shows on the WhatsApp button on the help center page"}, "validationLengthError": "{fieldName} must be between {minLength} and {maxLength} characters long", "@validationLengthError": {"description": "Error message for fields that don't meet length requirements", "placeholders": {"fieldName": {"type": "String", "example": "Password"}, "minLength": {"type": "int", "example": "8"}, "maxLength": {"type": "int", "example": "16"}}}, "chooseLanguage": "Choose Language", "@chooseLanguage": {"description": "Title for the language selection modal"}, "selectPreferredLanguage": "Select your preferred language", "@selectPreferredLanguage": {"description": "Subtitle for the language selection modal"}, "cancel": "Cancel", "@cancel": {"description": "Text for cancel buttons"}, "apply": "Apply", "@apply": {"description": "Text for apply/confirm buttons"}, "languageChangeError": "Failed to change language. Please try again.", "@languageChangeError": {"description": "Error message shown when language change fails"}, "noLanguagesAvailable": "No languages available", "@noLanguagesAvailable": {"description": "Message shown when no languages are available to select"}, "languageSetupPageAppBarSubtitle": "Step 3 of 3", "@languageSetupPageAppBarSubtitle": {"description": "Subtitle of the profile setup page showing progress"}, "welcomeBackText": "Welcome Back, {firstName}", "@welcomeBackText": {"description": "Greeting message shown to returning users", "placeholders": {"firstName": {"type": "String", "example": "<PERSON>"}}}, "froggyTalkLovesYou": "FroggyTalk Loves You.", "@froggyTalkLovesYou": {"description": "Affirmative message showing app's appreciation for users"}, "updateLanguagePrompt": "Please click the button below to update your language", "@updateLanguagePrompt": {"description": "Instruction text prompting user to update their language setting"}, "updateYourLanguage": "Update Your Language.", "@updateYourLanguage": {"description": "Button or action text for language update functionality"}, "inAppPurchaseLabel": "In-App Purchase", "@inAppPurchaseLabel": {"description": "Label indicating this is an in-app purchase from the app store"}, "restorePurchases": "<PERSON><PERSON> Purchases", "@restorePurchases": {"description": "Button text for restoring previous in-app purchases"}, "storePrice": "Store Price", "@storePrice": {"description": "Label for the price listed in the app store"}, "timeLeftModalTitle": "{days} days left", "@timeLeftModalTitle": {"description": "Title showing the number of days left in the modal", "placeholders": {"days": {"type": "String", "example": "2"}}}, "timeLeftModalMessage": "{days} days left to be a winner of the Cash prize. Refer {count} people and be No 1", "@timeLeftModalMessage": {"description": "Message showing days left and number of referrals needed", "placeholders": {"days": {"type": "String", "example": "25"}, "count": {"type": "int", "example": "30"}}}, "confirm": "Confirm", "@confirm": {"description": "Text for confirm/verification buttons"}, "languageSelectionDisclaimer": "You can change your language under", "@languageSelectionDisclaimer": {"description": "Message informing users where they can find language settings"}, "activateAutoCredit": "Activate auto credit", "@activateAutoCredit": {"description": "Text for button to enable automatic account crediting"}, "autoAccountCreditHelp": "will be charged to your card and added to your balance whenever it falls below", "@autoAccountCreditHelp": {"description": "Explanation text for the auto credit feature"}, "avoidCallDisruption": "to avoid being cut off during a call", "@avoidCallDisruption": {"description": "Additional explanation for auto credit feature benefit"}, "cashPrizeAmount": "{currencySymbol}{amount} FREE Cash", "@cashPrizeAmount": {"description": "Text showing the cash prize amount for promotions and referrals", "placeholders": {"currencySymbol": {"type": "String", "example": "€"}, "amount": {"type": "String", "example": "250"}}}, "monthlyReferralBoard": "{month} Promo Leaders", "@monthlyReferralBoard": {"description": "Title for the monthly promotion leaderboard showing top referrers", "placeholders": {"month": {"type": "String", "example": "Jan'25"}}}, "termsAndConditionsApply": "Terms and conditions apply", "@termsAndConditionsApply": {"description": "Text indicating that terms and conditions apply to an offer or promotion"}, "getNow": "Get Now", "@getNow": {"description": "Text for a button or link to get an offer or promotion"}, "payWithGooglePay": "Checkout with Google Pay", "@payWithGooglePay": {"description": "Text shown on button to pay using Google Pay"}, "payWithApplePay": "Pay via AppStore", "@payWithApplePay": {"description": "Text shown on button to pay using Apple Pay"}, "paymentSummaryPlayStoreFeeLabel": "+ {percentage} Commission & VAT", "@paymentSummaryPlayStoreFeeLabel": {"description": "Label showing the Play Store fee percentage and VAT in payment summary", "placeholders": {"percentage": {"type": "String", "example": "30"}}}, "failedToLoadLeaderboard": "Failed to load leaderboard", "@failedToLoadLeaderboard": {"description": "Error message displayed when the referral leaderboard cannot be loaded"}, "retry": "Retry", "@retry": {"description": "Text for button to retry a failed operation"}, "googlePay": "Google Pay", "@googlePay": {"description": "Text shown on button to pay using Google Pay"}, "appStoreCost": "+ {value} App Store Cost", "@appStoreCost": {"description": "Label showing the cost charged by the App Store including fees and VAT", "placeholders": {"value": {"type": "String", "example": "USD 2.99"}}}, "notification_call_reminder_title": "Call Reminder", "@notification_call_reminder_title": {"description": "Title for call reminder notifications"}, "notification_call_reminder_body": "Don''t forget to call {contact}", "@notification_call_reminder_body": {"description": "Body text for call reminder notifications", "placeholders": {"contact": {"type": "String", "example": "<PERSON>"}}}, "notification_promotion_title": "Special Offer", "@notification_promotion_title": {"description": "Title for promotion notifications"}, "notification_promotion_body": "New offer: {offer} - {description}", "@notification_promotion_body": {"description": "Body text for promotion notifications", "placeholders": {"offer": {"type": "String", "example": "50% Off International Calls"}, "description": {"type": "String", "example": "Save on your next international call"}}}, "notification_security_alert_title": "Security Alert", "@notification_security_alert_title": {"description": "Title for security alert notifications"}, "notification_security_alert_body": "Security alert: {alertType} detected on {device}", "@notification_security_alert_body": {"description": "Body text for security alert notifications", "placeholders": {"alertType": {"type": "String", "example": "Login attempt"}, "device": {"type": "String", "example": "iPhone 12"}}}, "notification_daily_reminder_title": "Daily Reminder", "@notification_daily_reminder_title": {"description": "Title for daily reminder notifications"}, "notification_daily_reminder_body": "Don''t forget: {reminderType}", "@notification_daily_reminder_body": {"description": "Body text for daily reminder notifications", "placeholders": {"reminderType": {"type": "String", "example": "Check your balance"}}}}