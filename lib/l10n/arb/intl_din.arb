{"@@locale": "din", "acceptButtonText": "<PERSON><PERSON> g<PERSON><PERSON>m", "@acceptButtonText": {"description": "Text shown on buttons that accept or confirm an action"}, "accountBalanceCardTitle": "Caar", "@accountBalanceCardTitle": {"description": "Title of the account balance page"}, "activateAutoCredit": "Loi dhɛ̈n de rot", "@activateAutoCredit": {"description": "Text for button to enable automatic account crediting"}, "all": "<PERSON><PERSON>", "@all": {"description": "Text shown for all notifications"}, "allChannels": "<PERSON><PERSON><PERSON>e", "@allChannels": {"description": "Text shown to represent all available radio channels or stations"}, "allowAccessButtonText": "<PERSON><PERSON><PERSON> y<PERSON> b<PERSON> k<PERSON>n wïc looi", "@allowAccessButtonText": {"description": "ተጠቃሚውን የመሳሪያ ባህሪዎች መድረሻ እንዲፈቀድ በመጠየቅ ላይ የሚታይ ጠንቅቆ የተጻፈ ጽሑፍ"}, "allowShareInfoWithRadio": "<PERSON><PERSON><PERSON> ɣok wëlkuɔ rɔm kek {radioName}", "@allowShareInfoWithRadio": {"description": "Text shown when requesting permission to share user information with a radio station", "placeholders": {"radioName": {"type": "String", "example": "KEXP Radio"}}}, "appBootingTextBelow": "Kuɔɔ<PERSON> kɔc ke baai bïk rëër ke cï röt rek", "@appBootingTextBelow": {"description": "Text that shows below the app title on the booting screen"}, "appLanguageAppbarTitle": "<PERSON><PERSON><PERSON>", "@appLanguageAppbarTitle": {"description": "This is a title for the app bar"}, "apply": "<PERSON><PERSON>", "@apply": {"description": "Text for apply/confirm buttons"}, "appTitle": "<PERSON><PERSON>T<PERSON>", "@appTitle": {"description": "The name of the app"}, "autoAccountCreditHelp": "abï tääu në kaamdu yic ku bï mat në wëu tɔ̈ në yï nhom të cï yen lööny piny .", "@autoAccountCreditHelp": {"description": "Explanation text for the auto credit feature"}, "avoidCallDisruption": "ku b<PERSON> yïn cïï tɛ̈ɛ̈m wei në thɛɛr ke cööt .", "@avoidCallDisruption": {"description": "Additional explanation for auto credit feature benefit"}, "buyCreditAmountCustomPlaceholder": "min {min} - max {max}", "@buyCreditAmountCustomPlaceholder": {"description": "Placeholder text for the custom amount input field on the buy credit page", "placeholders": {"min": {"type": "String", "example": "$50"}, "max": {"type": "String", "example": "$500"}}}, "buyCreditAmountRecommended": "Aye yɔ̈ɔ̈k", "@buyCreditAmountRecommended": {"description": "Text that shows above the recommended amount on the buy credit page"}, "buyCreditAppBarTitle": "Ɣɔc K<PERSON>it", "@buyCreditAppBarTitle": {"description": "The app page title for buy credit"}, "buyCreditButtonText": "Ɣɔc dhɛ̈n", "@buyCreditButtonText": {"description": "Text for the buy credit button on the account balance page"}, "buyCreditEnterCustomAmountLabel": "Tääu ë wëu cïke tääu thïn", "@buyCreditEnterCustomAmountLabel": {"description": "Label for the custom amount input field on the buy credit page"}, "buyCreditPageTitle": "<PERSON><PERSON>n de wëu kɔɔr", "@buyCreditPageTitle": {"description": "Title of the buy credit page"}, "callButtonText": "Ba cuut", "@callButtonText": {"description": "Text for the call action button on a page"}, "callingAppBarTitle": "<PERSON><PERSON><PERSON>", "@callingAppBarTitle": {"description": "Title of the calling page"}, "callingPageBluetoothOptionsText": "<PERSON><PERSON><PERSON>", "@callingPageBluetoothOptionsText": {"description": "Text that shows to toggle the bluetooth on the calling page"}, "callingPageCreditErrorCardTitle": "Aɣeer në wëu ke cööt", "@callingPageCreditErrorCardTitle": {}, "callingPagePhoneOptionsText": "Tëlëpun", "@callingPagePhoneOptionsText": {"description": "Text that shows to toggle the phone on the calling page"}, "callingPageSpeakerOptionsText": "<PERSON>an ye jam", "@callingPageSpeakerOptionsText": {"description": "Text that shows to toggle the speaker on the calling page"}, "callingPageTimeLeft": "{time} c<PERSON> dö<PERSON>", "@callingPageTimeLeft": {"description": "Text that shows the time left for the call to end", "placeholders": {"time": {"type": "String", "example": "2"}}}, "callLogTypeAnswered": "Kë cï dhu<PERSON>om", "@callLogTypeAnswered": {"description": "Text that shows when the call log type is answered"}, "callLogTypeBusy": "Ye luui", "@callLogTypeBusy": {"description": "Text that shows when the call log type is busy"}, "callLogTypeCancel": "<PERSON><PERSON><PERSON> cɔk k<PERSON>c", "@callLogTypeCancel": {"description": "Text that shows when the call log type is cancelled"}, "callLogTypeIncoming": "Kë bɔ̈", "@callLogTypeIncoming": {"description": "Text that shows when the call log type is incoming"}, "callLogTypeMissed": "<PERSON><PERSON><PERSON>", "@callLogTypeMissed": {"description": "Text that shows when the call log type is missed"}, "callLogTypeOutgoing": "Kä cë bɛ̈n", "@callLogTypeOutgoing": {"description": "Text that shows when the call log type is outgoing"}, "callLogTypeUnavailable": "A<PERSON>ï<PERSON> tɔ̈u", "@callLogTypeUnavailable": {"description": "Text that shows when the call log type is unavailable"}, "callRatesAppBarTitle": "Kä ye cɔl", "@callRatesAppBarTitle": {"description": "Title of the call rates page"}, "callTypeAnswered": "<PERSON>ɔl lɔ biyic", "@callTypeAnswered": {"description": "Text that shows when the call is answered"}, "callTypeBusy": "Ye luui", "@callTypeBusy": {"description": "Text that shows when the call is busy"}, "callTypeCancel": "Cɔl cï puɔ̈l", "@callTypeCancel": {"description": "Text that shows when the call is cancelled"}, "callTypeIncoming": "Cɔl bɔ̈", "@callTypeIncoming": {"description": "Text that shows when the call is incoming"}, "callTypeMissed": "<PERSON><PERSON><PERSON> c<PERSON>", "@callTypeMissed": {"description": "Text that shows when the call is missed"}, "callTypeOutgoing": "<PERSON>ɔl lɔ biyic", "@callTypeOutgoing": {"description": "Text that shows when the call is outgoing"}, "callTypeUnavailable": "<PERSON><PERSON> luui ë yeen ac<PERSON> tɔ̈u", "@callTypeUnavailable": {"description": "Text that shows when the user is unavailable"}, "cancel": "Riɔ̈k", "@cancel": {"description": "Text for cancel buttons"}, "cancelButtonText": "riɔ̈k", "@cancelButtonText": {}, "cashPrizeAmount": "{currencySymbol}{amount} Wëu ë FREE", "@cashPrizeAmount": {"description": "Text showing the cash prize amount for promotions and referrals", "placeholders": {"currencySymbol": {"type": "String", "example": "€"}, "amount": {"type": "String", "example": "250"}}}, "changeChannelButtonText": "Waar të yenë ye luɔɔi thïn", "@changeChannelButtonText": {"description": "Text shown on button to change radio channel"}, "channel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@channel": {"description": "Text shown to represent a radio channel or station"}, "chatWithLiveAgentAppbarTitle": "<PERSON><PERSON> lui n<PERSON>", "@chatWithLiveAgentAppbarTitle": {}, "checkBackLaterText": "<PERSON><PERSON><PERSON> thië<PERSON>u ba dhuk ciëën", "@checkBackLaterText": {"description": "Message shown when a feature or content is temporarily unavailable"}, "chooseLanguage": "<PERSON><PERSON>", "@chooseLanguage": {"description": "Title for the language selection modal"}, "closeButtonText": "<PERSON>", "@closeButtonText": {"description": "Text shown on buttons that close views or dialogs"}, "confirm": "Bɛ̈n gam", "@confirm": {"description": "Text for confirm/verification buttons"}, "confirmationAppBarTitle": "<PERSON><PERSON><PERSON>", "@confirmationAppBarTitle": {"description": "Title of the confirmation page"}, "confirmationFailedButtonText": "Them ëyadɛ̈", "@confirmationFailedButtonText": {"description": "Text for the try again button on the confirmation page"}, "confirmationFailedContactSupportDescription": "Na nɔŋ kë wïc ba thiëëc ka wïc kuɔɔny, ka yï yup akutnhom de kuɔɔnyda .", "@confirmationFailedContactSupportDescription": {"description": "Description that shows when the payment fails"}, "confirmationFailedContactSupportText": "<PERSON><PERSON><PERSON>", "@confirmationFailedContactSupportText": {"description": "Text for the contact support button on the confirmation page"}, "confirmationFailedDescription": "Wëu ë tääu-pïnydu akëc thök. <PERSON><PERSON><PERSON> thiëcku ba bɛɛr them", "@confirmationFailedDescription": {"description": "Description that shows when the payment fails"}, "confirmationFailedTitle": "<PERSON><PERSON><PERSON> c<PERSON> cuat<PERSON>y acï <PERSON>", "@confirmationFailedTitle": {"description": "Title that shows when the payment fails"}, "confirmationSuccessButtonText": "<PERSON>k", "@confirmationSuccessButtonText": {"description": "Text for the success button on the confirmation page"}, "confirmationSuccessDescription": "{amount} ac<PERSON> tiɛ̈m bï mat thïn tën wëu tɔ̈u yïn thïn", "@confirmationSuccessDescription": {"description": "Description that shows when the payment is successful", "placeholders": {"amount": {"type": "String", "example": "$10"}}}, "confirmationSuccessTitle": "<PERSON><PERSON><PERSON> cï tiɛ̈m", "@confirmationSuccessTitle": {"description": "Title that shows when the payment is successful"}, "contactsAllContactsTabText": "Kɔc ye keek yup", "@contactsAllContactsTabText": {"description": "Text that shows above the contacts list on the home page"}, "contactsCallRateText": "{min} minit {secs} thök", "@contactsCallRateText": {"description": "Text that shows the duration of a call", "placeholders": {"min": {"type": "String", "example": "10"}, "secs": {"type": "String", "example": "3"}}}, "contactsFavouriteCallsTabText": "Kë nhaïr", "@contactsFavouriteCallsTabText": {"description": "Text that shows above the Favourite calls list on the home page"}, "contactsNoCallsEmptyMessage": "<PERSON><PERSON><PERSON> a<PERSON>ïn gɛɛr de cö<PERSON>t ëmɛn", "@contactsNoCallsEmptyMessage": {"description": "Message that shows when the user has no recent calls"}, "contactsNoContactsButtonText": "Tïŋ kä ye keek yup", "@contactsNoContactsButtonText": {"description": "Text for the view contacts button on the home page"}, "contactsSearchContactsPlaceholder": "<PERSON><PERSON><PERSON><PERSON> rin ke yök", "@contactsSearchContactsPlaceholder": {"description": "Placeholder text for the search contact name"}, "contactsSearchForContactsPlaceholder": "<PERSON><PERSON><PERSON><PERSON> rin ke yök", "@contactsSearchForContactsPlaceholder": {"description": "Placeholder text for the search input field on the contacts page"}, "couponAppliedButtonText": "Acï luɔ̈ɔ̈i", "@couponAppliedButtonText": {"description": "ኩፖኑ በትክክል ሲተገብር የሚታይ ጽሑፍ"}, "couponApplyButtonText": "<PERSON><PERSON>", "@couponApplyButtonText": {"description": "የኩፖን አተግባለሁ አዝራር ጽሑፍ"}, "credit": "<PERSON><PERSON><PERSON>", "@credit": {"description": "Text shown for credit"}, "declineButtonText": "Bɛ̈n piny", "@declineButtonText": {"description": "Text shown on buttons that decline or reject an action"}, "deleteAccountWarning": "<PERSON><PERSON> wïc ba looi acïï lëu bï dhuɔ̈k ciëën. Tïŋ lɔn cï yïn ɣön-la<PERSON><PERSON> n<PERSON>.", "@deleteAccountWarning": {"description": "Warning message shown when user attempts to delete their account"}, "dialerAppBarTitle": "<PERSON>an ye yup", "@dialerAppBarTitle": {"description": "Title of the dialer page"}, "dialerCopyActionText": "thö<PERSON>", "@dialerCopyActionText": {"description": "Text for the copy action on the dialer page"}, "dialerErrorInvalidNumber": "Namba c<PERSON><PERSON> lui", "@dialerErrorInvalidNumber": {"description": "Error message that shows when the user enters an invalid number"}, "dialerErrorWithoutCountryCode": "Tääu namba de baai thïn (e.g +1)", "@dialerErrorWithoutCountryCode": {"description": "Error message that shows when the user does not add a country code"}, "dialerKeypadABC": "ABC", "@dialerKeypadABC": {"description": "Text that shows on the ABC button on the dialer page"}, "dialerKeypadDEF": "DEF", "@dialerKeypadDEF": {"description": "Text that shows on the DEF button on the dialer page"}, "dialerKeypadGHI": "GHI", "@dialerKeypadGHI": {"description": "Text that shows on the GHI button on the dialer page"}, "dialerKeypadJKL": "JKL", "@dialerKeypadJKL": {"description": "Text that shows on the JKL button on the dialer page"}, "dialerKeypadMNO": "MNO", "@dialerKeypadMNO": {"description": "Text that shows on the MNO button on the dialer page"}, "dialerKeypadPQRS": "PQRS", "@dialerKeypadPQRS": {"description": "Text that shows on the PQRS button on the dialer page"}, "dialerKeypadTUV": "TUV", "@dialerKeypadTUV": {"description": "Text that shows on the TUV button on the dialer page"}, "dialerKeypadWXYZ": "WXYZ", "@dialerKeypadWXYZ": {"description": "Text that shows on the WXYZ button on the dialer page"}, "dialerMuteStatusMuted": "<PERSON><PERSON><PERSON>", "@dialerMuteStatusMuted": {"description": "Text that shows when the dialer is muted"}, "dialerMuteStatusUnmuted": "<PERSON><PERSON><PERSON><PERSON>", "@dialerMuteStatusUnmuted": {"description": "Text that shows when the dialer is unmuted"}, "dialerPasteActionText": "Gääu", "@dialerPasteActionText": {"description": "Text for the paste action on the dialer page"}, "dialerSearchResultsHeader": "<PERSON><PERSON> c<PERSON>k", "@dialerSearchResultsHeader": {"description": "Header text for the search results on the dialer page"}, "dialerSearchResultsNoResults": "Acïn k<PERSON> cï <PERSON>ök", "@dialerSearchResultsNoResults": {"description": "Text that shows when no results are found for the search input field on the dialer page"}, "dialerStatusConnected": "<PERSON><PERSON><PERSON>", "@dialerStatusConnected": {"description": "Text that shows when the dialer status is connected"}, "dialerStatusConnecting": "<PERSON><PERSON><PERSON>", "@dialerStatusConnecting": {"description": "Text that shows when the dialer status is connecting"}, "dialerStatusEnded": "<PERSON><PERSON><PERSON>", "@dialerStatusEnded": {"description": "Text that shows when the dialer status is ended"}, "dialerStatusError": "Kë cï wuɔ̈c acï rot looi", "@dialerStatusError": {"description": "Text that shows when an error occurs in the dialer"}, "dialerStatusFailedToConnect": "Cök a<PERSON>ï <PERSON>ö<PERSON>", "@dialerStatusFailedToConnect": {"description": "Text that shows when the dialer fails to connect"}, "dialerStatusHold": "<PERSON><PERSON>", "@dialerStatusHold": {"description": "Text that shows when the dialer status is on hold"}, "dialerStatusInitial": "<PERSON><PERSON><PERSON>", "@dialerStatusInitial": {"description": "Text that shows when the dialer status is initial"}, "dialerStatusInitiating": "<PERSON><PERSON><PERSON>", "@dialerStatusInitiating": {"description": "Text that shows when the dialer status is initiating"}, "dialerStatusRinging": "<PERSON>i<PERSON>", "@dialerStatusRinging": {"description": "Text that shows when the dialer status is ringing"}, "dialerStatusUnknown": "kë kuc", "@dialerStatusUnknown": {"description": "Text that shows when the dialer status is unknown"}, "emptyContactList": "<PERSON><PERSON><PERSON> acïn lööŋ ë yök.", "@emptyContactList": {"description": "Message that shows when the user has no contact list"}, "emptyFavouriteContactList": "<PERSON><PERSON><PERSON> acïn lööŋ ë yök ë kɔc nhiar.", "@emptyFavouriteContactList": {"description": "Message that shows when the user has no favourite contact list"}, "enterCouponOptionalPlaceholder": "Tääu kë cï <PERSON>ök (Optional)", "@enterCouponOptionalPlaceholder": {"description": "የኩፖን ኮድ መጠቀም ስፍራ ጽሑፍ"}, "enterOtpPageAppBarSubtitle": "Kueer 2 de 3", "@enterOtpPageAppBarSubtitle": {}, "enterOtpPageAppBarTitle": "Ba Jɔ̈k de Käŋ", "@enterOtpPageAppBarTitle": {}, "enterOtpPageEditButton": "<PERSON><PERSON> de W<PERSON>p ë rɛ̈ɛ̈c? Yïn thiëcku ba cɔ̈kpiny", "@enterOtpPageEditButton": {"description": "Text that shows on the edit phone number button"}, "enterOtpPageErrorMessage": "Code cie yic", "@enterOtpPageErrorMessage": {"description": "Error message that shows when the user enters an incorrect OTP"}, "enterOtpPagePhoneLabel": "Tääu ë namba duɔ̈ɔ̈n de yök", "@enterOtpPagePhoneLabel": {}, "enterOtpPagePhoneLabelDescription": "A cë tuɔɔc në namba de WhatsApp yic {phoneNumber}", "@enterOtpPagePhoneLabelDescription": {"description": "Description text for the OTP input field on the OTP entry page", "placeholders": {"phoneNumber": {"type": "String", "example": "+1234567890"}}}, "enterOtpPageResendOtpButton": "<PERSON><PERSON><PERSON><PERSON>", "@enterOtpPageResendOtpButton": {"description": "Text that shows on the confirm verificatio button"}, "enterOtpPageResendOtpRetries": "{count, plural, zero{<PERSON><PERSON><PERSON><PERSON> kë cï them cï döŋ} one{1 thëm de akër cï döŋ} two{} few{} many{} other{{count} kä juëc them}}", "@enterOtpPageResendOtpRetries": {"description": "Message showing the number of retries a user has left", "placeholders": {"count": {"type": "int", "example": "3"}}}, "enterOtpPageResendOtpRetriesError": "<PERSON><PERSON><PERSON> a<PERSON> lëu ba lööŋ ke gɛ̈tgɛ̈t yök në namba de WhatsApp kënë yic në thɛɛr ke 24 yiic .", "@enterOtpPageResendOtpRetriesError": {"description": "Error message that shows when the user has exhausted the number of retries"}, "enterOtpPageSubmitButton": "Gam OTP", "@enterOtpPageSubmitButton": {"description": "Text that shows on the confirm verificatio button"}, "enterVoucherCodeLabel": "Tääu ë namba de wëu", "@enterVoucherCodeLabel": {"description": "Label for voucher code input field"}, "errorLoadingRadioStationsText": "Awuɔ̈c në tɛ̈ɛ̈u de ɣän ke radiö", "@errorLoadingRadioStationsText": {"description": "Message shown when radio stations fail to load"}, "failedToLoadLeaderboard": "<PERSON><PERSON><PERSON> löny b<PERSON> thöny de bäny tääu thïn", "@failedToLoadLeaderboard": {"description": "Error message displayed when the referral leaderboard cannot be loaded"}, "free": "KË CIEN KË GËLE", "@free": {"description": "Text shown for free promotional content"}, "freeCreditAppbarTitle": "<PERSON><PERSON><PERSON> cïn wëu ye tääu thïn", "@freeCreditAppbarTitle": {}, "freeCreditPageContent_1": "Tääu ë code de gɛ̈tpiny tënë mäthku & kɔc ke paandu.", "@freeCreditPageContent_1": {}, "freeCreditPageContent_2": "Këk aye FroggyTalk yök ku gɛ̈t rin ë thäät ë thäät ë thäät.", "@freeCreditPageContent_2": {}, "freeCreditPageContent_3": "<PERSON><PERSON><PERSON> ab<PERSON> {amount} të cï raan ɣön ë määl ɣɔɔc ë thää tueŋ.", "@freeCreditPageContent_3": {}, "freeCreditPageHeadingText": "<PERSON><PERSON><PERSON><PERSON> m<PERSON> & yök {amount}", "@freeCreditPageHeadingText": {}, "freeCreditPageShareReferralLinkButtonText": "Tɛ̈k kë ye tuɔɔc", "@freeCreditPageShareReferralLinkButtonText": {}, "freeCreditPageSubHeadingText": "Kä ca ke tuɔɔc ({amount})", "@freeCreditPageSubHeadingText": {}, "froggytalkCustomerLabel": "<PERSON><PERSON> <PERSON> de <PERSON>ö<PERSON>", "@froggytalkCustomerLabel": {"description": "Default label shown when user name is not available"}, "froggyTalkLovesYou": "FroggyTalk acï y<PERSON> y<PERSON>.", "@froggyTalkLovesYou": {"description": "Affirmative message showing app's appreciation for users"}, "getNow": "Y<PERSON>k <PERSON>", "@getNow": {"description": "Text for a button or link to get an offer or promotion"}, "helpCenterAppbarTitle": "<PERSON><PERSON><PERSON>", "@helpCenterAppbarTitle": {}, "helpCenterPageMenu_1": "<PERSON><PERSON> wɛ̈lɛ̈/ka <PERSON><PERSON>", "@helpCenterPageMenu_1": {}, "helpCenterPageMenu_2": "<PERSON> wenë raan lui në p<PERSON>", "@helpCenterPageMenu_2": {}, "helpCenterPageMenu_3": "<PERSON>ë ye thiëc", "@helpCenterPageMenu_3": {}, "helpCenterPageMenu_4": "<PERSON><PERSON><PERSON>ä<PERSON>", "@helpCenterPageMenu_4": {"description": "Text that shows on the WhatsApp button on the help center page"}, "homeNavigationBarText": "<PERSON><PERSON>", "@homeNavigationBarText": {"description": "navbar text for home page"}, "inAppPurchaseLabel": "Ɣɔc <PERSON>h<PERSON>n App", "@inAppPurchaseLabel": {"description": "Label indicating this is an in-app purchase from the app store"}, "internetConnectionAlertTextError": "Oop! Aye tïŋ ke yïn këc lɔ në aliiric", "@internetConnectionAlertTextError": {}, "internetConnectionAlertTextSuccess": "Käŋ ëbɛ̈n acï guiir! Yïn acï yök <PERSON>.", "@internetConnectionAlertTextSuccess": {}, "invalidInternationalPhoneFormat": "Namba de Telepun de Pinynhom acïï lui", "@invalidInternationalPhoneFormat": {"description": "Error message shown when phone number format is invalid"}, "invalidPhoneNumber": "<PERSON><PERSON><PERSON> thiëcku ba namba de telepun tɔ̈u thïn", "@invalidPhoneNumber": {"description": "Error message shown when phone number is invalid"}, "keypadNavigationBarText": "k<PERSON><PERSON> thok", "@keypadNavigationBarText": {"description": "navbar text for keypad page"}, "landlineText": "<PERSON><PERSON><PERSON> de piny", "@landlineText": {"description": "Text that shows when the number is a landline number"}, "languageChangeError": "Thoŋ acï wuɔ̈c acï rot looi. <PERSON><PERSON><PERSON> thi<PERSON>u ba bɛɛr them.", "@languageChangeError": {"description": "Error message shown when language change fails"}, "languageSelectionDisclaimer": "You can change your language under", "@languageSelectionDisclaimer": {"description": "Message informing users where they can find language settings"}, "languageSetupPageAppBarSubtitle": "Kueer 3 de 3", "@languageSetupPageAppBarSubtitle": {"description": "Subtitle of the profile setup page showing progress"}, "loadingRadioStationsText": "Tä<PERSON>u ë ɣän ke radiö...", "@loadingRadioStationsText": {"description": "Message shown while radio stations are being loaded"}, "loadVoucherCardButtonText": "Cärjë", "@loadVoucherCardButtonText": {}, "loadVoucherCardErrorText": "<PERSON><PERSON> rɛɛc, them ba bɛɛr them", "@loadVoucherCardErrorText": {}, "loadVoucherCardLabelText": "Tääu ë namba de wëu ke nambaai kaa 10", "@loadVoucherCardLabelText": {}, "loadVoucherCardTitle": "Namba de <PERSON>", "@loadVoucherCardTitle": {}, "loadVoucherCodeTitle": "Namba de <PERSON>", "@loadVoucherCodeTitle": {"description": "Title shown on the load voucher code dialog"}, "loginPageAppBarSubtitle": "Kueer 1 de 3", "@loginPageAppBarSubtitle": {"description": "Subtitle of the login page"}, "loginPageAppBarTitle": "Ba Jɔ̈k de Käŋ", "@loginPageAppBarTitle": {"description": "Title of the login page"}, "loginPageCheckboxLabel": "Ɣɛn anɔŋ namba de tuɔɔc", "@loginPageCheckboxLabel": {}, "loginPageErrorMessage": "Namba acie tääu në WhatsApp yic .", "@loginPageErrorMessage": {}, "loginPagePhoneLabel": "Tääu namba duɔ̈ɔ̈n de WhatsApp", "@loginPagePhoneLabel": {}, "loginPagePhoneLabelDescription": "Aköl ë thää tök ë thää ë thää abï tuɔɔc tënë yïn në WhatsApp .", "@loginPagePhoneLabelDescription": {}, "loginPagePhoneNumberError": "<PERSON><PERSON> de telepun du a<PERSON><PERSON> thök, y<PERSON><PERSON> thi<PERSON><PERSON><PERSON> ba thöl", "@loginPagePhoneNumberError": {}, "loginPageReferralLabel": "Tääu ë namba de tuɔɔc", "@loginPageReferralLabel": {}, "loginPageSubmitButton": "Thiëc ë yök", "@loginPageSubmitButton": {}, "minsOrMin": "{count, plural, zero{} one{1 minit} two{} few{} many{} other{{count} minit}}", "@minsOrMin": {"description": "Text that shows either '1 min' or '{count} mins' based on the count", "placeholders": {"count": {"type": "int", "example": "5"}}}, "mobileText": "Tɛ̈ɛ̈n", "@mobileText": {"description": "Text that shows when the number is a mobile number"}, "monthlyReferralBoard": "{month} <PERSON><PERSON><PERSON>", "@monthlyReferralBoard": {"description": "Title for the monthly promotion leaderboard showing top referrers", "placeholders": {"month": {"type": "String", "example": "Jan'25"}}}, "moreAppbarTitle": "Wëu ë wëu", "@moreAppbarTitle": {}, "moreNavigationBarText": "Käjuëc", "@moreNavigationBarText": {"description": "navbar text for more page"}, "morePageAccountBalanceCardTitle": "Caar", "@morePageAccountBalanceCardTitle": {}, "morePageAppVersionMenuText": "App ë tɔ̈ thïn", "@morePageAppVersionMenuText": {}, "morePageCallRatesMenuText": "Kä ye cɔl", "@morePageCallRatesMenuText": {}, "morePageHelpCenterMenuText": "<PERSON><PERSON><PERSON>", "@morePageHelpCenterMenuText": {}, "morePageLanguageMenuText": "<PERSON><PERSON><PERSON>", "@morePageLanguageMenuText": {}, "morePageLoadVoucherMenuText": "Tääu ë wëu", "@morePageLoadVoucherMenuText": {}, "morePageLogoutMenuText": "<PERSON><PERSON> bi<PERSON>c", "@morePageLogoutMenuText": {}, "morePageProfileMenuText": "Bɛ̈k", "@morePageProfileMenuText": {}, "morePageRadioMenuText": "Radiö", "@morePageRadioMenuText": {}, "morePageReferralCodeCardButtonText": "<PERSON><PERSON>", "@morePageReferralCodeCardButtonText": {}, "morePageReferralCodeCardContent": "{numberOfPeople} kɔc aacï namba de tuɔɔcdu luɔ̈ɔ̈i", "@morePageReferralCodeCardContent": {}, "morePageReferralCodeCardDescription": "Yök {amount} të tuɔɔc yïn m<PERSON> & kɔc ke paandu", "@morePageReferralCodeCardDescription": {}, "morePageReferralCodeCardTitle": "Namba de tuɔɔc", "@morePageReferralCodeCardTitle": {}, "nextTrack": "Dɛ̈t bɔ̈", "@nextTrack": {"description": "Text shown on button to play the next radio track or station"}, "noBuyCreditOptionsAvailable": "<PERSON><PERSON><PERSON><PERSON> Ɣɔc de Käŋ Tɔ̈u", "@noBuyCreditOptionsAvailable": {"description": "Message that shows when there are no buy credit options available"}, "noFavoriteContactsMessage": "<PERSON><PERSON><PERSON><PERSON> kɔc n<PERSON>r keek", "@noFavoriteContactsMessage": {"description": "Message shown when there are no favorite contacts to display"}, "noLanguagesAvailable": "<PERSON><PERSON><PERSON><PERSON> thoŋ tɔ̈u", "@noLanguagesAvailable": {"description": "Message shown when no languages are available to select"}, "noNotificationsYet": "<PERSON><PERSON>ïn kë cï lëk ëmɛn", "@noNotificationsYet": {"description": "Text shown when there are no notifications"}, "noRadioStationsAvailableText": "<PERSON><PERSON><PERSON><PERSON>n ke radiö tɔ̈u", "@noRadioStationsAvailableText": {"description": "Message shown when there are no radio stations to display"}, "notEnoughCreditMessage": "<PERSON><PERSON><PERSON> acïn wëu juëc ë dhɛ̈n", "@notEnoughCreditMessage": {"description": "Message shown when user has insufficient credit for a call"}, "notification": "Lɛ̈k", "@notification": {"description": "Text shown for a single notification"}, "notificationsAppBarTitle": "Kä ye kɔc lɛ̈k", "@notificationsAppBarTitle": {"description": "Title of the notifications page"}, "notificationsEmptyMessage": "<PERSON><PERSON><PERSON> a<PERSON>ïn gɛɛr de kë bï yïn lɛ̈k", "@notificationsEmptyMessage": {"description": "Message that shows when the user has no notifications"}, "notificationSettingsAppBarTitle": "<PERSON><PERSON><PERSON><PERSON>r de Lɛ̈k", "@notificationSettingsAppBarTitle": {"description": "Title of the notification settings page"}, "notificationSettingsAutoDeleteNotificationText": "Kë ye nyuɔɔth cuɔt wei yetök", "@notificationSettingsAutoDeleteNotificationText": {"description": "Text that shows to toggle the auto delete notification on the notification settings page"}, "notificationSettingsNotificationSoundText": "<PERSON><PERSON><PERSON>", "@notificationSettingsNotificationSoundText": {"description": "Text that shows to toggle the notification sound on the notification settings page"}, "notificationsTabAllText": "<PERSON><PERSON>", "@notificationsTabAllText": {"description": "Text that shows on the all tab on the notifications page"}, "notificationsTabUnreadText": "<PERSON><PERSON> këc kueen", "@notificationsTabUnreadText": {"description": "Text that shows on the unread tab on the notifications page"}, "notificationsTimeAgo": "{time} {time, plural, one{{duration}} other{{duration}s}} cë lɔ", "@notificationsTimeAgo": {"description": "Text that shows the time since the notification was received", "placeholders": {"time": {"type": "int", "example": "2"}, "duration": {"type": "String", "example": "hours"}}}, "nowPlaying": "Ëmɛnë ke pol", "@nowPlaying": {"description": "Text shown to indicate the currently playing radio track or station"}, "okButtonText": "<PERSON><PERSON>", "@okButtonText": {"description": "Text shown on OK confirmation button"}, "onboardingPageFooterAndText": "ku", "@onboardingPageFooterAndText": {"description": "Text that shows between the terms and conditions link and the privacy policy link on the onboarding page"}, "onboardingPageFooterPrivacy": "<PERSON><PERSON><PERSON><PERSON> ke Käke <PERSON>an", "@onboardingPageFooterPrivacy": {"description": "Text that shows below the submit button on the onboarding page"}, "onboardingPageFooterTermsConditionsText": "<PERSON><PERSON><PERSON>ŋ ku Käke <PERSON>i", "@onboardingPageFooterTermsConditionsText": {"description": "Text that shows below the submit button on the onboarding page"}, "onboardingPageFooterText": "<PERSON>ë gut yïn Gɔ̈t, yïn ac<PERSON> gam .", "@onboardingPageFooterText": {"description": "Text that shows below the submit button on the onboarding page"}, "onboardingPageSliderHeader1": "Thook juëc", "@onboardingPageSliderHeader1": {"description": "Header text for the first slide on the onboarding page"}, "onboardingPageSliderHeader2": "<PERSON>ɔl nambaai ke telepun ka telepun ke piny në pinynhom ëbɛ̈n", "@onboardingPageSliderHeader2": {"description": "Header text for the second slide on the onboarding page"}, "onboardingPageSliderHeader3": "<PERSON><PERSON><PERSON> piɔ<PERSON>", "@onboardingPageSliderHeader3": {"description": "Header text for the third slide on the onboarding page"}, "onboardingPageSliderText1": "FroggyTalk ee yïn puɔ̈l ba thoŋ duɔ̈ɔ̈n kɔɔr ba kuany cïmën Tho<PERSON> de <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> de Hausa ku kɔ̈k.", "@onboardingPageSliderText1": {"description": "Text for the first slide on the onboarding page"}, "onboardingPageSliderText2": "Loi telepun tënë gɛɛr de telepun ë thäät yiic ku gɛɛr de telepun ë pinynhom. Raan ë yök acïï wïc bï naŋ telepun ë thäät ku thäät ë thäät.", "@onboardingPageSliderText2": {"description": "Text for the second slide on the onboarding page"}, "onboardingPageSliderText3": "Ɣɔc wëu ke cööt në wëu ke baai ke yïn kɔɔr ba wëu cuatpiny.", "@onboardingPageSliderText3": {"description": "Text for the third slide on the onboarding page"}, "onboardingPageSubmitButtonText": "<PERSON><PERSON><PERSON>", "@onboardingPageSubmitButtonText": {"description": "Text for the submit button on the onboarding page"}, "outOfCreditLabel": "Aɣeer në dhɛ̈n", "@outOfCreditLabel": {"description": "Label shown when user has no remaining credit"}, "paymentFailureAppBarTitle": "Cë bɛ̈n", "@paymentFailureAppBarTitle": {"description": "Title shown in app bar of payment failure page"}, "paymentFailureHomeButton": "<PERSON><PERSON>", "@paymentFailureHomeButton": {"description": "Text for button to return to home page"}, "paymentFailureMessage": "Wëu duɔ̈ɔ̈n cïke cuatpiny de {amount} akëc tiɛ̈m", "@paymentFailureMessage": {"description": "Message shown when payment fails, includes amount", "placeholders": {"amount": {"type": "String", "example": "$50.00"}}}, "paymentFailureTitle": "<PERSON><PERSON><PERSON> c<PERSON>ke cuatpiny akëc tiɛ̈m", "@paymentFailureTitle": {"description": "Main title shown when payment fails"}, "paymentFailureTryAgainButton": "Them ëyadɛ̈", "@paymentFailureTryAgainButton": {"description": "Text for button to retry payment"}, "paymentOptionsAppBarTitle": "Kä ye keek cuatpiny", "@paymentOptionsAppBarTitle": {"description": "Title of the payment options page"}, "paymentOptionsAutoCreditDescription": "Kënë abï ɣön-wëu du tääu-pïny ë rot ë {amount} të cï wëu ë ɣön-wëu tɔ̈u piiny {minAmount}.", "@paymentOptionsAutoCreditDescription": {"description": "Description of the auto credit payment method"}, "paymentOptionsAutoCreditTitle": "<PERSON><PERSON><PERSON><PERSON>", "@paymentOptionsAutoCreditTitle": {"description": "Title of the auto credit payment method"}, "paymentOptionsSelectPaymentMethod": "<PERSON><PERSON> kueer kɔɔr ba cuatpiny", "@paymentOptionsSelectPaymentMethod": {"description": "Text that shows above the payment method selection dropdown"}, "paymentOptionsSummaryAmountToCreditText": "<PERSON><PERSON><PERSON> b<PERSON> keek tääu thïn", "@paymentOptionsSummaryAmountToCreditText": {"description": "Text that shows the amount to be credited on the payment options page"}, "paymentOptionsSummaryDiscountButtonText": "<PERSON><PERSON>", "@paymentOptionsSummaryDiscountButtonText": {"description": "Text for the apply discount button on the payment options page"}, "paymentOptionsSummaryDiscountPlaceholder": "Tääu ë namba de kë bï dhuɔ̈kciëën(optional)", "@paymentOptionsSummaryDiscountPlaceholder": {"description": "Placeholder text for the discount code input field on the payment options page"}, "paymentOptionsSummaryDiscountText": "W<PERSON>u cë bɛ̈n", "@paymentOptionsSummaryDiscountText": {"description": "Text that shows the discount on the payment options page"}, "paymentOptionsSummaryTotalPaymentText": "Amat de wëu c<PERSON>ke cuat<PERSON>y", "@paymentOptionsSummaryTotalPaymentText": {"description": "Text that shows the total payment on the payment options page"}, "paymentOptionsSummaryVatFeesText": "VAT + wëu", "@paymentOptionsSummaryVatFeesText": {"description": "Text that shows the VAT and fees on the payment options page"}, "paymentSummaryAmountToCreditLabel": "<PERSON><PERSON><PERSON> bï keek tääu në wëu yiic", "@paymentSummaryAmountToCreditLabel": {"description": "በክፍያ ማጠቃለያ ውስጥ የሚቀርብ መጠን ምልክት"}, "paymentSummaryDiscountLabel": "W<PERSON>u cë bɛ̈n", "@paymentSummaryDiscountLabel": {"description": "በክፍያ ማጠቃለያ ውስጥ ቅናሽ ምልክት"}, "paymentSummaryPlayStoreFeeLabel": "  + {percentage}% Ajuɛɛr & VAT", "@paymentSummaryPlayStoreFeeLabel": {"description": "Label showing the Play Store fee percentage and VAT in payment summary", "placeholders": {"percentage": {"type": "String", "example": "30"}}}, "paymentSummaryTotalLabel": "Amat de wëu c<PERSON>ke cuat<PERSON>y", "@paymentSummaryTotalLabel": {"description": "በክፍያ ማጠቃለያ ውስጥ ጠቅላላ ክፍያ ምልክት"}, "paymentSummaryVatFeesLabel": "VAT + Wëu", "@paymentSummaryVatFeesLabel": {"description": "በክፍያ ማጠቃለያ ውስጥ ተጨማሪ ክፍያዎች ምልክት"}, "payWithApplePay": "Apple Pay", "@payWithApplePay": {"description": "Text shown on button to pay using Apple Pay"}, "payWithGooglePay": "Google Pay", "@payWithGooglePay": {"description": "Text shown on button to pay using Google Pay"}, "perMinRate": "{rate}/minit", "@perMinRate": {"description": "Rate per minute for a call", "placeholders": {"rate": {"type": "String", "example": "$10"}}}, "perMinRateSingle": "/minit", "@perMinRateSingle": {"description": "Rate per minute for a call in short form"}, "perMinuteRate": "{rate}/minit", "@perMinuteRate": {"description": "Rate per minute for a call", "placeholders": {"rate": {"type": "String", "example": "$10"}}}, "perMinuteSlashLabel": "{min}/minit", "@perMinuteSlashLabel": {"description": "Label showing '/minutes' for call rate displays", "placeholders": {"min": {"type": "String", "example": "5"}}}, "permissionButtonAccept": "<PERSON><PERSON><PERSON>", "@permissionButtonAccept": {"description": "Text for the accept button on the permission request dialog"}, "permissionButtonSkip": "Lɔ̈ɔ̈m", "@permissionButtonSkip": {"description": "Text for the skip button on the permission request dialog"}, "permissionDeniedMessage": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> ba puɔ̈l në ajuɛɛr de app yic", "@permissionDeniedMessage": {"description": "Message shown when a permission is denied by the user"}, "permissionForCameraTitle": "Ɣok awïc puɔ̈l buk thuraai ku bidiö dɔm", "@permissionForCameraTitle": {"description": "Title for the camera permission request dialog"}, "permissionForContactListTitle": "Ye FroggyTalk puɔ̈l b<PERSON> yïn yök në lööŋku yiic / Athör ë telepun", "@permissionForContactListTitle": {"description": "Title for the contact list permission request dialog"}, "permissionForMicrophoneTitle": "Ye FroggyTalk gäm dhöl b<PERSON> yïn jam në jamdu yic", "@permissionForMicrophoneTitle": {"description": "Title for the microphone permission request dialog"}, "permissionForNotificationTitle": "Ye FroggyTalk puɔ̈l bï yïn tuɔɔc kä cï cɔ̈kpiny", "@permissionForNotificationTitle": {"description": "Title for the notification permission request dialog"}, "permissionForStorageTitle": "Ɣok awïc puɔ̈l buk kä cïke yök tɔ̈ɔ̈u në aditku yiic", "@permissionForStorageTitle": {"description": "Title for the storage permission request dialog"}, "permissionPermanentlyDeniedMessage": "<PERSON><PERSON><PERSON> ac<PERSON> j<PERSON>i athɛɛr, <PERSON><PERSON><PERSON> thi<PERSON> ba puɔ̈l në ajuɛɛr de app yic", "@permissionPermanentlyDeniedMessage": {"description": "Message shown when a permission is permanently denied by the user"}, "perM_Rate": "{rate}/m", "@perM_Rate": {"description": "Rate per minute for a call", "placeholders": {"rate": {"type": "String", "example": "$10"}}}, "phoneNumberStartRule": "<PERSON><PERSON><PERSON> thi<PERSON>u ba gɛ̈tdu jɔɔk në + wala 00 .", "@phoneNumberStartRule": {"description": "Error message shown when phone number doesn't start with + or 00"}, "playRadio": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@playRadio": {"description": "Text shown on button to start playing the radio"}, "previousTrack": "<PERSON><PERSON> thɛɛr", "@previousTrack": {"description": "Text shown on button to play the previous radio track or station"}, "proceedToPaymentButtonText": "Lɔ tueŋ", "@proceedToPaymentButtonText": {"description": "Text for the proceed to payment button on the buy credit page"}, "profileAppbarTitle": "Bɛ̈k", "@profileAppbarTitle": {}, "profileDeleteAccountButtonText": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>n", "@profileDeleteAccountButtonText": {}, "profileDeleteAccountCardContent": "Tïŋ lɔn wïc yïn ba ɣön-la<PERSON>du nyaai. Yïn ab<PERSON> määr ë wëu ë ɣön-wëu du yic.", "@profileDeleteAccountCardContent": {}, "profileDeleteAccountCardTitle": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>n", "@profileDeleteAccountCardTitle": {}, "profileLabel": "Bɛ̈k", "@profileLabel": {"description": "Label used for profile sections"}, "profileLabelChangeLocation": "Waar të tɔ̈ yïn th<PERSON>n", "@profileLabelChangeLocation": {}, "profileLabelEmailAddress": "Tääu ë rinke imeel", "@profileLabelEmailAddress": {}, "profileLabelFullName": "Tääu rin ëbɛ̈n", "@profileLabelFullName": {"description": "profile<PERSON>abelFull<PERSON>ame"}, "profileUpdatedSuccessfullyMessage": "K<PERSON> tɔ̈u yïn thïn acï cɔ̈kpiny apieth", "@profileUpdatedSuccessfullyMessage": {"description": "Message shown when profile is updated successfully"}, "profileUpdateFailedMessage": "Cɔ̈kpiny de kë tɔ̈u në yï guɔ̈p acï löny", "@profileUpdateFailedMessage": {"description": "Message shown when profile update fails"}, "quickAdvertSubtitle": "Ɣɔc thaar de aliir tënë kɔc ke paandu në pinynhom ëbɛ̈n", "@quickAdvertSubtitle": {"description": "Subtitle for the quick advert on the home page"}, "quickAdvertTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> wëu tënë bɛ̈i kaa 140+", "@quickAdvertTitle": {"description": "Title for the quick advert on the home page"}, "radioAppBarTitle": "Radiö", "@radioAppBarTitle": {}, "radioComingSoonText": "Radio Abë bën !!!", "@radioComingSoonText": {"description": "Text shown when radio feature is not yet available"}, "radioPageChooseChannelButtonText": "Kuany kë ye cɔl", "@radioPageChooseChannelButtonText": {}, "radioPageNoChannelsText": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "@radioPageNoChannelsText": {}, "radioPageSearchPlaceholder": "Kuany kë ye cɔl", "@radioPageSearchPlaceholder": {}, "ratesNavigationBarText": "<PERSON><PERSON>", "@ratesNavigationBarText": {"description": "navbar text for rates page"}, "recentCallsText": "Cɔl thiɔ̈kë", "@recentCallsText": {"description": "Text that shows above the recent calls list on the home page"}, "rechargeButtonText": "Cärjë", "@rechargeButtonText": {"description": "Text shown on recharge button"}, "referAndEarn": "Tïŋ & Yök", "@referAndEarn": {"description": "Text shown for refer and earn promotional text"}, "referralCardButtonText": "<PERSON><PERSON><PERSON> cïn wëu ye tääu thïn", "@referralCardButtonText": {"description": "Text for the referral card button"}, "referralCardDescription1": "Tääu ë raan dɛ̈t ku yök {percentageAmount} ë wëu ë thäät ë thäät.", "@referralCardDescription1": {"description": "Message showing the referral benefit", "placeholders": {"percentageAmount": {"type": "String", "example": "10%"}}}, "referralCardDescription2": "{count, plural, zero{<PERSON><PERSON><PERSON><PERSON> raan cï lööŋku tääu-pïny.} one{<PERSON><PERSON> tök acï namba de gɛ̈tpinydu luɔ̈ɔ̈i.} two{} few{} many{} other{{numberOfPeople} kɔc aacï namba de gɛ̈tgɛ̈tdu luɔ̈ɔ̈i.}}", "@referralCardDescription2": {"description": "Message showing how many people have used the referral code, with pluralization.", "placeholders": {"numberOfPeople": {"type": "int", "example": "2"}}}, "referralCardTitle": "Namba de Tuɔɔc", "@referralCardTitle": {"description": "Title for the referral card"}, "referralCodeOnCopyActionResponse": "FroggyTalk acï tääu në kë yenë käŋ tɔ̈ɔ̈u thïn", "@referralCodeOnCopyActionResponse": {}, "refreshText": "Cɔ̈kpiny", "@refreshText": {"description": "Text shown on button to refresh or reload content"}, "restorePurchases": "Dhuɔ̈kë kä cïke ɣɔɔc", "@restorePurchases": {"description": "Button text for restoring previous in-app purchases"}, "retry": "Them", "@retry": {"description": "Text for button to retry a failed operation"}, "saveAndProceedToPaymentButtonText": "Tɔ̈ɔ̈u & Lɔ tueŋ ke yïn tääu wëu", "@saveAndProceedToPaymentButtonText": {"description": "Text shown on button to save profile changes and proceed to payment"}, "saveChangesButtonText": "Tɔ̈ɔ̈u kä cïke waar", "@saveChangesButtonText": {}, "savedSuccessfullyMessage": "Acï tɔ̈ɔ̈u apieth", "@savedSuccessfullyMessage": {"description": "Message shown when changes are saved successfully"}, "searchCountryPlaceholder": "<PERSON><PERSON><PERSON><PERSON> baai", "@searchCountryPlaceholder": {"description": "Placeholder text for the search input field on the country selection page"}, "searchFavouriteContactMessage": "<PERSON><PERSON><PERSON><PERSON>", "@searchFavouriteContactMessage": {"description": "Placeholder text for searching favorite contacts"}, "searchForCountryNoResults": "Acïn k<PERSON> cï <PERSON>ök", "@searchForCountryNoResults": {"description": "Text that shows when no results are found for the search input field on the country selection page"}, "searchForCountryPlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "@searchForCountryPlaceholder": {"description": "Placeholder text for the search input field on the country selection page"}, "searchRecentCallsMessage": "<PERSON><PERSON><PERSON><PERSON> Thiɔ̈kë", "@searchRecentCallsMessage": {"description": "Placeholder text for searching recent calls"}, "secsOrSec": "{count, plural, zero{} one{1 sec} two{} few{} many{} other{{count} thää}}", "@secsOrSec": {"description": "Text that shows either '1 sec' or '{count} secs' based on the count", "placeholders": {"count": {"type": "int", "example": "10"}}}, "securedByStripe": "Acï gël në Strip", "@securedByStripe": {"description": "Text showing that payment is secured by Stripe payment processor"}, "selectCountryPlaceholder": "<PERSON><PERSON> baai", "@selectCountryPlaceholder": {"description": "Placeholder text for the country selection dropdown"}, "selectPreferredLanguage": "<PERSON><PERSON> thoŋ de yïn bɛ̈ɛ̈r", "@selectPreferredLanguage": {"description": "Subtitle for the language selection modal"}, "sendCreditButtonText": "<PERSON><PERSON><PERSON><PERSON> w<PERSON>u", "@sendCreditButtonText": {"description": "Text for the send credit button on the home page"}, "settings": "Kä ye keek guiir", "@settings": {"description": "Text shown for settings"}, "shareReferralText": "Tɛ̈k FroggyTalk App {appLink} kë kɔc ku bɛ̈k yïn bɛ̈ɛ̈r bï yïn namba de tuɔɔc {referralCode}. Yök wëu kë cï raan bï yök ku wëu thïn tënë wëu tɔ̈u yïn thïn.", "@shareReferralText": {"description": "Text shown when sharing referral code", "placeholders": {"appLink": {"type": "String", "example": "https://link-to.app/AHA423kdwG"}, "referralCode": {"type": "String", "example": "ABC123"}}}, "somethingWentWrongMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> acï rɛ̈ɛ̈c. <PERSON><PERSON><PERSON> thiëcku ba bɛɛr them.", "@somethingWentWrongMessage": {"description": "Message that shows when an error occurs"}, "stopRadio": "Kɔ̈ɔ̈c Radiö", "@stopRadio": {"description": "Text shown on button to stop the currently playing radio"}, "storePrice": "<PERSON><PERSON>u ke cuän", "@storePrice": {"description": "Label for the price listed in the app store"}, "termsAndConditionsApply": "L<PERSON><PERSON>ŋ ku kä cïke lueel aye luui", "@termsAndConditionsApply": {"description": "Text indicating that terms and conditions apply to an offer or promotion"}, "timeLeftModalMessage": "{days}nïn lik cï döŋ bï yïn ya raan cï yök në wëu ke Cash. Jɛk {count} kɔc ku ye No 1", "@timeLeftModalMessage": {"description": "Message showing days left and number of referrals needed", "placeholders": {"days": {"type": "String", "example": "25"}, "count": {"type": "int", "example": "30"}}}, "timeLeftModalTitle": "{days}n<PERSON>n ka döŋ", "@timeLeftModalTitle": {"description": "Title showing the number of days left in the modal", "placeholders": {"days": {"type": "String", "example": "2"}}}, "tryAgainText": "Them ëyadɛ̈", "@tryAgainText": {"description": "Text shown on button to retry an action that failed"}, "unknownErrorText": "Kë kuc cï wuɔ̈c acï rot looi", "@unknownErrorText": {"description": "Generic message shown when an unspecified error occurs"}, "unread": "<PERSON><PERSON> këc kueen", "@unread": {"description": "Text shown for unread notifications"}, "updateLanguagePrompt": "<PERSON><PERSON><PERSON> thiëcku ba loi kë ye thök në thoŋ de yïn", "@updateLanguagePrompt": {"description": "Instruction text prompting user to update their language setting"}, "updateProfileButtonText": "Cɔ̈kpiny këdu", "@updateProfileButtonText": {"description": "Text shown on button to update user profile"}, "updateYourLanguage": "<PERSON><PERSON><PERSON><PERSON>.", "@updateYourLanguage": {"description": "Button or action text for language update functionality"}, "upgradeDialogButtonNo": "<PERSON><PERSON><PERSON>", "@upgradeDialogButtonNo": {"description": "እትም ማስተካከልን ለመሰረዝ የሚጠቀምበት አዝራር ጽሑፍ"}, "upgradeDialogButtonYes": "Lɛ̈k", "@upgradeDialogButtonYes": {"description": "እትም ማስተካከልን ለመቀጠል የሚጠቀምበት አዝራር ጽሑፍ"}, "upgradeDialogMessage": "Aciëŋ yam de FroggyTalk acï yök. Ye wïc ba jäl ëmɛn?", "@upgradeDialogMessage": {"description": "አዳዲስ እትም ሲኖር ተጠቃሚውን ለመዘመን የሚጠይቅ መልእክት"}, "upgradeDialogTitle": "Cɔ̈kpiny Atɔ̈u", "@upgradeDialogTitle": {"description": "አዳዲስ እትም ሲኖር የሚታይ እትም ርዕስ"}, "validationCouponCodeIncomplete": "<PERSON><PERSON> de thök de wëu a<PERSON> thök wɛ̈lɛ̈/ka këc lui", "@validationCouponCodeIncomplete": {"description": "Error message shown when coupon code length or format is invalid"}, "validationCouponInvalid": "<PERSON><PERSON><PERSON> thiëcku ba namba de thök de wëu tääu thïn", "@validationCouponInvalid": {"description": "Error message shown when coupon code is invalid"}, "validationEmailInvalid": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> c<PERSON> lui.", "@validationEmailInvalid": {"description": "Error message shown when email format is invalid"}, "validationFieldIsRequired": "{fieldName} aye kɔɔr", "@validationFieldIsRequired": {"description": "Generic required field error message", "placeholders": {"fieldName": {"type": "String", "example": "Email"}}}, "validationLengthError": "{fieldName} adhil tɔ̈u në kaam de {minLength} ku {maxLength} ë wël bɛ̈ɛ̈r", "@validationLengthError": {"description": "Error message for fields that don't meet length requirements", "placeholders": {"fieldName": {"type": "String", "example": "Password"}, "minLength": {"type": "int", "example": "8"}, "maxLength": {"type": "int", "example": "16"}}}, "validationMinLengthError": "{fieldName} adhil ya kë thiin koor {minLength} ë wël bɛ̈ɛ̈r", "@validationMinLengthError": {"description": "Error message for fields that don't meet minimum length requirement", "placeholders": {"fieldName": {"type": "String", "example": "Password"}, "minLength": {"type": "int", "example": "8"}}}, "validationPhoneNumberIncomplete": "Namba de telepun këc thök.", "@validationPhoneNumberIncomplete": {"description": "Error message shown when phone number is incomplete"}, "viewButtonText": "<PERSON><PERSON><PERSON>", "@viewButtonText": {"description": "Text for the view action button on page"}, "viewContactDetailAppBarTitle": "Käk ë yök", "@viewContactDetailAppBarTitle": {"description": "Title of the contact detail page"}, "voucherLoadedSuccessMessage": "Kë cï tääu në wëu yiic acï tääu apieth", "@voucherLoadedSuccessMessage": {"description": "Message shown when voucher is successfully loaded"}, "welcomeBackText": "Ɣɛn baai, {firstName}", "@welcomeBackText": {"description": "Greeting message shown to returning users", "placeholders": {"firstName": {"type": "String", "example": "<PERSON>"}}}, "googlePay": "Google Pay", "@googlePay": {"description": "Text shown on button to pay using Google Pay"}, "appStoreCost": "+ {value} Wëu AppStore", "@appStoreCost": {"description": "Label showing the cost charged by the App Store including fees and VAT", "placeholders": {"value": {"type": "String", "example": "USD 2.99"}}}}