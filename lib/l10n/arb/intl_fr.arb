{"@@locale": "fr", "acceptButtonText": "Accepter ", "@acceptButtonText": {"description": "Text shown on buttons that accept or confirm an action"}, "accountBalanceCardTitle": "Solde ", "@accountBalanceCardTitle": {"description": "Title of the account balance page"}, "activateAutoCredit": "Activer le crédit automatique", "@activateAutoCredit": {"description": "Text for button to enable automatic account crediting"}, "all": "Tous ", "@all": {"description": "Text shown for all notifications"}, "allChannels": "Toutes les chaînes ", "@allChannels": {"description": "Text shown to represent all available radio channels or stations"}, "allowAccessButtonText": "Autoriser l''accès pour cette action requise ", "@allowAccessButtonText": {"description": "ተጠቃሚውን የመሳሪያ ባህሪዎች መድረሻ እንዲፈቀድ በመጠየቅ ላይ የሚታይ ጠንቅቆ የተጻፈ ጽሑፍ"}, "allowShareInfoWithRadio": "Autorisez-nous à partager vos informations avec {radioName}\n", "@allowShareInfoWithRadio": {"description": "Text shown when requesting permission to share user information with a radio station", "placeholders": {"radioName": {"type": "String", "example": "KEXP Radio"}}}, "appBootingTextBelow": "Aider les communautés à rester connectées ", "@appBootingTextBelow": {"description": "Text that shows below the app title on the booting screen"}, "appLanguageAppbarTitle": "Langue de l''application ", "@appLanguageAppbarTitle": {"description": "This is a title for the app bar"}, "apply": "Appliquer", "@apply": {"description": "Text for apply/confirm buttons"}, "appTitle": "<PERSON><PERSON>T<PERSON>", "@appTitle": {"description": "The name of the app"}, "autoAccountCreditHelp": "seront facturés sur votre carte et ajoutés à votre solde chaque fois qu''il descendra en dessous de", "@autoAccountCreditHelp": {"description": "Explanation text for the auto credit feature"}, "avoidCallDisruption": "pour éviter d''être coupé pendant un appel", "@avoidCallDisruption": {"description": "Additional explanation for auto credit feature benefit"}, "buyCreditAmountCustomPlaceholder": "min {min} - max {max}", "@buyCreditAmountCustomPlaceholder": {"description": "Placeholder text for the custom amount input field on the buy credit page", "placeholders": {"min": {"type": "String", "example": "$50"}, "max": {"type": "String", "example": "$500"}}}, "buyCreditAmountRecommended": "Recommandé ", "@buyCreditAmountRecommended": {"description": "Text that shows above the recommended amount on the buy credit page"}, "buyCreditAppBarTitle": "Acheter du crédit ", "@buyCreditAppBarTitle": {"description": "The app page title for buy credit"}, "buyCreditButtonText": "Acheter du crédit ", "@buyCreditButtonText": {"description": "Text for the buy credit button on the account balance page"}, "buyCreditEnterCustomAmountLabel": "<PERSON><PERSON> ", "@buyCreditEnterCustomAmountLabel": {"description": "Label for the custom amount input field on the buy credit page"}, "buyCreditPageTitle": "<PERSON><PERSON> ", "@buyCreditPageTitle": {"description": "Title of the buy credit page"}, "callButtonText": "<PERSON><PERSON><PERSON> ", "@callButtonText": {"description": "Text for the call action button on a page"}, "callingAppBarTitle": "Appel en cours ", "@callingAppBarTitle": {"description": "Title of the calling page"}, "callingPageBluetoothOptionsText": "Bluetooth", "@callingPageBluetoothOptionsText": {"description": "Text that shows to toggle the bluetooth on the calling page"}, "callingPageCreditErrorCardTitle": "Crédit d''appel épuisé ", "@callingPageCreditErrorCardTitle": {}, "callingPagePhoneOptionsText": "Téléphone ", "@callingPagePhoneOptionsText": {"description": "Text that shows to toggle the phone on the calling page"}, "callingPageSpeakerOptionsText": "Haut-parleur ", "@callingPageSpeakerOptionsText": {"description": "Text that shows to toggle the speaker on the calling page"}, "callingPageTimeLeft": "{time} restante(s)", "@callingPageTimeLeft": {"description": "Text that shows the time left for the call to end", "placeholders": {"time": {"type": "String", "example": "2"}}}, "callLogTypeAnswered": "Répondu ", "@callLogTypeAnswered": {"description": "Text that shows when the call log type is answered"}, "callLogTypeBusy": "<PERSON><PERSON><PERSON><PERSON> ", "@callLogTypeBusy": {"description": "Text that shows when the call log type is busy"}, "callLogTypeCancel": "<PERSON><PERSON><PERSON> ", "@callLogTypeCancel": {"description": "Text that shows when the call log type is cancelled"}, "callLogTypeIncoming": "Entrant ", "@callLogTypeIncoming": {"description": "Text that shows when the call log type is incoming"}, "callLogTypeMissed": "<PERSON><PERSON><PERSON> ", "@callLogTypeMissed": {"description": "Text that shows when the call log type is missed"}, "callLogTypeOutgoing": "Sortant ", "@callLogTypeOutgoing": {"description": "Text that shows when the call log type is outgoing"}, "callLogTypeUnavailable": "Indisponible ", "@callLogTypeUnavailable": {"description": "Text that shows when the call log type is unavailable"}, "callRatesAppBarTitle": "Tarifs d''appel ", "@callRatesAppBarTitle": {"description": "Title of the call rates page"}, "callTypeAnswered": "Appel sortant ", "@callTypeAnswered": {"description": "Text that shows when the call is answered"}, "callTypeBusy": "<PERSON><PERSON><PERSON><PERSON> ", "@callTypeBusy": {"description": "Text that shows when the call is busy"}, "callTypeCancel": "<PERSON><PERSON> annulé ", "@callTypeCancel": {"description": "Text that shows when the call is cancelled"}, "callTypeIncoming": "A<PERSON> entrant ", "@callTypeIncoming": {"description": "Text that shows when the call is incoming"}, "callTypeMissed": "<PERSON><PERSON> ", "@callTypeMissed": {"description": "Text that shows when the call is missed"}, "callTypeOutgoing": "Appel sortant ", "@callTypeOutgoing": {"description": "Text that shows when the call is outgoing"}, "callTypeUnavailable": "Utilisateur indisponible ", "@callTypeUnavailable": {"description": "Text that shows when the user is unavailable"}, "cancel": "Annuler", "@cancel": {"description": "Text for cancel buttons"}, "cancelButtonText": "Annuler ", "@cancelButtonText": {}, "cashPrizeAmount": "{currencySymbol}{amount} Argent GRATUIT", "@cashPrizeAmount": {"description": "Text showing the cash prize amount for promotions and referrals", "placeholders": {"currencySymbol": {"type": "String", "example": "€"}, "amount": {"type": "String", "example": "250"}}}, "changeChannelButtonText": "Changer <PERSON> <PERSON><PERSON><PERSON> ", "@changeChannelButtonText": {"description": "Text shown on button to change radio channel"}, "channel": "<PERSON><PERSON><PERSON> ", "@channel": {"description": "Text shown to represent a radio channel or station"}, "chatWithLiveAgentAppbarTitle": "Agent en direct ", "@chatWithLiveAgentAppbarTitle": {}, "checkBackLaterText": "Veuillez réessayer plus tard ", "@checkBackLaterText": {"description": "Message shown when a feature or content is temporarily unavailable"}, "chooseLanguage": "Choisir la langue", "@chooseLanguage": {"description": "Title for the language selection modal"}, "closeButtonText": "<PERSON><PERSON><PERSON> ", "@closeButtonText": {"description": "Text shown on buttons that close views or dialogs"}, "confirm": "Confirmer", "@confirm": {"description": "Text for confirm/verification buttons"}, "confirmationAppBarTitle": "Confirmation", "@confirmationAppBarTitle": {"description": "Title of the confirmation page"}, "confirmationFailedButtonText": "<PERSON><PERSON><PERSON><PERSON> ", "@confirmationFailedButtonText": {"description": "Text for the try again button on the confirmation page"}, "confirmationFailedContactSupportDescription": "Pour toute question ou assistance, contactez notre service client ", "@confirmationFailedContactSupportDescription": {"description": "Description that shows when the payment fails"}, "confirmationFailedContactSupportText": "<PERSON>er le support ", "@confirmationFailedContactSupportText": {"description": "Text for the contact support button on the confirmation page"}, "confirmationFailedDescription": "Votre paiement a ÉCHOUÉ. Veuillez réessayer ", "@confirmationFailedDescription": {"description": "Description that shows when the payment fails"}, "confirmationFailedTitle": "<PERSON><PERSON><PERSON> ", "@confirmationFailedTitle": {"description": "Title that shows when the payment fails"}, "confirmationSuccessButtonText": "<PERSON><PERSON><PERSON><PERSON> ", "@confirmationSuccessButtonText": {"description": "Text for the success button on the confirmation page"}, "confirmationSuccessDescription": "{amount} ont été crédités avec succès sur votre solde ", "@confirmationSuccessDescription": {"description": "Description that shows when the payment is successful", "placeholders": {"amount": {"type": "String", "example": "$10"}}}, "confirmationSuccessTitle": "<PERSON><PERSON><PERSON> ", "@confirmationSuccessTitle": {"description": "Title that shows when the payment is successful"}, "contactsAllContactsTabText": "Contacts", "@contactsAllContactsTabText": {"description": "Text that shows above the contacts list on the home page"}, "contactsCallRateText": "{min} min {secs} secs", "@contactsCallRateText": {"description": "Text that shows the duration of a call", "placeholders": {"min": {"type": "String", "example": "10"}, "secs": {"type": "String", "example": "3"}}}, "contactsFavouriteCallsTabText": "<PERSON><PERSON><PERSON> ", "@contactsFavouriteCallsTabText": {"description": "Text that shows above the Favourite calls list on the home page"}, "contactsNoCallsEmptyMessage": "Vous n''avez aucun appel récent ", "@contactsNoCallsEmptyMessage": {"description": "Message that shows when the user has no recent calls"}, "contactsNoContactsButtonText": "Voir les Contacts", "@contactsNoContactsButtonText": {"description": "Text for the view contacts button on the home page"}, "contactsSearchContactsPlaceholder": "Rechercher un contact ", "@contactsSearchContactsPlaceholder": {"description": "Placeholder text for the search contact name"}, "contactsSearchForContactsPlaceholder": "Rechercher un contact ", "@contactsSearchForContactsPlaceholder": {"description": "Placeholder text for the search input field on the contacts page"}, "couponAppliedButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "@couponAppliedButtonText": {"description": "ኩፖኑ በትክክል ሲተገብር የሚታይ ጽሑፍ"}, "couponApplyButtonText": "Appliquer ", "@couponApplyButtonText": {"description": "የኩፖን አተግባለሁ አዝራር ጽሑፍ"}, "credit": "Crédit ", "@credit": {"description": "Text shown for credit"}, "declineButtonText": "Refuser ", "@declineButtonText": {"description": "Text shown on buttons that decline or reject an action"}, "deleteAccountWarning": "Cette action est irréversible. Veuillez confirmer que vous souhaitez supprimer votre compte.", "@deleteAccountWarning": {"description": "Warning message shown when user attempts to delete their account"}, "dialerAppBarTitle": "<PERSON><PERSON><PERSON> ", "@dialerAppBarTitle": {"description": "Title of the dialer page"}, "dialerCopyActionText": "<PERSON><PERSON><PERSON> ", "@dialerCopyActionText": {"description": "Text for the copy action on the dialer page"}, "dialerErrorInvalidNumber": "Numéro invalide ", "@dialerErrorInvalidNumber": {"description": "Error message that shows when the user enters an invalid number"}, "dialerErrorWithoutCountryCode": "Ajoutez l''indicatif pays (e.g +1)", "@dialerErrorWithoutCountryCode": {"description": "Error message that shows when the user does not add a country code"}, "dialerKeypadABC": "ABC", "@dialerKeypadABC": {"description": "Text that shows on the ABC button on the dialer page"}, "dialerKeypadDEF": "DEF", "@dialerKeypadDEF": {"description": "Text that shows on the DEF button on the dialer page"}, "dialerKeypadGHI": "GHI", "@dialerKeypadGHI": {"description": "Text that shows on the GHI button on the dialer page"}, "dialerKeypadJKL": "JKL", "@dialerKeypadJKL": {"description": "Text that shows on the JKL button on the dialer page"}, "dialerKeypadMNO": "MNO", "@dialerKeypadMNO": {"description": "Text that shows on the MNO button on the dialer page"}, "dialerKeypadPQRS": "PQRS", "@dialerKeypadPQRS": {"description": "Text that shows on the PQRS button on the dialer page"}, "dialerKeypadTUV": "TUV", "@dialerKeypadTUV": {"description": "Text that shows on the TUV button on the dialer page"}, "dialerKeypadWXYZ": "WXYZ", "@dialerKeypadWXYZ": {"description": "Text that shows on the WXYZ button on the dialer page"}, "dialerMuteStatusMuted": "<PERSON><PERSON> ", "@dialerMuteStatusMuted": {"description": "Text that shows when the dialer is muted"}, "dialerMuteStatusUnmuted": "Non muet ", "@dialerMuteStatusUnmuted": {"description": "Text that shows when the dialer is unmuted"}, "dialerPasteActionText": "<PERSON><PERSON> ", "@dialerPasteActionText": {"description": "Text for the paste action on the dialer page"}, "dialerSearchResultsHeader": "Résultats ", "@dialerSearchResultsHeader": {"description": "Header text for the search results on the dialer page"}, "dialerSearchResultsNoResults": "Aucun résultat trouvé ", "@dialerSearchResultsNoResults": {"description": "Text that shows when no results are found for the search input field on the dialer page"}, "dialerStatusConnected": "Connecté ", "@dialerStatusConnected": {"description": "Text that shows when the dialer status is connected"}, "dialerStatusConnecting": "Appel en cours ", "@dialerStatusConnecting": {"description": "Text that shows when the dialer status is connecting"}, "dialerStatusEnded": "<PERSON><PERSON><PERSON><PERSON> ", "@dialerStatusEnded": {"description": "Text that shows when the dialer status is ended"}, "dialerStatusError": "Une erreur est survenue ", "@dialerStatusError": {"description": "Text that shows when an error occurs in the dialer"}, "dialerStatusFailedToConnect": "Échec de la connexion ", "@dialerStatusFailedToConnect": {"description": "Text that shows when the dialer fails to connect"}, "dialerStatusHold": "En attente ", "@dialerStatusHold": {"description": "Text that shows when the dialer status is on hold"}, "dialerStatusInitial": "Appel en cours ", "@dialerStatusInitial": {"description": "Text that shows when the dialer status is initial"}, "dialerStatusInitiating": "Appel en cours ", "@dialerStatusInitiating": {"description": "Text that shows when the dialer status is initiating"}, "dialerStatusRinging": "Sonnerie en cours ", "@dialerStatusRinging": {"description": "Text that shows when the dialer status is ringing"}, "dialerStatusUnknown": "Inconnu ", "@dialerStatusUnknown": {"description": "Text that shows when the dialer status is unknown"}, "emptyContactList": "Votre liste de contacts est vide.", "@emptyContactList": {"description": "Message that shows when the user has no contact list"}, "emptyFavouriteContactList": "Vous n''avez aucun contact favori ", "@emptyFavouriteContactList": {"description": "Message that shows when the user has no favourite contact list"}, "enterCouponOptionalPlaceholder": "Entrez un code promo (Optionnel)", "@enterCouponOptionalPlaceholder": {"description": "የኩፖን ኮድ መጠቀም ስፍራ ጽሑፍ"}, "enterOtpPageAppBarSubtitle": "Étape 2 sur 3", "@enterOtpPageAppBarSubtitle": {}, "enterOtpPageAppBarTitle": "Commencer ", "@enterOtpPageAppBarTitle": {}, "enterOtpPageEditButton": "Mouvais numéro WhatsApp ? Modifier ", "@enterOtpPageEditButton": {"description": "Text that shows on the edit phone number button"}, "enterOtpPageErrorMessage": "Code incorrect ", "@enterOtpPageErrorMessage": {"description": "Error message that shows when the user enters an incorrect OTP"}, "enterOtpPagePhoneLabel": "Entrez votre code de vérification ", "@enterOtpPagePhoneLabel": {}, "enterOtpPagePhoneLabelDescription": "Un code à 4 chiffres a été envoyé à votre numéro WhatsApp  {phoneNumber}", "@enterOtpPagePhoneLabelDescription": {"description": "Description text for the OTP input field on the OTP entry page", "placeholders": {"phoneNumber": {"type": "String", "example": "+1234567890"}}}, "enterOtpPageResendOtpButton": "Renvoyer le code ", "@enterOtpPageResendOtpButton": {"description": "Text that shows on the confirm verificatio button"}, "enterOtpPageResendOtpRetries": "{count, plural, zero{Plus d''essais } one{Dernier essai } other{# essais restants }}", "@enterOtpPageResendOtpRetries": {"description": "Message showing the number of retries a user has left", "placeholders": {"count": {"type": "int", "example": "3"}}}, "enterOtpPageResendOtpRetriesError": "Vous ne pouvez plus recevoir de codes sur ce numéro WhatsApp pendant 24 heures ", "@enterOtpPageResendOtpRetriesError": {"description": "Error message that shows when the user has exhausted the number of retries"}, "enterOtpPageSubmitButton": "Confirmer le code OTP ", "@enterOtpPageSubmitButton": {"description": "Text that shows on the confirm verificatio button"}, "enterVoucherCodeLabel": "Entrez le code promo ", "@enterVoucherCodeLabel": {"description": "Label for voucher code input field"}, "errorLoadingRadioStationsText": "Échec du chargement des stations de radio ", "@errorLoadingRadioStationsText": {"description": "Message shown when radio stations fail to load"}, "failedToLoadLeaderboard": "Échec du chargement du classement", "@failedToLoadLeaderboard": {"description": "Error message displayed when the referral leaderboard cannot be loaded"}, "free": "GRATUIT ", "@free": {"description": "Text shown for free promotional content"}, "freeCreditAppbarTitle": "<PERSON><PERSON><PERSON> gratuit ", "@freeCreditAppbarTitle": {"description": "Title shown in app bar of free credit page"}, "freeCreditPageContent_1": "Partagez votre code de parrainage avec vos amis & famille.", "@freeCreditPageContent_1": {"description": "First instruction step for referral program"}, "freeCreditPageContent_2": "<PERSON><PERSON> té<PERSON><PERSON>rgent Froggy<PERSON>alk et  s''inscrire avec votre code.", "@freeCreditPageContent_2": {"description": "Second instruction step for referral program"}, "freeCreditPageContent_3": "Vous gagnez {amount} quand la personne achète du crédit pour la première fois.", "@freeCreditPageContent_3": {"description": "Third instruction step showing reward amount", "placeholders": {"amount": {"type": "String", "example": "€5.00"}}}, "freeCreditPageHeadingText": "Parrainez un ami et gagnez {amount}", "@freeCreditPageHeadingText": {"description": "Main heading showing referral reward amount", "placeholders": {"amount": {"type": "String", "example": "€5.00"}}}, "freeCreditPageShareReferralLinkButtonText": "Partager le lien de parrainage ", "@freeCreditPageShareReferralLinkButtonText": {"description": "Text shown on button to share referral link"}, "freeCreditPageSubHeadingText": "<PERSON><PERSON> fill<PERSON> ({amount})", "@freeCreditPageSubHeadingText": {"description": "Subheading showing number of referrals", "placeholders": {"amount": {"type": "String", "example": "3"}}}, "froggytalkCustomerLabel": "Client <PERSON> ", "@froggytalkCustomerLabel": {"description": "Default label shown when user name is not available"}, "froggyTalkLovesYou": "FroggyTalk vous aime.", "@froggyTalkLovesYou": {"description": "Affirmative message showing app's appreciation for users"}, "getNow": "Obtenez maintenant", "@getNow": {"description": "Text for a button or link to get an offer or promotion"}, "helpCenterAppbarTitle": "Centre d''aide ", "@helpCenterAppbarTitle": {}, "helpCenterPageMenu_1": "Faire une réclamation ou suggestion ", "@helpCenterPageMenu_1": {}, "helpCenterPageMenu_2": "Discuter avec un agent ", "@helpCenterPageMenu_2": {}, "helpCenterPageMenu_3": "FAQ", "@helpCenterPageMenu_3": {}, "helpCenterPageMenu_4": "<PERSON><PERSON><PERSON><PERSON> ( Centre d''aide)", "@helpCenterPageMenu_4": {"description": "Text that shows on the WhatsApp button on the help center page"}, "homeNavigationBarText": "Accueil ", "@homeNavigationBarText": {"description": "navbar text for home page"}, "inAppPurchaseLabel": "<PERSON><PERSON><PERSON>", "@inAppPurchaseLabel": {"description": "Label indicating this is an in-app purchase from the app store"}, "internetConnectionAlertTextError": "Oups ! Vous semblez hors ligne.", "@internetConnectionAlertTextError": {}, "internetConnectionAlertTextSuccess": "Tout est bon ! Vous êtes reconnecté (e).", "@internetConnectionAlertTextSuccess": {}, "invalidInternationalPhoneFormat": "Format de numéro international invalide ", "@invalidInternationalPhoneFormat": {"description": "Error message shown when phone number format is invalid"}, "invalidPhoneNumber": "Veuillez entrer un numéro de téléphone valide ", "@invalidPhoneNumber": {"description": "Error message shown when phone number is invalid"}, "keypadNavigationBarText": "<PERSON><PERSON><PERSON> ", "@keypadNavigationBarText": {"description": "navbar text for keypad page"}, "landlineText": "Ligne fixe ", "@landlineText": {"description": "Text that shows when the number is a landline number"}, "languageChangeError": "Échec du changement de langue. Veuillez réessayer.", "@languageChangeError": {"description": "Error message shown when language change fails"}, "languageSelectionDisclaimer": "Vous pouvez changer votre langue sous", "@languageSelectionDisclaimer": {"description": "Message informing users where they can find language settings"}, "languageSetupPageAppBarSubtitle": "Étape 3 sur 3", "@languageSetupPageAppBarSubtitle": {"description": "Subtitle of the profile setup page showing progress"}, "loadingRadioStationsText": "Chargement des stations de radio...", "@loadingRadioStationsText": {"description": "Message shown while radio stations are being loaded"}, "loadVoucherCardButtonText": "Recharger", "@loadVoucherCardButtonText": {}, "loadVoucherCardErrorText": "Code incorrect, ve<PERSON><PERSON><PERSON> réessayer ", "@loadVoucherCardErrorText": {}, "loadVoucherCardLabelText": "Entrez un code promo à 10 chiffres ", "@loadVoucherCardLabelText": {}, "loadVoucherCardTitle": "Charger un code promo ", "@loadVoucherCardTitle": {}, "loadVoucherCodeTitle": "Charger un code promo ", "@loadVoucherCodeTitle": {"description": "Title shown on the load voucher code dialog"}, "loginPageAppBarSubtitle": "Étape 1 sur 3", "@loginPageAppBarSubtitle": {"description": "Subtitle of the login page"}, "loginPageAppBarTitle": "Commencer ", "@loginPageAppBarTitle": {"description": "Title of the login page"}, "loginPageCheckboxLabel": "J''ai un code de parrainage ", "@loginPageCheckboxLabel": {}, "loginPageErrorMessage": "Le numéro n''est pas associé à un compte WhatsApp ", "@loginPageErrorMessage": {}, "loginPagePhoneLabel": "Entrez votre numéro WhatsApp", "@loginPagePhoneLabel": {}, "loginPagePhoneLabelDescription": "Un code de vérification unique vous sera envoyé via WhatsApp", "@loginPagePhoneLabelDescription": {}, "loginPagePhoneNumberError": "Votre numéro de téléphone est incomplet, veuille<PERSON> le compléter ", "@loginPagePhoneNumberError": {}, "loginPageReferralLabel": "Entrez votre code de parrainage ", "@loginPageReferralLabel": {}, "loginPageSubmitButton": "Demander la vérification ", "@loginPageSubmitButton": {}, "minsOrMin": "{count, plural, zero{0 min} one{1 min} other{mins}}", "@minsOrMin": {"description": "Text that shows either '1 min' or '{count} mins' based on the count", "placeholders": {"count": {"type": "int", "example": "5"}}}, "mobileText": "Mobile", "@mobileText": {"description": "Text that shows when the number is a mobile number"}, "monthlyReferralBoard": "{month} <PERSON><PERSON> Gagnante", "@monthlyReferralBoard": {"description": "Title for the monthly promotion leaderboard showing top referrers", "placeholders": {"month": {"type": "String", "example": "Jan'25"}}}, "moreAppbarTitle": "<PERSON><PERSON><PERSON> ", "@moreAppbarTitle": {}, "moreNavigationBarText": "Plus ", "@moreNavigationBarText": {"description": "navbar text for more page"}, "morePageAccountBalanceCardTitle": "Solde ", "@morePageAccountBalanceCardTitle": {}, "morePageAppVersionMenuText": "Version de l''application ", "@morePageAppVersionMenuText": {}, "morePageCallRatesMenuText": "Tarifs d''appel ", "@morePageCallRatesMenuText": {}, "morePageHelpCenterMenuText": "Centre d''aide ", "@morePageHelpCenterMenuText": {}, "morePageLanguageMenuText": "<PERSON><PERSON> ", "@morePageLanguageMenuText": {}, "morePageLoadVoucherMenuText": "Charger un un code promo ", "@morePageLoadVoucherMenuText": {}, "morePageLogoutMenuText": "Déconnexion ", "@morePageLogoutMenuText": {}, "morePageProfileMenuText": "Profil", "@morePageProfileMenuText": {}, "morePageRadioMenuText": "Radio", "@morePageRadioMenuText": {}, "morePageReferralCodeCardButtonText": "Plus d''infos ", "@morePageReferralCodeCardButtonText": {}, "morePageReferralCodeCardContent": "{numberOfPeople} personnes ont utilisé votre code de parrainage ", "@morePageReferralCodeCardContent": {}, "morePageReferralCodeCardDescription": "<PERSON><PERSON><PERSON> {amount} en parrainant vos amis famille", "@morePageReferralCodeCardDescription": {}, "morePageReferralCodeCardTitle": "Code de parrainage ", "@morePageReferralCodeCardTitle": {}, "nextTrack": "Piste suivante ", "@nextTrack": {"description": "Text shown on button to play the next radio track or station"}, "noBuyCreditOptionsAvailable": "Aucune option d''achat de crédit disponible", "@noBuyCreditOptionsAvailable": {"description": "Message that shows when there are no buy credit options available"}, "noFavoriteContactsMessage": "Aucun contact favori ", "@noFavoriteContactsMessage": {"description": "Message shown when there are no favorite contacts to display"}, "noLanguagesAvailable": "Aucune langue disponible", "@noLanguagesAvailable": {"description": "Message shown when no languages are available to select"}, "noNotificationsYet": "Aucune notification pour le moment ", "@noNotificationsYet": {"description": "Text shown when there are no notifications"}, "noRadioStationsAvailableText": "Aucune station de radio disponible ", "@noRadioStationsAvailableText": {"description": "Message shown when there are no radio stations to display"}, "notEnoughCreditMessage": "Crédit insuffisant ", "@notEnoughCreditMessage": {"description": "Message shown when user has insufficient credit for a call"}, "notification": "Notification", "@notification": {"description": "Text shown for a single notification"}, "notificationsAppBarTitle": "Notifications", "@notificationsAppBarTitle": {"description": "Title of the notifications page"}, "notificationsEmptyMessage": "Vous n''avez aucune notification ", "@notificationsEmptyMessage": {"description": "Message that shows when the user has no notifications"}, "notificationSettingsAppBarTitle": "Paramètres de notification ", "@notificationSettingsAppBarTitle": {"description": "Title of the notification settings page"}, "notificationSettingsAutoDeleteNotificationText": "Suppression automatique des notifications ", "@notificationSettingsAutoDeleteNotificationText": {"description": "Text that shows to toggle the auto delete notification on the notification settings page"}, "notificationSettingsNotificationSoundText": "Son de notification ", "@notificationSettingsNotificationSoundText": {"description": "Text that shows to toggle the notification sound on the notification settings page"}, "notificationsTabAllText": "Toutes ", "@notificationsTabAllText": {"description": "Text that shows on the all tab on the notifications page"}, "notificationsTabUnreadText": "Non lus ", "@notificationsTabUnreadText": {"description": "Text that shows on the unread tab on the notifications page"}, "notificationsTimeAgo": "{time} {time, plural, one{{duration}} other{{duration}s}} iI ya ", "@notificationsTimeAgo": {"description": "Text that shows the time since the notification was received", "placeholders": {"time": {"type": "int", "example": "2"}, "duration": {"type": "String", "example": "hours"}}}, "nowPlaying": "En cours de lecture ", "@nowPlaying": {"description": "Text shown to indicate the currently playing radio track or station"}, "okButtonText": "Ok", "@okButtonText": {"description": "Text shown on OK confirmation button"}, "onboardingPageFooterAndText": "et", "@onboardingPageFooterAndText": {"description": "Text that shows between the terms and conditions link and the privacy policy link on the onboarding page"}, "onboardingPageFooterPrivacy": "Politique de confidentialité ", "@onboardingPageFooterPrivacy": {"description": "Text that shows below the submit button on the onboarding page"}, "onboardingPageFooterTermsConditionsText": "Conditions générales ", "@onboardingPageFooterTermsConditionsText": {"description": "Text that shows below the submit button on the onboarding page"}, "onboardingPageFooterText": "En cliquant sur commencer. Vous acceptez les ", "@onboardingPageFooterText": {"description": "Text that shows below the submit button on the onboarding page"}, "onboardingPageSliderHeader1": "Multilingue ", "@onboardingPageSliderHeader1": {"description": "Header text for the first slide on the onboarding page"}, "onboardingPageSliderHeader2": "Appelez mobiles et fixes dans le monde entier ", "@onboardingPageSliderHeader2": {"description": "Header text for the second slide on the onboarding page"}, "onboardingPageSliderHeader3": "Paiement facile ", "@onboardingPageSliderHeader3": {"description": "Header text for the third slide on the onboarding page"}, "onboardingPageSliderText1": "FroggyTalk vous permet de choisir votre langue préférée (Tigrigna, Amharique, Haussa, etc.)", "@onboardingPageSliderText1": {"description": "Text for the first slide on the onboarding page"}, "onboardingPageSliderText2": "Passez des appels vers n'importe quel mobile ou fixe dans le monde. Le destinataire n'a pas besoin de smartphone ou de connexion internet.", "@onboardingPageSliderText2": {"description": "Text for the second slide on the onboarding page"}, "onboardingPageSliderText3": "Achetez du crédit d''appel dans votre devise locale avec votre moyen de paiement préféré.", "@onboardingPageSliderText3": {"description": "Text for the third slide on the onboarding page"}, "onboardingPageSubmitButtonText": "Commencer ", "@onboardingPageSubmitButtonText": {"description": "Text for the submit button on the onboarding page"}, "outOfCreditLabel": "Plus de crédit ", "@outOfCreditLabel": {"description": "Label shown when user has no remaining credit"}, "paymentFailureAppBarTitle": "Échec ", "@paymentFailureAppBarTitle": {"description": "Title shown in app bar of payment failure page"}, "paymentFailureHomeButton": "Accueil ", "@paymentFailureHomeButton": {"description": "Text for button to return to home page"}, "paymentFailureMessage": "Votre paiement de {amount} a échoué ", "@paymentFailureMessage": {"description": "Message shown when payment fails, includes amount", "placeholders": {"amount": {"type": "String", "example": "$50.00"}}}, "paymentFailureTitle": "<PERSON><PERSON><PERSON> ", "@paymentFailureTitle": {"description": "Main title shown when payment fails"}, "paymentFailureTryAgainButton": "<PERSON><PERSON><PERSON><PERSON> ", "@paymentFailureTryAgainButton": {"description": "Text for button to retry payment"}, "paymentOptionsAppBarTitle": " Options de paiement ", "@paymentOptionsAppBarTitle": {"description": "Title of the payment options page"}, "paymentOptionsAutoCreditDescription": "Votre compte sera automatiquement crédité de {amount} lorsque votre solde descend sous {minAmount}.", "@paymentOptionsAutoCreditDescription": {"description": "Description of the auto credit payment method"}, "paymentOptionsAutoCreditTitle": "Recharge automatique ", "@paymentOptionsAutoCreditTitle": {"description": "Title of the auto credit payment method"}, "paymentOptionsSelectPaymentMethod": "Choisissez votre moyen de paiement ", "@paymentOptionsSelectPaymentMethod": {"description": "Text that shows above the payment method selection dropdown"}, "paymentOptionsSummaryAmountToCreditText": "Montant à créditer ", "@paymentOptionsSummaryAmountToCreditText": {"description": "Text that shows the amount to be credited on the payment options page"}, "paymentOptionsSummaryDiscountButtonText": "Appliquer ", "@paymentOptionsSummaryDiscountButtonText": {"description": "Text for the apply discount button on the payment options page"}, "paymentOptionsSummaryDiscountPlaceholder": "Code promo (optionnel)", "@paymentOptionsSummaryDiscountPlaceholder": {"description": "Placeholder text for the discount code input field on the payment options page"}, "paymentOptionsSummaryDiscountText": "Remise ", "@paymentOptionsSummaryDiscountText": {"description": "Text that shows the discount on the payment options page"}, "paymentOptionsSummaryTotalPaymentText": "Total à payer ", "@paymentOptionsSummaryTotalPaymentText": {"description": "Text that shows the total payment on the payment options page"}, "paymentOptionsSummaryVatFeesText": "TVA + frais ", "@paymentOptionsSummaryVatFeesText": {"description": "Text that shows the VAT and fees on the payment options page"}, "paymentSummaryAmountToCreditLabel": "Montant à créditer ", "@paymentSummaryAmountToCreditLabel": {"description": "በክፍያ ማጠቃለያ ውስጥ የሚቀርብ መጠን ምልክት"}, "paymentSummaryDiscountLabel": "Remise ", "@paymentSummaryDiscountLabel": {"description": "በክፍያ ማጠቃለያ ውስጥ ቅናሽ ምልክት"}, "paymentSummaryPlayStoreFeeLabel": " +{percentage}% Commission et TVA", "@paymentSummaryPlayStoreFeeLabel": {"description": "Label showing the Play Store fee percentage and VAT in payment summary", "placeholders": {"percentage": {"type": "String", "example": "30"}}}, "paymentSummaryTotalLabel": "Total à payer ", "@paymentSummaryTotalLabel": {"description": "በክፍያ ማጠቃለያ ውስጥ ጠቅላላ ክፍያ ምልክት"}, "paymentSummaryVatFeesLabel": "TVA+  Frais ", "@paymentSummaryVatFeesLabel": {"description": "በክፍያ ማጠቃለያ ውስጥ ተጨማሪ ክፍያዎች ምልክት"}, "payWithApplePay": "Apple Pay", "@payWithApplePay": {"description": "Text shown on button to pay using Apple Pay"}, "payWithGooglePay": "\nGoogle Pay", "@payWithGooglePay": {"description": "Text shown on button to pay using Google Pay"}, "perMinRate": "{rate}/min", "@perMinRate": {"description": "Rate per minute for a call", "placeholders": {"rate": {"type": "String", "example": "$10"}}}, "perMinRateSingle": "/min", "@perMinRateSingle": {"description": "Rate per minute for a call in short form"}, "perMinuteRate": "{rate}/minute", "@perMinuteRate": {"description": "Rate per minute for a call", "placeholders": {"rate": {"type": "String", "example": "$10"}}}, "perMinuteSlashLabel": "{min}/min ", "@perMinuteSlashLabel": {"description": "Label showing '/minutes' for call rate displays", "placeholders": {"min": {"type": "String", "example": "5"}}}, "permissionButtonAccept": "Autoriser l''accès ", "@permissionButtonAccept": {"description": "Text for the accept button on the permission request dialog"}, "permissionButtonSkip": "Passer ", "@permissionButtonSkip": {"description": "Text for the skip button on the permission request dialog"}, "permissionDeniedMessage": "Permission refusée ", "@permissionDeniedMessage": {"description": "Message shown when a permission is denied by the user"}, "permissionForCameraTitle": "Nous avons besoin d''accéder à votre appareil photo et vidéo.", "@permissionForCameraTitle": {"description": "Title for the camera permission request dialog"}, "permissionForContactListTitle": "Autorisez FroggyTalk à accéder à votre liste de contacts  / répertoire ", "@permissionForContactListTitle": {"description": "Title for the contact list permission request dialog"}, "permissionForMicrophoneTitle": "Autorisez FroggyTalk à utiliser votre to Microphone pour les appels ", "@permissionForMicrophoneTitle": {"description": "Title for the microphone permission request dialog"}, "permissionForNotificationTitle": "Autorisez <PERSON> à vous envoyer des notifications ", "@permissionForNotificationTitle": {"description": "Title for the notification permission request dialog"}, "permissionForStorageTitle": "Nous avons besoin d''accéder à votre stokage pour enregistrer des données temporaires.", "@permissionForStorageTitle": {"description": "Title for the storage permission request dialog"}, "permissionPermanentlyDeniedMessage": "Permission définitivement refusée ", "@permissionPermanentlyDeniedMessage": {"description": "Message shown when a permission is permanently denied by the user"}, "perM_Rate": "{rate}/min", "@perM_Rate": {"description": "Rate per minute for a call", "placeholders": {"rate": {"type": "String", "example": "$10"}}}, "phoneNumberStartRule": "Veuillez commencer par + ou 00", "@phoneNumberStartRule": {"description": "Error message shown when phone number doesn't start with + or 00"}, "playRadio": "Écouter la radio ", "@playRadio": {"description": "Text shown on button to start playing the radio"}, "previousTrack": "Piste précédent<PERSON> ", "@previousTrack": {"description": "Text shown on button to play the previous radio track or station"}, "proceedToPaymentButtonText": "Procéder au paiement ", "@proceedToPaymentButtonText": {"description": "Text for the proceed to payment button on the buy credit page"}, "profileAppbarTitle": "Profil", "@profileAppbarTitle": {}, "profileDeleteAccountButtonText": "Supprimer le compte ", "@profileDeleteAccountButtonText": {}, "profileDeleteAccountCardContent": "Veuillez confirmer la suppression de votre compte. Vous perdez tout solde disponible.", "@profileDeleteAccountCardContent": {}, "profileDeleteAccountCardTitle": "Supprimer le compte ", "@profileDeleteAccountCardTitle": {}, "profileLabel": "Profil ", "@profileLabel": {"description": "Label used for profile sections"}, "profileLabelChangeLocation": "Changer de localisation ", "@profileLabelChangeLocation": {}, "profileLabelEmailAddress": "Entrer l''adresse e-mail ", "@profileLabelEmailAddress": {}, "profileLabelFullName": "Entrer le nom complet ", "@profileLabelFullName": {"description": "profile<PERSON>abelFull<PERSON>ame"}, "profileUpdatedSuccessfullyMessage": "Profil mis à jour avec succès ", "@profileUpdatedSuccessfullyMessage": {"description": "Message shown when profile is updated successfully"}, "profileUpdateFailedMessage": "Échec de la mise à jour du profil ", "@profileUpdateFailedMessage": {"description": "Message shown when profile update fails"}, "quickAdvertSubtitle": "Achetez du crédit pour votre famille dans le monde entier.", "@quickAdvertSubtitle": {"description": "Subtitle for the quick advert on the home page"}, "quickAdvertTitle": "Envoyer du crédit vers 140+ pays ", "@quickAdvertTitle": {"description": "Title for the quick advert on the home page"}, "radioAppBarTitle": "Radio", "@radioAppBarTitle": {}, "radioComingSoonText": "Radio disponible bientôt !", "@radioComingSoonText": {"description": "Text shown when radio feature is not yet available"}, "radioPageChooseChannelButtonText": "<PERSON>sir une chaîne ", "@radioPageChooseChannelButtonText": {}, "radioPageNoChannelsText": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> ", "@radioPageNoChannelsText": {}, "radioPageSearchPlaceholder": "<PERSON>sir une chaîne ", "@radioPageSearchPlaceholder": {}, "ratesNavigationBarText": "<PERSON><PERSON><PERSON> ", "@ratesNavigationBarText": {"description": "navbar text for rates page"}, "recentCallsText": "<PERSON><PERSON><PERSON> r<PERSON>s ", "@recentCallsText": {"description": "Text that shows above the recent calls list on the home page"}, "rechargeButtonText": "Recharger", "@rechargeButtonText": {"description": "Text shown on recharge button"}, "referAndEarn": "Parrainez & Gagnez ", "@referAndEarn": {"description": "Text shown for refer and earn promotional text"}, "referralCardButtonText": "<PERSON><PERSON><PERSON> gratuit ", "@referralCardButtonText": {"description": "Text for the referral card button"}, "referralCardDescription1": "Parrainez un ami et recevez {percentageAmount}  de crédit gratuit ", "@referralCardDescription1": {"description": "Message showing the referral benefit", "placeholders": {"percentageAmount": {"type": "String", "example": "10%"}}}, "referralCardDescription2": "{count, plural, zero{Personne n''a utilisé votre code de parrainage.} one{Une personne a utilisé votre code de parrainage.} other{{numberOfPeople} personnes ont utilisé votre code de parrainage }}", "@referralCardDescription2": {"description": "Message showing how many people have used the referral code, with pluralization.", "placeholders": {"numberOfPeople": {"type": "int", "example": "2"}}}, "referralCardTitle": "Code de parrainage ", "@referralCardTitle": {"description": "Title for the referral card"}, "referralCodeOnCopyActionResponse": "FroggyTalk copié dans le presse-papiers ", "@referralCodeOnCopyActionResponse": {}, "refreshText": "Actualiser ", "@refreshText": {"description": "Text shown on button to refresh or reload content"}, "restorePurchases": "<PERSON><PERSON> Purchases", "@restorePurchases": {"description": "Button text for restoring previous in-app purchases"}, "retry": "<PERSON><PERSON><PERSON><PERSON>", "@retry": {"description": "Text for button to retry a failed operation"}, "saveAndProceedToPaymentButtonText": "Enregistrer et Payer ", "@saveAndProceedToPaymentButtonText": {"description": "Text shown on button to save profile changes and proceed to payment"}, "saveChangesButtonText": "Enregistrer les modifications ", "@saveChangesButtonText": {}, "savedSuccessfullyMessage": "Enregistré avec succès ", "@savedSuccessfullyMessage": {"description": "Message shown when changes are saved successfully"}, "searchCountryPlaceholder": "Rechercher un pays ", "@searchCountryPlaceholder": {"description": "Placeholder text for the search input field on the country selection page"}, "searchFavouriteContactMessage": "Rechercher un contact favori ", "@searchFavouriteContactMessage": {"description": "Placeholder text for searching favorite contacts"}, "searchForCountryNoResults": "Aucun résultat trouvé ", "@searchForCountryNoResults": {"description": "Text that shows when no results are found for the search input field on the country selection page"}, "searchForCountryPlaceholder": "Rechercher un pays ", "@searchForCountryPlaceholder": {"description": "Placeholder text for the search input field on the country selection page"}, "searchRecentCallsMessage": "Rechercher dans les appels récentes ", "@searchRecentCallsMessage": {"description": "Placeholder text for searching recent calls"}, "secsOrSec": "{count, plural, zero{0} one{1 sec} other{secs}}", "@secsOrSec": {"description": "Text that shows either '1 sec' or '{count} secs' based on the count", "placeholders": {"count": {"type": "int", "example": "10"}}}, "securedByStripe": "Sécurisé par stripe ", "@securedByStripe": {"description": "Text showing that payment is secured by Stripe payment processor"}, "selectCountryPlaceholder": "Selection un pays ", "@selectCountryPlaceholder": {"description": "Placeholder text for the country selection dropdown"}, "selectPreferredLanguage": "Sélectionnez votre langue préférée", "@selectPreferredLanguage": {"description": "Subtitle for the language selection modal"}, "sendCreditButtonText": "Envoyer du crédit ", "@sendCreditButtonText": {"description": "Text for the send credit button on the home page"}, "settings": "Paramètres ", "@settings": {"description": "Text shown for settings"}, "shareReferralText": "Téléchargez l''application FroggyTalk sur {appLink} et inscrivez-vous avec mon code de parrainage {referralCode}. Elle offre les meilleurs tarifs et une excellente expérience d''appel international.", "@shareReferralText": {"description": "Text shown when sharing referral code", "placeholders": {"appLink": {"type": "String", "example": "https://link-to.app/AHA423kdwG"}, "referralCode": {"type": "String", "example": "ABC123"}}}, "somethingWentWrongMessage": "Une erreur s''est produite. Veuillez réessayer.", "@somethingWentWrongMessage": {"description": "Message that shows when an error occurs"}, "stopRadio": "Arrêter la radio ", "@stopRadio": {"description": "Text shown on button to stop the currently playing radio"}, "storePrice": "Prix en Magasin", "@storePrice": {"description": "Label for the price listed in the app store"}, "termsAndConditionsApply": "Des conditions générales s''appliquent", "@termsAndConditionsApply": {"description": "Text indicating that terms and conditions apply to an offer or promotion"}, "timeLeftModalMessage": "Il reste {days} jours pour gagner le prix en espèces. Parrainez {count} personnes et devenez numéro 1.", "@timeLeftModalMessage": {"description": "Message showing days left and number of referrals needed", "placeholders": {"days": {"type": "String", "example": "25"}, "count": {"type": "int", "example": "30"}}}, "timeLeftModalTitle": "{days} jours restants", "@timeLeftModalTitle": {"description": "Title showing the number of days left in the modal", "placeholders": {"days": {"type": "String", "example": "2"}}}, "tryAgainText": "<PERSON><PERSON><PERSON><PERSON> ", "@tryAgainText": {"description": "Text shown on button to retry an action that failed"}, "unknownErrorText": "Une erreur inconnue est survenue ", "@unknownErrorText": {"description": "Generic message shown when an unspecified error occurs"}, "unread": "Non lus ", "@unread": {"description": "Text shown for unread notifications"}, "updateLanguagePrompt": "Veuillez cliquer sur le bouton ci-dessous pour mettre à jour votre langue", "@updateLanguagePrompt": {"description": "Instruction text prompting user to update their language setting"}, "updateProfileButtonText": "Mettre à jour le profil ", "@updateProfileButtonText": {"description": "Text shown on button to update user profile"}, "updateYourLanguage": "Mettre à jour votre langue.", "@updateYourLanguage": {"description": "Button or action text for language update functionality"}, "upgradeDialogButtonNo": "Plus tard ", "@upgradeDialogButtonNo": {"description": "እትም ማስተካከልን ለመሰረዝ የሚጠቀምበት አዝራር ጽሑፍ"}, "upgradeDialogButtonYes": "Mettre à jour ", "@upgradeDialogButtonYes": {"description": "እትም ማስተካከልን ለመቀጠል የሚጠቀምበት አዝራር ጽሑፍ"}, "upgradeDialogMessage": "Une nouvelle version de FroggyTalk est disponible. Souhaitez-vous mettre à jour maintenant ?", "@upgradeDialogMessage": {"description": "አዳዲስ እትም ሲኖር ተጠቃሚውን ለመዘመን የሚጠይቅ መልእክት"}, "upgradeDialogTitle": "Mise à jour disponible ", "@upgradeDialogTitle": {"description": "አዳዲስ እትም ሲኖር የሚታይ እትም ርዕስ"}, "validationCouponCodeIncomplete": "Code promo incomplet ou invalide ", "@validationCouponCodeIncomplete": {"description": "Error message shown when coupon code length or format is invalid"}, "validationCouponInvalid": "Veuillez entrer un code promo valide ", "@validationCouponInvalid": {"description": "Error message shown when coupon code is invalid"}, "validationEmailInvalid": "Adress e-mail invalide ", "@validationEmailInvalid": {"description": "Error message shown when email format is invalid"}, "validationFieldIsRequired": "{fieldName} est obligatoire ", "@validationFieldIsRequired": {"description": "Generic required field error message", "placeholders": {"fieldName": {"type": "String", "example": "Email"}}}, "validationLengthError": "{fieldName} doit contenir entre {minLength} et {maxLength} caractères.", "@validationLengthError": {"description": "Error message for fields that don't meet length requirements", "placeholders": {"fieldName": {"type": "String", "example": "Password"}, "minLength": {"type": "int", "example": "8"}, "maxLength": {"type": "int", "example": "16"}}}, "validationMinLengthError": "{fieldName} doit contenir au moins {minLength} caractères", "@validationMinLengthError": {"description": "Error message for fields that don't meet minimum length requirement", "placeholders": {"fieldName": {"type": "String", "example": "Password"}, "minLength": {"type": "int", "example": "8"}}}, "validationPhoneNumberIncomplete": "Numéro de téléphone incomplet.", "@validationPhoneNumberIncomplete": {"description": "Error message shown when phone number is incomplete"}, "viewButtonText": "Voir ", "@viewButtonText": {"description": "Text for the view action button on page"}, "viewContactDetailAppBarTitle": "<PERSON><PERSON><PERSON> du contact ", "@viewContactDetailAppBarTitle": {"description": "Title of the contact detail page"}, "voucherLoadedSuccessMessage": "Code promo chargé avec succès ", "@voucherLoadedSuccessMessage": {"description": "Message shown when voucher is successfully loaded"}, "welcomeBackText": "<PERSON> retour, {firstName}", "@welcomeBackText": {"description": "Greeting message shown to returning users", "placeholders": {"firstName": {"type": "String", "example": "<PERSON>"}}}, "googlePay": "Google Pay", "@googlePay": {"description": "Text shown on button to pay using Google Pay"}, "appStoreCost": "+ {value} Frais App Store", "@appStoreCost": {"description": "Label showing the cost charged by the App Store including fees and VAT", "placeholders": {"value": {"type": "String", "example": "USD 2.99"}}}}