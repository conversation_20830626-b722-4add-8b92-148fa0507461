{"@@locale": "ha", "acceptButtonText": "Karɓa", "@acceptButtonText": {"description": "Text shown on buttons that accept or confirm an action"}, "accountBalanceCardTitle": "kudin asusu", "@accountBalanceCardTitle": {"description": "Title of the account balance page"}, "activateAutoCredit": "<PERSON>nna caja kati ta automatik", "@activateAutoCredit": {"description": "Text for button to enable automatic account crediting"}, "all": "<PERSON><PERSON>", "@all": {"description": "Text shown for all notifications"}, "allChannels": "<PERSON><PERSON> ta<PERSON>shi", "@allChannels": {"description": "Text shown to represent all available radio channels or stations"}, "allowAccessButtonText": "<PERSON><PERSON> i<PERSON>in yin wannan aikin da ake bukata", "@allowAccessButtonText": {"description": "ተጠቃሚውን የመሳሪያ ባህሪዎች መድረሻ እንዲፈቀድ በመጠየቅ ላይ የሚታይ ጠንቅቆ የተጻፈ ጽሑፍ"}, "allowShareInfoWithRadio": "<PERSON><PERSON> damar raba bayanan ka da {radioName}", "@allowShareInfoWithRadio": {"description": "Text shown when requesting permission to share user information with a radio station", "placeholders": {"radioName": {"type": "String", "example": "KEXP Radio"}}}, "appBootingTextBelow": "Tai<PERSON><PERSON> da na kasar waje kiran gida", "@appBootingTextBelow": {"description": "Text that shows below the app title on the booting screen"}, "appLanguageAppbarTitle": "<PERSON><PERSON><PERSON> na manhaja", "@appLanguageAppbarTitle": {"description": "This is a title for the app bar"}, "apply": "Apply", "@apply": {"description": "Text for apply/confirm buttons"}, "appTitle": "<PERSON><PERSON>T<PERSON>", "@appTitle": {"description": "The name of the app"}, "autoAccountCreditHelp": "za a caje katin ka a saka shi cikin ma''auni a duk lokacin da balanse ya faɗi ƙasa", "@autoAccountCreditHelp": {"description": "Explanation text for the auto credit feature"}, "avoidCallDisruption": "don gujewa katsewar waya", "@avoidCallDisruption": {"description": "Additional explanation for auto credit feature benefit"}, "buyCreditAmountCustomPlaceholder": "<PERSON><PERSON> ƙanƙanta adadi {min} -  {max}<PERSON><PERSON> yawa adadi     ", "@buyCreditAmountCustomPlaceholder": {"description": "Placeholder text for the custom amount input field on the buy credit page", "placeholders": {"min": {"type": "String", "example": "$50"}, "max": {"type": "String", "example": "$500"}}}, "buyCreditAmountRecommended": "An ba da shawara akan adadin sayen kuɗi", "@buyCreditAmountRecommended": {"description": "Text that shows above the recommended amount on the buy credit page"}, "buyCreditAppBarTitle": "<PERSON>ya kati", "@buyCreditAppBarTitle": {"description": "The app page title for buy credit"}, "buyCreditButtonText": "<PERSON>ya kati", "@buyCreditButtonText": {"description": "Text for the buy credit button on the account balance page"}, "buyCreditEnterCustomAmountLabel": "<PERSON><PERSON> da adadin da kake so", "@buyCreditEnterCustomAmountLabel": {"description": "Label for the custom amount input field on the buy credit page"}, "buyCreditPageTitle": "<PERSON><PERSON> da aka fi so", "@buyCreditPageTitle": {"description": "Title of the buy credit page"}, "callButtonText": "<PERSON>", "@callButtonText": {"description": "Text for the call action button on a page"}, "callingAppBarTitle": "<PERSON>na kira", "@callingAppBarTitle": {"description": "Title of the calling page"}, "callingPageBluetoothOptionsText": "Bluetooth", "@callingPageBluetoothOptionsText": {"description": "Text that shows to toggle the bluetooth on the calling page"}, "callingPageCreditErrorCardTitle": "Katin kira ya kare", "@callingPageCreditErrorCardTitle": {}, "callingPagePhoneOptionsText": "<PERSON><PERSON>", "@callingPagePhoneOptionsText": {"description": "Text that shows to toggle the phone on the calling page"}, "callingPageSpeakerOptionsText": "<PERSON><PERSON><PERSON><PERSON>", "@callingPageSpeakerOptionsText": {"description": "Text that shows to toggle the speaker on the calling page"}, "callingPageTimeLeft": "  {time} <PERSON><PERSON>", "@callingPageTimeLeft": {"description": "Text that shows the time left for the call to end", "placeholders": {"time": {"type": "String", "example": "2"}}}, "callLogTypeAnswered": "An amsa", "@callLogTypeAnswered": {"description": "Text that shows when the call log type is answered"}, "callLogTypeBusy": "<PERSON> cikin waya", "@callLogTypeBusy": {"description": "Text that shows when the call log type is busy"}, "callLogTypeCancel": "An fasa", "@callLogTypeCancel": {"description": "Text that shows when the call log type is cancelled"}, "callLogTypeIncoming": "<PERSON> da ke shigowa", "@callLogTypeIncoming": {"description": "Text that shows when the call log type is incoming"}, "callLogTypeMissed": "<PERSON> aka rasa", "@callLogTypeMissed": {"description": "Text that shows when the call log type is missed"}, "callLogTypeOutgoing": "<PERSON> da ke <PERSON>ta", "@callLogTypeOutgoing": {"description": "Text that shows when the call log type is outgoing"}, "callLogTypeUnavailable": "<PERSON> ", "@callLogTypeUnavailable": {"description": "Text that shows when the call log type is unavailable"}, "callRatesAppBarTitle": "<PERSON><PERSON><PERSON> kira", "@callRatesAppBarTitle": {"description": "Title of the call rates page"}, "callTypeAnswered": "<PERSON> da ke fita", "@callTypeAnswered": {"description": "Text that shows when the call is answered"}, "callTypeBusy": "<PERSON> cikin waya", "@callTypeBusy": {"description": "Text that shows when the call is busy"}, "callTypeCancel": "<PERSON> aka fasa", "@callTypeCancel": {"description": "Text that shows when the call is cancelled"}, "callTypeIncoming": "<PERSON> mai shigowa", "@callTypeIncoming": {"description": "Text that shows when the call is incoming"}, "callTypeMissed": "<PERSON> da baka dauka ba", "@callTypeMissed": {"description": "Text that shows when the call is missed"}, "callTypeOutgoing": "<PERSON> mai fita", "@callTypeOutgoing": {"description": "Text that shows when the call is outgoing"}, "callTypeUnavailable": "Mai amfani baya nan", "@callTypeUnavailable": {"description": "Text that shows when the user is unavailable"}, "cancel": "Cancel", "@cancel": {"description": "Text for cancel buttons"}, "cancelButtonText": "Soke", "@cancelButtonText": {}, "cashPrizeAmount": " KYAUTAR kudi{currencySymbol}{amount}", "@cashPrizeAmount": {"description": "Text showing the cash prize amount for promotions and referrals", "placeholders": {"currencySymbol": {"type": "String", "example": "€"}, "amount": {"type": "String", "example": "250"}}}, "changeChannelButtonText": "<PERSON><PERSON>", "@changeChannelButtonText": {"description": "Text shown on button to change radio channel"}, "channel": "<PERSON><PERSON><PERSON>", "@channel": {"description": "Text shown to represent a radio channel or station"}, "chatWithLiveAgentAppbarTitle": "<PERSON><PERSON><PERSON> kula", "@chatWithLiveAgentAppbarTitle": {}, "checkBackLaterText": "Da fatan za ka sake duba daga baya", "@checkBackLaterText": {"description": "Message shown when a feature or content is temporarily unavailable"}, "chooseLanguage": "Choose Language", "@chooseLanguage": {"description": "Title for the language selection modal"}, "closeButtonText": "Rufe", "@closeButtonText": {"description": "Text shown on buttons that close views or dialogs"}, "confirm": "Ka tabbatar", "@confirm": {"description": "Text for confirm/verification buttons"}, "confirmationAppBarTitle": "Tabbatarwa", "@confirmationAppBarTitle": {"description": "Title of the confirmation page"}, "confirmationFailedButtonText": "<PERSON>ke gwa<PERSON>wa", "@confirmationFailedButtonText": {"description": "Text for the try again button on the confirmation page"}, "confirmationFailedContactSupportDescription": "Idan kana da wasu tambayoyi ko buƙatar taimako, da fatan za a tuntuɓi ƙungiyar tallafin mu", "@confirmationFailedContactSupportDescription": {"description": "Description that shows when the payment fails"}, "confirmationFailedContactSupportText": "Tuntuɓi tallafin mu", "@confirmationFailedContactSupportText": {"description": "Text for the contact support button on the confirmation page"}, "confirmationFailedDescription": "Biyan kuɗin ka bai yi nasara ba. <PERSON>ke gwadawa", "@confirmationFailedDescription": {"description": "Description that shows when the payment fails"}, "confirmationFailedTitle": "<PERSON><PERSON><PERSON> bai shiga ba", "@confirmationFailedTitle": {"description": "Title that shows when the payment fails"}, "confirmationSuccessButtonText": "An gama", "@confirmationSuccessButtonText": {"description": "Text for the success button on the confirmation page"}, "confirmationSuccessDescription": "An kara {amount} a asusun ka", "@confirmationSuccessDescription": {"description": "Description that shows when the payment is successful", "placeholders": {"amount": {"type": "String", "example": "$10"}}}, "confirmationSuccessTitle": "<PERSON>iyan kuɗi ya yi nasara", "@confirmationSuccessTitle": {"description": "Title that shows when the payment is successful"}, "contactsAllContactsTabText": "Tuntuɓa", "@contactsAllContactsTabText": {"description": "Text that shows above the contacts list on the home page"}, "contactsCallRateText": " {secs} mintuna {min}  sekondi", "@contactsCallRateText": {"description": "Text that shows the duration of a call", "placeholders": {"min": {"type": "String", "example": "10"}, "secs": {"type": "String", "example": "3"}}}, "contactsFavouriteCallsTabText": "Ma fi so", "@contactsFavouriteCallsTabText": {"description": "Text that shows above the Favourite calls list on the home page"}, "contactsNoCallsEmptyMessage": "Ba ka da wani kiran", "@contactsNoCallsEmptyMessage": {"description": "Message that shows when the user has no recent calls"}, "contactsNoContactsButtonText": "<PERSON><PERSON> la<PERSON>", "@contactsNoContactsButtonText": {"description": "Text for the view contacts button on the home page"}, "contactsSearchContactsPlaceholder": "<PERSON><PERSON><PERSON> bayanan tuntuɓar", "@contactsSearchContactsPlaceholder": {"description": "Placeholder text for the search contact name"}, "contactsSearchForContactsPlaceholder": "<PERSON><PERSON><PERSON> bayanan tuntuɓar", "@contactsSearchForContactsPlaceholder": {"description": "Placeholder text for the search input field on the contacts page"}, "couponAppliedButtonText": "An nema", "@couponAppliedButtonText": {"description": "ኩፖኑ በትክክል ሲተገብር የሚታይ ጽሑፍ"}, "couponApplyButtonText": "nema", "@couponApplyButtonText": {"description": "የኩፖን አተግባለሁ አዝራር ጽሑፍ"}, "credit": "<PERSON><PERSON><PERSON>", "@credit": {"description": "Text shown for credit"}, "declineButtonText": "Ƙi", "@declineButtonText": {"description": "Text shown on buttons that decline or reject an action"}, "deleteAccountWarning": "<PERSON><PERSON> da kake shirin yi ba zai iya dawo baya ba. Da fatan za a tabbatar da cewa za ka goge asusunka\n\n \n", "@deleteAccountWarning": {"description": "Warning message shown when user attempts to delete their account"}, "dialerAppBarTitle": "mabugin waya", "@dialerAppBarTitle": {"description": "Title of the dialer page"}, "dialerCopyActionText": "K<PERSON><PERSON>", "@dialerCopyActionText": {"description": "Text for the copy action on the dialer page"}, "dialerErrorInvalidNumber": "la<PERSON><PERSON> baata aiki", "@dialerErrorInvalidNumber": {"description": "Error message that shows when the user enters an invalid number"}, "dialerErrorWithoutCountryCode": "kara lambar ƙasa (misali +1)", "@dialerErrorWithoutCountryCode": {"description": "Error message that shows when the user does not add a country code"}, "dialerKeypadABC": "ABC", "@dialerKeypadABC": {"description": "Text that shows on the ABC button on the dialer page"}, "dialerKeypadDEF": "DEF", "@dialerKeypadDEF": {"description": "Text that shows on the DEF button on the dialer page"}, "dialerKeypadGHI": "GHI", "@dialerKeypadGHI": {"description": "Text that shows on the GHI button on the dialer page"}, "dialerKeypadJKL": "JKL", "@dialerKeypadJKL": {"description": "Text that shows on the JKL button on the dialer page"}, "dialerKeypadMNO": "MNO", "@dialerKeypadMNO": {"description": "Text that shows on the MNO button on the dialer page"}, "dialerKeypadPQRS": "PQRS", "@dialerKeypadPQRS": {"description": "Text that shows on the PQRS button on the dialer page"}, "dialerKeypadTUV": "TUV", "@dialerKeypadTUV": {"description": "Text that shows on the TUV button on the dialer page"}, "dialerKeypadWXYZ": "WXYZ", "@dialerKeypadWXYZ": {"description": "Text that shows on the WXYZ button on the dialer page"}, "dialerMuteStatusMuted": "<PERSON> kashe sauti", "@dialerMuteStatusMuted": {"description": "Text that shows when the dialer is muted"}, "dialerMuteStatusUnmuted": "An kunna sauti", "@dialerMuteStatusUnmuted": {"description": "Text that shows when the dialer is unmuted"}, "dialerPasteActionText": "liƙa", "@dialerPasteActionText": {"description": "Text for the paste action on the dialer page"}, "dialerSearchResultsHeader": "<PERSON><PERSON><PERSON><PERSON>", "@dialerSearchResultsHeader": {"description": "Header text for the search results on the dialer page"}, "dialerSearchResultsNoResults": "<PERSON> saka<PERSON>ko da''aka samu ", "@dialerSearchResultsNoResults": {"description": "Text that shows when no results are found for the search input field on the dialer page"}, "dialerStatusConnected": "\n<PERSON> ya hadu", "@dialerStatusConnected": {"description": "Text that shows when the dialer status is connected"}, "dialerStatusConnecting": "<PERSON> k<PERSON>\n", "@dialerStatusConnecting": {"description": "Text that shows when the dialer status is connecting"}, "dialerStatusEnded": "An gama kiran", "@dialerStatusEnded": {"description": "Text that shows when the dialer status is ended"}, "dialerStatusError": "Kusk<PERSON> Ya Faru", "@dialerStatusError": {"description": "Text that shows when an error occurs in the dialer"}, "dialerStatusFailedToConnect": "<PERSON> bai hadu ba", "@dialerStatusFailedToConnect": {"description": "Text that shows when the dialer fails to connect"}, "dialerStatusHold": "\nAn dakatar", "@dialerStatusHold": {"description": "Text that shows when the dialer status is on hold"}, "dialerStatusInitial": "<PERSON> k<PERSON>", "@dialerStatusInitial": {"description": "Text that shows when the dialer status is initial"}, "dialerStatusInitiating": "<PERSON> k<PERSON>", "@dialerStatusInitiating": {"description": "Text that shows when the dialer status is initiating"}, "dialerStatusRinging": "\n<PERSON>", "@dialerStatusRinging": {"description": "Text that shows when the dialer status is ringing"}, "dialerStatusUnknown": "Ba a sani ba", "@dialerStatusUnknown": {"description": "Text that shows when the dialer status is unknown"}, "emptyContactList": "<PERSON> jerin la<PERSON> tuntuɓarka", "@emptyContactList": {"description": "Message that shows when the user has no contact list"}, "emptyFavouriteContactList": "<PERSON> jerin la<PERSON> tuntuɓar da kake so", "@emptyFavouriteContactList": {"description": "Message that shows when the user has no favourite contact list"}, "enterCouponOptionalPlaceholder": "<PERSON><PERSON> da kupon na ragi (<PERSON><PERSON> <PERSON>)", "@enterCouponOptionalPlaceholder": {"description": "የኩፖን ኮድ መጠቀም ስፍራ ጽሑፍ"}, "enterOtpPageAppBarSubtitle": "<PERSON>ki na biyu a cikin uku", "@enterOtpPageAppBarSubtitle": {}, "enterOtpPageAppBarTitle": "Farawa", "@enterOtpPageAppBarTitle": {}, "enterOtpPageEditButton": ".<PERSON><PERSON> ba daidai ba", "@enterOtpPageEditButton": {"description": "Text that shows on the edit phone number button"}, "enterOtpPageErrorMessage": "Code din ba daidai ba", "@enterOtpPageErrorMessage": {"description": "Error message that shows when the user enters an incorrect OTP"}, "enterOtpPagePhoneLabel": "<PERSON><PERSON> da lambar tabbatarwa", "@enterOtpPagePhoneLabel": {}, "enterOtpPagePhoneLabelDescription": "An aika lamba mai lambobi hudu zuwa lambar WhatsApp ɗinka {phoneNumber}", "@enterOtpPagePhoneLabelDescription": {"description": "Description text for the OTP input field on the OTP entry page", "placeholders": {"phoneNumber": {"type": "String", "example": "+1234567890"}}}, "enterOtpPageResendOtpButton": "Sake tura code", "@enterOtpPageResendOtpButton": {"description": "Text that shows on the confirm verificatio button"}, "enterOtpPageResendOtpRetries": "{count, plural, zero{<PERSON> gwajin da ya rage} one{<PERSON><PERSON><PERSON> ne karshe d a ye rage} other{{count} Ƙarin gwaje-gwaje}}", "@enterOtpPageResendOtpRetries": {"description": "Message showing the number of retries a user has left", "placeholders": {"count": {"type": "int", "example": "3"}}}, "enterOtpPageResendOtpRetriesError": "Ba za ka iya samun lambar tantancewa a wannan lambar ta WhatsApp ba nan da awa ashirin da hudu masu zuwa", "@enterOtpPageResendOtpRetriesError": {"description": "Error message that shows when the user has exhausted the number of retries"}, "enterOtpPageSubmitButton": "Tabbatar da OTP", "@enterOtpPageSubmitButton": {"description": "Text that shows on the confirm verificatio button"}, "enterVoucherCodeLabel": "<PERSON><PERSON> da lambar kati\n\n", "@enterVoucherCodeLabel": {"description": "Label for voucher code input field"}, "errorLoadingRadioStationsText": "<PERSON><PERSON><PERSON> yayi sanadiyyar samar da tashar rediyo", "@errorLoadingRadioStationsText": {"description": "Message shown when radio stations fail to load"}, "failedToLoadLeaderboard": "An kasa loda jerin masu nasara", "@failedToLoadLeaderboard": {"description": "Error message displayed when the referral leaderboard cannot be loaded"}, "free": "\n<PERSON><PERSON><PERSON>", "@free": {"description": "Text shown for free promotional content"}, "freeCreditAppbarTitle": "<PERSON><PERSON>", "@freeCreditAppbarTitle": {}, "freeCreditPageContent_1": "<PERSON><PERSON> lambar  gayyatar ka da abokanka da iyalanka", "@freeCreditPageContent_1": {}, "freeCreditPageContent_2": "\n<PERSON> sauke <PERSON>alk kuma suyi rijista tare da lambar gayyatar ka ", "@freeCreditPageContent_2": {}, "freeCreditPageContent_3": "Za ka samu {amount} lokacin da wanda ka gayyata ya sayi katin farko", "@freeCreditPageContent_3": {}, "freeCreditPageHeadingText": "{amount} <PERSON><PERSON><PERSON><PERSON> abo<PERSON>ka ka samu  ", "@freeCreditPageHeadingText": {}, "freeCreditPageShareReferralLinkButtonText": "Gayyatar abokai zuwa FroggyTalk", "@freeCreditPageShareReferralLinkButtonText": {}, "freeCreditPageSubHeadingText": "Abokan da na gayyata ({amount})", "@freeCreditPageSubHeadingText": {}, "froggytalkCustomerLabel": "Abokin ciniki na Froggytalk", "@froggytalkCustomerLabel": {"description": "Default label shown when user name is not available"}, "froggyTalkLovesYou": "<PERSON><PERSON><PERSON><PERSON> na son ku.", "@froggyTalkLovesYou": {"description": "Affirmative message showing app's appreciation for users"}, "getNow": "<PERSON>a samu yanzu", "@getNow": {"description": "Text for a button or link to get an offer or promotion"}, "helpCenterAppbarTitle": "<PERSON><PERSON><PERSON>", "@helpCenterAppbarTitle": {}, "helpCenterPageMenu_1": "Yi Korafi ko <PERSON> Shawara", "@helpCenterPageMenu_1": {}, "helpCenterPageMenu_2": "<PERSON> taɗi tare da wakilin kulawa", "@helpCenterPageMenu_2": {}, "helpCenterPageMenu_3": "Tambayoyin da ake yawan yi", "@helpCenterPageMenu_3": {}, "helpCenterPageMenu_4": "WhatsApp", "@helpCenterPageMenu_4": {"description": "Text that shows on the WhatsApp button on the help center page"}, "homeNavigationBarText": "Gida", "@homeNavigationBarText": {"description": "navbar text for home page"}, "inAppPurchaseLabel": "<PERSON>ya kati a cikin App", "@inAppPurchaseLabel": {"description": "Label indicating this is an in-app purchase from the app store"}, "internetConnectionAlertTextError": "Kash! Ka rasa haɗin yanar gizo", "@internetConnectionAlertTextError": {}, "internetConnectionAlertTextSuccess": "An shirya! An haɗu da Intanet ɗin ka", "@internetConnectionAlertTextSuccess": {}, "invalidInternationalPhoneFormat": "<PERSON><PERSON> ta Ƙasashen Duniya mara inganci\n\n", "@invalidInternationalPhoneFormat": {"description": "Error message shown when phone number format is invalid"}, "invalidPhoneNumber": "Da fatan za a shigar da ingantacciyar lambar waya", "@invalidPhoneNumber": {"description": "Error message shown when phone number is invalid"}, "keypadNavigationBarText": "<PERSON><PERSON><PERSON>", "@keypadNavigationBarText": {"description": "navbar text for keypad page"}, "landlineText": "<PERSON><PERSON> gida", "@landlineText": {"description": "Text that shows when the number is a landline number"}, "languageChangeError": "Failed to change language. Please try again.", "@languageChangeError": {"description": "Error message shown when language change fails"}, "languageSelectionDisclaimer": "Za ka iya zabi wani yaren a ", "@languageSelectionDisclaimer": {"description": "Message informing users where they can find language settings"}, "languageSetupPageAppBarSubtitle": "Mataki na uku a cikin uku", "@languageSetupPageAppBarSubtitle": {"description": "Subtitle of the profile setup page showing progress"}, "loadingRadioStationsText": "Ana samar da tashar rediyo...", "@loadingRadioStationsText": {"description": "Message shown while radio stations are being loaded"}, "loadVoucherCardButtonText": "<PERSON>da kati", "@loadVoucherCardButtonText": {}, "loadVoucherCardErrorText": "lambar ba dai dai ba, sake gwadawa", "@loadVoucherCardErrorText": {}, "loadVoucherCardLabelText": "<PERSON><PERSON> da lambar kati mai lamba goma", "@loadVoucherCardLabelText": {}, "loadVoucherCardTitle": "Sa kati", "@loadVoucherCardTitle": {}, "loadVoucherCodeTitle": "Loda Lambar kati", "@loadVoucherCodeTitle": {"description": "Title shown on the load voucher code dialog"}, "loginPageAppBarSubtitle": "<PERSON>ki na daya a cikin uku", "@loginPageAppBarSubtitle": {"description": "Subtitle of the login page"}, "loginPageAppBarTitle": "Farawa", "@loginPageAppBarTitle": {"description": "Title of the login page"}, "loginPageCheckboxLabel": "An gayyace ni da wani lamba", "@loginPageCheckboxLabel": {}, "loginPageErrorMessage": "Lambar nan ba ta kan WhatsApp", "@loginPageErrorMessage": {}, "loginPagePhoneLabel": "<PERSON><PERSON> da lambar ka ta WhatsApp", "@loginPagePhoneLabel": {}, "loginPagePhoneLabelDescription": "Za a tura ma OTP ta WhatsApp", "@loginPagePhoneLabelDescription": {}, "loginPagePhoneNumberError": "<PERSON><PERSON> way<PERSON>a ba ta cika ba, do patan za ka cika ta", "@loginPagePhoneNumberError": {}, "loginPageReferralLabel": "<PERSON><PERSON> da lambar gayyatar a<PERSON>", "@loginPageReferralLabel": {}, "loginPageSubmitButton": "<PERSON><PERSON> tabba<PERSON>wa", "@loginPageSubmitButton": {}, "minsOrMin": "{count, plural, zero{} one{1 minti} other{{count} mintuna}}", "@minsOrMin": {"description": "Text that shows either '1 min' or '{count} mins' based on the count", "placeholders": {"count": {"type": "int", "example": "5"}}}, "mobileText": "<PERSON><PERSON> salula", "@mobileText": {"description": "Text that shows when the number is a mobile number"}, "monthlyReferralBoard": "<PERSON><PERSON> jago<PERSON>ar talla a watar {month} ", "@monthlyReferralBoard": {"description": "Title for the monthly promotion leaderboard showing top referrers", "placeholders": {"month": {"type": "String", "example": "Jan'25"}}}, "moreAppbarTitle": "<PERSON><PERSON><PERSON>", "@moreAppbarTitle": {}, "moreNavigationBarText": "Ƙarin abubuwa", "@moreNavigationBarText": {"description": "navbar text for more page"}, "morePageAccountBalanceCardTitle": "<PERSON><PERSON>'auni", "@morePageAccountBalanceCardTitle": {}, "morePageAppVersionMenuText": "<PERSON><PERSON> manhaja", "@morePageAppVersionMenuText": {}, "morePageCallRatesMenuText": "<PERSON><PERSON><PERSON> kira", "@morePageCallRatesMenuText": {}, "morePageHelpCenterMenuText": "<PERSON><PERSON><PERSON>", "@morePageHelpCenterMenuText": {}, "morePageLanguageMenuText": "<PERSON><PERSON>", "@morePageLanguageMenuText": {}, "morePageLoadVoucherMenuText": "Saka kati", "@morePageLoadVoucherMenuText": {}, "morePageLogoutMenuText": "Fita", "@morePageLogoutMenuText": {}, "morePageProfileMenuText": "Bayanan <PERSON>", "@morePageProfileMenuText": {}, "morePageRadioMenuText": "Rediyo", "@morePageRadioMenuText": {}, "morePageReferralCodeCardButtonText": "<PERSON>", "@morePageReferralCodeCardButtonText": {}, "morePageReferralCodeCardContent": "   {numberOfPeople}\nmutane sun yi amfani da lambar gayyatar wa", "@morePageReferralCodeCardContent": {}, "morePageReferralCodeCardDescription": "Samu {amount} in ka gayyace Iyali da abokai zuwa FroggyTalk", "@morePageReferralCodeCardDescription": {}, "morePageReferralCodeCardTitle": "Lambar gayyatar abokai", "@morePageReferralCodeCardTitle": {}, "nextTrack": "<PERSON> waƙa", "@nextTrack": {"description": "Text shown on button to play the next radio track or station"}, "noBuyCreditOptionsAvailable": "<PERSON> zaɓuɓɓukan siyan kati da ake da su", "@noBuyCreditOptionsAvailable": {"description": "Message that shows when there are no buy credit options available"}, "noFavoriteContactsMessage": "<PERSON>", "@noFavoriteContactsMessage": {"description": "Message shown when there are no favorite contacts to display"}, "noLanguagesAvailable": "No languages available", "@noLanguagesAvailable": {"description": "Message shown when no languages are available to select"}, "noNotificationsYet": "<PERSON><PERSON> tuku<PERSON>", "@noNotificationsYet": {"description": "Text shown when there are no notifications"}, "noRadioStationsAvailableText": "<PERSON> tashar rediyo da ke samuwa", "@noRadioStationsAvailableText": {"description": "Message shown when there are no radio stations to display"}, "notEnoughCreditMessage": "<PERSON>a ka da is<PERSON>hen k<PERSON>it", "@notEnoughCreditMessage": {"description": "Message shown when user has insufficient credit for a call"}, "notification": "Sanarwa", "@notification": {"description": "Text shown for a single notification"}, "notificationsAppBarTitle": "Sanarwa", "@notificationsAppBarTitle": {"description": "Title of the notifications page"}, "notificationsEmptyMessage": "Ba ka da sanarwa", "@notificationsEmptyMessage": {"description": "Message that shows when the user has no notifications"}, "notificationSettingsAppBarTitle": "<PERSON><PERSON><PERSON>", "@notificationSettingsAppBarTitle": {"description": "Title of the notification settings page"}, "notificationSettingsAutoDeleteNotificationText": "Goge sanarwar ta atomatik", "@notificationSettingsAutoDeleteNotificationText": {"description": "Text that shows to toggle the auto delete notification on the notification settings page"}, "notificationSettingsNotificationSoundText": "<PERSON><PERSON><PERSON> sa<PERSON>", "@notificationSettingsNotificationSoundText": {"description": "Text that shows to toggle the notification sound on the notification settings page"}, "notificationsTabAllText": "<PERSON><PERSON>", "@notificationsTabAllText": {"description": "Text that shows on the all tab on the notifications page"}, "notificationsTabUnreadText": "Ba a karanta ba", "@notificationsTabUnreadText": {"description": "Text that shows on the unread tab on the notifications page"}, "notificationsTimeAgo": "{time} {time, plural, one{{duration}} other{{duration}s}} a baya", "@notificationsTimeAgo": {"description": "Text that shows the time since the notification was received", "placeholders": {"time": {"type": "int", "example": "2"}, "duration": {"type": "String", "example": "hours"}}}, "nowPlaying": "<PERSON>", "@nowPlaying": {"description": "Text shown to indicate the currently playing radio track or station"}, "okButtonText": "\nToh", "@okButtonText": {"description": "Text shown on OK confirmation button"}, "onboardingPageFooterAndText": "da", "@onboardingPageFooterAndText": {"description": "Text that shows between the terms and conditions link and the privacy policy link on the onboarding page"}, "onboardingPageFooterPrivacy": "<PERSON><PERSON><PERSON>", "@onboardingPageFooterPrivacy": {"description": "Text that shows below the submit button on the onboarding page"}, "onboardingPageFooterTermsConditionsText": "<PERSON><PERSON>'ido<PERSON> amfani da FroggyTalk", "@onboardingPageFooterTermsConditionsText": {"description": "Text that shows below the submit button on the onboarding page"}, "onboardingPageFooterText": "In ka fara ka yadda da", "@onboardingPageFooterText": {"description": "Text that shows below the submit button on the onboarding page"}, "onboardingPageSliderHeader1": "Harsuna daban-daban", "@onboardingPageSliderHeader1": {"description": "Header text for the first slide on the onboarding page"}, "onboardingPageSliderHeader2": "Yi kira zuwa kowane wayar hannu ko layin ƙasa a ko''ina cikin duniya", "@onboardingPageSliderHeader2": {"description": "Header text for the second slide on the onboarding page"}, "onboardingPageSliderHeader3": "<PERSON><PERSON> da sauki", "@onboardingPageSliderHeader3": {"description": "Header text for the third slide on the onboarding page"}, "onboardingPageSliderText1": "<PERSON><PERSON><PERSON><PERSON> yana ba ka damar zaɓar harshen da ka fi so, mi<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> da saura<PERSON>u", "@onboardingPageSliderText1": {"description": "Text for the first slide on the onboarding page"}, "onboardingPageSliderText2": "Yi kira zuwa kowane wayar hannu ko layin ƙasa a ko''ina cikin duniya.\nMai karɓa ba ya buƙatar wayar salula ko haɗin intanet", "@onboardingPageSliderText2": {"description": "Text for the second slide on the onboarding page"}, "onboardingPageSliderText3": "<PERSON><PERSON> k<PERSON>it na kira a cikin kudin kasarka tare da hanyar biyan da ka fi so.", "@onboardingPageSliderText3": {"description": "Text for the third slide on the onboarding page"}, "onboardingPageSubmitButtonText": "Fara", "@onboardingPageSubmitButtonText": {"description": "Text for the submit button on the onboarding page"}, "outOfCreditLabel": "<PERSON> k<PERSON>", "@outOfCreditLabel": {"description": "Label shown when user has no remaining credit"}, "paymentFailureAppBarTitle": "Qasa", "@paymentFailureAppBarTitle": {"description": "Title shown in app bar of payment failure page"}, "paymentFailureHomeButton": "Gida", "@paymentFailureHomeButton": {"description": "Text for button to return to home page"}, "paymentFailureMessage": "Biyan kuɗin ka na {amount} bai yi nasara ba", "@paymentFailureMessage": {"description": "Message shown when payment fails, includes amount", "placeholders": {"amount": {"type": "String", "example": "$50.00"}}}, "paymentFailureTitle": "Biyan Kuɗi Bai <PERSON>", "@paymentFailureTitle": {"description": "Main title shown when payment fails"}, "paymentFailureTryAgainButton": "<PERSON>ke gwa<PERSON>wa", "@paymentFailureTryAgainButton": {"description": "Text for button to retry payment"}, "paymentOptionsAppBarTitle": "<PERSON><PERSON><PERSON><PERSON> biyan kuɗi", "@paymentOptionsAppBarTitle": {"description": "Title of the payment options page"}, "paymentOptionsAutoCreditDescription": "Za mu sanya wa asusunka da {amount} idan ragowar kuɗinka ya yi ƙasa da {minAmount}", "@paymentOptionsAutoCreditDescription": {"description": "Description of the auto credit payment method"}, "paymentOptionsAutoCreditTitle": "<PERSON>ka kati automatik", "@paymentOptionsAutoCreditTitle": {"description": "Title of the auto credit payment method"}, "paymentOptionsSelectPaymentMethod": "Zaɓi hanyar biyan kuɗi da ka fi so", "@paymentOptionsSelectPaymentMethod": {"description": "Text that shows above the payment method selection dropdown"}, "paymentOptionsSummaryAmountToCreditText": "<PERSON><PERSON> da za ka samu a asusun ka", "@paymentOptionsSummaryAmountToCreditText": {"description": "Text that shows the amount to be credited on the payment options page"}, "paymentOptionsSummaryDiscountButtonText": "<PERSON><PERSON><PERSON>", "@paymentOptionsSummaryDiscountButtonText": {"description": "Text for the apply discount button on the payment options page"}, "paymentOptionsSummaryDiscountPlaceholder": "<PERSON><PERSON> da lambar samun ragi", "@paymentOptionsSummaryDiscountPlaceholder": {"description": "Placeholder text for the discount code input field on the payment options page"}, "paymentOptionsSummaryDiscountText": "<PERSON><PERSON>", "@paymentOptionsSummaryDiscountText": {"description": "Text that shows the discount on the payment options page"}, "paymentOptionsSummaryTotalPaymentText": "Gabadayan kuɗin da za ka biya", "@paymentOptionsSummaryTotalPaymentText": {"description": "Text that shows the total payment on the payment options page"}, "paymentOptionsSummaryVatFeesText": "<PERSON><PERSON>", "@paymentOptionsSummaryVatFeesText": {"description": "Text that shows the VAT and fees on the payment options page"}, "paymentSummaryAmountToCreditLabel": "<PERSON><PERSON> k<PERSON> da zai shiga ", "@paymentSummaryAmountToCreditLabel": {"description": "በክፍያ ማጠቃለያ ውስጥ የሚቀርብ መጠን ምልክት"}, "paymentSummaryDiscountLabel": "<PERSON><PERSON>", "@paymentSummaryDiscountLabel": {"description": "በክፍያ ማጠቃለያ ውስጥ ቅናሽ ምልክት"}, "paymentSummaryPlayStoreFeeLabel": "Laadar {percentage}% <PERSON> <PERSON><PERSON>+", "@paymentSummaryPlayStoreFeeLabel": {"description": "Label showing the Play Store fee percentage and VAT in payment summary", "placeholders": {"percentage": {"type": "String", "example": "30"}}}, "paymentSummaryTotalLabel": "Gabadaya abinda za''a biya", "@paymentSummaryTotalLabel": {"description": "በክፍያ ማጠቃለያ ውስጥ ጠቅላላ ክፍያ ምልክት"}, "paymentSummaryVatFeesLabel": "Haraji + <PERSON>din <PERSON>", "@paymentSummaryVatFeesLabel": {"description": "በክፍያ ማጠቃለያ ውስጥ ተጨማሪ ክፍያዎች ምልክት"}, "payWithApplePay": "Apple Pay", "@payWithApplePay": {"description": "Text shown on button to pay using Apple Pay"}, "payWithGooglePay": "Google Pay", "@payWithGooglePay": {"description": "Text shown on button to pay using Google Pay"}, "perMinRate": "{rate}/mintuna", "@perMinRate": {"description": "Rate per minute for a call", "placeholders": {"rate": {"type": "String", "example": "$10"}}}, "perMinRateSingle": "minti/\n", "@perMinRateSingle": {"description": "Rate per minute for a call in short form"}, "perMinuteRate": "{rate}/mintuna", "@perMinuteRate": {"description": "Rate per minute for a call", "placeholders": {"rate": {"type": "String", "example": "$10"}}}, "perMinuteSlashLabel": "{min}/mintuna", "@perMinuteSlashLabel": {"description": "Label showing '/minutes' for call rate displays", "placeholders": {"min": {"type": "String", "example": "5"}}}, "permissionButtonAccept": "<PERSON>a damar shiga", "@permissionButtonAccept": {"description": "Text for the accept button on the permission request dialog"}, "permissionButtonSkip": "<PERSON><PERSON><PERSON><PERSON>", "@permissionButtonSkip": {"description": "Text for the skip button on the permission request dialog"}, "permissionDeniedMessage": "<PERSON> ki <PERSON>, <PERSON> fatan za a kunna shi a cikin saitunan manhaja", "@permissionDeniedMessage": {"description": "Message shown when a permission is denied by the user"}, "permissionForCameraTitle": "<PERSON><PERSON> bukatar izini don daukar hotuna da bidiyo", "@permissionForCameraTitle": {"description": "Title for the camera permission request dialog"}, "permissionForContactListTitle": "Bawa FroggyTalk damar shiga bayanar tuntubar ka", "@permissionForContactListTitle": {"description": "Title for the contact list permission request dialog"}, "permissionForMicrophoneTitle": "Bawa FroggyTalk damar amfani da makirufo naka don sarrafa kiran", "@permissionForMicrophoneTitle": {"description": "Title for the microphone permission request dialog"}, "permissionForNotificationTitle": "Bawa FroggyTalk damar tura ma sako", "@permissionForNotificationTitle": {"description": "Title for the notification permission request dialog"}, "permissionForStorageTitle": "<PERSON><PERSON> bukatar izini don adana bayanan wucin gadi a kan na''urarka", "@permissionForStorageTitle": {"description": "Title for the storage permission request dialog"}, "permissionPermanentlyDeniedMessage": "<PERSON><PERSON><PERSON> an hana shi <PERSON>, <PERSON> fatan za a kunna shi a cikin saitunan app", "@permissionPermanentlyDeniedMessage": {"description": "Message shown when a permission is permanently denied by the user"}, "perM_Rate": "{rate}/mintuna", "@perM_Rate": {"description": "Rate per minute for a call", "placeholders": {"rate": {"type": "String", "example": "$10"}}}, "phoneNumberStartRule": "Da fatan za a fara shigar da lambar da + ko 00", "@phoneNumberStartRule": {"description": "Error message shown when phone number doesn't start with + or 00"}, "playRadio": "<PERSON><PERSON> rediyo", "@playRadio": {"description": "Text shown on button to start playing the radio"}, "previousTrack": "Waƙar da ta wuce", "@previousTrack": {"description": "Text shown on button to play the previous radio track or station"}, "proceedToPaymentButtonText": "Cigaba", "@proceedToPaymentButtonText": {"description": "Text for the proceed to payment button on the buy credit page"}, "profileAppbarTitle": "<PERSON><PERSON><PERSON> kai", "@profileAppbarTitle": {}, "profileDeleteAccountButtonText": "Share as<PERSON>un ka", "@profileDeleteAccountButtonText": {}, "profileDeleteAccountCardContent": "Da fatan za ka tabbatar da cewa kana son a share asusun ka. Za ka rasa duk wani kudi da ke cikin asusun ka", "@profileDeleteAccountCardContent": {}, "profileDeleteAccountCardTitle": "Share as<PERSON>un ka", "@profileDeleteAccountCardTitle": {}, "profileLabel": "<PERSON><PERSON><PERSON>\n", "@profileLabel": {"description": "Label used for profile sections"}, "profileLabelChangeLocation": "<PERSON><PERSON> kasar da kake", "@profileLabelChangeLocation": {}, "profileLabelEmailAddress": "<PERSON><PERSON> da ad<PERSON>hin imel", "@profileLabelEmailAddress": {}, "profileLabelFullName": "<PERSON><PERSON> da cikakken suna", "@profileLabelFullName": {"description": "profile<PERSON>abelFull<PERSON>ame"}, "profileUpdatedSuccessfullyMessage": "An sabunta bayanan ka cikin nasara", "@profileUpdatedSuccessfullyMessage": {"description": "Message shown when profile is updated successfully"}, "profileUpdateFailedMessage": "\n<PERSON><PERSON><PERSON> bayanan ka bai yi nasara ba ", "@profileUpdateFailedMessage": {"description": "Message shown when profile update fails"}, "quickAdvertSubtitle": "<PERSON>ya kati wa yan uwa ko ina a duniya", "@quickAdvertSubtitle": {"description": "Subtitle for the quick advert on the home page"}, "quickAdvertTitle": "<PERSON>ra kati zuwa kasashe fiye da dari da arba''in da uku", "@quickAdvertTitle": {"description": "Title for the quick advert on the home page"}, "radioAppBarTitle": "Rediyo", "@radioAppBarTitle": {}, "radioComingSoonText": "<PERSON>iyo na zuwa nan ba da jimawa ba !!!", "@radioComingSoonText": {"description": "Text shown when radio feature is not yet available"}, "radioPageChooseChannelButtonText": "Zaɓi <PERSON><PERSON>", "@radioPageChooseChannelButtonText": {}, "radioPageNoChannelsText": "<PERSON> tasha", "@radioPageNoChannelsText": {}, "radioPageSearchPlaceholder": "Zaɓi <PERSON><PERSON>", "@radioPageSearchPlaceholder": {}, "ratesNavigationBarText": "<PERSON><PERSON> kira", "@ratesNavigationBarText": {"description": "navbar text for rates page"}, "recentCallsText": "<PERSON>", "@recentCallsText": {"description": "Text that shows above the recent calls list on the home page"}, "rechargeButtonText": "<PERSON><PERSON><PERSON> kati", "@rechargeButtonText": {"description": "Text shown on recharge button"}, "referAndEarn": "<PERSON><PERSON><PERSON> wani ka samu kyauta", "@referAndEarn": {"description": "Text shown for refer and earn promotional text"}, "referralCardButtonText": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "@referralCardButtonText": {"description": "Text for the referral card button"}, "referralCardDescription1": "<PERSON><PERSON><PERSON> wani kuma ka sami {percentageAmount} kyauta.", "@referralCardDescription1": {"description": "Message showing the referral benefit", "placeholders": {"percentageAmount": {"type": "String", "example": "10%"}}}, "referralCardDescription2": "{count, plural, zero{<PERSON> wanda ya yi amfani da lambar gayyatar ka.} one{<PERSON><PERSON> daya ya yi amfani da lambar gayyatar ka.} other{{numberOfPeople} sun yi amfani da lambar gayyatar ka.}}", "@referralCardDescription2": {"description": "Message showing how many people have used the referral code, with pluralization.", "placeholders": {"numberOfPeople": {"type": "int", "example": "2"}}}, "referralCardTitle": "Lambar gayyatar abokai", "@referralCardTitle": {"description": "Title for the referral card"}, "referralCodeOnCopyActionResponse": "FroggyTalk ta liƙa zuwa allo", "@referralCodeOnCopyActionResponse": {}, "refreshText": "Sabunta", "@refreshText": {"description": "Text shown on button to refresh or reload content"}, "restorePurchases": "<PERSON><PERSON> Purchases", "@restorePurchases": {"description": "Button text for restoring previous in-app purchases"}, "retry": "<PERSON>ke gwa<PERSON>wa", "@retry": {"description": "Text for button to retry a failed operation"}, "saveAndProceedToPaymentButtonText": "A<PERSON><PERSON> ka ci gaba zuwa biyan kudi\n", "@saveAndProceedToPaymentButtonText": {"description": "Text shown on button to save profile changes and proceed to payment"}, "saveChangesButtonText": "<PERSON><PERSON><PERSON> canje-canje da aka yi", "@saveChangesButtonText": {}, "savedSuccessfullyMessage": "An ajiye cikin nasara", "@savedSuccessfullyMessage": {"description": "Message shown when changes are saved successfully"}, "searchCountryPlaceholder": "<PERSON><PERSON><PERSON> kasa", "@searchCountryPlaceholder": {"description": "Placeholder text for the search input field on the country selection page"}, "searchFavouriteContactMessage": "<PERSON>emo lambobin da ka fi so", "@searchFavouriteContactMessage": {"description": "Placeholder text for searching favorite contacts"}, "searchForCountryNoResults": "Ba a sami sakamako ba", "@searchForCountryNoResults": {"description": "Text that shows when no results are found for the search input field on the country selection page"}, "searchForCountryPlaceholder": "<PERSON><PERSON><PERSON> sunan kasa", "@searchForCountryPlaceholder": {"description": "Placeholder text for the search input field on the country selection page"}, "searchRecentCallsMessage": "<PERSON><PERSON><PERSON> kiran da aka yi kwanan nan", "@searchRecentCallsMessage": {"description": "Placeholder text for searching recent calls"}, "secsOrSec": "{count, plural, zero{} one{1 sakan} other{{count} sakanni}}", "@secsOrSec": {"description": "Text that shows either '1 sec' or '{count} secs' based on the count", "placeholders": {"count": {"type": "int", "example": "10"}}}, "securedByStripe": "An Karɓa ta Stripe", "@securedByStripe": {"description": "Text showing that payment is secured by Stripe payment processor"}, "selectCountryPlaceholder": "<PERSON><PERSON> ƙasa", "@selectCountryPlaceholder": {"description": "Placeholder text for the country selection dropdown"}, "selectPreferredLanguage": "Select your preferred language", "@selectPreferredLanguage": {"description": "Subtitle for the language selection modal"}, "sendCreditButtonText": "<PERSON><PERSON> kati", "@sendCreditButtonText": {"description": "Text for the send credit button on the home page"}, "settings": "<PERSON><PERSON><PERSON>", "@settings": {"description": "Text shown for settings"}, "shareReferralText": "<PERSON><PERSON>alk App {appLink} kuma yi rijista tare da lambar gayyata nan {referralCode}. <PERSON><PERSON> da mafi kyawun farashi da ƙwarewar kira.", "@shareReferralText": {"description": "Text shown when sharing referral code", "placeholders": {"appLink": {"type": "String", "example": "https://link-to.app/AHA423kdwG"}, "referralCode": {"type": "String", "example": "ABC123"}}}, "somethingWentWrongMessage": "<PERSON><PERSON> abu ya faru ba daidai ba. Da fatan za a sake gwadawa.", "@somethingWentWrongMessage": {"description": "Message that shows when an error occurs"}, "stopRadio": "<PERSON><PERSON><PERSON> da red<PERSON>", "@stopRadio": {"description": "Text shown on button to stop the currently playing radio"}, "storePrice": "<PERSON><PERSON>", "@storePrice": {"description": "Label for the price listed in the app store"}, "termsAndConditionsApply": "Za a yi amfani da sharuɗɗa da sharuɗɗa", "@termsAndConditionsApply": {"description": "Text indicating that terms and conditions apply to an offer or promotion"}, "timeLeftModalMessage": "<PERSON><PERSON> rana {days}a sami wanda zai ci kudin. <PERSON><PERSON><PERSON> mutane{count} ka ci kudin", "@timeLeftModalMessage": {"description": "Message showing days left and number of referrals needed", "placeholders": {"days": {"type": "String", "example": "25"}, "count": {"type": "int", "example": "30"}}}, "timeLeftModalTitle": " {days} <PERSON><PERSON> rana", "@timeLeftModalTitle": {"description": "Title showing the number of days left in the modal", "placeholders": {"days": {"type": "String", "example": "2"}}}, "tryAgainText": "<PERSON>ke gwa<PERSON>wa", "@tryAgainText": {"description": "Text shown on button to retry an action that failed"}, "unknownErrorText": "<PERSON><PERSON> kuskure ya faru", "@unknownErrorText": {"description": "Generic message shown when an unspecified error occurs"}, "unread": "Ba a karanta ba", "@unread": {"description": "Text shown for unread notifications"}, "updateLanguagePrompt": "Da fatan za a danna maballin da ke ƙasa don sabunta yaren ku", "@updateLanguagePrompt": {"description": "Instruction text prompting user to update their language setting"}, "updateProfileButtonText": "Sabunta bayanan martaba", "@updateProfileButtonText": {"description": "Text shown on button to update user profile"}, "updateYourLanguage": "Sabunta <PERSON>.", "@updateYourLanguage": {"description": "Button or action text for language update functionality"}, "upgradeDialogButtonNo": "Ba Yanzu Ba", "@upgradeDialogButtonNo": {"description": "እትም ማስተካከልን ለመሰረዝ የሚጠቀምበት አዝራር ጽሑፍ"}, "upgradeDialogButtonYes": "Sabunta", "@upgradeDialogButtonYes": {"description": "እትም ማስተካከልን ለመቀጠል የሚጠቀምበት አዝራር ጽሑፍ"}, "upgradeDialogMessage": "Sabon sigar FroggyTalk tana samuwa\n?Kana so ka sabunta yanzu", "@upgradeDialogMessage": {"description": "አዳዲስ እትም ሲኖር ተጠቃሚውን ለመዘመን የሚጠይቅ መልእክት"}, "upgradeDialogTitle": "<PERSON><PERSON><PERSON> tana samuwa", "@upgradeDialogTitle": {"description": "አዳዲስ እትም ሲኖር የሚታይ እትም ርዕስ"}, "validationCouponCodeIncomplete": "lambar rangwame dake kan takardar ba ta cika ba ko kuma ba ta aiki", "@validationCouponCodeIncomplete": {"description": "Error message shown when coupon code length or format is invalid"}, "validationCouponInvalid": "Da fatan za a shigar da ingantaccen lamba", "@validationCouponInvalid": {"description": "Error message shown when coupon code is invalid"}, "validationEmailInvalid": "<PERSON><PERSON><PERSON> imel mara inganci.", "@validationEmailInvalid": {"description": "Error message shown when email format is invalid"}, "validationFieldIsRequired": "  <PERSON> b<PERSON> {fieldName} ", "@validationFieldIsRequired": {"description": "Generic required field error message", "placeholders": {"fieldName": {"type": "String", "example": "Email"}}}, "validationLengthError": "{fieldName} ya kamata ya kasance tsakanin haruffa {minLength} zuwa {maxLength}", "@validationLengthError": {"description": "Error message for fields that don't meet length requirements", "placeholders": {"fieldName": {"type": "String", "example": "Password"}, "minLength": {"type": "int", "example": "8"}, "maxLength": {"type": "int", "example": "16"}}}, "validationMinLengthError": "{fieldName} dole ne ya zama aƙalla tsawon haruffa {minLength}", "@validationMinLengthError": {"description": "Error message for fields that don't meet minimum length requirement", "placeholders": {"fieldName": {"type": "String", "example": "Password"}, "minLength": {"type": "int", "example": "8"}}}, "validationPhoneNumberIncomplete": "<PERSON><PERSON> waya ba ta cika ba.", "@validationPhoneNumberIncomplete": {"description": "Error message shown when phone number is incomplete"}, "viewButtonText": "<PERSON><PERSON>", "@viewButtonText": {"description": "Text for the view action button on page"}, "viewContactDetailAppBarTitle": "Bayanan tuntuɓar juna", "@viewContactDetailAppBarTitle": {"description": "Title of the contact detail page"}, "voucherLoadedSuccessMessage": "An loda kati cikin nasara", "@voucherLoadedSuccessMessage": {"description": "Message shown when voucher is successfully loaded"}, "welcomeBackText": "<PERSON><PERSON>, {firstName}", "@welcomeBackText": {"description": "Greeting message shown to returning users", "placeholders": {"firstName": {"type": "String", "example": "<PERSON>"}}}, "googlePay": "Google Pay", "@googlePay": {"description": "Text shown on button to pay using Google Pay"}, "appStoreCost": "+ {value} <PERSON><PERSON> AppStore", "@appStoreCost": {"description": "Label showing the cost charged by the App Store including fees and VAT", "placeholders": {"value": {"type": "String", "example": "USD 2.99"}}}}