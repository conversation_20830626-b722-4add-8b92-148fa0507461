{"@@locale": "ar", "acceptButtonText": "قبول", "@acceptButtonText": {"description": "Text shown on buttons that accept or confirm an action"}, "accountBalanceCardTitle": "الرصيد", "@accountBalanceCardTitle": {"description": "Title of the account balance page"}, "activateAutoCredit": "تفعيل الشحن التلقائي", "@activateAutoCredit": {"description": "Text for button to enable automatic account crediting"}, "all": "الكل", "@all": {"description": "Text shown for all notifications"}, "allChannels": "جميع القنوات", "@allChannels": {"description": "Text shown to represent all available radio channels or stations"}, "allowAccessButtonText": "السماح بالوصول لإجراء هذا الإجراء المطلوب", "@allowAccessButtonText": {"description": "ተጠቃሚውን የመሳሪያ ባህሪዎች መድረሻ እንዲፈቀድ በመጠየቅ ላይ የሚታይ ጠንቅቆ የተጻፈ ጽሑፍ"}, "allowShareInfoWithRadio": "السماح لنا بمشاركة معلوماتك مع {radioName}", "@allowShareInfoWithRadio": {"description": "Text shown when requesting permission to share user information with a radio station", "placeholders": {"radioName": {"type": "String", "example": "KEXP Radio"}}}, "appBootingTextBelow": "مساعدة المجتمعات على البقاء متصلة", "@appBootingTextBelow": {"description": "Text that shows below the app title on the booting screen"}, "appLanguageAppbarTitle": "لغة التطبيق", "@appLanguageAppbarTitle": {"description": "This is a title for the app bar"}, "apply": "تقديم الطلب", "@apply": {"description": "Text for apply/confirm buttons"}, "appTitle": "<PERSON><PERSON>T<PERSON>", "@appTitle": {"description": "The name of the app"}, "autoAccountCreditHelp": "سيتم خصم المبلغ من بطاقتك وإضافته إلى رصيدك كلما انخفض إلى ما دون", "@autoAccountCreditHelp": {"description": "Explanation text for the auto credit feature"}, "avoidCallDisruption": " لتجنب انقطاع المكالمة أثناء التحدث\n", "@avoidCallDisruption": {"description": "Additional explanation for auto credit feature benefit"}, "buyCreditAmountCustomPlaceholder": "الحد الأدنى {min} - الحد الأقصى {max}\n", "@buyCreditAmountCustomPlaceholder": {"description": "Placeholder text for the custom amount input field on the buy credit page", "placeholders": {"min": {"type": "String", "example": "$50"}, "max": {"type": "String", "example": "$500"}}}, "buyCreditAmountRecommended": "مُوصى به", "@buyCreditAmountRecommended": {"description": "Text that shows above the recommended amount on the buy credit page"}, "buyCreditAppBarTitle": "<PERSON><PERSON><PERSON> الرصيد", "@buyCreditAppBarTitle": {"description": "The app page title for buy credit"}, "buyCreditButtonText": "اشترِ رصيدًا", "@buyCreditButtonText": {"description": "Text for the buy credit button on the account balance page"}, "buyCreditEnterCustomAmountLabel": "أدخل مبلغًا مخصصًا", "@buyCreditEnterCustomAmountLabel": {"description": "Label for the custom amount input field on the buy credit page"}, "buyCreditPageTitle": "المبلغ المفضل", "@buyCreditPageTitle": {"description": "Title of the buy credit page"}, "callButtonText": "اتصال", "@callButtonText": {"description": "Text for the call action button on a page"}, "callingAppBarTitle": "جارٍ الاتصال", "@callingAppBarTitle": {"description": "Title of the calling page"}, "callingPageBluetoothOptionsText": "البلوتوث", "@callingPageBluetoothOptionsText": {"description": "Text that shows to toggle the bluetooth on the calling page"}, "callingPageCreditErrorCardTitle": "رصيد المكالمات غير كافٍ", "@callingPageCreditErrorCardTitle": {}, "callingPagePhoneOptionsText": "الهاتف", "@callingPagePhoneOptionsText": {"description": "Text that shows to toggle the phone on the calling page"}, "callingPageSpeakerOptionsText": "مك<PERSON><PERSON> الصوت", "@callingPageSpeakerOptionsText": {"description": "Text that shows to toggle the speaker on the calling page"}, "callingPageTimeLeft": "{time} متبقي", "@callingPageTimeLeft": {"description": "Text that shows the time left for the call to end", "placeholders": {"time": {"type": "String", "example": "2"}}}, "callLogTypeAnswered": "تم الرد", "@callLogTypeAnswered": {"description": "Text that shows when the call log type is answered"}, "callLogTypeBusy": "مشغول", "@callLogTypeBusy": {"description": "Text that shows when the call log type is busy"}, "callLogTypeCancel": "تم الإلغاء\n", "@callLogTypeCancel": {"description": "Text that shows when the call log type is cancelled"}, "callLogTypeIncoming": "وارد", "@callLogTypeIncoming": {"description": "Text that shows when the call log type is incoming"}, "callLogTypeMissed": "فائت", "@callLogTypeMissed": {"description": "Text that shows when the call log type is missed"}, "callLogTypeOutgoing": "صادر", "@callLogTypeOutgoing": {"description": "Text that shows when the call log type is outgoing"}, "callLogTypeUnavailable": "<PERSON>ير متوفر", "@callLogTypeUnavailable": {"description": "Text that shows when the call log type is unavailable"}, "callRatesAppBarTitle": "أسعار المكالمات", "@callRatesAppBarTitle": {"description": "Title of the call rates page"}, "callTypeAnswered": "مكالمة صادرة", "@callTypeAnswered": {"description": "Text that shows when the call is answered"}, "callTypeBusy": "مشغول", "@callTypeBusy": {"description": "Text that shows when the call is busy"}, "callTypeCancel": "مكالمة تم إلغائها", "@callTypeCancel": {"description": "Text that shows when the call is cancelled"}, "callTypeIncoming": "مكالمة واردة", "@callTypeIncoming": {"description": "Text that shows when the call is incoming"}, "callTypeMissed": "مكالمة فائتة", "@callTypeMissed": {"description": "Text that shows when the call is missed"}, "callTypeOutgoing": "مكالمة صادرة", "@callTypeOutgoing": {"description": "Text that shows when the call is outgoing"}, "callTypeUnavailable": "المستخدم غير متاح", "@callTypeUnavailable": {"description": "Text that shows when the user is unavailable"}, "cancel": " لتجنب انقطاع المكالمة أثناء التحدث\n", "@cancel": {"description": "Text for cancel buttons"}, "cancelButtonText": "إلغاء", "@cancelButtonText": {}, "cashPrizeAmount": "{currencySymbol}{amount} كاش مجاني", "@cashPrizeAmount": {"description": "Text showing the cash prize amount for promotions and referrals", "placeholders": {"currencySymbol": {"type": "String", "example": "€"}, "amount": {"type": "String", "example": "250"}}}, "changeChannelButtonText": "تغيير القناة", "@changeChannelButtonText": {"description": "Text shown on button to change radio channel"}, "channel": "القناة", "@channel": {"description": "Text shown to represent a radio channel or station"}, "chatWithLiveAgentAppbarTitle": "مندو<PERSON> خدمة العملاء متاح", "@chatWithLiveAgentAppbarTitle": {}, "checkBackLaterText": "يرجى التحقق لاحقاً", "@checkBackLaterText": {"description": "Message shown when a feature or content is temporarily unavailable"}, "chooseLanguage": "اختر اللغة\n", "@chooseLanguage": {"description": "Title for the language selection modal"}, "closeButtonText": "إغلاق", "@closeButtonText": {"description": "Text shown on buttons that close views or dialogs"}, "confirm": " تأكيد", "@confirm": {"description": "Text for confirm/verification buttons"}, "confirmationAppBarTitle": "تأكيد", "@confirmationAppBarTitle": {"description": "Title of the confirmation page"}, "confirmationFailedButtonText": "حاول مرة أخرى", "@confirmationFailedButtonText": {"description": "Text for the try again button on the confirmation page"}, "confirmationFailedContactSupportDescription": "إذا كان لديك أي أسئلة أو تحتاج إلى مساعدة، يرجى التواصل مع فريق الدعم الخاص بنا", "@confirmationFailedContactSupportDescription": {"description": "Description that shows when the payment fails"}, "confirmationFailedContactSupportText": "تواصل مع الدعم", "@confirmationFailedContactSupportText": {"description": "Text for the contact support button on the confirmation page"}, "confirmationFailedDescription": "لم تنجح عملية الدفع الخاصة بك. يرجى المحاولة مرة أخرى", "@confirmationFailedDescription": {"description": "Description that shows when the payment fails"}, "confirmationFailedTitle": "فشل الدفع", "@confirmationFailedTitle": {"description": "Title that shows when the payment fails"}, "confirmationSuccessButtonText": "تم", "@confirmationSuccessButtonText": {"description": "Text for the success button on the confirmation page"}, "confirmationSuccessDescription": "تمت إضافة {amount} بنجاح إلى رصيدك", "@confirmationSuccessDescription": {"description": "Description that shows when the payment is successful", "placeholders": {"amount": {"type": "String", "example": "$10"}}}, "confirmationSuccessTitle": "تم الدفع بنجاح", "@confirmationSuccessTitle": {"description": "Title that shows when the payment is successful"}, "contactsAllContactsTabText": "جهات الاتصال", "@contactsAllContactsTabText": {"description": "Text that shows above the contacts list on the home page"}, "contactsCallRateText": "{min} دقيقة {secs} ثانية", "@contactsCallRateText": {"description": "Text that shows the duration of a call", "placeholders": {"min": {"type": "String", "example": "10"}, "secs": {"type": "String", "example": "3"}}}, "contactsFavouriteCallsTabText": "المفضلة", "@contactsFavouriteCallsTabText": {"description": "Text that shows above the Favourite calls list on the home page"}, "contactsNoCallsEmptyMessage": "ليس لديك أي مكالمات حديثة", "@contactsNoCallsEmptyMessage": {"description": "Message that shows when the user has no recent calls"}, "contactsNoContactsButtonText": "عرض جهات الاتصال", "@contactsNoContactsButtonText": {"description": "Text for the view contacts button on the home page"}, "contactsSearchContactsPlaceholder": "ابحث عن اسم جهة الاتصال", "@contactsSearchContactsPlaceholder": {"description": "Placeholder text for the search contact name"}, "contactsSearchForContactsPlaceholder": "ابحث عن اسم جهة الاتصال", "@contactsSearchForContactsPlaceholder": {"description": "Placeholder text for the search input field on the contacts page"}, "couponAppliedButtonText": "تم التطبيق", "@couponAppliedButtonText": {"description": "ኩፖኑ በትክክል ሲተገብር የሚታይ ጽሑፍ"}, "couponApplyButtonText": "تطبيق", "@couponApplyButtonText": {"description": "የኩፖን አተግባለሁ አዝራር ጽሑፍ"}, "credit": "رصيد\n", "@credit": {"description": "Text shown for credit"}, "declineButtonText": "<PERSON><PERSON><PERSON>", "@declineButtonText": {"description": "Text shown on buttons that decline or reject an action"}, "deleteAccountWarning": "الإجراء الذي على وشك اتخاذه لا يمكن التراجع عنه. يرجى تأكيد أنك تقوم بحذف حسابك.", "@deleteAccountWarning": {"description": "Warning message shown when user attempts to delete their account"}, "dialerAppBarTitle": "لوحة الاتصال", "@dialerAppBarTitle": {"description": "Title of the dialer page"}, "dialerCopyActionText": "نسخ", "@dialerCopyActionText": {"description": "Text for the copy action on the dialer page"}, "dialerErrorInvalidNumber": "رقم غير صالح", "@dialerErrorInvalidNumber": {"description": "Error message that shows when the user enters an invalid number"}, "dialerErrorWithoutCountryCode": "أض<PERSON> <PERSON><PERSON><PERSON> البلد (مثل +1)", "@dialerErrorWithoutCountryCode": {"description": "Error message that shows when the user does not add a country code"}, "dialerKeypadABC": "ABC", "@dialerKeypadABC": {"description": "Text that shows on the ABC button on the dialer page"}, "dialerKeypadDEF": "DEF", "@dialerKeypadDEF": {"description": "Text that shows on the DEF button on the dialer page"}, "dialerKeypadGHI": "GHI", "@dialerKeypadGHI": {"description": "Text that shows on the GHI button on the dialer page"}, "dialerKeypadJKL": "JKL", "@dialerKeypadJKL": {"description": "Text that shows on the JKL button on the dialer page"}, "dialerKeypadMNO": "MNO", "@dialerKeypadMNO": {"description": "Text that shows on the MNO button on the dialer page"}, "dialerKeypadPQRS": "PQRS", "@dialerKeypadPQRS": {"description": "Text that shows on the PQRS button on the dialer page"}, "dialerKeypadTUV": "TUV", "@dialerKeypadTUV": {"description": "Text that shows on the TUV button on the dialer page"}, "dialerKeypadWXYZ": "WXYZ", "@dialerKeypadWXYZ": {"description": "Text that shows on the WXYZ button on the dialer page"}, "dialerMuteStatusMuted": "صامت", "@dialerMuteStatusMuted": {"description": "Text that shows when the dialer is muted"}, "dialerMuteStatusUnmuted": "غير صامت", "@dialerMuteStatusUnmuted": {"description": "Text that shows when the dialer is unmuted"}, "dialerPasteActionText": "لصق", "@dialerPasteActionText": {"description": "Text for the paste action on the dialer page"}, "dialerSearchResultsHeader": "النتائج", "@dialerSearchResultsHeader": {"description": "Header text for the search results on the dialer page"}, "dialerSearchResultsNoResults": "لا توجد نتائج", "@dialerSearchResultsNoResults": {"description": "Text that shows when no results are found for the search input field on the dialer page"}, "dialerStatusConnected": "متصل", "@dialerStatusConnected": {"description": "Text that shows when the dialer status is connected"}, "dialerStatusConnecting": "جاري الاتصال", "@dialerStatusConnecting": {"description": "Text that shows when the dialer status is connecting"}, "dialerStatusEnded": "انتهى", "@dialerStatusEnded": {"description": "Text that shows when the dialer status is ended"}, "dialerStatusError": "<PERSON><PERSON><PERSON>", "@dialerStatusError": {"description": "Text that shows when an error occurs in the dialer"}, "dialerStatusFailedToConnect": "فشل الاتصال", "@dialerStatusFailedToConnect": {"description": "Text that shows when the dialer fails to connect"}, "dialerStatusHold": "قيد الانتظار", "@dialerStatusHold": {"description": "Text that shows when the dialer status is on hold"}, "dialerStatusInitial": "جاري الاتصال", "@dialerStatusInitial": {"description": "Text that shows when the dialer status is initial"}, "dialerStatusInitiating": "جاري الاتصال", "@dialerStatusInitiating": {"description": "Text that shows when the dialer status is initiating"}, "dialerStatusRinging": "رنين", "@dialerStatusRinging": {"description": "Text that shows when the dialer status is ringing"}, "dialerStatusUnknown": "غير معروف", "@dialerStatusUnknown": {"description": "Text that shows when the dialer status is unknown"}, "emptyContactList": "ليس لديك قائمة جهات اتصال.", "@emptyContactList": {"description": "Message that shows when the user has no contact list"}, "emptyFavouriteContactList": "ليس لديك قائمة جهات اتصال مفضلة.", "@emptyFavouriteContactList": {"description": "Message that shows when the user has no favourite contact list"}, "enterCouponOptionalPlaceholder": "أدخل القسيمة (اختياري)", "@enterCouponOptionalPlaceholder": {"description": "የኩፖን ኮድ መጠቀም ስፍራ ጽሑፍ"}, "enterOtpPageAppBarSubtitle": "الخطوة 2 من 3", "@enterOtpPageAppBarSubtitle": {}, "enterOtpPageAppBarTitle": "البدء", "@enterOtpPageAppBarTitle": {}, "enterOtpPageEditButton": "هل رقم واتساب خاطئ؟ يرجى التعديل", "@enterOtpPageEditButton": {"description": "Text that shows on the edit phone number button"}, "enterOtpPageErrorMessage": "رمز غير صحيح\n", "@enterOtpPageErrorMessage": {"description": "Error message that shows when the user enters an incorrect OTP"}, "enterOtpPagePhoneLabel": "أدخل رمز التحقق الخاص بك", "@enterOtpPagePhoneLabel": {}, "enterOtpPagePhoneLabelDescription": "تم إرسال رمز مكون من 4 أرقام إلى رقم واتساب الخاص بك {phoneNumber}", "@enterOtpPagePhoneLabelDescription": {"description": "Description text for the OTP input field on the OTP entry page", "placeholders": {"phoneNumber": {"type": "String", "example": "+1234567890"}}}, "enterOtpPageResendOtpButton": "إعادة إرسال الرمز", "@enterOtpPageResendOtpButton": {"description": "Text that shows on the confirm verificatio button"}, "enterOtpPageResendOtpRetries": "{count, plural, zero{لم يتبق أي محاولات} one{محاولة أخيرة واحدة} two{} few{} many{} other{ محاولات متبقية}}", "@enterOtpPageResendOtpRetries": {"description": "Message showing the number of retries a user has left", "placeholders": {"count": {"type": "int", "example": "3"}}}, "enterOtpPageResendOtpRetriesError": "لا يمكنك استقبال رموز التحقق على هذا الرقم من واتساب خلال الـ 24 ساعة القادمة", "@enterOtpPageResendOtpRetriesError": {"description": "Error message that shows when the user has exhausted the number of retries"}, "enterOtpPageSubmitButton": "تأكيد رمز التفعيل", "@enterOtpPageSubmitButton": {"description": "Text that shows on the confirm verificatio button"}, "enterVoucherCodeLabel": "أدخل رمز القسيمة", "@enterVoucherCodeLabel": {"description": "Label for voucher code input field"}, "errorLoadingRadioStationsText": "خطأ في تحميل محطات الراديو", "@errorLoadingRadioStationsText": {"description": "Message shown when radio stations fail to load"}, "failedToLoadLeaderboard": "فشل في تحميل قائمة المتصدرين\n", "@failedToLoadLeaderboard": {"description": "Error message displayed when the referral leaderboard cannot be loaded"}, "free": "ادعُ شخصًا واربح", "@free": {"description": "Text shown for free promotional content"}, "freeCreditAppbarTitle": "ر<PERSON>ي<PERSON> مجاني", "@freeCreditAppbarTitle": {}, "freeCreditPageContent_1": "شارك رمز الدعوة الخاص بك مع أصدقائك وعائلتك.", "@freeCreditPageContent_1": {}, "freeCreditPageContent_2": "يقومون بتحميل تطبيق FroggyTalk والتسجيل باستخدام رمزك.", "@freeCreditPageContent_2": {}, "freeCreditPageContent_3": "تكسب {amount} عندما يشتري الشخص رصيد لأول مرة.", "@freeCreditPageContent_3": {}, "freeCreditPageHeadingText": "ادعُ صديقًا واربح {amount}", "@freeCreditPageHeadingText": {}, "freeCreditPageShareReferralLinkButtonText": "مشاركة رابط الدعوة", "@freeCreditPageShareReferralLinkButtonText": {}, "freeCreditPageSubHeadingText": "دعواتي ({amount})", "@freeCreditPageSubHeadingText": {}, "froggytalkCustomerLabel": "ع<PERSON>يل Froggytalk", "@froggytalkCustomerLabel": {"description": "Default label shown when user name is not available"}, "froggyTalkLovesYou": "فروغي توك يحبك.", "@froggyTalkLovesYou": {"description": "Affirmative message showing app's appreciation for users"}, "getNow": "احصل عليه الآن", "@getNow": {"description": "Text for a button or link to get an offer or promotion"}, "helpCenterAppbarTitle": "مركز المساعدة", "@helpCenterAppbarTitle": {}, "helpCenterPageMenu_1": "تقديم شكوى أو اقتراح", "@helpCenterPageMenu_1": {}, "helpCenterPageMenu_2": "الدردشة مع مندوب خدمة العملاء", "@helpCenterPageMenu_2": {}, "helpCenterPageMenu_3": "الأسئلة الشائعة", "@helpCenterPageMenu_3": {}, "helpCenterPageMenu_4": "WhatsApp", "@helpCenterPageMenu_4": {"description": "Text that shows on the WhatsApp button on the help center page"}, "homeNavigationBarText": "الرئيسية", "@homeNavigationBarText": {"description": "navbar text for home page"}, "inAppPurchaseLabel": "الشراء داخل التطبيق", "@inAppPurchaseLabel": {"description": "Label indicating this is an in-app purchase from the app store"}, "internetConnectionAlertTextError": "عذرًا! يبدو أنك غير متصل.", "@internetConnectionAlertTextError": {}, "internetConnectionAlertTextSuccess": "كل شيء جاهز! أنت متصل مرة أخرى.", "@internetConnectionAlertTextSuccess": {}, "invalidInternationalPhoneFormat": "صيغة رقم الهاتف الدولي غير صالحة", "@invalidInternationalPhoneFormat": {"description": "Error message shown when phone number format is invalid"}, "invalidPhoneNumber": "ير<PERSON>ى إدخال رقم هاتف صالح", "@invalidPhoneNumber": {"description": "Error message shown when phone number is invalid"}, "keypadNavigationBarText": "لوحة المفاتيح", "@keypadNavigationBarText": {"description": "navbar text for keypad page"}, "landlineText": "هات<PERSON> أرضي", "@landlineText": {"description": "Text that shows when the number is a landline number"}, "languageChangeError": "فشل في تغيير اللغة. يرجى المحاولة مرة أخرى", "@languageChangeError": {"description": "Error message shown when language change fails"}, "languageSelectionDisclaimer": " يمكنك تغيير لغتك من خلال\n", "@languageSelectionDisclaimer": {"description": "Message informing users where they can find language settings"}, "languageSetupPageAppBarSubtitle": "الخطوة 3 من 3", "@languageSetupPageAppBarSubtitle": {"description": "Subtitle of the profile setup page showing progress"}, "loadingRadioStationsText": "جاري تحميل محطات الراديو...", "@loadingRadioStationsText": {"description": "Message shown while radio stations are being loaded"}, "loadVoucherCardButtonText": "إعادة الشحن", "@loadVoucherCardButtonText": {}, "loadVoucherCardErrorText": "رمز خاطئ، حاول مرة أخرى", "@loadVoucherCardErrorText": {}, "loadVoucherCardLabelText": "أدخل رمز قسيمة مكون من 10 أرقام", "@loadVoucherCardLabelText": {}, "loadVoucherCardTitle": "تحميل رمز القسيمة", "@loadVoucherCardTitle": {}, "loadVoucherCodeTitle": "تحميل رمز القسيمة", "@loadVoucherCodeTitle": {"description": "Title shown on the load voucher code dialog"}, "loginPageAppBarSubtitle": "الخطوة 1 من 3", "@loginPageAppBarSubtitle": {"description": "Subtitle of the login page"}, "loginPageAppBarTitle": "البدء", "@loginPageAppBarTitle": {"description": "Title of the login page"}, "loginPageCheckboxLabel": "لدي رمز دعوة", "@loginPageCheckboxLabel": {}, "loginPageErrorMessage": "الرقم غير مرتبط بحساب واتساب", "@loginPageErrorMessage": {}, "loginPagePhoneLabel": "أدخل رقم واتساب الخاص بك", "@loginPagePhoneLabel": {}, "loginPagePhoneLabelDescription": "سيتم إرسال رمز تحقق لمرة واحدة إلى واتساب الخاص بك", "@loginPagePhoneLabelDescription": {}, "loginPagePhoneNumberError": "رقم هاتفك غير مكتمل، يرجى إكماله", "@loginPagePhoneNumberError": {}, "loginPageReferralLabel": "أدخل رمز الإحالة", "@loginPageReferralLabel": {}, "loginPageSubmitButton": "طل<PERSON> التحقق", "@loginPageSubmitButton": {}, "minsOrMin": "{count, plural, zero{minsOrMin[zero]} one{دقيقة} two{} few{} many{} other{دقائق}}", "@minsOrMin": {"description": "Text that shows either '1 min' or '{count} mins' based on the count", "placeholders": {"count": {"type": "int", "example": "5"}}}, "mobileText": "ها<PERSON><PERSON> محمول", "@mobileText": {"description": "Text that shows when the number is a mobile number"}, "monthlyReferralBoard": "{month}  قادة العروض الترويجية", "@monthlyReferralBoard": {"description": "Title for the monthly promotion leaderboard showing top referrers", "placeholders": {"month": {"type": "String", "example": "Jan'25"}}}, "moreAppbarTitle": "الحساب", "@moreAppbarTitle": {}, "moreNavigationBarText": "المزيد", "@moreNavigationBarText": {"description": "navbar text for more page"}, "morePageAccountBalanceCardTitle": "الرصيد", "@morePageAccountBalanceCardTitle": {}, "morePageAppVersionMenuText": "إصدار التطبيق", "@morePageAppVersionMenuText": {}, "morePageCallRatesMenuText": "أسعار المكالمات", "@morePageCallRatesMenuText": {}, "morePageHelpCenterMenuText": "مركز المساعدة", "@morePageHelpCenterMenuText": {}, "morePageLanguageMenuText": "اللغة", "@morePageLanguageMenuText": {}, "morePageLoadVoucherMenuText": "شحن بإستخدام قسيمه", "@morePageLoadVoucherMenuText": {}, "morePageLogoutMenuText": "تسجيل الخروج", "@morePageLogoutMenuText": {}, "morePageProfileMenuText": "الملف الشخصي\n", "@morePageProfileMenuText": {}, "morePageRadioMenuText": "الراديو", "@morePageRadioMenuText": {}, "morePageReferralCodeCardButtonText": "مزيد من المعلومات", "@morePageReferralCodeCardButtonText": {}, "morePageReferralCodeCardContent": "{numberOfPeople} أشخاص استخدموا رمز الدعوة الخاص بك", "@morePageReferralCodeCardContent": {}, "morePageReferralCodeCardDescription": "احصل على {amount} عندما تدعو أصدقائك وعائلتك", "@morePageReferralCodeCardDescription": {}, "morePageReferralCodeCardTitle": "رمز الدعوة", "@morePageReferralCodeCardTitle": {}, "nextTrack": "المسار التالي", "@nextTrack": {"description": "Text shown on button to play the next radio track or station"}, "noBuyCreditOptionsAvailable": "لا توجد خيارات متاحة لشراء الرصيد", "@noBuyCreditOptionsAvailable": {"description": "Message that shows when there are no buy credit options available"}, "noFavoriteContactsMessage": "لا توجد جهات اتصال مفضلة", "@noFavoriteContactsMessage": {"description": "Message shown when there are no favorite contacts to display"}, "noLanguagesAvailable": "لا توجد لغة متاحة", "@noLanguagesAvailable": {"description": "Message shown when no languages are available to select"}, "noNotificationsYet": "لا توجد إشعارات بعد", "@noNotificationsYet": {"description": "Text shown when there are no notifications"}, "noRadioStationsAvailableText": "لا توجد محطات راديو متاحة", "@noRadioStationsAvailableText": {"description": "Message shown when there are no radio stations to display"}, "notEnoughCreditMessage": "ليس لديك رصيد كافٍ", "@notEnoughCreditMessage": {"description": "Message shown when user has insufficient credit for a call"}, "notification": "إشعار", "@notification": {"description": "Text shown for a single notification"}, "notificationsAppBarTitle": "الإشعارات", "@notificationsAppBarTitle": {"description": "Title of the notifications page"}, "notificationsEmptyMessage": "ليس لديك أي إشعارات", "@notificationsEmptyMessage": {"description": "Message that shows when the user has no notifications"}, "notificationSettingsAppBarTitle": "إعدادات الإشعارات", "@notificationSettingsAppBarTitle": {"description": "Title of the notification settings page"}, "notificationSettingsAutoDeleteNotificationText": "الإشعار يتم حذفه تلقائيًا", "@notificationSettingsAutoDeleteNotificationText": {"description": "Text that shows to toggle the auto delete notification on the notification settings page"}, "notificationSettingsNotificationSoundText": "صوت الإشعار\n", "@notificationSettingsNotificationSoundText": {"description": "Text that shows to toggle the notification sound on the notification settings page"}, "notificationsTabAllText": "الكل", "@notificationsTabAllText": {"description": "Text that shows on the all tab on the notifications page"}, "notificationsTabUnreadText": "غير مقروء", "@notificationsTabUnreadText": {"description": "Text that shows on the unread tab on the notifications page"}, "notificationsTimeAgo": "{time} {time, plural, one{{duration}} other{{duration}s}} مضت", "@notificationsTimeAgo": {"description": "Text that shows the time since the notification was received", "placeholders": {"time": {"type": "int", "example": "2"}, "duration": {"type": "String", "example": "hours"}}}, "nowPlaying": "الآن يلعب", "@nowPlaying": {"description": "Text shown to indicate the currently playing radio track or station"}, "okButtonText": "موافق", "@okButtonText": {"description": "Text shown on OK confirmation button"}, "onboardingPageFooterAndText": "و", "@onboardingPageFooterAndText": {"description": "Text that shows between the terms and conditions link and the privacy policy link on the onboarding page"}, "onboardingPageFooterPrivacy": "سياسة الخصوصية", "@onboardingPageFooterPrivacy": {"description": "Text that shows below the submit button on the onboarding page"}, "onboardingPageFooterTermsConditionsText": "الشروط والأحكام\n", "@onboardingPageFooterTermsConditionsText": {"description": "Text that shows below the submit button on the onboarding page"}, "onboardingPageFooterText": "بالنقر على \"البدء\"، فإنك تقبل", "@onboardingPageFooterText": {"description": "Text that shows below the submit button on the onboarding page"}, "onboardingPageSliderHeader1": "تعد<PERSON> اللغات", "@onboardingPageSliderHeader1": {"description": "Header text for the first slide on the onboarding page"}, "onboardingPageSliderHeader2": "اتصل بأرقام الهواتف المحمولة أو الخطوط الأرضية في جميع أنحاء العالم", "@onboardingPageSliderHeader2": {"description": "Header text for the second slide on the onboarding page"}, "onboardingPageSliderHeader3": "سهولة الدفع", "@onboardingPageSliderHeader3": {"description": "Header text for the third slide on the onboarding page"}, "onboardingPageSliderText1": "يوفر FroggyTalk لك اختيار اللغة المفضلة مثل التغرينية، الأمهرية، الهوسا وغيرها.", "@onboardingPageSliderText1": {"description": "Text for the first slide on the onboarding page"}, "onboardingPageSliderText2": "قم بإجراء مكالمات لأي هاتف محمول أو خط أرضي في أي مكان بالعالم. لا يحتاج المستقبل إلى هاتف ذكي أو اتصال بالإنترنت.", "@onboardingPageSliderText2": {"description": "Text for the second slide on the onboarding page"}, "onboardingPageSliderText3": "اشترِ رصيد مكالمات بعملتك المحلية باستخدام طريقة الدفع المفضلة لديك.", "@onboardingPageSliderText3": {"description": "Text for the third slide on the onboarding page"}, "onboardingPageSubmitButtonText": "ابد<PERSON>", "@onboardingPageSubmitButtonText": {"description": "Text for the submit button on the onboarding page"}, "outOfCreditLabel": "الرصيد غير كافٍ", "@outOfCreditLabel": {"description": "Label shown when user has no remaining credit"}, "paymentFailureAppBarTitle": "فشل", "@paymentFailureAppBarTitle": {"description": "Title shown in app bar of payment failure page"}, "paymentFailureHomeButton": "الرئيسية", "@paymentFailureHomeButton": {"description": "Text for button to return to home page"}, "paymentFailureMessage": "لم تتم عملية الدفع الخاصة بك بقيمة {amount}", "@paymentFailureMessage": {"description": "Message shown when payment fails, includes amount", "placeholders": {"amount": {"type": "String", "example": "$50.00"}}}, "paymentFailureTitle": "فشل الدفع", "@paymentFailureTitle": {"description": "Main title shown when payment fails"}, "paymentFailureTryAgainButton": "حاول مرة أخرى", "@paymentFailureTryAgainButton": {"description": "Text for button to retry payment"}, "paymentOptionsAppBarTitle": "خيارات الدفع", "@paymentOptionsAppBarTitle": {"description": "Title of the payment options page"}, "paymentOptionsAutoCreditDescription": "سيتم تلقائيًا إضافة {amount} إلى حسابك عندما يكون رصيدك أقل من {minAmount}.", "@paymentOptionsAutoCreditDescription": {"description": "Description of the auto credit payment method"}, "paymentOptionsAutoCreditTitle": "إضافة تلقائية", "@paymentOptionsAutoCreditTitle": {"description": "Title of the auto credit payment method"}, "paymentOptionsSelectPaymentMethod": "اختر الطريقة المفضلة للدفع", "@paymentOptionsSelectPaymentMethod": {"description": "Text that shows above the payment method selection dropdown"}, "paymentOptionsSummaryAmountToCreditText": "المبلغ المضاف إلى الرصيد", "@paymentOptionsSummaryAmountToCreditText": {"description": "Text that shows the amount to be credited on the payment options page"}, "paymentOptionsSummaryDiscountButtonText": "تطبيق", "@paymentOptionsSummaryDiscountButtonText": {"description": "Text for the apply discount button on the payment options page"}, "paymentOptionsSummaryDiscountPlaceholder": "أدخل رمز الخصم (اختياري)", "@paymentOptionsSummaryDiscountPlaceholder": {"description": "Placeholder text for the discount code input field on the payment options page"}, "paymentOptionsSummaryDiscountText": "خصم", "@paymentOptionsSummaryDiscountText": {"description": "Text that shows the discount on the payment options page"}, "paymentOptionsSummaryTotalPaymentText": "إجمالي الدفع", "@paymentOptionsSummaryTotalPaymentText": {"description": "Text that shows the total payment on the payment options page"}, "paymentOptionsSummaryVatFeesText": "ضريبة القيمة المضافة + الرسوم", "@paymentOptionsSummaryVatFeesText": {"description": "Text that shows the VAT and fees on the payment options page"}, "paymentSummaryAmountToCreditLabel": "المبلغ الذي سيتم إضافته", "@paymentSummaryAmountToCreditLabel": {"description": "በክፍያ ማጠቃለያ ውስጥ የሚቀርብ መጠን ምልክት"}, "paymentSummaryDiscountLabel": "الخصم", "@paymentSummaryDiscountLabel": {"description": "በክፍያ ማጠቃለያ ውስጥ ቅናሽ ምልክት"}, "paymentSummaryPlayStoreFeeLabel": "+ {percentage}% عمولة + ضريبة", "@paymentSummaryPlayStoreFeeLabel": {"description": "Label showing the Play Store fee percentage and VAT in payment summary", "placeholders": {"percentage": {"type": "String", "example": "30"}}}, "paymentSummaryTotalLabel": "إجمالي الدفع", "@paymentSummaryTotalLabel": {"description": "በክፍያ ማጠቃለያ ውስጥ ጠቅላላ ክፍያ ምልክት"}, "paymentSummaryVatFeesLabel": "ضريبة القيمة المضافة + الرسوم", "@paymentSummaryVatFeesLabel": {"description": "በክፍያ ማጠቃለያ ውስጥ ተጨማሪ ክፍያዎች ምልክት"}, "payWithApplePay": "Apple Pay", "@payWithApplePay": {"description": "Text shown on button to pay using Apple Pay"}, "payWithGooglePay": "Google Pay", "@payWithGooglePay": {"description": "Text shown on button to pay using Google Pay"}, "perMinRate": "{rate}/د", "@perMinRate": {"description": "Rate per minute for a call", "placeholders": {"rate": {"type": "String", "example": "$10"}}}, "perMinRateSingle": "/دقيقة", "@perMinRateSingle": {"description": "Rate per minute for a call in short form"}, "perMinuteRate": "{rate}/دقيقة", "@perMinuteRate": {"description": "Rate per minute for a call", "placeholders": {"rate": {"type": "String", "example": "$10"}}}, "perMinuteSlashLabel": "{min}/دقائق", "@perMinuteSlashLabel": {"description": "Label showing '/minutes' for call rate displays", "placeholders": {"min": {"type": "String", "example": "5"}}}, "permissionButtonAccept": "السماح بالوصول", "@permissionButtonAccept": {"description": "Text for the accept button on the permission request dialog"}, "permissionButtonSkip": "تخطي", "@permissionButtonSkip": {"description": "Text for the skip button on the permission request dialog"}, "permissionDeniedMessage": "تم رفض الإذن، يرجى تمكينه في إعدادات التطبيق", "@permissionDeniedMessage": {"description": "Message shown when a permission is denied by the user"}, "permissionForCameraTitle": "نحتاج إلى إذن لالتقاط الصور ومقاطع الفيديو.", "@permissionForCameraTitle": {"description": "Title for the camera permission request dialog"}, "permissionForContactListTitle": "اسمح لتطبيق FroggyTalk بالوصول إلى قائمة جهات الاتصال / دليل الهاتف الخاص بك", "@permissionForContactListTitle": {"description": "Title for the contact list permission request dialog"}, "permissionForMicrophoneTitle": "اسمح لتطبيق FroggyTalk بالوصول إلى الميكروفون الخاص بك لإجراء المكالمات", "@permissionForMicrophoneTitle": {"description": "Title for the microphone permission request dialog"}, "permissionForNotificationTitle": "اسمح لتطبيق FroggyTalk بإرسال التحديثات لك", "@permissionForNotificationTitle": {"description": "Title for the notification permission request dialog"}, "permissionForStorageTitle": "نحتاج إلى إذن لتخزين البيانات المؤقتة على جهازك.", "@permissionForStorageTitle": {"description": "Title for the storage permission request dialog"}, "permissionPermanentlyDeniedMessage": "تم رفض الإذن بشكل دائم", "@permissionPermanentlyDeniedMessage": {"description": "Message shown when a permission is permanently denied by the user"}, "perM_Rate": "{rate}/د", "@perM_Rate": {"description": "Rate per minute for a call", "placeholders": {"rate": {"type": "String", "example": "$10"}}}, "phoneNumberStartRule": "يرجى وضع + أو 00 في بداية الرقم", "@phoneNumberStartRule": {"description": "Error message shown when phone number doesn't start with + or 00"}, "playRadio": "تشغيل الراديو", "@playRadio": {"description": "Text shown on button to start playing the radio"}, "previousTrack": "المسار السابق", "@previousTrack": {"description": "Text shown on button to play the previous radio track or station"}, "proceedToPaymentButtonText": "متابعة", "@proceedToPaymentButtonText": {"description": "Text for the proceed to payment button on the buy credit page"}, "profileAppbarTitle": "الملف الشخصي", "@profileAppbarTitle": {}, "profileDeleteAccountButtonText": "<PERSON><PERSON><PERSON> الح<PERSON>اب", "@profileDeleteAccountButtonText": {}, "profileDeleteAccountCardContent": "يرجى التأكيد إذا كنت تريد حذف حسابك. ستفقد أي رصيد موجود في حسابك.", "@profileDeleteAccountCardContent": {}, "profileDeleteAccountCardTitle": "<PERSON><PERSON><PERSON> الح<PERSON>اب\n", "@profileDeleteAccountCardTitle": {}, "profileLabel": "الملف الشخصي", "@profileLabel": {"description": "Label used for profile sections"}, "profileLabelChangeLocation": "تغيير الموقع", "@profileLabelChangeLocation": {}, "profileLabelEmailAddress": "أدخل عنوان البريد الإلكتروني", "@profileLabelEmailAddress": {}, "profileLabelFullName": "أد<PERSON>ل الاسم الكامل", "@profileLabelFullName": {"description": "profile<PERSON>abelFull<PERSON>ame"}, "profileUpdatedSuccessfullyMessage": "تم تحديث الملف الشخصي بنجاح", "@profileUpdatedSuccessfullyMessage": {"description": "Message shown when profile is updated successfully"}, "profileUpdateFailedMessage": "فشل تحديث الملف الشخصي", "@profileUpdateFailedMessage": {"description": "Message shown when profile update fails"}, "quickAdvertSubtitle": "اشترِ رصيدًا لعائلتك في جميع أنحاء العالم", "@quickAdvertSubtitle": {"description": "Subtitle for the quick advert on the home page"}, "quickAdvertTitle": "أرسل رصيدًا إلى أكثر من 140 دولة", "@quickAdvertTitle": {"description": "Title for the quick advert on the home page"}, "radioAppBarTitle": "الراديو", "@radioAppBarTitle": {}, "radioComingSoonText": "الراديو قادم قريبًا !!!", "@radioComingSoonText": {"description": "Text shown when radio feature is not yet available"}, "radioPageChooseChannelButtonText": "اختر المحطة", "@radioPageChooseChannelButtonText": {}, "radioPageNoChannelsText": "لم يتم العثور على محطات", "@radioPageNoChannelsText": {}, "radioPageSearchPlaceholder": "اختر المحطة", "@radioPageSearchPlaceholder": {}, "ratesNavigationBarText": "الأسعار", "@ratesNavigationBarText": {"description": "navbar text for rates page"}, "recentCallsText": "المكالمات الأخيرة", "@recentCallsText": {"description": "Text that shows above the recent calls list on the home page"}, "rechargeButtonText": "إعادة شحن", "@rechargeButtonText": {"description": "Text shown on recharge button"}, "referAndEarn": "دعوة و اكسب", "@referAndEarn": {"description": "Text shown for refer and earn promotional text"}, "referralCardButtonText": "ر<PERSON>ي<PERSON> مجاني", "@referralCardButtonText": {"description": "Text for the referral card button"}, "referralCardDescription1": "ادعُ شخصًا واحصل على {percentageAmount} رصيد مجاني.", "@referralCardDescription1": {"description": "Message showing the referral benefit", "placeholders": {"percentageAmount": {"type": "String", "example": "10%"}}}, "referralCardDescription2": "{count, plural, zero{لم يستخدم أحد رمز الدعوة الخاص بك.} one{شخص واحد استخدم رمز الدعوة الخاص بك.} two{} few{} many{} other{{numberOfPeople} أشخاص استخدموا رمز الدعوة الخاص بك.}}", "@referralCardDescription2": {"description": "Message showing how many people have used the referral code, with pluralization.", "placeholders": {"numberOfPeople": {"type": "int", "example": "2"}}}, "referralCardTitle": "رمز الدعوة", "@referralCardTitle": {"description": "Title for the referral card"}, "referralCodeOnCopyActionResponse": "تم نسخ FroggyTalk إلى الحافظة", "@referralCodeOnCopyActionResponse": {}, "refreshText": "تحديث", "@refreshText": {"description": "Text shown on button to refresh or reload content"}, "restorePurchases": "استعادة المشتريات", "@restorePurchases": {"description": "Button text for restoring previous in-app purchases"}, "retry": "اختر لغتك المفضلة\n", "@retry": {"description": "Text for button to retry a failed operation"}, "saveAndProceedToPaymentButtonText": "حفظ والمتابعة إلى الدفع", "@saveAndProceedToPaymentButtonText": {"description": "Text shown on button to save profile changes and proceed to payment"}, "saveChangesButtonText": "حفظ التغييرات", "@saveChangesButtonText": {}, "savedSuccessfullyMessage": "تم الحفظ بنجاح", "@savedSuccessfullyMessage": {"description": "Message shown when changes are saved successfully"}, "searchCountryPlaceholder": "ابحث عن دولة", "@searchCountryPlaceholder": {"description": "Placeholder text for the search input field on the country selection page"}, "searchFavouriteContactMessage": "البحث عن جهة اتصال مفضلة", "@searchFavouriteContactMessage": {"description": "Placeholder text for searching favorite contacts"}, "searchForCountryNoResults": "لا توجد نتائج", "@searchForCountryNoResults": {"description": "Text that shows when no results are found for the search input field on the country selection page"}, "searchForCountryPlaceholder": "ابحث عن دولة", "@searchForCountryPlaceholder": {"description": "Placeholder text for the search input field on the country selection page"}, "searchRecentCallsMessage": "البحث عن المكالمات الأخيرة", "@searchRecentCallsMessage": {"description": "Placeholder text for searching recent calls"}, "secsOrSec": "{count, plural, zero{} one{ثانية} two{} few{} many{} other{ثواني}}", "@secsOrSec": {"description": "Text that shows either '1 sec' or '{count} secs' based on the count", "placeholders": {"count": {"type": "int", "example": "10"}}}, "securedByStripe": "مؤمن بواسطة سترايب", "@securedByStripe": {"description": "Text showing that payment is secured by Stripe payment processor"}, "selectCountryPlaceholder": "اختر الدولة", "@selectCountryPlaceholder": {"description": "Placeholder text for the country selection dropdown"}, "selectPreferredLanguage": "إعادة المحاولة", "@selectPreferredLanguage": {"description": "Subtitle for the language selection modal"}, "sendCreditButtonText": "أرسل رصيدًا", "@sendCreditButtonText": {"description": "Text for the send credit button on the home page"}, "settings": "الإعدادات", "@settings": {"description": "Text shown for settings"}, "shareReferralText": "قم بتحميل تطبيق FroggyTalk {appLink} وقم بالتسجيل باستخدام رمز الدعوة الخاص بي {referralCode}. يحتوي على أفضل الأسعار وتجربة المكالمات الدولية.", "@shareReferralText": {"description": "Text shown when sharing referral code", "placeholders": {"appLink": {"type": "String", "example": "https://link-to.app/AHA423kdwG"}, "referralCode": {"type": "String", "example": "ABC123"}}}, "somethingWentWrongMessage": "حدث خطأ ما. ير<PERSON>ى المحاولة مرة أخرى.", "@somethingWentWrongMessage": {"description": "Message that shows when an error occurs"}, "stopRadio": "<PERSON>ي<PERSON><PERSON><PERSON> الراديو", "@stopRadio": {"description": "Text shown on button to stop the currently playing radio"}, "storePrice": "سعر المتجر", "@storePrice": {"description": "Label for the price listed in the app store"}, "termsAndConditionsApply": "اختر لغتك المفضلة\n", "@termsAndConditionsApply": {"description": "Text indicating that terms and conditions apply to an offer or promotion"}, "timeLeftModalMessage": "تبقّى {days} أيام لتكون الفائز بجائزة الكاش. قم بدعوة {count} أشخاص وكن رقم ", "@timeLeftModalMessage": {"description": "Message showing days left and number of referrals needed", "placeholders": {"days": {"type": "String", "example": "25"}, "count": {"type": "int", "example": "30"}}}, "timeLeftModalTitle": " الأيام المتبقية\n", "@timeLeftModalTitle": {"description": "Title showing the number of days left in the modal", "placeholders": {"days": {"type": "String", "example": "2"}}}, "tryAgainText": "حاول مرة أخرى", "@tryAgainText": {"description": "Text shown on button to retry an action that failed"}, "unknownErrorText": "حد<PERSON> خطأ غير معروف", "@unknownErrorText": {"description": "Generic message shown when an unspecified error occurs"}, "unread": "غير مقروء", "@unread": {"description": "Text shown for unread notifications"}, "updateLanguagePrompt": "يرجى النقر على الزر أدناه لتحديث لغتك", "@updateLanguagePrompt": {"description": "Instruction text prompting user to update their language setting"}, "updateProfileButtonText": "تحديث الملف الشخصي", "@updateProfileButtonText": {"description": "Text shown on button to update user profile"}, "updateYourLanguage": "تحديث لغتك.", "@updateYourLanguage": {"description": "Button or action text for language update functionality"}, "upgradeDialogButtonNo": "ليس الآن\n", "@upgradeDialogButtonNo": {"description": "እትም ማስተካከልን ለመሰረዝ የሚጠቀምበት አዝራር ጽሑፍ"}, "upgradeDialogButtonYes": "تحديث", "@upgradeDialogButtonYes": {"description": "እትም ማስተካከልን ለመቀጠል የሚጠቀምበት አዝራር ጽሑፍ"}, "upgradeDialogMessage": "يتوفر إصدار جديد من FroggyTalk. هل ترغب في التحديث الآن؟", "@upgradeDialogMessage": {"description": "አዳዲስ እትም ሲኖር ተጠቃሚውን ለመዘመን የሚጠይቅ መልእክት"}, "upgradeDialogTitle": "تحديث متوفر", "@upgradeDialogTitle": {"description": "አዳዲስ እትም ሲኖር የሚታይ እትም ርዕስ"}, "validationCouponCodeIncomplete": "رمز الخصم غير مكتمل أو غير صالح.", "@validationCouponCodeIncomplete": {"description": "Error message shown when coupon code length or format is invalid"}, "validationCouponInvalid": "ير<PERSON>ى إدخال رمز خصم صالح.", "@validationCouponInvalid": {"description": "Error message shown when coupon code is invalid"}, "validationEmailInvalid": "البريد الإلكتروني غير صالح.", "@validationEmailInvalid": {"description": "Error message shown when email format is invalid"}, "validationFieldIsRequired": "{fieldName} مطلوب", "@validationFieldIsRequired": {"description": "Generic required field error message", "placeholders": {"fieldName": {"type": "String", "example": "Email"}}}, "validationLengthError": "يجب أن يكون طول {fieldName} بين {minLength} و {maxLength} حرفًا", "@validationLengthError": {"description": "Error message for fields that don't meet length requirements", "placeholders": {"fieldName": {"type": "String", "example": "Password"}, "minLength": {"type": "int", "example": "8"}, "maxLength": {"type": "int", "example": "16"}}}, "validationMinLengthError": "{fieldName} يجب أن يكون طوله على الأقل {minLength} حرفًا", "@validationMinLengthError": {"description": "Error message for fields that don't meet minimum length requirement", "placeholders": {"fieldName": {"type": "String", "example": "Password"}, "minLength": {"type": "int", "example": "8"}}}, "validationPhoneNumberIncomplete": "رقم الهاتف غير مكتمل.", "@validationPhoneNumberIncomplete": {"description": "Error message shown when phone number is incomplete"}, "viewButtonText": "<PERSON><PERSON><PERSON>", "@viewButtonText": {"description": "Text for the view action button on page"}, "viewContactDetailAppBarTitle": "تفاصيل جهة الاتصال", "@viewContactDetailAppBarTitle": {"description": "Title of the contact detail page"}, "voucherLoadedSuccessMessage": "تم تحميل القسيمة بنجاح", "@voucherLoadedSuccessMessage": {"description": "Message shown when voucher is successfully loaded"}, "welcomeBackText": "مرحبًا بعودتك، {firstName}", "@welcomeBackText": {"description": "Greeting message shown to returning users", "placeholders": {"firstName": {"type": "String", "example": "<PERSON>"}}}, "googlePay": "Google Pay", "@googlePay": {"description": "Text shown on button to pay using Google Pay"}, "appStoreCost": "+ {value} تكلفة متجر التطبيقات", "@appStoreCost": {"description": "Label showing the cost charged by the App Store including fees and VAT", "placeholders": {"value": {"type": "String", "example": "USD 2.99"}}}}