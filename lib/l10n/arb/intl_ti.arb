{"@@locale": "ti", "acceptButtonText": "ይቕበሉ", "@acceptButtonText": {"description": "Text shown on buttons that accept or confirm an action"}, "accountBalanceCardTitle": "ተረፍ ሕሳብ", "@accountBalanceCardTitle": {"description": "Title of the account balance page"}, "activateAutoCredit": "ኣውቶ ክሬዲት ንጡፍ ምግባር ", "@activateAutoCredit": {"description": "Text for button to enable automatic account crediting"}, "all": "ኩሎም", "@all": {"description": "Text shown for all notifications"}, "allChannels": "ኩሎም ጣብያታት", "@allChannels": {"description": "Text shown to represent all available radio channels or stations"}, "allowAccessButtonText": "ነዚ ኣድላዩ ስጉምቲ ንምፍፃም ዘድሊ ፍቃድ ንምሃብ", "@allowAccessButtonText": {"description": "ተጠቃሚውን የመሳሪያ ባህሪዎች መድረሻ እንዲፈቀድ በመጠየቅ ላይ የሚታይ ጠንቅቆ የተጻፈ ጽሑፍ"}, "allowShareInfoWithRadio": "ሓበሬታኹም ምስ {radioName} ክንካፈል ፍቓድ ይሃቡና", "@allowShareInfoWithRadio": {"description": "Text shown when requesting permission to share user information with a radio station", "placeholders": {"radioName": {"type": "String", "example": "KEXP Radio"}}}, "appBootingTextBelow": "ርክብ ማሕበረሰባት ክናዋሕ ኣብ ምሕጋዝ", "@appBootingTextBelow": {"description": "Text that shows below the app title on the booting screen"}, "appLanguageAppbarTitle": "ቋንቋ መተግበሪ", "@appLanguageAppbarTitle": {"description": "This is a title for the app bar"}, "apply": "ኣተግብር", "@apply": {"description": "Text for apply/confirm buttons"}, "appTitle": "<PERSON><PERSON>T<PERSON>", "@appTitle": {"description": "The name of the app"}, "autoAccountCreditHelp": "ኣብ ካርድኹም ክኽፈልን ድሕሪኡ ኣብ ዝወደቐሉ እዋን ኣብ ሚዛንኩም ክውሰኽን እዩ", "@autoAccountCreditHelp": {"description": "Explanation text for the auto credit feature"}, "avoidCallDisruption": "ኣብ እዋን ጻውዒት ንከይቛረጽ", "@avoidCallDisruption": {"description": "Additional explanation for auto credit feature benefit"}, "buyCreditAmountCustomPlaceholder": "ዝተሓተ {min} - ዝለዓለ {max}", "@buyCreditAmountCustomPlaceholder": {"description": "Placeholder text for the custom amount input field on the buy credit page", "placeholders": {"min": {"type": "String", "example": "$50"}, "max": {"type": "String", "example": "$500"}}}, "buyCreditAmountRecommended": "ዝምከር", "@buyCreditAmountRecommended": {"description": "Text that shows above the recommended amount on the buy credit page"}, "buyCreditAppBarTitle": "ካርዲ ንምዕዳግ", "@buyCreditAppBarTitle": {"description": "The app page title for buy credit"}, "buyCreditButtonText": "ካርዲ ንምዕዳግ", "@buyCreditButtonText": {"description": "Text for the buy credit button on the account balance page"}, "buyCreditEnterCustomAmountLabel": "ዝመረፅዎ መጠን የእትዉ", "@buyCreditEnterCustomAmountLabel": {"description": "Label for the custom amount input field on the buy credit page"}, "buyCreditPageTitle": "ዝመርፅዎ መጠን", "@buyCreditPageTitle": {"description": "Title of the buy credit page"}, "callButtonText": "ደወል", "@callButtonText": {"description": "Text for the call action button on a page"}, "callingAppBarTitle": "ኣብ ምድዋል", "@callingAppBarTitle": {"description": "Title of the calling page"}, "callingPageBluetoothOptionsText": "ብሉቱዝ", "@callingPageBluetoothOptionsText": {"description": "Text that shows to toggle the bluetooth on the calling page"}, "callingPageCreditErrorCardTitle": "ናይ መደወሊ ሒሳብ ወዲኦም", "@callingPageCreditErrorCardTitle": {}, "callingPagePhoneOptionsText": "ስልኪ", "@callingPagePhoneOptionsText": {"description": "Text that shows to toggle the phone on the calling page"}, "callingPageSpeakerOptionsText": "ስፒከር", "@callingPageSpeakerOptionsText": {"description": "Text that shows to toggle the speaker on the calling page"}, "callingPageTimeLeft": "{time} ተሪፉ\n", "@callingPageTimeLeft": {"description": "Text that shows the time left for the call to end", "placeholders": {"time": {"type": "String", "example": "2"}}}, "callLogTypeAnswered": "ዝተልዓለ", "@callLogTypeAnswered": {"description": "Text that shows when the call log type is answered"}, "callLogTypeBusy": "ዝተትሓዘ መስመር", "@callLogTypeBusy": {"description": "Text that shows when the call log type is busy"}, "callLogTypeCancel": "ዝተሰረዘ", "@callLogTypeCancel": {"description": "Text that shows when the call log type is cancelled"}, "callLogTypeIncoming": "ኣታዊ ደወል", "@callLogTypeIncoming": {"description": "Text that shows when the call log type is incoming"}, "callLogTypeMissed": "ዘይተልዓለ ደወል", "@callLogTypeMissed": {"description": "Text that shows when the call log type is missed"}, "callLogTypeOutgoing": "ወፃኢ ደወል", "@callLogTypeOutgoing": {"description": "Text that shows when the call log type is outgoing"}, "callLogTypeUnavailable": "ዘይተረኸበ", "@callLogTypeUnavailable": {"description": "Text that shows when the call log type is unavailable"}, "callRatesAppBarTitle": "ናይ መደወሊ ተመናት", "@callRatesAppBarTitle": {"description": "Title of the call rates page"}, "callTypeAnswered": "ወፃኢ ደወል", "@callTypeAnswered": {"description": "Text that shows when the call is answered"}, "callTypeBusy": "ዝተትሓዘ መስመር", "@callTypeBusy": {"description": "Text that shows when the call is busy"}, "callTypeCancel": "ዝተሰረዘ ደወል", "@callTypeCancel": {"description": "Text that shows when the call is cancelled"}, "callTypeIncoming": "ኣታዊ ደወል", "@callTypeIncoming": {"description": "Text that shows when the call is incoming"}, "callTypeMissed": "ዘይተልዓለ ደወል", "@callTypeMissed": {"description": "Text that shows when the call is missed"}, "callTypeOutgoing": "ወፃኢ ደወል", "@callTypeOutgoing": {"description": "Text that shows when the call is outgoing"}, "callTypeUnavailable": "ምርካብ ዘይከኣል ዓሚል", "@callTypeUnavailable": {"description": "Text that shows when the user is unavailable"}, "cancel": "ሰርዝ", "@cancel": {"description": "Text for cancel buttons"}, "cancelButtonText": "ንምስራዝ", "@cancelButtonText": {}, "cashPrizeAmount": "{currencySymbol}{amount} ነጻ ጥረ ገንዘብ", "@cashPrizeAmount": {"description": "Text showing the cash prize amount for promotions and referrals", "placeholders": {"currencySymbol": {"type": "String", "example": "€"}, "amount": {"type": "String", "example": "250"}}}, "changeChannelButtonText": "ጣብያ ንምቅያር", "@changeChannelButtonText": {"description": "Text shown on button to change radio channel"}, "channel": "ጣብያ", "@channel": {"description": "Text shown to represent a radio channel or station"}, "chatWithLiveAgentAppbarTitle": "አብ መስመር ዝርከብ ወኪል", "@chatWithLiveAgentAppbarTitle": {}, "checkBackLaterText": "በይዞም ድሓር ይርኣዩ", "@checkBackLaterText": {"description": "Message shown when a feature or content is temporarily unavailable"}, "chooseLanguage": "ቋንቋ ምረጹ", "@chooseLanguage": {"description": "Title for the language selection modal"}, "closeButtonText": "ንምዕፃው", "@closeButtonText": {"description": "Text shown on buttons that close views or dialogs"}, "confirm": "ኣረጋግጹ ", "@confirm": {"description": "Text for confirm/verification buttons"}, "confirmationAppBarTitle": "መረጋገፂ", "@confirmationAppBarTitle": {"description": "Title of the confirmation page"}, "confirmationFailedButtonText": "መሊሶም ይፈትኑ", "@confirmationFailedButtonText": {"description": "Text for the try again button on the confirmation page"}, "confirmationFailedContactSupportDescription": "ዝኾነ ሕቶ እንተሃልይኩም ወይ ድማ ሓገዝ እንተድልይኩም፤ በይዛኩም ክፍሊ ሓገዝና ርከቡ", "@confirmationFailedContactSupportDescription": {"description": "Description that shows when the payment fails"}, "confirmationFailedContactSupportText": "ክፍሊ ሓገዝ ንምርካብ", "@confirmationFailedContactSupportText": {"description": "Text for the contact support button on the confirmation page"}, "confirmationFailedDescription": "ክፍሊቶም አይተሳኸዐን። በይዘኦም ደጊሞም ይፈትኑ", "@confirmationFailedDescription": {"description": "Description that shows when the payment fails"}, "confirmationFailedTitle": "ክፍሊት ኣይተፈፀመን", "@confirmationFailedTitle": {"description": "Title that shows when the payment fails"}, "confirmationSuccessButtonText": "ተዛዚሙ", "@confirmationSuccessButtonText": {"description": "Text for the success button on the confirmation page"}, "confirmationSuccessDescription": "{amount} ናብ ሕሳቦም ብዝተሳኸዐ መንገዲ አትዩ ኣሎ", "@confirmationSuccessDescription": {"description": "Description that shows when the payment is successful", "placeholders": {"amount": {"type": "String", "example": "$10"}}}, "confirmationSuccessTitle": "ክፍሊት ተሳኺዑ አሎ", "@confirmationSuccessTitle": {"description": "Title that shows when the payment is successful"}, "contactsAllContactsTabText": "መራክቦታታ", "@contactsAllContactsTabText": {"description": "Text that shows above the contacts list on the home page"}, "contactsCallRateText": "{min} ደቒቓታት {secs} ሰከንድ\n", "@contactsCallRateText": {"description": "Text that shows the duration of a call", "placeholders": {"min": {"type": "String", "example": "10"}, "secs": {"type": "String", "example": "3"}}}, "contactsFavouriteCallsTabText": "ዝተመረፁ", "@contactsFavouriteCallsTabText": {"description": "Text that shows above the Favourite calls list on the home page"}, "contactsNoCallsEmptyMessage": "ኣብ ቀረባ እዋን ዝተገበሩ ደወላት የብሎምን", "@contactsNoCallsEmptyMessage": {"description": "Message that shows when the user has no recent calls"}, "contactsNoContactsButtonText": "መራክቦ ንምርኣይ", "@contactsNoContactsButtonText": {"description": "Text for the view contacts button on the home page"}, "contactsSearchContactsPlaceholder": "መራክቦ ሽም ንምድላይ", "@contactsSearchContactsPlaceholder": {"description": "Placeholder text for the search contact name"}, "contactsSearchForContactsPlaceholder": "መራክቦ ሽም ንምድላይ", "@contactsSearchForContactsPlaceholder": {"description": "Placeholder text for the search input field on the contacts page"}, "couponAppliedButtonText": "ኣብ ተግበር ውዒሉ", "@couponAppliedButtonText": {"description": "ኩፖኑ በትክክል ሲተገብር የሚታይ ጽሑፍ"}, "couponApplyButtonText": "ንምጥቃም", "@couponApplyButtonText": {"description": "የኩፖን አተግባለሁ አዝራር ጽሑፍ"}, "credit": "ሕሳብ", "@credit": {"description": "Text shown for credit"}, "declineButtonText": "ይነፅጉ", "@declineButtonText": {"description": "Text shown on buttons that decline or reject an action"}, "deleteAccountWarning": "እቲ ዝፍፅምዎ ዝተዳለዉ ስጉምቲ ዘይምለስ ሳዕቤን ዘኸትል እዩ። በይዘኦም ሕሳቦም ይዓፅዉ ከምዘለዉ የረጋግፁ።", "@deleteAccountWarning": {"description": "Warning message shown when user attempts to delete their account"}, "dialerAppBarTitle": "መደወሊ", "@dialerAppBarTitle": {"description": "Title of the dialer page"}, "dialerCopyActionText": "ንምቕዳሕ", "@dialerCopyActionText": {"description": "Text for the copy action on the dialer page"}, "dialerErrorInvalidNumber": "ዝተሰሓሐተ ቁፅሪ", "@dialerErrorInvalidNumber": {"description": "Error message that shows when the user enters an invalid number"}, "dialerErrorWithoutCountryCode": "ኮድ ሃገር የእትዉ (ንኣብነት +1)", "@dialerErrorWithoutCountryCode": {"description": "Error message that shows when the user does not add a country code"}, "dialerKeypadABC": "ABC", "@dialerKeypadABC": {"description": "Text that shows on the ABC button on the dialer page"}, "dialerKeypadDEF": "DEF", "@dialerKeypadDEF": {"description": "Text that shows on the DEF button on the dialer page"}, "dialerKeypadGHI": "GHI", "@dialerKeypadGHI": {"description": "Text that shows on the GHI button on the dialer page"}, "dialerKeypadJKL": "JKL", "@dialerKeypadJKL": {"description": "Text that shows on the JKL button on the dialer page"}, "dialerKeypadMNO": "MNO", "@dialerKeypadMNO": {"description": "Text that shows on the MNO button on the dialer page"}, "dialerKeypadPQRS": "PQRS", "@dialerKeypadPQRS": {"description": "Text that shows on the PQRS button on the dialer page"}, "dialerKeypadTUV": "TUV", "@dialerKeypadTUV": {"description": "Text that shows on the TUV button on the dialer page"}, "dialerKeypadWXYZ": "WXYZ", "@dialerKeypadWXYZ": {"description": "Text that shows on the WXYZ button on the dialer page"}, "dialerMuteStatusMuted": "ድምፂ ጠፊኡ", "@dialerMuteStatusMuted": {"description": "Text that shows when the dialer is muted"}, "dialerMuteStatusUnmuted": "ድምፂ ይስማዕ", "@dialerMuteStatusUnmuted": {"description": "Text that shows when the dialer is unmuted"}, "dialerPasteActionText": "ንምልጣፍ", "@dialerPasteActionText": {"description": "Text for the paste action on the dialer page"}, "dialerSearchResultsHeader": "ውፅኢታት", "@dialerSearchResultsHeader": {"description": "Header text for the search results on the dialer page"}, "dialerSearchResultsNoResults": "ዝተረኸበ ውፅኢት የለን", "@dialerSearchResultsNoResults": {"description": "Text that shows when no results are found for the search input field on the dialer page"}, "dialerStatusConnected": "ተራኪቡ", "@dialerStatusConnected": {"description": "Text that shows when the dialer status is connected"}, "dialerStatusConnecting": "ኣብ ምድዋል", "@dialerStatusConnecting": {"description": "Text that shows when the dialer status is connecting"}, "dialerStatusEnded": "ተዓፂዉ", "@dialerStatusEnded": {"description": "Text that shows when the dialer status is ended"}, "dialerStatusError": "ስሕተት ተፈጢሩ", "@dialerStatusError": {"description": "Text that shows when an error occurs in the dialer"}, "dialerStatusFailedToConnect": "ምርካብ ኣይተክኣለን", "@dialerStatusFailedToConnect": {"description": "Text that shows when the dialer fails to connect"}, "dialerStatusHold": "ኣብ ምፅባይ", "@dialerStatusHold": {"description": "Text that shows when the dialer status is on hold"}, "dialerStatusInitial": "ኣብ ምድዋል", "@dialerStatusInitial": {"description": "Text that shows when the dialer status is initial"}, "dialerStatusInitiating": "ኣብ ምድዋል", "@dialerStatusInitiating": {"description": "Text that shows when the dialer status is initiating"}, "dialerStatusRinging": "ይፅውዕ ኣሎ", "@dialerStatusRinging": {"description": "Text that shows when the dialer status is ringing"}, "dialerStatusUnknown": "ዘይፍለጥ", "@dialerStatusUnknown": {"description": "Text that shows when the dialer status is unknown"}, "emptyContactList": "ዝርዝር መራክቦ የብሎምን", "@emptyContactList": {"description": "Message that shows when the user has no contact list"}, "emptyFavouriteContactList": "ናይ ዝተመረፁ መራክቦታት ዝርዝር የብሎምን", "@emptyFavouriteContactList": {"description": "Message that shows when the user has no favourite contact list"}, "enterCouponOptionalPlaceholder": "ኩፖን የእትዉ (እንተደልዮም)", "@enterCouponOptionalPlaceholder": {"description": "የኩፖን ኮድ መጠቀም ስፍራ ጽሑፍ"}, "enterOtpPageAppBarSubtitle": "2ይ ክፋል ካብ 3", "@enterOtpPageAppBarSubtitle": {}, "enterOtpPageAppBarTitle": "ንምጅማር", "@enterOtpPageAppBarTitle": {}, "enterOtpPageEditButton": "ዝተሰሓሐተ ቁፅሪ WhatsApp? በይዘኦም የስተኻክሉ", "@enterOtpPageEditButton": {"description": "Text that shows on the edit phone number button"}, "enterOtpPageErrorMessage": "ዝተሰሓሐተ ኮድ", "@enterOtpPageErrorMessage": {"description": "Error message that shows when the user enters an incorrect OTP"}, "enterOtpPagePhoneLabel": "መረጋገፂ ኮድ የእትዉ", "@enterOtpPagePhoneLabel": {}, "enterOtpPagePhoneLabelDescription": "በዓል 4 ቁፅሪ ኮድ ናብ WhatsApp ቁፅሮም {phoneNumber} ተላኢኹ ኣሎ", "@enterOtpPagePhoneLabelDescription": {"description": "Description text for the OTP input field on the OTP entry page", "placeholders": {"phoneNumber": {"type": "String", "example": "+1234567890"}}}, "enterOtpPageResendOtpButton": "ኮድ ዳግማይ ንምልኣኽ", "@enterOtpPageResendOtpButton": {"description": "Text that shows on the confirm verificatio button"}, "enterOtpPageResendOtpRetries": "{count, plural, zero{ዝተረፎም ፈተነ የለን} one{ሓደ ናይ መወዳእታ ፈተነ} other{{count} ተወሳኪ ፈተነታት}}", "@enterOtpPageResendOtpRetries": {"description": "Message showing the number of retries a user has left", "placeholders": {"count": {"type": "int", "example": "3"}}}, "enterOtpPageResendOtpRetriesError": "በዚ ቁፅሪ WhatsApp ንዝቕፅል 24 ሰዓታት ናይ መረጋገፂ ኮድ ክትቅበሉ ኣይትኽእሉን", "@enterOtpPageResendOtpRetriesError": {"description": "Error message that shows when the user has exhausted the number of retries"}, "enterOtpPageSubmitButton": "ይ ሓደ ግዘ ናይ ሕለፍ ቃል ንምርግጋፅ", "@enterOtpPageSubmitButton": {"description": "Text that shows on the confirm verificatio button"}, "enterVoucherCodeLabel": "ኮድ ካርዲ የእትዉ", "@enterVoucherCodeLabel": {"description": "Label for voucher code input field"}, "errorLoadingRadioStationsText": "ናይ ሬድዮ ጣብያታት ኣብ ምፅዓን ስሕተት ተፈጢሩ", "@errorLoadingRadioStationsText": {"description": "Message shown when radio stations fail to load"}, "failedToLoadLeaderboard": "ሊደርቦርድ ክጽዕን ኣይከኣለን", "@failedToLoadLeaderboard": {"description": "Error message displayed when the referral leaderboard cannot be loaded"}, "free": "ናፃ", "@free": {"description": "Text shown for free promotional content"}, "freeCreditAppbarTitle": "ናፃ ካርዲ", "@freeCreditAppbarTitle": {}, "freeCreditPageContent_1": "መአንገዲ ኮድኩም ንመሓዙትኩም & ቤተሰብኩም ኣባፅሑ።", "@freeCreditPageContent_1": {}, "freeCreditPageContent_2": "FroggyTalk ኣውሪዶም መአንገዲ ኮድኩም ተጠቒሞም ይመዝገቡ።", "@freeCreditPageContent_2": {}, "freeCreditPageContent_3": "እቲ ሰብ ንመጀመርታ እዋን ካርዲ ክገዝእ ከሎ {amount} ክረክቡ እዮም።", "@freeCreditPageContent_3": {}, "freeCreditPageHeadingText": "መሓዛኹም ኣንጊድኩም ናይ {amount} ኣታዊ  ርኸቡ", "@freeCreditPageHeadingText": {}, "freeCreditPageShareReferralLinkButtonText": "ናይ መአንገዲ መፈንጠሪ ንምልኣክ", "@freeCreditPageShareReferralLinkButtonText": {}, "freeCreditPageSubHeadingText": "ኣነ ዝኣንገድኩዎም ({amount})", "@freeCreditPageSubHeadingText": {}, "froggytalkCustomerLabel": "ዓሚል Froggytalk", "@froggytalkCustomerLabel": {"description": "Default label shown when user name is not available"}, "froggyTalkLovesYou": "FroggyTalk የፍቅረኩም ኣሎ።", "@froggyTalkLovesYou": {"description": "Affirmative message showing app's appreciation for users"}, "getNow": "ሕጂ ርኸቡ", "@getNow": {"description": "Text for a button or link to get an offer or promotion"}, "helpCenterAppbarTitle": "ማእኸል ሓገዝ", "@helpCenterAppbarTitle": {}, "helpCenterPageMenu_1": "ጥርዓን ወይ ሓሳብ ይሃቡ", "@helpCenterPageMenu_1": {}, "helpCenterPageMenu_2": "አብ መስመር ዝርከብ ወኪልና ይርከቡ", "@helpCenterPageMenu_2": {}, "helpCenterPageMenu_3": "ብተደጋጋሚ ዝሕተቱ ሕቶታት", "@helpCenterPageMenu_3": {}, "helpCenterPageMenu_4": "WhatsApp", "@helpCenterPageMenu_4": {"description": "Text that shows on the WhatsApp button on the help center page"}, "homeNavigationBarText": "መበገሲ ገፅ", "@homeNavigationBarText": {"description": "navbar text for home page"}, "inAppPurchaseLabel": "ዕድጊ ኣብ ውሽጢ ኣፕሊኬሽን", "@inAppPurchaseLabel": {"description": "Label indicating this is an in-app purchase from the app store"}, "internetConnectionAlertTextError": "እውይ! ካብ መስመር ወፂኦ እዮም ዘለዉ", "@internetConnectionAlertTextError": {}, "internetConnectionAlertTextSuccess": "ኹሉ ድሉው እዩ! መሊሶም ተራኺቦም ኣለዉ።", "@internetConnectionAlertTextSuccess": {}, "invalidInternationalPhoneFormat": "ዘይሰርሕ ዓይነት ዓለምለኻዊ ቁፅሪ ስልኪ", "@invalidInternationalPhoneFormat": {"description": "Error message shown when phone number format is invalid"}, "invalidPhoneNumber": "በይዘኦም ትክክል ዝኮነ ቁፅሪ ስልኪ የእትዉ", "@invalidPhoneNumber": {"description": "Error message shown when phone number is invalid"}, "keypadNavigationBarText": "መፅሐፊ", "@keypadNavigationBarText": {"description": "navbar text for keypad page"}, "landlineText": "ናይ መስመር ስልኪ", "@landlineText": {"description": "Text that shows when the number is a landline number"}, "languageChangeError": "ቋንቋታት ክቕይር ኣይከኣለን። በጃኹም ደጊምኩም ፈትኑ", "@languageChangeError": {"description": "Error message shown when language change fails"}, "languageSelectionDisclaimer": "ኣብ ታሕቲ ቋንቋ ክትቅይሩ ትኽእሉ ኢኹም።", "@languageSelectionDisclaimer": {"description": "Message informing users where they can find language settings"}, "languageSetupPageAppBarSubtitle": "3ይ ክፋል ካብ 3", "@languageSetupPageAppBarSubtitle": {"description": "Subtitle of the profile setup page showing progress"}, "loadingRadioStationsText": "ናይ ሬድዮ ጣብያታት ይጽዓን ኣሎ...", "@loadingRadioStationsText": {"description": "Message shown while radio stations are being loaded"}, "loadVoucherCardButtonText": "ንምምላእ", "@loadVoucherCardButtonText": {}, "loadVoucherCardErrorText": "ዝተሰሓሐተ ኮድ፤ መሊሶም ይፈትኑ", "@loadVoucherCardErrorText": {}, "loadVoucherCardLabelText": "ባዓል 10 ኣሃዝ ካይ ካርዲ ኮድ የእትዉ", "@loadVoucherCardLabelText": {}, "loadVoucherCardTitle": "ኮድ ካርዲ የእትዉ", "@loadVoucherCardTitle": {}, "loadVoucherCodeTitle": "ኮድ ካርዲ ንምእታዉ", "@loadVoucherCodeTitle": {"description": "Title shown on the load voucher code dialog"}, "loginPageAppBarSubtitle": "1ይ ክፋል ካብ 3", "@loginPageAppBarSubtitle": {"description": "Subtitle of the login page"}, "loginPageAppBarTitle": "መጀመሪ", "@loginPageAppBarTitle": {"description": "Title of the login page"}, "loginPageCheckboxLabel": "ዝተኣንገድኩሉ ኮድ አለኒ", "@loginPageCheckboxLabel": {}, "loginPageErrorMessage": "በዚ ቁፅሪ ስልኪ ዝተከፈተ WhatsApp የለን", "@loginPageErrorMessage": {}, "loginPagePhoneLabel": "ናይ WhatsApp ቁፅሮም የእትዉ", "@loginPagePhoneLabel": {}, "loginPagePhoneLabelDescription": "ብWhatsApp ኣቢሉ ናይ ሓደ ግዘ መረጋገፂ ኮድ ክለኣከሎም እዩ", "@loginPagePhoneLabelDescription": {}, "loginPagePhoneNumberError": "ቁፅሪ ስልኮም ኣይተማልአን። በይዞኦም የማልእዎ", "@loginPagePhoneNumberError": {}, "loginPageReferralLabel": "ዝተኣንገዱሉ ኮድ ንምእታዉ", "@loginPageReferralLabel": {}, "loginPageSubmitButton": "መረጋገፂ ክለኣከሎም ይሕተቱ", "@loginPageSubmitButton": {}, "minsOrMin": "{count, plural, zero{} one{1 ደቒቕ} other{{count} ደቃይቅ}}", "@minsOrMin": {"description": "Text that shows either '1 min' or '{count} mins' based on the count", "placeholders": {"count": {"type": "int", "example": "5"}}}, "mobileText": "ሞባይል", "@mobileText": {"description": "Text that shows when the number is a mobile number"}, "monthlyReferralBoard": "{month} መራሕቲ ፕሮሞ", "@monthlyReferralBoard": {"description": "Title for the monthly promotion leaderboard showing top referrers", "placeholders": {"month": {"type": "String", "example": "Jan'25"}}}, "moreAppbarTitle": "ሕሳብ", "@moreAppbarTitle": {}, "moreNavigationBarText": "ተወሳኺ", "@moreNavigationBarText": {"description": "navbar text for more page"}, "morePageAccountBalanceCardTitle": "ተረፍ ሕሳብ", "@morePageAccountBalanceCardTitle": {}, "morePageAppVersionMenuText": "ናይ መተግበሪ ሕታም", "@morePageAppVersionMenuText": {}, "morePageCallRatesMenuText": "ናይ ፃውዒት ተመናት", "@morePageCallRatesMenuText": {}, "morePageHelpCenterMenuText": "ማእኸል ሓገዝ", "@morePageHelpCenterMenuText": {}, "morePageLanguageMenuText": "ቋንቋ", "@morePageLanguageMenuText": {}, "morePageLoadVoucherMenuText": "ካርዲ ንምምላእ", "@morePageLoadVoucherMenuText": {}, "morePageLogoutMenuText": "ንምውፃእ", "@morePageLogoutMenuText": {}, "morePageProfileMenuText": "መግለፂ", "@morePageProfileMenuText": {}, "morePageRadioMenuText": "ሬድዮ", "@morePageRadioMenuText": {}, "morePageReferralCodeCardButtonText": "ተወሳኺ ሓበሬታ", "@morePageReferralCodeCardButtonText": {}, "morePageReferralCodeCardContent": "{numberOfPeople} ሰባት ናቶም መጋበዚ ኮድ ተጠቒሞም አለዉ", "@morePageReferralCodeCardContent": {}, "morePageReferralCodeCardDescription": "መሓዙቶምን ኣዝማዶምን ጋቢዞም {amount} ይርኸቡ", "@morePageReferralCodeCardDescription": {}, "morePageReferralCodeCardTitle": "መአንገዲ ኮድ", "@morePageReferralCodeCardTitle": {}, "nextTrack": "ዝቕፅል መዝሙር", "@nextTrack": {"description": "Text shown on button to play the next radio track or station"}, "noBuyCreditOptionsAvailable": "ሕሳብ ናይ ምግዛእ መማረፂ የለን", "@noBuyCreditOptionsAvailable": {"description": "Message that shows when there are no buy credit options available"}, "noFavoriteContactsMessage": "ዝተመረፁ መራክቦታት የለውን", "@noFavoriteContactsMessage": {"description": "Message shown when there are no favorite contacts to display"}, "noLanguagesAvailable": "ቋንቋ የለን", "@noLanguagesAvailable": {"description": "Message shown when no languages are available to select"}, "noNotificationsYet": "ዝበፅሐ መፍለጢ የለን", "@noNotificationsYet": {"description": "Text shown when there are no notifications"}, "noRadioStationsAvailableText": "ዝርከቡ ናይ ሬድዮ ጣብያታት የለውን", "@noRadioStationsAvailableText": {"description": "Message shown when there are no radio stations to display"}, "notEnoughCreditMessage": "እኩል ሕሳብ የብሎምን", "@notEnoughCreditMessage": {"description": "Message shown when user has insufficient credit for a call"}, "notification": "መፍለጢ", "@notification": {"description": "Text shown for a single notification"}, "notificationsAppBarTitle": "መፍለጢ", "@notificationsAppBarTitle": {"description": "Title of the notifications page"}, "notificationsEmptyMessage": "ዝበዝሖም ዝኮነ መፍለጢ የለን", "@notificationsEmptyMessage": {"description": "Message that shows when the user has no notifications"}, "notificationSettingsAppBarTitle": "መስርሕታት መፍለጢ", "@notificationSettingsAppBarTitle": {"description": "Title of the notification settings page"}, "notificationSettingsAutoDeleteNotificationText": "መፍለጢ ባዕሉ ይጥፋእ", "@notificationSettingsAutoDeleteNotificationText": {"description": "Text that shows to toggle the auto delete notification on the notification settings page"}, "notificationSettingsNotificationSoundText": "ናይ መፍለጢ ድምፂ", "@notificationSettingsNotificationSoundText": {"description": "Text that shows to toggle the notification sound on the notification settings page"}, "notificationsTabAllText": "ኹሎም", "@notificationsTabAllText": {"description": "Text that shows on the all tab on the notifications page"}, "notificationsTabUnreadText": "ዘይተነበቡ", "@notificationsTabUnreadText": {"description": "Text that shows on the unread tab on the notifications page"}, "notificationsTimeAgo": "ቅድሚ {time} {time, plural, one{{duration}} other{{duration}}} \n", "@notificationsTimeAgo": {"description": "Text that shows the time since the notification was received", "placeholders": {"time": {"type": "int", "example": "2"}, "duration": {"type": "String", "example": "hours"}}}, "nowPlaying": "ሕጂ ምጽዋት", "@nowPlaying": {"description": "Text shown to indicate the currently playing radio track or station"}, "okButtonText": "እሺ", "@okButtonText": {"description": "Text shown on OK confirmation button"}, "onboardingPageFooterAndText": "ከምኡውን", "@onboardingPageFooterAndText": {"description": "Text that shows between the terms and conditions link and the privacy policy link on the onboarding page"}, "onboardingPageFooterPrivacy": "ፖሊሲ ውልቃዊ ጕዳያት", "@onboardingPageFooterPrivacy": {"description": "Text that shows below the submit button on the onboarding page"}, "onboardingPageFooterTermsConditionsText": "ውዕልን ኵነታትን", "@onboardingPageFooterTermsConditionsText": {"description": "Text that shows below the submit button on the onboarding page"}, "onboardingPageFooterText": "ንምጅማር ዝብል ብምጥዋቕ ብተወሳኪ ዝቅበልዎ", "@onboardingPageFooterText": {"description": "Text that shows below the submit button on the onboarding page"}, "onboardingPageSliderHeader1": "ብዙሕ ቋንቋታት\n", "@onboardingPageSliderHeader1": {"description": "Header text for the first slide on the onboarding page"}, "onboardingPageSliderHeader2": "ኣብ መላእ ዓለም ናብ ዝመረፅዎ ሞባይል ወይ ናይ መስመር ስልኪ ይደውሉ", "@onboardingPageSliderHeader2": {"description": "Header text for the second slide on the onboarding page"}, "onboardingPageSliderHeader3": "ቀሊል ክፍሊት", "@onboardingPageSliderHeader3": {"description": "Header text for the third slide on the onboarding page"}, "onboardingPageSliderText1": "FroggyTalk ብመረፅዎ ቋንቋ ንኣብነት ብትግርኛ፣ አምሓርኛ፣ ሃውሳ ወዘተ ክግልገሉ የክእል።", "@onboardingPageSliderText1": {"description": "Text for the first slide on the onboarding page"}, "onboardingPageSliderText2": "ኣብ ዝኾነ ክፍሊ ዓለም ናብ ዝርከብ ሞባይል ወይ ናይ መስመር ስልኪ ይደውሉ። እቶም ተቐባሊ ዘመናዊ ሞባይል ኮነ ኢንተርነት ኣየድልዮምን።", "@onboardingPageSliderText2": {"description": "Text for the second slide on the onboarding page"}, "onboardingPageSliderText3": "ኣብ ከባቢኣኦም ብዝጥቀምዎ ገንዘብ ናይ መደወሊ ካርድ ብዝመረፅዎ ሜላ ኽፍሊት ይዓድጉ።", "@onboardingPageSliderText3": {"description": "Text for the third slide on the onboarding page"}, "onboardingPageSubmitButtonText": "ንምጅማር", "@onboardingPageSubmitButtonText": {"description": "Text for the submit button on the onboarding page"}, "outOfCreditLabel": "ተረፍ ሕሳብ የብሎምን", "@outOfCreditLabel": {"description": "Label shown when user has no remaining credit"}, "paymentFailureAppBarTitle": "ኣይተሳክዐን", "@paymentFailureAppBarTitle": {"description": "Title shown in app bar of payment failure page"}, "paymentFailureHomeButton": "መበገሲ ገፅ", "@paymentFailureHomeButton": {"description": "Text for button to return to home page"}, "paymentFailureMessage": "ናይ {amount} ክፍሊቶም ኣይተሳክዐን", "@paymentFailureMessage": {"description": "Message shown when payment fails, includes amount", "placeholders": {"amount": {"type": "String", "example": "$50.00"}}}, "paymentFailureTitle": "ክፍሊት ኣይተሳክዐን", "@paymentFailureTitle": {"description": "Main title shown when payment fails"}, "paymentFailureTryAgainButton": "መሊሶም ንምፍታን", "@paymentFailureTryAgainButton": {"description": "Text for button to retry payment"}, "paymentOptionsAppBarTitle": "ናይ ክፍሊት መማረፅታት", "@paymentOptionsAppBarTitle": {"description": "Title of the payment options page"}, "paymentOptionsAutoCreditDescription": "እዚ ድማ ሕሳቦም ትሕቲ {minAmount} እንትኮን ባዕሉ ናብ ሒሳቦም {amount} ከእትወሎም አሎ።", "@paymentOptionsAutoCreditDescription": {"description": "Description of the auto credit payment method"}, "paymentOptionsAutoCreditTitle": "ዓርሱ ዝመልእ", "@paymentOptionsAutoCreditTitle": {"description": "Title of the auto credit payment method"}, "paymentOptionsSelectPaymentMethod": "ዝደለይዎ ሜላ ክፍሊት ይምረፁ", "@paymentOptionsSelectPaymentMethod": {"description": "Text that shows above the payment method selection dropdown"}, "paymentOptionsSummaryAmountToCreditText": "ኣታዊ ዝኮን መጠን", "@paymentOptionsSummaryAmountToCreditText": {"description": "Text that shows the amount to be credited on the payment options page"}, "paymentOptionsSummaryDiscountButtonText": "ንምትግባር", "@paymentOptionsSummaryDiscountButtonText": {"description": "Text for the apply discount button on the payment options page"}, "paymentOptionsSummaryDiscountPlaceholder": "ናይ ቅናሽ ኮድ የእትዉ(እንተሃልይዎም)", "@paymentOptionsSummaryDiscountPlaceholder": {"description": "Placeholder text for the discount code input field on the payment options page"}, "paymentOptionsSummaryDiscountText": "ቅናሽ", "@paymentOptionsSummaryDiscountText": {"description": "Text that shows the discount on the payment options page"}, "paymentOptionsSummaryTotalPaymentText": "ጠቕላላ ክፍሊት", "@paymentOptionsSummaryTotalPaymentText": {"description": "Text that shows the total payment on the payment options page"}, "paymentOptionsSummaryVatFeesText": "VAT + ክፍሊት", "@paymentOptionsSummaryVatFeesText": {"description": "Text that shows the VAT and fees on the payment options page"}, "paymentSummaryAmountToCreditLabel": "ኣታዊ ዝግበር መጠን", "@paymentSummaryAmountToCreditLabel": {"description": "በክፍያ ማጠቃለያ ውስጥ የሚቀርብ መጠን ምልክት"}, "paymentSummaryDiscountLabel": "ቅናሽ", "@paymentSummaryDiscountLabel": {"description": "በክፍያ ማጠቃለያ ውስጥ ቅናሽ ምልክት"}, "paymentSummaryPlayStoreFeeLabel": "  + {percentage}% ኮሚሽንን ቫት", "@paymentSummaryPlayStoreFeeLabel": {"description": "Label showing the Play Store fee percentage and VAT in payment summary", "placeholders": {"percentage": {"type": "String", "example": "30"}}}, "paymentSummaryTotalLabel": "ጠቅላላ ክፍሊት", "@paymentSummaryTotalLabel": {"description": "በክፍያ ማጠቃለያ ውስጥ ጠቅላላ ክፍያ ምልክት"}, "paymentSummaryVatFeesLabel": "VAT + ክፍሊታት", "@paymentSummaryVatFeesLabel": {"description": "በክፍያ ማጠቃለያ ውስጥ ተጨማሪ ክፍያዎች ምልክት"}, "payWithApplePay": "Apple Pay", "@payWithApplePay": {"description": "Text shown on button to pay using Apple Pay"}, "payWithGooglePay": "Google Pay", "@payWithGooglePay": {"description": "Text shown on button to pay using Google Pay"}, "perMinRate": "{rate}/ደቒቓ\n", "@perMinRate": {"description": "Rate per minute for a call", "placeholders": {"rate": {"type": "String", "example": "$10"}}}, "perMinRateSingle": "/ደቒቓ", "@perMinRateSingle": {"description": "Rate per minute for a call in short form"}, "perMinuteRate": "{rate}/ደቒቓ\n", "@perMinuteRate": {"description": "Rate per minute for a call", "placeholders": {"rate": {"type": "String", "example": "$10"}}}, "perMinuteSlashLabel": "{min}/ደቃይቅ", "@perMinuteSlashLabel": {"description": "Label showing '/minutes' for call rate displays", "placeholders": {"min": {"type": "String", "example": "5"}}}, "permissionButtonAccept": "ፍቃድ ንምሃብ", "@permissionButtonAccept": {"description": "Text for the accept button on the permission request dialog"}, "permissionButtonSkip": "ንምስጋር", "@permissionButtonSkip": {"description": "Text for the skip button on the permission request dialog"}, "permissionDeniedMessage": "ፍቓድ ተኣግዶሎም ኣሎ፣ ኣብ መተግበሪ ቅንብሮታት እንዲከፍቱ በጃኹም", "@permissionDeniedMessage": {"description": "Message shown when a permission is denied by the user"}, "permissionForCameraTitle": "ስእልታትን ቪድዮታትን ንምስኣል ፍቓድ የድልየና እዩ", "@permissionForCameraTitle": {"description": "Title for the camera permission request dialog"}, "permissionForContactListTitle": "FroggyTalk ናብ ዝርዝር መራክቦታትኩም / መዝገብ ቁፅሪ ስልክታት ክኣቱ ፍቃድ ይሃቡ", "@permissionForContactListTitle": {"description": "Title for the contact list permission request dialog"}, "permissionForMicrophoneTitle": "FroggyTalk ማይክሮፎን ክጥቀም ብምግባር ደወላት ንምትእንጋድ ይፍቀዱ", "@permissionForMicrophoneTitle": {"description": "Title for the microphone permission request dialog"}, "permissionForNotificationTitle": "FroggyTalk ምምሕያሻት ክሰደልኩም ፍቃድ ይሃቡ", "@permissionForNotificationTitle": {"description": "Title for the notification permission request dialog"}, "permissionForStorageTitle": "ንዝተወሰነ ግዜ ኣብ ሞባይሎም ሰነዳት ክነቕምጥ ፍቓድ የድልየና እዩ", "@permissionForStorageTitle": {"description": "Title for the storage permission request dialog"}, "permissionPermanentlyDeniedMessage": "ፍቓድ ብፍፁም ተኣግዶሎም ኣሎ፣ ኣብ መተግበሪ ቅንብሮታት እንዲከፍቱ በጃኹም", "@permissionPermanentlyDeniedMessage": {"description": "Message shown when a permission is permanently denied by the user"}, "perM_Rate": "{rate}/ደቒቓ\n", "@perM_Rate": {"description": "Rate per minute for a call", "placeholders": {"rate": {"type": "String", "example": "$10"}}}, "phoneNumberStartRule": "በይዘኦም ብ + ወይ 00 ጀሚሮም ይፅሓፉ", "@phoneNumberStartRule": {"description": "Error message shown when phone number doesn't start with + or 00"}, "playRadio": "ሬድዮ ንምጅማር", "@playRadio": {"description": "Text shown on button to start playing the radio"}, "previousTrack": "ዝሓለፈ መዝሙር", "@previousTrack": {"description": "Text shown on button to play the previous radio track or station"}, "proceedToPaymentButtonText": "ንምቅፃል", "@proceedToPaymentButtonText": {"description": "Text for the proceed to payment button on the buy credit page"}, "profileAppbarTitle": "መግለፂ", "@profileAppbarTitle": {}, "profileDeleteAccountButtonText": "ሕሳቦም ንምድምሳስ", "@profileDeleteAccountButtonText": {}, "profileDeleteAccountCardContent": "በይዘኦም ሕሳቦም ክድምስሱ ከምዝደልዩ የረጋግፁ። ኣብ ሒሳቦም ዝሎ ተረፍ ገንዘብ  እውን ሓቢዉ ክጠፍእ እዩ።", "@profileDeleteAccountCardContent": {}, "profileDeleteAccountCardTitle": "ሕሳቦም ንምድምሳስ", "@profileDeleteAccountCardTitle": {}, "profileLabel": "መግለፂ", "@profileLabel": {"description": "Label used for profile sections"}, "profileLabelChangeLocation": "ቦታ ንምቕያር", "@profileLabelChangeLocation": {}, "profileLabelEmailAddress": "ኢመይል ኣድራሻ የእቱዉ", "@profileLabelEmailAddress": {}, "profileLabelFullName": "ምሉእ ስም የእትዉ", "@profileLabelFullName": {"description": "profile<PERSON>abelFull<PERSON>ame"}, "profileUpdatedSuccessfullyMessage": "መግለፂ ብዝተሳክዐ መልክዑ ተመሓይሹ", "@profileUpdatedSuccessfullyMessage": {"description": "Message shown when profile is updated successfully"}, "profileUpdateFailedMessage": "መግለፂ ምምሕያሽ ኣይተክኣለን", "@profileUpdateFailedMessage": {"description": "Message shown when profile update fails"}, "quickAdvertSubtitle": "ኣብ መላእ ዓለም ንዝርከቡ ኣዝማድኩም ናይ ኣየር ሰዓት ዓድጉሎም", "@quickAdvertSubtitle": {"description": "Subtitle for the quick advert on the home page"}, "quickAdvertTitle": "ናብ 140+ ሃገራት ካርዲ ይስደዱ", "@quickAdvertTitle": {"description": "Title for the quick advert on the home page"}, "radioAppBarTitle": "ሬድዮ", "@radioAppBarTitle": {}, "radioComingSoonText": "ሬድዮ ኣብ ቀረባ እዋን ክጅምር እዩ !!!", "@radioComingSoonText": {"description": "Text shown when radio feature is not yet available"}, "radioPageChooseChannelButtonText": "ጣብያ ንምምራፅ", "@radioPageChooseChannelButtonText": {}, "radioPageNoChannelsText": "ዝተረኸቡ ጣብያታት የለውን", "@radioPageNoChannelsText": {}, "radioPageSearchPlaceholder": "ጣብያ ንምምራፅ", "@radioPageSearchPlaceholder": {}, "ratesNavigationBarText": "ተመን", "@ratesNavigationBarText": {"description": "navbar text for rates page"}, "recentCallsText": "ናይ ቀረባ እዋን ደወላይ", "@recentCallsText": {"description": "Text that shows above the recent calls list on the home page"}, "rechargeButtonText": "ንምምላእ", "@rechargeButtonText": {"description": "Text shown on recharge button"}, "referAndEarn": "ኣንዲጎም ኣታዊ ይርከቡ", "@referAndEarn": {"description": "Text shown for refer and earn promotional text"}, "referralCardButtonText": "ናይ ናፃ ካርዲ", "@referralCardButtonText": {"description": "Text for the referral card button"}, "referralCardDescription1": "ሰብ ኣንጊዶም ናይ {percentageAmount} ናፃ ካርዲ ይርኽቡ።", "@referralCardDescription1": {"description": "Message showing the referral benefit", "placeholders": {"percentageAmount": {"type": "String", "example": "10%"}}}, "referralCardDescription2": "{count, plural, zero{መአንገዲ ኮዶም ዝተጠቐሞ ሰብ የለን።} one{ሓደ ሰብ መአንገዲ ኮዶም ተጠቑሞም ኣለዉ።} other{{numberOfPeople} ሰባት መአንገዲ ኮዶም ተጠቑሞም ኣለዉ።}}", "@referralCardDescription2": {"description": "Message showing how many people have used the referral code, with pluralization.", "placeholders": {"numberOfPeople": {"type": "int", "example": "2"}}}, "referralCardTitle": "መአንገዲ ኮድ", "@referralCardTitle": {"description": "Title for the referral card"}, "referralCodeOnCopyActionResponse": "FroggyTalk ንግዚኡ ተቀሚጡ ኣሎ", "@referralCodeOnCopyActionResponse": {}, "refreshText": "ንምሕዳስ", "@refreshText": {"description": "Text shown on button to refresh or reload content"}, "restorePurchases": "ዕድጊታት ምምላስ", "@restorePurchases": {"description": "Button text for restoring previous in-app purchases"}, "retry": "ዳግማይ ፈትኑ", "@retry": {"description": "Text for button to retry a failed operation"}, "saveAndProceedToPaymentButtonText": "መረዳእታ ንምዕቃብ & ናብ ክፍሊት ንምቅፃል", "@saveAndProceedToPaymentButtonText": {"description": "Text shown on button to save profile changes and proceed to payment"}, "saveChangesButtonText": "ለውጥታት ንምዕቃብ", "@saveChangesButtonText": {}, "savedSuccessfullyMessage": "ብዝተሳክዐ መልክዑ ተዓቂቡ", "@savedSuccessfullyMessage": {"description": "Message shown when changes are saved successfully"}, "searchCountryPlaceholder": "ሃገር ንምድላይ", "@searchCountryPlaceholder": {"description": "Placeholder text for the search input field on the country selection page"}, "searchFavouriteContactMessage": "ዝተመረፁ መራክቦታት ንምእላሽ", "@searchFavouriteContactMessage": {"description": "Placeholder text for searching favorite contacts"}, "searchForCountryNoResults": "ውፅኢት ኣይተረኽበን", "@searchForCountryNoResults": {"description": "Text that shows when no results are found for the search input field on the country selection page"}, "searchForCountryPlaceholder": "ሃገር ንምድላይ", "@searchForCountryPlaceholder": {"description": "Placeholder text for the search input field on the country selection page"}, "searchRecentCallsMessage": "ኣብ ቀረባ እዋን ዝተገበሩ ደወላት ንምእላሽ", "@searchRecentCallsMessage": {"description": "Placeholder text for searching recent calls"}, "secsOrSec": "{count, plural, zero{} one{1 ሰከንድ} other{{count} ሰከንድ}}", "@secsOrSec": {"description": "Text that shows either '1 sec' or '{count} secs' based on the count", "placeholders": {"count": {"type": "int", "example": "10"}}}, "securedByStripe": "ብስትራይፕ ዝተሓለወ", "@securedByStripe": {"description": "Text showing that payment is secured by Stripe payment processor"}, "selectCountryPlaceholder": "ሃገር ንምምራፅ", "@selectCountryPlaceholder": {"description": "Placeholder text for the country selection dropdown"}, "selectPreferredLanguage": "ዝደለኽምዎ ቋንቋ ምረጹ", "@selectPreferredLanguage": {"description": "Subtitle for the language selection modal"}, "sendCreditButtonText": "ካርዲ ንምልኣኽ", "@sendCreditButtonText": {"description": "Text for the send credit button on the home page"}, "settings": "መስርሕታት", "@settings": {"description": "Text shown for settings"}, "shareReferralText": "ናይ FroggyTalk መተግበሪ {appLink} ኣውሪዶም ናተይ መአንገዲ ኮድ {referralCode} ብምጥቃም ይመዝገቡ። ካብ ኩሉ ዝረከሰ ናይ ዋባ ተመንን ዓለምለኻዊ ደወል ምግባር ዘክእልን እዩ።", "@shareReferralText": {"description": "Text shown when sharing referral code", "placeholders": {"appLink": {"type": "String", "example": "https://link-to.app/AHA423kdwG"}, "referralCode": {"type": "String", "example": "ABC123"}}}, "somethingWentWrongMessage": "ስሕተት ተፈጢሩ ኣሎ። በይዘኦም መሊሶም ይፈትኑ።", "@somethingWentWrongMessage": {"description": "Message that shows when an error occurs"}, "stopRadio": "ሬድዮ ንምድምዳም", "@stopRadio": {"description": "Text shown on button to stop the currently playing radio"}, "storePrice": "ዋጋ መደብር", "@storePrice": {"description": "Label for the price listed in the app store"}, "termsAndConditionsApply": "ውዕላትን ቅጥዕታትን ይምልከቱ", "@termsAndConditionsApply": {"description": "Text indicating that terms and conditions apply to an offer or promotion"}, "timeLeftModalMessage": "{days} ናይቲ ናይ ገንዘብ ሽልማት ተዓወቲ ንምዃን መዓልታት ተሪፍዎም ኣሎ።ተወከሱ  {count} ንሰባት እሞ ቁጽሪ 1 ኩኑ", "@timeLeftModalMessage": {"description": "Message showing days left and number of referrals needed", "placeholders": {"days": {"type": "String", "example": "25"}, "count": {"type": "int", "example": "30"}}}, "timeLeftModalTitle": "{days} መዓልታት ተሪፎም ", "@timeLeftModalTitle": {"description": "Title showing the number of days left in the modal", "placeholders": {"days": {"type": "String", "example": "2"}}}, "tryAgainText": "መሊሶም ይፈትኑ", "@tryAgainText": {"description": "Text shown on button to retry an action that failed"}, "unknownErrorText": "ዘይፍለጥ ስሕተት ተፈጢሩ", "@unknownErrorText": {"description": "Generic message shown when an unspecified error occurs"}, "unread": "ዘይተነበበ", "@unread": {"description": "Text shown for unread notifications"}, "updateLanguagePrompt": "በይዘኦም ቋንቋኦም ንምምሕያሽ ኣብ ታሕቲ ዘሎ ማዕጠንቲ የጠውቁ", "@updateLanguagePrompt": {"description": "Instruction text prompting user to update their language setting"}, "updateProfileButtonText": "መግለፂኦም ንምምሕያሽ", "@updateProfileButtonText": {"description": "Text shown on button to update user profile"}, "updateYourLanguage": "ቋንቋኦም ንምምሕያሽ።", "@updateYourLanguage": {"description": "Button or action text for language update functionality"}, "upgradeDialogButtonNo": "ሐዚ ኣይኮነን", "@upgradeDialogButtonNo": {"description": "እትም ማስተካከልን ለመሰረዝ የሚጠቀምበት አዝራር ጽሑፍ"}, "upgradeDialogButtonYes": "ንምምሕያሽ", "@upgradeDialogButtonYes": {"description": "እትም ማስተካከልን ለመቀጠል የሚጠቀምበት አዝራር ጽሑፍ"}, "upgradeDialogMessage": "ዝተመሓየሸ ናይ FroggyTalk መተግበሪ ድሉው እዩ። ሐዚ ክፅዕኑ ይደልዩ?", "@upgradeDialogMessage": {"description": "አዳዲስ እትም ሲኖር ተጠቃሚውን ለመዘመን የሚጠይቅ መልእክት"}, "upgradeDialogTitle": "ሓድሽ ዝወፅአ", "@upgradeDialogTitle": {"description": "አዳዲስ እትም ሲኖር የሚታይ እትም ርዕስ"}, "validationCouponCodeIncomplete": "ዘይተማልአ ወይ ዝተሰሓሐተ ናይ ቅናሽ ኮድ ", "@validationCouponCodeIncomplete": {"description": "Error message shown when coupon code length or format is invalid"}, "validationCouponInvalid": "በይዘኦም ትክክል ዝኮነ ኮድ ቅናሽ የእትዉ", "@validationCouponInvalid": {"description": "Error message shown when coupon code is invalid"}, "validationEmailInvalid": "ዝተሰሓሐተ ኣድራሻ ኢሜይል።", "@validationEmailInvalid": {"description": "Error message shown when email format is invalid"}, "validationFieldIsRequired": "{fieldName} ኣድላዩ እዩ", "@validationFieldIsRequired": {"description": "Generic required field error message", "placeholders": {"fieldName": {"type": "String", "example": "Email"}}}, "validationLengthError": "{fieldName} ካብ {minLength} ክሳብ {maxLength} ፊደላት ክኸውን ኣለዎ", "@validationLengthError": {"description": "Error message for fields that don't meet length requirements", "placeholders": {"fieldName": {"type": "String", "example": "Password"}, "minLength": {"type": "int", "example": "8"}, "maxLength": {"type": "int", "example": "16"}}}, "validationMinLengthError": "{fieldName} ብንኡሱ {minLength} ዝንውሓቱ ክከውን ኣለዎ", "@validationMinLengthError": {"description": "Error message for fields that don't meet minimum length requirement", "placeholders": {"fieldName": {"type": "String", "example": "Password"}, "minLength": {"type": "int", "example": "8"}}}, "validationPhoneNumberIncomplete": "ዘይተማልአ ቁፅሪ ስልኪ።", "@validationPhoneNumberIncomplete": {"description": "Error message shown when phone number is incomplete"}, "viewButtonText": "ንምርኣይ", "@viewButtonText": {"description": "Text for the view action button on page"}, "viewContactDetailAppBarTitle": "ዝርዝር መራክቦ", "@viewContactDetailAppBarTitle": {"description": "Title of the contact detail page"}, "voucherLoadedSuccessMessage": "እቲ ካርዲ ብዝተሳክዐ መልክዑ ኣቲዉ", "@voucherLoadedSuccessMessage": {"description": "Message shown when voucher is successfully loaded"}, "welcomeBackText": "እንቋዕ ተመለስኩም, {firstName}", "@welcomeBackText": {"description": "Greeting message shown to returning users", "placeholders": {"firstName": {"type": "String", "example": "<PERSON>"}}}, "googlePay": "Google Pay", "@googlePay": {"description": "Text shown on button to pay using Google Pay"}, "appStoreCost": "+ {value} ዋጋ AppStore", "@appStoreCost": {"description": "Label showing the cost charged by the App Store including fees and VAT", "placeholders": {"value": {"type": "String", "example": "USD 2.99"}}}}