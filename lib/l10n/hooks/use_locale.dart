import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:navigation/navigation.dart';

/// A custom Flutter hook that provides access to the current locale's
/// localizations.
///
/// This hook uses the `useContext` function to obtain the current
/// `BuildContext` and then retrieves the `AppLocalizations` instance
/// associated with that context. The `AppLocalizations` class typically
/// contains localized strings and other locale-specific resources for
/// the application.
///
/// This hook is useful for accessing localized resources within
/// functional components or other places where the `BuildContext`
/// is not directly available.
///
/// Returns:
///   An instance of `AppLocalizations` containing the localized
///   resources for the current locale.
///
/// Example usage:
/// ```dart
/// final localizations = useLocale();
/// print(localizations.someLocalizedString);
/// ```
AppLocalizations useLocale() {
  final context = useContext();
  return context.l10n;
}

/// A Flutter Hook that provides access to localized strings and resources.
///
/// This hook is a wrapper around the [useLocale] hook that returns an
/// [AppLocalizations] instance. It allows components to access the current
/// app's localizations in a more concise way.
///
/// Example usage:
/// ```dart
/// final l10n = useLocalizations();
/// return Text(l10n.someTranslatedString);
/// ```
///
/// The hook automatically handles locale changes and will trigger a rebuild
/// of the widget when the app's locale changes.
///
/// Returns:
/// - An [AppLocalizations] instance that provides access to all localized
///   strings and resources defined in the app's localization files.
///
/// Note: This hook must be used within a widget that is wrapped with a
/// [MaterialApp] or [WidgetsApp] that has localization delegates properly
/// configured.
///
/// See also:
/// * [AppLocalizations], which contains all the localized strings
/// * [MaterialApp.localizationsDelegates], where localization delegates are
///   configured
/// * [useLocale], the underlying hook that this function wraps
AppLocalizations useLocalizations() {
  return useLocale();
}

/// Returns localized values without requiring a widget's build context.
///
/// This hook provides a convenient way to access localized strings and values
/// from [AppLocalizations] in places where getting a [BuildContext] would be
/// cumbersome or impossible, such as in business logic or service layers.
///
/// It retrieves the current [AppLocalizations] instance using the globally
/// available [FroggyRouter.context], which must be properly initialized and
/// not null when this hook is called.
///
/// Example usage:
/// ```dart
/// final l10n = useLocaleWithoutContext();
/// print(l10n.someTranslatedString);
/// ```
///
/// Throws:
/// * A [StateError] if [FroggyRouter.context] is null
///
/// Returns:
/// * An [AppLocalizations] instance for the current locale
///
/// Note: This hook should be used carefully as it bypasses Flutter's normal
/// context-based localization system. Consider using the context-based
/// `AppLocalizations.of(context)` when possible.
AppLocalizations useLocalizationsWithoutContext() {
  final context = FroggyRouter.context;

  if (context == null) {
    throw StateError('FroggyRouter.context is null. Ensure that the '
        'FroggyRouter is initialized and has a valid context.');
  }

  return AppLocalizations.of(context);
}
