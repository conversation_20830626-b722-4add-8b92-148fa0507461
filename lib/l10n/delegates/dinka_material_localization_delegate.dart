import 'package:flutter/material.dart';
import 'package:froggytalk/l10n/l10n.dart';

class DinkaMaterialLocalizations extends DefaultMaterialLocalizations {
  DinkaMaterialLocalizations(this._appLocalizations);
  final AppLocalizations _appLocalizations;

  String get appTitle => _appLocalizations.appTitle;
  String get appLanguageAppbarTitle => _appLocalizations.appLanguageAppbarTitle;
  String get buyCreditAppBarTitle => _appLocalizations.buyCreditAppBarTitle;
  String get callingAppBarTitle => _appLocalizations.callingAppBarTitle;
  String get callRatesAppBarTitle => _appLocalizations.callRatesAppBarTitle;
  String get chatWithLiveAgentAppbarTitle =>
      _appLocalizations.chatWithLiveAgentAppbarTitle;
  String get confirmationAppBarTitle =>
      _appLocalizations.confirmationAppBarTitle;
  String get dialerAppBarTitle => _appLocalizations.dialerAppBarTitle;
  String get enterOtpPageAppBarTitle =>
      _appLocalizations.enterOtpPageAppBarTitle;
  String get freeCreditAppbarTitle => _appLocalizations.freeCreditAppbarTitle;
  String get helpCenterAppbarTitle => _appLocalizations.helpCenterAppbarTitle;
  String get loginPageAppBarTitle => _appLocalizations.loginPageAppBarTitle;
  String get moreAppbarTitle => _appLocalizations.moreAppbarTitle;
  String get notificationsAppBarTitle =>
      _appLocalizations.notificationsAppBarTitle;
  String get notificationSettingsAppBarTitle =>
      _appLocalizations.notificationSettingsAppBarTitle;
  String get paymentOptionsAppBarTitle =>
      _appLocalizations.paymentOptionsAppBarTitle;
  String get profileAppbarTitle => _appLocalizations.profileAppbarTitle;
  String get radioAppBarTitle => _appLocalizations.radioAppBarTitle;
  String get viewContactDetailAppBarTitle =>
      _appLocalizations.viewContactDetailAppBarTitle;
  String get morePageLanguageMenuText =>
      _appLocalizations.morePageLanguageMenuText;
  String get contactsFavouriteCallsTabText =>
      _appLocalizations.contactsFavouriteCallsTabText;

  // @override
  String Function(String month) get monthlyReferralBoard =>
      _appLocalizations.monthlyReferralBoard;

  @override
  String get closeButtonTooltip => _appLocalizations.closeButtonText;

  @override
  String get okButtonLabel => _appLocalizations.okButtonText;
}

class DinkaMaterialLocalizationsDelegate
    extends LocalizationsDelegate<MaterialLocalizations> {
  const DinkaMaterialLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    // Support the Dinka locale ('din')
    return locale.languageCode == 'din';
  }

  @override
  Future<MaterialLocalizations> load(Locale locale) async {
    final appLocalizations = await AppLocalizations.delegate.load(locale);
    return DinkaMaterialLocalizations(appLocalizations);
  }

  @override
  bool shouldReload(DinkaMaterialLocalizationsDelegate old) => false;
}
