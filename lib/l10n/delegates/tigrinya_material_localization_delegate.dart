import 'package:flutter/material.dart';
import 'package:froggytalk/l10n/l10n.dart';

class TigrinyaMaterialLocalizations extends DefaultMaterialLocalizations {
  TigrinyaMaterialLocalizations(this._appLocalizations);
  final AppLocalizations _appLocalizations;

  String get appTitle => _appLocalizations.appTitle;
  String get appLanguageAppbarTitle => _appLocalizations.appLanguageAppbarTitle;
  String get buyCreditAppBarTitle => _appLocalizations.buyCreditAppBarTitle;
  String get callingAppBarTitle => _appLocalizations.callingAppBarTitle;
  String get callRatesAppBarTitle => _appLocalizations.callRatesAppBarTitle;
  String get chatWithLiveAgentAppbarTitle =>
      _appLocalizations.chatWithLiveAgentAppbarTitle;
  String get confirmationAppBarTitle =>
      _appLocalizations.confirmationAppBarTitle;
  String get dialerAppBarTitle => _appLocalizations.dialerAppBarTitle;
  String get enterOtpPageAppBarTitle =>
      _appLocalizations.enterOtpPageAppBarTitle;
  String get freeCreditAppbarTitle => _appLocalizations.freeCreditAppbarTitle;
  String get helpCenterAppbarTitle => _appLocalizations.helpCenterAppbarTitle;
  String get loginPageAppBarTitle => _appLocalizations.loginPageAppBarTitle;
  String get moreAppbarTitle => _appLocalizations.moreAppbarTitle;
  String get notificationsAppBarTitle =>
      _appLocalizations.notificationsAppBarTitle;
  String get notificationSettingsAppBarTitle =>
      _appLocalizations.notificationSettingsAppBarTitle;
  String get paymentOptionsAppBarTitle =>
      _appLocalizations.paymentOptionsAppBarTitle;
  String get profileAppbarTitle => _appLocalizations.profileAppbarTitle;
  String get radioAppBarTitle => _appLocalizations.radioAppBarTitle;
  String get viewContactDetailAppBarTitle =>
      _appLocalizations.viewContactDetailAppBarTitle;
  String get morePageLanguageMenuText =>
      _appLocalizations.morePageLanguageMenuText;
  String get contactsFavouriteCallsTabText =>
      _appLocalizations.contactsFavouriteCallsTabText;

  String Function(String month) get monthlyReferralBoard =>
      _appLocalizations.monthlyReferralBoard;
  // String get callRatesAppBarTitle => _appLocalizations.callRatesAppBarTitle;
  // String get callRatesAppBarTitle => _appLocalizations.callRatesAppBarTitle;
  // String get callRatesAppBarTitle => _appLocalizations.callRatesAppBarTitle;
  // String get callRatesAppBarTitle => _appLocalizations.callRatesAppBarTitle;
  // String get callRatesAppBarTitle => _appLocalizations.callRatesAppBarTitle;
  // String get callRatesAppBarTitle => _appLocalizations.callRatesAppBarTitle;
  // String get callRatesAppBarTitle => _appLocalizations.callRatesAppBarTitle;
  // String get callRatesAppBarTitle => _appLocalizations.callRatesAppBarTitle;
  // String get callRatesAppBarTitle => _appLocalizations.callRatesAppBarTitle;
  // String get callRatesAppBarTitle => _appLocalizations.callRatesAppBarTitle;
  // String get callRatesAppBarTitle => _appLocalizations.callRatesAppBarTitle;
  // String get callRatesAppBarTitle => _appLocalizations.callRatesAppBarTitle;
  // String get callRatesAppBarTitle => _appLocalizations.callRatesAppBarTitle;
  // String get callRatesAppBarTitle => _appLocalizations.callRatesAppBarTitle;
  // String get callRatesAppBarTitle => _appLocalizations.callRatesAppBarTitle;
  // String get callRatesAppBarTitle => _appLocalizations.callRatesAppBarTitle;
  // String get callRatesAppBarTitle => _appLocalizations.callRatesAppBarTitle;
  // String get callRatesAppBarTitle => _appLocalizations.callRatesAppBarTitle;
  // String get callRatesAppBarTitle => _appLocalizations.callRatesAppBarTitle;
  // String get callRatesAppBarTitle => _appLocalizations.callRatesAppBarTitle;
  // String get moreAppbarTitle => _appLocalizations.moreAppbarTitle;
}

class TigrinyaMaterialLocalizationsDelegate
    extends LocalizationsDelegate<MaterialLocalizations> {
  const TigrinyaMaterialLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    // Ensure that the delegate supports
    // the same locales as your AppLocalizations
    return AppLocalizations.supportedLocales.contains(locale);
  }

  @override
  Future<MaterialLocalizations> load(Locale locale) async {
    // Load your custom AppLocalizations for the given locale
    final appLocalizations = await AppLocalizations.delegate.load(locale);
    return TigrinyaMaterialLocalizations(appLocalizations);
  }

  @override
  bool shouldReload(TigrinyaMaterialLocalizationsDelegate old) => false;
}
