// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get accountBalanceCardTitle => 'Balance';

  @override
  String get all => 'All';

  @override
  String get allowAccessButtonText => 'Allow Access to Perform this Required Action';

  @override
  String get appBootingTextBelow => 'Helping communities stay connected';

  @override
  String get appLanguageAppbarTitle => 'App Language';

  @override
  String get appTitle => 'FroggyTalk';

  @override
  String buyCreditAmountCustomPlaceholder(String min, String max) {
    return 'min $min - max $max';
  }

  @override
  String get buyCreditAmountRecommended => 'Recommended';

  @override
  String get buyCreditAppBarTitle => 'Buy Credit';

  @override
  String get buyCreditButtonText => 'Buy credit';

  @override
  String get buyCreditEnterCustomAmountLabel => 'Enter custom amount';

  @override
  String get buyCreditPageTitle => 'Preferred amount';

  @override
  String get callButtonText => 'Call';

  @override
  String get callingAppBarTitle => 'Calling';

  @override
  String get callingPageBluetoothOptionsText => 'Bluetooth';

  @override
  String get callingPageCreditErrorCardTitle => 'Out of call credit';

  @override
  String get callingPagePhoneOptionsText => 'Phone';

  @override
  String get callingPageSpeakerOptionsText => 'Speaker';

  @override
  String callingPageTimeLeft(String time) {
    return '$time left';
  }

  @override
  String get callLogTypeAnswered => 'Answered';

  @override
  String get callLogTypeBusy => 'Busy';

  @override
  String get callLogTypeCancel => 'Cancelled';

  @override
  String get callLogTypeIncoming => 'Incoming';

  @override
  String get callLogTypeMissed => 'Missed';

  @override
  String get callLogTypeOutgoing => 'Outgoing';

  @override
  String get callLogTypeUnavailable => 'Unavailable';

  @override
  String get callRatesAppBarTitle => 'Call rates';

  @override
  String get callTypeAnswered => 'Outgoing call';

  @override
  String get callTypeBusy => 'Busy';

  @override
  String get callTypeCancel => 'Cancelled call';

  @override
  String get callTypeIncoming => 'Incoming call';

  @override
  String get callTypeMissed => 'Missed call';

  @override
  String get callTypeOutgoing => 'Outgoing call';

  @override
  String get callTypeUnavailable => 'User Unavailable';

  @override
  String get cancelButtonText => 'Cancel';

  @override
  String get changeChannelButtonText => 'Change Channel';

  @override
  String get chatWithLiveAgentAppbarTitle => 'Live agent';

  @override
  String get closeButtonText => 'Close';

  @override
  String get confirmationAppBarTitle => 'Confirmation';

  @override
  String get confirmationFailedButtonText => 'Try Again';

  @override
  String get confirmationFailedContactSupportDescription => 'If you have any questions or need help, please contact our support team';

  @override
  String get confirmationFailedContactSupportText => 'Contact Support';

  @override
  String get confirmationFailedDescription => 'Your payment was NOT successful. Please try again';

  @override
  String get confirmationFailedTitle => 'Payment Failed';

  @override
  String get confirmationSuccessButtonText => 'Done';

  @override
  String confirmationSuccessDescription(String amount) {
    return '$amount has been successfully added to your balance';
  }

  @override
  String get confirmationSuccessTitle => 'Payment Successful';

  @override
  String get contactsAllContactsTabText => 'Contacts';

  @override
  String contactsCallRateText(String min, String secs) {
    return '$min mins $secs secs';
  }

  @override
  String get contactsFavouriteCallsTabText => 'Favourite';

  @override
  String get contactsNoCallsEmptyMessage => 'You do not have any recent calls';

  @override
  String get contactsNoContactsButtonText => 'View Contacts';

  @override
  String get contactsSearchContactsPlaceholder => 'Search  contact name';

  @override
  String get contactsSearchForContactsPlaceholder => 'Search for contact name';

  @override
  String get couponAppliedButtonText => 'Applied';

  @override
  String get couponApplyButtonText => 'Apply';

  @override
  String get credit => 'Credit';

  @override
  String get deleteAccountWarning => 'The action you are about to take cannot be reversed. Please confirm you are deleting your account.';

  @override
  String get dialerAppBarTitle => 'Dialer';

  @override
  String get dialerCopyActionText => 'Copy';

  @override
  String get dialerErrorInvalidNumber => 'Invalid number';

  @override
  String get dialerErrorWithoutCountryCode => 'Add country code (e.g +1)';

  @override
  String get dialerKeypadABC => 'ABC';

  @override
  String get dialerKeypadDEF => 'DEF';

  @override
  String get dialerKeypadGHI => 'GHI';

  @override
  String get dialerKeypadJKL => 'JKL';

  @override
  String get dialerKeypadMNO => 'MNO';

  @override
  String get dialerKeypadPQRS => 'PQRS';

  @override
  String get dialerKeypadTUV => 'TUV';

  @override
  String get dialerKeypadWXYZ => 'WXYZ';

  @override
  String get dialerMuteStatusMuted => 'Muted';

  @override
  String get dialerMuteStatusUnmuted => 'Un-Muted';

  @override
  String get dialerPasteActionText => 'Paste';

  @override
  String get dialerSearchResultsHeader => 'Results';

  @override
  String get dialerSearchResultsNoResults => 'No results found';

  @override
  String get dialerStatusConnected => 'Connected';

  @override
  String get dialerStatusConnecting => 'Calling';

  @override
  String get dialerStatusEnded => 'Ended';

  @override
  String get dialerStatusError => 'Error Occurred';

  @override
  String get dialerStatusFailedToConnect => 'Connect Failed';

  @override
  String get dialerStatusHold => 'On Hold';

  @override
  String get dialerStatusInitial => 'Calling';

  @override
  String get dialerStatusInitiating => 'Calling';

  @override
  String get dialerStatusRinging => 'Ringing';

  @override
  String get dialerStatusUnknown => 'unknown';

  @override
  String get emptyContactList => 'You have no contact list.';

  @override
  String get emptyFavouriteContactList => 'You have no favourite contact list.';

  @override
  String get enterCouponOptionalPlaceholder => 'Enter coupon (Optional)';

  @override
  String get enterOtpPageAppBarSubtitle => 'Step 2 of 3';

  @override
  String get enterOtpPageAppBarTitle => 'Getting Started';

  @override
  String get enterOtpPageEditButton => 'Wrong number? Please Edit';

  @override
  String get enterOtpPageErrorMessage => 'Incorrect code';

  @override
  String get enterOtpPagePhoneLabel => 'Enter your verification code';

  @override
  String enterOtpPagePhoneLabelDescription(String phoneNumber) {
    return 'A 4 digit code has been sent to your number $phoneNumber';
  }

  @override
  String get enterOtpPageResendOtpButton => 'Resend Code';

  @override
  String enterOtpPageResendOtpRetries(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count more tries',
      one: '1 final try left',
      zero: 'No tries left',
    );
    return '$_temp0';
  }

  @override
  String get enterOtpPageResendOtpRetriesError => 'You cannot receive verification codes on this number for the next 24 hours';

  @override
  String get enterOtpPageSubmitButton => 'Confirm OTP';

  @override
  String get enterVoucherCodeLabel => 'Enter voucher code';

  @override
  String get free => 'FREE';

  @override
  String get freeCreditAppbarTitle => 'Free Credit';

  @override
  String get freeCreditPageContent_1 => 'Share your referral code with your friends & family.';

  @override
  String get freeCreditPageContent_2 => 'They download FroggyTalk and register with your referral code.';

  @override
  String freeCreditPageContent_3(String amount) {
    return 'You earn $amount when the person buys credit the first time.';
  }

  @override
  String freeCreditPageHeadingText(String amount) {
    return 'Refer a friend & earn $amount';
  }

  @override
  String get freeCreditPageShareReferralLinkButtonText => 'Share referral link';

  @override
  String freeCreditPageSubHeadingText(String amount) {
    return 'My referrals ($amount)';
  }

  @override
  String get froggytalkCustomerLabel => 'Froggytalk Customer';

  @override
  String get helpCenterAppbarTitle => 'Help Center';

  @override
  String get helpCenterPageMenu_1 => 'Make a Complaint or Suggestion';

  @override
  String get helpCenterPageMenu_2 => 'Chat with Live agent';

  @override
  String get helpCenterPageMenu_3 => 'FAQ';

  @override
  String get homeNavigationBarText => 'Home';

  @override
  String get internetConnectionAlertTextError => 'Oops! It looks like you\'re offline';

  @override
  String get internetConnectionAlertTextSuccess => 'All set! You\'re connected again.';

  @override
  String get invalidInternationalPhoneFormat => 'Invalid International Phone Number Format';

  @override
  String get invalidPhoneNumber => 'Please enter a valid Phone number';

  @override
  String get keypadNavigationBarText => 'Keypad';

  @override
  String get landlineText => 'Landline';

  @override
  String get loadVoucherCardButtonText => 'Recharge';

  @override
  String get loadVoucherCardErrorText => 'Wrong code, Try again';

  @override
  String get loadVoucherCardLabelText => 'Enter 10 digit voucher code';

  @override
  String get loadVoucherCardTitle => 'Load Voucher Code';

  @override
  String get loadVoucherCodeTitle => 'Load Voucher Code';

  @override
  String get loginPageAppBarSubtitle => 'Step 1 of 3';

  @override
  String get loginPageAppBarTitle => 'Getting Started';

  @override
  String get loginPageCheckboxLabel => 'I have a referral code';

  @override
  String get loginPageErrorMessage => 'The number is not linked to a account';

  @override
  String get loginPagePhoneLabel => 'Enter your number.';

  @override
  String get loginPagePhoneLabelDescription => 'A One-Time verification code will be sent to you via WhatsApp or SMS.';

  @override
  String get loginPagePhoneNumberError => 'Your phone number is incomplete, please complete it';

  @override
  String get loginPageReferralLabel => 'Enter referral code';

  @override
  String get loginPageSubmitButton => 'Request Verification';

  @override
  String minsOrMin(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count mins',
      one: '1 min',
      zero: '',
    );
    return '$_temp0';
  }

  @override
  String get mobileText => 'Mobile';

  @override
  String get moreAppbarTitle => 'Account';

  @override
  String get moreNavigationBarText => 'More';

  @override
  String get morePageAccountBalanceCardTitle => 'Balance';

  @override
  String get morePageAppVersionMenuText => 'App version';

  @override
  String get morePageCallRatesMenuText => 'Call rates';

  @override
  String get morePageHelpCenterMenuText => 'Help Center';

  @override
  String get morePageLanguageMenuText => 'Language';

  @override
  String get morePageLoadVoucherMenuText => 'Load Voucher';

  @override
  String get morePageLogoutMenuText => 'Log out';

  @override
  String get morePageProfileMenuText => 'Profile';

  @override
  String get morePageRadioMenuText => 'Radio';

  @override
  String get morePageReferralCodeCardButtonText => 'More info';

  @override
  String morePageReferralCodeCardContent(int numberOfPeople) {
    return '$numberOfPeople people have used your referral code';
  }

  @override
  String morePageReferralCodeCardDescription(String amount) {
    return 'Get $amount when you refer your friends & family';
  }

  @override
  String get morePageReferralCodeCardTitle => 'Referral code';

  @override
  String get noBuyCreditOptionsAvailable => 'No Buy Credit Options Available';

  @override
  String get noFavoriteContactsMessage => 'No Favourite Contacts';

  @override
  String get noNotificationsYet => 'No notifications yet';

  @override
  String get notEnoughCreditMessage => 'You don\'t have enough credit';

  @override
  String get notification => 'Notification';

  @override
  String get notificationsAppBarTitle => 'Notifications';

  @override
  String get notificationsEmptyMessage => 'You do not have any notifications';

  @override
  String get notificationSettingsAppBarTitle => 'Notification Settings';

  @override
  String get notificationSettingsAutoDeleteNotificationText => 'Auto delete notification';

  @override
  String get notificationSettingsNotificationSoundText => 'Notification sound';

  @override
  String get notificationsTabAllText => 'All';

  @override
  String get notificationsTabUnreadText => 'Unread';

  @override
  String notificationsTimeAgo(int time, String duration) {
    String _temp0 = intl.Intl.pluralLogic(
      time,
      locale: localeName,
      other: '${duration}s',
      one: '$duration',
    );
    return '$time $_temp0 ago';
  }

  @override
  String get okButtonText => 'Ok';

  @override
  String get onboardingPageFooterAndText => 'and';

  @override
  String get onboardingPageFooterPrivacy => 'Privacy Policy';

  @override
  String get onboardingPageFooterTermsConditionsText => 'Terms and Conditions';

  @override
  String get onboardingPageFooterText => 'By Clicking Get Started, you are accepting the';

  @override
  String get onboardingPageSliderHeader1 => 'Multi-Language';

  @override
  String get onboardingPageSliderHeader2 => 'Call mobile or landline numbers worldwide';

  @override
  String get onboardingPageSliderHeader3 => 'Easy payment';

  @override
  String get onboardingPageSliderText1 => 'FroggyTalk allows you select your preferred language e.g Tignrigna, Amharic, Hausa etc.';

  @override
  String get onboardingPageSliderText2 => 'Make calls to any mobile or landline anywhere in the world. The receiver does not need a smartphone or internet connection.';

  @override
  String get onboardingPageSliderText3 => 'Buy call credit in your local currency with your preferred payment method.';

  @override
  String get onboardingPageSubmitButtonText => 'Get Started';

  @override
  String get outOfCreditLabel => 'Out of credit';

  @override
  String get paymentFailureAppBarTitle => 'Failed';

  @override
  String get paymentFailureHomeButton => 'Home';

  @override
  String paymentFailureMessage(String amount) {
    return 'Your payment of $amount was unsuccessful';
  }

  @override
  String get paymentFailureTitle => 'Payment Unsuccessful';

  @override
  String get paymentFailureTryAgainButton => 'Try Again';

  @override
  String get paymentOptionsAppBarTitle => 'Payment Options';

  @override
  String paymentOptionsAutoCreditDescription(String amount, String minAmount) {
    return 'This will automatically credit your account with $amount when your balance is below  $minAmount.';
  }

  @override
  String get paymentOptionsAutoCreditTitle => 'Auto Credit';

  @override
  String get paymentOptionsSelectPaymentMethod => 'Choose preferred method of payment';

  @override
  String get paymentOptionsSummaryAmountToCreditText => 'Amount to be credited';

  @override
  String get paymentOptionsSummaryDiscountButtonText => 'Apply';

  @override
  String get paymentOptionsSummaryDiscountPlaceholder => 'Enter discount code(optional)';

  @override
  String get paymentOptionsSummaryDiscountText => 'Discount';

  @override
  String get paymentOptionsSummaryTotalPaymentText => 'Total payment';

  @override
  String get paymentOptionsSummaryVatFeesText => 'VAT + fees';

  @override
  String get paymentSummaryAmountToCreditLabel => 'Amount to be Credited';

  @override
  String get paymentSummaryDiscountLabel => 'Discount';

  @override
  String get paymentSummaryTotalLabel => 'Total payment';

  @override
  String get paymentSummaryVatFeesLabel => 'VAT + Fees';

  @override
  String perMinRate(String rate) {
    return '$rate/min';
  }

  @override
  String get perMinRateSingle => '/min';

  @override
  String perMinuteRate(String rate) {
    return '$rate/minute';
  }

  @override
  String perMinuteSlashLabel(String min) {
    return '$min/minutes';
  }

  @override
  String get permissionButtonAccept => 'Allow Access';

  @override
  String get permissionButtonSkip => 'Skip';

  @override
  String get permissionForCameraTitle => 'We need permission to capture photos and videos';

  @override
  String get permissionForContactListTitle => 'Give FroggyTalk permission to access your Contact list / Phonebook';

  @override
  String get permissionForMicrophoneTitle => 'Give FroggyTalk access to your Microphone to process calls';

  @override
  String get permissionForNotificationTitle => 'Give FroggyTalk permission to send you Updates';

  @override
  String get permissionForStorageTitle => 'We need permission to store temporary data on your device';

  @override
  String get permissionPermanentlyDeniedMessage => 'Permission is permanently denied, Please enable it in the app settings';

  @override
  String get permissionDeniedMessage => 'Permission is denied, Please enable it in the app settings';

  @override
  String perM_Rate(String rate) {
    return '$rate/m';
  }

  @override
  String get phoneNumberStartRule => 'Please start your entry with either + or 00';

  @override
  String get proceedToPaymentButtonText => 'Proceed';

  @override
  String get profileAppbarTitle => 'Profile';

  @override
  String get profileDeleteAccountButtonText => 'Delete account';

  @override
  String get profileDeleteAccountCardContent => 'Please confirm you want your account deleted. You will lose any balance on your account.';

  @override
  String get profileDeleteAccountCardTitle => 'Delete account';

  @override
  String get profileLabel => 'Profile';

  @override
  String get profileLabelChangeLocation => 'Change location';

  @override
  String get profileLabelEmailAddress => 'Enter email address';

  @override
  String get profileLabelFullName => 'Enter full name';

  @override
  String get profileUpdatedSuccessfullyMessage => 'Profile updated successfully';

  @override
  String get profileUpdateFailedMessage => 'Profile update failed';

  @override
  String get quickAdvertSubtitle => 'Buy airtime for your family worldwide';

  @override
  String get quickAdvertTitle => 'Send credit to 140+ countries';

  @override
  String get radioAppBarTitle => 'Radio';

  @override
  String get radioComingSoonText => 'Radio Coming soon !!!';

  @override
  String get radioPageChooseChannelButtonText => 'Choose Channel';

  @override
  String get radioPageNoChannelsText => 'No channels found';

  @override
  String get radioPageSearchPlaceholder => 'Choose Channel';

  @override
  String get ratesNavigationBarText => 'Rates';

  @override
  String get recentCallsText => 'Recent Calls';

  @override
  String get rechargeButtonText => 'Recharge';

  @override
  String get referAndEarn => 'Refer & Earn';

  @override
  String get referralCardButtonText => 'Free Credit';

  @override
  String referralCardDescription1(String percentageAmount) {
    return 'Refer someone and get $percentageAmount free credit.';
  }

  @override
  String referralCardDescription2(int numberOfPeople, num count) {
    String _temp0 = intl.Intl.pluralLogic(
      numberOfPeople,
      locale: localeName,
      other: '$numberOfPeople people have used your referral code.',
      one: 'One person has used your referral code.',
      zero: 'No one has used your referral code.',
    );
    return '$_temp0';
  }

  @override
  String get referralCardTitle => 'Referral Code';

  @override
  String get referralCodeOnCopyActionResponse => 'FroggyTalk pasted to clipboard';

  @override
  String get saveAndProceedToPaymentButtonText => 'Save & Proceed to Payment';

  @override
  String get saveChangesButtonText => 'Save changes';

  @override
  String get savedSuccessfullyMessage => 'Saved successfully';

  @override
  String get searchCountryPlaceholder => 'Search country';

  @override
  String get searchFavouriteContactMessage => 'Search For Favourite Contact';

  @override
  String get searchForCountryNoResults => 'No results found';

  @override
  String get searchForCountryPlaceholder => 'Search for Country';

  @override
  String get searchRecentCallsMessage => 'Search Recent Calls';

  @override
  String secsOrSec(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count secs',
      one: '1 sec',
      zero: '',
    );
    return '$_temp0';
  }

  @override
  String get selectCountryPlaceholder => 'Select country';

  @override
  String get sendCreditButtonText => 'Send Credit';

  @override
  String get settings => 'Settings';

  @override
  String shareReferralText(String appLink, String referralCode) {
    return 'Download FroggyTalk App $appLink and register with my referral code $referralCode. It has the best rates and international calling experience.';
  }

  @override
  String get somethingWentWrongMessage => 'Something went wrong. Please try again.';

  @override
  String get unread => 'Unread';

  @override
  String get updateProfileButtonText => 'Update Profile';

  @override
  String get upgradeDialogButtonNo => 'Not Now';

  @override
  String get upgradeDialogButtonYes => 'Update';

  @override
  String get upgradeDialogMessage => 'A new version of FroggyTalk is available. Would you like to update now?';

  @override
  String get upgradeDialogTitle => 'Update Available';

  @override
  String get validationCouponCodeIncomplete => 'Discount code is incomplete or invalid';

  @override
  String get validationCouponInvalid => 'Please enter a valid discount code';

  @override
  String get validationEmailInvalid => 'Invalid email address.';

  @override
  String validationFieldIsRequired(String fieldName) {
    return '$fieldName is required';
  }

  @override
  String validationMinLengthError(String fieldName, int minLength) {
    return '$fieldName must be at least $minLength characters long';
  }

  @override
  String get validationPhoneNumberIncomplete => 'Incomplete phone number.';

  @override
  String get viewButtonText => 'View';

  @override
  String get viewContactDetailAppBarTitle => 'Contact Details';

  @override
  String get voucherLoadedSuccessMessage => 'Voucher Loaded Successfully';

  @override
  String get securedByStripe => 'Secured by Stripe';

  @override
  String get nextTrack => 'Next Track';

  @override
  String get stopRadio => 'Stop Radio';

  @override
  String get playRadio => 'Play Radio';

  @override
  String get previousTrack => 'Previous Track';

  @override
  String get channel => 'Channel';

  @override
  String get allChannels => 'All Channels';

  @override
  String get acceptButtonText => 'Accept';

  @override
  String get declineButtonText => 'Decline';

  @override
  String allowShareInfoWithRadio(String radioName) {
    return 'Allow us share your information with $radioName';
  }

  @override
  String get nowPlaying => 'Now Playing';

  @override
  String get checkBackLaterText => 'Please check back later';

  @override
  String get refreshText => 'Refresh';

  @override
  String get noRadioStationsAvailableText => 'No radio stations available';

  @override
  String get tryAgainText => 'Try Again';

  @override
  String get unknownErrorText => 'An unknown error occurred';

  @override
  String get errorLoadingRadioStationsText => 'Error loading radio stations';

  @override
  String get loadingRadioStationsText => 'Loading radio stations...';

  @override
  String get helpCenterPageMenu_4 => 'WhatsApp';

  @override
  String validationLengthError(String fieldName, int minLength, int maxLength) {
    return '$fieldName must be between $minLength and $maxLength characters long';
  }

  @override
  String get chooseLanguage => 'Choose Language';

  @override
  String get selectPreferredLanguage => 'Select your preferred language';

  @override
  String get cancel => 'Cancel';

  @override
  String get apply => 'Apply';

  @override
  String get languageChangeError => 'Failed to change language. Please try again.';

  @override
  String get noLanguagesAvailable => 'No languages available';

  @override
  String get languageSetupPageAppBarSubtitle => 'Step 3 of 3';

  @override
  String welcomeBackText(String firstName) {
    return 'Welcome Back, $firstName';
  }

  @override
  String get froggyTalkLovesYou => 'FroggyTalk Loves You.';

  @override
  String get updateLanguagePrompt => 'Please click the button below to update your language';

  @override
  String get updateYourLanguage => 'Update Your Language.';

  @override
  String get inAppPurchaseLabel => 'In-App Purchase';

  @override
  String get restorePurchases => 'Restore Purchases';

  @override
  String get storePrice => 'Store Price';

  @override
  String timeLeftModalTitle(String days) {
    return '$days days left';
  }

  @override
  String timeLeftModalMessage(String days, int count) {
    return '$days days left to be a winner of the Cash prize. Refer $count people and be No 1';
  }

  @override
  String get confirm => 'Confirm';

  @override
  String get languageSelectionDisclaimer => 'You can change your language under';

  @override
  String get activateAutoCredit => 'Activate auto credit';

  @override
  String get autoAccountCreditHelp => 'will be charged to your card and added to your balance whenever it falls below';

  @override
  String get avoidCallDisruption => 'to avoid being cut off during a call';

  @override
  String cashPrizeAmount(String currencySymbol, String amount) {
    return '$currencySymbol$amount FREE Cash';
  }

  @override
  String monthlyReferralBoard(String month) {
    return '$month Promo Leaders';
  }

  @override
  String get termsAndConditionsApply => 'Terms and conditions apply';

  @override
  String get getNow => 'Get Now';

  @override
  String get payWithGooglePay => 'Checkout with Google Pay';

  @override
  String get payWithApplePay => 'Pay via AppStore';

  @override
  String paymentSummaryPlayStoreFeeLabel(String percentage) {
    return '+ $percentage Commission & VAT';
  }

  @override
  String get failedToLoadLeaderboard => 'Failed to load leaderboard';

  @override
  String get retry => 'Retry';

  @override
  String get googlePay => 'Google Pay';

  @override
  String appStoreCost(String value) {
    return '+ $value App Store Cost';
  }

  @override
  String get notification_call_reminder_title => 'Call Reminder';

  @override
  String notification_call_reminder_body(String contact) {
    return 'Don\'t forget to call $contact';
  }

  @override
  String get notification_promotion_title => 'Special Offer';

  @override
  String notification_promotion_body(String offer, String description) {
    return 'New offer: $offer - $description';
  }

  @override
  String get notification_security_alert_title => 'Security Alert';

  @override
  String notification_security_alert_body(String alertType, String device) {
    return 'Security alert: $alertType detected on $device';
  }

  @override
  String get notification_daily_reminder_title => 'Daily Reminder';

  @override
  String notification_daily_reminder_body(String reminderType) {
    return 'Don\'t forget: $reminderType';
  }
}
