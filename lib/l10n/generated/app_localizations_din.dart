// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Dinka (`din`).
class AppLocalizationsDin extends AppLocalizations {
  AppLocalizationsDin([String locale = 'din']) : super(locale);

  @override
  String get accountBalanceCardTitle => 'Caar';

  @override
  String get all => 'Eben';

  @override
  String get allowAccessButtonText => 'Päl yök bï kën wïc looi';

  @override
  String get appBootingTextBelow => 'Kuɔɔny kɔc ke baai bïk rëër ke cï röt rek';

  @override
  String get appLanguageAppbarTitle => 'Thoŋ de App';

  @override
  String get appTitle => 'FroggyTalk';

  @override
  String buyCreditAmountCustomPlaceholder(String min, String max) {
    return 'min $min - max $max';
  }

  @override
  String get buyCreditAmountRecommended => 'Aye yɔ̈ɔ̈k';

  @override
  String get buyCreditAppBarTitle => 'Ɣɔc Kredit';

  @override
  String get buyCreditButtonText => 'Ɣɔc dhɛ̈n';

  @override
  String get buyCreditEnterCustomAmountLabel => 'Tääu ë wëu cïke tääu thïn';

  @override
  String get buyCreditPageTitle => 'Ciin de wëu kɔɔr';

  @override
  String get callButtonText => 'Ba cuut';

  @override
  String get callingAppBarTitle => 'Cɔl';

  @override
  String get callingPageBluetoothOptionsText => 'Bluutooth';

  @override
  String get callingPageCreditErrorCardTitle => 'Aɣeer në wëu ke cööt';

  @override
  String get callingPagePhoneOptionsText => 'Tëlëpun';

  @override
  String get callingPageSpeakerOptionsText => 'Raan ye jam';

  @override
  String callingPageTimeLeft(String time) {
    return '$time cï döŋ';
  }

  @override
  String get callLogTypeAnswered => 'Kë cï dhuknhom';

  @override
  String get callLogTypeBusy => 'Ye luui';

  @override
  String get callLogTypeCancel => 'Acï cɔk kääc';

  @override
  String get callLogTypeIncoming => 'Kë bɔ̈';

  @override
  String get callLogTypeMissed => 'Acï määr';

  @override
  String get callLogTypeOutgoing => 'Kä cë bɛ̈n';

  @override
  String get callLogTypeUnavailable => 'Acïï tɔ̈u';

  @override
  String get callRatesAppBarTitle => 'Kä ye cɔl';

  @override
  String get callTypeAnswered => 'Cɔl lɔ biyic';

  @override
  String get callTypeBusy => 'Ye luui';

  @override
  String get callTypeCancel => 'Cɔl cï puɔ̈l';

  @override
  String get callTypeIncoming => 'Cɔl bɔ̈';

  @override
  String get callTypeMissed => 'Cɔl cï määr';

  @override
  String get callTypeOutgoing => 'Cɔl lɔ biyic';

  @override
  String get callTypeUnavailable => 'Raan luui ë yeen acïï tɔ̈u';

  @override
  String get cancelButtonText => 'riɔ̈k';

  @override
  String get changeChannelButtonText => 'Waar të yenë ye luɔɔi thïn';

  @override
  String get chatWithLiveAgentAppbarTitle => 'Raan lui në pïïric';

  @override
  String get closeButtonText => 'Ba thïök';

  @override
  String get confirmationAppBarTitle => 'Gäm';

  @override
  String get confirmationFailedButtonText => 'Them ëyadɛ̈';

  @override
  String get confirmationFailedContactSupportDescription => 'Na nɔŋ kë wïc ba thiëëc ka wïc kuɔɔny, ka yï yup akutnhom de kuɔɔnyda .';

  @override
  String get confirmationFailedContactSupportText => 'Cɔl Kuɔɔny';

  @override
  String get confirmationFailedDescription => 'Wëu ë tääu-pïnydu akëc thök. Yïn thiëcku ba bɛɛr them';

  @override
  String get confirmationFailedTitle => 'Wëu cïke cuatpiny acï löny';

  @override
  String get confirmationSuccessButtonText => 'E thök';

  @override
  String confirmationSuccessDescription(String amount) {
    return '$amount acï tiɛ̈m bï mat thïn tën wëu tɔ̈u yïn thïn';
  }

  @override
  String get confirmationSuccessTitle => 'Wëu cï tiɛ̈m';

  @override
  String get contactsAllContactsTabText => 'Kɔc ye keek yup';

  @override
  String contactsCallRateText(String min, String secs) {
    return '$min minit $secs thök';
  }

  @override
  String get contactsFavouriteCallsTabText => 'Kë nhaïr';

  @override
  String get contactsNoCallsEmptyMessage => 'Yïn acïn gɛɛr de cööt ëmɛn';

  @override
  String get contactsNoContactsButtonText => 'Tïŋ kä ye keek yup';

  @override
  String get contactsSearchContactsPlaceholder => 'Kɔɔr rin ke yök';

  @override
  String get contactsSearchForContactsPlaceholder => 'Kɔɔr rin ke yök';

  @override
  String get couponAppliedButtonText => 'Acï luɔ̈ɔ̈i';

  @override
  String get couponApplyButtonText => 'Loi';

  @override
  String get credit => 'Wëu';

  @override
  String get deleteAccountWarning => 'Kë wïc ba looi acïï lëu bï dhuɔ̈k ciëën. Tïŋ lɔn cï yïn ɣön-laacdu nyaai.';

  @override
  String get dialerAppBarTitle => 'Raan ye yup';

  @override
  String get dialerCopyActionText => 'thöŋ';

  @override
  String get dialerErrorInvalidNumber => 'Namba cïï lui';

  @override
  String get dialerErrorWithoutCountryCode => 'Tääu namba de baai thïn (e.g +1)';

  @override
  String get dialerKeypadABC => 'ABC';

  @override
  String get dialerKeypadDEF => 'DEF';

  @override
  String get dialerKeypadGHI => 'GHI';

  @override
  String get dialerKeypadJKL => 'JKL';

  @override
  String get dialerKeypadMNO => 'MNO';

  @override
  String get dialerKeypadPQRS => 'PQRS';

  @override
  String get dialerKeypadTUV => 'TUV';

  @override
  String get dialerKeypadWXYZ => 'WXYZ';

  @override
  String get dialerMuteStatusMuted => 'Acï biɛt';

  @override
  String get dialerMuteStatusUnmuted => 'Acïï Biet';

  @override
  String get dialerPasteActionText => 'Gääu';

  @override
  String get dialerSearchResultsHeader => 'Kä cï yök';

  @override
  String get dialerSearchResultsNoResults => 'Acïn kë cï yök';

  @override
  String get dialerStatusConnected => 'Acï rek';

  @override
  String get dialerStatusConnecting => 'Cɔl';

  @override
  String get dialerStatusEnded => 'Acï thök';

  @override
  String get dialerStatusError => 'Kë cï wuɔ̈c acï rot looi';

  @override
  String get dialerStatusFailedToConnect => 'Cök acï löny';

  @override
  String get dialerStatusHold => 'Në Muŋ';

  @override
  String get dialerStatusInitial => 'Cɔl';

  @override
  String get dialerStatusInitiating => 'Cɔl';

  @override
  String get dialerStatusRinging => 'Riŋ';

  @override
  String get dialerStatusUnknown => 'kë kuc';

  @override
  String get emptyContactList => 'Yïn acïn lööŋ ë yök.';

  @override
  String get emptyFavouriteContactList => 'Yïn acïn lööŋ ë yök ë kɔc nhiar.';

  @override
  String get enterCouponOptionalPlaceholder => 'Tääu kë cï yök (Optional)';

  @override
  String get enterOtpPageAppBarSubtitle => 'Kueer 2 de 3';

  @override
  String get enterOtpPageAppBarTitle => 'Ba Jɔ̈k de Käŋ';

  @override
  String get enterOtpPageEditButton => 'Namba de Wëtäp ë rɛ̈ɛ̈c? Yïn thiëcku ba cɔ̈kpiny';

  @override
  String get enterOtpPageErrorMessage => 'Code cie yic';

  @override
  String get enterOtpPagePhoneLabel => 'Tääu ë namba duɔ̈ɔ̈n de yök';

  @override
  String enterOtpPagePhoneLabelDescription(String phoneNumber) {
    return 'A cë tuɔɔc në namba de WhatsApp yic $phoneNumber';
  }

  @override
  String get enterOtpPageResendOtpButton => 'Tuɔɔc Namba';

  @override
  String enterOtpPageResendOtpRetries(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count kä juëc them',
      many: '',
      few: '',
      two: '',
      one: '1 thëm de akër cï döŋ',
      zero: 'Acïn kë cï them cï döŋ',
    );
    return '$_temp0';
  }

  @override
  String get enterOtpPageResendOtpRetriesError => 'Yïn acïï lëu ba lööŋ ke gɛ̈tgɛ̈t yök në namba de WhatsApp kënë yic në thɛɛr ke 24 yiic .';

  @override
  String get enterOtpPageSubmitButton => 'Gam OTP';

  @override
  String get enterVoucherCodeLabel => 'Tääu ë namba de wëu';

  @override
  String get free => 'KË CIEN KË GËLE';

  @override
  String get freeCreditAppbarTitle => 'Kredit cïn wëu ye tääu thïn';

  @override
  String get freeCreditPageContent_1 => 'Tääu ë code de gɛ̈tpiny tënë mäthku & kɔc ke paandu.';

  @override
  String get freeCreditPageContent_2 => 'Këk aye FroggyTalk yök ku gɛ̈t rin ë thäät ë thäät ë thäät.';

  @override
  String freeCreditPageContent_3(String amount) {
    return 'Yïn abï yök $amount të cï raan ɣön ë määl ɣɔɔc ë thää tueŋ.';
  }

  @override
  String freeCreditPageHeadingText(String amount) {
    return 'Tuɔɔc mäthdu & yök $amount';
  }

  @override
  String get freeCreditPageShareReferralLinkButtonText => 'Tɛ̈k kë ye tuɔɔc';

  @override
  String freeCreditPageSubHeadingText(String amount) {
    return 'Kä ca ke tuɔɔc ($amount)';
  }

  @override
  String get froggytalkCustomerLabel => 'Raan Ɣöc de Frögïtölk';

  @override
  String get helpCenterAppbarTitle => 'Tëde Kuɔɔny';

  @override
  String get helpCenterPageMenu_1 => 'Loi Guɛl wɛ̈lɛ̈/ka Kë Luel';

  @override
  String get helpCenterPageMenu_2 => 'Jam wenë raan lui në pïïric';

  @override
  String get helpCenterPageMenu_3 => 'Kë ye thiëc';

  @override
  String get homeNavigationBarText => 'Baai';

  @override
  String get internetConnectionAlertTextError => 'Oop! Aye tïŋ ke yïn këc lɔ në aliiric';

  @override
  String get internetConnectionAlertTextSuccess => 'Käŋ ëbɛ̈n acï guiir! Yïn acï yök ëya.';

  @override
  String get invalidInternationalPhoneFormat => 'Namba de Telepun de Pinynhom acïï lui';

  @override
  String get invalidPhoneNumber => 'Yïn thiëcku ba namba de telepun tɔ̈u thïn';

  @override
  String get keypadNavigationBarText => 'këde thok';

  @override
  String get landlineText => 'Telepun de piny';

  @override
  String get loadVoucherCardButtonText => 'Cärjë';

  @override
  String get loadVoucherCardErrorText => 'Namba rɛɛc, them ba bɛɛr them';

  @override
  String get loadVoucherCardLabelText => 'Tääu ë namba de wëu ke nambaai kaa 10';

  @override
  String get loadVoucherCardTitle => 'Namba de Wëu de Luɔɔi';

  @override
  String get loadVoucherCodeTitle => 'Namba de Wëu de Luɔɔi';

  @override
  String get loginPageAppBarSubtitle => 'Kueer 1 de 3';

  @override
  String get loginPageAppBarTitle => 'Ba Jɔ̈k de Käŋ';

  @override
  String get loginPageCheckboxLabel => 'Ɣɛn anɔŋ namba de tuɔɔc';

  @override
  String get loginPageErrorMessage => 'Namba acie tääu në WhatsApp yic .';

  @override
  String get loginPagePhoneLabel => 'Tääu namba duɔ̈ɔ̈n de WhatsApp';

  @override
  String get loginPagePhoneLabelDescription => 'Aköl ë thää tök ë thää ë thää abï tuɔɔc tënë yïn në WhatsApp .';

  @override
  String get loginPagePhoneNumberError => 'Namba de telepun du akëc thök, yïn thiëcku ba thöl';

  @override
  String get loginPageReferralLabel => 'Tääu ë namba de tuɔɔc';

  @override
  String get loginPageSubmitButton => 'Thiëc ë yök';

  @override
  String minsOrMin(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count minit',
      many: '',
      few: '',
      two: '',
      one: '1 minit',
      zero: '',
    );
    return '$_temp0';
  }

  @override
  String get mobileText => 'Tɛ̈ɛ̈n';

  @override
  String get moreAppbarTitle => 'Wëu ë wëu';

  @override
  String get moreNavigationBarText => 'Käjuëc';

  @override
  String get morePageAccountBalanceCardTitle => 'Caar';

  @override
  String get morePageAppVersionMenuText => 'App ë tɔ̈ thïn';

  @override
  String get morePageCallRatesMenuText => 'Kä ye cɔl';

  @override
  String get morePageHelpCenterMenuText => 'Tëde Kuɔɔny';

  @override
  String get morePageLanguageMenuText => 'Thoŋ';

  @override
  String get morePageLoadVoucherMenuText => 'Tääu ë wëu';

  @override
  String get morePageLogoutMenuText => 'Lɔ biyic';

  @override
  String get morePageProfileMenuText => 'Bɛ̈k';

  @override
  String get morePageRadioMenuText => 'Radiö';

  @override
  String get morePageReferralCodeCardButtonText => 'Wël juëc';

  @override
  String morePageReferralCodeCardContent(int numberOfPeople) {
    return '$numberOfPeople kɔc aacï namba de tuɔɔcdu luɔ̈ɔ̈i';
  }

  @override
  String morePageReferralCodeCardDescription(String amount) {
    return 'Yök $amount të tuɔɔc yïn mäthku & kɔc ke paandu';
  }

  @override
  String get morePageReferralCodeCardTitle => 'Namba de tuɔɔc';

  @override
  String get noBuyCreditOptionsAvailable => 'Acïn Käke Ɣɔc de Käŋ Tɔ̈u';

  @override
  String get noFavoriteContactsMessage => 'Acïn kɔc nhiar keek';

  @override
  String get noNotificationsYet => 'Acïn kë cï lëk ëmɛn';

  @override
  String get notEnoughCreditMessage => 'Yïn acïn wëu juëc ë dhɛ̈n';

  @override
  String get notification => 'Lɛ̈k';

  @override
  String get notificationsAppBarTitle => 'Kä ye kɔc lɛ̈k';

  @override
  String get notificationsEmptyMessage => 'Yïn acïn gɛɛr de kë bï yïn lɛ̈k';

  @override
  String get notificationSettingsAppBarTitle => 'Ajuɛɛr de Lɛ̈k';

  @override
  String get notificationSettingsAutoDeleteNotificationText => 'Kë ye nyuɔɔth cuɔt wei yetök';

  @override
  String get notificationSettingsNotificationSoundText => 'Röl de lëk';

  @override
  String get notificationsTabAllText => 'Eben';

  @override
  String get notificationsTabUnreadText => 'Kë këc kueen';

  @override
  String notificationsTimeAgo(int time, String duration) {
    String _temp0 = intl.Intl.pluralLogic(
      time,
      locale: localeName,
      other: '${duration}s',
      one: '$duration',
    );
    return '$time $_temp0 cë lɔ';
  }

  @override
  String get okButtonText => 'Eyen';

  @override
  String get onboardingPageFooterAndText => 'ku';

  @override
  String get onboardingPageFooterPrivacy => 'Lööŋ ke Käke Raan';

  @override
  String get onboardingPageFooterTermsConditionsText => 'Lööŋ ku Käke Luɔɔi';

  @override
  String get onboardingPageFooterText => 'Të gut yïn Gɔ̈t, yïn acï gam .';

  @override
  String get onboardingPageSliderHeader1 => 'Thook juëc';

  @override
  String get onboardingPageSliderHeader2 => 'Cɔl nambaai ke telepun ka telepun ke piny në pinynhom ëbɛ̈n';

  @override
  String get onboardingPageSliderHeader3 => 'Wëu piɔlic';

  @override
  String get onboardingPageSliderText1 => 'FroggyTalk ee yïn puɔ̈l ba thoŋ duɔ̈ɔ̈n kɔɔr ba kuany cïmën Thoŋ de Tignrigna, Amharic, Thoŋ de Hausa ku kɔ̈k.';

  @override
  String get onboardingPageSliderText2 => 'Loi telepun tënë gɛɛr de telepun ë thäät yiic ku gɛɛr de telepun ë pinynhom. Raan ë yök acïï wïc bï naŋ telepun ë thäät ku thäät ë thäät.';

  @override
  String get onboardingPageSliderText3 => 'Ɣɔc wëu ke cööt në wëu ke baai ke yïn kɔɔr ba wëu cuatpiny.';

  @override
  String get onboardingPageSubmitButtonText => 'Jɔk';

  @override
  String get outOfCreditLabel => 'Aɣeer në dhɛ̈n';

  @override
  String get paymentFailureAppBarTitle => 'Cë bɛ̈n';

  @override
  String get paymentFailureHomeButton => 'Baai';

  @override
  String paymentFailureMessage(String amount) {
    return 'Wëu duɔ̈ɔ̈n cïke cuatpiny de $amount akëc tiɛ̈m';
  }

  @override
  String get paymentFailureTitle => 'Wëu cïke cuatpiny akëc tiɛ̈m';

  @override
  String get paymentFailureTryAgainButton => 'Them ëyadɛ̈';

  @override
  String get paymentOptionsAppBarTitle => 'Kä ye keek cuatpiny';

  @override
  String paymentOptionsAutoCreditDescription(String amount, String minAmount) {
    return 'Kënë abï ɣön-wëu du tääu-pïny ë rot ë $amount të cï wëu ë ɣön-wëu tɔ̈u piiny $minAmount.';
  }

  @override
  String get paymentOptionsAutoCreditTitle => 'Krïdit de Riäth';

  @override
  String get paymentOptionsSelectPaymentMethod => 'Kuany kueer kɔɔr ba cuatpiny';

  @override
  String get paymentOptionsSummaryAmountToCreditText => 'Wëu bï keek tääu thïn';

  @override
  String get paymentOptionsSummaryDiscountButtonText => 'Loi';

  @override
  String get paymentOptionsSummaryDiscountPlaceholder => 'Tääu ë namba de kë bï dhuɔ̈kciëën(optional)';

  @override
  String get paymentOptionsSummaryDiscountText => 'Wëu cë bɛ̈n';

  @override
  String get paymentOptionsSummaryTotalPaymentText => 'Amat de wëu cïke cuatpiny';

  @override
  String get paymentOptionsSummaryVatFeesText => 'VAT + wëu';

  @override
  String get paymentSummaryAmountToCreditLabel => 'Wëu bï keek tääu në wëu yiic';

  @override
  String get paymentSummaryDiscountLabel => 'Wëu cë bɛ̈n';

  @override
  String get paymentSummaryTotalLabel => 'Amat de wëu cïke cuatpiny';

  @override
  String get paymentSummaryVatFeesLabel => 'VAT + Wëu';

  @override
  String perMinRate(String rate) {
    return '$rate/minit';
  }

  @override
  String get perMinRateSingle => '/minit';

  @override
  String perMinuteRate(String rate) {
    return '$rate/minit';
  }

  @override
  String perMinuteSlashLabel(String min) {
    return '$min/minit';
  }

  @override
  String get permissionButtonAccept => 'Päl yök';

  @override
  String get permissionButtonSkip => 'Lɔ̈ɔ̈m';

  @override
  String get permissionForCameraTitle => 'Ɣok awïc puɔ̈l buk thuraai ku bidiö dɔm';

  @override
  String get permissionForContactListTitle => 'Ye FroggyTalk puɔ̈l bï yïn yök në lööŋku yiic / Athör ë telepun';

  @override
  String get permissionForMicrophoneTitle => 'Ye FroggyTalk gäm dhöl bï yïn jam në jamdu yic';

  @override
  String get permissionForNotificationTitle => 'Ye FroggyTalk puɔ̈l bï yïn tuɔɔc kä cï cɔ̈kpiny';

  @override
  String get permissionForStorageTitle => 'Ɣok awïc puɔ̈l buk kä cïke yök tɔ̈ɔ̈u në aditku yiic';

  @override
  String get permissionPermanentlyDeniedMessage => 'Päl acï jäi athɛɛr, Yïn thiëcku ba puɔ̈l në ajuɛɛr de app yic';

  @override
  String get permissionDeniedMessage => 'Päl acï jäi, Yïn thiëcku ba puɔ̈l në ajuɛɛr de app yic';

  @override
  String perM_Rate(String rate) {
    return '$rate/m';
  }

  @override
  String get phoneNumberStartRule => 'Yïn thiëcku ba gɛ̈tdu jɔɔk në + wala 00 .';

  @override
  String get proceedToPaymentButtonText => 'Lɔ tueŋ';

  @override
  String get profileAppbarTitle => 'Bɛ̈k';

  @override
  String get profileDeleteAccountButtonText => 'Cuɔth akɔɔn';

  @override
  String get profileDeleteAccountCardContent => 'Tïŋ lɔn wïc yïn ba ɣön-laacdu nyaai. Yïn abï määr ë wëu ë ɣön-wëu du yic.';

  @override
  String get profileDeleteAccountCardTitle => 'Cuɔth akɔɔn';

  @override
  String get profileLabel => 'Bɛ̈k';

  @override
  String get profileLabelChangeLocation => 'Waar të tɔ̈ yïn thïn';

  @override
  String get profileLabelEmailAddress => 'Tääu ë rinke imeel';

  @override
  String get profileLabelFullName => 'Tääu rin ëbɛ̈n';

  @override
  String get profileUpdatedSuccessfullyMessage => 'Kë tɔ̈u yïn thïn acï cɔ̈kpiny apieth';

  @override
  String get profileUpdateFailedMessage => 'Cɔ̈kpiny de kë tɔ̈u në yï guɔ̈p acï löny';

  @override
  String get quickAdvertSubtitle => 'Ɣɔc thaar de aliir tënë kɔc ke paandu në pinynhom ëbɛ̈n';

  @override
  String get quickAdvertTitle => 'Tuɔɔcë wëu tënë bɛ̈i kaa 140+';

  @override
  String get radioAppBarTitle => 'Radiö';

  @override
  String get radioComingSoonText => 'Radio Abë bën !!!';

  @override
  String get radioPageChooseChannelButtonText => 'Kuany kë ye cɔl';

  @override
  String get radioPageNoChannelsText => 'Acïn dhöl cï yök';

  @override
  String get radioPageSearchPlaceholder => 'Kuany kë ye cɔl';

  @override
  String get ratesNavigationBarText => 'Theer';

  @override
  String get recentCallsText => 'Cɔl thiɔ̈kë';

  @override
  String get rechargeButtonText => 'Cärjë';

  @override
  String get referAndEarn => 'Tïŋ & Yök';

  @override
  String get referralCardButtonText => 'Kredit cïn wëu ye tääu thïn';

  @override
  String referralCardDescription1(String percentageAmount) {
    return 'Tääu ë raan dɛ̈t ku yök $percentageAmount ë wëu ë thäät ë thäät.';
  }

  @override
  String referralCardDescription2(int numberOfPeople, num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$numberOfPeople kɔc aacï namba de gɛ̈tgɛ̈tdu luɔ̈ɔ̈i.',
      many: '',
      few: '',
      two: '',
      one: 'Raan tök acï namba de gɛ̈tpinydu luɔ̈ɔ̈i.',
      zero: 'Acïn raan cï lööŋku tääu-pïny.',
    );
    return '$_temp0';
  }

  @override
  String get referralCardTitle => 'Namba de Tuɔɔc';

  @override
  String get referralCodeOnCopyActionResponse => 'FroggyTalk acï tääu në kë yenë käŋ tɔ̈ɔ̈u thïn';

  @override
  String get saveAndProceedToPaymentButtonText => 'Tɔ̈ɔ̈u & Lɔ tueŋ ke yïn tääu wëu';

  @override
  String get saveChangesButtonText => 'Tɔ̈ɔ̈u kä cïke waar';

  @override
  String get savedSuccessfullyMessage => 'Acï tɔ̈ɔ̈u apieth';

  @override
  String get searchCountryPlaceholder => 'Kɔɔr baai';

  @override
  String get searchFavouriteContactMessage => 'Kɔɔr Këdu Nhiar';

  @override
  String get searchForCountryNoResults => 'Acïn kë cï yök';

  @override
  String get searchForCountryPlaceholder => 'Kɔɔr Paan';

  @override
  String get searchRecentCallsMessage => 'Kɔɔr Cööt Thiɔ̈kë';

  @override
  String secsOrSec(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count thää',
      many: '',
      few: '',
      two: '',
      one: '1 sec',
      zero: '',
    );
    return '$_temp0';
  }

  @override
  String get selectCountryPlaceholder => 'Kuany baai';

  @override
  String get sendCreditButtonText => 'Tuɔɔc wëu';

  @override
  String get settings => 'Kä ye keek guiir';

  @override
  String shareReferralText(String appLink, String referralCode) {
    return 'Tɛ̈k FroggyTalk App $appLink kë kɔc ku bɛ̈k yïn bɛ̈ɛ̈r bï yïn namba de tuɔɔc $referralCode. Yök wëu kë cï raan bï yök ku wëu thïn tënë wëu tɔ̈u yïn thïn.';
  }

  @override
  String get somethingWentWrongMessage => 'Këdäŋ acï rɛ̈ɛ̈c. Yïn thiëcku ba bɛɛr them.';

  @override
  String get unread => 'Kë këc kueen';

  @override
  String get updateProfileButtonText => 'Cɔ̈kpiny këdu';

  @override
  String get upgradeDialogButtonNo => 'Acië mëën';

  @override
  String get upgradeDialogButtonYes => 'Lɛ̈k';

  @override
  String get upgradeDialogMessage => 'Aciëŋ yam de FroggyTalk acï yök. Ye wïc ba jäl ëmɛn?';

  @override
  String get upgradeDialogTitle => 'Cɔ̈kpiny Atɔ̈u';

  @override
  String get validationCouponCodeIncomplete => 'Namba de thök de wëu acïï thök wɛ̈lɛ̈/ka këc lui';

  @override
  String get validationCouponInvalid => 'Yïn thiëcku ba namba de thök de wëu tääu thïn';

  @override
  String get validationEmailInvalid => 'Ajuɛɛr de imeel cïï lui.';

  @override
  String validationFieldIsRequired(String fieldName) {
    return '$fieldName aye kɔɔr';
  }

  @override
  String validationMinLengthError(String fieldName, int minLength) {
    return '$fieldName adhil ya kë thiin koor $minLength ë wël bɛ̈ɛ̈r';
  }

  @override
  String get validationPhoneNumberIncomplete => 'Namba de telepun këc thök.';

  @override
  String get viewButtonText => 'Tïŋ';

  @override
  String get viewContactDetailAppBarTitle => 'Käk ë yök';

  @override
  String get voucherLoadedSuccessMessage => 'Kë cï tääu në wëu yiic acï tääu apieth';

  @override
  String get securedByStripe => 'Acï gël në Strip';

  @override
  String get nextTrack => 'Dɛ̈t bɔ̈';

  @override
  String get stopRadio => 'Kɔ̈ɔ̈c Radiö';

  @override
  String get playRadio => 'Puɔɔl Radiö';

  @override
  String get previousTrack => 'Kë thɛɛr';

  @override
  String get channel => 'Ajuɛɛr';

  @override
  String get allChannels => 'Käŋ kedhie';

  @override
  String get acceptButtonText => 'Bi gääm';

  @override
  String get declineButtonText => 'Bɛ̈n piny';

  @override
  String allowShareInfoWithRadio(String radioName) {
    return 'Päl ɣok wëlkuɔ rɔm kek $radioName';
  }

  @override
  String get nowPlaying => 'Ëmɛnë ke pol';

  @override
  String get checkBackLaterText => 'Yïn thiëcku ba dhuk ciëën';

  @override
  String get refreshText => 'Cɔ̈kpiny';

  @override
  String get noRadioStationsAvailableText => 'Acïn ɣän ke radiö tɔ̈u';

  @override
  String get tryAgainText => 'Them ëyadɛ̈';

  @override
  String get unknownErrorText => 'Kë kuc cï wuɔ̈c acï rot looi';

  @override
  String get errorLoadingRadioStationsText => 'Awuɔ̈c në tɛ̈ɛ̈u de ɣän ke radiö';

  @override
  String get loadingRadioStationsText => 'Tääu ë ɣän ke radiö...';

  @override
  String get helpCenterPageMenu_4 => 'Wäthäp';

  @override
  String validationLengthError(String fieldName, int minLength, int maxLength) {
    return '$fieldName adhil tɔ̈u në kaam de $minLength ku $maxLength ë wël bɛ̈ɛ̈r';
  }

  @override
  String get chooseLanguage => 'Kuany Thoŋ';

  @override
  String get selectPreferredLanguage => 'Kuany thoŋ de yïn bɛ̈ɛ̈r';

  @override
  String get cancel => 'Riɔ̈k';

  @override
  String get apply => 'Loi';

  @override
  String get languageChangeError => 'Thoŋ acï wuɔ̈c acï rot looi. Yïn thiëcku ba bɛɛr them.';

  @override
  String get noLanguagesAvailable => 'Acïn thoŋ tɔ̈u';

  @override
  String get languageSetupPageAppBarSubtitle => 'Kueer 3 de 3';

  @override
  String welcomeBackText(String firstName) {
    return 'Ɣɛn baai, $firstName';
  }

  @override
  String get froggyTalkLovesYou => 'FroggyTalk acï yök yïn.';

  @override
  String get updateLanguagePrompt => 'Yïn thiëcku ba loi kë ye thök në thoŋ de yïn';

  @override
  String get updateYourLanguage => 'Thök Thoŋ de Yïn.';

  @override
  String get inAppPurchaseLabel => 'Ɣɔc Thïn App';

  @override
  String get restorePurchases => 'Dhuɔ̈kë kä cïke ɣɔɔc';

  @override
  String get storePrice => 'Wëu ke cuän';

  @override
  String timeLeftModalTitle(String days) {
    return '${days}nïn ka döŋ';
  }

  @override
  String timeLeftModalMessage(String days, int count) {
    return '${days}nïn lik cï döŋ bï yïn ya raan cï yök në wëu ke Cash. Jɛk $count kɔc ku ye No 1';
  }

  @override
  String get confirm => 'Bɛ̈n gam';

  @override
  String get languageSelectionDisclaimer => 'You can change your language under';

  @override
  String get activateAutoCredit => 'Loi dhɛ̈n de rot';

  @override
  String get autoAccountCreditHelp => 'abï tääu në kaamdu yic ku bï mat në wëu tɔ̈ në yï nhom të cï yen lööny piny .';

  @override
  String get avoidCallDisruption => 'ku bï yïn cïï tɛ̈ɛ̈m wei në thɛɛr ke cööt .';

  @override
  String cashPrizeAmount(String currencySymbol, String amount) {
    return '$currencySymbol$amount Wëu ë FREE';
  }

  @override
  String monthlyReferralBoard(String month) {
    return '$month Bäny de Promo';
  }

  @override
  String get termsAndConditionsApply => 'Lööŋ ku kä cïke lueel aye luui';

  @override
  String get getNow => 'Yök ëmën';

  @override
  String get payWithGooglePay => 'Google Pay';

  @override
  String get payWithApplePay => 'Apple Pay';

  @override
  String paymentSummaryPlayStoreFeeLabel(String percentage) {
    return '  + $percentage% Ajuɛɛr & VAT';
  }

  @override
  String get failedToLoadLeaderboard => 'Acï löny bï thöny de bäny tääu thïn';

  @override
  String get retry => 'Them';

  @override
  String get googlePay => 'Google Pay';

  @override
  String appStoreCost(String value) {
    return '+ $value Wëu AppStore';
  }

  @override
  String get notification_call_reminder_title => 'Call Reminder';

  @override
  String notification_call_reminder_body(String contact) {
    return 'Don\'t forget to call $contact';
  }

  @override
  String get notification_promotion_title => 'Special Offer';

  @override
  String notification_promotion_body(String offer, String description) {
    return 'New offer: $offer - $description';
  }

  @override
  String get notification_security_alert_title => 'Security Alert';

  @override
  String notification_security_alert_body(String alertType, String device) {
    return 'Security alert: $alertType detected on $device';
  }

  @override
  String get notification_daily_reminder_title => 'Daily Reminder';

  @override
  String notification_daily_reminder_body(String reminderType) {
    return 'Don\'t forget: $reminderType';
  }
}
