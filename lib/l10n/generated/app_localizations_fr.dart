// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get accountBalanceCardTitle => 'Solde ';

  @override
  String get all => 'Tous ';

  @override
  String get allowAccessButtonText => 'Autoriser l\'accès pour cette action requise ';

  @override
  String get appBootingTextBelow => 'Aider les communautés à rester connectées ';

  @override
  String get appLanguageAppbarTitle => 'Langue de l\'application ';

  @override
  String get appTitle => 'FroggyTalk';

  @override
  String buyCreditAmountCustomPlaceholder(String min, String max) {
    return 'min $min - max $max';
  }

  @override
  String get buyCreditAmountRecommended => 'Recommandé ';

  @override
  String get buyCreditAppBarTitle => 'Acheter du crédit ';

  @override
  String get buyCreditButtonText => 'Acheter du crédit ';

  @override
  String get buyCreditEnterCustomAmountLabel => 'Montant personnalisé ';

  @override
  String get buyCreditPageTitle => 'Montant souhaité ';

  @override
  String get callButtonText => 'Appeler ';

  @override
  String get callingAppBarTitle => 'Appel en cours ';

  @override
  String get callingPageBluetoothOptionsText => 'Bluetooth';

  @override
  String get callingPageCreditErrorCardTitle => 'Crédit d\'appel épuisé ';

  @override
  String get callingPagePhoneOptionsText => 'Téléphone ';

  @override
  String get callingPageSpeakerOptionsText => 'Haut-parleur ';

  @override
  String callingPageTimeLeft(String time) {
    return '$time restante(s)';
  }

  @override
  String get callLogTypeAnswered => 'Répondu ';

  @override
  String get callLogTypeBusy => 'Occupé ';

  @override
  String get callLogTypeCancel => 'Annulé ';

  @override
  String get callLogTypeIncoming => 'Entrant ';

  @override
  String get callLogTypeMissed => 'Manqué ';

  @override
  String get callLogTypeOutgoing => 'Sortant ';

  @override
  String get callLogTypeUnavailable => 'Indisponible ';

  @override
  String get callRatesAppBarTitle => 'Tarifs d\'appel ';

  @override
  String get callTypeAnswered => 'Appel sortant ';

  @override
  String get callTypeBusy => 'Occupé ';

  @override
  String get callTypeCancel => 'Appel annulé ';

  @override
  String get callTypeIncoming => 'Appel entrant ';

  @override
  String get callTypeMissed => 'Appel Manqué ';

  @override
  String get callTypeOutgoing => 'Appel sortant ';

  @override
  String get callTypeUnavailable => 'Utilisateur indisponible ';

  @override
  String get cancelButtonText => 'Annuler ';

  @override
  String get changeChannelButtonText => 'Changer de chaîne ';

  @override
  String get chatWithLiveAgentAppbarTitle => 'Agent en direct ';

  @override
  String get closeButtonText => 'Fermer ';

  @override
  String get confirmationAppBarTitle => 'Confirmation';

  @override
  String get confirmationFailedButtonText => 'Réessayer ';

  @override
  String get confirmationFailedContactSupportDescription => 'Pour toute question ou assistance, contactez notre service client ';

  @override
  String get confirmationFailedContactSupportText => 'Contacter le support ';

  @override
  String get confirmationFailedDescription => 'Votre paiement a ÉCHOUÉ. Veuillez réessayer ';

  @override
  String get confirmationFailedTitle => 'Paiement échoué ';

  @override
  String get confirmationSuccessButtonText => 'Terminé ';

  @override
  String confirmationSuccessDescription(String amount) {
    return '$amount ont été crédités avec succès sur votre solde ';
  }

  @override
  String get confirmationSuccessTitle => 'Paiement réussi ';

  @override
  String get contactsAllContactsTabText => 'Contacts';

  @override
  String contactsCallRateText(String min, String secs) {
    return '$min min $secs secs';
  }

  @override
  String get contactsFavouriteCallsTabText => 'Favoris ';

  @override
  String get contactsNoCallsEmptyMessage => 'Vous n\'avez aucun appel récent ';

  @override
  String get contactsNoContactsButtonText => 'Voir les Contacts';

  @override
  String get contactsSearchContactsPlaceholder => 'Rechercher un contact ';

  @override
  String get contactsSearchForContactsPlaceholder => 'Rechercher un contact ';

  @override
  String get couponAppliedButtonText => 'Appliqué ';

  @override
  String get couponApplyButtonText => 'Appliquer ';

  @override
  String get credit => 'Crédit ';

  @override
  String get deleteAccountWarning => 'Cette action est irréversible. Veuillez confirmer que vous souhaitez supprimer votre compte.';

  @override
  String get dialerAppBarTitle => 'Clavier ';

  @override
  String get dialerCopyActionText => 'Copier ';

  @override
  String get dialerErrorInvalidNumber => 'Numéro invalide ';

  @override
  String get dialerErrorWithoutCountryCode => 'Ajoutez l\'indicatif pays (e.g +1)';

  @override
  String get dialerKeypadABC => 'ABC';

  @override
  String get dialerKeypadDEF => 'DEF';

  @override
  String get dialerKeypadGHI => 'GHI';

  @override
  String get dialerKeypadJKL => 'JKL';

  @override
  String get dialerKeypadMNO => 'MNO';

  @override
  String get dialerKeypadPQRS => 'PQRS';

  @override
  String get dialerKeypadTUV => 'TUV';

  @override
  String get dialerKeypadWXYZ => 'WXYZ';

  @override
  String get dialerMuteStatusMuted => 'Muet ';

  @override
  String get dialerMuteStatusUnmuted => 'Non muet ';

  @override
  String get dialerPasteActionText => 'Coller ';

  @override
  String get dialerSearchResultsHeader => 'Résultats ';

  @override
  String get dialerSearchResultsNoResults => 'Aucun résultat trouvé ';

  @override
  String get dialerStatusConnected => 'Connecté ';

  @override
  String get dialerStatusConnecting => 'Appel en cours ';

  @override
  String get dialerStatusEnded => 'Terminé ';

  @override
  String get dialerStatusError => 'Une erreur est survenue ';

  @override
  String get dialerStatusFailedToConnect => 'Échec de la connexion ';

  @override
  String get dialerStatusHold => 'En attente ';

  @override
  String get dialerStatusInitial => 'Appel en cours ';

  @override
  String get dialerStatusInitiating => 'Appel en cours ';

  @override
  String get dialerStatusRinging => 'Sonnerie en cours ';

  @override
  String get dialerStatusUnknown => 'Inconnu ';

  @override
  String get emptyContactList => 'Votre liste de contacts est vide.';

  @override
  String get emptyFavouriteContactList => 'Vous n\'avez aucun contact favori ';

  @override
  String get enterCouponOptionalPlaceholder => 'Entrez un code promo (Optionnel)';

  @override
  String get enterOtpPageAppBarSubtitle => 'Étape 2 sur 3';

  @override
  String get enterOtpPageAppBarTitle => 'Commencer ';

  @override
  String get enterOtpPageEditButton => 'Mouvais numéro WhatsApp ? Modifier ';

  @override
  String get enterOtpPageErrorMessage => 'Code incorrect ';

  @override
  String get enterOtpPagePhoneLabel => 'Entrez votre code de vérification ';

  @override
  String enterOtpPagePhoneLabelDescription(String phoneNumber) {
    return 'Un code à 4 chiffres a été envoyé à votre numéro WhatsApp  $phoneNumber';
  }

  @override
  String get enterOtpPageResendOtpButton => 'Renvoyer le code ';

  @override
  String enterOtpPageResendOtpRetries(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '# essais restants ',
      one: 'Dernier essai ',
      zero: 'Plus d\'essais ',
    );
    return '$_temp0';
  }

  @override
  String get enterOtpPageResendOtpRetriesError => 'Vous ne pouvez plus recevoir de codes sur ce numéro WhatsApp pendant 24 heures ';

  @override
  String get enterOtpPageSubmitButton => 'Confirmer le code OTP ';

  @override
  String get enterVoucherCodeLabel => 'Entrez le code promo ';

  @override
  String get free => 'GRATUIT ';

  @override
  String get freeCreditAppbarTitle => 'Crédit gratuit ';

  @override
  String get freeCreditPageContent_1 => 'Partagez votre code de parrainage avec vos amis & famille.';

  @override
  String get freeCreditPageContent_2 => 'Ils téléchargent FroggyTalk et  s\'inscrire avec votre code.';

  @override
  String freeCreditPageContent_3(String amount) {
    return 'Vous gagnez $amount quand la personne achète du crédit pour la première fois.';
  }

  @override
  String freeCreditPageHeadingText(String amount) {
    return 'Parrainez un ami et gagnez $amount';
  }

  @override
  String get freeCreditPageShareReferralLinkButtonText => 'Partager le lien de parrainage ';

  @override
  String freeCreditPageSubHeadingText(String amount) {
    return 'Mes filleuls ($amount)';
  }

  @override
  String get froggytalkCustomerLabel => 'Client FroggyTalk ';

  @override
  String get helpCenterAppbarTitle => 'Centre d\'aide ';

  @override
  String get helpCenterPageMenu_1 => 'Faire une réclamation ou suggestion ';

  @override
  String get helpCenterPageMenu_2 => 'Discuter avec un agent ';

  @override
  String get helpCenterPageMenu_3 => 'FAQ';

  @override
  String get homeNavigationBarText => 'Accueil ';

  @override
  String get internetConnectionAlertTextError => 'Oups ! Vous semblez hors ligne.';

  @override
  String get internetConnectionAlertTextSuccess => 'Tout est bon ! Vous êtes reconnecté (e).';

  @override
  String get invalidInternationalPhoneFormat => 'Format de numéro international invalide ';

  @override
  String get invalidPhoneNumber => 'Veuillez entrer un numéro de téléphone valide ';

  @override
  String get keypadNavigationBarText => 'Clavier ';

  @override
  String get landlineText => 'Ligne fixe ';

  @override
  String get loadVoucherCardButtonText => 'Recharger';

  @override
  String get loadVoucherCardErrorText => 'Code incorrect, veuillez réessayer ';

  @override
  String get loadVoucherCardLabelText => 'Entrez un code promo à 10 chiffres ';

  @override
  String get loadVoucherCardTitle => 'Charger un code promo ';

  @override
  String get loadVoucherCodeTitle => 'Charger un code promo ';

  @override
  String get loginPageAppBarSubtitle => 'Étape 1 sur 3';

  @override
  String get loginPageAppBarTitle => 'Commencer ';

  @override
  String get loginPageCheckboxLabel => 'J\'ai un code de parrainage ';

  @override
  String get loginPageErrorMessage => 'Le numéro n\'est pas associé à un compte WhatsApp ';

  @override
  String get loginPagePhoneLabel => 'Entrez votre numéro WhatsApp';

  @override
  String get loginPagePhoneLabelDescription => 'Un code de vérification unique vous sera envoyé via WhatsApp';

  @override
  String get loginPagePhoneNumberError => 'Votre numéro de téléphone est incomplet, veuillez le compléter ';

  @override
  String get loginPageReferralLabel => 'Entrez votre code de parrainage ';

  @override
  String get loginPageSubmitButton => 'Demander la vérification ';

  @override
  String minsOrMin(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'mins',
      one: '1 min',
      zero: '0 min',
    );
    return '$_temp0';
  }

  @override
  String get mobileText => 'Mobile';

  @override
  String get moreAppbarTitle => 'Compte ';

  @override
  String get moreNavigationBarText => 'Plus ';

  @override
  String get morePageAccountBalanceCardTitle => 'Solde ';

  @override
  String get morePageAppVersionMenuText => 'Version de l\'application ';

  @override
  String get morePageCallRatesMenuText => 'Tarifs d\'appel ';

  @override
  String get morePageHelpCenterMenuText => 'Centre d\'aide ';

  @override
  String get morePageLanguageMenuText => 'Langue ';

  @override
  String get morePageLoadVoucherMenuText => 'Charger un un code promo ';

  @override
  String get morePageLogoutMenuText => 'Déconnexion ';

  @override
  String get morePageProfileMenuText => 'Profil';

  @override
  String get morePageRadioMenuText => 'Radio';

  @override
  String get morePageReferralCodeCardButtonText => 'Plus d\'infos ';

  @override
  String morePageReferralCodeCardContent(int numberOfPeople) {
    return '$numberOfPeople personnes ont utilisé votre code de parrainage ';
  }

  @override
  String morePageReferralCodeCardDescription(String amount) {
    return 'Recevez $amount en parrainant vos amis famille';
  }

  @override
  String get morePageReferralCodeCardTitle => 'Code de parrainage ';

  @override
  String get noBuyCreditOptionsAvailable => 'Aucune option d\'achat de crédit disponible';

  @override
  String get noFavoriteContactsMessage => 'Aucun contact favori ';

  @override
  String get noNotificationsYet => 'Aucune notification pour le moment ';

  @override
  String get notEnoughCreditMessage => 'Crédit insuffisant ';

  @override
  String get notification => 'Notification';

  @override
  String get notificationsAppBarTitle => 'Notifications';

  @override
  String get notificationsEmptyMessage => 'Vous n\'avez aucune notification ';

  @override
  String get notificationSettingsAppBarTitle => 'Paramètres de notification ';

  @override
  String get notificationSettingsAutoDeleteNotificationText => 'Suppression automatique des notifications ';

  @override
  String get notificationSettingsNotificationSoundText => 'Son de notification ';

  @override
  String get notificationsTabAllText => 'Toutes ';

  @override
  String get notificationsTabUnreadText => 'Non lus ';

  @override
  String notificationsTimeAgo(int time, String duration) {
    String _temp0 = intl.Intl.pluralLogic(
      time,
      locale: localeName,
      other: '${duration}s',
      one: '$duration',
    );
    return '$time $_temp0 iI ya ';
  }

  @override
  String get okButtonText => 'Ok';

  @override
  String get onboardingPageFooterAndText => 'et';

  @override
  String get onboardingPageFooterPrivacy => 'Politique de confidentialité ';

  @override
  String get onboardingPageFooterTermsConditionsText => 'Conditions générales ';

  @override
  String get onboardingPageFooterText => 'En cliquant sur commencer. Vous acceptez les ';

  @override
  String get onboardingPageSliderHeader1 => 'Multilingue ';

  @override
  String get onboardingPageSliderHeader2 => 'Appelez mobiles et fixes dans le monde entier ';

  @override
  String get onboardingPageSliderHeader3 => 'Paiement facile ';

  @override
  String get onboardingPageSliderText1 => 'FroggyTalk vous permet de choisir votre langue préférée (Tigrigna, Amharique, Haussa, etc.)';

  @override
  String get onboardingPageSliderText2 => 'Passez des appels vers nimporte quel mobile ou fixe dans le monde. Le destinataire na pas besoin de smartphone ou de connexion internet.';

  @override
  String get onboardingPageSliderText3 => 'Achetez du crédit d\'appel dans votre devise locale avec votre moyen de paiement préféré.';

  @override
  String get onboardingPageSubmitButtonText => 'Commencer ';

  @override
  String get outOfCreditLabel => 'Plus de crédit ';

  @override
  String get paymentFailureAppBarTitle => 'Échec ';

  @override
  String get paymentFailureHomeButton => 'Accueil ';

  @override
  String paymentFailureMessage(String amount) {
    return 'Votre paiement de $amount a échoué ';
  }

  @override
  String get paymentFailureTitle => 'Paiement échoué ';

  @override
  String get paymentFailureTryAgainButton => 'Réessayer ';

  @override
  String get paymentOptionsAppBarTitle => ' Options de paiement ';

  @override
  String paymentOptionsAutoCreditDescription(String amount, String minAmount) {
    return 'Votre compte sera automatiquement crédité de $amount lorsque votre solde descend sous $minAmount.';
  }

  @override
  String get paymentOptionsAutoCreditTitle => 'Recharge automatique ';

  @override
  String get paymentOptionsSelectPaymentMethod => 'Choisissez votre moyen de paiement ';

  @override
  String get paymentOptionsSummaryAmountToCreditText => 'Montant à créditer ';

  @override
  String get paymentOptionsSummaryDiscountButtonText => 'Appliquer ';

  @override
  String get paymentOptionsSummaryDiscountPlaceholder => 'Code promo (optionnel)';

  @override
  String get paymentOptionsSummaryDiscountText => 'Remise ';

  @override
  String get paymentOptionsSummaryTotalPaymentText => 'Total à payer ';

  @override
  String get paymentOptionsSummaryVatFeesText => 'TVA + frais ';

  @override
  String get paymentSummaryAmountToCreditLabel => 'Montant à créditer ';

  @override
  String get paymentSummaryDiscountLabel => 'Remise ';

  @override
  String get paymentSummaryTotalLabel => 'Total à payer ';

  @override
  String get paymentSummaryVatFeesLabel => 'TVA+  Frais ';

  @override
  String perMinRate(String rate) {
    return '$rate/min';
  }

  @override
  String get perMinRateSingle => '/min';

  @override
  String perMinuteRate(String rate) {
    return '$rate/minute';
  }

  @override
  String perMinuteSlashLabel(String min) {
    return '$min/min ';
  }

  @override
  String get permissionButtonAccept => 'Autoriser l\'accès ';

  @override
  String get permissionButtonSkip => 'Passer ';

  @override
  String get permissionForCameraTitle => 'Nous avons besoin d\'accéder à votre appareil photo et vidéo.';

  @override
  String get permissionForContactListTitle => 'Autorisez FroggyTalk à accéder à votre liste de contacts  / répertoire ';

  @override
  String get permissionForMicrophoneTitle => 'Autorisez FroggyTalk à utiliser votre to Microphone pour les appels ';

  @override
  String get permissionForNotificationTitle => 'Autorisez FroggyTalk à vous envoyer des notifications ';

  @override
  String get permissionForStorageTitle => 'Nous avons besoin d\'accéder à votre stokage pour enregistrer des données temporaires.';

  @override
  String get permissionPermanentlyDeniedMessage => 'Permission définitivement refusée ';

  @override
  String get permissionDeniedMessage => 'Permission refusée ';

  @override
  String perM_Rate(String rate) {
    return '$rate/min';
  }

  @override
  String get phoneNumberStartRule => 'Veuillez commencer par + ou 00';

  @override
  String get proceedToPaymentButtonText => 'Procéder au paiement ';

  @override
  String get profileAppbarTitle => 'Profil';

  @override
  String get profileDeleteAccountButtonText => 'Supprimer le compte ';

  @override
  String get profileDeleteAccountCardContent => 'Veuillez confirmer la suppression de votre compte. Vous perdez tout solde disponible.';

  @override
  String get profileDeleteAccountCardTitle => 'Supprimer le compte ';

  @override
  String get profileLabel => 'Profil ';

  @override
  String get profileLabelChangeLocation => 'Changer de localisation ';

  @override
  String get profileLabelEmailAddress => 'Entrer l\'adresse e-mail ';

  @override
  String get profileLabelFullName => 'Entrer le nom complet ';

  @override
  String get profileUpdatedSuccessfullyMessage => 'Profil mis à jour avec succès ';

  @override
  String get profileUpdateFailedMessage => 'Échec de la mise à jour du profil ';

  @override
  String get quickAdvertSubtitle => 'Achetez du crédit pour votre famille dans le monde entier.';

  @override
  String get quickAdvertTitle => 'Envoyer du crédit vers 140+ pays ';

  @override
  String get radioAppBarTitle => 'Radio';

  @override
  String get radioComingSoonText => 'Radio disponible bientôt !';

  @override
  String get radioPageChooseChannelButtonText => 'Choisir une chaîne ';

  @override
  String get radioPageNoChannelsText => 'Aucune chaîne Trouvée ';

  @override
  String get radioPageSearchPlaceholder => 'Choisir une chaîne ';

  @override
  String get ratesNavigationBarText => 'Tarifs ';

  @override
  String get recentCallsText => 'Appels récents ';

  @override
  String get rechargeButtonText => 'Recharger';

  @override
  String get referAndEarn => 'Parrainez & Gagnez ';

  @override
  String get referralCardButtonText => 'Crédit gratuit ';

  @override
  String referralCardDescription1(String percentageAmount) {
    return 'Parrainez un ami et recevez $percentageAmount  de crédit gratuit ';
  }

  @override
  String referralCardDescription2(int numberOfPeople, num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$numberOfPeople personnes ont utilisé votre code de parrainage ',
      one: 'Une personne a utilisé votre code de parrainage.',
      zero: 'Personne n\'a utilisé votre code de parrainage.',
    );
    return '$_temp0';
  }

  @override
  String get referralCardTitle => 'Code de parrainage ';

  @override
  String get referralCodeOnCopyActionResponse => 'FroggyTalk copié dans le presse-papiers ';

  @override
  String get saveAndProceedToPaymentButtonText => 'Enregistrer et Payer ';

  @override
  String get saveChangesButtonText => 'Enregistrer les modifications ';

  @override
  String get savedSuccessfullyMessage => 'Enregistré avec succès ';

  @override
  String get searchCountryPlaceholder => 'Rechercher un pays ';

  @override
  String get searchFavouriteContactMessage => 'Rechercher un contact favori ';

  @override
  String get searchForCountryNoResults => 'Aucun résultat trouvé ';

  @override
  String get searchForCountryPlaceholder => 'Rechercher un pays ';

  @override
  String get searchRecentCallsMessage => 'Rechercher dans les appels récentes ';

  @override
  String secsOrSec(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'secs',
      one: '1 sec',
      zero: '0',
    );
    return '$_temp0';
  }

  @override
  String get selectCountryPlaceholder => 'Selection un pays ';

  @override
  String get sendCreditButtonText => 'Envoyer du crédit ';

  @override
  String get settings => 'Paramètres ';

  @override
  String shareReferralText(String appLink, String referralCode) {
    return 'Téléchargez l\'application FroggyTalk sur $appLink et inscrivez-vous avec mon code de parrainage $referralCode. Elle offre les meilleurs tarifs et une excellente expérience d\'appel international.';
  }

  @override
  String get somethingWentWrongMessage => 'Une erreur s\'est produite. Veuillez réessayer.';

  @override
  String get unread => 'Non lus ';

  @override
  String get updateProfileButtonText => 'Mettre à jour le profil ';

  @override
  String get upgradeDialogButtonNo => 'Plus tard ';

  @override
  String get upgradeDialogButtonYes => 'Mettre à jour ';

  @override
  String get upgradeDialogMessage => 'Une nouvelle version de FroggyTalk est disponible. Souhaitez-vous mettre à jour maintenant ?';

  @override
  String get upgradeDialogTitle => 'Mise à jour disponible ';

  @override
  String get validationCouponCodeIncomplete => 'Code promo incomplet ou invalide ';

  @override
  String get validationCouponInvalid => 'Veuillez entrer un code promo valide ';

  @override
  String get validationEmailInvalid => 'Adress e-mail invalide ';

  @override
  String validationFieldIsRequired(String fieldName) {
    return '$fieldName est obligatoire ';
  }

  @override
  String validationMinLengthError(String fieldName, int minLength) {
    return '$fieldName doit contenir au moins $minLength caractères';
  }

  @override
  String get validationPhoneNumberIncomplete => 'Numéro de téléphone incomplet.';

  @override
  String get viewButtonText => 'Voir ';

  @override
  String get viewContactDetailAppBarTitle => 'Détails du contact ';

  @override
  String get voucherLoadedSuccessMessage => 'Code promo chargé avec succès ';

  @override
  String get securedByStripe => 'Sécurisé par stripe ';

  @override
  String get nextTrack => 'Piste suivante ';

  @override
  String get stopRadio => 'Arrêter la radio ';

  @override
  String get playRadio => 'Écouter la radio ';

  @override
  String get previousTrack => 'Piste précédente ';

  @override
  String get channel => 'Chaîne ';

  @override
  String get allChannels => 'Toutes les chaînes ';

  @override
  String get acceptButtonText => 'Accepter ';

  @override
  String get declineButtonText => 'Refuser ';

  @override
  String allowShareInfoWithRadio(String radioName) {
    return 'Autorisez-nous à partager vos informations avec $radioName\n';
  }

  @override
  String get nowPlaying => 'En cours de lecture ';

  @override
  String get checkBackLaterText => 'Veuillez réessayer plus tard ';

  @override
  String get refreshText => 'Actualiser ';

  @override
  String get noRadioStationsAvailableText => 'Aucune station de radio disponible ';

  @override
  String get tryAgainText => 'Réessayer ';

  @override
  String get unknownErrorText => 'Une erreur inconnue est survenue ';

  @override
  String get errorLoadingRadioStationsText => 'Échec du chargement des stations de radio ';

  @override
  String get loadingRadioStationsText => 'Chargement des stations de radio...';

  @override
  String get helpCenterPageMenu_4 => 'WhatsApp ( Centre d\'aide)';

  @override
  String validationLengthError(String fieldName, int minLength, int maxLength) {
    return '$fieldName doit contenir entre $minLength et $maxLength caractères.';
  }

  @override
  String get chooseLanguage => 'Choisir la langue';

  @override
  String get selectPreferredLanguage => 'Sélectionnez votre langue préférée';

  @override
  String get cancel => 'Annuler';

  @override
  String get apply => 'Appliquer';

  @override
  String get languageChangeError => 'Échec du changement de langue. Veuillez réessayer.';

  @override
  String get noLanguagesAvailable => 'Aucune langue disponible';

  @override
  String get languageSetupPageAppBarSubtitle => 'Étape 3 sur 3';

  @override
  String welcomeBackText(String firstName) {
    return 'Bon retour, $firstName';
  }

  @override
  String get froggyTalkLovesYou => 'FroggyTalk vous aime.';

  @override
  String get updateLanguagePrompt => 'Veuillez cliquer sur le bouton ci-dessous pour mettre à jour votre langue';

  @override
  String get updateYourLanguage => 'Mettre à jour votre langue.';

  @override
  String get inAppPurchaseLabel => 'Achat Intégré';

  @override
  String get restorePurchases => 'Restore Purchases';

  @override
  String get storePrice => 'Prix en Magasin';

  @override
  String timeLeftModalTitle(String days) {
    return '$days jours restants';
  }

  @override
  String timeLeftModalMessage(String days, int count) {
    return 'Il reste $days jours pour gagner le prix en espèces. Parrainez $count personnes et devenez numéro 1.';
  }

  @override
  String get confirm => 'Confirmer';

  @override
  String get languageSelectionDisclaimer => 'Vous pouvez changer votre langue sous';

  @override
  String get activateAutoCredit => 'Activer le crédit automatique';

  @override
  String get autoAccountCreditHelp => 'seront facturés sur votre carte et ajoutés à votre solde chaque fois qu\'il descendra en dessous de';

  @override
  String get avoidCallDisruption => 'pour éviter d\'être coupé pendant un appel';

  @override
  String cashPrizeAmount(String currencySymbol, String amount) {
    return '$currencySymbol$amount Argent GRATUIT';
  }

  @override
  String monthlyReferralBoard(String month) {
    return '$month Liste Gagnante';
  }

  @override
  String get termsAndConditionsApply => 'Des conditions générales s\'appliquent';

  @override
  String get getNow => 'Obtenez maintenant';

  @override
  String get payWithGooglePay => '\nGoogle Pay';

  @override
  String get payWithApplePay => 'Apple Pay';

  @override
  String paymentSummaryPlayStoreFeeLabel(String percentage) {
    return ' +$percentage% Commission et TVA';
  }

  @override
  String get failedToLoadLeaderboard => 'Échec du chargement du classement';

  @override
  String get retry => 'Réessayer';

  @override
  String get googlePay => 'Google Pay';

  @override
  String appStoreCost(String value) {
    return '+ $value Frais App Store';
  }

  @override
  String get notification_call_reminder_title => 'Call Reminder';

  @override
  String notification_call_reminder_body(String contact) {
    return 'Don\'t forget to call $contact';
  }

  @override
  String get notification_promotion_title => 'Special Offer';

  @override
  String notification_promotion_body(String offer, String description) {
    return 'New offer: $offer - $description';
  }

  @override
  String get notification_security_alert_title => 'Security Alert';

  @override
  String notification_security_alert_body(String alertType, String device) {
    return 'Security alert: $alertType detected on $device';
  }

  @override
  String get notification_daily_reminder_title => 'Daily Reminder';

  @override
  String notification_daily_reminder_body(String reminderType) {
    return 'Don\'t forget: $reminderType';
  }
}
