// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Amharic (`am`).
class AppLocalizationsAm extends AppLocalizations {
  AppLocalizationsAm([String locale = 'am']) : super(locale);

  @override
  String get accountBalanceCardTitle => 'ቀሪ ሂሳብ';

  @override
  String get all => 'ሁሉም';

  @override
  String get allowAccessButtonText => 'ይህን አስፈላጊ እርምጃ ለማካሄድ ፍቃድ ይስጡ';

  @override
  String get appBootingTextBelow => 'ማህበረሰቦች እንደተሳሰሩ እንዲቆዩ በመርዳት ላይ';

  @override
  String get appLanguageAppbarTitle => 'የመተግበሪያ ቋንቋ';

  @override
  String get appTitle => 'FroggyTalk';

  @override
  String buyCreditAmountCustomPlaceholder(String min, String max) {
    return 'ዝቅተኛ $min - ከፍተኛ $max';
  }

  @override
  String get buyCreditAmountRecommended => 'የሚመከረው';

  @override
  String get buyCreditAppBarTitle => 'ካርድ ይግዙ';

  @override
  String get buyCreditButtonText => 'ካርድ ይግዙ';

  @override
  String get buyCreditEnterCustomAmountLabel => 'የሚፈልጉትን መጠን ያስገቡ';

  @override
  String get buyCreditPageTitle => 'ተመራጭ መጠን';

  @override
  String get callButtonText => 'ይደውሉ';

  @override
  String get callingAppBarTitle => 'በመደወል ላይ';

  @override
  String get callingPageBluetoothOptionsText => 'ብሉቱዝ';

  @override
  String get callingPageCreditErrorCardTitle => 'የመደወያ ሂሳብዎ አልቋል';

  @override
  String get callingPagePhoneOptionsText => 'ስልክ';

  @override
  String get callingPageSpeakerOptionsText => 'ስፒከር';

  @override
  String callingPageTimeLeft(String time) {
    return '$time ቀርቷል';
  }

  @override
  String get callLogTypeAnswered => 'የተመለሰ';

  @override
  String get callLogTypeBusy => 'መስመሩ ተይዟል';

  @override
  String get callLogTypeCancel => 'የተሰረዘ';

  @override
  String get callLogTypeIncoming => 'ገቢ ጥሪ';

  @override
  String get callLogTypeMissed => 'ያልተመለሰ ጥሪ';

  @override
  String get callLogTypeOutgoing => 'ወጪ ጥሪ';

  @override
  String get callLogTypeUnavailable => 'ማግኘት ኣልተቻለም';

  @override
  String get callRatesAppBarTitle => 'የጥሪ ተመኖች';

  @override
  String get callTypeAnswered => 'ወጪ ጥሪ';

  @override
  String get callTypeBusy => 'መስመሩ ተይዟል';

  @override
  String get callTypeCancel => 'የተሰረዘ ጥሪ';

  @override
  String get callTypeIncoming => 'ገቢ ጥሪ';

  @override
  String get callTypeMissed => 'ያልተመለሰ ጥሪ';

  @override
  String get callTypeOutgoing => 'ወጪ ጥሪ';

  @override
  String get callTypeUnavailable => 'ደንበኛውን ማግኘት ኣልተቻለም';

  @override
  String get cancelButtonText => 'ለመሰረዝ';

  @override
  String get changeChannelButtonText => 'ጣብያ ለመቀየር';

  @override
  String get chatWithLiveAgentAppbarTitle => 'ቀጥታ ወኪል';

  @override
  String get closeButtonText => 'ለመዝጋት';

  @override
  String get confirmationAppBarTitle => 'ማረጋገጫ';

  @override
  String get confirmationFailedButtonText => 'እንደገና ይሞክሩ';

  @override
  String get confirmationFailedContactSupportDescription => 'ማንኛውም አይነት ጥያቄ ካለዎት ወይም እርዳታ ካስፈለግዎ እባክዎን የድጋፍ ቡድናችንን ያነጋግሩ';

  @override
  String get confirmationFailedContactSupportText => 'የደንበኛ ማዕከል ያነጋግሩ';

  @override
  String get confirmationFailedDescription => 'ክፍያዎ አልተሳካም። እባክዎን እንደገና ይሞክሩ';

  @override
  String get confirmationFailedTitle => 'ክፍያ አልተሳካም';

  @override
  String get confirmationSuccessButtonText => 'ተከናውኗል';

  @override
  String confirmationSuccessDescription(String amount) {
    return '$amount በተሳካ ሁኔታ ወደ ሂሳብዎ ገቢ ሆኗል';
  }

  @override
  String get confirmationSuccessTitle => 'ክፍያ ተሳክቷል';

  @override
  String get contactsAllContactsTabText => 'ስልክ ቁጥሮች';

  @override
  String contactsCallRateText(String min, String secs) {
    return '$min ደቂቃዎች $secs ሰከንድ\n';
  }

  @override
  String get contactsFavouriteCallsTabText => 'የተመረጡ';

  @override
  String get contactsNoCallsEmptyMessage => 'ምንም የቅርብ ጊዜ ጥሪዎች የሉዎትም';

  @override
  String get contactsNoContactsButtonText => 'ስልክ ቁጥር ለማየት';

  @override
  String get contactsSearchContactsPlaceholder => 'ስልክ ቁጥር አድራሻ ስም ለመፈለግ';

  @override
  String get contactsSearchForContactsPlaceholder => 'ስልክ ቁጥር አድራሻ በስም ለመፈለግ';

  @override
  String get couponAppliedButtonText => 'ተግባራዊ ሆኗል';

  @override
  String get couponApplyButtonText => 'ተግባራዊ ለማድረግ';

  @override
  String get credit => 'ሂሳብ';

  @override
  String get deleteAccountWarning => 'ሊወስዱት ያለው እርምጃ በኋላ ሊቀለበስ አይችልም። እባክዎ ሂሳብዎን ማጥፋት እንደሚፈልጉ ያረጋግጡ።';

  @override
  String get dialerAppBarTitle => 'መደወያ';

  @override
  String get dialerCopyActionText => 'ለመቅዳት';

  @override
  String get dialerErrorInvalidNumber => 'የተሳሳተ ቁጥር';

  @override
  String get dialerErrorWithoutCountryCode => 'የሀገር ኮድ ያክሉ (ለምሳሌ +1)';

  @override
  String get dialerKeypadABC => 'ABC';

  @override
  String get dialerKeypadDEF => 'DEF';

  @override
  String get dialerKeypadGHI => 'GHI';

  @override
  String get dialerKeypadJKL => 'JKL';

  @override
  String get dialerKeypadMNO => 'MNO';

  @override
  String get dialerKeypadPQRS => 'PQRS';

  @override
  String get dialerKeypadTUV => 'TUV';

  @override
  String get dialerKeypadWXYZ => 'WXYZ';

  @override
  String get dialerMuteStatusMuted => 'ድምፅ ጠፍቷል';

  @override
  String get dialerMuteStatusUnmuted => 'ድምፅ ይሰማል';

  @override
  String get dialerPasteActionText => 'ለመለጠፍ';

  @override
  String get dialerSearchResultsHeader => 'ውጤቶች';

  @override
  String get dialerSearchResultsNoResults => 'ምንም ውጤቶች አልተገኙም';

  @override
  String get dialerStatusConnected => 'ተገናኝቷል';

  @override
  String get dialerStatusConnecting => 'በመደወል ላይ';

  @override
  String get dialerStatusEnded => 'ተጠናቋል';

  @override
  String get dialerStatusError => 'ስህተት ተከስቷል';

  @override
  String get dialerStatusFailedToConnect => 'መገናኘት አልተቻለም';

  @override
  String get dialerStatusHold => 'መስመር ላይ በመቆየት';

  @override
  String get dialerStatusInitial => 'በመደወል ላይ';

  @override
  String get dialerStatusInitiating => 'በመደወል ላይ';

  @override
  String get dialerStatusRinging => 'እየጠራ ነው';

  @override
  String get dialerStatusUnknown => 'ያልታወቀ';

  @override
  String get emptyContactList => 'የስልክ ቁጥር ዝርዝር የለዎትም';

  @override
  String get emptyFavouriteContactList => 'የተመረጡ የስልክ ቁጥሮች ዝርዝር የለዎትም።';

  @override
  String get enterCouponOptionalPlaceholder => 'ኩፖን ያስገቡ (ካለዎት)';

  @override
  String get enterOtpPageAppBarSubtitle => 'ክፍል 2 ከ 3';

  @override
  String get enterOtpPageAppBarTitle => 'እንጀምር';

  @override
  String get enterOtpPageEditButton => 'የተሳሳተ የWhatsApp ቁጥር? እባክዎን እንደገና ይሞክሩ';

  @override
  String get enterOtpPageErrorMessage => 'የተሳሳተ ኮድ';

  @override
  String get enterOtpPagePhoneLabel => 'የማረጋገጫ ኮድዎን ያስገቡ';

  @override
  String enterOtpPagePhoneLabelDescription(String phoneNumber) {
    return 'ባለ 4-አሃዝ ኮድ ወደ WhatsApp ቁጥርዎ $phoneNumber ተልኳል';
  }

  @override
  String get enterOtpPageResendOtpButton => 'ኮድ ዳግም ለመላክ';

  @override
  String enterOtpPageResendOtpRetries(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count ተጨማሪ ሙከራዎች',
      one: 'አንድ የመጨረሻ ሙከራ',
      zero: 'ምንም ሙከራዎች አልቀሩም',
    );
    return '$_temp0';
  }

  @override
  String get enterOtpPageResendOtpRetriesError => 'በዚህ የWhatsApp ቁጥር ለሚቀጥሉት 24 ሰዓታት የማረጋገጫ ኮዶችን መቀበል አይችሉም';

  @override
  String get enterOtpPageSubmitButton => 'የአንድ ጊዜ የይለፍ ቃሉን ለማረጋገጥ';

  @override
  String get enterVoucherCodeLabel => 'የካርዱ ኮድ ያስገቡ';

  @override
  String get free => 'ነፃ';

  @override
  String get freeCreditAppbarTitle => 'ነፃ ክሬዲት';

  @override
  String get freeCreditPageContent_1 => 'የመጋበዣ ኮድዎን ለጓደኞችዎ እና ለቤተሰብዎ ያገሩ።';

  @override
  String get freeCreditPageContent_2 => 'FroggyTalkን በማውረድ የእርስዎን የመጋበዣ ኮድ በመጠቀም ይመዝገቡ።';

  @override
  String freeCreditPageContent_3(String amount) {
    return 'ግለሰቡ ለመጀመሪያ ጊዜ ክሬዲት ሲገዛ $amount ያገኛሉ።';
  }

  @override
  String freeCreditPageHeadingText(String amount) {
    return 'ጓደኛዎን ጋብዘው $amount ያግኙ';
  }

  @override
  String get freeCreditPageShareReferralLinkButtonText => 'የመጋበዣ ማስፈንጠሪያ ያጋሩ';

  @override
  String freeCreditPageSubHeadingText(String amount) {
    return 'እኔ የጋበዝኳቸው ($amount)';
  }

  @override
  String get froggytalkCustomerLabel => 'የFroggytalk ደንበኛ';

  @override
  String get helpCenterAppbarTitle => 'የእገዛ ማዕከል';

  @override
  String get helpCenterPageMenu_1 => 'ቅሬታ ወይም አስተያየት ይስጡ';

  @override
  String get helpCenterPageMenu_2 => 'ከቀጥታ ወኪል ጋር ይወያዩ';

  @override
  String get helpCenterPageMenu_3 => 'በተደጋጋሚ የሚጠየቁ ጥያቄዎች';

  @override
  String get homeNavigationBarText => 'ዋና ገጽ';

  @override
  String get internetConnectionAlertTextError => 'ውይ! ከመስመር ውጭ ሳይሆኑ ኣይቀሩል';

  @override
  String get internetConnectionAlertTextSuccess => 'ሁሉም ዝግጁ! እንደገና ግንኙ ሆነዋል።';

  @override
  String get invalidInternationalPhoneFormat => 'የተሳሳተ የዓለም አቀፍ ስልክ ቁጥር አቀማመጥ';

  @override
  String get invalidPhoneNumber => 'እባክዎ ትክክለኛ የስልክ ቁጥር ያስገቡ';

  @override
  String get keypadNavigationBarText => 'መጻፊያ';

  @override
  String get landlineText => 'የመስመር ስልክ';

  @override
  String get loadVoucherCardButtonText => 'ለመሙላት';

  @override
  String get loadVoucherCardErrorText => 'የተሳሳተ ኮድ፤ ደግመው ይሞክሩ';

  @override
  String get loadVoucherCardLabelText => 'ባለ 10 አሃዝ የካርድ ኮዱን ያስገቡ';

  @override
  String get loadVoucherCardTitle => 'የካርድ ኮድ ያስገቡ';

  @override
  String get loadVoucherCodeTitle => 'የካርዱ ኮድ ለማስገባት';

  @override
  String get loginPageAppBarSubtitle => 'ክፍል 1 ከ 3';

  @override
  String get loginPageAppBarTitle => 'እንጀምር';

  @override
  String get loginPageCheckboxLabel => 'የግብዣ ኮድ አለኝ';

  @override
  String get loginPageErrorMessage => 'ከዚህ ቁጥር ጋር የተያያዘ የWhatsApp መለያ አልተገናኘም።';

  @override
  String get loginPagePhoneLabel => 'የWhatsApp ቁጥርዎን ያስገቡ';

  @override
  String get loginPagePhoneLabelDescription => 'የአንድ ጊዜ ማረጋገጫ ኮድ ወደ WhatsApp ይላክልዎታል';

  @override
  String get loginPagePhoneNumberError => 'ስልክ ቁጥሮዎ አልተሟላም። እባክዎ ያሟሉት';

  @override
  String get loginPageReferralLabel => 'የግብዣ ኮድ ያስገቡ';

  @override
  String get loginPageSubmitButton => 'ማረጋገጫ ይጠይቁ';

  @override
  String minsOrMin(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count ደቂቃዎች',
      one: '1 ደቂቃ',
      zero: '',
    );
    return '$_temp0';
  }

  @override
  String get mobileText => 'ሞባይል';

  @override
  String get moreAppbarTitle => 'ሂሳብ';

  @override
  String get moreNavigationBarText => 'ተጨማሪ';

  @override
  String get morePageAccountBalanceCardTitle => 'ቀሪ ሂሳብ';

  @override
  String get morePageAppVersionMenuText => 'የመተግበሪያ ስሪት';

  @override
  String get morePageCallRatesMenuText => 'የጥሪ ተመኖች';

  @override
  String get morePageHelpCenterMenuText => 'የእገዛ ማዕከል';

  @override
  String get morePageLanguageMenuText => 'ቋንቋ';

  @override
  String get morePageLoadVoucherMenuText => 'ካርድ ለመሙላት';

  @override
  String get morePageLogoutMenuText => 'ዘግተው ለመውጣት';

  @override
  String get morePageProfileMenuText => 'ፕሮፋይል';

  @override
  String get morePageRadioMenuText => 'ሬድዮ';

  @override
  String get morePageReferralCodeCardButtonText => 'ተጨማሪ መረጃ';

  @override
  String morePageReferralCodeCardContent(int numberOfPeople) {
    return '$numberOfPeople ሰዎች የእርስዎን የመጋበዣ ኮድ ተጠቅመዋል';
  }

  @override
  String morePageReferralCodeCardDescription(String amount) {
    return 'ጓደኞችዎን እና ቤተሰብዎን ጋብዘው $amount ያግኙ';
  }

  @override
  String get morePageReferralCodeCardTitle => 'መጋበዣ ኮድ';

  @override
  String get noBuyCreditOptionsAvailable => 'ምንም ካርድ የመግዣ አማራጮች አልተገኙም';

  @override
  String get noFavoriteContactsMessage => 'የተመረጡ ስልክ ቁጥሮች የሉም';

  @override
  String get noNotificationsYet => 'እስካሁን ምንም ማሳወቂያዎች የሉም';

  @override
  String get notEnoughCreditMessage => 'በቂ ሂሳብ የለዎትም';

  @override
  String get notification => 'ማሳወቂያ';

  @override
  String get notificationsAppBarTitle => 'ማሳወቂያዎች';

  @override
  String get notificationsEmptyMessage => 'ምንም ማሳወቂያዎች የሉዎትም';

  @override
  String get notificationSettingsAppBarTitle => 'የማሳወቂያ መሳሪያዎች';

  @override
  String get notificationSettingsAutoDeleteNotificationText => 'ማሳወቂያ በራስ ሰር ይሰረዝ';

  @override
  String get notificationSettingsNotificationSoundText => 'የማሳወቂያ ድምፅ';

  @override
  String get notificationsTabAllText => 'ሁሉም';

  @override
  String get notificationsTabUnreadText => 'ያልተነበበ';

  @override
  String notificationsTimeAgo(int time, String duration) {
    String _temp0 = intl.Intl.pluralLogic(
      time,
      locale: localeName,
      other: '$duration',
      one: '$duration',
    );
    return 'ከ $time $_temp0 በፊት';
  }

  @override
  String get okButtonText => 'እሺ';

  @override
  String get onboardingPageFooterAndText => 'እና';

  @override
  String get onboardingPageFooterPrivacy => 'የግል ጉዳዮች ፖሊሲ';

  @override
  String get onboardingPageFooterTermsConditionsText => 'ውሎች እና ሁኔታዎች';

  @override
  String get onboardingPageFooterText => 'እንጀምር የሚለውን ሲጫኑ በተጨማሪ ተቀባይ የሚያደርጉት';

  @override
  String get onboardingPageSliderHeader1 => 'ባለብዙ ቋንቋ';

  @override
  String get onboardingPageSliderHeader2 => 'በዓለም ዙሪያ ወዳለ የትኛውም የሞባይል ወይም መደበኛ ስልክ ቁጥሮች ይደውሉ';

  @override
  String get onboardingPageSliderHeader3 => 'ቀላል ክፍያ';

  @override
  String get onboardingPageSliderText1 => 'FroggyTalk የፈለጉትን ቋንቋ እንዲመርጡ ያስችለዎታል ለምሳሌ እነደ አማርኛ፣ ትግርኛ፣ ሀውሳ ወዘተ።';

  @override
  String get onboardingPageSliderText2 => 'በአለም ላይ ባለ ወደ ማንኛውም ሞባይል ወይም መደበኛ ስልክ ይደውሉ። ተቀባዩ ዘመናዊ ስልክ ወይም ኢንተርኔት እንዲኖሮት አያስፈልም።';

  @override
  String get onboardingPageSliderText3 => 'የመረጡትን የመክፈያ ዘዴ በመጠቀም በአገር ውስጥ ምንዛሬ የጥሪ ካርድ ይግዙ።';

  @override
  String get onboardingPageSubmitButtonText => 'እንጀምር';

  @override
  String get outOfCreditLabel => 'ቀሪ ሂሳብ የለዎትም';

  @override
  String get paymentFailureAppBarTitle => 'አልተሳካም';

  @override
  String get paymentFailureHomeButton => 'ዋና ገጽ';

  @override
  String paymentFailureMessage(String amount) {
    return 'የ $amount ክፍያዎ አልተሳካም';
  }

  @override
  String get paymentFailureTitle => 'ክፍያ አልተሳካም';

  @override
  String get paymentFailureTryAgainButton => 'እንደገና ለመሞከር';

  @override
  String get paymentOptionsAppBarTitle => 'የክፍያ አማራጮች';

  @override
  String paymentOptionsAutoCreditDescription(String amount, String minAmount) {
    return 'ይህ ሂሳብዎ ከ $minAmount በታች በሚሆንበት ጊዜ በራስ-ሰር ወደ ሂሳብዎ የ $amount ገቢ የሚያደርግልዎት ይሆናል።';
  }

  @override
  String get paymentOptionsAutoCreditTitle => 'በራሱ የሚሞላ';

  @override
  String get paymentOptionsSelectPaymentMethod => 'የሚፈልጉት የክፍያ ዘዴ ይምረጡ';

  @override
  String get paymentOptionsSummaryAmountToCreditText => 'ገቢየሚሆነው መጠን';

  @override
  String get paymentOptionsSummaryDiscountButtonText => 'ለመተግበር';

  @override
  String get paymentOptionsSummaryDiscountPlaceholder => 'የቅናሽ ኮድ ያስገቡ (ካለዎት)';

  @override
  String get paymentOptionsSummaryDiscountText => 'ቅናሽ';

  @override
  String get paymentOptionsSummaryTotalPaymentText => 'ጠቅላላ ክፍያ';

  @override
  String get paymentOptionsSummaryVatFeesText => 'ተጨማሪ እሴት ታክስ + ክፍያዎች';

  @override
  String get paymentSummaryAmountToCreditLabel => 'ገቢ የሚደረግ መጠን';

  @override
  String get paymentSummaryDiscountLabel => 'ቅናሽ';

  @override
  String get paymentSummaryTotalLabel => 'ጠቅላላ ክፍያ';

  @override
  String get paymentSummaryVatFeesLabel => 'የተጨማሪ እሴት ታክስ + ክፍያዎች';

  @override
  String perMinRate(String rate) {
    return '$rate/ደቂቃ\n';
  }

  @override
  String get perMinRateSingle => '/ደቂቃ';

  @override
  String perMinuteRate(String rate) {
    return '$rate/ደቂቃ\n';
  }

  @override
  String perMinuteSlashLabel(String min) {
    return '$min/ደቂቃዎች';
  }

  @override
  String get permissionButtonAccept => 'ፍቃድ ለመስጠት';

  @override
  String get permissionButtonSkip => 'ይዝለሉ';

  @override
  String get permissionForCameraTitle => 'ፎቶና ቪዲዮ ለማንሳት ፍቃድ ያስፈልገናል';

  @override
  String get permissionForContactListTitle => 'FroggyTalk ወደ የስልክ ቁጥር ዝርዝርዎ መግባት እንዲችን ይፍቀዱ';

  @override
  String get permissionForMicrophoneTitle => 'FroggyTalk ጥሪዎች ማስተናገድ እንዲችል ማይክሮፎንዎን የመጠቀም ፈቃድ ይስጡ';

  @override
  String get permissionForNotificationTitle => 'FroggyTalk ማሻሻያዎች እንዲልክልዎ ይፍቀዱ';

  @override
  String get permissionForStorageTitle => 'ጊዜያዊ ውሂቦችን በስልክዎ ላይ ለማከማቸት ፍቃድ ያስፈልገናል';

  @override
  String get permissionPermanentlyDeniedMessage => 'ፈቃድ በቋሚነት ተከልክሏል፣ እባክዎ በመተግበሪያ ቅንብሮች ውስጥ እንዲከፍቱት ያረጋግጡ';

  @override
  String get permissionDeniedMessage => 'ፍቃድ ተከልክሏል፣ እባክዎ በመተግበሪያው ቅንብሮች ውስጥ ያንቁት';

  @override
  String perM_Rate(String rate) {
    return '$rate/ደቂቃ\n';
  }

  @override
  String get phoneNumberStartRule => 'እባክዎ ሲያስገቡ በ + ወይም 00 ይጀምሩ';

  @override
  String get proceedToPaymentButtonText => 'ለመቀጠል';

  @override
  String get profileAppbarTitle => 'ፕሮፋይል';

  @override
  String get profileDeleteAccountButtonText => 'ኣካውንት ለመሰረዝ';

  @override
  String get profileDeleteAccountCardContent => 'እባክዎ ኣካውንቱ እንዲሰረዝ እንደሚፈልጉ ያረጋግጡ። በሂሳብዎ ላይ ማንኛውንም ቀሪ ሂሳብ ያጣሉ።';

  @override
  String get profileDeleteAccountCardTitle => 'ኣካውንት የተሰረዘ';

  @override
  String get profileLabel => 'መገለጫ';

  @override
  String get profileLabelChangeLocation => 'አካባቢን ይቀይሩ';

  @override
  String get profileLabelEmailAddress => 'የኢሜል አድራሻ ያስገቡ';

  @override
  String get profileLabelFullName => 'ሙሉ ስም ያስገቡ';

  @override
  String get profileUpdatedSuccessfullyMessage => 'መገለጫዎ በተሳካ ሁኔታ ተሻሽሏል';

  @override
  String get profileUpdateFailedMessage => 'መገለጫዎን ማሻሻያ ኣልተቻለም';

  @override
  String get quickAdvertSubtitle => 'በመላው ዓለም ላሉ ቤተሰብዎ የአየር ሰዓት ይግዙ';

  @override
  String get quickAdvertTitle => 'ካርድ ወደ 140+ አገሮች ይላኩ';

  @override
  String get radioAppBarTitle => 'ሬዲዮ';

  @override
  String get radioComingSoonText => 'ሬድዮ በቅርቡ ይጀምራል !!!';

  @override
  String get radioPageChooseChannelButtonText => 'ጣብያ ለመምረጥ';

  @override
  String get radioPageNoChannelsText => 'ምንም ጣብያዎች አልተገኙም።';

  @override
  String get radioPageSearchPlaceholder => 'ጣብያ ለመምረጥ';

  @override
  String get ratesNavigationBarText => 'ተመኖች';

  @override
  String get recentCallsText => 'የቅርብ ጊዜ ጥሪዎች';

  @override
  String get rechargeButtonText => 'ለመሙላት';

  @override
  String get referAndEarn => 'ጋብዘው ገቢ ያግኙ';

  @override
  String get referralCardButtonText => 'ነፃ ካርድ';

  @override
  String referralCardDescription1(String percentageAmount) {
    return 'ሰው በመጋበዝ የ $percentageAmount ነፃ ካርድ ያግኙ።';
  }

  @override
  String referralCardDescription2(int numberOfPeople, num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$numberOfPeople ሰዎች የግብዣ ኮድዎን ተጠቅመዋል።',
      one: 'አንድ ሰው የግብዣ ኮድዎን ተጠቅመዋል።',
      zero: 'ማንም የግብዣ ኮድዎን አልተጠቀምም።',
    );
    return '$_temp0';
  }

  @override
  String get referralCardTitle => 'መጋበዣ ኮድ';

  @override
  String get referralCodeOnCopyActionResponse => 'FroggyTalk ለጊዜው ተቀምጧል';

  @override
  String get saveAndProceedToPaymentButtonText => 'ለማስቀመጥ & ወደ ክፍያ ለመቀጠል';

  @override
  String get saveChangesButtonText => 'ለውጦችን ያስቀምጡ';

  @override
  String get savedSuccessfullyMessage => 'በተሳካ ሁኔታ ተቀምጧል';

  @override
  String get searchCountryPlaceholder => 'ሀገር ለመፈለፍ';

  @override
  String get searchFavouriteContactMessage => 'የተመረጡ ስልክ ቁጥሮች ለመፈለግ';

  @override
  String get searchForCountryNoResults => 'ምንም ውጤቶች አልተገኙም';

  @override
  String get searchForCountryPlaceholder => 'ሀገር ለመፈለግ';

  @override
  String get searchRecentCallsMessage => 'የቅርብ ጊዜ ጥሪዎችን ለመፈለግ';

  @override
  String secsOrSec(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count ሰከንዶች',
      one: '1 ሰከንድ',
      zero: '',
    );
    return '$_temp0';
  }

  @override
  String get selectCountryPlaceholder => 'ሀገር ለመምረጥ';

  @override
  String get sendCreditButtonText => 'ካርድ ለመላክ';

  @override
  String get settings => 'መሳሪያዎች';

  @override
  String shareReferralText(String appLink, String referralCode) {
    return 'የFroggyTalk መተግበሪያውን $appLink አውርዳው በእኔ የግብዣ ኮድ $referralCode ይመዝገቡ። ከሁሉም የቀነሰ ተመን እና የዓለም አቀፍ ጥሪዎችን ማድረግ ያስችልዎታል።';
  }

  @override
  String get somethingWentWrongMessage => 'ስህተት ተከስቷል። እባክዎ እንደገና ይሞክሩ።';

  @override
  String get unread => 'ያልተነበበ';

  @override
  String get updateProfileButtonText => 'መገለጫዎን ለማሻሻል';

  @override
  String get upgradeDialogButtonNo => 'አሁን አይደለም';

  @override
  String get upgradeDialogButtonYes => 'ለማሻሻል';

  @override
  String get upgradeDialogMessage => 'የFroggyTalk አዲስ እትም ወጥቷል። አሁን ማዘመን ይፈልጋሉ?';

  @override
  String get upgradeDialogTitle => 'አዳዲስ እትም';

  @override
  String get validationCouponCodeIncomplete => 'ያልተሟላ ወይም የተሳሳተ የቅናሽ ኮድ';

  @override
  String get validationCouponInvalid => 'እባክዎ ትክክለኛ የቅናሽ ኮድ ያስገቡ';

  @override
  String get validationEmailInvalid => 'የተሳሳተ የኢሜል አድራሻ።';

  @override
  String validationFieldIsRequired(String fieldName) {
    return '$fieldName ያስፈልጋል';
  }

  @override
  String validationMinLengthError(String fieldName, int minLength) {
    return '$fieldName ቢያንስ $minLength ቁልዎች ርዝመት ሊኖረው ይገባል';
  }

  @override
  String get validationPhoneNumberIncomplete => 'ያልተሟላ የስልክ ቁጥር።';

  @override
  String get viewButtonText => 'ለማየት';

  @override
  String get viewContactDetailAppBarTitle => 'የስልክ ቁጥር ዝርዝር';

  @override
  String get voucherLoadedSuccessMessage => 'ካርዱ በተሳካ ሁኔታ ገብቷል';

  @override
  String get securedByStripe => 'በስትራይፕ የተጠበቀ';

  @override
  String get nextTrack => 'ቀጣይ ትራክ';

  @override
  String get stopRadio => 'ሬዲዮን ማቆም';

  @override
  String get playRadio => 'ሬዲዮ ማጫወት';

  @override
  String get previousTrack => 'ቀዳሚ ትራክ';

  @override
  String get channel => 'ጣቢያ';

  @override
  String get allChannels => 'ሁሉም ጣቢያዎች';

  @override
  String get acceptButtonText => 'መቀበል';

  @override
  String get declineButtonText => 'መከልከል';

  @override
  String allowShareInfoWithRadio(String radioName) {
    return 'መረጃዎን ከ $radioName ጋር እንድናጋራ ይፍቀዱልን';
  }

  @override
  String get nowPlaying => 'አሁን በመጫወት ላይ';

  @override
  String get checkBackLaterText => 'እባክዎ በኋላ እንደገና ይመልከቱ';

  @override
  String get refreshText => 'ለማደስ';

  @override
  String get noRadioStationsAvailableText => 'ምንም የሬዲዮ ጣቢያዎች አልተገኙም';

  @override
  String get tryAgainText => 'እንደገና ይሞክሩ';

  @override
  String get unknownErrorText => 'ያልታወቀ ስህተት ተከስቷል';

  @override
  String get errorLoadingRadioStationsText => 'የሬዲዮ ጣቢያዎችን መጫን አልተቻለም';

  @override
  String get loadingRadioStationsText => 'የሬዲዮ ጣቢያዎችን በመጫን ላይ...';

  @override
  String get helpCenterPageMenu_4 => 'WhatsApp';

  @override
  String validationLengthError(String fieldName, int minLength, int maxLength) {
    return '$fieldName ከ$minLength እስከ $maxLength ቁምፊዎች መሆን አለበት';
  }

  @override
  String get chooseLanguage => 'Choose Language';

  @override
  String get selectPreferredLanguage => 'የሚፈልጉትን  ቋንቋ ይምረጡ';

  @override
  String get cancel => 'መሰረዝ ';

  @override
  String get apply => 'መተግበር ';

  @override
  String get languageChangeError => 'ቋንቋ መቀየር አልተቻለም። እባክዎትን ደግመው ይሞክሩ ';

  @override
  String get noLanguagesAvailable => 'ቋንቋዎች የሉም';

  @override
  String get languageSetupPageAppBarSubtitle => 'ክፍል 3 ከ 3';

  @override
  String welcomeBackText(String firstName) {
    return 'እንኳን በደህና ተመለሱ፣ $firstName';
  }

  @override
  String get froggyTalkLovesYou => 'ፍሮጊታልክ ይወድዎታል።';

  @override
  String get updateLanguagePrompt => 'እባክዎ ቋንቋዎን ለማዘመን ከታች ያለውን አዝራር ይጫኑ';

  @override
  String get updateYourLanguage => 'ቋንቋዎን ያዘምኑ።';

  @override
  String get inAppPurchaseLabel => 'በመተግበሪያው ውስጥ መግዛት';

  @override
  String get restorePurchases => 'ግዢን ወደነበረበት መመለስ';

  @override
  String get storePrice => 'የመደብር ዋጋ';

  @override
  String timeLeftModalTitle(String days) {
    return '$days ቀናቶች ቀሩ';
  }

  @override
  String timeLeftModalMessage(String days, int count) {
    return '$daysየገንዘብ ሽልማቱ አሸናፊ ለመሆን ቀናት ቀርተዋል። ይጋብዙ $count ሰዎችን እና አንደኛ ቁጥር ይሁኑ';
  }

  @override
  String get confirm => 'Confirm';

  @override
  String get languageSelectionDisclaimer => 'ቋንቋዎን ከስር መቀየር ይችላሉ።';

  @override
  String get activateAutoCredit => 'አውቶ ክሬዲት ተግባራዊ አድርጉ';

  @override
  String get autoAccountCreditHelp => 'ወደ ካርድዎ እንዲከፍል እና ከታች በወደቀ ቁጥር ወደ ሂሳብዎ ይጨመራል';

  @override
  String get avoidCallDisruption => 'በጥሪ ጊዜ መቆራረጥን ለማስወገድ';

  @override
  String cashPrizeAmount(String currencySymbol, String amount) {
    return '$currencySymbol$amount ነፃ ገንዘብ ';
  }

  @override
  String monthlyReferralBoard(String month) {
    return '$month የፕሮሞ መሪዎች ';
  }

  @override
  String get termsAndConditionsApply => 'ውሎች እና ሁኔታዎች ተፈጻሚ ይሆናሉ';

  @override
  String get getNow => 'አሁን ያግኙ ';

  @override
  String get payWithGooglePay => 'Google Pay';

  @override
  String get payWithApplePay => 'Apple Pay';

  @override
  String paymentSummaryPlayStoreFeeLabel(String percentage) {
    return '  + $percentage% ኮሚሸን እና ቫት';
  }

  @override
  String get failedToLoadLeaderboard => 'የሊደርቦርድን መጫን አልተሳካም።';

  @override
  String get retry => 'እንደገና ይሞክሩ';

  @override
  String get googlePay => 'Google Pay';

  @override
  String appStoreCost(String value) {
    return '+ $value የመደብር ዋጋ';
  }

  @override
  String get notification_call_reminder_title => 'Call Reminder';

  @override
  String notification_call_reminder_body(String contact) {
    return 'Don\'t forget to call $contact';
  }

  @override
  String get notification_promotion_title => 'Special Offer';

  @override
  String notification_promotion_body(String offer, String description) {
    return 'New offer: $offer - $description';
  }

  @override
  String get notification_security_alert_title => 'Security Alert';

  @override
  String notification_security_alert_body(String alertType, String device) {
    return 'Security alert: $alertType detected on $device';
  }

  @override
  String get notification_daily_reminder_title => 'Daily Reminder';

  @override
  String notification_daily_reminder_body(String reminderType) {
    return 'Don\'t forget: $reminderType';
  }
}
