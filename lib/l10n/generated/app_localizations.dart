import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_am.dart';
import 'app_localizations_ar.dart';
import 'app_localizations_din.dart';
import 'app_localizations_en.dart';
import 'app_localizations_fr.dart';
import 'app_localizations_ha.dart';
import 'app_localizations_ti.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'generated/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('am'),
    Locale('ar'),
    Locale('din'),
    Locale('en'),
    Locale('fr'),
    Locale('ha'),
    Locale('ti')
  ];

  /// Title of the account balance page
  ///
  /// In en, this message translates to:
  /// **'Balance'**
  String get accountBalanceCardTitle;

  /// Text shown for all notifications
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get all;

  /// Text shown when requesting the user to allow access to device features
  ///
  /// In en, this message translates to:
  /// **'Allow Access to Perform this Required Action'**
  String get allowAccessButtonText;

  /// Text that shows below the app title on the booting screen
  ///
  /// In en, this message translates to:
  /// **'Helping communities stay connected'**
  String get appBootingTextBelow;

  /// This is a title for the app bar
  ///
  /// In en, this message translates to:
  /// **'App Language'**
  String get appLanguageAppbarTitle;

  /// The name of the app
  ///
  /// In en, this message translates to:
  /// **'FroggyTalk'**
  String get appTitle;

  /// Placeholder text for the custom amount input field on the buy credit page
  ///
  /// In en, this message translates to:
  /// **'min {min} - max {max}'**
  String buyCreditAmountCustomPlaceholder(String min, String max);

  /// Text that shows above the recommended amount on the buy credit page
  ///
  /// In en, this message translates to:
  /// **'Recommended'**
  String get buyCreditAmountRecommended;

  /// The app page title for buy credit
  ///
  /// In en, this message translates to:
  /// **'Buy Credit'**
  String get buyCreditAppBarTitle;

  /// Text for the buy credit button on the account balance page
  ///
  /// In en, this message translates to:
  /// **'Buy credit'**
  String get buyCreditButtonText;

  /// Label for the custom amount input field on the buy credit page
  ///
  /// In en, this message translates to:
  /// **'Enter custom amount'**
  String get buyCreditEnterCustomAmountLabel;

  /// Title of the buy credit page
  ///
  /// In en, this message translates to:
  /// **'Preferred amount'**
  String get buyCreditPageTitle;

  /// Text for the call action button on a page
  ///
  /// In en, this message translates to:
  /// **'Call'**
  String get callButtonText;

  /// Title of the calling page
  ///
  /// In en, this message translates to:
  /// **'Calling'**
  String get callingAppBarTitle;

  /// Text that shows to toggle the bluetooth on the calling page
  ///
  /// In en, this message translates to:
  /// **'Bluetooth'**
  String get callingPageBluetoothOptionsText;

  /// No description provided for @callingPageCreditErrorCardTitle.
  ///
  /// In en, this message translates to:
  /// **'Out of call credit'**
  String get callingPageCreditErrorCardTitle;

  /// Text that shows to toggle the phone on the calling page
  ///
  /// In en, this message translates to:
  /// **'Phone'**
  String get callingPagePhoneOptionsText;

  /// Text that shows to toggle the speaker on the calling page
  ///
  /// In en, this message translates to:
  /// **'Speaker'**
  String get callingPageSpeakerOptionsText;

  /// Text that shows the time left for the call to end
  ///
  /// In en, this message translates to:
  /// **'{time} left'**
  String callingPageTimeLeft(String time);

  /// Text that shows when the call log type is answered
  ///
  /// In en, this message translates to:
  /// **'Answered'**
  String get callLogTypeAnswered;

  /// Text that shows when the call log type is busy
  ///
  /// In en, this message translates to:
  /// **'Busy'**
  String get callLogTypeBusy;

  /// Text that shows when the call log type is cancelled
  ///
  /// In en, this message translates to:
  /// **'Cancelled'**
  String get callLogTypeCancel;

  /// Text that shows when the call log type is incoming
  ///
  /// In en, this message translates to:
  /// **'Incoming'**
  String get callLogTypeIncoming;

  /// Text that shows when the call log type is missed
  ///
  /// In en, this message translates to:
  /// **'Missed'**
  String get callLogTypeMissed;

  /// Text that shows when the call log type is outgoing
  ///
  /// In en, this message translates to:
  /// **'Outgoing'**
  String get callLogTypeOutgoing;

  /// Text that shows when the call log type is unavailable
  ///
  /// In en, this message translates to:
  /// **'Unavailable'**
  String get callLogTypeUnavailable;

  /// Title of the call rates page
  ///
  /// In en, this message translates to:
  /// **'Call rates'**
  String get callRatesAppBarTitle;

  /// Text that shows when the call is answered
  ///
  /// In en, this message translates to:
  /// **'Outgoing call'**
  String get callTypeAnswered;

  /// Text that shows when the call is busy
  ///
  /// In en, this message translates to:
  /// **'Busy'**
  String get callTypeBusy;

  /// Text that shows when the call is cancelled
  ///
  /// In en, this message translates to:
  /// **'Cancelled call'**
  String get callTypeCancel;

  /// Text that shows when the call is incoming
  ///
  /// In en, this message translates to:
  /// **'Incoming call'**
  String get callTypeIncoming;

  /// Text that shows when the call is missed
  ///
  /// In en, this message translates to:
  /// **'Missed call'**
  String get callTypeMissed;

  /// Text that shows when the call is outgoing
  ///
  /// In en, this message translates to:
  /// **'Outgoing call'**
  String get callTypeOutgoing;

  /// Text that shows when the user is unavailable
  ///
  /// In en, this message translates to:
  /// **'User Unavailable'**
  String get callTypeUnavailable;

  /// No description provided for @cancelButtonText.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancelButtonText;

  /// Text shown on button to change radio channel
  ///
  /// In en, this message translates to:
  /// **'Change Channel'**
  String get changeChannelButtonText;

  /// No description provided for @chatWithLiveAgentAppbarTitle.
  ///
  /// In en, this message translates to:
  /// **'Live agent'**
  String get chatWithLiveAgentAppbarTitle;

  /// Text shown on buttons that close views or dialogs
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get closeButtonText;

  /// Title of the confirmation page
  ///
  /// In en, this message translates to:
  /// **'Confirmation'**
  String get confirmationAppBarTitle;

  /// Text for the try again button on the confirmation page
  ///
  /// In en, this message translates to:
  /// **'Try Again'**
  String get confirmationFailedButtonText;

  /// Description that shows when the payment fails
  ///
  /// In en, this message translates to:
  /// **'If you have any questions or need help, please contact our support team'**
  String get confirmationFailedContactSupportDescription;

  /// Text for the contact support button on the confirmation page
  ///
  /// In en, this message translates to:
  /// **'Contact Support'**
  String get confirmationFailedContactSupportText;

  /// Description that shows when the payment fails
  ///
  /// In en, this message translates to:
  /// **'Your payment was NOT successful. Please try again'**
  String get confirmationFailedDescription;

  /// Title that shows when the payment fails
  ///
  /// In en, this message translates to:
  /// **'Payment Failed'**
  String get confirmationFailedTitle;

  /// Text for the success button on the confirmation page
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get confirmationSuccessButtonText;

  /// Description that shows when the payment is successful
  ///
  /// In en, this message translates to:
  /// **'{amount} has been successfully added to your balance'**
  String confirmationSuccessDescription(String amount);

  /// Title that shows when the payment is successful
  ///
  /// In en, this message translates to:
  /// **'Payment Successful'**
  String get confirmationSuccessTitle;

  /// Text that shows above the contacts list on the home page
  ///
  /// In en, this message translates to:
  /// **'Contacts'**
  String get contactsAllContactsTabText;

  /// Text that shows the duration of a call
  ///
  /// In en, this message translates to:
  /// **'{min} mins {secs} secs'**
  String contactsCallRateText(String min, String secs);

  /// Text that shows above the Favourite calls list on the home page
  ///
  /// In en, this message translates to:
  /// **'Favourite'**
  String get contactsFavouriteCallsTabText;

  /// Message that shows when the user has no recent calls
  ///
  /// In en, this message translates to:
  /// **'You do not have any recent calls'**
  String get contactsNoCallsEmptyMessage;

  /// Text for the view contacts button on the home page
  ///
  /// In en, this message translates to:
  /// **'View Contacts'**
  String get contactsNoContactsButtonText;

  /// Placeholder text for the search contact name
  ///
  /// In en, this message translates to:
  /// **'Search  contact name'**
  String get contactsSearchContactsPlaceholder;

  /// Placeholder text for the search input field on the contacts page
  ///
  /// In en, this message translates to:
  /// **'Search for contact name'**
  String get contactsSearchForContactsPlaceholder;

  /// Text shown when the coupon is successfully applied
  ///
  /// In en, this message translates to:
  /// **'Applied'**
  String get couponAppliedButtonText;

  /// Text for the apply coupon button
  ///
  /// In en, this message translates to:
  /// **'Apply'**
  String get couponApplyButtonText;

  /// Text shown for credit
  ///
  /// In en, this message translates to:
  /// **'Credit'**
  String get credit;

  /// Warning message shown when user attempts to delete their account
  ///
  /// In en, this message translates to:
  /// **'The action you are about to take cannot be reversed. Please confirm you are deleting your account.'**
  String get deleteAccountWarning;

  /// Title of the dialer page
  ///
  /// In en, this message translates to:
  /// **'Dialer'**
  String get dialerAppBarTitle;

  /// Text for the copy action on the dialer page
  ///
  /// In en, this message translates to:
  /// **'Copy'**
  String get dialerCopyActionText;

  /// Error message that shows when the user enters an invalid number
  ///
  /// In en, this message translates to:
  /// **'Invalid number'**
  String get dialerErrorInvalidNumber;

  /// Error message that shows when the user does not add a country code
  ///
  /// In en, this message translates to:
  /// **'Add country code (e.g +1)'**
  String get dialerErrorWithoutCountryCode;

  /// Text that shows on the ABC button on the dialer page
  ///
  /// In en, this message translates to:
  /// **'ABC'**
  String get dialerKeypadABC;

  /// Text that shows on the DEF button on the dialer page
  ///
  /// In en, this message translates to:
  /// **'DEF'**
  String get dialerKeypadDEF;

  /// Text that shows on the GHI button on the dialer page
  ///
  /// In en, this message translates to:
  /// **'GHI'**
  String get dialerKeypadGHI;

  /// Text that shows on the JKL button on the dialer page
  ///
  /// In en, this message translates to:
  /// **'JKL'**
  String get dialerKeypadJKL;

  /// Text that shows on the MNO button on the dialer page
  ///
  /// In en, this message translates to:
  /// **'MNO'**
  String get dialerKeypadMNO;

  /// Text that shows on the PQRS button on the dialer page
  ///
  /// In en, this message translates to:
  /// **'PQRS'**
  String get dialerKeypadPQRS;

  /// Text that shows on the TUV button on the dialer page
  ///
  /// In en, this message translates to:
  /// **'TUV'**
  String get dialerKeypadTUV;

  /// Text that shows on the WXYZ button on the dialer page
  ///
  /// In en, this message translates to:
  /// **'WXYZ'**
  String get dialerKeypadWXYZ;

  /// Text that shows when the dialer is muted
  ///
  /// In en, this message translates to:
  /// **'Muted'**
  String get dialerMuteStatusMuted;

  /// Text that shows when the dialer is unmuted
  ///
  /// In en, this message translates to:
  /// **'Un-Muted'**
  String get dialerMuteStatusUnmuted;

  /// Text for the paste action on the dialer page
  ///
  /// In en, this message translates to:
  /// **'Paste'**
  String get dialerPasteActionText;

  /// Header text for the search results on the dialer page
  ///
  /// In en, this message translates to:
  /// **'Results'**
  String get dialerSearchResultsHeader;

  /// Text that shows when no results are found for the search input field on the dialer page
  ///
  /// In en, this message translates to:
  /// **'No results found'**
  String get dialerSearchResultsNoResults;

  /// Text that shows when the dialer status is connected
  ///
  /// In en, this message translates to:
  /// **'Connected'**
  String get dialerStatusConnected;

  /// Text that shows when the dialer status is connecting
  ///
  /// In en, this message translates to:
  /// **'Calling'**
  String get dialerStatusConnecting;

  /// Text that shows when the dialer status is ended
  ///
  /// In en, this message translates to:
  /// **'Ended'**
  String get dialerStatusEnded;

  /// Text that shows when an error occurs in the dialer
  ///
  /// In en, this message translates to:
  /// **'Error Occurred'**
  String get dialerStatusError;

  /// Text that shows when the dialer fails to connect
  ///
  /// In en, this message translates to:
  /// **'Connect Failed'**
  String get dialerStatusFailedToConnect;

  /// Text that shows when the dialer status is on hold
  ///
  /// In en, this message translates to:
  /// **'On Hold'**
  String get dialerStatusHold;

  /// Text that shows when the dialer status is initial
  ///
  /// In en, this message translates to:
  /// **'Calling'**
  String get dialerStatusInitial;

  /// Text that shows when the dialer status is initiating
  ///
  /// In en, this message translates to:
  /// **'Calling'**
  String get dialerStatusInitiating;

  /// Text that shows when the dialer status is ringing
  ///
  /// In en, this message translates to:
  /// **'Ringing'**
  String get dialerStatusRinging;

  /// Text that shows when the dialer status is unknown
  ///
  /// In en, this message translates to:
  /// **'unknown'**
  String get dialerStatusUnknown;

  /// Message that shows when the user has no contact list
  ///
  /// In en, this message translates to:
  /// **'You have no contact list.'**
  String get emptyContactList;

  /// Message that shows when the user has no favourite contact list
  ///
  /// In en, this message translates to:
  /// **'You have no favourite contact list.'**
  String get emptyFavouriteContactList;

  /// Placeholder text for entering an optional coupon code
  ///
  /// In en, this message translates to:
  /// **'Enter coupon (Optional)'**
  String get enterCouponOptionalPlaceholder;

  /// No description provided for @enterOtpPageAppBarSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Step 2 of 3'**
  String get enterOtpPageAppBarSubtitle;

  /// No description provided for @enterOtpPageAppBarTitle.
  ///
  /// In en, this message translates to:
  /// **'Getting Started'**
  String get enterOtpPageAppBarTitle;

  /// Text that shows on the edit phone number button
  ///
  /// In en, this message translates to:
  /// **'Wrong number? Please Edit'**
  String get enterOtpPageEditButton;

  /// Error message that shows when the user enters an incorrect OTP
  ///
  /// In en, this message translates to:
  /// **'Incorrect code'**
  String get enterOtpPageErrorMessage;

  /// No description provided for @enterOtpPagePhoneLabel.
  ///
  /// In en, this message translates to:
  /// **'Enter your verification code'**
  String get enterOtpPagePhoneLabel;

  /// Description text for the OTP input field on the OTP entry page
  ///
  /// In en, this message translates to:
  /// **'A 4 digit code has been sent to your number {phoneNumber}'**
  String enterOtpPagePhoneLabelDescription(String phoneNumber);

  /// Text that shows on the confirm verificatio button
  ///
  /// In en, this message translates to:
  /// **'Resend Code'**
  String get enterOtpPageResendOtpButton;

  /// Message showing the number of retries a user has left
  ///
  /// In en, this message translates to:
  /// **'{count, plural, =0{No tries left} =1{1 final try left} other{{count} more tries}}'**
  String enterOtpPageResendOtpRetries(int count);

  /// Error message that shows when the user has exhausted the number of retries
  ///
  /// In en, this message translates to:
  /// **'You cannot receive verification codes on this number for the next 24 hours'**
  String get enterOtpPageResendOtpRetriesError;

  /// Text that shows on the confirm verificatio button
  ///
  /// In en, this message translates to:
  /// **'Confirm OTP'**
  String get enterOtpPageSubmitButton;

  /// Label for voucher code input field
  ///
  /// In en, this message translates to:
  /// **'Enter voucher code'**
  String get enterVoucherCodeLabel;

  /// Text shown for free promotional content
  ///
  /// In en, this message translates to:
  /// **'FREE'**
  String get free;

  /// No description provided for @freeCreditAppbarTitle.
  ///
  /// In en, this message translates to:
  /// **'Free Credit'**
  String get freeCreditAppbarTitle;

  /// No description provided for @freeCreditPageContent_1.
  ///
  /// In en, this message translates to:
  /// **'Share your referral code with your friends & family.'**
  String get freeCreditPageContent_1;

  /// No description provided for @freeCreditPageContent_2.
  ///
  /// In en, this message translates to:
  /// **'They download FroggyTalk and register with your referral code.'**
  String get freeCreditPageContent_2;

  /// Text that shows the amount the user gets when the person they referred buys credit for the first time
  ///
  /// In en, this message translates to:
  /// **'You earn {amount} when the person buys credit the first time.'**
  String freeCreditPageContent_3(String amount);

  /// Text that shows the amount the user gets when they refer a friend
  ///
  /// In en, this message translates to:
  /// **'Refer a friend & earn {amount}'**
  String freeCreditPageHeadingText(String amount);

  /// No description provided for @freeCreditPageShareReferralLinkButtonText.
  ///
  /// In en, this message translates to:
  /// **'Share referral link'**
  String get freeCreditPageShareReferralLinkButtonText;

  /// Text that shows the amount the user gets when they refer a friend
  ///
  /// In en, this message translates to:
  /// **'My referrals ({amount})'**
  String freeCreditPageSubHeadingText(String amount);

  /// Default label shown when user name is not available
  ///
  /// In en, this message translates to:
  /// **'Froggytalk Customer'**
  String get froggytalkCustomerLabel;

  /// Title of the help center page
  ///
  /// In en, this message translates to:
  /// **'Help Center'**
  String get helpCenterAppbarTitle;

  /// Text that shows on the make a complaint or suggestion button on the help center page
  ///
  /// In en, this message translates to:
  /// **'Make a Complaint or Suggestion'**
  String get helpCenterPageMenu_1;

  /// Text that shows on the chat with live agent button on the help center page
  ///
  /// In en, this message translates to:
  /// **'Chat with Live agent'**
  String get helpCenterPageMenu_2;

  /// Text that shows on the FAQ button on the help center page
  ///
  /// In en, this message translates to:
  /// **'FAQ'**
  String get helpCenterPageMenu_3;

  /// navbar text for home page
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get homeNavigationBarText;

  /// No description provided for @internetConnectionAlertTextError.
  ///
  /// In en, this message translates to:
  /// **'Oops! It looks like you\'\'re offline'**
  String get internetConnectionAlertTextError;

  /// No description provided for @internetConnectionAlertTextSuccess.
  ///
  /// In en, this message translates to:
  /// **'All set! You\'\'re connected again.'**
  String get internetConnectionAlertTextSuccess;

  /// Error message shown when phone number format is invalid
  ///
  /// In en, this message translates to:
  /// **'Invalid International Phone Number Format'**
  String get invalidInternationalPhoneFormat;

  /// Error message shown when phone number is invalid
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid Phone number'**
  String get invalidPhoneNumber;

  /// navbar text for keypad page
  ///
  /// In en, this message translates to:
  /// **'Keypad'**
  String get keypadNavigationBarText;

  /// Text that shows when the number is a landline number
  ///
  /// In en, this message translates to:
  /// **'Landline'**
  String get landlineText;

  /// No description provided for @loadVoucherCardButtonText.
  ///
  /// In en, this message translates to:
  /// **'Recharge'**
  String get loadVoucherCardButtonText;

  /// No description provided for @loadVoucherCardErrorText.
  ///
  /// In en, this message translates to:
  /// **'Wrong code, Try again'**
  String get loadVoucherCardErrorText;

  /// No description provided for @loadVoucherCardLabelText.
  ///
  /// In en, this message translates to:
  /// **'Enter 10 digit voucher code'**
  String get loadVoucherCardLabelText;

  /// No description provided for @loadVoucherCardTitle.
  ///
  /// In en, this message translates to:
  /// **'Load Voucher Code'**
  String get loadVoucherCardTitle;

  /// Title shown on the load voucher code dialog
  ///
  /// In en, this message translates to:
  /// **'Load Voucher Code'**
  String get loadVoucherCodeTitle;

  /// Subtitle of the login page
  ///
  /// In en, this message translates to:
  /// **'Step 1 of 3'**
  String get loginPageAppBarSubtitle;

  /// Title of the login page
  ///
  /// In en, this message translates to:
  /// **'Getting Started'**
  String get loginPageAppBarTitle;

  /// No description provided for @loginPageCheckboxLabel.
  ///
  /// In en, this message translates to:
  /// **'I have a referral code'**
  String get loginPageCheckboxLabel;

  /// No description provided for @loginPageErrorMessage.
  ///
  /// In en, this message translates to:
  /// **'The number is not linked to a account'**
  String get loginPageErrorMessage;

  /// No description provided for @loginPagePhoneLabel.
  ///
  /// In en, this message translates to:
  /// **'Enter your number.'**
  String get loginPagePhoneLabel;

  /// No description provided for @loginPagePhoneLabelDescription.
  ///
  /// In en, this message translates to:
  /// **'A One-Time verification code will be sent to you via WhatsApp or SMS.'**
  String get loginPagePhoneLabelDescription;

  /// No description provided for @loginPagePhoneNumberError.
  ///
  /// In en, this message translates to:
  /// **'Your phone number is incomplete, please complete it'**
  String get loginPagePhoneNumberError;

  /// No description provided for @loginPageReferralLabel.
  ///
  /// In en, this message translates to:
  /// **'Enter referral code'**
  String get loginPageReferralLabel;

  /// No description provided for @loginPageSubmitButton.
  ///
  /// In en, this message translates to:
  /// **'Request Verification'**
  String get loginPageSubmitButton;

  /// Text that shows either '1 min' or '{count} mins' based on the count
  ///
  /// In en, this message translates to:
  /// **'{count, plural, zero{} one{1 min} other{{count} mins}}'**
  String minsOrMin(int count);

  /// Text that shows when the number is a mobile number
  ///
  /// In en, this message translates to:
  /// **'Mobile'**
  String get mobileText;

  /// No description provided for @moreAppbarTitle.
  ///
  /// In en, this message translates to:
  /// **'Account'**
  String get moreAppbarTitle;

  /// navbar text for more page
  ///
  /// In en, this message translates to:
  /// **'More'**
  String get moreNavigationBarText;

  /// No description provided for @morePageAccountBalanceCardTitle.
  ///
  /// In en, this message translates to:
  /// **'Balance'**
  String get morePageAccountBalanceCardTitle;

  /// No description provided for @morePageAppVersionMenuText.
  ///
  /// In en, this message translates to:
  /// **'App version'**
  String get morePageAppVersionMenuText;

  /// No description provided for @morePageCallRatesMenuText.
  ///
  /// In en, this message translates to:
  /// **'Call rates'**
  String get morePageCallRatesMenuText;

  /// No description provided for @morePageHelpCenterMenuText.
  ///
  /// In en, this message translates to:
  /// **'Help Center'**
  String get morePageHelpCenterMenuText;

  /// No description provided for @morePageLanguageMenuText.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get morePageLanguageMenuText;

  /// No description provided for @morePageLoadVoucherMenuText.
  ///
  /// In en, this message translates to:
  /// **'Load Voucher'**
  String get morePageLoadVoucherMenuText;

  /// No description provided for @morePageLogoutMenuText.
  ///
  /// In en, this message translates to:
  /// **'Log out'**
  String get morePageLogoutMenuText;

  /// No description provided for @morePageProfileMenuText.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get morePageProfileMenuText;

  /// No description provided for @morePageRadioMenuText.
  ///
  /// In en, this message translates to:
  /// **'Radio'**
  String get morePageRadioMenuText;

  /// No description provided for @morePageReferralCodeCardButtonText.
  ///
  /// In en, this message translates to:
  /// **'More info'**
  String get morePageReferralCodeCardButtonText;

  /// Text that shows the number of people that have used the referral code
  ///
  /// In en, this message translates to:
  /// **'{numberOfPeople} people have used your referral code'**
  String morePageReferralCodeCardContent(int numberOfPeople);

  /// Text that shows the amount the user gets when they refer their friends and family
  ///
  /// In en, this message translates to:
  /// **'Get {amount} when you refer your friends & family'**
  String morePageReferralCodeCardDescription(String amount);

  /// No description provided for @morePageReferralCodeCardTitle.
  ///
  /// In en, this message translates to:
  /// **'Referral code'**
  String get morePageReferralCodeCardTitle;

  /// Message that shows when there are no buy credit options available
  ///
  /// In en, this message translates to:
  /// **'No Buy Credit Options Available'**
  String get noBuyCreditOptionsAvailable;

  /// Message shown when there are no favorite contacts to display
  ///
  /// In en, this message translates to:
  /// **'No Favourite Contacts'**
  String get noFavoriteContactsMessage;

  /// Text shown when there are no notifications
  ///
  /// In en, this message translates to:
  /// **'No notifications yet'**
  String get noNotificationsYet;

  /// Message shown when user has insufficient credit for a call
  ///
  /// In en, this message translates to:
  /// **'You don\'\'t have enough credit'**
  String get notEnoughCreditMessage;

  /// Text shown for a single notification
  ///
  /// In en, this message translates to:
  /// **'Notification'**
  String get notification;

  /// Title of the notifications page
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get notificationsAppBarTitle;

  /// Message that shows when the user has no notifications
  ///
  /// In en, this message translates to:
  /// **'You do not have any notifications'**
  String get notificationsEmptyMessage;

  /// Title of the notification settings page
  ///
  /// In en, this message translates to:
  /// **'Notification Settings'**
  String get notificationSettingsAppBarTitle;

  /// Text that shows to toggle the auto delete notification on the notification settings page
  ///
  /// In en, this message translates to:
  /// **'Auto delete notification'**
  String get notificationSettingsAutoDeleteNotificationText;

  /// Text that shows to toggle the notification sound on the notification settings page
  ///
  /// In en, this message translates to:
  /// **'Notification sound'**
  String get notificationSettingsNotificationSoundText;

  /// Text that shows on the all tab on the notifications page
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get notificationsTabAllText;

  /// Text that shows on the unread tab on the notifications page
  ///
  /// In en, this message translates to:
  /// **'Unread'**
  String get notificationsTabUnreadText;

  /// Text that shows the time since the notification was received
  ///
  /// In en, this message translates to:
  /// **'{time} {time, plural, one{{duration}} other{{duration}s}} ago'**
  String notificationsTimeAgo(int time, String duration);

  /// Text shown on OK confirmation button
  ///
  /// In en, this message translates to:
  /// **'Ok'**
  String get okButtonText;

  /// Text that shows between the terms and conditions link and the privacy policy link on the onboarding page
  ///
  /// In en, this message translates to:
  /// **'and'**
  String get onboardingPageFooterAndText;

  /// Text that shows below the submit button on the onboarding page
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get onboardingPageFooterPrivacy;

  /// Text that shows below the submit button on the onboarding page
  ///
  /// In en, this message translates to:
  /// **'Terms and Conditions'**
  String get onboardingPageFooterTermsConditionsText;

  /// Text that shows below the submit button on the onboarding page
  ///
  /// In en, this message translates to:
  /// **'By Clicking Get Started, you are accepting the'**
  String get onboardingPageFooterText;

  /// Header text for the first slide on the onboarding page
  ///
  /// In en, this message translates to:
  /// **'Multi-Language'**
  String get onboardingPageSliderHeader1;

  /// Header text for the second slide on the onboarding page
  ///
  /// In en, this message translates to:
  /// **'Call mobile or landline numbers worldwide'**
  String get onboardingPageSliderHeader2;

  /// Header text for the third slide on the onboarding page
  ///
  /// In en, this message translates to:
  /// **'Easy payment'**
  String get onboardingPageSliderHeader3;

  /// Text for the first slide on the onboarding page
  ///
  /// In en, this message translates to:
  /// **'FroggyTalk allows you select your preferred language e.g Tignrigna, Amharic, Hausa etc.'**
  String get onboardingPageSliderText1;

  /// Text for the second slide on the onboarding page
  ///
  /// In en, this message translates to:
  /// **'Make calls to any mobile or landline anywhere in the world. The receiver does not need a smartphone or internet connection.'**
  String get onboardingPageSliderText2;

  /// Text for the third slide on the onboarding page
  ///
  /// In en, this message translates to:
  /// **'Buy call credit in your local currency with your preferred payment method.'**
  String get onboardingPageSliderText3;

  /// Text for the submit button on the onboarding page
  ///
  /// In en, this message translates to:
  /// **'Get Started'**
  String get onboardingPageSubmitButtonText;

  /// Label shown when user has no remaining credit
  ///
  /// In en, this message translates to:
  /// **'Out of credit'**
  String get outOfCreditLabel;

  /// Title shown in app bar of payment failure page
  ///
  /// In en, this message translates to:
  /// **'Failed'**
  String get paymentFailureAppBarTitle;

  /// Text for button to return to home page
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get paymentFailureHomeButton;

  /// Message shown when payment fails, includes amount
  ///
  /// In en, this message translates to:
  /// **'Your payment of {amount} was unsuccessful'**
  String paymentFailureMessage(String amount);

  /// Main title shown when payment fails
  ///
  /// In en, this message translates to:
  /// **'Payment Unsuccessful'**
  String get paymentFailureTitle;

  /// Text for button to retry payment
  ///
  /// In en, this message translates to:
  /// **'Try Again'**
  String get paymentFailureTryAgainButton;

  /// Title of the payment options page
  ///
  /// In en, this message translates to:
  /// **'Payment Options'**
  String get paymentOptionsAppBarTitle;

  /// Description of the auto credit payment method
  ///
  /// In en, this message translates to:
  /// **'This will automatically credit your account with {amount} when your balance is below  {minAmount}.'**
  String paymentOptionsAutoCreditDescription(String amount, String minAmount);

  /// Title of the auto credit payment method
  ///
  /// In en, this message translates to:
  /// **'Auto Credit'**
  String get paymentOptionsAutoCreditTitle;

  /// Text that shows above the payment method selection dropdown
  ///
  /// In en, this message translates to:
  /// **'Choose preferred method of payment'**
  String get paymentOptionsSelectPaymentMethod;

  /// Text that shows the amount to be credited on the payment options page
  ///
  /// In en, this message translates to:
  /// **'Amount to be credited'**
  String get paymentOptionsSummaryAmountToCreditText;

  /// Text for the apply discount button on the payment options page
  ///
  /// In en, this message translates to:
  /// **'Apply'**
  String get paymentOptionsSummaryDiscountButtonText;

  /// Placeholder text for the discount code input field on the payment options page
  ///
  /// In en, this message translates to:
  /// **'Enter discount code(optional)'**
  String get paymentOptionsSummaryDiscountPlaceholder;

  /// Text that shows the discount on the payment options page
  ///
  /// In en, this message translates to:
  /// **'Discount'**
  String get paymentOptionsSummaryDiscountText;

  /// Text that shows the total payment on the payment options page
  ///
  /// In en, this message translates to:
  /// **'Total payment'**
  String get paymentOptionsSummaryTotalPaymentText;

  /// Text that shows the VAT and fees on the payment options page
  ///
  /// In en, this message translates to:
  /// **'VAT + fees'**
  String get paymentOptionsSummaryVatFeesText;

  /// Label for the amount to be credited in the payment summary
  ///
  /// In en, this message translates to:
  /// **'Amount to be Credited'**
  String get paymentSummaryAmountToCreditLabel;

  /// Label for the discount in the payment summary
  ///
  /// In en, this message translates to:
  /// **'Discount'**
  String get paymentSummaryDiscountLabel;

  /// Label for the total payment in the payment summary
  ///
  /// In en, this message translates to:
  /// **'Total payment'**
  String get paymentSummaryTotalLabel;

  /// Label for the VAT and additional fees in the payment summary
  ///
  /// In en, this message translates to:
  /// **'VAT + Fees'**
  String get paymentSummaryVatFeesLabel;

  /// Rate per minute for a call
  ///
  /// In en, this message translates to:
  /// **'{rate}/min'**
  String perMinRate(String rate);

  /// Rate per minute for a call in short form
  ///
  /// In en, this message translates to:
  /// **'/min'**
  String get perMinRateSingle;

  /// Rate per minute for a call
  ///
  /// In en, this message translates to:
  /// **'{rate}/minute'**
  String perMinuteRate(String rate);

  /// Label showing '/minutes' for call rate displays
  ///
  /// In en, this message translates to:
  /// **'{min}/minutes'**
  String perMinuteSlashLabel(String min);

  /// Text for the accept button on the permission request dialog
  ///
  /// In en, this message translates to:
  /// **'Allow Access'**
  String get permissionButtonAccept;

  /// Text for the skip button on the permission request dialog
  ///
  /// In en, this message translates to:
  /// **'Skip'**
  String get permissionButtonSkip;

  /// Title for the camera permission request dialog
  ///
  /// In en, this message translates to:
  /// **'We need permission to capture photos and videos'**
  String get permissionForCameraTitle;

  /// Title for the contact list permission request dialog
  ///
  /// In en, this message translates to:
  /// **'Give FroggyTalk permission to access your Contact list / Phonebook'**
  String get permissionForContactListTitle;

  /// Title for the microphone permission request dialog
  ///
  /// In en, this message translates to:
  /// **'Give FroggyTalk access to your Microphone to process calls'**
  String get permissionForMicrophoneTitle;

  /// Title for the notification permission request dialog
  ///
  /// In en, this message translates to:
  /// **'Give FroggyTalk permission to send you Updates'**
  String get permissionForNotificationTitle;

  /// Title for the storage permission request dialog
  ///
  /// In en, this message translates to:
  /// **'We need permission to store temporary data on your device'**
  String get permissionForStorageTitle;

  /// Message shown when a permission is permanently denied by the user
  ///
  /// In en, this message translates to:
  /// **'Permission is permanently denied, Please enable it in the app settings'**
  String get permissionPermanentlyDeniedMessage;

  /// Message shown when a permission is denied by the user
  ///
  /// In en, this message translates to:
  /// **'Permission is denied, Please enable it in the app settings'**
  String get permissionDeniedMessage;

  /// Rate per minute for a call
  ///
  /// In en, this message translates to:
  /// **'{rate}/m'**
  String perM_Rate(String rate);

  /// Error message shown when phone number doesn't start with + or 00
  ///
  /// In en, this message translates to:
  /// **'Please start your entry with either + or 00'**
  String get phoneNumberStartRule;

  /// Text for the proceed to payment button on the buy credit page
  ///
  /// In en, this message translates to:
  /// **'Proceed'**
  String get proceedToPaymentButtonText;

  /// No description provided for @profileAppbarTitle.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profileAppbarTitle;

  /// No description provided for @profileDeleteAccountButtonText.
  ///
  /// In en, this message translates to:
  /// **'Delete account'**
  String get profileDeleteAccountButtonText;

  /// No description provided for @profileDeleteAccountCardContent.
  ///
  /// In en, this message translates to:
  /// **'Please confirm you want your account deleted. You will lose any balance on your account.'**
  String get profileDeleteAccountCardContent;

  /// No description provided for @profileDeleteAccountCardTitle.
  ///
  /// In en, this message translates to:
  /// **'Delete account'**
  String get profileDeleteAccountCardTitle;

  /// Label used for profile sections
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profileLabel;

  /// No description provided for @profileLabelChangeLocation.
  ///
  /// In en, this message translates to:
  /// **'Change location'**
  String get profileLabelChangeLocation;

  /// No description provided for @profileLabelEmailAddress.
  ///
  /// In en, this message translates to:
  /// **'Enter email address'**
  String get profileLabelEmailAddress;

  /// profileLabelFullName
  ///
  /// In en, this message translates to:
  /// **'Enter full name'**
  String get profileLabelFullName;

  /// Message shown when profile is updated successfully
  ///
  /// In en, this message translates to:
  /// **'Profile updated successfully'**
  String get profileUpdatedSuccessfullyMessage;

  /// Message shown when profile update fails
  ///
  /// In en, this message translates to:
  /// **'Profile update failed'**
  String get profileUpdateFailedMessage;

  /// Subtitle for the quick advert on the home page
  ///
  /// In en, this message translates to:
  /// **'Buy airtime for your family worldwide'**
  String get quickAdvertSubtitle;

  /// Title for the quick advert on the home page
  ///
  /// In en, this message translates to:
  /// **'Send credit to 140+ countries'**
  String get quickAdvertTitle;

  /// No description provided for @radioAppBarTitle.
  ///
  /// In en, this message translates to:
  /// **'Radio'**
  String get radioAppBarTitle;

  /// Text shown when radio feature is not yet available
  ///
  /// In en, this message translates to:
  /// **'Radio Coming soon !!!'**
  String get radioComingSoonText;

  /// No description provided for @radioPageChooseChannelButtonText.
  ///
  /// In en, this message translates to:
  /// **'Choose Channel'**
  String get radioPageChooseChannelButtonText;

  /// No description provided for @radioPageNoChannelsText.
  ///
  /// In en, this message translates to:
  /// **'No channels found'**
  String get radioPageNoChannelsText;

  /// No description provided for @radioPageSearchPlaceholder.
  ///
  /// In en, this message translates to:
  /// **'Choose Channel'**
  String get radioPageSearchPlaceholder;

  /// navbar text for rates page
  ///
  /// In en, this message translates to:
  /// **'Rates'**
  String get ratesNavigationBarText;

  /// Text that shows above the recent calls list on the home page
  ///
  /// In en, this message translates to:
  /// **'Recent Calls'**
  String get recentCallsText;

  /// Text shown on recharge button
  ///
  /// In en, this message translates to:
  /// **'Recharge'**
  String get rechargeButtonText;

  /// Text shown for refer and earn promotional text
  ///
  /// In en, this message translates to:
  /// **'Refer & Earn'**
  String get referAndEarn;

  /// Text for the referral card button
  ///
  /// In en, this message translates to:
  /// **'Free Credit'**
  String get referralCardButtonText;

  /// Message showing the referral benefit
  ///
  /// In en, this message translates to:
  /// **'Refer someone and get {percentageAmount} free credit.'**
  String referralCardDescription1(String percentageAmount);

  /// Message showing how many people have used the referral code, with pluralization.
  ///
  /// In en, this message translates to:
  /// **'{numberOfPeople, plural, zero{No one has used your referral code.} one{One person has used your referral code.} other{{numberOfPeople} people have used your referral code.}}'**
  String referralCardDescription2(int numberOfPeople, num count);

  /// Title for the referral card
  ///
  /// In en, this message translates to:
  /// **'Referral Code'**
  String get referralCardTitle;

  /// No description provided for @referralCodeOnCopyActionResponse.
  ///
  /// In en, this message translates to:
  /// **'FroggyTalk pasted to clipboard'**
  String get referralCodeOnCopyActionResponse;

  /// Text shown on button to save profile changes and proceed to payment
  ///
  /// In en, this message translates to:
  /// **'Save & Proceed to Payment'**
  String get saveAndProceedToPaymentButtonText;

  /// No description provided for @saveChangesButtonText.
  ///
  /// In en, this message translates to:
  /// **'Save changes'**
  String get saveChangesButtonText;

  /// Message shown when changes are saved successfully
  ///
  /// In en, this message translates to:
  /// **'Saved successfully'**
  String get savedSuccessfullyMessage;

  /// Placeholder text for the search input field on the country selection page
  ///
  /// In en, this message translates to:
  /// **'Search country'**
  String get searchCountryPlaceholder;

  /// Placeholder text for searching favorite contacts
  ///
  /// In en, this message translates to:
  /// **'Search For Favourite Contact'**
  String get searchFavouriteContactMessage;

  /// Text that shows when no results are found for the search input field on the country selection page
  ///
  /// In en, this message translates to:
  /// **'No results found'**
  String get searchForCountryNoResults;

  /// Placeholder text for the search input field on the country selection page
  ///
  /// In en, this message translates to:
  /// **'Search for Country'**
  String get searchForCountryPlaceholder;

  /// Placeholder text for searching recent calls
  ///
  /// In en, this message translates to:
  /// **'Search Recent Calls'**
  String get searchRecentCallsMessage;

  /// Text that shows either '1 sec' or '{count} secs' based on the count
  ///
  /// In en, this message translates to:
  /// **'{count, plural, zero{} one{1 sec} other{{count} secs}}'**
  String secsOrSec(int count);

  /// Placeholder text for the country selection dropdown
  ///
  /// In en, this message translates to:
  /// **'Select country'**
  String get selectCountryPlaceholder;

  /// Text for the send credit button on the home page
  ///
  /// In en, this message translates to:
  /// **'Send Credit'**
  String get sendCreditButtonText;

  /// Text shown for settings
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// Text shown when sharing referral code
  ///
  /// In en, this message translates to:
  /// **'Download FroggyTalk App {appLink} and register with my referral code {referralCode}. It has the best rates and international calling experience.'**
  String shareReferralText(String appLink, String referralCode);

  /// Message that shows when an error occurs
  ///
  /// In en, this message translates to:
  /// **'Something went wrong. Please try again.'**
  String get somethingWentWrongMessage;

  /// Text shown for unread notifications
  ///
  /// In en, this message translates to:
  /// **'Unread'**
  String get unread;

  /// Text shown on button to update user profile
  ///
  /// In en, this message translates to:
  /// **'Update Profile'**
  String get updateProfileButtonText;

  /// Text for the button to dismiss the update dialog
  ///
  /// In en, this message translates to:
  /// **'Not Now'**
  String get upgradeDialogButtonNo;

  /// Text for the button to proceed with the update
  ///
  /// In en, this message translates to:
  /// **'Update'**
  String get upgradeDialogButtonYes;

  /// Message prompting the user to update to the latest version
  ///
  /// In en, this message translates to:
  /// **'A new version of FroggyTalk is available. Would you like to update now?'**
  String get upgradeDialogMessage;

  /// Title shown when a new update is available
  ///
  /// In en, this message translates to:
  /// **'Update Available'**
  String get upgradeDialogTitle;

  /// Error message shown when coupon code length or format is invalid
  ///
  /// In en, this message translates to:
  /// **'Discount code is incomplete or invalid'**
  String get validationCouponCodeIncomplete;

  /// Error message shown when coupon code is invalid
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid discount code'**
  String get validationCouponInvalid;

  /// Error message shown when email format is invalid
  ///
  /// In en, this message translates to:
  /// **'Invalid email address.'**
  String get validationEmailInvalid;

  /// Generic required field error message
  ///
  /// In en, this message translates to:
  /// **'{fieldName} is required'**
  String validationFieldIsRequired(String fieldName);

  /// Error message for fields that don't meet minimum length requirement
  ///
  /// In en, this message translates to:
  /// **'{fieldName} must be at least {minLength} characters long'**
  String validationMinLengthError(String fieldName, int minLength);

  /// Error message shown when phone number is incomplete
  ///
  /// In en, this message translates to:
  /// **'Incomplete phone number.'**
  String get validationPhoneNumberIncomplete;

  /// Text for the view action button on page
  ///
  /// In en, this message translates to:
  /// **'View'**
  String get viewButtonText;

  /// Title of the contact detail page
  ///
  /// In en, this message translates to:
  /// **'Contact Details'**
  String get viewContactDetailAppBarTitle;

  /// Message shown when voucher is successfully loaded
  ///
  /// In en, this message translates to:
  /// **'Voucher Loaded Successfully'**
  String get voucherLoadedSuccessMessage;

  /// Text showing that payment is secured by Stripe payment processor
  ///
  /// In en, this message translates to:
  /// **'Secured by Stripe'**
  String get securedByStripe;

  /// Text shown on button to play the next radio track or station
  ///
  /// In en, this message translates to:
  /// **'Next Track'**
  String get nextTrack;

  /// Text shown on button to stop the currently playing radio
  ///
  /// In en, this message translates to:
  /// **'Stop Radio'**
  String get stopRadio;

  /// Text shown on button to start playing the radio
  ///
  /// In en, this message translates to:
  /// **'Play Radio'**
  String get playRadio;

  /// Text shown on button to play the previous radio track or station
  ///
  /// In en, this message translates to:
  /// **'Previous Track'**
  String get previousTrack;

  /// Text shown to represent a radio channel or station
  ///
  /// In en, this message translates to:
  /// **'Channel'**
  String get channel;

  /// Text shown to represent all available radio channels or stations
  ///
  /// In en, this message translates to:
  /// **'All Channels'**
  String get allChannels;

  /// Text shown on buttons that accept or confirm an action
  ///
  /// In en, this message translates to:
  /// **'Accept'**
  String get acceptButtonText;

  /// Text shown on buttons that decline or reject an action
  ///
  /// In en, this message translates to:
  /// **'Decline'**
  String get declineButtonText;

  /// Text shown when requesting permission to share user information with a radio station
  ///
  /// In en, this message translates to:
  /// **'Allow us share your information with {radioName}'**
  String allowShareInfoWithRadio(String radioName);

  /// Text shown to indicate the currently playing radio track or station
  ///
  /// In en, this message translates to:
  /// **'Now Playing'**
  String get nowPlaying;

  /// Message shown when a feature or content is temporarily unavailable
  ///
  /// In en, this message translates to:
  /// **'Please check back later'**
  String get checkBackLaterText;

  /// Text shown on button to refresh or reload content
  ///
  /// In en, this message translates to:
  /// **'Refresh'**
  String get refreshText;

  /// Message shown when there are no radio stations to display
  ///
  /// In en, this message translates to:
  /// **'No radio stations available'**
  String get noRadioStationsAvailableText;

  /// Text shown on button to retry an action that failed
  ///
  /// In en, this message translates to:
  /// **'Try Again'**
  String get tryAgainText;

  /// Generic message shown when an unspecified error occurs
  ///
  /// In en, this message translates to:
  /// **'An unknown error occurred'**
  String get unknownErrorText;

  /// Message shown when radio stations fail to load
  ///
  /// In en, this message translates to:
  /// **'Error loading radio stations'**
  String get errorLoadingRadioStationsText;

  /// Message shown while radio stations are being loaded
  ///
  /// In en, this message translates to:
  /// **'Loading radio stations...'**
  String get loadingRadioStationsText;

  /// Text that shows on the WhatsApp button on the help center page
  ///
  /// In en, this message translates to:
  /// **'WhatsApp'**
  String get helpCenterPageMenu_4;

  /// Error message for fields that don't meet length requirements
  ///
  /// In en, this message translates to:
  /// **'{fieldName} must be between {minLength} and {maxLength} characters long'**
  String validationLengthError(String fieldName, int minLength, int maxLength);

  /// Title for the language selection modal
  ///
  /// In en, this message translates to:
  /// **'Choose Language'**
  String get chooseLanguage;

  /// Subtitle for the language selection modal
  ///
  /// In en, this message translates to:
  /// **'Select your preferred language'**
  String get selectPreferredLanguage;

  /// Text for cancel buttons
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// Text for apply/confirm buttons
  ///
  /// In en, this message translates to:
  /// **'Apply'**
  String get apply;

  /// Error message shown when language change fails
  ///
  /// In en, this message translates to:
  /// **'Failed to change language. Please try again.'**
  String get languageChangeError;

  /// Message shown when no languages are available to select
  ///
  /// In en, this message translates to:
  /// **'No languages available'**
  String get noLanguagesAvailable;

  /// Subtitle of the profile setup page showing progress
  ///
  /// In en, this message translates to:
  /// **'Step 3 of 3'**
  String get languageSetupPageAppBarSubtitle;

  /// Greeting message shown to returning users
  ///
  /// In en, this message translates to:
  /// **'Welcome Back, {firstName}'**
  String welcomeBackText(String firstName);

  /// Affirmative message showing app's appreciation for users
  ///
  /// In en, this message translates to:
  /// **'FroggyTalk Loves You.'**
  String get froggyTalkLovesYou;

  /// Instruction text prompting user to update their language setting
  ///
  /// In en, this message translates to:
  /// **'Please click the button below to update your language'**
  String get updateLanguagePrompt;

  /// Button or action text for language update functionality
  ///
  /// In en, this message translates to:
  /// **'Update Your Language.'**
  String get updateYourLanguage;

  /// Label indicating this is an in-app purchase from the app store
  ///
  /// In en, this message translates to:
  /// **'In-App Purchase'**
  String get inAppPurchaseLabel;

  /// Button text for restoring previous in-app purchases
  ///
  /// In en, this message translates to:
  /// **'Restore Purchases'**
  String get restorePurchases;

  /// Label for the price listed in the app store
  ///
  /// In en, this message translates to:
  /// **'Store Price'**
  String get storePrice;

  /// Title showing the number of days left in the modal
  ///
  /// In en, this message translates to:
  /// **'{days} days left'**
  String timeLeftModalTitle(String days);

  /// Message showing days left and number of referrals needed
  ///
  /// In en, this message translates to:
  /// **'{days} days left to be a winner of the Cash prize. Refer {count} people and be No 1'**
  String timeLeftModalMessage(String days, int count);

  /// Text for confirm/verification buttons
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get confirm;

  /// Message informing users where they can find language settings
  ///
  /// In en, this message translates to:
  /// **'You can change your language under'**
  String get languageSelectionDisclaimer;

  /// Text for button to enable automatic account crediting
  ///
  /// In en, this message translates to:
  /// **'Activate auto credit'**
  String get activateAutoCredit;

  /// Explanation text for the auto credit feature
  ///
  /// In en, this message translates to:
  /// **'will be charged to your card and added to your balance whenever it falls below'**
  String get autoAccountCreditHelp;

  /// Additional explanation for auto credit feature benefit
  ///
  /// In en, this message translates to:
  /// **'to avoid being cut off during a call'**
  String get avoidCallDisruption;

  /// Text showing the cash prize amount for promotions and referrals
  ///
  /// In en, this message translates to:
  /// **'{currencySymbol}{amount} FREE Cash'**
  String cashPrizeAmount(String currencySymbol, String amount);

  /// Title for the monthly promotion leaderboard showing top referrers
  ///
  /// In en, this message translates to:
  /// **'{month} Promo Leaders'**
  String monthlyReferralBoard(String month);

  /// Text indicating that terms and conditions apply to an offer or promotion
  ///
  /// In en, this message translates to:
  /// **'Terms and conditions apply'**
  String get termsAndConditionsApply;

  /// Text for a button or link to get an offer or promotion
  ///
  /// In en, this message translates to:
  /// **'Get Now'**
  String get getNow;

  /// Text shown on button to pay using Google Pay
  ///
  /// In en, this message translates to:
  /// **'Checkout with Google Pay'**
  String get payWithGooglePay;

  /// Text shown on button to pay using Apple Pay
  ///
  /// In en, this message translates to:
  /// **'Pay via AppStore'**
  String get payWithApplePay;

  /// Label showing the Play Store fee percentage and VAT in payment summary
  ///
  /// In en, this message translates to:
  /// **'+ {percentage} Commission & VAT'**
  String paymentSummaryPlayStoreFeeLabel(String percentage);

  /// Error message displayed when the referral leaderboard cannot be loaded
  ///
  /// In en, this message translates to:
  /// **'Failed to load leaderboard'**
  String get failedToLoadLeaderboard;

  /// Text for button to retry a failed operation
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// Text shown on button to pay using Google Pay
  ///
  /// In en, this message translates to:
  /// **'Google Pay'**
  String get googlePay;

  /// Label showing the cost charged by the App Store including fees and VAT
  ///
  /// In en, this message translates to:
  /// **'+ {value} App Store Cost'**
  String appStoreCost(String value);

  /// Title for call reminder notifications
  ///
  /// In en, this message translates to:
  /// **'Call Reminder'**
  String get notification_call_reminder_title;

  /// Body text for call reminder notifications
  ///
  /// In en, this message translates to:
  /// **'Don\'\'t forget to call {contact}'**
  String notification_call_reminder_body(String contact);

  /// Title for promotion notifications
  ///
  /// In en, this message translates to:
  /// **'Special Offer'**
  String get notification_promotion_title;

  /// Body text for promotion notifications
  ///
  /// In en, this message translates to:
  /// **'New offer: {offer} - {description}'**
  String notification_promotion_body(String offer, String description);

  /// Title for security alert notifications
  ///
  /// In en, this message translates to:
  /// **'Security Alert'**
  String get notification_security_alert_title;

  /// Body text for security alert notifications
  ///
  /// In en, this message translates to:
  /// **'Security alert: {alertType} detected on {device}'**
  String notification_security_alert_body(String alertType, String device);

  /// Title for daily reminder notifications
  ///
  /// In en, this message translates to:
  /// **'Daily Reminder'**
  String get notification_daily_reminder_title;

  /// Body text for daily reminder notifications
  ///
  /// In en, this message translates to:
  /// **'Don\'\'t forget: {reminderType}'**
  String notification_daily_reminder_body(String reminderType);
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['am', 'ar', 'din', 'en', 'fr', 'ha', 'ti'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'am': return AppLocalizationsAm();
    case 'ar': return AppLocalizationsAr();
    case 'din': return AppLocalizationsDin();
    case 'en': return AppLocalizationsEn();
    case 'fr': return AppLocalizationsFr();
    case 'ha': return AppLocalizationsHa();
    case 'ti': return AppLocalizationsTi();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
