// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get accountBalanceCardTitle => 'الرصيد';

  @override
  String get all => 'الكل';

  @override
  String get allowAccessButtonText => 'السماح بالوصول لإجراء هذا الإجراء المطلوب';

  @override
  String get appBootingTextBelow => 'مساعدة المجتمعات على البقاء متصلة';

  @override
  String get appLanguageAppbarTitle => 'لغة التطبيق';

  @override
  String get appTitle => 'FroggyTalk';

  @override
  String buyCreditAmountCustomPlaceholder(String min, String max) {
    return 'الحد الأدنى $min - الحد الأقصى $max\n';
  }

  @override
  String get buyCreditAmountRecommended => 'مُوصى به';

  @override
  String get buyCreditAppBarTitle => 'شحن الرصيد';

  @override
  String get buyCreditButtonText => 'اشترِ رصيدًا';

  @override
  String get buyCreditEnterCustomAmountLabel => 'أدخل مبلغًا مخصصًا';

  @override
  String get buyCreditPageTitle => 'المبلغ المفضل';

  @override
  String get callButtonText => 'اتصال';

  @override
  String get callingAppBarTitle => 'جارٍ الاتصال';

  @override
  String get callingPageBluetoothOptionsText => 'البلوتوث';

  @override
  String get callingPageCreditErrorCardTitle => 'رصيد المكالمات غير كافٍ';

  @override
  String get callingPagePhoneOptionsText => 'الهاتف';

  @override
  String get callingPageSpeakerOptionsText => 'مكبر الصوت';

  @override
  String callingPageTimeLeft(String time) {
    return '$time متبقي';
  }

  @override
  String get callLogTypeAnswered => 'تم الرد';

  @override
  String get callLogTypeBusy => 'مشغول';

  @override
  String get callLogTypeCancel => 'تم الإلغاء\n';

  @override
  String get callLogTypeIncoming => 'وارد';

  @override
  String get callLogTypeMissed => 'فائت';

  @override
  String get callLogTypeOutgoing => 'صادر';

  @override
  String get callLogTypeUnavailable => 'غير متوفر';

  @override
  String get callRatesAppBarTitle => 'أسعار المكالمات';

  @override
  String get callTypeAnswered => 'مكالمة صادرة';

  @override
  String get callTypeBusy => 'مشغول';

  @override
  String get callTypeCancel => 'مكالمة تم إلغائها';

  @override
  String get callTypeIncoming => 'مكالمة واردة';

  @override
  String get callTypeMissed => 'مكالمة فائتة';

  @override
  String get callTypeOutgoing => 'مكالمة صادرة';

  @override
  String get callTypeUnavailable => 'المستخدم غير متاح';

  @override
  String get cancelButtonText => 'إلغاء';

  @override
  String get changeChannelButtonText => 'تغيير القناة';

  @override
  String get chatWithLiveAgentAppbarTitle => 'مندوب خدمة العملاء متاح';

  @override
  String get closeButtonText => 'إغلاق';

  @override
  String get confirmationAppBarTitle => 'تأكيد';

  @override
  String get confirmationFailedButtonText => 'حاول مرة أخرى';

  @override
  String get confirmationFailedContactSupportDescription => 'إذا كان لديك أي أسئلة أو تحتاج إلى مساعدة، يرجى التواصل مع فريق الدعم الخاص بنا';

  @override
  String get confirmationFailedContactSupportText => 'تواصل مع الدعم';

  @override
  String get confirmationFailedDescription => 'لم تنجح عملية الدفع الخاصة بك. يرجى المحاولة مرة أخرى';

  @override
  String get confirmationFailedTitle => 'فشل الدفع';

  @override
  String get confirmationSuccessButtonText => 'تم';

  @override
  String confirmationSuccessDescription(String amount) {
    return 'تمت إضافة $amount بنجاح إلى رصيدك';
  }

  @override
  String get confirmationSuccessTitle => 'تم الدفع بنجاح';

  @override
  String get contactsAllContactsTabText => 'جهات الاتصال';

  @override
  String contactsCallRateText(String min, String secs) {
    return '$min دقيقة $secs ثانية';
  }

  @override
  String get contactsFavouriteCallsTabText => 'المفضلة';

  @override
  String get contactsNoCallsEmptyMessage => 'ليس لديك أي مكالمات حديثة';

  @override
  String get contactsNoContactsButtonText => 'عرض جهات الاتصال';

  @override
  String get contactsSearchContactsPlaceholder => 'ابحث عن اسم جهة الاتصال';

  @override
  String get contactsSearchForContactsPlaceholder => 'ابحث عن اسم جهة الاتصال';

  @override
  String get couponAppliedButtonText => 'تم التطبيق';

  @override
  String get couponApplyButtonText => 'تطبيق';

  @override
  String get credit => 'رصيد\n';

  @override
  String get deleteAccountWarning => 'الإجراء الذي على وشك اتخاذه لا يمكن التراجع عنه. يرجى تأكيد أنك تقوم بحذف حسابك.';

  @override
  String get dialerAppBarTitle => 'لوحة الاتصال';

  @override
  String get dialerCopyActionText => 'نسخ';

  @override
  String get dialerErrorInvalidNumber => 'رقم غير صالح';

  @override
  String get dialerErrorWithoutCountryCode => 'أضف رمز البلد (مثل +1)';

  @override
  String get dialerKeypadABC => 'ABC';

  @override
  String get dialerKeypadDEF => 'DEF';

  @override
  String get dialerKeypadGHI => 'GHI';

  @override
  String get dialerKeypadJKL => 'JKL';

  @override
  String get dialerKeypadMNO => 'MNO';

  @override
  String get dialerKeypadPQRS => 'PQRS';

  @override
  String get dialerKeypadTUV => 'TUV';

  @override
  String get dialerKeypadWXYZ => 'WXYZ';

  @override
  String get dialerMuteStatusMuted => 'صامت';

  @override
  String get dialerMuteStatusUnmuted => 'غير صامت';

  @override
  String get dialerPasteActionText => 'لصق';

  @override
  String get dialerSearchResultsHeader => 'النتائج';

  @override
  String get dialerSearchResultsNoResults => 'لا توجد نتائج';

  @override
  String get dialerStatusConnected => 'متصل';

  @override
  String get dialerStatusConnecting => 'جاري الاتصال';

  @override
  String get dialerStatusEnded => 'انتهى';

  @override
  String get dialerStatusError => 'حدث خطأ';

  @override
  String get dialerStatusFailedToConnect => 'فشل الاتصال';

  @override
  String get dialerStatusHold => 'قيد الانتظار';

  @override
  String get dialerStatusInitial => 'جاري الاتصال';

  @override
  String get dialerStatusInitiating => 'جاري الاتصال';

  @override
  String get dialerStatusRinging => 'رنين';

  @override
  String get dialerStatusUnknown => 'غير معروف';

  @override
  String get emptyContactList => 'ليس لديك قائمة جهات اتصال.';

  @override
  String get emptyFavouriteContactList => 'ليس لديك قائمة جهات اتصال مفضلة.';

  @override
  String get enterCouponOptionalPlaceholder => 'أدخل القسيمة (اختياري)';

  @override
  String get enterOtpPageAppBarSubtitle => 'الخطوة 2 من 3';

  @override
  String get enterOtpPageAppBarTitle => 'البدء';

  @override
  String get enterOtpPageEditButton => 'هل رقم واتساب خاطئ؟ يرجى التعديل';

  @override
  String get enterOtpPageErrorMessage => 'رمز غير صحيح\n';

  @override
  String get enterOtpPagePhoneLabel => 'أدخل رمز التحقق الخاص بك';

  @override
  String enterOtpPagePhoneLabelDescription(String phoneNumber) {
    return 'تم إرسال رمز مكون من 4 أرقام إلى رقم واتساب الخاص بك $phoneNumber';
  }

  @override
  String get enterOtpPageResendOtpButton => 'إعادة إرسال الرمز';

  @override
  String enterOtpPageResendOtpRetries(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: ' محاولات متبقية',
      many: '',
      few: '',
      two: '',
      one: 'محاولة أخيرة واحدة',
      zero: 'لم يتبق أي محاولات',
    );
    return '$_temp0';
  }

  @override
  String get enterOtpPageResendOtpRetriesError => 'لا يمكنك استقبال رموز التحقق على هذا الرقم من واتساب خلال الـ 24 ساعة القادمة';

  @override
  String get enterOtpPageSubmitButton => 'تأكيد رمز التفعيل';

  @override
  String get enterVoucherCodeLabel => 'أدخل رمز القسيمة';

  @override
  String get free => 'ادعُ شخصًا واربح';

  @override
  String get freeCreditAppbarTitle => 'رصيد مجاني';

  @override
  String get freeCreditPageContent_1 => 'شارك رمز الدعوة الخاص بك مع أصدقائك وعائلتك.';

  @override
  String get freeCreditPageContent_2 => 'يقومون بتحميل تطبيق FroggyTalk والتسجيل باستخدام رمزك.';

  @override
  String freeCreditPageContent_3(String amount) {
    return 'تكسب $amount عندما يشتري الشخص رصيد لأول مرة.';
  }

  @override
  String freeCreditPageHeadingText(String amount) {
    return 'ادعُ صديقًا واربح $amount';
  }

  @override
  String get freeCreditPageShareReferralLinkButtonText => 'مشاركة رابط الدعوة';

  @override
  String freeCreditPageSubHeadingText(String amount) {
    return 'دعواتي ($amount)';
  }

  @override
  String get froggytalkCustomerLabel => 'عميل Froggytalk';

  @override
  String get helpCenterAppbarTitle => 'مركز المساعدة';

  @override
  String get helpCenterPageMenu_1 => 'تقديم شكوى أو اقتراح';

  @override
  String get helpCenterPageMenu_2 => 'الدردشة مع مندوب خدمة العملاء';

  @override
  String get helpCenterPageMenu_3 => 'الأسئلة الشائعة';

  @override
  String get homeNavigationBarText => 'الرئيسية';

  @override
  String get internetConnectionAlertTextError => 'عذرًا! يبدو أنك غير متصل.';

  @override
  String get internetConnectionAlertTextSuccess => 'كل شيء جاهز! أنت متصل مرة أخرى.';

  @override
  String get invalidInternationalPhoneFormat => 'صيغة رقم الهاتف الدولي غير صالحة';

  @override
  String get invalidPhoneNumber => 'يرجى إدخال رقم هاتف صالح';

  @override
  String get keypadNavigationBarText => 'لوحة المفاتيح';

  @override
  String get landlineText => 'هاتف أرضي';

  @override
  String get loadVoucherCardButtonText => 'إعادة الشحن';

  @override
  String get loadVoucherCardErrorText => 'رمز خاطئ، حاول مرة أخرى';

  @override
  String get loadVoucherCardLabelText => 'أدخل رمز قسيمة مكون من 10 أرقام';

  @override
  String get loadVoucherCardTitle => 'تحميل رمز القسيمة';

  @override
  String get loadVoucherCodeTitle => 'تحميل رمز القسيمة';

  @override
  String get loginPageAppBarSubtitle => 'الخطوة 1 من 3';

  @override
  String get loginPageAppBarTitle => 'البدء';

  @override
  String get loginPageCheckboxLabel => 'لدي رمز دعوة';

  @override
  String get loginPageErrorMessage => 'الرقم غير مرتبط بحساب واتساب';

  @override
  String get loginPagePhoneLabel => 'أدخل رقم واتساب الخاص بك';

  @override
  String get loginPagePhoneLabelDescription => 'سيتم إرسال رمز تحقق لمرة واحدة إلى واتساب الخاص بك';

  @override
  String get loginPagePhoneNumberError => 'رقم هاتفك غير مكتمل، يرجى إكماله';

  @override
  String get loginPageReferralLabel => 'أدخل رمز الإحالة';

  @override
  String get loginPageSubmitButton => 'طلب التحقق';

  @override
  String minsOrMin(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'دقائق',
      many: '',
      few: '',
      two: '',
      one: 'دقيقة',
      zero: 'minsOrMin[zero]',
    );
    return '$_temp0';
  }

  @override
  String get mobileText => 'هاتف محمول';

  @override
  String get moreAppbarTitle => 'الحساب';

  @override
  String get moreNavigationBarText => 'المزيد';

  @override
  String get morePageAccountBalanceCardTitle => 'الرصيد';

  @override
  String get morePageAppVersionMenuText => 'إصدار التطبيق';

  @override
  String get morePageCallRatesMenuText => 'أسعار المكالمات';

  @override
  String get morePageHelpCenterMenuText => 'مركز المساعدة';

  @override
  String get morePageLanguageMenuText => 'اللغة';

  @override
  String get morePageLoadVoucherMenuText => 'شحن بإستخدام قسيمه';

  @override
  String get morePageLogoutMenuText => 'تسجيل الخروج';

  @override
  String get morePageProfileMenuText => 'الملف الشخصي\n';

  @override
  String get morePageRadioMenuText => 'الراديو';

  @override
  String get morePageReferralCodeCardButtonText => 'مزيد من المعلومات';

  @override
  String morePageReferralCodeCardContent(int numberOfPeople) {
    return '$numberOfPeople أشخاص استخدموا رمز الدعوة الخاص بك';
  }

  @override
  String morePageReferralCodeCardDescription(String amount) {
    return 'احصل على $amount عندما تدعو أصدقائك وعائلتك';
  }

  @override
  String get morePageReferralCodeCardTitle => 'رمز الدعوة';

  @override
  String get noBuyCreditOptionsAvailable => 'لا توجد خيارات متاحة لشراء الرصيد';

  @override
  String get noFavoriteContactsMessage => 'لا توجد جهات اتصال مفضلة';

  @override
  String get noNotificationsYet => 'لا توجد إشعارات بعد';

  @override
  String get notEnoughCreditMessage => 'ليس لديك رصيد كافٍ';

  @override
  String get notification => 'إشعار';

  @override
  String get notificationsAppBarTitle => 'الإشعارات';

  @override
  String get notificationsEmptyMessage => 'ليس لديك أي إشعارات';

  @override
  String get notificationSettingsAppBarTitle => 'إعدادات الإشعارات';

  @override
  String get notificationSettingsAutoDeleteNotificationText => 'الإشعار يتم حذفه تلقائيًا';

  @override
  String get notificationSettingsNotificationSoundText => 'صوت الإشعار\n';

  @override
  String get notificationsTabAllText => 'الكل';

  @override
  String get notificationsTabUnreadText => 'غير مقروء';

  @override
  String notificationsTimeAgo(int time, String duration) {
    String _temp0 = intl.Intl.pluralLogic(
      time,
      locale: localeName,
      other: '${duration}s',
      one: '$duration',
    );
    return '$time $_temp0 مضت';
  }

  @override
  String get okButtonText => 'موافق';

  @override
  String get onboardingPageFooterAndText => 'و';

  @override
  String get onboardingPageFooterPrivacy => 'سياسة الخصوصية';

  @override
  String get onboardingPageFooterTermsConditionsText => 'الشروط والأحكام\n';

  @override
  String get onboardingPageFooterText => 'بالنقر على \"البدء\"، فإنك تقبل';

  @override
  String get onboardingPageSliderHeader1 => 'تعدد اللغات';

  @override
  String get onboardingPageSliderHeader2 => 'اتصل بأرقام الهواتف المحمولة أو الخطوط الأرضية في جميع أنحاء العالم';

  @override
  String get onboardingPageSliderHeader3 => 'سهولة الدفع';

  @override
  String get onboardingPageSliderText1 => 'يوفر FroggyTalk لك اختيار اللغة المفضلة مثل التغرينية، الأمهرية، الهوسا وغيرها.';

  @override
  String get onboardingPageSliderText2 => 'قم بإجراء مكالمات لأي هاتف محمول أو خط أرضي في أي مكان بالعالم. لا يحتاج المستقبل إلى هاتف ذكي أو اتصال بالإنترنت.';

  @override
  String get onboardingPageSliderText3 => 'اشترِ رصيد مكالمات بعملتك المحلية باستخدام طريقة الدفع المفضلة لديك.';

  @override
  String get onboardingPageSubmitButtonText => 'ابدأ';

  @override
  String get outOfCreditLabel => 'الرصيد غير كافٍ';

  @override
  String get paymentFailureAppBarTitle => 'فشل';

  @override
  String get paymentFailureHomeButton => 'الرئيسية';

  @override
  String paymentFailureMessage(String amount) {
    return 'لم تتم عملية الدفع الخاصة بك بقيمة $amount';
  }

  @override
  String get paymentFailureTitle => 'فشل الدفع';

  @override
  String get paymentFailureTryAgainButton => 'حاول مرة أخرى';

  @override
  String get paymentOptionsAppBarTitle => 'خيارات الدفع';

  @override
  String paymentOptionsAutoCreditDescription(String amount, String minAmount) {
    return 'سيتم تلقائيًا إضافة $amount إلى حسابك عندما يكون رصيدك أقل من $minAmount.';
  }

  @override
  String get paymentOptionsAutoCreditTitle => 'إضافة تلقائية';

  @override
  String get paymentOptionsSelectPaymentMethod => 'اختر الطريقة المفضلة للدفع';

  @override
  String get paymentOptionsSummaryAmountToCreditText => 'المبلغ المضاف إلى الرصيد';

  @override
  String get paymentOptionsSummaryDiscountButtonText => 'تطبيق';

  @override
  String get paymentOptionsSummaryDiscountPlaceholder => 'أدخل رمز الخصم (اختياري)';

  @override
  String get paymentOptionsSummaryDiscountText => 'خصم';

  @override
  String get paymentOptionsSummaryTotalPaymentText => 'إجمالي الدفع';

  @override
  String get paymentOptionsSummaryVatFeesText => 'ضريبة القيمة المضافة + الرسوم';

  @override
  String get paymentSummaryAmountToCreditLabel => 'المبلغ الذي سيتم إضافته';

  @override
  String get paymentSummaryDiscountLabel => 'الخصم';

  @override
  String get paymentSummaryTotalLabel => 'إجمالي الدفع';

  @override
  String get paymentSummaryVatFeesLabel => 'ضريبة القيمة المضافة + الرسوم';

  @override
  String perMinRate(String rate) {
    return '$rate/د';
  }

  @override
  String get perMinRateSingle => '/دقيقة';

  @override
  String perMinuteRate(String rate) {
    return '$rate/دقيقة';
  }

  @override
  String perMinuteSlashLabel(String min) {
    return '$min/دقائق';
  }

  @override
  String get permissionButtonAccept => 'السماح بالوصول';

  @override
  String get permissionButtonSkip => 'تخطي';

  @override
  String get permissionForCameraTitle => 'نحتاج إلى إذن لالتقاط الصور ومقاطع الفيديو.';

  @override
  String get permissionForContactListTitle => 'اسمح لتطبيق FroggyTalk بالوصول إلى قائمة جهات الاتصال / دليل الهاتف الخاص بك';

  @override
  String get permissionForMicrophoneTitle => 'اسمح لتطبيق FroggyTalk بالوصول إلى الميكروفون الخاص بك لإجراء المكالمات';

  @override
  String get permissionForNotificationTitle => 'اسمح لتطبيق FroggyTalk بإرسال التحديثات لك';

  @override
  String get permissionForStorageTitle => 'نحتاج إلى إذن لتخزين البيانات المؤقتة على جهازك.';

  @override
  String get permissionPermanentlyDeniedMessage => 'تم رفض الإذن بشكل دائم';

  @override
  String get permissionDeniedMessage => 'تم رفض الإذن، يرجى تمكينه في إعدادات التطبيق';

  @override
  String perM_Rate(String rate) {
    return '$rate/د';
  }

  @override
  String get phoneNumberStartRule => 'يرجى وضع + أو 00 في بداية الرقم';

  @override
  String get proceedToPaymentButtonText => 'متابعة';

  @override
  String get profileAppbarTitle => 'الملف الشخصي';

  @override
  String get profileDeleteAccountButtonText => 'حذف الحساب';

  @override
  String get profileDeleteAccountCardContent => 'يرجى التأكيد إذا كنت تريد حذف حسابك. ستفقد أي رصيد موجود في حسابك.';

  @override
  String get profileDeleteAccountCardTitle => 'حذف الحساب\n';

  @override
  String get profileLabel => 'الملف الشخصي';

  @override
  String get profileLabelChangeLocation => 'تغيير الموقع';

  @override
  String get profileLabelEmailAddress => 'أدخل عنوان البريد الإلكتروني';

  @override
  String get profileLabelFullName => 'أدخل الاسم الكامل';

  @override
  String get profileUpdatedSuccessfullyMessage => 'تم تحديث الملف الشخصي بنجاح';

  @override
  String get profileUpdateFailedMessage => 'فشل تحديث الملف الشخصي';

  @override
  String get quickAdvertSubtitle => 'اشترِ رصيدًا لعائلتك في جميع أنحاء العالم';

  @override
  String get quickAdvertTitle => 'أرسل رصيدًا إلى أكثر من 140 دولة';

  @override
  String get radioAppBarTitle => 'الراديو';

  @override
  String get radioComingSoonText => 'الراديو قادم قريبًا !!!';

  @override
  String get radioPageChooseChannelButtonText => 'اختر المحطة';

  @override
  String get radioPageNoChannelsText => 'لم يتم العثور على محطات';

  @override
  String get radioPageSearchPlaceholder => 'اختر المحطة';

  @override
  String get ratesNavigationBarText => 'الأسعار';

  @override
  String get recentCallsText => 'المكالمات الأخيرة';

  @override
  String get rechargeButtonText => 'إعادة شحن';

  @override
  String get referAndEarn => 'دعوة و اكسب';

  @override
  String get referralCardButtonText => 'رصيد مجاني';

  @override
  String referralCardDescription1(String percentageAmount) {
    return 'ادعُ شخصًا واحصل على $percentageAmount رصيد مجاني.';
  }

  @override
  String referralCardDescription2(int numberOfPeople, num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$numberOfPeople أشخاص استخدموا رمز الدعوة الخاص بك.',
      many: '',
      few: '',
      two: '',
      one: 'شخص واحد استخدم رمز الدعوة الخاص بك.',
      zero: 'لم يستخدم أحد رمز الدعوة الخاص بك.',
    );
    return '$_temp0';
  }

  @override
  String get referralCardTitle => 'رمز الدعوة';

  @override
  String get referralCodeOnCopyActionResponse => 'تم نسخ FroggyTalk إلى الحافظة';

  @override
  String get saveAndProceedToPaymentButtonText => 'حفظ والمتابعة إلى الدفع';

  @override
  String get saveChangesButtonText => 'حفظ التغييرات';

  @override
  String get savedSuccessfullyMessage => 'تم الحفظ بنجاح';

  @override
  String get searchCountryPlaceholder => 'ابحث عن دولة';

  @override
  String get searchFavouriteContactMessage => 'البحث عن جهة اتصال مفضلة';

  @override
  String get searchForCountryNoResults => 'لا توجد نتائج';

  @override
  String get searchForCountryPlaceholder => 'ابحث عن دولة';

  @override
  String get searchRecentCallsMessage => 'البحث عن المكالمات الأخيرة';

  @override
  String secsOrSec(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'ثواني',
      many: '',
      few: '',
      two: '',
      one: 'ثانية',
      zero: '',
    );
    return '$_temp0';
  }

  @override
  String get selectCountryPlaceholder => 'اختر الدولة';

  @override
  String get sendCreditButtonText => 'أرسل رصيدًا';

  @override
  String get settings => 'الإعدادات';

  @override
  String shareReferralText(String appLink, String referralCode) {
    return 'قم بتحميل تطبيق FroggyTalk $appLink وقم بالتسجيل باستخدام رمز الدعوة الخاص بي $referralCode. يحتوي على أفضل الأسعار وتجربة المكالمات الدولية.';
  }

  @override
  String get somethingWentWrongMessage => 'حدث خطأ ما. يرجى المحاولة مرة أخرى.';

  @override
  String get unread => 'غير مقروء';

  @override
  String get updateProfileButtonText => 'تحديث الملف الشخصي';

  @override
  String get upgradeDialogButtonNo => 'ليس الآن\n';

  @override
  String get upgradeDialogButtonYes => 'تحديث';

  @override
  String get upgradeDialogMessage => 'يتوفر إصدار جديد من FroggyTalk. هل ترغب في التحديث الآن؟';

  @override
  String get upgradeDialogTitle => 'تحديث متوفر';

  @override
  String get validationCouponCodeIncomplete => 'رمز الخصم غير مكتمل أو غير صالح.';

  @override
  String get validationCouponInvalid => 'يرجى إدخال رمز خصم صالح.';

  @override
  String get validationEmailInvalid => 'البريد الإلكتروني غير صالح.';

  @override
  String validationFieldIsRequired(String fieldName) {
    return '$fieldName مطلوب';
  }

  @override
  String validationMinLengthError(String fieldName, int minLength) {
    return '$fieldName يجب أن يكون طوله على الأقل $minLength حرفًا';
  }

  @override
  String get validationPhoneNumberIncomplete => 'رقم الهاتف غير مكتمل.';

  @override
  String get viewButtonText => 'عرض';

  @override
  String get viewContactDetailAppBarTitle => 'تفاصيل جهة الاتصال';

  @override
  String get voucherLoadedSuccessMessage => 'تم تحميل القسيمة بنجاح';

  @override
  String get securedByStripe => 'مؤمن بواسطة سترايب';

  @override
  String get nextTrack => 'المسار التالي';

  @override
  String get stopRadio => 'إيقاف الراديو';

  @override
  String get playRadio => 'تشغيل الراديو';

  @override
  String get previousTrack => 'المسار السابق';

  @override
  String get channel => 'القناة';

  @override
  String get allChannels => 'جميع القنوات';

  @override
  String get acceptButtonText => 'قبول';

  @override
  String get declineButtonText => 'رفض';

  @override
  String allowShareInfoWithRadio(String radioName) {
    return 'السماح لنا بمشاركة معلوماتك مع $radioName';
  }

  @override
  String get nowPlaying => 'الآن يلعب';

  @override
  String get checkBackLaterText => 'يرجى التحقق لاحقاً';

  @override
  String get refreshText => 'تحديث';

  @override
  String get noRadioStationsAvailableText => 'لا توجد محطات راديو متاحة';

  @override
  String get tryAgainText => 'حاول مرة أخرى';

  @override
  String get unknownErrorText => 'حدث خطأ غير معروف';

  @override
  String get errorLoadingRadioStationsText => 'خطأ في تحميل محطات الراديو';

  @override
  String get loadingRadioStationsText => 'جاري تحميل محطات الراديو...';

  @override
  String get helpCenterPageMenu_4 => 'WhatsApp';

  @override
  String validationLengthError(String fieldName, int minLength, int maxLength) {
    return 'يجب أن يكون طول $fieldName بين $minLength و $maxLength حرفًا';
  }

  @override
  String get chooseLanguage => 'اختر اللغة\n';

  @override
  String get selectPreferredLanguage => 'إعادة المحاولة';

  @override
  String get cancel => ' لتجنب انقطاع المكالمة أثناء التحدث\n';

  @override
  String get apply => 'تقديم الطلب';

  @override
  String get languageChangeError => 'فشل في تغيير اللغة. يرجى المحاولة مرة أخرى';

  @override
  String get noLanguagesAvailable => 'لا توجد لغة متاحة';

  @override
  String get languageSetupPageAppBarSubtitle => 'الخطوة 3 من 3';

  @override
  String welcomeBackText(String firstName) {
    return 'مرحبًا بعودتك، $firstName';
  }

  @override
  String get froggyTalkLovesYou => 'فروغي توك يحبك.';

  @override
  String get updateLanguagePrompt => 'يرجى النقر على الزر أدناه لتحديث لغتك';

  @override
  String get updateYourLanguage => 'تحديث لغتك.';

  @override
  String get inAppPurchaseLabel => 'الشراء داخل التطبيق';

  @override
  String get restorePurchases => 'استعادة المشتريات';

  @override
  String get storePrice => 'سعر المتجر';

  @override
  String timeLeftModalTitle(String days) {
    return ' الأيام المتبقية\n';
  }

  @override
  String timeLeftModalMessage(String days, int count) {
    return 'تبقّى $days أيام لتكون الفائز بجائزة الكاش. قم بدعوة $count أشخاص وكن رقم ';
  }

  @override
  String get confirm => ' تأكيد';

  @override
  String get languageSelectionDisclaimer => ' يمكنك تغيير لغتك من خلال\n';

  @override
  String get activateAutoCredit => 'تفعيل الشحن التلقائي';

  @override
  String get autoAccountCreditHelp => 'سيتم خصم المبلغ من بطاقتك وإضافته إلى رصيدك كلما انخفض إلى ما دون';

  @override
  String get avoidCallDisruption => ' لتجنب انقطاع المكالمة أثناء التحدث\n';

  @override
  String cashPrizeAmount(String currencySymbol, String amount) {
    return '$currencySymbol$amount كاش مجاني';
  }

  @override
  String monthlyReferralBoard(String month) {
    return '$month  قادة العروض الترويجية';
  }

  @override
  String get termsAndConditionsApply => 'اختر لغتك المفضلة\n';

  @override
  String get getNow => 'احصل عليه الآن';

  @override
  String get payWithGooglePay => 'Google Pay';

  @override
  String get payWithApplePay => 'Apple Pay';

  @override
  String paymentSummaryPlayStoreFeeLabel(String percentage) {
    return '+ $percentage% عمولة + ضريبة';
  }

  @override
  String get failedToLoadLeaderboard => 'فشل في تحميل قائمة المتصدرين\n';

  @override
  String get retry => 'اختر لغتك المفضلة\n';

  @override
  String get googlePay => 'Google Pay';

  @override
  String appStoreCost(String value) {
    return '+ $value تكلفة متجر التطبيقات';
  }

  @override
  String get notification_call_reminder_title => 'Call Reminder';

  @override
  String notification_call_reminder_body(String contact) {
    return 'Don\'t forget to call $contact';
  }

  @override
  String get notification_promotion_title => 'Special Offer';

  @override
  String notification_promotion_body(String offer, String description) {
    return 'New offer: $offer - $description';
  }

  @override
  String get notification_security_alert_title => 'Security Alert';

  @override
  String notification_security_alert_body(String alertType, String device) {
    return 'Security alert: $alertType detected on $device';
  }

  @override
  String get notification_daily_reminder_title => 'Daily Reminder';

  @override
  String notification_daily_reminder_body(String reminderType) {
    return 'Don\'t forget: $reminderType';
  }
}
