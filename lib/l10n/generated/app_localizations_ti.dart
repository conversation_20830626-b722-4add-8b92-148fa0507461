// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Tigrinya (`ti`).
class AppLocalizationsTi extends AppLocalizations {
  AppLocalizationsTi([String locale = 'ti']) : super(locale);

  @override
  String get accountBalanceCardTitle => 'ተረፍ ሕሳብ';

  @override
  String get all => 'ኩሎም';

  @override
  String get allowAccessButtonText => 'ነዚ ኣድላዩ ስጉምቲ ንምፍፃም ዘድሊ ፍቃድ ንምሃብ';

  @override
  String get appBootingTextBelow => 'ርክብ ማሕበረሰባት ክናዋሕ ኣብ ምሕጋዝ';

  @override
  String get appLanguageAppbarTitle => 'ቋንቋ መተግበሪ';

  @override
  String get appTitle => 'FroggyTalk';

  @override
  String buyCreditAmountCustomPlaceholder(String min, String max) {
    return 'ዝተሓተ $min - ዝለዓለ $max';
  }

  @override
  String get buyCreditAmountRecommended => 'ዝምከር';

  @override
  String get buyCreditAppBarTitle => 'ካርዲ ንምዕዳግ';

  @override
  String get buyCreditButtonText => 'ካርዲ ንምዕዳግ';

  @override
  String get buyCreditEnterCustomAmountLabel => 'ዝመረፅዎ መጠን የእትዉ';

  @override
  String get buyCreditPageTitle => 'ዝመርፅዎ መጠን';

  @override
  String get callButtonText => 'ደወል';

  @override
  String get callingAppBarTitle => 'ኣብ ምድዋል';

  @override
  String get callingPageBluetoothOptionsText => 'ብሉቱዝ';

  @override
  String get callingPageCreditErrorCardTitle => 'ናይ መደወሊ ሒሳብ ወዲኦም';

  @override
  String get callingPagePhoneOptionsText => 'ስልኪ';

  @override
  String get callingPageSpeakerOptionsText => 'ስፒከር';

  @override
  String callingPageTimeLeft(String time) {
    return '$time ተሪፉ\n';
  }

  @override
  String get callLogTypeAnswered => 'ዝተልዓለ';

  @override
  String get callLogTypeBusy => 'ዝተትሓዘ መስመር';

  @override
  String get callLogTypeCancel => 'ዝተሰረዘ';

  @override
  String get callLogTypeIncoming => 'ኣታዊ ደወል';

  @override
  String get callLogTypeMissed => 'ዘይተልዓለ ደወል';

  @override
  String get callLogTypeOutgoing => 'ወፃኢ ደወል';

  @override
  String get callLogTypeUnavailable => 'ዘይተረኸበ';

  @override
  String get callRatesAppBarTitle => 'ናይ መደወሊ ተመናት';

  @override
  String get callTypeAnswered => 'ወፃኢ ደወል';

  @override
  String get callTypeBusy => 'ዝተትሓዘ መስመር';

  @override
  String get callTypeCancel => 'ዝተሰረዘ ደወል';

  @override
  String get callTypeIncoming => 'ኣታዊ ደወል';

  @override
  String get callTypeMissed => 'ዘይተልዓለ ደወል';

  @override
  String get callTypeOutgoing => 'ወፃኢ ደወል';

  @override
  String get callTypeUnavailable => 'ምርካብ ዘይከኣል ዓሚል';

  @override
  String get cancelButtonText => 'ንምስራዝ';

  @override
  String get changeChannelButtonText => 'ጣብያ ንምቅያር';

  @override
  String get chatWithLiveAgentAppbarTitle => 'አብ መስመር ዝርከብ ወኪል';

  @override
  String get closeButtonText => 'ንምዕፃው';

  @override
  String get confirmationAppBarTitle => 'መረጋገፂ';

  @override
  String get confirmationFailedButtonText => 'መሊሶም ይፈትኑ';

  @override
  String get confirmationFailedContactSupportDescription => 'ዝኾነ ሕቶ እንተሃልይኩም ወይ ድማ ሓገዝ እንተድልይኩም፤ በይዛኩም ክፍሊ ሓገዝና ርከቡ';

  @override
  String get confirmationFailedContactSupportText => 'ክፍሊ ሓገዝ ንምርካብ';

  @override
  String get confirmationFailedDescription => 'ክፍሊቶም አይተሳኸዐን። በይዘኦም ደጊሞም ይፈትኑ';

  @override
  String get confirmationFailedTitle => 'ክፍሊት ኣይተፈፀመን';

  @override
  String get confirmationSuccessButtonText => 'ተዛዚሙ';

  @override
  String confirmationSuccessDescription(String amount) {
    return '$amount ናብ ሕሳቦም ብዝተሳኸዐ መንገዲ አትዩ ኣሎ';
  }

  @override
  String get confirmationSuccessTitle => 'ክፍሊት ተሳኺዑ አሎ';

  @override
  String get contactsAllContactsTabText => 'መራክቦታታ';

  @override
  String contactsCallRateText(String min, String secs) {
    return '$min ደቒቓታት $secs ሰከንድ\n';
  }

  @override
  String get contactsFavouriteCallsTabText => 'ዝተመረፁ';

  @override
  String get contactsNoCallsEmptyMessage => 'ኣብ ቀረባ እዋን ዝተገበሩ ደወላት የብሎምን';

  @override
  String get contactsNoContactsButtonText => 'መራክቦ ንምርኣይ';

  @override
  String get contactsSearchContactsPlaceholder => 'መራክቦ ሽም ንምድላይ';

  @override
  String get contactsSearchForContactsPlaceholder => 'መራክቦ ሽም ንምድላይ';

  @override
  String get couponAppliedButtonText => 'ኣብ ተግበር ውዒሉ';

  @override
  String get couponApplyButtonText => 'ንምጥቃም';

  @override
  String get credit => 'ሕሳብ';

  @override
  String get deleteAccountWarning => 'እቲ ዝፍፅምዎ ዝተዳለዉ ስጉምቲ ዘይምለስ ሳዕቤን ዘኸትል እዩ። በይዘኦም ሕሳቦም ይዓፅዉ ከምዘለዉ የረጋግፁ።';

  @override
  String get dialerAppBarTitle => 'መደወሊ';

  @override
  String get dialerCopyActionText => 'ንምቕዳሕ';

  @override
  String get dialerErrorInvalidNumber => 'ዝተሰሓሐተ ቁፅሪ';

  @override
  String get dialerErrorWithoutCountryCode => 'ኮድ ሃገር የእትዉ (ንኣብነት +1)';

  @override
  String get dialerKeypadABC => 'ABC';

  @override
  String get dialerKeypadDEF => 'DEF';

  @override
  String get dialerKeypadGHI => 'GHI';

  @override
  String get dialerKeypadJKL => 'JKL';

  @override
  String get dialerKeypadMNO => 'MNO';

  @override
  String get dialerKeypadPQRS => 'PQRS';

  @override
  String get dialerKeypadTUV => 'TUV';

  @override
  String get dialerKeypadWXYZ => 'WXYZ';

  @override
  String get dialerMuteStatusMuted => 'ድምፂ ጠፊኡ';

  @override
  String get dialerMuteStatusUnmuted => 'ድምፂ ይስማዕ';

  @override
  String get dialerPasteActionText => 'ንምልጣፍ';

  @override
  String get dialerSearchResultsHeader => 'ውፅኢታት';

  @override
  String get dialerSearchResultsNoResults => 'ዝተረኸበ ውፅኢት የለን';

  @override
  String get dialerStatusConnected => 'ተራኪቡ';

  @override
  String get dialerStatusConnecting => 'ኣብ ምድዋል';

  @override
  String get dialerStatusEnded => 'ተዓፂዉ';

  @override
  String get dialerStatusError => 'ስሕተት ተፈጢሩ';

  @override
  String get dialerStatusFailedToConnect => 'ምርካብ ኣይተክኣለን';

  @override
  String get dialerStatusHold => 'ኣብ ምፅባይ';

  @override
  String get dialerStatusInitial => 'ኣብ ምድዋል';

  @override
  String get dialerStatusInitiating => 'ኣብ ምድዋል';

  @override
  String get dialerStatusRinging => 'ይፅውዕ ኣሎ';

  @override
  String get dialerStatusUnknown => 'ዘይፍለጥ';

  @override
  String get emptyContactList => 'ዝርዝር መራክቦ የብሎምን';

  @override
  String get emptyFavouriteContactList => 'ናይ ዝተመረፁ መራክቦታት ዝርዝር የብሎምን';

  @override
  String get enterCouponOptionalPlaceholder => 'ኩፖን የእትዉ (እንተደልዮም)';

  @override
  String get enterOtpPageAppBarSubtitle => '2ይ ክፋል ካብ 3';

  @override
  String get enterOtpPageAppBarTitle => 'ንምጅማር';

  @override
  String get enterOtpPageEditButton => 'ዝተሰሓሐተ ቁፅሪ WhatsApp? በይዘኦም የስተኻክሉ';

  @override
  String get enterOtpPageErrorMessage => 'ዝተሰሓሐተ ኮድ';

  @override
  String get enterOtpPagePhoneLabel => 'መረጋገፂ ኮድ የእትዉ';

  @override
  String enterOtpPagePhoneLabelDescription(String phoneNumber) {
    return 'በዓል 4 ቁፅሪ ኮድ ናብ WhatsApp ቁፅሮም $phoneNumber ተላኢኹ ኣሎ';
  }

  @override
  String get enterOtpPageResendOtpButton => 'ኮድ ዳግማይ ንምልኣኽ';

  @override
  String enterOtpPageResendOtpRetries(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count ተወሳኪ ፈተነታት',
      one: 'ሓደ ናይ መወዳእታ ፈተነ',
      zero: 'ዝተረፎም ፈተነ የለን',
    );
    return '$_temp0';
  }

  @override
  String get enterOtpPageResendOtpRetriesError => 'በዚ ቁፅሪ WhatsApp ንዝቕፅል 24 ሰዓታት ናይ መረጋገፂ ኮድ ክትቅበሉ ኣይትኽእሉን';

  @override
  String get enterOtpPageSubmitButton => 'ይ ሓደ ግዘ ናይ ሕለፍ ቃል ንምርግጋፅ';

  @override
  String get enterVoucherCodeLabel => 'ኮድ ካርዲ የእትዉ';

  @override
  String get free => 'ናፃ';

  @override
  String get freeCreditAppbarTitle => 'ናፃ ካርዲ';

  @override
  String get freeCreditPageContent_1 => 'መአንገዲ ኮድኩም ንመሓዙትኩም & ቤተሰብኩም ኣባፅሑ።';

  @override
  String get freeCreditPageContent_2 => 'FroggyTalk ኣውሪዶም መአንገዲ ኮድኩም ተጠቒሞም ይመዝገቡ።';

  @override
  String freeCreditPageContent_3(String amount) {
    return 'እቲ ሰብ ንመጀመርታ እዋን ካርዲ ክገዝእ ከሎ $amount ክረክቡ እዮም።';
  }

  @override
  String freeCreditPageHeadingText(String amount) {
    return 'መሓዛኹም ኣንጊድኩም ናይ $amount ኣታዊ  ርኸቡ';
  }

  @override
  String get freeCreditPageShareReferralLinkButtonText => 'ናይ መአንገዲ መፈንጠሪ ንምልኣክ';

  @override
  String freeCreditPageSubHeadingText(String amount) {
    return 'ኣነ ዝኣንገድኩዎም ($amount)';
  }

  @override
  String get froggytalkCustomerLabel => 'ዓሚል Froggytalk';

  @override
  String get helpCenterAppbarTitle => 'ማእኸል ሓገዝ';

  @override
  String get helpCenterPageMenu_1 => 'ጥርዓን ወይ ሓሳብ ይሃቡ';

  @override
  String get helpCenterPageMenu_2 => 'አብ መስመር ዝርከብ ወኪልና ይርከቡ';

  @override
  String get helpCenterPageMenu_3 => 'ብተደጋጋሚ ዝሕተቱ ሕቶታት';

  @override
  String get homeNavigationBarText => 'መበገሲ ገፅ';

  @override
  String get internetConnectionAlertTextError => 'እውይ! ካብ መስመር ወፂኦ እዮም ዘለዉ';

  @override
  String get internetConnectionAlertTextSuccess => 'ኹሉ ድሉው እዩ! መሊሶም ተራኺቦም ኣለዉ።';

  @override
  String get invalidInternationalPhoneFormat => 'ዘይሰርሕ ዓይነት ዓለምለኻዊ ቁፅሪ ስልኪ';

  @override
  String get invalidPhoneNumber => 'በይዘኦም ትክክል ዝኮነ ቁፅሪ ስልኪ የእትዉ';

  @override
  String get keypadNavigationBarText => 'መፅሐፊ';

  @override
  String get landlineText => 'ናይ መስመር ስልኪ';

  @override
  String get loadVoucherCardButtonText => 'ንምምላእ';

  @override
  String get loadVoucherCardErrorText => 'ዝተሰሓሐተ ኮድ፤ መሊሶም ይፈትኑ';

  @override
  String get loadVoucherCardLabelText => 'ባዓል 10 ኣሃዝ ካይ ካርዲ ኮድ የእትዉ';

  @override
  String get loadVoucherCardTitle => 'ኮድ ካርዲ የእትዉ';

  @override
  String get loadVoucherCodeTitle => 'ኮድ ካርዲ ንምእታዉ';

  @override
  String get loginPageAppBarSubtitle => '1ይ ክፋል ካብ 3';

  @override
  String get loginPageAppBarTitle => 'መጀመሪ';

  @override
  String get loginPageCheckboxLabel => 'ዝተኣንገድኩሉ ኮድ አለኒ';

  @override
  String get loginPageErrorMessage => 'በዚ ቁፅሪ ስልኪ ዝተከፈተ WhatsApp የለን';

  @override
  String get loginPagePhoneLabel => 'ናይ WhatsApp ቁፅሮም የእትዉ';

  @override
  String get loginPagePhoneLabelDescription => 'ብWhatsApp ኣቢሉ ናይ ሓደ ግዘ መረጋገፂ ኮድ ክለኣከሎም እዩ';

  @override
  String get loginPagePhoneNumberError => 'ቁፅሪ ስልኮም ኣይተማልአን። በይዞኦም የማልእዎ';

  @override
  String get loginPageReferralLabel => 'ዝተኣንገዱሉ ኮድ ንምእታዉ';

  @override
  String get loginPageSubmitButton => 'መረጋገፂ ክለኣከሎም ይሕተቱ';

  @override
  String minsOrMin(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count ደቃይቅ',
      one: '1 ደቒቕ',
      zero: '',
    );
    return '$_temp0';
  }

  @override
  String get mobileText => 'ሞባይል';

  @override
  String get moreAppbarTitle => 'ሕሳብ';

  @override
  String get moreNavigationBarText => 'ተወሳኺ';

  @override
  String get morePageAccountBalanceCardTitle => 'ተረፍ ሕሳብ';

  @override
  String get morePageAppVersionMenuText => 'ናይ መተግበሪ ሕታም';

  @override
  String get morePageCallRatesMenuText => 'ናይ ፃውዒት ተመናት';

  @override
  String get morePageHelpCenterMenuText => 'ማእኸል ሓገዝ';

  @override
  String get morePageLanguageMenuText => 'ቋንቋ';

  @override
  String get morePageLoadVoucherMenuText => 'ካርዲ ንምምላእ';

  @override
  String get morePageLogoutMenuText => 'ንምውፃእ';

  @override
  String get morePageProfileMenuText => 'መግለፂ';

  @override
  String get morePageRadioMenuText => 'ሬድዮ';

  @override
  String get morePageReferralCodeCardButtonText => 'ተወሳኺ ሓበሬታ';

  @override
  String morePageReferralCodeCardContent(int numberOfPeople) {
    return '$numberOfPeople ሰባት ናቶም መጋበዚ ኮድ ተጠቒሞም አለዉ';
  }

  @override
  String morePageReferralCodeCardDescription(String amount) {
    return 'መሓዙቶምን ኣዝማዶምን ጋቢዞም $amount ይርኸቡ';
  }

  @override
  String get morePageReferralCodeCardTitle => 'መአንገዲ ኮድ';

  @override
  String get noBuyCreditOptionsAvailable => 'ሕሳብ ናይ ምግዛእ መማረፂ የለን';

  @override
  String get noFavoriteContactsMessage => 'ዝተመረፁ መራክቦታት የለውን';

  @override
  String get noNotificationsYet => 'ዝበፅሐ መፍለጢ የለን';

  @override
  String get notEnoughCreditMessage => 'እኩል ሕሳብ የብሎምን';

  @override
  String get notification => 'መፍለጢ';

  @override
  String get notificationsAppBarTitle => 'መፍለጢ';

  @override
  String get notificationsEmptyMessage => 'ዝበዝሖም ዝኮነ መፍለጢ የለን';

  @override
  String get notificationSettingsAppBarTitle => 'መስርሕታት መፍለጢ';

  @override
  String get notificationSettingsAutoDeleteNotificationText => 'መፍለጢ ባዕሉ ይጥፋእ';

  @override
  String get notificationSettingsNotificationSoundText => 'ናይ መፍለጢ ድምፂ';

  @override
  String get notificationsTabAllText => 'ኹሎም';

  @override
  String get notificationsTabUnreadText => 'ዘይተነበቡ';

  @override
  String notificationsTimeAgo(int time, String duration) {
    String _temp0 = intl.Intl.pluralLogic(
      time,
      locale: localeName,
      other: '$duration',
      one: '$duration',
    );
    return 'ቅድሚ $time $_temp0 \n';
  }

  @override
  String get okButtonText => 'እሺ';

  @override
  String get onboardingPageFooterAndText => 'ከምኡውን';

  @override
  String get onboardingPageFooterPrivacy => 'ፖሊሲ ውልቃዊ ጕዳያት';

  @override
  String get onboardingPageFooterTermsConditionsText => 'ውዕልን ኵነታትን';

  @override
  String get onboardingPageFooterText => 'ንምጅማር ዝብል ብምጥዋቕ ብተወሳኪ ዝቅበልዎ';

  @override
  String get onboardingPageSliderHeader1 => 'ብዙሕ ቋንቋታት\n';

  @override
  String get onboardingPageSliderHeader2 => 'ኣብ መላእ ዓለም ናብ ዝመረፅዎ ሞባይል ወይ ናይ መስመር ስልኪ ይደውሉ';

  @override
  String get onboardingPageSliderHeader3 => 'ቀሊል ክፍሊት';

  @override
  String get onboardingPageSliderText1 => 'FroggyTalk ብመረፅዎ ቋንቋ ንኣብነት ብትግርኛ፣ አምሓርኛ፣ ሃውሳ ወዘተ ክግልገሉ የክእል።';

  @override
  String get onboardingPageSliderText2 => 'ኣብ ዝኾነ ክፍሊ ዓለም ናብ ዝርከብ ሞባይል ወይ ናይ መስመር ስልኪ ይደውሉ። እቶም ተቐባሊ ዘመናዊ ሞባይል ኮነ ኢንተርነት ኣየድልዮምን።';

  @override
  String get onboardingPageSliderText3 => 'ኣብ ከባቢኣኦም ብዝጥቀምዎ ገንዘብ ናይ መደወሊ ካርድ ብዝመረፅዎ ሜላ ኽፍሊት ይዓድጉ።';

  @override
  String get onboardingPageSubmitButtonText => 'ንምጅማር';

  @override
  String get outOfCreditLabel => 'ተረፍ ሕሳብ የብሎምን';

  @override
  String get paymentFailureAppBarTitle => 'ኣይተሳክዐን';

  @override
  String get paymentFailureHomeButton => 'መበገሲ ገፅ';

  @override
  String paymentFailureMessage(String amount) {
    return 'ናይ $amount ክፍሊቶም ኣይተሳክዐን';
  }

  @override
  String get paymentFailureTitle => 'ክፍሊት ኣይተሳክዐን';

  @override
  String get paymentFailureTryAgainButton => 'መሊሶም ንምፍታን';

  @override
  String get paymentOptionsAppBarTitle => 'ናይ ክፍሊት መማረፅታት';

  @override
  String paymentOptionsAutoCreditDescription(String amount, String minAmount) {
    return 'እዚ ድማ ሕሳቦም ትሕቲ $minAmount እንትኮን ባዕሉ ናብ ሒሳቦም $amount ከእትወሎም አሎ።';
  }

  @override
  String get paymentOptionsAutoCreditTitle => 'ዓርሱ ዝመልእ';

  @override
  String get paymentOptionsSelectPaymentMethod => 'ዝደለይዎ ሜላ ክፍሊት ይምረፁ';

  @override
  String get paymentOptionsSummaryAmountToCreditText => 'ኣታዊ ዝኮን መጠን';

  @override
  String get paymentOptionsSummaryDiscountButtonText => 'ንምትግባር';

  @override
  String get paymentOptionsSummaryDiscountPlaceholder => 'ናይ ቅናሽ ኮድ የእትዉ(እንተሃልይዎም)';

  @override
  String get paymentOptionsSummaryDiscountText => 'ቅናሽ';

  @override
  String get paymentOptionsSummaryTotalPaymentText => 'ጠቕላላ ክፍሊት';

  @override
  String get paymentOptionsSummaryVatFeesText => 'VAT + ክፍሊት';

  @override
  String get paymentSummaryAmountToCreditLabel => 'ኣታዊ ዝግበር መጠን';

  @override
  String get paymentSummaryDiscountLabel => 'ቅናሽ';

  @override
  String get paymentSummaryTotalLabel => 'ጠቅላላ ክፍሊት';

  @override
  String get paymentSummaryVatFeesLabel => 'VAT + ክፍሊታት';

  @override
  String perMinRate(String rate) {
    return '$rate/ደቒቓ\n';
  }

  @override
  String get perMinRateSingle => '/ደቒቓ';

  @override
  String perMinuteRate(String rate) {
    return '$rate/ደቒቓ\n';
  }

  @override
  String perMinuteSlashLabel(String min) {
    return '$min/ደቃይቅ';
  }

  @override
  String get permissionButtonAccept => 'ፍቃድ ንምሃብ';

  @override
  String get permissionButtonSkip => 'ንምስጋር';

  @override
  String get permissionForCameraTitle => 'ስእልታትን ቪድዮታትን ንምስኣል ፍቓድ የድልየና እዩ';

  @override
  String get permissionForContactListTitle => 'FroggyTalk ናብ ዝርዝር መራክቦታትኩም / መዝገብ ቁፅሪ ስልክታት ክኣቱ ፍቃድ ይሃቡ';

  @override
  String get permissionForMicrophoneTitle => 'FroggyTalk ማይክሮፎን ክጥቀም ብምግባር ደወላት ንምትእንጋድ ይፍቀዱ';

  @override
  String get permissionForNotificationTitle => 'FroggyTalk ምምሕያሻት ክሰደልኩም ፍቃድ ይሃቡ';

  @override
  String get permissionForStorageTitle => 'ንዝተወሰነ ግዜ ኣብ ሞባይሎም ሰነዳት ክነቕምጥ ፍቓድ የድልየና እዩ';

  @override
  String get permissionPermanentlyDeniedMessage => 'ፍቓድ ብፍፁም ተኣግዶሎም ኣሎ፣ ኣብ መተግበሪ ቅንብሮታት እንዲከፍቱ በጃኹም';

  @override
  String get permissionDeniedMessage => 'ፍቓድ ተኣግዶሎም ኣሎ፣ ኣብ መተግበሪ ቅንብሮታት እንዲከፍቱ በጃኹም';

  @override
  String perM_Rate(String rate) {
    return '$rate/ደቒቓ\n';
  }

  @override
  String get phoneNumberStartRule => 'በይዘኦም ብ + ወይ 00 ጀሚሮም ይፅሓፉ';

  @override
  String get proceedToPaymentButtonText => 'ንምቅፃል';

  @override
  String get profileAppbarTitle => 'መግለፂ';

  @override
  String get profileDeleteAccountButtonText => 'ሕሳቦም ንምድምሳስ';

  @override
  String get profileDeleteAccountCardContent => 'በይዘኦም ሕሳቦም ክድምስሱ ከምዝደልዩ የረጋግፁ። ኣብ ሒሳቦም ዝሎ ተረፍ ገንዘብ  እውን ሓቢዉ ክጠፍእ እዩ።';

  @override
  String get profileDeleteAccountCardTitle => 'ሕሳቦም ንምድምሳስ';

  @override
  String get profileLabel => 'መግለፂ';

  @override
  String get profileLabelChangeLocation => 'ቦታ ንምቕያር';

  @override
  String get profileLabelEmailAddress => 'ኢመይል ኣድራሻ የእቱዉ';

  @override
  String get profileLabelFullName => 'ምሉእ ስም የእትዉ';

  @override
  String get profileUpdatedSuccessfullyMessage => 'መግለፂ ብዝተሳክዐ መልክዑ ተመሓይሹ';

  @override
  String get profileUpdateFailedMessage => 'መግለፂ ምምሕያሽ ኣይተክኣለን';

  @override
  String get quickAdvertSubtitle => 'ኣብ መላእ ዓለም ንዝርከቡ ኣዝማድኩም ናይ ኣየር ሰዓት ዓድጉሎም';

  @override
  String get quickAdvertTitle => 'ናብ 140+ ሃገራት ካርዲ ይስደዱ';

  @override
  String get radioAppBarTitle => 'ሬድዮ';

  @override
  String get radioComingSoonText => 'ሬድዮ ኣብ ቀረባ እዋን ክጅምር እዩ !!!';

  @override
  String get radioPageChooseChannelButtonText => 'ጣብያ ንምምራፅ';

  @override
  String get radioPageNoChannelsText => 'ዝተረኸቡ ጣብያታት የለውን';

  @override
  String get radioPageSearchPlaceholder => 'ጣብያ ንምምራፅ';

  @override
  String get ratesNavigationBarText => 'ተመን';

  @override
  String get recentCallsText => 'ናይ ቀረባ እዋን ደወላይ';

  @override
  String get rechargeButtonText => 'ንምምላእ';

  @override
  String get referAndEarn => 'ኣንዲጎም ኣታዊ ይርከቡ';

  @override
  String get referralCardButtonText => 'ናይ ናፃ ካርዲ';

  @override
  String referralCardDescription1(String percentageAmount) {
    return 'ሰብ ኣንጊዶም ናይ $percentageAmount ናፃ ካርዲ ይርኽቡ።';
  }

  @override
  String referralCardDescription2(int numberOfPeople, num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$numberOfPeople ሰባት መአንገዲ ኮዶም ተጠቑሞም ኣለዉ።',
      one: 'ሓደ ሰብ መአንገዲ ኮዶም ተጠቑሞም ኣለዉ።',
      zero: 'መአንገዲ ኮዶም ዝተጠቐሞ ሰብ የለን።',
    );
    return '$_temp0';
  }

  @override
  String get referralCardTitle => 'መአንገዲ ኮድ';

  @override
  String get referralCodeOnCopyActionResponse => 'FroggyTalk ንግዚኡ ተቀሚጡ ኣሎ';

  @override
  String get saveAndProceedToPaymentButtonText => 'መረዳእታ ንምዕቃብ & ናብ ክፍሊት ንምቅፃል';

  @override
  String get saveChangesButtonText => 'ለውጥታት ንምዕቃብ';

  @override
  String get savedSuccessfullyMessage => 'ብዝተሳክዐ መልክዑ ተዓቂቡ';

  @override
  String get searchCountryPlaceholder => 'ሃገር ንምድላይ';

  @override
  String get searchFavouriteContactMessage => 'ዝተመረፁ መራክቦታት ንምእላሽ';

  @override
  String get searchForCountryNoResults => 'ውፅኢት ኣይተረኽበን';

  @override
  String get searchForCountryPlaceholder => 'ሃገር ንምድላይ';

  @override
  String get searchRecentCallsMessage => 'ኣብ ቀረባ እዋን ዝተገበሩ ደወላት ንምእላሽ';

  @override
  String secsOrSec(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count ሰከንድ',
      one: '1 ሰከንድ',
      zero: '',
    );
    return '$_temp0';
  }

  @override
  String get selectCountryPlaceholder => 'ሃገር ንምምራፅ';

  @override
  String get sendCreditButtonText => 'ካርዲ ንምልኣኽ';

  @override
  String get settings => 'መስርሕታት';

  @override
  String shareReferralText(String appLink, String referralCode) {
    return 'ናይ FroggyTalk መተግበሪ $appLink ኣውሪዶም ናተይ መአንገዲ ኮድ $referralCode ብምጥቃም ይመዝገቡ። ካብ ኩሉ ዝረከሰ ናይ ዋባ ተመንን ዓለምለኻዊ ደወል ምግባር ዘክእልን እዩ።';
  }

  @override
  String get somethingWentWrongMessage => 'ስሕተት ተፈጢሩ ኣሎ። በይዘኦም መሊሶም ይፈትኑ።';

  @override
  String get unread => 'ዘይተነበበ';

  @override
  String get updateProfileButtonText => 'መግለፂኦም ንምምሕያሽ';

  @override
  String get upgradeDialogButtonNo => 'ሐዚ ኣይኮነን';

  @override
  String get upgradeDialogButtonYes => 'ንምምሕያሽ';

  @override
  String get upgradeDialogMessage => 'ዝተመሓየሸ ናይ FroggyTalk መተግበሪ ድሉው እዩ። ሐዚ ክፅዕኑ ይደልዩ?';

  @override
  String get upgradeDialogTitle => 'ሓድሽ ዝወፅአ';

  @override
  String get validationCouponCodeIncomplete => 'ዘይተማልአ ወይ ዝተሰሓሐተ ናይ ቅናሽ ኮድ ';

  @override
  String get validationCouponInvalid => 'በይዘኦም ትክክል ዝኮነ ኮድ ቅናሽ የእትዉ';

  @override
  String get validationEmailInvalid => 'ዝተሰሓሐተ ኣድራሻ ኢሜይል።';

  @override
  String validationFieldIsRequired(String fieldName) {
    return '$fieldName ኣድላዩ እዩ';
  }

  @override
  String validationMinLengthError(String fieldName, int minLength) {
    return '$fieldName ብንኡሱ $minLength ዝንውሓቱ ክከውን ኣለዎ';
  }

  @override
  String get validationPhoneNumberIncomplete => 'ዘይተማልአ ቁፅሪ ስልኪ።';

  @override
  String get viewButtonText => 'ንምርኣይ';

  @override
  String get viewContactDetailAppBarTitle => 'ዝርዝር መራክቦ';

  @override
  String get voucherLoadedSuccessMessage => 'እቲ ካርዲ ብዝተሳክዐ መልክዑ ኣቲዉ';

  @override
  String get securedByStripe => 'ብስትራይፕ ዝተሓለወ';

  @override
  String get nextTrack => 'ዝቕፅል መዝሙር';

  @override
  String get stopRadio => 'ሬድዮ ንምድምዳም';

  @override
  String get playRadio => 'ሬድዮ ንምጅማር';

  @override
  String get previousTrack => 'ዝሓለፈ መዝሙር';

  @override
  String get channel => 'ጣብያ';

  @override
  String get allChannels => 'ኩሎም ጣብያታት';

  @override
  String get acceptButtonText => 'ይቕበሉ';

  @override
  String get declineButtonText => 'ይነፅጉ';

  @override
  String allowShareInfoWithRadio(String radioName) {
    return 'ሓበሬታኹም ምስ $radioName ክንካፈል ፍቓድ ይሃቡና';
  }

  @override
  String get nowPlaying => 'ሕጂ ምጽዋት';

  @override
  String get checkBackLaterText => 'በይዞም ድሓር ይርኣዩ';

  @override
  String get refreshText => 'ንምሕዳስ';

  @override
  String get noRadioStationsAvailableText => 'ዝርከቡ ናይ ሬድዮ ጣብያታት የለውን';

  @override
  String get tryAgainText => 'መሊሶም ይፈትኑ';

  @override
  String get unknownErrorText => 'ዘይፍለጥ ስሕተት ተፈጢሩ';

  @override
  String get errorLoadingRadioStationsText => 'ናይ ሬድዮ ጣብያታት ኣብ ምፅዓን ስሕተት ተፈጢሩ';

  @override
  String get loadingRadioStationsText => 'ናይ ሬድዮ ጣብያታት ይጽዓን ኣሎ...';

  @override
  String get helpCenterPageMenu_4 => 'WhatsApp';

  @override
  String validationLengthError(String fieldName, int minLength, int maxLength) {
    return '$fieldName ካብ $minLength ክሳብ $maxLength ፊደላት ክኸውን ኣለዎ';
  }

  @override
  String get chooseLanguage => 'ቋንቋ ምረጹ';

  @override
  String get selectPreferredLanguage => 'ዝደለኽምዎ ቋንቋ ምረጹ';

  @override
  String get cancel => 'ሰርዝ';

  @override
  String get apply => 'ኣተግብር';

  @override
  String get languageChangeError => 'ቋንቋታት ክቕይር ኣይከኣለን። በጃኹም ደጊምኩም ፈትኑ';

  @override
  String get noLanguagesAvailable => 'ቋንቋ የለን';

  @override
  String get languageSetupPageAppBarSubtitle => '3ይ ክፋል ካብ 3';

  @override
  String welcomeBackText(String firstName) {
    return 'እንቋዕ ተመለስኩም, $firstName';
  }

  @override
  String get froggyTalkLovesYou => 'FroggyTalk የፍቅረኩም ኣሎ።';

  @override
  String get updateLanguagePrompt => 'በይዘኦም ቋንቋኦም ንምምሕያሽ ኣብ ታሕቲ ዘሎ ማዕጠንቲ የጠውቁ';

  @override
  String get updateYourLanguage => 'ቋንቋኦም ንምምሕያሽ።';

  @override
  String get inAppPurchaseLabel => 'ዕድጊ ኣብ ውሽጢ ኣፕሊኬሽን';

  @override
  String get restorePurchases => 'ዕድጊታት ምምላስ';

  @override
  String get storePrice => 'ዋጋ መደብር';

  @override
  String timeLeftModalTitle(String days) {
    return '$days መዓልታት ተሪፎም ';
  }

  @override
  String timeLeftModalMessage(String days, int count) {
    return '$days ናይቲ ናይ ገንዘብ ሽልማት ተዓወቲ ንምዃን መዓልታት ተሪፍዎም ኣሎ።ተወከሱ  $count ንሰባት እሞ ቁጽሪ 1 ኩኑ';
  }

  @override
  String get confirm => 'ኣረጋግጹ ';

  @override
  String get languageSelectionDisclaimer => 'ኣብ ታሕቲ ቋንቋ ክትቅይሩ ትኽእሉ ኢኹም።';

  @override
  String get activateAutoCredit => 'ኣውቶ ክሬዲት ንጡፍ ምግባር ';

  @override
  String get autoAccountCreditHelp => 'ኣብ ካርድኹም ክኽፈልን ድሕሪኡ ኣብ ዝወደቐሉ እዋን ኣብ ሚዛንኩም ክውሰኽን እዩ';

  @override
  String get avoidCallDisruption => 'ኣብ እዋን ጻውዒት ንከይቛረጽ';

  @override
  String cashPrizeAmount(String currencySymbol, String amount) {
    return '$currencySymbol$amount ነጻ ጥረ ገንዘብ';
  }

  @override
  String monthlyReferralBoard(String month) {
    return '$month መራሕቲ ፕሮሞ';
  }

  @override
  String get termsAndConditionsApply => 'ውዕላትን ቅጥዕታትን ይምልከቱ';

  @override
  String get getNow => 'ሕጂ ርኸቡ';

  @override
  String get payWithGooglePay => 'Google Pay';

  @override
  String get payWithApplePay => 'Apple Pay';

  @override
  String paymentSummaryPlayStoreFeeLabel(String percentage) {
    return '  + $percentage% ኮሚሽንን ቫት';
  }

  @override
  String get failedToLoadLeaderboard => 'ሊደርቦርድ ክጽዕን ኣይከኣለን';

  @override
  String get retry => 'ዳግማይ ፈትኑ';

  @override
  String get googlePay => 'Google Pay';

  @override
  String appStoreCost(String value) {
    return '+ $value ዋጋ AppStore';
  }

  @override
  String get notification_call_reminder_title => 'Call Reminder';

  @override
  String notification_call_reminder_body(String contact) {
    return 'Don\'t forget to call $contact';
  }

  @override
  String get notification_promotion_title => 'Special Offer';

  @override
  String notification_promotion_body(String offer, String description) {
    return 'New offer: $offer - $description';
  }

  @override
  String get notification_security_alert_title => 'Security Alert';

  @override
  String notification_security_alert_body(String alertType, String device) {
    return 'Security alert: $alertType detected on $device';
  }

  @override
  String get notification_daily_reminder_title => 'Daily Reminder';

  @override
  String notification_daily_reminder_body(String reminderType) {
    return 'Don\'t forget: $reminderType';
  }
}
