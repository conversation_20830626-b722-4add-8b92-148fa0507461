// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Hausa (`ha`).
class AppLocalizationsHa extends AppLocalizations {
  AppLocalizationsHa([String locale = 'ha']) : super(locale);

  @override
  String get accountBalanceCardTitle => 'kudin asusu';

  @override
  String get all => 'Duka';

  @override
  String get allowAccessButtonText => 'Bada izinin yin wannan aikin da ake bukata';

  @override
  String get appBootingTextBelow => 'Tai<PERSON><PERSON> da na kasar waje kiran gida';

  @override
  String get appLanguageAppbarTitle => 'Harshe na manhaja';

  @override
  String get appTitle => 'FroggyTalk';

  @override
  String buyCreditAmountCustomPlaceholder(String min, String max) {
    return 'Mafi ƙanƙanta adadi $min -  ${max}Mafi yawa adadi     ';
  }

  @override
  String get buyCreditAmountRecommended => 'An ba da shawara akan adadin sayen kuɗi';

  @override
  String get buyCreditAppBarTitle => 'Siya kati';

  @override
  String get buyCreditButtonText => 'Siya kati';

  @override
  String get buyCreditEnterCustomAmountLabel => 'Shigar da adadin da kake so';

  @override
  String get buyCreditPageTitle => 'Adadin da aka fi so';

  @override
  String get callButtonText => 'Kira';

  @override
  String get callingAppBarTitle => 'Kana kira';

  @override
  String get callingPageBluetoothOptionsText => 'Bluetooth';

  @override
  String get callingPageCreditErrorCardTitle => 'Katin kira ya kare';

  @override
  String get callingPagePhoneOptionsText => 'Waya';

  @override
  String get callingPageSpeakerOptionsText => 'Lasifikar sauti';

  @override
  String callingPageTimeLeft(String time) {
    return '  $time Saura';
  }

  @override
  String get callLogTypeAnswered => 'An amsa';

  @override
  String get callLogTypeBusy => 'Ana cikin waya';

  @override
  String get callLogTypeCancel => 'An fasa';

  @override
  String get callLogTypeIncoming => 'Kiran da ke shigowa';

  @override
  String get callLogTypeMissed => 'Kiran da aka rasa';

  @override
  String get callLogTypeOutgoing => 'Kiran da ke Fita';

  @override
  String get callLogTypeUnavailable => 'Babu ';

  @override
  String get callRatesAppBarTitle => 'Farashin kira';

  @override
  String get callTypeAnswered => 'Kiran da ke fita';

  @override
  String get callTypeBusy => 'Ana cikin waya';

  @override
  String get callTypeCancel => 'Kiran da aka fasa';

  @override
  String get callTypeIncoming => 'Kira mai shigowa';

  @override
  String get callTypeMissed => 'Kiran da baka dauka ba';

  @override
  String get callTypeOutgoing => 'Kira mai fita';

  @override
  String get callTypeUnavailable => 'Mai amfani baya nan';

  @override
  String get cancelButtonText => 'Soke';

  @override
  String get changeChannelButtonText => 'Canza Tashar';

  @override
  String get chatWithLiveAgentAppbarTitle => 'Wakilin kula';

  @override
  String get closeButtonText => 'Rufe';

  @override
  String get confirmationAppBarTitle => 'Tabbatarwa';

  @override
  String get confirmationFailedButtonText => 'Sake gwadawa';

  @override
  String get confirmationFailedContactSupportDescription => 'Idan kana da wasu tambayoyi ko buƙatar taimako, da fatan za a tuntuɓi ƙungiyar tallafin mu';

  @override
  String get confirmationFailedContactSupportText => 'Tuntuɓi tallafin mu';

  @override
  String get confirmationFailedDescription => 'Biyan kuɗin ka bai yi nasara ba. Sake gwadawa';

  @override
  String get confirmationFailedTitle => 'Biyan bai shiga ba';

  @override
  String get confirmationSuccessButtonText => 'An gama';

  @override
  String confirmationSuccessDescription(String amount) {
    return 'An kara $amount a asusun ka';
  }

  @override
  String get confirmationSuccessTitle => 'Biyan kuɗi ya yi nasara';

  @override
  String get contactsAllContactsTabText => 'Tuntuɓa';

  @override
  String contactsCallRateText(String min, String secs) {
    return ' $secs mintuna $min  sekondi';
  }

  @override
  String get contactsFavouriteCallsTabText => 'Ma fi so';

  @override
  String get contactsNoCallsEmptyMessage => 'Ba ka da wani kiran';

  @override
  String get contactsNoContactsButtonText => 'Duba lambobi';

  @override
  String get contactsSearchContactsPlaceholder => 'Bincika bayanan tuntuɓar';

  @override
  String get contactsSearchForContactsPlaceholder => 'Bincika bayanan tuntuɓar';

  @override
  String get couponAppliedButtonText => 'An nema';

  @override
  String get couponApplyButtonText => 'nema';

  @override
  String get credit => 'Kiredit';

  @override
  String get deleteAccountWarning => 'Aikin da kake shirin yi ba zai iya dawo baya ba. Da fatan za a tabbatar da cewa za ka goge asusunka\n\n \n';

  @override
  String get dialerAppBarTitle => 'mabugin waya';

  @override
  String get dialerCopyActionText => 'Kwafi';

  @override
  String get dialerErrorInvalidNumber => 'lamabar baata aiki';

  @override
  String get dialerErrorWithoutCountryCode => 'kara lambar ƙasa (misali +1)';

  @override
  String get dialerKeypadABC => 'ABC';

  @override
  String get dialerKeypadDEF => 'DEF';

  @override
  String get dialerKeypadGHI => 'GHI';

  @override
  String get dialerKeypadJKL => 'JKL';

  @override
  String get dialerKeypadMNO => 'MNO';

  @override
  String get dialerKeypadPQRS => 'PQRS';

  @override
  String get dialerKeypadTUV => 'TUV';

  @override
  String get dialerKeypadWXYZ => 'WXYZ';

  @override
  String get dialerMuteStatusMuted => 'An kashe sauti';

  @override
  String get dialerMuteStatusUnmuted => 'An kunna sauti';

  @override
  String get dialerPasteActionText => 'liƙa';

  @override
  String get dialerSearchResultsHeader => 'Sakamako';

  @override
  String get dialerSearchResultsNoResults => 'Babu sakamako da\'aka samu ';

  @override
  String get dialerStatusConnected => '\nKiran ya hadu';

  @override
  String get dialerStatusConnecting => 'Ana kira\n';

  @override
  String get dialerStatusEnded => 'An gama kiran';

  @override
  String get dialerStatusError => 'Kuskure Ya Faru';

  @override
  String get dialerStatusFailedToConnect => 'Kiran bai hadu ba';

  @override
  String get dialerStatusHold => '\nAn dakatar';

  @override
  String get dialerStatusInitial => 'Ana kira';

  @override
  String get dialerStatusInitiating => 'Ana kira';

  @override
  String get dialerStatusRinging => '\nAna Kira';

  @override
  String get dialerStatusUnknown => 'Ba a sani ba';

  @override
  String get emptyContactList => 'Babu jerin lambobin tuntuɓarka';

  @override
  String get emptyFavouriteContactList => 'Babu jerin lambobin tuntuɓar da kake so';

  @override
  String get enterCouponOptionalPlaceholder => 'Shigar da kupon na ragi (Zabi ne)';

  @override
  String get enterOtpPageAppBarSubtitle => 'Mataki na biyu a cikin uku';

  @override
  String get enterOtpPageAppBarTitle => 'Farawa';

  @override
  String get enterOtpPageEditButton => '.Nambar WhatsApp ba daidai ba';

  @override
  String get enterOtpPageErrorMessage => 'Code din ba daidai ba';

  @override
  String get enterOtpPagePhoneLabel => 'Shigar da lambar tabbatarwa';

  @override
  String enterOtpPagePhoneLabelDescription(String phoneNumber) {
    return 'An aika lamba mai lambobi hudu zuwa lambar WhatsApp ɗinka $phoneNumber';
  }

  @override
  String get enterOtpPageResendOtpButton => 'Sake tura code';

  @override
  String enterOtpPageResendOtpRetries(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count Ƙarin gwaje-gwaje',
      one: 'Gwaji ne karshe d a ye rage',
      zero: 'Babu gwajin da ya rage',
    );
    return '$_temp0';
  }

  @override
  String get enterOtpPageResendOtpRetriesError => 'Ba za ka iya samun lambar tantancewa a wannan lambar ta WhatsApp ba nan da awa ashirin da hudu masu zuwa';

  @override
  String get enterOtpPageSubmitButton => 'Tabbatar da OTP';

  @override
  String get enterVoucherCodeLabel => 'Shigar da lambar kati\n\n';

  @override
  String get free => '\nKyauta';

  @override
  String get freeCreditAppbarTitle => 'Katin Kyauta';

  @override
  String get freeCreditPageContent_1 => 'Raba lambar  gayyatar ka da abokanka da iyalanka';

  @override
  String get freeCreditPageContent_2 => '\nSu sauke FroggyTalk kuma suyi rijista tare da lambar gayyatar ka ';

  @override
  String freeCreditPageContent_3(String amount) {
    return 'Za ka samu $amount lokacin da wanda ka gayyata ya sayi katin farko';
  }

  @override
  String freeCreditPageHeadingText(String amount) {
    return '$amount Gaayyaci abokinka ka samu  ';
  }

  @override
  String get freeCreditPageShareReferralLinkButtonText => 'Gayyatar abokai zuwa FroggyTalk';

  @override
  String freeCreditPageSubHeadingText(String amount) {
    return 'Abokan da na gayyata ($amount)';
  }

  @override
  String get froggytalkCustomerLabel => 'Abokin ciniki na Froggytalk';

  @override
  String get helpCenterAppbarTitle => 'Cibiyar Taimako';

  @override
  String get helpCenterPageMenu_1 => 'Yi Korafi ko Ba da Shawara';

  @override
  String get helpCenterPageMenu_2 => 'Yi taɗi tare da wakilin kulawa';

  @override
  String get helpCenterPageMenu_3 => 'Tambayoyin da ake yawan yi';

  @override
  String get homeNavigationBarText => 'Gida';

  @override
  String get internetConnectionAlertTextError => 'Kash! Ka rasa haɗin yanar gizo';

  @override
  String get internetConnectionAlertTextSuccess => 'An shirya! An haɗu da Intanet ɗin ka';

  @override
  String get invalidInternationalPhoneFormat => 'Tsarin Lambar Waya ta Ƙasashen Duniya mara inganci\n\n';

  @override
  String get invalidPhoneNumber => 'Da fatan za a shigar da ingantacciyar lambar waya';

  @override
  String get keypadNavigationBarText => 'Maballin Waya';

  @override
  String get landlineText => 'Wayar gida';

  @override
  String get loadVoucherCardButtonText => 'Loda kati';

  @override
  String get loadVoucherCardErrorText => 'lambar ba dai dai ba, sake gwadawa';

  @override
  String get loadVoucherCardLabelText => 'Shigar da lambar kati mai lamba goma';

  @override
  String get loadVoucherCardTitle => 'Sa kati';

  @override
  String get loadVoucherCodeTitle => 'Loda Lambar kati';

  @override
  String get loginPageAppBarSubtitle => 'Mataki na daya a cikin uku';

  @override
  String get loginPageAppBarTitle => 'Farawa';

  @override
  String get loginPageCheckboxLabel => 'An gayyace ni da wani lamba';

  @override
  String get loginPageErrorMessage => 'Lambar nan ba ta kan WhatsApp';

  @override
  String get loginPagePhoneLabel => 'Shigar da lambar ka ta WhatsApp';

  @override
  String get loginPagePhoneLabelDescription => 'Za a tura ma OTP ta WhatsApp';

  @override
  String get loginPagePhoneNumberError => 'Lambar wayarka ba ta cika ba, do patan za ka cika ta';

  @override
  String get loginPageReferralLabel => 'Shigar da lambar gayyatar abokai';

  @override
  String get loginPageSubmitButton => 'Neman tabbatarwa';

  @override
  String minsOrMin(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count mintuna',
      one: '1 minti',
      zero: '',
    );
    return '$_temp0';
  }

  @override
  String get mobileText => 'Wayar salula';

  @override
  String get moreAppbarTitle => 'Asusu';

  @override
  String get moreNavigationBarText => 'Ƙarin abubuwa';

  @override
  String get morePageAccountBalanceCardTitle => 'Ma\'auni';

  @override
  String get morePageAppVersionMenuText => 'Sigar manhaja';

  @override
  String get morePageCallRatesMenuText => 'Farashin kira';

  @override
  String get morePageHelpCenterMenuText => 'Cibiyar Taimako';

  @override
  String get morePageLanguageMenuText => 'Yare';

  @override
  String get morePageLoadVoucherMenuText => 'Saka kati';

  @override
  String get morePageLogoutMenuText => 'Fita';

  @override
  String get morePageProfileMenuText => 'Bayanan Kai';

  @override
  String get morePageRadioMenuText => 'Rediyo';

  @override
  String get morePageReferralCodeCardButtonText => 'Karin bayani';

  @override
  String morePageReferralCodeCardContent(int numberOfPeople) {
    return '   $numberOfPeople\nmutane sun yi amfani da lambar gayyatar wa';
  }

  @override
  String morePageReferralCodeCardDescription(String amount) {
    return 'Samu $amount in ka gayyace Iyali da abokai zuwa FroggyTalk';
  }

  @override
  String get morePageReferralCodeCardTitle => 'Lambar gayyatar abokai';

  @override
  String get noBuyCreditOptionsAvailable => 'Babu zaɓuɓɓukan siyan kati da ake da su';

  @override
  String get noFavoriteContactsMessage => 'Babu Lambobin Da Aka Fi So';

  @override
  String get noNotificationsYet => 'Babu sanarwa tukunan';

  @override
  String get notEnoughCreditMessage => 'Baa ka da isasshen kiredit';

  @override
  String get notification => 'Sanarwa';

  @override
  String get notificationsAppBarTitle => 'Sanarwa';

  @override
  String get notificationsEmptyMessage => 'Ba ka da sanarwa';

  @override
  String get notificationSettingsAppBarTitle => 'Saitunan sanarwa';

  @override
  String get notificationSettingsAutoDeleteNotificationText => 'Goge sanarwar ta atomatik';

  @override
  String get notificationSettingsNotificationSoundText => 'Sautin sanarwa';

  @override
  String get notificationsTabAllText => 'Duka';

  @override
  String get notificationsTabUnreadText => 'Ba a karanta ba';

  @override
  String notificationsTimeAgo(int time, String duration) {
    String _temp0 = intl.Intl.pluralLogic(
      time,
      locale: localeName,
      other: '${duration}s',
      one: '$duration',
    );
    return '$time $_temp0 a baya';
  }

  @override
  String get okButtonText => '\nToh';

  @override
  String get onboardingPageFooterAndText => 'da';

  @override
  String get onboardingPageFooterPrivacy => 'Manufar Sirri';

  @override
  String get onboardingPageFooterTermsConditionsText => 'Ka\'idojin amfani da FroggyTalk';

  @override
  String get onboardingPageFooterText => 'In ka fara ka yadda da';

  @override
  String get onboardingPageSliderHeader1 => 'Harsuna daban-daban';

  @override
  String get onboardingPageSliderHeader2 => 'Yi kira zuwa kowane wayar hannu ko layin ƙasa a ko\'ina cikin duniya';

  @override
  String get onboardingPageSliderHeader3 => 'Biya da sauki';

  @override
  String get onboardingPageSliderText1 => 'FroggyTalk yana ba ka damar zaɓar harshen da ka fi so, misali Tigrinya, Amharic, Hausa da sauransu';

  @override
  String get onboardingPageSliderText2 => 'Yi kira zuwa kowane wayar hannu ko layin ƙasa a ko\'ina cikin duniya.\nMai karɓa ba ya buƙatar wayar salula ko haɗin intanet';

  @override
  String get onboardingPageSliderText3 => 'Sayi kiredit na kira a cikin kudin kasarka tare da hanyar biyan da ka fi so.';

  @override
  String get onboardingPageSubmitButtonText => 'Fara';

  @override
  String get outOfCreditLabel => 'Babu kiredit';

  @override
  String get paymentFailureAppBarTitle => 'Qasa';

  @override
  String get paymentFailureHomeButton => 'Gida';

  @override
  String paymentFailureMessage(String amount) {
    return 'Biyan kuɗin ka na $amount bai yi nasara ba';
  }

  @override
  String get paymentFailureTitle => 'Biyan Kuɗi Bai Yi Nasara Ba';

  @override
  String get paymentFailureTryAgainButton => 'Sake gwadawa';

  @override
  String get paymentOptionsAppBarTitle => 'Hanyoyin biyan kuɗi';

  @override
  String paymentOptionsAutoCreditDescription(String amount, String minAmount) {
    return 'Za mu sanya wa asusunka da $amount idan ragowar kuɗinka ya yi ƙasa da $minAmount';
  }

  @override
  String get paymentOptionsAutoCreditTitle => 'Saka kati automatik';

  @override
  String get paymentOptionsSelectPaymentMethod => 'Zaɓi hanyar biyan kuɗi da ka fi so';

  @override
  String get paymentOptionsSummaryAmountToCreditText => 'Kudin da za ka samu a asusun ka';

  @override
  String get paymentOptionsSummaryDiscountButtonText => 'Aiwatar';

  @override
  String get paymentOptionsSummaryDiscountPlaceholder => 'Shigar da lambar samun ragi';

  @override
  String get paymentOptionsSummaryDiscountText => 'Ragi';

  @override
  String get paymentOptionsSummaryTotalPaymentText => 'Gabadayan kuɗin da za ka biya';

  @override
  String get paymentOptionsSummaryVatFeesText => 'Haraji';

  @override
  String get paymentSummaryAmountToCreditLabel => 'Adadin kiredit da zai shiga ';

  @override
  String get paymentSummaryDiscountLabel => 'Ragi';

  @override
  String get paymentSummaryTotalLabel => 'Gabadaya abinda za\'a biya';

  @override
  String get paymentSummaryVatFeesLabel => 'Haraji + Kudin Ayyuka';

  @override
  String perMinRate(String rate) {
    return '$rate/mintuna';
  }

  @override
  String get perMinRateSingle => 'minti/\n';

  @override
  String perMinuteRate(String rate) {
    return '$rate/mintuna';
  }

  @override
  String perMinuteSlashLabel(String min) {
    return '$min/mintuna';
  }

  @override
  String get permissionButtonAccept => 'Bada damar shiga';

  @override
  String get permissionButtonSkip => 'Tsallaka';

  @override
  String get permissionForCameraTitle => 'Muna bukatar izini don daukar hotuna da bidiyo';

  @override
  String get permissionForContactListTitle => 'Bawa FroggyTalk damar shiga bayanar tuntubar ka';

  @override
  String get permissionForMicrophoneTitle => 'Bawa FroggyTalk damar amfani da makirufo naka don sarrafa kiran';

  @override
  String get permissionForNotificationTitle => 'Bawa FroggyTalk damar tura ma sako';

  @override
  String get permissionForStorageTitle => 'Muna bukatar izini don adana bayanan wucin gadi a kan na\'urarka';

  @override
  String get permissionPermanentlyDeniedMessage => 'Izini an hana shi dindindin, Da fatan za a kunna shi a cikin saitunan app';

  @override
  String get permissionDeniedMessage => 'An ki izinin, Da fatan za a kunna shi a cikin saitunan manhaja';

  @override
  String perM_Rate(String rate) {
    return '$rate/mintuna';
  }

  @override
  String get phoneNumberStartRule => 'Da fatan za a fara shigar da lambar da + ko 00';

  @override
  String get proceedToPaymentButtonText => 'Cigaba';

  @override
  String get profileAppbarTitle => 'Bayanan kai';

  @override
  String get profileDeleteAccountButtonText => 'Share asusun ka';

  @override
  String get profileDeleteAccountCardContent => 'Da fatan za ka tabbatar da cewa kana son a share asusun ka. Za ka rasa duk wani kudi da ke cikin asusun ka';

  @override
  String get profileDeleteAccountCardTitle => 'Share asusun ka';

  @override
  String get profileLabel => 'Bayanan Ka\n';

  @override
  String get profileLabelChangeLocation => 'Canza kasar da kake';

  @override
  String get profileLabelEmailAddress => 'Shigar da adireshin imel';

  @override
  String get profileLabelFullName => 'Shigar da cikakken suna';

  @override
  String get profileUpdatedSuccessfullyMessage => 'An sabunta bayanan ka cikin nasara';

  @override
  String get profileUpdateFailedMessage => '\nSabunta bayanan ka bai yi nasara ba ';

  @override
  String get quickAdvertSubtitle => 'Siya kati wa yan uwa ko ina a duniya';

  @override
  String get quickAdvertTitle => 'Tura kati zuwa kasashe fiye da dari da arba\'in da uku';

  @override
  String get radioAppBarTitle => 'Rediyo';

  @override
  String get radioComingSoonText => 'Rediyo na zuwa nan ba da jimawa ba !!!';

  @override
  String get radioPageChooseChannelButtonText => 'Zaɓi Tasha';

  @override
  String get radioPageNoChannelsText => 'Babu tasha';

  @override
  String get radioPageSearchPlaceholder => 'Zaɓi Tasha';

  @override
  String get ratesNavigationBarText => 'Farshin kira';

  @override
  String get recentCallsText => 'Kira';

  @override
  String get rechargeButtonText => 'Caje kati';

  @override
  String get referAndEarn => 'Gayyaci wani ka samu kyauta';

  @override
  String get referralCardButtonText => 'Kyautar kiredit';

  @override
  String referralCardDescription1(String percentageAmount) {
    return 'Gayyaci wani kuma ka sami $percentageAmount kyauta.';
  }

  @override
  String referralCardDescription2(int numberOfPeople, num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$numberOfPeople sun yi amfani da lambar gayyatar ka.',
      one: 'Mutum daya ya yi amfani da lambar gayyatar ka.',
      zero: 'Babu wanda ya yi amfani da lambar gayyatar ka.',
    );
    return '$_temp0';
  }

  @override
  String get referralCardTitle => 'Lambar gayyatar abokai';

  @override
  String get referralCodeOnCopyActionResponse => 'FroggyTalk ta liƙa zuwa allo';

  @override
  String get saveAndProceedToPaymentButtonText => 'Ajiye ka ci gaba zuwa biyan kudi\n';

  @override
  String get saveChangesButtonText => 'Ajiye canje-canje da aka yi';

  @override
  String get savedSuccessfullyMessage => 'An ajiye cikin nasara';

  @override
  String get searchCountryPlaceholder => 'Bincika kasa';

  @override
  String get searchFavouriteContactMessage => 'Nemo lambobin da ka fi so';

  @override
  String get searchForCountryNoResults => 'Ba a sami sakamako ba';

  @override
  String get searchForCountryPlaceholder => 'Bincike sunan kasa';

  @override
  String get searchRecentCallsMessage => 'Nemo kiran da aka yi kwanan nan';

  @override
  String secsOrSec(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count sakanni',
      one: '1 sakan',
      zero: '',
    );
    return '$_temp0';
  }

  @override
  String get selectCountryPlaceholder => 'Zabi ƙasa';

  @override
  String get sendCreditButtonText => 'Tura kati';

  @override
  String get settings => 'Saituna';

  @override
  String shareReferralText(String appLink, String referralCode) {
    return 'Sauke FroggyTalk App $appLink kuma yi rijista tare da lambar gayyata nan $referralCode. Yana da mafi kyawun farashi da ƙwarewar kira.';
  }

  @override
  String get somethingWentWrongMessage => 'Wani abu ya faru ba daidai ba. Da fatan za a sake gwadawa.';

  @override
  String get unread => 'Ba a karanta ba';

  @override
  String get updateProfileButtonText => 'Sabunta bayanan martaba';

  @override
  String get upgradeDialogButtonNo => 'Ba Yanzu Ba';

  @override
  String get upgradeDialogButtonYes => 'Sabunta';

  @override
  String get upgradeDialogMessage => 'Sabon sigar FroggyTalk tana samuwa\n?Kana so ka sabunta yanzu';

  @override
  String get upgradeDialogTitle => 'Sabunta tana samuwa';

  @override
  String get validationCouponCodeIncomplete => 'lambar rangwame dake kan takardar ba ta cika ba ko kuma ba ta aiki';

  @override
  String get validationCouponInvalid => 'Da fatan za a shigar da ingantaccen lamba';

  @override
  String get validationEmailInvalid => 'Adireshin imel mara inganci.';

  @override
  String validationFieldIsRequired(String fieldName) {
    return '  Ana bukata $fieldName ';
  }

  @override
  String validationMinLengthError(String fieldName, int minLength) {
    return '$fieldName dole ne ya zama aƙalla tsawon haruffa $minLength';
  }

  @override
  String get validationPhoneNumberIncomplete => 'Lambar waya ba ta cika ba.';

  @override
  String get viewButtonText => 'Duba';

  @override
  String get viewContactDetailAppBarTitle => 'Bayanan tuntuɓar juna';

  @override
  String get voucherLoadedSuccessMessage => 'An loda kati cikin nasara';

  @override
  String get securedByStripe => 'An Karɓa ta Stripe';

  @override
  String get nextTrack => 'Karin waƙa';

  @override
  String get stopRadio => 'Tsayar da rediyo';

  @override
  String get playRadio => 'Kunna rediyo';

  @override
  String get previousTrack => 'Waƙar da ta wuce';

  @override
  String get channel => 'Tashar';

  @override
  String get allChannels => 'Duka tasoshi';

  @override
  String get acceptButtonText => 'Karɓa';

  @override
  String get declineButtonText => 'Ƙi';

  @override
  String allowShareInfoWithRadio(String radioName) {
    return 'Bamu damar raba bayanan ka da $radioName';
  }

  @override
  String get nowPlaying => 'Ana Wasa';

  @override
  String get checkBackLaterText => 'Da fatan za ka sake duba daga baya';

  @override
  String get refreshText => 'Sabunta';

  @override
  String get noRadioStationsAvailableText => 'Babu tashar rediyo da ke samuwa';

  @override
  String get tryAgainText => 'Sake gwadawa';

  @override
  String get unknownErrorText => 'Wani kuskure ya faru';

  @override
  String get errorLoadingRadioStationsText => 'Kuskure yayi sanadiyyar samar da tashar rediyo';

  @override
  String get loadingRadioStationsText => 'Ana samar da tashar rediyo...';

  @override
  String get helpCenterPageMenu_4 => 'WhatsApp';

  @override
  String validationLengthError(String fieldName, int minLength, int maxLength) {
    return '$fieldName ya kamata ya kasance tsakanin haruffa $minLength zuwa $maxLength';
  }

  @override
  String get chooseLanguage => 'Choose Language';

  @override
  String get selectPreferredLanguage => 'Select your preferred language';

  @override
  String get cancel => 'Cancel';

  @override
  String get apply => 'Apply';

  @override
  String get languageChangeError => 'Failed to change language. Please try again.';

  @override
  String get noLanguagesAvailable => 'No languages available';

  @override
  String get languageSetupPageAppBarSubtitle => 'Mataki na uku a cikin uku';

  @override
  String welcomeBackText(String firstName) {
    return 'Barka da dawowa, $firstName';
  }

  @override
  String get froggyTalkLovesYou => 'FroggyTalk na son ku.';

  @override
  String get updateLanguagePrompt => 'Da fatan za a danna maballin da ke ƙasa don sabunta yaren ku';

  @override
  String get updateYourLanguage => 'Sabunta Yaren Ka.';

  @override
  String get inAppPurchaseLabel => 'Siya kati a cikin App';

  @override
  String get restorePurchases => 'Restore Purchases';

  @override
  String get storePrice => 'Farashi';

  @override
  String timeLeftModalTitle(String days) {
    return ' $days Saura rana';
  }

  @override
  String timeLeftModalMessage(String days, int count) {
    return 'Saura rana ${days}a sami wanda zai ci kudin. Gayyaci mutane$count ka ci kudin';
  }

  @override
  String get confirm => 'Ka tabbatar';

  @override
  String get languageSelectionDisclaimer => 'Za ka iya zabi wani yaren a ';

  @override
  String get activateAutoCredit => 'Kunna caja kati ta automatik';

  @override
  String get autoAccountCreditHelp => 'za a caje katin ka a saka shi cikin ma\'auni a duk lokacin da balanse ya faɗi ƙasa';

  @override
  String get avoidCallDisruption => 'don gujewa katsewar waya';

  @override
  String cashPrizeAmount(String currencySymbol, String amount) {
    return ' KYAUTAR kudi$currencySymbol$amount';
  }

  @override
  String monthlyReferralBoard(String month) {
    return 'Masu jagorantar talla a watar $month ';
  }

  @override
  String get termsAndConditionsApply => 'Za a yi amfani da sharuɗɗa da sharuɗɗa';

  @override
  String get getNow => 'Fara samu yanzu';

  @override
  String get payWithGooglePay => 'Google Pay';

  @override
  String get payWithApplePay => 'Apple Pay';

  @override
  String paymentSummaryPlayStoreFeeLabel(String percentage) {
    return 'Laadar $percentage% da Haraji+';
  }

  @override
  String get failedToLoadLeaderboard => 'An kasa loda jerin masu nasara';

  @override
  String get retry => 'Sake gwadawa';

  @override
  String get googlePay => 'Google Pay';

  @override
  String appStoreCost(String value) {
    return '+ $value Kudin AppStore';
  }

  @override
  String get notification_call_reminder_title => 'Call Reminder';

  @override
  String notification_call_reminder_body(String contact) {
    return 'Don\'t forget to call $contact';
  }

  @override
  String get notification_promotion_title => 'Special Offer';

  @override
  String notification_promotion_body(String offer, String description) {
    return 'New offer: $offer - $description';
  }

  @override
  String get notification_security_alert_title => 'Security Alert';

  @override
  String notification_security_alert_body(String alertType, String device) {
    return 'Security alert: $alertType detected on $device';
  }

  @override
  String get notification_daily_reminder_title => 'Daily Reminder';

  @override
  String notification_daily_reminder_body(String reminderType) {
    return 'Don\'t forget: $reminderType';
  }
}
