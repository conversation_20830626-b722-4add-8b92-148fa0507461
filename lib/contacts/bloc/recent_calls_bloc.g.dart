// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recent_calls_bloc.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RecentCallsStateImpl _$$RecentCallsStateImplFromJson(
        Map<String, dynamic> json) =>
    _$RecentCallsStateImpl(
      recentCalls: (json['recentCalls'] as List<dynamic>?)
              ?.map((e) =>
                  RecentCallsResponseModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      filteredRecentCalls: (json['filteredRecentCalls'] as List<dynamic>?)
              ?.map((e) =>
                  RecentCallsResponseModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      status: $enumDecodeNullable(_$RecentCallsStatusEnumMap, json['status']) ??
          RecentCallsStatus.initial,
      message: json['message'] as String?,
      searchTerm: json['searchTerm'] as String?,
    );

const _$$RecentCallsStateImplFieldMap = <String, String>{
  'recentCalls': 'recentCalls',
  'filteredRecentCalls': 'filteredRecentCalls',
  'status': 'status',
  'message': 'message',
  'searchTerm': 'searchTerm',
};

Map<String, dynamic> _$$RecentCallsStateImplToJson(
        _$RecentCallsStateImpl instance) =>
    <String, dynamic>{
      'recentCalls': instance.recentCalls.map((e) => e.toJson()).toList(),
      'filteredRecentCalls':
          instance.filteredRecentCalls.map((e) => e.toJson()).toList(),
      'status': _$RecentCallsStatusEnumMap[instance.status]!,
      if (instance.message case final value?) 'message': value,
      if (instance.searchTerm case final value?) 'searchTerm': value,
    };

const _$RecentCallsStatusEnumMap = {
  RecentCallsStatus.initial: 'initial',
  RecentCallsStatus.inProgress: 'inProgress',
  RecentCallsStatus.success: 'success',
  RecentCallsStatus.failure: 'failure',
};
