import 'dart:async';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:rxdart/rxdart.dart';

part 'recent_calls_bloc.freezed.dart';
part 'recent_calls_bloc.g.dart';
part 'recent_calls_event.dart';
part 'recent_calls_state.dart';

// Define the debounce transformer
EventTransformer<RecentCallsEvent> _debounce<RecentCallsEvent>(
  Duration duration,
) {
  return (events, mapper) => events.debounceTime(duration).switchMap(mapper);
}

class RecentCallsBloc extends HydratedBloc<RecentCallsEvent, RecentCallsState> {
  RecentCallsBloc() : super(RecentCallsState.initial()) {
    on<_Started>(_onStarted);
    on<_Searched>(
      _onSearched,
      transformer: _debounce(const Duration(milliseconds: 300)),
    );
    on<_Initial>(_onInitial);
  }

  @override
  RecentCallsState? fromJson(Map<String, dynamic> json) {
    try {
      return RecentCallsState.fromJson(json);
    } catch (_) {
      return RecentCallsState.initial();
    }
  }

  @override
  Map<String, dynamic>? toJson(RecentCallsState state) {
    try {
      return {
        'recentCalls': state.recentCalls,
        'filteredRecentCalls': state.filteredRecentCalls,
        // 'status': state.status,
        // 'searchedCalls': state.searchedCalls,
        // 'errorMessage': state.errorMessage,
      };
      // return state.toJson();
    } catch (_) {
      return null;
    }
  }

  final _recentCallsRepository = RecentCallsRepository();

  FutureOr<void> _onStarted(
    _Started event,
    Emitter<RecentCallsState> emit,
  ) async {
    emit(state.setLoading());

    final response = await _recentCallsRepository.execute();

    if (response.isRight) {
      final recentCalls =
          response.right.data?.map((e) => e.attributes!).toList() ?? [];
      await Future<void>.delayed(const Duration(milliseconds: 500));
      emit(state.setSuccess(recentCalls));
    } else {
      emit(state.setFailure(response.left.message));
    }
  }

  Future<void> _onSearched(
    _Searched event,
    Emitter<RecentCallsState> emit,
  ) async {
    final query = event.value ?? '';

    // Don't emit loading state for empty queries
    if (query.isNotEmpty) {
      emit(state.setLoading());
    }

    // Add debounce to prevent rapid updates
    await Future<void>.delayed(const Duration(milliseconds: 300));

    // Handle empty query by showing all contacts
    if (query.isEmpty) {
      emit(state.setSuccess(state.recentCalls));
      return;
    }

    // Perform search for non-empty queries
    emit(state.searchIn(searchTerm: query));
  }

  FutureOr<void> _onInitial(
    _Initial event,
    Emitter<RecentCallsState> emit,
  ) {
    emit(RecentCallsState.initial());
  }
}
