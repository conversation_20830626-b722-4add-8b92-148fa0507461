import 'dart:async';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:intl/intl.dart';

part 'recent_call_item_bloc.freezed.dart';
// part 'recent_call_item_bloc.g.dart';
part 'recent_call_item_event.dart';
part 'recent_call_item_state.dart';

class RecentCallItemBloc
    extends Bloc<RecentCallItemEvent, RecentCallItemState> {
  RecentCallItemBloc({
    required PhoneNumberService phoneNumberService,
    required this.recentCallItem,
  })  : _phoneNumberService = phoneNumberService,
        super(RecentCallItemState.initial()) {
    on<_Started>(_onStarted);
  }

  // @override
  // RecentCallItemState? fromJson(Map<String, dynamic> json) {
  //   try {
  //     return RecentCallItemState.fromJson(json);
  //   } catch (_) {
  //     return RecentCallItemState.initial();
  //   }
  // }

  // @override
  // Map<String, dynamic>? toJson(RecentCallItemState state) {
  //   try {
  //     // return state.toJson();
  //     return {
  //       'lengthOfCall': state.lengthOfCall,
  //       'phoneNumber': state.phoneNumber,
  //       'createdAt': state.createdAt,
  //       'formattedPhoneNumber': state.formattedPhoneNumber,
  //       'callStatus': state.callStatus,
  //       'countryCode': state.countryCode,
  //       'sessionBIll': state.sessionBIll,
  //     };
  //   } catch (_) {
  //     return null;
  //   }
  // }

  final PhoneNumberService _phoneNumberService;
  final RecentCallsResponseModel recentCallItem;

  FutureOr<void> _onStarted(_Started event, Emitter<RecentCallItemState> emit) {
    final phoneNumber = "+${recentCallItem.calledNumber ?? ''}";
    emit(
      state.copyWith(
        phoneNumber: phoneNumber,
        formattedPhoneNumber: _phoneNumberService.format(phoneNumber) ?? '',
        lengthOfCall: recentCallItem.duration ?? '',
        createdAt: recentCallItem.startTime ?? '',
        callStatus: recentCallItem.status,
        countryCode: _phoneNumberService.getCountryCode(phoneNumber),
        sessionBIll: recentCallItem.sessionBill,
      ),
    );
  }
}
