part of 'contact_list_item_bloc.dart';

@freezed
class ContactListItemState with _$ContactListItemState {
  factory ContactListItemState({
    required String fullName,
    required String phoneNumber,
    String? formattedPhoneNumber,
    CallRatesAmountModel? callRate,
    @Default(false) bool isCallRateLoading,
    @Default(false) bool isFavourited,
    @Default(false) bool hasReloadedFavs,
    String? callRateErrorMessage,
    String? countryCode,
  }) = _ContactListItemState;

  factory ContactListItemState.fromJson(Map<String, dynamic> json) =>
      _$ContactListItemStateFromJson(json);

  factory ContactListItemState.initial() => ContactListItemState(
        phoneNumber: '',
        fullName: '',
      );

  ContactListItemState._();

  String? get countryFlag {
    if (countryCode == null) {
      return null;
    }

    return 'flags/${(countryCode ?? "nl").toLowerCase()}.svg';
  }
}
