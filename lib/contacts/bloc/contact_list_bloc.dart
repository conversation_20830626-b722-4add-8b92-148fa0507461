import 'dart:async';

import 'package:fast_contacts/fast_contacts.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:utils/utils.dart';

part 'contact_list_bloc.freezed.dart';
// part 'contact_list_bloc.g.dart';
part 'contact_list_event.dart';
part 'contact_list_state.dart';

class ContactListBloc extends Bloc<ContactListEvent, ContactListState> {
  ContactListBloc(
    ContactListService contactsService,
  )   : _contactsService = contactsService,
        super(ContactListState.initial()) {
    on<_Started>(_onStarted);
    on<_UpdatedSearchField>(
      _onSearchFieldUpdating,
      transformer: debounce(duration: const Duration(microseconds: 300)),
    );
    on<_Resetted>(_onReset);
  }

  final ContactListService _contactsService;

  FutureOr<void> _onReset(_Resetted event, Emitter<ContactListState> emit) {
    emit(ContactListState.initial());
  }

  /// Isolate computation function that must be declared at top level
  // ignore: unused_element
  Future<Map<String, List<Contact>>> _groupAndSortContacts(
    List<Contact> contacts,
  ) async {
    final groupedContacts = <String, List<Contact>>{};

    // Sort contacts first
    contacts.sort((a, b) => a.displayName.compareTo(b.displayName));

    // Group by first letter
    for (final contact in contacts) {
      final firstLetter = contact.displayName.toUpperCase().substring(0, 1);
      groupedContacts.putIfAbsent(firstLetter, () => []).add(contact);
    }

    return groupedContacts;
  }

  FutureOr<void> _onStarted(
    _Started event,
    Emitter<ContactListState> emit,
  ) async {
    emit(state.setLoading());

    final contacts = _contactsService.loadContacts(
      requestForPermission: event.requestForPermission,
    );

    // // Create isolate for heavy computation
    // final groupedContacts = await compute(
    //   _groupAndSortContacts,
    //   contacts,
    // );

    emit(state.setSuccess(contacts));
  }

  FutureOr<void> _onSearchFieldUpdating(
    _UpdatedSearchField event,
    Emitter<ContactListState> emit,
  ) async {
    final query = event.value ?? '';

    // Don't emit loading state for empty queries
    if (query.isNotEmpty) {
      emit(state.setLoading());
    }

    // Add debounce to prevent rapid updates
    await Future<void>.delayed(const Duration(milliseconds: 300));

    // Handle empty query by showing all contacts
    if (query.isEmpty) {
      emit(state.setSuccess(state.contacts));
      return;
    }

    // Perform search for non-empty queries
    emit(state.searchIn(searchTerm: query));
  }
}
