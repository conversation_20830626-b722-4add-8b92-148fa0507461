part of 'favourite_contacts_bloc.dart';

@freezed
class FavouriteContactsEvent with _$FavouriteContactsEvent {
  const factory FavouriteContactsEvent.started() = _Started;

  const factory FavouriteContactsEvent.reset() = _Initial;

  const factory FavouriteContactsEvent.updatedSearchField({
    String? value,
  }) = _SearchedFavouriteContact;

  const factory FavouriteContactsEvent.added({
    required String phoneNumber,
    required String name,
    String? id,
    String? countryCode,
  }) = _AddedFavouriteContact;

  const factory FavouriteContactsEvent.deleted({
    required String phoneNumber,
  }) = _DeletedFavouriteContact;
}
