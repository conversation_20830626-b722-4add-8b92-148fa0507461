part of 'contact_list_bloc.dart';

@freezed
class ContactListEvent with _$ContactListEvent {
  const factory ContactListEvent.started({
    @Default(false) bool requestForPermission,
  }) = _Started;

  const factory ContactListEvent.reset() = _Resetted;

  const factory ContactListEvent.updatedSearchField({String? value}) =
      _UpdatedSearchField;

  const factory ContactListEvent.fetchedCallRate({
    required String countryCode,
  }) = _FetchedCallRate;
}
