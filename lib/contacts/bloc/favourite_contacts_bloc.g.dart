// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'favourite_contacts_bloc.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$FavouriteContactsStateImpl _$$FavouriteContactsStateImplFromJson(
        Map<String, dynamic> json) =>
    _$FavouriteContactsStateImpl(
      favouriteContacts: (json['favouriteContacts'] as List<dynamic>?)
              ?.map((e) => FavouriteContactsResponseModel.fromJson(
                  e as Map<String, dynamic>))
              .toList() ??
          const [],
      fiilteredFavouriteContacts:
          (json['fiilteredFavouriteContacts'] as List<dynamic>?)
                  ?.map((e) => FavouriteContactsResponseModel.fromJson(
                      e as Map<String, dynamic>))
                  .toList() ??
              const [],
      status: $enumDecodeNullable(
              _$FavouriteContactsStatusEnumMap, json['status']) ??
          FavouriteContactsStatus.initial,
      message: json['message'] as String?,
      searchTerm: json['searchTerm'] as String?,
    );

const _$$FavouriteContactsStateImplFieldMap = <String, String>{
  'favouriteContacts': 'favouriteContacts',
  'fiilteredFavouriteContacts': 'fiilteredFavouriteContacts',
  'status': 'status',
  'message': 'message',
  'searchTerm': 'searchTerm',
};

Map<String, dynamic> _$$FavouriteContactsStateImplToJson(
        _$FavouriteContactsStateImpl instance) =>
    <String, dynamic>{
      'favouriteContacts':
          instance.favouriteContacts.map((e) => e.toJson()).toList(),
      'fiilteredFavouriteContacts':
          instance.fiilteredFavouriteContacts.map((e) => e.toJson()).toList(),
      'status': _$FavouriteContactsStatusEnumMap[instance.status]!,
      if (instance.message case final value?) 'message': value,
      if (instance.searchTerm case final value?) 'searchTerm': value,
    };

const _$FavouriteContactsStatusEnumMap = {
  FavouriteContactsStatus.initial: 'initial',
  FavouriteContactsStatus.inProgress: 'inProgress',
  FavouriteContactsStatus.success: 'success',
  FavouriteContactsStatus.failure: 'failure',
};
