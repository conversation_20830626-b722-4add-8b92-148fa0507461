import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'contact_tabs_bloc.freezed.dart';
part 'contact_tabs_event.dart';
part 'contact_tabs_state.dart';

/// Manages the state of tabs in the contact list view
class ContactTabsBloc extends Bloc<ContactTabsEvent, ContactTabsState> {
  ContactTabsBloc() : super(ContactTabsState.initial()) {
    on<_TabChanged>(_onTabChanged);
  }

  FutureOr<void> _onTabChanged(
    _TabChanged event,
    Emitter<ContactTabsState> emit,
  ) {
    emit(state.copyWith(selectedIndex: event.index));
  }
}
