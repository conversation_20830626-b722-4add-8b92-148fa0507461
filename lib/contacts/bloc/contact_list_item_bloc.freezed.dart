// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'contact_list_item_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ContactListItemEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(dynamic item) started,
    required TResult Function() fetchedCallRates,
    required TResult Function() addedToFavourites,
    required TResult Function() removedFromFavourites,
    required TResult Function() toggleAsFavourites,
    required TResult Function() checkIfFavourited,
    required TResult Function() fetchedRecentCalls,
    required TResult Function() processPhoneNumber,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(dynamic item)? started,
    TResult? Function()? fetchedCallRates,
    TResult? Function()? addedToFavourites,
    TResult? Function()? removedFromFavourites,
    TResult? Function()? toggleAsFavourites,
    TResult? Function()? checkIfFavourited,
    TResult? Function()? fetchedRecentCalls,
    TResult? Function()? processPhoneNumber,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(dynamic item)? started,
    TResult Function()? fetchedCallRates,
    TResult Function()? addedToFavourites,
    TResult Function()? removedFromFavourites,
    TResult Function()? toggleAsFavourites,
    TResult Function()? checkIfFavourited,
    TResult Function()? fetchedRecentCalls,
    TResult Function()? processPhoneNumber,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_FetchedCallRates value) fetchedCallRates,
    required TResult Function(_AddedToFavourites value) addedToFavourites,
    required TResult Function(_RemovedFromFavourites value)
        removedFromFavourites,
    required TResult Function(_ToggleAsFavourites value) toggleAsFavourites,
    required TResult Function(_CheckIfFavourited value) checkIfFavourited,
    required TResult Function(_FetchedRecentCalls value) fetchedRecentCalls,
    required TResult Function(_ProcessPhoneNumber value) processPhoneNumber,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_FetchedCallRates value)? fetchedCallRates,
    TResult? Function(_AddedToFavourites value)? addedToFavourites,
    TResult? Function(_RemovedFromFavourites value)? removedFromFavourites,
    TResult? Function(_ToggleAsFavourites value)? toggleAsFavourites,
    TResult? Function(_CheckIfFavourited value)? checkIfFavourited,
    TResult? Function(_FetchedRecentCalls value)? fetchedRecentCalls,
    TResult? Function(_ProcessPhoneNumber value)? processPhoneNumber,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_FetchedCallRates value)? fetchedCallRates,
    TResult Function(_AddedToFavourites value)? addedToFavourites,
    TResult Function(_RemovedFromFavourites value)? removedFromFavourites,
    TResult Function(_ToggleAsFavourites value)? toggleAsFavourites,
    TResult Function(_CheckIfFavourited value)? checkIfFavourited,
    TResult Function(_FetchedRecentCalls value)? fetchedRecentCalls,
    TResult Function(_ProcessPhoneNumber value)? processPhoneNumber,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContactListItemEventCopyWith<$Res> {
  factory $ContactListItemEventCopyWith(ContactListItemEvent value,
          $Res Function(ContactListItemEvent) then) =
      _$ContactListItemEventCopyWithImpl<$Res, ContactListItemEvent>;
}

/// @nodoc
class _$ContactListItemEventCopyWithImpl<$Res,
        $Val extends ContactListItemEvent>
    implements $ContactListItemEventCopyWith<$Res> {
  _$ContactListItemEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ContactListItemEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$StartedImplCopyWith<$Res> {
  factory _$$StartedImplCopyWith(
          _$StartedImpl value, $Res Function(_$StartedImpl) then) =
      __$$StartedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({dynamic item});
}

/// @nodoc
class __$$StartedImplCopyWithImpl<$Res>
    extends _$ContactListItemEventCopyWithImpl<$Res, _$StartedImpl>
    implements _$$StartedImplCopyWith<$Res> {
  __$$StartedImplCopyWithImpl(
      _$StartedImpl _value, $Res Function(_$StartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of ContactListItemEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? item = freezed,
  }) {
    return _then(_$StartedImpl(
      item: freezed == item
          ? _value.item
          : item // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ));
  }
}

/// @nodoc

class _$StartedImpl implements _Started {
  const _$StartedImpl({required this.item});

  @override
  final dynamic item;

  @override
  String toString() {
    return 'ContactListItemEvent.started(item: $item)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StartedImpl &&
            const DeepCollectionEquality().equals(other.item, item));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(item));

  /// Create a copy of ContactListItemEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StartedImplCopyWith<_$StartedImpl> get copyWith =>
      __$$StartedImplCopyWithImpl<_$StartedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(dynamic item) started,
    required TResult Function() fetchedCallRates,
    required TResult Function() addedToFavourites,
    required TResult Function() removedFromFavourites,
    required TResult Function() toggleAsFavourites,
    required TResult Function() checkIfFavourited,
    required TResult Function() fetchedRecentCalls,
    required TResult Function() processPhoneNumber,
  }) {
    return started(item);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(dynamic item)? started,
    TResult? Function()? fetchedCallRates,
    TResult? Function()? addedToFavourites,
    TResult? Function()? removedFromFavourites,
    TResult? Function()? toggleAsFavourites,
    TResult? Function()? checkIfFavourited,
    TResult? Function()? fetchedRecentCalls,
    TResult? Function()? processPhoneNumber,
  }) {
    return started?.call(item);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(dynamic item)? started,
    TResult Function()? fetchedCallRates,
    TResult Function()? addedToFavourites,
    TResult Function()? removedFromFavourites,
    TResult Function()? toggleAsFavourites,
    TResult Function()? checkIfFavourited,
    TResult Function()? fetchedRecentCalls,
    TResult Function()? processPhoneNumber,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started(item);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_FetchedCallRates value) fetchedCallRates,
    required TResult Function(_AddedToFavourites value) addedToFavourites,
    required TResult Function(_RemovedFromFavourites value)
        removedFromFavourites,
    required TResult Function(_ToggleAsFavourites value) toggleAsFavourites,
    required TResult Function(_CheckIfFavourited value) checkIfFavourited,
    required TResult Function(_FetchedRecentCalls value) fetchedRecentCalls,
    required TResult Function(_ProcessPhoneNumber value) processPhoneNumber,
  }) {
    return started(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_FetchedCallRates value)? fetchedCallRates,
    TResult? Function(_AddedToFavourites value)? addedToFavourites,
    TResult? Function(_RemovedFromFavourites value)? removedFromFavourites,
    TResult? Function(_ToggleAsFavourites value)? toggleAsFavourites,
    TResult? Function(_CheckIfFavourited value)? checkIfFavourited,
    TResult? Function(_FetchedRecentCalls value)? fetchedRecentCalls,
    TResult? Function(_ProcessPhoneNumber value)? processPhoneNumber,
  }) {
    return started?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_FetchedCallRates value)? fetchedCallRates,
    TResult Function(_AddedToFavourites value)? addedToFavourites,
    TResult Function(_RemovedFromFavourites value)? removedFromFavourites,
    TResult Function(_ToggleAsFavourites value)? toggleAsFavourites,
    TResult Function(_CheckIfFavourited value)? checkIfFavourited,
    TResult Function(_FetchedRecentCalls value)? fetchedRecentCalls,
    TResult Function(_ProcessPhoneNumber value)? processPhoneNumber,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started(this);
    }
    return orElse();
  }
}

abstract class _Started implements ContactListItemEvent {
  const factory _Started({required final dynamic item}) = _$StartedImpl;

  dynamic get item;

  /// Create a copy of ContactListItemEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StartedImplCopyWith<_$StartedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FetchedCallRatesImplCopyWith<$Res> {
  factory _$$FetchedCallRatesImplCopyWith(_$FetchedCallRatesImpl value,
          $Res Function(_$FetchedCallRatesImpl) then) =
      __$$FetchedCallRatesImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$FetchedCallRatesImplCopyWithImpl<$Res>
    extends _$ContactListItemEventCopyWithImpl<$Res, _$FetchedCallRatesImpl>
    implements _$$FetchedCallRatesImplCopyWith<$Res> {
  __$$FetchedCallRatesImplCopyWithImpl(_$FetchedCallRatesImpl _value,
      $Res Function(_$FetchedCallRatesImpl) _then)
      : super(_value, _then);

  /// Create a copy of ContactListItemEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$FetchedCallRatesImpl implements _FetchedCallRates {
  const _$FetchedCallRatesImpl();

  @override
  String toString() {
    return 'ContactListItemEvent.fetchedCallRates()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$FetchedCallRatesImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(dynamic item) started,
    required TResult Function() fetchedCallRates,
    required TResult Function() addedToFavourites,
    required TResult Function() removedFromFavourites,
    required TResult Function() toggleAsFavourites,
    required TResult Function() checkIfFavourited,
    required TResult Function() fetchedRecentCalls,
    required TResult Function() processPhoneNumber,
  }) {
    return fetchedCallRates();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(dynamic item)? started,
    TResult? Function()? fetchedCallRates,
    TResult? Function()? addedToFavourites,
    TResult? Function()? removedFromFavourites,
    TResult? Function()? toggleAsFavourites,
    TResult? Function()? checkIfFavourited,
    TResult? Function()? fetchedRecentCalls,
    TResult? Function()? processPhoneNumber,
  }) {
    return fetchedCallRates?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(dynamic item)? started,
    TResult Function()? fetchedCallRates,
    TResult Function()? addedToFavourites,
    TResult Function()? removedFromFavourites,
    TResult Function()? toggleAsFavourites,
    TResult Function()? checkIfFavourited,
    TResult Function()? fetchedRecentCalls,
    TResult Function()? processPhoneNumber,
    required TResult orElse(),
  }) {
    if (fetchedCallRates != null) {
      return fetchedCallRates();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_FetchedCallRates value) fetchedCallRates,
    required TResult Function(_AddedToFavourites value) addedToFavourites,
    required TResult Function(_RemovedFromFavourites value)
        removedFromFavourites,
    required TResult Function(_ToggleAsFavourites value) toggleAsFavourites,
    required TResult Function(_CheckIfFavourited value) checkIfFavourited,
    required TResult Function(_FetchedRecentCalls value) fetchedRecentCalls,
    required TResult Function(_ProcessPhoneNumber value) processPhoneNumber,
  }) {
    return fetchedCallRates(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_FetchedCallRates value)? fetchedCallRates,
    TResult? Function(_AddedToFavourites value)? addedToFavourites,
    TResult? Function(_RemovedFromFavourites value)? removedFromFavourites,
    TResult? Function(_ToggleAsFavourites value)? toggleAsFavourites,
    TResult? Function(_CheckIfFavourited value)? checkIfFavourited,
    TResult? Function(_FetchedRecentCalls value)? fetchedRecentCalls,
    TResult? Function(_ProcessPhoneNumber value)? processPhoneNumber,
  }) {
    return fetchedCallRates?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_FetchedCallRates value)? fetchedCallRates,
    TResult Function(_AddedToFavourites value)? addedToFavourites,
    TResult Function(_RemovedFromFavourites value)? removedFromFavourites,
    TResult Function(_ToggleAsFavourites value)? toggleAsFavourites,
    TResult Function(_CheckIfFavourited value)? checkIfFavourited,
    TResult Function(_FetchedRecentCalls value)? fetchedRecentCalls,
    TResult Function(_ProcessPhoneNumber value)? processPhoneNumber,
    required TResult orElse(),
  }) {
    if (fetchedCallRates != null) {
      return fetchedCallRates(this);
    }
    return orElse();
  }
}

abstract class _FetchedCallRates implements ContactListItemEvent {
  const factory _FetchedCallRates() = _$FetchedCallRatesImpl;
}

/// @nodoc
abstract class _$$AddedToFavouritesImplCopyWith<$Res> {
  factory _$$AddedToFavouritesImplCopyWith(_$AddedToFavouritesImpl value,
          $Res Function(_$AddedToFavouritesImpl) then) =
      __$$AddedToFavouritesImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$AddedToFavouritesImplCopyWithImpl<$Res>
    extends _$ContactListItemEventCopyWithImpl<$Res, _$AddedToFavouritesImpl>
    implements _$$AddedToFavouritesImplCopyWith<$Res> {
  __$$AddedToFavouritesImplCopyWithImpl(_$AddedToFavouritesImpl _value,
      $Res Function(_$AddedToFavouritesImpl) _then)
      : super(_value, _then);

  /// Create a copy of ContactListItemEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$AddedToFavouritesImpl implements _AddedToFavourites {
  const _$AddedToFavouritesImpl();

  @override
  String toString() {
    return 'ContactListItemEvent.addedToFavourites()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$AddedToFavouritesImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(dynamic item) started,
    required TResult Function() fetchedCallRates,
    required TResult Function() addedToFavourites,
    required TResult Function() removedFromFavourites,
    required TResult Function() toggleAsFavourites,
    required TResult Function() checkIfFavourited,
    required TResult Function() fetchedRecentCalls,
    required TResult Function() processPhoneNumber,
  }) {
    return addedToFavourites();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(dynamic item)? started,
    TResult? Function()? fetchedCallRates,
    TResult? Function()? addedToFavourites,
    TResult? Function()? removedFromFavourites,
    TResult? Function()? toggleAsFavourites,
    TResult? Function()? checkIfFavourited,
    TResult? Function()? fetchedRecentCalls,
    TResult? Function()? processPhoneNumber,
  }) {
    return addedToFavourites?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(dynamic item)? started,
    TResult Function()? fetchedCallRates,
    TResult Function()? addedToFavourites,
    TResult Function()? removedFromFavourites,
    TResult Function()? toggleAsFavourites,
    TResult Function()? checkIfFavourited,
    TResult Function()? fetchedRecentCalls,
    TResult Function()? processPhoneNumber,
    required TResult orElse(),
  }) {
    if (addedToFavourites != null) {
      return addedToFavourites();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_FetchedCallRates value) fetchedCallRates,
    required TResult Function(_AddedToFavourites value) addedToFavourites,
    required TResult Function(_RemovedFromFavourites value)
        removedFromFavourites,
    required TResult Function(_ToggleAsFavourites value) toggleAsFavourites,
    required TResult Function(_CheckIfFavourited value) checkIfFavourited,
    required TResult Function(_FetchedRecentCalls value) fetchedRecentCalls,
    required TResult Function(_ProcessPhoneNumber value) processPhoneNumber,
  }) {
    return addedToFavourites(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_FetchedCallRates value)? fetchedCallRates,
    TResult? Function(_AddedToFavourites value)? addedToFavourites,
    TResult? Function(_RemovedFromFavourites value)? removedFromFavourites,
    TResult? Function(_ToggleAsFavourites value)? toggleAsFavourites,
    TResult? Function(_CheckIfFavourited value)? checkIfFavourited,
    TResult? Function(_FetchedRecentCalls value)? fetchedRecentCalls,
    TResult? Function(_ProcessPhoneNumber value)? processPhoneNumber,
  }) {
    return addedToFavourites?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_FetchedCallRates value)? fetchedCallRates,
    TResult Function(_AddedToFavourites value)? addedToFavourites,
    TResult Function(_RemovedFromFavourites value)? removedFromFavourites,
    TResult Function(_ToggleAsFavourites value)? toggleAsFavourites,
    TResult Function(_CheckIfFavourited value)? checkIfFavourited,
    TResult Function(_FetchedRecentCalls value)? fetchedRecentCalls,
    TResult Function(_ProcessPhoneNumber value)? processPhoneNumber,
    required TResult orElse(),
  }) {
    if (addedToFavourites != null) {
      return addedToFavourites(this);
    }
    return orElse();
  }
}

abstract class _AddedToFavourites implements ContactListItemEvent {
  const factory _AddedToFavourites() = _$AddedToFavouritesImpl;
}

/// @nodoc
abstract class _$$RemovedFromFavouritesImplCopyWith<$Res> {
  factory _$$RemovedFromFavouritesImplCopyWith(
          _$RemovedFromFavouritesImpl value,
          $Res Function(_$RemovedFromFavouritesImpl) then) =
      __$$RemovedFromFavouritesImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$RemovedFromFavouritesImplCopyWithImpl<$Res>
    extends _$ContactListItemEventCopyWithImpl<$Res,
        _$RemovedFromFavouritesImpl>
    implements _$$RemovedFromFavouritesImplCopyWith<$Res> {
  __$$RemovedFromFavouritesImplCopyWithImpl(_$RemovedFromFavouritesImpl _value,
      $Res Function(_$RemovedFromFavouritesImpl) _then)
      : super(_value, _then);

  /// Create a copy of ContactListItemEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$RemovedFromFavouritesImpl implements _RemovedFromFavourites {
  const _$RemovedFromFavouritesImpl();

  @override
  String toString() {
    return 'ContactListItemEvent.removedFromFavourites()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RemovedFromFavouritesImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(dynamic item) started,
    required TResult Function() fetchedCallRates,
    required TResult Function() addedToFavourites,
    required TResult Function() removedFromFavourites,
    required TResult Function() toggleAsFavourites,
    required TResult Function() checkIfFavourited,
    required TResult Function() fetchedRecentCalls,
    required TResult Function() processPhoneNumber,
  }) {
    return removedFromFavourites();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(dynamic item)? started,
    TResult? Function()? fetchedCallRates,
    TResult? Function()? addedToFavourites,
    TResult? Function()? removedFromFavourites,
    TResult? Function()? toggleAsFavourites,
    TResult? Function()? checkIfFavourited,
    TResult? Function()? fetchedRecentCalls,
    TResult? Function()? processPhoneNumber,
  }) {
    return removedFromFavourites?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(dynamic item)? started,
    TResult Function()? fetchedCallRates,
    TResult Function()? addedToFavourites,
    TResult Function()? removedFromFavourites,
    TResult Function()? toggleAsFavourites,
    TResult Function()? checkIfFavourited,
    TResult Function()? fetchedRecentCalls,
    TResult Function()? processPhoneNumber,
    required TResult orElse(),
  }) {
    if (removedFromFavourites != null) {
      return removedFromFavourites();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_FetchedCallRates value) fetchedCallRates,
    required TResult Function(_AddedToFavourites value) addedToFavourites,
    required TResult Function(_RemovedFromFavourites value)
        removedFromFavourites,
    required TResult Function(_ToggleAsFavourites value) toggleAsFavourites,
    required TResult Function(_CheckIfFavourited value) checkIfFavourited,
    required TResult Function(_FetchedRecentCalls value) fetchedRecentCalls,
    required TResult Function(_ProcessPhoneNumber value) processPhoneNumber,
  }) {
    return removedFromFavourites(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_FetchedCallRates value)? fetchedCallRates,
    TResult? Function(_AddedToFavourites value)? addedToFavourites,
    TResult? Function(_RemovedFromFavourites value)? removedFromFavourites,
    TResult? Function(_ToggleAsFavourites value)? toggleAsFavourites,
    TResult? Function(_CheckIfFavourited value)? checkIfFavourited,
    TResult? Function(_FetchedRecentCalls value)? fetchedRecentCalls,
    TResult? Function(_ProcessPhoneNumber value)? processPhoneNumber,
  }) {
    return removedFromFavourites?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_FetchedCallRates value)? fetchedCallRates,
    TResult Function(_AddedToFavourites value)? addedToFavourites,
    TResult Function(_RemovedFromFavourites value)? removedFromFavourites,
    TResult Function(_ToggleAsFavourites value)? toggleAsFavourites,
    TResult Function(_CheckIfFavourited value)? checkIfFavourited,
    TResult Function(_FetchedRecentCalls value)? fetchedRecentCalls,
    TResult Function(_ProcessPhoneNumber value)? processPhoneNumber,
    required TResult orElse(),
  }) {
    if (removedFromFavourites != null) {
      return removedFromFavourites(this);
    }
    return orElse();
  }
}

abstract class _RemovedFromFavourites implements ContactListItemEvent {
  const factory _RemovedFromFavourites() = _$RemovedFromFavouritesImpl;
}

/// @nodoc
abstract class _$$ToggleAsFavouritesImplCopyWith<$Res> {
  factory _$$ToggleAsFavouritesImplCopyWith(_$ToggleAsFavouritesImpl value,
          $Res Function(_$ToggleAsFavouritesImpl) then) =
      __$$ToggleAsFavouritesImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ToggleAsFavouritesImplCopyWithImpl<$Res>
    extends _$ContactListItemEventCopyWithImpl<$Res, _$ToggleAsFavouritesImpl>
    implements _$$ToggleAsFavouritesImplCopyWith<$Res> {
  __$$ToggleAsFavouritesImplCopyWithImpl(_$ToggleAsFavouritesImpl _value,
      $Res Function(_$ToggleAsFavouritesImpl) _then)
      : super(_value, _then);

  /// Create a copy of ContactListItemEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ToggleAsFavouritesImpl implements _ToggleAsFavourites {
  const _$ToggleAsFavouritesImpl();

  @override
  String toString() {
    return 'ContactListItemEvent.toggleAsFavourites()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ToggleAsFavouritesImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(dynamic item) started,
    required TResult Function() fetchedCallRates,
    required TResult Function() addedToFavourites,
    required TResult Function() removedFromFavourites,
    required TResult Function() toggleAsFavourites,
    required TResult Function() checkIfFavourited,
    required TResult Function() fetchedRecentCalls,
    required TResult Function() processPhoneNumber,
  }) {
    return toggleAsFavourites();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(dynamic item)? started,
    TResult? Function()? fetchedCallRates,
    TResult? Function()? addedToFavourites,
    TResult? Function()? removedFromFavourites,
    TResult? Function()? toggleAsFavourites,
    TResult? Function()? checkIfFavourited,
    TResult? Function()? fetchedRecentCalls,
    TResult? Function()? processPhoneNumber,
  }) {
    return toggleAsFavourites?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(dynamic item)? started,
    TResult Function()? fetchedCallRates,
    TResult Function()? addedToFavourites,
    TResult Function()? removedFromFavourites,
    TResult Function()? toggleAsFavourites,
    TResult Function()? checkIfFavourited,
    TResult Function()? fetchedRecentCalls,
    TResult Function()? processPhoneNumber,
    required TResult orElse(),
  }) {
    if (toggleAsFavourites != null) {
      return toggleAsFavourites();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_FetchedCallRates value) fetchedCallRates,
    required TResult Function(_AddedToFavourites value) addedToFavourites,
    required TResult Function(_RemovedFromFavourites value)
        removedFromFavourites,
    required TResult Function(_ToggleAsFavourites value) toggleAsFavourites,
    required TResult Function(_CheckIfFavourited value) checkIfFavourited,
    required TResult Function(_FetchedRecentCalls value) fetchedRecentCalls,
    required TResult Function(_ProcessPhoneNumber value) processPhoneNumber,
  }) {
    return toggleAsFavourites(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_FetchedCallRates value)? fetchedCallRates,
    TResult? Function(_AddedToFavourites value)? addedToFavourites,
    TResult? Function(_RemovedFromFavourites value)? removedFromFavourites,
    TResult? Function(_ToggleAsFavourites value)? toggleAsFavourites,
    TResult? Function(_CheckIfFavourited value)? checkIfFavourited,
    TResult? Function(_FetchedRecentCalls value)? fetchedRecentCalls,
    TResult? Function(_ProcessPhoneNumber value)? processPhoneNumber,
  }) {
    return toggleAsFavourites?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_FetchedCallRates value)? fetchedCallRates,
    TResult Function(_AddedToFavourites value)? addedToFavourites,
    TResult Function(_RemovedFromFavourites value)? removedFromFavourites,
    TResult Function(_ToggleAsFavourites value)? toggleAsFavourites,
    TResult Function(_CheckIfFavourited value)? checkIfFavourited,
    TResult Function(_FetchedRecentCalls value)? fetchedRecentCalls,
    TResult Function(_ProcessPhoneNumber value)? processPhoneNumber,
    required TResult orElse(),
  }) {
    if (toggleAsFavourites != null) {
      return toggleAsFavourites(this);
    }
    return orElse();
  }
}

abstract class _ToggleAsFavourites implements ContactListItemEvent {
  const factory _ToggleAsFavourites() = _$ToggleAsFavouritesImpl;
}

/// @nodoc
abstract class _$$CheckIfFavouritedImplCopyWith<$Res> {
  factory _$$CheckIfFavouritedImplCopyWith(_$CheckIfFavouritedImpl value,
          $Res Function(_$CheckIfFavouritedImpl) then) =
      __$$CheckIfFavouritedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CheckIfFavouritedImplCopyWithImpl<$Res>
    extends _$ContactListItemEventCopyWithImpl<$Res, _$CheckIfFavouritedImpl>
    implements _$$CheckIfFavouritedImplCopyWith<$Res> {
  __$$CheckIfFavouritedImplCopyWithImpl(_$CheckIfFavouritedImpl _value,
      $Res Function(_$CheckIfFavouritedImpl) _then)
      : super(_value, _then);

  /// Create a copy of ContactListItemEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CheckIfFavouritedImpl implements _CheckIfFavourited {
  const _$CheckIfFavouritedImpl();

  @override
  String toString() {
    return 'ContactListItemEvent.checkIfFavourited()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CheckIfFavouritedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(dynamic item) started,
    required TResult Function() fetchedCallRates,
    required TResult Function() addedToFavourites,
    required TResult Function() removedFromFavourites,
    required TResult Function() toggleAsFavourites,
    required TResult Function() checkIfFavourited,
    required TResult Function() fetchedRecentCalls,
    required TResult Function() processPhoneNumber,
  }) {
    return checkIfFavourited();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(dynamic item)? started,
    TResult? Function()? fetchedCallRates,
    TResult? Function()? addedToFavourites,
    TResult? Function()? removedFromFavourites,
    TResult? Function()? toggleAsFavourites,
    TResult? Function()? checkIfFavourited,
    TResult? Function()? fetchedRecentCalls,
    TResult? Function()? processPhoneNumber,
  }) {
    return checkIfFavourited?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(dynamic item)? started,
    TResult Function()? fetchedCallRates,
    TResult Function()? addedToFavourites,
    TResult Function()? removedFromFavourites,
    TResult Function()? toggleAsFavourites,
    TResult Function()? checkIfFavourited,
    TResult Function()? fetchedRecentCalls,
    TResult Function()? processPhoneNumber,
    required TResult orElse(),
  }) {
    if (checkIfFavourited != null) {
      return checkIfFavourited();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_FetchedCallRates value) fetchedCallRates,
    required TResult Function(_AddedToFavourites value) addedToFavourites,
    required TResult Function(_RemovedFromFavourites value)
        removedFromFavourites,
    required TResult Function(_ToggleAsFavourites value) toggleAsFavourites,
    required TResult Function(_CheckIfFavourited value) checkIfFavourited,
    required TResult Function(_FetchedRecentCalls value) fetchedRecentCalls,
    required TResult Function(_ProcessPhoneNumber value) processPhoneNumber,
  }) {
    return checkIfFavourited(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_FetchedCallRates value)? fetchedCallRates,
    TResult? Function(_AddedToFavourites value)? addedToFavourites,
    TResult? Function(_RemovedFromFavourites value)? removedFromFavourites,
    TResult? Function(_ToggleAsFavourites value)? toggleAsFavourites,
    TResult? Function(_CheckIfFavourited value)? checkIfFavourited,
    TResult? Function(_FetchedRecentCalls value)? fetchedRecentCalls,
    TResult? Function(_ProcessPhoneNumber value)? processPhoneNumber,
  }) {
    return checkIfFavourited?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_FetchedCallRates value)? fetchedCallRates,
    TResult Function(_AddedToFavourites value)? addedToFavourites,
    TResult Function(_RemovedFromFavourites value)? removedFromFavourites,
    TResult Function(_ToggleAsFavourites value)? toggleAsFavourites,
    TResult Function(_CheckIfFavourited value)? checkIfFavourited,
    TResult Function(_FetchedRecentCalls value)? fetchedRecentCalls,
    TResult Function(_ProcessPhoneNumber value)? processPhoneNumber,
    required TResult orElse(),
  }) {
    if (checkIfFavourited != null) {
      return checkIfFavourited(this);
    }
    return orElse();
  }
}

abstract class _CheckIfFavourited implements ContactListItemEvent {
  const factory _CheckIfFavourited() = _$CheckIfFavouritedImpl;
}

/// @nodoc
abstract class _$$FetchedRecentCallsImplCopyWith<$Res> {
  factory _$$FetchedRecentCallsImplCopyWith(_$FetchedRecentCallsImpl value,
          $Res Function(_$FetchedRecentCallsImpl) then) =
      __$$FetchedRecentCallsImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$FetchedRecentCallsImplCopyWithImpl<$Res>
    extends _$ContactListItemEventCopyWithImpl<$Res, _$FetchedRecentCallsImpl>
    implements _$$FetchedRecentCallsImplCopyWith<$Res> {
  __$$FetchedRecentCallsImplCopyWithImpl(_$FetchedRecentCallsImpl _value,
      $Res Function(_$FetchedRecentCallsImpl) _then)
      : super(_value, _then);

  /// Create a copy of ContactListItemEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$FetchedRecentCallsImpl implements _FetchedRecentCalls {
  const _$FetchedRecentCallsImpl();

  @override
  String toString() {
    return 'ContactListItemEvent.fetchedRecentCalls()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$FetchedRecentCallsImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(dynamic item) started,
    required TResult Function() fetchedCallRates,
    required TResult Function() addedToFavourites,
    required TResult Function() removedFromFavourites,
    required TResult Function() toggleAsFavourites,
    required TResult Function() checkIfFavourited,
    required TResult Function() fetchedRecentCalls,
    required TResult Function() processPhoneNumber,
  }) {
    return fetchedRecentCalls();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(dynamic item)? started,
    TResult? Function()? fetchedCallRates,
    TResult? Function()? addedToFavourites,
    TResult? Function()? removedFromFavourites,
    TResult? Function()? toggleAsFavourites,
    TResult? Function()? checkIfFavourited,
    TResult? Function()? fetchedRecentCalls,
    TResult? Function()? processPhoneNumber,
  }) {
    return fetchedRecentCalls?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(dynamic item)? started,
    TResult Function()? fetchedCallRates,
    TResult Function()? addedToFavourites,
    TResult Function()? removedFromFavourites,
    TResult Function()? toggleAsFavourites,
    TResult Function()? checkIfFavourited,
    TResult Function()? fetchedRecentCalls,
    TResult Function()? processPhoneNumber,
    required TResult orElse(),
  }) {
    if (fetchedRecentCalls != null) {
      return fetchedRecentCalls();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_FetchedCallRates value) fetchedCallRates,
    required TResult Function(_AddedToFavourites value) addedToFavourites,
    required TResult Function(_RemovedFromFavourites value)
        removedFromFavourites,
    required TResult Function(_ToggleAsFavourites value) toggleAsFavourites,
    required TResult Function(_CheckIfFavourited value) checkIfFavourited,
    required TResult Function(_FetchedRecentCalls value) fetchedRecentCalls,
    required TResult Function(_ProcessPhoneNumber value) processPhoneNumber,
  }) {
    return fetchedRecentCalls(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_FetchedCallRates value)? fetchedCallRates,
    TResult? Function(_AddedToFavourites value)? addedToFavourites,
    TResult? Function(_RemovedFromFavourites value)? removedFromFavourites,
    TResult? Function(_ToggleAsFavourites value)? toggleAsFavourites,
    TResult? Function(_CheckIfFavourited value)? checkIfFavourited,
    TResult? Function(_FetchedRecentCalls value)? fetchedRecentCalls,
    TResult? Function(_ProcessPhoneNumber value)? processPhoneNumber,
  }) {
    return fetchedRecentCalls?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_FetchedCallRates value)? fetchedCallRates,
    TResult Function(_AddedToFavourites value)? addedToFavourites,
    TResult Function(_RemovedFromFavourites value)? removedFromFavourites,
    TResult Function(_ToggleAsFavourites value)? toggleAsFavourites,
    TResult Function(_CheckIfFavourited value)? checkIfFavourited,
    TResult Function(_FetchedRecentCalls value)? fetchedRecentCalls,
    TResult Function(_ProcessPhoneNumber value)? processPhoneNumber,
    required TResult orElse(),
  }) {
    if (fetchedRecentCalls != null) {
      return fetchedRecentCalls(this);
    }
    return orElse();
  }
}

abstract class _FetchedRecentCalls implements ContactListItemEvent {
  const factory _FetchedRecentCalls() = _$FetchedRecentCallsImpl;
}

/// @nodoc
abstract class _$$ProcessPhoneNumberImplCopyWith<$Res> {
  factory _$$ProcessPhoneNumberImplCopyWith(_$ProcessPhoneNumberImpl value,
          $Res Function(_$ProcessPhoneNumberImpl) then) =
      __$$ProcessPhoneNumberImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ProcessPhoneNumberImplCopyWithImpl<$Res>
    extends _$ContactListItemEventCopyWithImpl<$Res, _$ProcessPhoneNumberImpl>
    implements _$$ProcessPhoneNumberImplCopyWith<$Res> {
  __$$ProcessPhoneNumberImplCopyWithImpl(_$ProcessPhoneNumberImpl _value,
      $Res Function(_$ProcessPhoneNumberImpl) _then)
      : super(_value, _then);

  /// Create a copy of ContactListItemEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ProcessPhoneNumberImpl implements _ProcessPhoneNumber {
  const _$ProcessPhoneNumberImpl();

  @override
  String toString() {
    return 'ContactListItemEvent.processPhoneNumber()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ProcessPhoneNumberImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(dynamic item) started,
    required TResult Function() fetchedCallRates,
    required TResult Function() addedToFavourites,
    required TResult Function() removedFromFavourites,
    required TResult Function() toggleAsFavourites,
    required TResult Function() checkIfFavourited,
    required TResult Function() fetchedRecentCalls,
    required TResult Function() processPhoneNumber,
  }) {
    return processPhoneNumber();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(dynamic item)? started,
    TResult? Function()? fetchedCallRates,
    TResult? Function()? addedToFavourites,
    TResult? Function()? removedFromFavourites,
    TResult? Function()? toggleAsFavourites,
    TResult? Function()? checkIfFavourited,
    TResult? Function()? fetchedRecentCalls,
    TResult? Function()? processPhoneNumber,
  }) {
    return processPhoneNumber?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(dynamic item)? started,
    TResult Function()? fetchedCallRates,
    TResult Function()? addedToFavourites,
    TResult Function()? removedFromFavourites,
    TResult Function()? toggleAsFavourites,
    TResult Function()? checkIfFavourited,
    TResult Function()? fetchedRecentCalls,
    TResult Function()? processPhoneNumber,
    required TResult orElse(),
  }) {
    if (processPhoneNumber != null) {
      return processPhoneNumber();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_FetchedCallRates value) fetchedCallRates,
    required TResult Function(_AddedToFavourites value) addedToFavourites,
    required TResult Function(_RemovedFromFavourites value)
        removedFromFavourites,
    required TResult Function(_ToggleAsFavourites value) toggleAsFavourites,
    required TResult Function(_CheckIfFavourited value) checkIfFavourited,
    required TResult Function(_FetchedRecentCalls value) fetchedRecentCalls,
    required TResult Function(_ProcessPhoneNumber value) processPhoneNumber,
  }) {
    return processPhoneNumber(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_FetchedCallRates value)? fetchedCallRates,
    TResult? Function(_AddedToFavourites value)? addedToFavourites,
    TResult? Function(_RemovedFromFavourites value)? removedFromFavourites,
    TResult? Function(_ToggleAsFavourites value)? toggleAsFavourites,
    TResult? Function(_CheckIfFavourited value)? checkIfFavourited,
    TResult? Function(_FetchedRecentCalls value)? fetchedRecentCalls,
    TResult? Function(_ProcessPhoneNumber value)? processPhoneNumber,
  }) {
    return processPhoneNumber?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_FetchedCallRates value)? fetchedCallRates,
    TResult Function(_AddedToFavourites value)? addedToFavourites,
    TResult Function(_RemovedFromFavourites value)? removedFromFavourites,
    TResult Function(_ToggleAsFavourites value)? toggleAsFavourites,
    TResult Function(_CheckIfFavourited value)? checkIfFavourited,
    TResult Function(_FetchedRecentCalls value)? fetchedRecentCalls,
    TResult Function(_ProcessPhoneNumber value)? processPhoneNumber,
    required TResult orElse(),
  }) {
    if (processPhoneNumber != null) {
      return processPhoneNumber(this);
    }
    return orElse();
  }
}

abstract class _ProcessPhoneNumber implements ContactListItemEvent {
  const factory _ProcessPhoneNumber() = _$ProcessPhoneNumberImpl;
}

ContactListItemState _$ContactListItemStateFromJson(Map<String, dynamic> json) {
  return _ContactListItemState.fromJson(json);
}

/// @nodoc
mixin _$ContactListItemState {
  String get fullName => throw _privateConstructorUsedError;
  String get phoneNumber => throw _privateConstructorUsedError;
  String? get formattedPhoneNumber => throw _privateConstructorUsedError;
  CallRatesAmountModel? get callRate => throw _privateConstructorUsedError;
  bool get isCallRateLoading => throw _privateConstructorUsedError;
  bool get isFavourited => throw _privateConstructorUsedError;
  bool get hasReloadedFavs => throw _privateConstructorUsedError;
  String? get callRateErrorMessage => throw _privateConstructorUsedError;
  String? get countryCode => throw _privateConstructorUsedError;

  /// Serializes this ContactListItemState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ContactListItemState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ContactListItemStateCopyWith<ContactListItemState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContactListItemStateCopyWith<$Res> {
  factory $ContactListItemStateCopyWith(ContactListItemState value,
          $Res Function(ContactListItemState) then) =
      _$ContactListItemStateCopyWithImpl<$Res, ContactListItemState>;
  @useResult
  $Res call(
      {String fullName,
      String phoneNumber,
      String? formattedPhoneNumber,
      CallRatesAmountModel? callRate,
      bool isCallRateLoading,
      bool isFavourited,
      bool hasReloadedFavs,
      String? callRateErrorMessage,
      String? countryCode});

  $CallRatesAmountModelCopyWith<$Res>? get callRate;
}

/// @nodoc
class _$ContactListItemStateCopyWithImpl<$Res,
        $Val extends ContactListItemState>
    implements $ContactListItemStateCopyWith<$Res> {
  _$ContactListItemStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ContactListItemState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fullName = null,
    Object? phoneNumber = null,
    Object? formattedPhoneNumber = freezed,
    Object? callRate = freezed,
    Object? isCallRateLoading = null,
    Object? isFavourited = null,
    Object? hasReloadedFavs = null,
    Object? callRateErrorMessage = freezed,
    Object? countryCode = freezed,
  }) {
    return _then(_value.copyWith(
      fullName: null == fullName
          ? _value.fullName
          : fullName // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      formattedPhoneNumber: freezed == formattedPhoneNumber
          ? _value.formattedPhoneNumber
          : formattedPhoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      callRate: freezed == callRate
          ? _value.callRate
          : callRate // ignore: cast_nullable_to_non_nullable
              as CallRatesAmountModel?,
      isCallRateLoading: null == isCallRateLoading
          ? _value.isCallRateLoading
          : isCallRateLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isFavourited: null == isFavourited
          ? _value.isFavourited
          : isFavourited // ignore: cast_nullable_to_non_nullable
              as bool,
      hasReloadedFavs: null == hasReloadedFavs
          ? _value.hasReloadedFavs
          : hasReloadedFavs // ignore: cast_nullable_to_non_nullable
              as bool,
      callRateErrorMessage: freezed == callRateErrorMessage
          ? _value.callRateErrorMessage
          : callRateErrorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      countryCode: freezed == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of ContactListItemState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CallRatesAmountModelCopyWith<$Res>? get callRate {
    if (_value.callRate == null) {
      return null;
    }

    return $CallRatesAmountModelCopyWith<$Res>(_value.callRate!, (value) {
      return _then(_value.copyWith(callRate: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ContactListItemStateImplCopyWith<$Res>
    implements $ContactListItemStateCopyWith<$Res> {
  factory _$$ContactListItemStateImplCopyWith(_$ContactListItemStateImpl value,
          $Res Function(_$ContactListItemStateImpl) then) =
      __$$ContactListItemStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String fullName,
      String phoneNumber,
      String? formattedPhoneNumber,
      CallRatesAmountModel? callRate,
      bool isCallRateLoading,
      bool isFavourited,
      bool hasReloadedFavs,
      String? callRateErrorMessage,
      String? countryCode});

  @override
  $CallRatesAmountModelCopyWith<$Res>? get callRate;
}

/// @nodoc
class __$$ContactListItemStateImplCopyWithImpl<$Res>
    extends _$ContactListItemStateCopyWithImpl<$Res, _$ContactListItemStateImpl>
    implements _$$ContactListItemStateImplCopyWith<$Res> {
  __$$ContactListItemStateImplCopyWithImpl(_$ContactListItemStateImpl _value,
      $Res Function(_$ContactListItemStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ContactListItemState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fullName = null,
    Object? phoneNumber = null,
    Object? formattedPhoneNumber = freezed,
    Object? callRate = freezed,
    Object? isCallRateLoading = null,
    Object? isFavourited = null,
    Object? hasReloadedFavs = null,
    Object? callRateErrorMessage = freezed,
    Object? countryCode = freezed,
  }) {
    return _then(_$ContactListItemStateImpl(
      fullName: null == fullName
          ? _value.fullName
          : fullName // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      formattedPhoneNumber: freezed == formattedPhoneNumber
          ? _value.formattedPhoneNumber
          : formattedPhoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      callRate: freezed == callRate
          ? _value.callRate
          : callRate // ignore: cast_nullable_to_non_nullable
              as CallRatesAmountModel?,
      isCallRateLoading: null == isCallRateLoading
          ? _value.isCallRateLoading
          : isCallRateLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isFavourited: null == isFavourited
          ? _value.isFavourited
          : isFavourited // ignore: cast_nullable_to_non_nullable
              as bool,
      hasReloadedFavs: null == hasReloadedFavs
          ? _value.hasReloadedFavs
          : hasReloadedFavs // ignore: cast_nullable_to_non_nullable
              as bool,
      callRateErrorMessage: freezed == callRateErrorMessage
          ? _value.callRateErrorMessage
          : callRateErrorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      countryCode: freezed == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ContactListItemStateImpl extends _ContactListItemState {
  _$ContactListItemStateImpl(
      {required this.fullName,
      required this.phoneNumber,
      this.formattedPhoneNumber,
      this.callRate,
      this.isCallRateLoading = false,
      this.isFavourited = false,
      this.hasReloadedFavs = false,
      this.callRateErrorMessage,
      this.countryCode})
      : super._();

  factory _$ContactListItemStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$ContactListItemStateImplFromJson(json);

  @override
  final String fullName;
  @override
  final String phoneNumber;
  @override
  final String? formattedPhoneNumber;
  @override
  final CallRatesAmountModel? callRate;
  @override
  @JsonKey()
  final bool isCallRateLoading;
  @override
  @JsonKey()
  final bool isFavourited;
  @override
  @JsonKey()
  final bool hasReloadedFavs;
  @override
  final String? callRateErrorMessage;
  @override
  final String? countryCode;

  @override
  String toString() {
    return 'ContactListItemState(fullName: $fullName, phoneNumber: $phoneNumber, formattedPhoneNumber: $formattedPhoneNumber, callRate: $callRate, isCallRateLoading: $isCallRateLoading, isFavourited: $isFavourited, hasReloadedFavs: $hasReloadedFavs, callRateErrorMessage: $callRateErrorMessage, countryCode: $countryCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ContactListItemStateImpl &&
            (identical(other.fullName, fullName) ||
                other.fullName == fullName) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.formattedPhoneNumber, formattedPhoneNumber) ||
                other.formattedPhoneNumber == formattedPhoneNumber) &&
            (identical(other.callRate, callRate) ||
                other.callRate == callRate) &&
            (identical(other.isCallRateLoading, isCallRateLoading) ||
                other.isCallRateLoading == isCallRateLoading) &&
            (identical(other.isFavourited, isFavourited) ||
                other.isFavourited == isFavourited) &&
            (identical(other.hasReloadedFavs, hasReloadedFavs) ||
                other.hasReloadedFavs == hasReloadedFavs) &&
            (identical(other.callRateErrorMessage, callRateErrorMessage) ||
                other.callRateErrorMessage == callRateErrorMessage) &&
            (identical(other.countryCode, countryCode) ||
                other.countryCode == countryCode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      fullName,
      phoneNumber,
      formattedPhoneNumber,
      callRate,
      isCallRateLoading,
      isFavourited,
      hasReloadedFavs,
      callRateErrorMessage,
      countryCode);

  /// Create a copy of ContactListItemState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ContactListItemStateImplCopyWith<_$ContactListItemStateImpl>
      get copyWith =>
          __$$ContactListItemStateImplCopyWithImpl<_$ContactListItemStateImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ContactListItemStateImplToJson(
      this,
    );
  }
}

abstract class _ContactListItemState extends ContactListItemState {
  factory _ContactListItemState(
      {required final String fullName,
      required final String phoneNumber,
      final String? formattedPhoneNumber,
      final CallRatesAmountModel? callRate,
      final bool isCallRateLoading,
      final bool isFavourited,
      final bool hasReloadedFavs,
      final String? callRateErrorMessage,
      final String? countryCode}) = _$ContactListItemStateImpl;
  _ContactListItemState._() : super._();

  factory _ContactListItemState.fromJson(Map<String, dynamic> json) =
      _$ContactListItemStateImpl.fromJson;

  @override
  String get fullName;
  @override
  String get phoneNumber;
  @override
  String? get formattedPhoneNumber;
  @override
  CallRatesAmountModel? get callRate;
  @override
  bool get isCallRateLoading;
  @override
  bool get isFavourited;
  @override
  bool get hasReloadedFavs;
  @override
  String? get callRateErrorMessage;
  @override
  String? get countryCode;

  /// Create a copy of ContactListItemState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ContactListItemStateImplCopyWith<_$ContactListItemStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
