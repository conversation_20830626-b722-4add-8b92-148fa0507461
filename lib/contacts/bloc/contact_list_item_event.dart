part of 'contact_list_item_bloc.dart';

@freezed
class ContactListItemEvent with _$ContactListItemEvent {
  const factory ContactListItemEvent.started({
    required dynamic item,
  }) = _Started;
  const factory ContactListItemEvent.fetchedCallRates() = _FetchedCallRates;
  const factory ContactListItemEvent.addedToFavourites() = _AddedToFavourites;
  const factory ContactListItemEvent.removedFromFavourites() =
      _RemovedFromFavourites;
  const factory ContactListItemEvent.toggleAsFavourites() = _ToggleAsFavourites;
  const factory ContactListItemEvent.checkIfFavourited() = _CheckIfFavourited;
  const factory ContactListItemEvent.fetchedRecentCalls() = _FetchedRecentCalls;
  const factory ContactListItemEvent.processPhoneNumber() = _ProcessPhoneNumber;
}
