// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'recent_calls_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$RecentCallsEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(String? value) updatedSearchField,
    required TResult Function() reset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(String? value)? updatedSearchField,
    TResult? Function()? reset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(String? value)? updatedSearchField,
    TResult Function()? reset,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Searched value) updatedSearchField,
    required TResult Function(_Initial value) reset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Searched value)? updatedSearchField,
    TResult? Function(_Initial value)? reset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Searched value)? updatedSearchField,
    TResult Function(_Initial value)? reset,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RecentCallsEventCopyWith<$Res> {
  factory $RecentCallsEventCopyWith(
          RecentCallsEvent value, $Res Function(RecentCallsEvent) then) =
      _$RecentCallsEventCopyWithImpl<$Res, RecentCallsEvent>;
}

/// @nodoc
class _$RecentCallsEventCopyWithImpl<$Res, $Val extends RecentCallsEvent>
    implements $RecentCallsEventCopyWith<$Res> {
  _$RecentCallsEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RecentCallsEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$StartedImplCopyWith<$Res> {
  factory _$$StartedImplCopyWith(
          _$StartedImpl value, $Res Function(_$StartedImpl) then) =
      __$$StartedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$StartedImplCopyWithImpl<$Res>
    extends _$RecentCallsEventCopyWithImpl<$Res, _$StartedImpl>
    implements _$$StartedImplCopyWith<$Res> {
  __$$StartedImplCopyWithImpl(
      _$StartedImpl _value, $Res Function(_$StartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of RecentCallsEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$StartedImpl implements _Started {
  const _$StartedImpl();

  @override
  String toString() {
    return 'RecentCallsEvent.started()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$StartedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(String? value) updatedSearchField,
    required TResult Function() reset,
  }) {
    return started();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(String? value)? updatedSearchField,
    TResult? Function()? reset,
  }) {
    return started?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(String? value)? updatedSearchField,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Searched value) updatedSearchField,
    required TResult Function(_Initial value) reset,
  }) {
    return started(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Searched value)? updatedSearchField,
    TResult? Function(_Initial value)? reset,
  }) {
    return started?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Searched value)? updatedSearchField,
    TResult Function(_Initial value)? reset,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started(this);
    }
    return orElse();
  }
}

abstract class _Started implements RecentCallsEvent {
  const factory _Started() = _$StartedImpl;
}

/// @nodoc
abstract class _$$SearchedImplCopyWith<$Res> {
  factory _$$SearchedImplCopyWith(
          _$SearchedImpl value, $Res Function(_$SearchedImpl) then) =
      __$$SearchedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? value});
}

/// @nodoc
class __$$SearchedImplCopyWithImpl<$Res>
    extends _$RecentCallsEventCopyWithImpl<$Res, _$SearchedImpl>
    implements _$$SearchedImplCopyWith<$Res> {
  __$$SearchedImplCopyWithImpl(
      _$SearchedImpl _value, $Res Function(_$SearchedImpl) _then)
      : super(_value, _then);

  /// Create a copy of RecentCallsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = freezed,
  }) {
    return _then(_$SearchedImpl(
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$SearchedImpl implements _Searched {
  const _$SearchedImpl({this.value});

  @override
  final String? value;

  @override
  String toString() {
    return 'RecentCallsEvent.updatedSearchField(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchedImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  /// Create a copy of RecentCallsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchedImplCopyWith<_$SearchedImpl> get copyWith =>
      __$$SearchedImplCopyWithImpl<_$SearchedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(String? value) updatedSearchField,
    required TResult Function() reset,
  }) {
    return updatedSearchField(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(String? value)? updatedSearchField,
    TResult? Function()? reset,
  }) {
    return updatedSearchField?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(String? value)? updatedSearchField,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (updatedSearchField != null) {
      return updatedSearchField(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Searched value) updatedSearchField,
    required TResult Function(_Initial value) reset,
  }) {
    return updatedSearchField(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Searched value)? updatedSearchField,
    TResult? Function(_Initial value)? reset,
  }) {
    return updatedSearchField?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Searched value)? updatedSearchField,
    TResult Function(_Initial value)? reset,
    required TResult orElse(),
  }) {
    if (updatedSearchField != null) {
      return updatedSearchField(this);
    }
    return orElse();
  }
}

abstract class _Searched implements RecentCallsEvent {
  const factory _Searched({final String? value}) = _$SearchedImpl;

  String? get value;

  /// Create a copy of RecentCallsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SearchedImplCopyWith<_$SearchedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$RecentCallsEventCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of RecentCallsEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'RecentCallsEvent.reset()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(String? value) updatedSearchField,
    required TResult Function() reset,
  }) {
    return reset();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(String? value)? updatedSearchField,
    TResult? Function()? reset,
  }) {
    return reset?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(String? value)? updatedSearchField,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (reset != null) {
      return reset();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Searched value) updatedSearchField,
    required TResult Function(_Initial value) reset,
  }) {
    return reset(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Searched value)? updatedSearchField,
    TResult? Function(_Initial value)? reset,
  }) {
    return reset?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Searched value)? updatedSearchField,
    TResult Function(_Initial value)? reset,
    required TResult orElse(),
  }) {
    if (reset != null) {
      return reset(this);
    }
    return orElse();
  }
}

abstract class _Initial implements RecentCallsEvent {
  const factory _Initial() = _$InitialImpl;
}

RecentCallsState _$RecentCallsStateFromJson(Map<String, dynamic> json) {
  return _RecentCallsState.fromJson(json);
}

/// @nodoc
mixin _$RecentCallsState {
  List<RecentCallsResponseModel> get recentCalls =>
      throw _privateConstructorUsedError;
  List<RecentCallsResponseModel> get filteredRecentCalls =>
      throw _privateConstructorUsedError;
  RecentCallsStatus get status => throw _privateConstructorUsedError;
  String? get message => throw _privateConstructorUsedError;
  String? get searchTerm => throw _privateConstructorUsedError;

  /// Serializes this RecentCallsState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RecentCallsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RecentCallsStateCopyWith<RecentCallsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RecentCallsStateCopyWith<$Res> {
  factory $RecentCallsStateCopyWith(
          RecentCallsState value, $Res Function(RecentCallsState) then) =
      _$RecentCallsStateCopyWithImpl<$Res, RecentCallsState>;
  @useResult
  $Res call(
      {List<RecentCallsResponseModel> recentCalls,
      List<RecentCallsResponseModel> filteredRecentCalls,
      RecentCallsStatus status,
      String? message,
      String? searchTerm});
}

/// @nodoc
class _$RecentCallsStateCopyWithImpl<$Res, $Val extends RecentCallsState>
    implements $RecentCallsStateCopyWith<$Res> {
  _$RecentCallsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RecentCallsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? recentCalls = null,
    Object? filteredRecentCalls = null,
    Object? status = null,
    Object? message = freezed,
    Object? searchTerm = freezed,
  }) {
    return _then(_value.copyWith(
      recentCalls: null == recentCalls
          ? _value.recentCalls
          : recentCalls // ignore: cast_nullable_to_non_nullable
              as List<RecentCallsResponseModel>,
      filteredRecentCalls: null == filteredRecentCalls
          ? _value.filteredRecentCalls
          : filteredRecentCalls // ignore: cast_nullable_to_non_nullable
              as List<RecentCallsResponseModel>,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as RecentCallsStatus,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      searchTerm: freezed == searchTerm
          ? _value.searchTerm
          : searchTerm // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RecentCallsStateImplCopyWith<$Res>
    implements $RecentCallsStateCopyWith<$Res> {
  factory _$$RecentCallsStateImplCopyWith(_$RecentCallsStateImpl value,
          $Res Function(_$RecentCallsStateImpl) then) =
      __$$RecentCallsStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<RecentCallsResponseModel> recentCalls,
      List<RecentCallsResponseModel> filteredRecentCalls,
      RecentCallsStatus status,
      String? message,
      String? searchTerm});
}

/// @nodoc
class __$$RecentCallsStateImplCopyWithImpl<$Res>
    extends _$RecentCallsStateCopyWithImpl<$Res, _$RecentCallsStateImpl>
    implements _$$RecentCallsStateImplCopyWith<$Res> {
  __$$RecentCallsStateImplCopyWithImpl(_$RecentCallsStateImpl _value,
      $Res Function(_$RecentCallsStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of RecentCallsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? recentCalls = null,
    Object? filteredRecentCalls = null,
    Object? status = null,
    Object? message = freezed,
    Object? searchTerm = freezed,
  }) {
    return _then(_$RecentCallsStateImpl(
      recentCalls: null == recentCalls
          ? _value._recentCalls
          : recentCalls // ignore: cast_nullable_to_non_nullable
              as List<RecentCallsResponseModel>,
      filteredRecentCalls: null == filteredRecentCalls
          ? _value._filteredRecentCalls
          : filteredRecentCalls // ignore: cast_nullable_to_non_nullable
              as List<RecentCallsResponseModel>,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as RecentCallsStatus,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      searchTerm: freezed == searchTerm
          ? _value.searchTerm
          : searchTerm // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RecentCallsStateImpl extends _RecentCallsState {
  _$RecentCallsStateImpl(
      {final List<RecentCallsResponseModel> recentCalls = const [],
      final List<RecentCallsResponseModel> filteredRecentCalls = const [],
      this.status = RecentCallsStatus.initial,
      this.message,
      this.searchTerm})
      : _recentCalls = recentCalls,
        _filteredRecentCalls = filteredRecentCalls,
        super._();

  factory _$RecentCallsStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$RecentCallsStateImplFromJson(json);

  final List<RecentCallsResponseModel> _recentCalls;
  @override
  @JsonKey()
  List<RecentCallsResponseModel> get recentCalls {
    if (_recentCalls is EqualUnmodifiableListView) return _recentCalls;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_recentCalls);
  }

  final List<RecentCallsResponseModel> _filteredRecentCalls;
  @override
  @JsonKey()
  List<RecentCallsResponseModel> get filteredRecentCalls {
    if (_filteredRecentCalls is EqualUnmodifiableListView)
      return _filteredRecentCalls;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_filteredRecentCalls);
  }

  @override
  @JsonKey()
  final RecentCallsStatus status;
  @override
  final String? message;
  @override
  final String? searchTerm;

  @override
  String toString() {
    return 'RecentCallsState(recentCalls: $recentCalls, filteredRecentCalls: $filteredRecentCalls, status: $status, message: $message, searchTerm: $searchTerm)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RecentCallsStateImpl &&
            const DeepCollectionEquality()
                .equals(other._recentCalls, _recentCalls) &&
            const DeepCollectionEquality()
                .equals(other._filteredRecentCalls, _filteredRecentCalls) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.searchTerm, searchTerm) ||
                other.searchTerm == searchTerm));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_recentCalls),
      const DeepCollectionEquality().hash(_filteredRecentCalls),
      status,
      message,
      searchTerm);

  /// Create a copy of RecentCallsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RecentCallsStateImplCopyWith<_$RecentCallsStateImpl> get copyWith =>
      __$$RecentCallsStateImplCopyWithImpl<_$RecentCallsStateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RecentCallsStateImplToJson(
      this,
    );
  }
}

abstract class _RecentCallsState extends RecentCallsState {
  factory _RecentCallsState(
      {final List<RecentCallsResponseModel> recentCalls,
      final List<RecentCallsResponseModel> filteredRecentCalls,
      final RecentCallsStatus status,
      final String? message,
      final String? searchTerm}) = _$RecentCallsStateImpl;
  _RecentCallsState._() : super._();

  factory _RecentCallsState.fromJson(Map<String, dynamic> json) =
      _$RecentCallsStateImpl.fromJson;

  @override
  List<RecentCallsResponseModel> get recentCalls;
  @override
  List<RecentCallsResponseModel> get filteredRecentCalls;
  @override
  RecentCallsStatus get status;
  @override
  String? get message;
  @override
  String? get searchTerm;

  /// Create a copy of RecentCallsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RecentCallsStateImplCopyWith<_$RecentCallsStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
