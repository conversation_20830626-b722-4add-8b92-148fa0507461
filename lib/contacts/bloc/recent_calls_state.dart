part of 'recent_calls_bloc.dart';

@freezed
class RecentCallsState with _$RecentCallsState {
  factory RecentCallsState({
    @Default([]) List<RecentCallsResponseModel> recentCalls,
    @Default([]) List<RecentCallsResponseModel> filteredRecentCalls,
    @Default(RecentCallsStatus.initial) RecentCallsStatus status,
    String? message,
    String? searchTerm,
  }) = _RecentCallsState;

  factory RecentCallsState.fromJson(Map<String, dynamic> json) =>
      _$RecentCallsStateFromJson(json);

  factory RecentCallsState.initial() => RecentCallsState();

  RecentCallsState._();

  RecentCallsResponseModel get getLastDialedNumber {
    if (recentCalls.isEmpty) return RecentCallsResponseModel();

    return recentCalls.first;
  }

  RecentCallsState setSuccess(List<RecentCallsResponseModel> contacts) {
    return copyWith(
      recentCalls: contacts,
      filteredRecentCalls: contacts,
      status: RecentCallsStatus.success,
    );
  }

  RecentCallsState setFailure(String message) {
    return copyWith(
      status: RecentCallsStatus.failure,
      message: message,
    );
  }

  RecentCallsState setLoading() =>
      copyWith(status: RecentCallsStatus.inProgress);

  RecentCallsState searchIn({String? searchTerm}) {
    if (searchTerm != null && searchTerm.isNotEmpty && searchTerm.length >= 2) {
      final query = searchTerm.toLowerCase().trim();
      final searchResults = recentCalls.where((e) {
        final country = e.country;

        if (country != null) {
          return e.calledNumber!.contains(query) ||
              country.code!.toLowerCase().contains(query) ||
              country.name!.toLowerCase().contains(query) ||
              country.dialingCode!.contains(query);
        }

        return e.calledNumber!.contains(query);
      }).toList();

      return copyWith(
        filteredRecentCalls: searchResults,
        searchTerm: searchTerm,
        status: RecentCallsStatus.success,
      );
    }

    return copyWith(
      filteredRecentCalls: recentCalls,
      searchTerm: searchTerm,
      status: RecentCallsStatus.success,
    );
  }
}
