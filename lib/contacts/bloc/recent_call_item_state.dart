part of 'recent_call_item_bloc.dart';

@freezed
class RecentCallItemState with _$RecentCallItemState {
  factory RecentCallItemState({
    required String lengthOfCall,
    required String phoneNumber,
    required String createdAt,
    String? formattedPhoneNumber,
    String? callStatus,
    String? countryCode,
    RecentCallsSessionBillModel? sessionBIll,
  }) = _RecentCallItemState;

  // factory RecentCallItemState.fromJson(Map<String, dynamic> json) =>
  //     _$RecentCallItemStateFromJson(json);

  factory RecentCallItemState.initial() => RecentCallItemState(
        createdAt: '',
        lengthOfCall: '',
        phoneNumber: '',
      );

  RecentCallItemState._();

  String get parsedCreatedAt {
    final startTime = createdAt;
    if (startTime == '') {
      return '';
    }

    final parsedTime = DateTime.parse(startTime);
    final day = parsedTime.day;
    final suffix = day == 1 || day == 21 || day == 31
        ? 'st'
        : day == 2 || day == 22
            ? 'nd'
            : day == 3 || day == 23
                ? 'rd'
                : 'th';
    final formattedDate = '${DateFormat('MMM').format(parsedTime)} $day$suffix';
    final dayOfWeek = DateFormat('E').format(parsedTime);
    final time = DateFormat('HH:mm').format(parsedTime);
    return '$formattedDate, ($dayOfWeek) $time';
  }

  String get countryFlag {
    return 'flags/${(countryCode ?? "nl").toLowerCase()}.svg';
  }
}
