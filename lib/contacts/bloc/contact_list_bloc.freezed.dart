// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'contact_list_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ContactListEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool requestForPermission) started,
    required TResult Function() reset,
    required TResult Function(String? value) updatedSearchField,
    required TResult Function(String countryCode) fetchedCallRate,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool requestForPermission)? started,
    TResult? Function()? reset,
    TResult? Function(String? value)? updatedSearchField,
    TResult? Function(String countryCode)? fetchedCallRate,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool requestForPermission)? started,
    TResult Function()? reset,
    TResult Function(String? value)? updatedSearchField,
    TResult Function(String countryCode)? fetchedCallRate,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Resetted value) reset,
    required TResult Function(_UpdatedSearchField value) updatedSearchField,
    required TResult Function(_FetchedCallRate value) fetchedCallRate,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Resetted value)? reset,
    TResult? Function(_UpdatedSearchField value)? updatedSearchField,
    TResult? Function(_FetchedCallRate value)? fetchedCallRate,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Resetted value)? reset,
    TResult Function(_UpdatedSearchField value)? updatedSearchField,
    TResult Function(_FetchedCallRate value)? fetchedCallRate,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContactListEventCopyWith<$Res> {
  factory $ContactListEventCopyWith(
          ContactListEvent value, $Res Function(ContactListEvent) then) =
      _$ContactListEventCopyWithImpl<$Res, ContactListEvent>;
}

/// @nodoc
class _$ContactListEventCopyWithImpl<$Res, $Val extends ContactListEvent>
    implements $ContactListEventCopyWith<$Res> {
  _$ContactListEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ContactListEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$StartedImplCopyWith<$Res> {
  factory _$$StartedImplCopyWith(
          _$StartedImpl value, $Res Function(_$StartedImpl) then) =
      __$$StartedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool requestForPermission});
}

/// @nodoc
class __$$StartedImplCopyWithImpl<$Res>
    extends _$ContactListEventCopyWithImpl<$Res, _$StartedImpl>
    implements _$$StartedImplCopyWith<$Res> {
  __$$StartedImplCopyWithImpl(
      _$StartedImpl _value, $Res Function(_$StartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of ContactListEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? requestForPermission = null,
  }) {
    return _then(_$StartedImpl(
      requestForPermission: null == requestForPermission
          ? _value.requestForPermission
          : requestForPermission // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$StartedImpl implements _Started {
  const _$StartedImpl({this.requestForPermission = false});

  @override
  @JsonKey()
  final bool requestForPermission;

  @override
  String toString() {
    return 'ContactListEvent.started(requestForPermission: $requestForPermission)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StartedImpl &&
            (identical(other.requestForPermission, requestForPermission) ||
                other.requestForPermission == requestForPermission));
  }

  @override
  int get hashCode => Object.hash(runtimeType, requestForPermission);

  /// Create a copy of ContactListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StartedImplCopyWith<_$StartedImpl> get copyWith =>
      __$$StartedImplCopyWithImpl<_$StartedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool requestForPermission) started,
    required TResult Function() reset,
    required TResult Function(String? value) updatedSearchField,
    required TResult Function(String countryCode) fetchedCallRate,
  }) {
    return started(requestForPermission);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool requestForPermission)? started,
    TResult? Function()? reset,
    TResult? Function(String? value)? updatedSearchField,
    TResult? Function(String countryCode)? fetchedCallRate,
  }) {
    return started?.call(requestForPermission);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool requestForPermission)? started,
    TResult Function()? reset,
    TResult Function(String? value)? updatedSearchField,
    TResult Function(String countryCode)? fetchedCallRate,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started(requestForPermission);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Resetted value) reset,
    required TResult Function(_UpdatedSearchField value) updatedSearchField,
    required TResult Function(_FetchedCallRate value) fetchedCallRate,
  }) {
    return started(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Resetted value)? reset,
    TResult? Function(_UpdatedSearchField value)? updatedSearchField,
    TResult? Function(_FetchedCallRate value)? fetchedCallRate,
  }) {
    return started?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Resetted value)? reset,
    TResult Function(_UpdatedSearchField value)? updatedSearchField,
    TResult Function(_FetchedCallRate value)? fetchedCallRate,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started(this);
    }
    return orElse();
  }
}

abstract class _Started implements ContactListEvent {
  const factory _Started({final bool requestForPermission}) = _$StartedImpl;

  bool get requestForPermission;

  /// Create a copy of ContactListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StartedImplCopyWith<_$StartedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ResettedImplCopyWith<$Res> {
  factory _$$ResettedImplCopyWith(
          _$ResettedImpl value, $Res Function(_$ResettedImpl) then) =
      __$$ResettedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ResettedImplCopyWithImpl<$Res>
    extends _$ContactListEventCopyWithImpl<$Res, _$ResettedImpl>
    implements _$$ResettedImplCopyWith<$Res> {
  __$$ResettedImplCopyWithImpl(
      _$ResettedImpl _value, $Res Function(_$ResettedImpl) _then)
      : super(_value, _then);

  /// Create a copy of ContactListEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ResettedImpl implements _Resetted {
  const _$ResettedImpl();

  @override
  String toString() {
    return 'ContactListEvent.reset()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ResettedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool requestForPermission) started,
    required TResult Function() reset,
    required TResult Function(String? value) updatedSearchField,
    required TResult Function(String countryCode) fetchedCallRate,
  }) {
    return reset();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool requestForPermission)? started,
    TResult? Function()? reset,
    TResult? Function(String? value)? updatedSearchField,
    TResult? Function(String countryCode)? fetchedCallRate,
  }) {
    return reset?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool requestForPermission)? started,
    TResult Function()? reset,
    TResult Function(String? value)? updatedSearchField,
    TResult Function(String countryCode)? fetchedCallRate,
    required TResult orElse(),
  }) {
    if (reset != null) {
      return reset();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Resetted value) reset,
    required TResult Function(_UpdatedSearchField value) updatedSearchField,
    required TResult Function(_FetchedCallRate value) fetchedCallRate,
  }) {
    return reset(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Resetted value)? reset,
    TResult? Function(_UpdatedSearchField value)? updatedSearchField,
    TResult? Function(_FetchedCallRate value)? fetchedCallRate,
  }) {
    return reset?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Resetted value)? reset,
    TResult Function(_UpdatedSearchField value)? updatedSearchField,
    TResult Function(_FetchedCallRate value)? fetchedCallRate,
    required TResult orElse(),
  }) {
    if (reset != null) {
      return reset(this);
    }
    return orElse();
  }
}

abstract class _Resetted implements ContactListEvent {
  const factory _Resetted() = _$ResettedImpl;
}

/// @nodoc
abstract class _$$UpdatedSearchFieldImplCopyWith<$Res> {
  factory _$$UpdatedSearchFieldImplCopyWith(_$UpdatedSearchFieldImpl value,
          $Res Function(_$UpdatedSearchFieldImpl) then) =
      __$$UpdatedSearchFieldImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? value});
}

/// @nodoc
class __$$UpdatedSearchFieldImplCopyWithImpl<$Res>
    extends _$ContactListEventCopyWithImpl<$Res, _$UpdatedSearchFieldImpl>
    implements _$$UpdatedSearchFieldImplCopyWith<$Res> {
  __$$UpdatedSearchFieldImplCopyWithImpl(_$UpdatedSearchFieldImpl _value,
      $Res Function(_$UpdatedSearchFieldImpl) _then)
      : super(_value, _then);

  /// Create a copy of ContactListEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = freezed,
  }) {
    return _then(_$UpdatedSearchFieldImpl(
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$UpdatedSearchFieldImpl implements _UpdatedSearchField {
  const _$UpdatedSearchFieldImpl({this.value});

  @override
  final String? value;

  @override
  String toString() {
    return 'ContactListEvent.updatedSearchField(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdatedSearchFieldImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  /// Create a copy of ContactListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdatedSearchFieldImplCopyWith<_$UpdatedSearchFieldImpl> get copyWith =>
      __$$UpdatedSearchFieldImplCopyWithImpl<_$UpdatedSearchFieldImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool requestForPermission) started,
    required TResult Function() reset,
    required TResult Function(String? value) updatedSearchField,
    required TResult Function(String countryCode) fetchedCallRate,
  }) {
    return updatedSearchField(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool requestForPermission)? started,
    TResult? Function()? reset,
    TResult? Function(String? value)? updatedSearchField,
    TResult? Function(String countryCode)? fetchedCallRate,
  }) {
    return updatedSearchField?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool requestForPermission)? started,
    TResult Function()? reset,
    TResult Function(String? value)? updatedSearchField,
    TResult Function(String countryCode)? fetchedCallRate,
    required TResult orElse(),
  }) {
    if (updatedSearchField != null) {
      return updatedSearchField(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Resetted value) reset,
    required TResult Function(_UpdatedSearchField value) updatedSearchField,
    required TResult Function(_FetchedCallRate value) fetchedCallRate,
  }) {
    return updatedSearchField(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Resetted value)? reset,
    TResult? Function(_UpdatedSearchField value)? updatedSearchField,
    TResult? Function(_FetchedCallRate value)? fetchedCallRate,
  }) {
    return updatedSearchField?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Resetted value)? reset,
    TResult Function(_UpdatedSearchField value)? updatedSearchField,
    TResult Function(_FetchedCallRate value)? fetchedCallRate,
    required TResult orElse(),
  }) {
    if (updatedSearchField != null) {
      return updatedSearchField(this);
    }
    return orElse();
  }
}

abstract class _UpdatedSearchField implements ContactListEvent {
  const factory _UpdatedSearchField({final String? value}) =
      _$UpdatedSearchFieldImpl;

  String? get value;

  /// Create a copy of ContactListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdatedSearchFieldImplCopyWith<_$UpdatedSearchFieldImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FetchedCallRateImplCopyWith<$Res> {
  factory _$$FetchedCallRateImplCopyWith(_$FetchedCallRateImpl value,
          $Res Function(_$FetchedCallRateImpl) then) =
      __$$FetchedCallRateImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String countryCode});
}

/// @nodoc
class __$$FetchedCallRateImplCopyWithImpl<$Res>
    extends _$ContactListEventCopyWithImpl<$Res, _$FetchedCallRateImpl>
    implements _$$FetchedCallRateImplCopyWith<$Res> {
  __$$FetchedCallRateImplCopyWithImpl(
      _$FetchedCallRateImpl _value, $Res Function(_$FetchedCallRateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ContactListEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? countryCode = null,
  }) {
    return _then(_$FetchedCallRateImpl(
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$FetchedCallRateImpl implements _FetchedCallRate {
  const _$FetchedCallRateImpl({required this.countryCode});

  @override
  final String countryCode;

  @override
  String toString() {
    return 'ContactListEvent.fetchedCallRate(countryCode: $countryCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FetchedCallRateImpl &&
            (identical(other.countryCode, countryCode) ||
                other.countryCode == countryCode));
  }

  @override
  int get hashCode => Object.hash(runtimeType, countryCode);

  /// Create a copy of ContactListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FetchedCallRateImplCopyWith<_$FetchedCallRateImpl> get copyWith =>
      __$$FetchedCallRateImplCopyWithImpl<_$FetchedCallRateImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool requestForPermission) started,
    required TResult Function() reset,
    required TResult Function(String? value) updatedSearchField,
    required TResult Function(String countryCode) fetchedCallRate,
  }) {
    return fetchedCallRate(countryCode);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool requestForPermission)? started,
    TResult? Function()? reset,
    TResult? Function(String? value)? updatedSearchField,
    TResult? Function(String countryCode)? fetchedCallRate,
  }) {
    return fetchedCallRate?.call(countryCode);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool requestForPermission)? started,
    TResult Function()? reset,
    TResult Function(String? value)? updatedSearchField,
    TResult Function(String countryCode)? fetchedCallRate,
    required TResult orElse(),
  }) {
    if (fetchedCallRate != null) {
      return fetchedCallRate(countryCode);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Resetted value) reset,
    required TResult Function(_UpdatedSearchField value) updatedSearchField,
    required TResult Function(_FetchedCallRate value) fetchedCallRate,
  }) {
    return fetchedCallRate(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Resetted value)? reset,
    TResult? Function(_UpdatedSearchField value)? updatedSearchField,
    TResult? Function(_FetchedCallRate value)? fetchedCallRate,
  }) {
    return fetchedCallRate?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Resetted value)? reset,
    TResult Function(_UpdatedSearchField value)? updatedSearchField,
    TResult Function(_FetchedCallRate value)? fetchedCallRate,
    required TResult orElse(),
  }) {
    if (fetchedCallRate != null) {
      return fetchedCallRate(this);
    }
    return orElse();
  }
}

abstract class _FetchedCallRate implements ContactListEvent {
  const factory _FetchedCallRate({required final String countryCode}) =
      _$FetchedCallRateImpl;

  String get countryCode;

  /// Create a copy of ContactListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FetchedCallRateImplCopyWith<_$FetchedCallRateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$ContactListState {
  @ContactListConverter()
  List<Contact> get contacts => throw _privateConstructorUsedError;
  @ContactListConverter()
  List<Contact> get filteredContacts =>
      throw _privateConstructorUsedError; // @ContactListConverter()
// @Default({})
// Map<String, List<Contact>> groupedContacts,
  ContactListStatus get status => throw _privateConstructorUsedError;
  String? get searchTerm => throw _privateConstructorUsedError;
  String? get message => throw _privateConstructorUsedError;

  /// Create a copy of ContactListState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ContactListStateCopyWith<ContactListState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContactListStateCopyWith<$Res> {
  factory $ContactListStateCopyWith(
          ContactListState value, $Res Function(ContactListState) then) =
      _$ContactListStateCopyWithImpl<$Res, ContactListState>;
  @useResult
  $Res call(
      {@ContactListConverter() List<Contact> contacts,
      @ContactListConverter() List<Contact> filteredContacts,
      ContactListStatus status,
      String? searchTerm,
      String? message});
}

/// @nodoc
class _$ContactListStateCopyWithImpl<$Res, $Val extends ContactListState>
    implements $ContactListStateCopyWith<$Res> {
  _$ContactListStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ContactListState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contacts = null,
    Object? filteredContacts = null,
    Object? status = null,
    Object? searchTerm = freezed,
    Object? message = freezed,
  }) {
    return _then(_value.copyWith(
      contacts: null == contacts
          ? _value.contacts
          : contacts // ignore: cast_nullable_to_non_nullable
              as List<Contact>,
      filteredContacts: null == filteredContacts
          ? _value.filteredContacts
          : filteredContacts // ignore: cast_nullable_to_non_nullable
              as List<Contact>,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ContactListStatus,
      searchTerm: freezed == searchTerm
          ? _value.searchTerm
          : searchTerm // ignore: cast_nullable_to_non_nullable
              as String?,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ContactListStateImplCopyWith<$Res>
    implements $ContactListStateCopyWith<$Res> {
  factory _$$ContactListStateImplCopyWith(_$ContactListStateImpl value,
          $Res Function(_$ContactListStateImpl) then) =
      __$$ContactListStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@ContactListConverter() List<Contact> contacts,
      @ContactListConverter() List<Contact> filteredContacts,
      ContactListStatus status,
      String? searchTerm,
      String? message});
}

/// @nodoc
class __$$ContactListStateImplCopyWithImpl<$Res>
    extends _$ContactListStateCopyWithImpl<$Res, _$ContactListStateImpl>
    implements _$$ContactListStateImplCopyWith<$Res> {
  __$$ContactListStateImplCopyWithImpl(_$ContactListStateImpl _value,
      $Res Function(_$ContactListStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ContactListState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contacts = null,
    Object? filteredContacts = null,
    Object? status = null,
    Object? searchTerm = freezed,
    Object? message = freezed,
  }) {
    return _then(_$ContactListStateImpl(
      contacts: null == contacts
          ? _value._contacts
          : contacts // ignore: cast_nullable_to_non_nullable
              as List<Contact>,
      filteredContacts: null == filteredContacts
          ? _value._filteredContacts
          : filteredContacts // ignore: cast_nullable_to_non_nullable
              as List<Contact>,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ContactListStatus,
      searchTerm: freezed == searchTerm
          ? _value.searchTerm
          : searchTerm // ignore: cast_nullable_to_non_nullable
              as String?,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$ContactListStateImpl extends _ContactListState {
  _$ContactListStateImpl(
      {@ContactListConverter() final List<Contact> contacts = const [],
      @ContactListConverter() final List<Contact> filteredContacts = const [],
      this.status = ContactListStatus.initial,
      this.searchTerm,
      this.message})
      : _contacts = contacts,
        _filteredContacts = filteredContacts,
        super._();

  final List<Contact> _contacts;
  @override
  @JsonKey()
  @ContactListConverter()
  List<Contact> get contacts {
    if (_contacts is EqualUnmodifiableListView) return _contacts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_contacts);
  }

  final List<Contact> _filteredContacts;
  @override
  @JsonKey()
  @ContactListConverter()
  List<Contact> get filteredContacts {
    if (_filteredContacts is EqualUnmodifiableListView)
      return _filteredContacts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_filteredContacts);
  }

// @ContactListConverter()
// @Default({})
// Map<String, List<Contact>> groupedContacts,
  @override
  @JsonKey()
  final ContactListStatus status;
  @override
  final String? searchTerm;
  @override
  final String? message;

  @override
  String toString() {
    return 'ContactListState(contacts: $contacts, filteredContacts: $filteredContacts, status: $status, searchTerm: $searchTerm, message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ContactListStateImpl &&
            const DeepCollectionEquality().equals(other._contacts, _contacts) &&
            const DeepCollectionEquality()
                .equals(other._filteredContacts, _filteredContacts) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.searchTerm, searchTerm) ||
                other.searchTerm == searchTerm) &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_contacts),
      const DeepCollectionEquality().hash(_filteredContacts),
      status,
      searchTerm,
      message);

  /// Create a copy of ContactListState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ContactListStateImplCopyWith<_$ContactListStateImpl> get copyWith =>
      __$$ContactListStateImplCopyWithImpl<_$ContactListStateImpl>(
          this, _$identity);
}

abstract class _ContactListState extends ContactListState {
  factory _ContactListState(
      {@ContactListConverter() final List<Contact> contacts,
      @ContactListConverter() final List<Contact> filteredContacts,
      final ContactListStatus status,
      final String? searchTerm,
      final String? message}) = _$ContactListStateImpl;
  _ContactListState._() : super._();

  @override
  @ContactListConverter()
  List<Contact> get contacts;
  @override
  @ContactListConverter()
  List<Contact> get filteredContacts; // @ContactListConverter()
// @Default({})
// Map<String, List<Contact>> groupedContacts,
  @override
  ContactListStatus get status;
  @override
  String? get searchTerm;
  @override
  String? get message;

  /// Create a copy of ContactListState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ContactListStateImplCopyWith<_$ContactListStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
