part of 'contact_list_bloc.dart';

@freezed
class ContactListState with _$ContactListState {
  factory ContactListState({
    @ContactListConverter() @Default([]) List<Contact> contacts,
    @ContactListConverter() @Default([]) List<Contact> filteredContacts,
    // @ContactListConverter()
    // @Default({})
    // Map<String, List<Contact>> groupedContacts,
    @Default(ContactListStatus.initial) ContactListStatus status,
    String? searchTerm,
    String? message,
  }) = _ContactListState;

  // factory ContactListState.fromJson(Map<String, dynamic> json) =>
  //     _$ContactListStateFromJson(json);

  factory ContactListState.initial() => ContactListState(contacts: []);

  ContactListState._();

  bool get isSuccess {
    return status == ContactListStatus.success;
  }

  bool get isFailure {
    return status == ContactListStatus.failure;
  }

  bool get isInProgress {
    return status == ContactListStatus.inProgress;
  }

  bool get isInitial {
    return status == ContactListStatus.initial;
  }

  ContactListState setLoading() =>
      copyWith(status: ContactListStatus.inProgress);

  // set contacts
  ContactListState setSuccess(List<Contact> contacts) {
    return copyWith(
      contacts: contacts,
      filteredContacts: contacts,
      status: ContactListStatus.success,
    );
  }

  // set contacts
  ContactListState contactsSearchSuccess(
    String query,
    List<Contact> contacts,
    Map<String, List<Contact>> groupedContacts,
  ) {
    return copyWith(
      searchTerm: query,
      filteredContacts: contacts,
      // groupedContacts: groupedContacts,
      status: ContactListStatus.success,
    );
  }

  ContactListState searchIn({String? searchTerm}) {
    if (searchTerm != null && searchTerm.isNotEmpty && searchTerm.length >= 2) {
      final query = searchTerm.toLowerCase().trim();
      final searchResults = contacts.where((e) {
        return e.displayName.toLowerCase().contains(query.toLowerCase()) ||
            e.phones.any(
              (phone) => phone.number.contains(query),
            );
      }).toList();

      return copyWith(
        filteredContacts: searchResults,
        searchTerm: searchTerm,
        status: ContactListStatus.success,
      );
    }

    return copyWith(
      filteredContacts: contacts,
      searchTerm: searchTerm,
      status: ContactListStatus.success,
    );
  }

  // ContactListState searchIn({String? searchTerm}) {
  //   if (searchTerm != null &&
  //searchTerm.isNotEmpty && searchTerm.length >= 2) {
  //     final query = searchTerm.toLowerCase().trim();
  //     final searchResults = contacts.where((e) {
  //       return e.displayName.toLowerCase().contains(query.toLowerCase()) ||
  //           e.phones.any((phone) => phone.number.contains(query));
  //     }).toList()
  //       ..sort(
  //           (a, b) => a.displayName.compareTo(b.displayName)); // Sort results

  //     // Group the filtered results
  //     final groupedResults = <String, List<Contact>>{};
  //     for (final contact in searchResults) {
  //       final key = contact.displayName.isEmpty
  //           ? '#'
  //           : contact.displayName.characters.first.toUpperCase();
  //       groupedResults.putIfAbsent(key, () => []).add(contact);
  //     }

  //     return copyWith(
  //       filteredContacts: searchResults,
  //       groupedContacts: groupedResults, // Add grouped results to state
  //       searchTerm: searchTerm,
  //       status: ContactListStatus.success,
  //     );
  //   }

  //   // If no search term, return all contacts grouped
  //   final groupedAll = <String, List<Contact>>{};
  //   for (final contact in contacts) {
  //     final key = contact.displayName.isEmpty
  //         ? '#'
  //         : contact.displayName.characters.first.toUpperCase();
  //     groupedAll.putIfAbsent(key, () => []).add(contact);
  //   }

  //   return copyWith(
  //     filteredContacts: contacts,
  //     groupedContacts: groupedAll,
  //     searchTerm: searchTerm,
  //     status: ContactListStatus.success,
  //   );
  // }
}
