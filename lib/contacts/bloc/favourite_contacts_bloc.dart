import 'dart:async';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:rxdart/rxdart.dart';
import 'package:utils/utils.dart';

part 'favourite_contacts_bloc.freezed.dart';
part 'favourite_contacts_bloc.g.dart';
part 'favourite_contacts_event.dart';
part 'favourite_contacts_state.dart';

EventTransformer<FavouriteContactsEvent> _debounce<FavouriteContactsEvent>(
  Duration duration,
) {
  return (events, mapper) => events.debounceTime(duration).switchMap(mapper);
}

class FavouriteContactsBloc
    extends HydratedBloc<FavouriteContactsEvent, FavouriteContactsState> {
  FavouriteContactsBloc({
    required ContactListService contactsService,
    required PhoneNumberService phoneNumberService,
  })  : _contactsService = contactsService,
        _phoneNumberService = phoneNumberService,
        super(FavouriteContactsState.initial()) {
    on<_Started>(_onStarted);
    on<_AddedFavouriteContact>(_onAdded);
    on<_DeletedFavouriteContact>(_onDeleted);
    on<_SearchedFavouriteContact>(
      _onSearched,
      transformer: _debounce(const Duration(milliseconds: 300)),
    );
    on<_Initial>(_onInitial);
  }

  final ContactListService _contactsService;
  final PhoneNumberService _phoneNumberService;

  @override
  FavouriteContactsState? fromJson(Map<String, dynamic> json) {
    try {
      return FavouriteContactsState.fromJson(json);
    } catch (_) {
      return FavouriteContactsState.initial();
    }
  }

  @override
  Map<String, dynamic>? toJson(FavouriteContactsState state) {
    try {
      // return state.toJson();
      return {
        'favouriteContacts': state.favouriteContacts,
        'fiilteredFavouriteContacts': state.fiilteredFavouriteContacts,
        // 'status': state.status,
        // 'message': state.message,
        // 'searchTerm': state.searchTerm,
      };
    } catch (_) {
      return null;
    }
  }

  final _listFavouriteContactsRepository = ListFavouriteContactsRepository();
  final _addFavouriteContactsRepository = AddFavouriteContactRepository();
  final _deleteFavouriteContactsRepository = DeleteFavouriteContactRepository();

  FutureOr<void> _onInitial(
    _Initial event,
    Emitter<FavouriteContactsState> emit,
  ) {
    emit(FavouriteContactsState.initial());
  }

  FutureOr<void> _onStarted(
    _Started event,
    Emitter<FavouriteContactsState> emit,
  ) async {
    emit(state.setLoading());
    await Future<void>.delayed(const Duration(milliseconds: 500));

    final response = await _listFavouriteContactsRepository.execute();

    if (response.isRight) {
      final contacts = response.right.data;
      if (contacts != null) {
        for (final contact in contacts) {
          final phoneNumber = contact.phoneNumber;
          if (phoneNumber == null) {
            continue;
          }

          final countryCode =
              _phoneNumberService.findCountryFromNumber(phoneNumber);

          final updatedContact =
              contact.copyWith(countryCode: countryCode?.code);

          _contactsService.addFavoriteContact(updatedContact);

          emit(state.addFavouriteContact(updatedContact));
        }
      } else {
        emit(state.setSuccess([]));
      }

      // emit success
      emit(state.copyWith(status: FavouriteContactsStatus.success));
    } else {
      emit(state.setFailure(response.left.message));
    }
  }

  FutureOr<void> _onAdded(
    _AddedFavouriteContact event,
    Emitter<FavouriteContactsState> emit,
  ) async {
    emit(state.setLoading());

    final sanitizedPhoneNumber =
        event.phoneNumber.removeEmptySpaces().replaceAll('-', '');
    final response = await _addFavouriteContactsRepository.execute(
      name: event.name,
      phoneNumber: sanitizedPhoneNumber,
    );

    if (response.isRight) {
      // final favouriteContact = response.right.data;
      // if (favouriteContact != null) {
      //   emit(
      //     state.addFavouriteContact(favouriteContact),
      //   );
      // }
      final favouriteContactsResponseModel = FavouriteContactsResponseModel(
        id: event.hashCode.toString(),
        name: event.name,
        phoneNumber: sanitizedPhoneNumber,
        countryCode: event.countryCode,
      );

      _contactsService.addFavoriteContact(favouriteContactsResponseModel);

      emit(state.addFavouriteContact(favouriteContactsResponseModel));
    } else {
      emit(state.setFailure(response.left.message ?? ''));
    }
  }

  Future<void> _onDeleted(
    _DeletedFavouriteContact event,
    Emitter<FavouriteContactsState> emit,
  ) async {
    emit(state.setLoading());

    final sanitizedPhoneNumber =
        event.phoneNumber.removeEmptySpaces().replaceAll('-', '');

    final response = await _deleteFavouriteContactsRepository.execute(
      phoneNumber: sanitizedPhoneNumber,
    );

    if (response.isRight) {
      _contactsService.removeFavoriteContact(sanitizedPhoneNumber);
      emit(state.deleteFavouriteContact(sanitizedPhoneNumber));
    } else {
      emit(state.setFailure(response.left.message ?? ''));
    }
  }

  Future<void> _onSearched(
    _SearchedFavouriteContact event,
    Emitter<FavouriteContactsState> emit,
  ) async {
    final query = event.value ?? '';

    // Don't emit loading state for empty queries
    if (query.isNotEmpty) {
      emit(state.setLoading());
    }

    // Add debounce to prevent rapid updates
    await Future<void>.delayed(const Duration(milliseconds: 300));

    // Handle empty query by showing all contacts
    if (query.isEmpty) {
      emit(state.setSuccess(state.favouriteContacts));
      return;
    }

    // Perform search for non-empty queries
    emit(state.searchIn(searchTerm: query));
  }
}
