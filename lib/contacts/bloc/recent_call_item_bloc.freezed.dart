// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'recent_call_item_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$RecentCallItemEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RecentCallItemEventCopyWith<$Res> {
  factory $RecentCallItemEventCopyWith(
          RecentCallItemEvent value, $Res Function(RecentCallItemEvent) then) =
      _$RecentCallItemEventCopyWithImpl<$Res, RecentCallItemEvent>;
}

/// @nodoc
class _$RecentCallItemEventCopyWithImpl<$Res, $Val extends RecentCallItemEvent>
    implements $RecentCallItemEventCopyWith<$Res> {
  _$RecentCallItemEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RecentCallItemEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$StartedImplCopyWith<$Res> {
  factory _$$StartedImplCopyWith(
          _$StartedImpl value, $Res Function(_$StartedImpl) then) =
      __$$StartedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$StartedImplCopyWithImpl<$Res>
    extends _$RecentCallItemEventCopyWithImpl<$Res, _$StartedImpl>
    implements _$$StartedImplCopyWith<$Res> {
  __$$StartedImplCopyWithImpl(
      _$StartedImpl _value, $Res Function(_$StartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of RecentCallItemEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$StartedImpl implements _Started {
  const _$StartedImpl();

  @override
  String toString() {
    return 'RecentCallItemEvent.started()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$StartedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
  }) {
    return started();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
  }) {
    return started?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
  }) {
    return started(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
  }) {
    return started?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started(this);
    }
    return orElse();
  }
}

abstract class _Started implements RecentCallItemEvent {
  const factory _Started() = _$StartedImpl;
}

/// @nodoc
mixin _$RecentCallItemState {
  String get lengthOfCall => throw _privateConstructorUsedError;
  String get phoneNumber => throw _privateConstructorUsedError;
  String get createdAt => throw _privateConstructorUsedError;
  String? get formattedPhoneNumber => throw _privateConstructorUsedError;
  String? get callStatus => throw _privateConstructorUsedError;
  String? get countryCode => throw _privateConstructorUsedError;
  RecentCallsSessionBillModel? get sessionBIll =>
      throw _privateConstructorUsedError;

  /// Create a copy of RecentCallItemState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RecentCallItemStateCopyWith<RecentCallItemState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RecentCallItemStateCopyWith<$Res> {
  factory $RecentCallItemStateCopyWith(
          RecentCallItemState value, $Res Function(RecentCallItemState) then) =
      _$RecentCallItemStateCopyWithImpl<$Res, RecentCallItemState>;
  @useResult
  $Res call(
      {String lengthOfCall,
      String phoneNumber,
      String createdAt,
      String? formattedPhoneNumber,
      String? callStatus,
      String? countryCode,
      RecentCallsSessionBillModel? sessionBIll});

  $RecentCallsSessionBillModelCopyWith<$Res>? get sessionBIll;
}

/// @nodoc
class _$RecentCallItemStateCopyWithImpl<$Res, $Val extends RecentCallItemState>
    implements $RecentCallItemStateCopyWith<$Res> {
  _$RecentCallItemStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RecentCallItemState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lengthOfCall = null,
    Object? phoneNumber = null,
    Object? createdAt = null,
    Object? formattedPhoneNumber = freezed,
    Object? callStatus = freezed,
    Object? countryCode = freezed,
    Object? sessionBIll = freezed,
  }) {
    return _then(_value.copyWith(
      lengthOfCall: null == lengthOfCall
          ? _value.lengthOfCall
          : lengthOfCall // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String,
      formattedPhoneNumber: freezed == formattedPhoneNumber
          ? _value.formattedPhoneNumber
          : formattedPhoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      callStatus: freezed == callStatus
          ? _value.callStatus
          : callStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      countryCode: freezed == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionBIll: freezed == sessionBIll
          ? _value.sessionBIll
          : sessionBIll // ignore: cast_nullable_to_non_nullable
              as RecentCallsSessionBillModel?,
    ) as $Val);
  }

  /// Create a copy of RecentCallItemState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RecentCallsSessionBillModelCopyWith<$Res>? get sessionBIll {
    if (_value.sessionBIll == null) {
      return null;
    }

    return $RecentCallsSessionBillModelCopyWith<$Res>(_value.sessionBIll!,
        (value) {
      return _then(_value.copyWith(sessionBIll: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RecentCallItemStateImplCopyWith<$Res>
    implements $RecentCallItemStateCopyWith<$Res> {
  factory _$$RecentCallItemStateImplCopyWith(_$RecentCallItemStateImpl value,
          $Res Function(_$RecentCallItemStateImpl) then) =
      __$$RecentCallItemStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String lengthOfCall,
      String phoneNumber,
      String createdAt,
      String? formattedPhoneNumber,
      String? callStatus,
      String? countryCode,
      RecentCallsSessionBillModel? sessionBIll});

  @override
  $RecentCallsSessionBillModelCopyWith<$Res>? get sessionBIll;
}

/// @nodoc
class __$$RecentCallItemStateImplCopyWithImpl<$Res>
    extends _$RecentCallItemStateCopyWithImpl<$Res, _$RecentCallItemStateImpl>
    implements _$$RecentCallItemStateImplCopyWith<$Res> {
  __$$RecentCallItemStateImplCopyWithImpl(_$RecentCallItemStateImpl _value,
      $Res Function(_$RecentCallItemStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of RecentCallItemState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lengthOfCall = null,
    Object? phoneNumber = null,
    Object? createdAt = null,
    Object? formattedPhoneNumber = freezed,
    Object? callStatus = freezed,
    Object? countryCode = freezed,
    Object? sessionBIll = freezed,
  }) {
    return _then(_$RecentCallItemStateImpl(
      lengthOfCall: null == lengthOfCall
          ? _value.lengthOfCall
          : lengthOfCall // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String,
      formattedPhoneNumber: freezed == formattedPhoneNumber
          ? _value.formattedPhoneNumber
          : formattedPhoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      callStatus: freezed == callStatus
          ? _value.callStatus
          : callStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      countryCode: freezed == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionBIll: freezed == sessionBIll
          ? _value.sessionBIll
          : sessionBIll // ignore: cast_nullable_to_non_nullable
              as RecentCallsSessionBillModel?,
    ));
  }
}

/// @nodoc

class _$RecentCallItemStateImpl extends _RecentCallItemState {
  _$RecentCallItemStateImpl(
      {required this.lengthOfCall,
      required this.phoneNumber,
      required this.createdAt,
      this.formattedPhoneNumber,
      this.callStatus,
      this.countryCode,
      this.sessionBIll})
      : super._();

  @override
  final String lengthOfCall;
  @override
  final String phoneNumber;
  @override
  final String createdAt;
  @override
  final String? formattedPhoneNumber;
  @override
  final String? callStatus;
  @override
  final String? countryCode;
  @override
  final RecentCallsSessionBillModel? sessionBIll;

  @override
  String toString() {
    return 'RecentCallItemState(lengthOfCall: $lengthOfCall, phoneNumber: $phoneNumber, createdAt: $createdAt, formattedPhoneNumber: $formattedPhoneNumber, callStatus: $callStatus, countryCode: $countryCode, sessionBIll: $sessionBIll)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RecentCallItemStateImpl &&
            (identical(other.lengthOfCall, lengthOfCall) ||
                other.lengthOfCall == lengthOfCall) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.formattedPhoneNumber, formattedPhoneNumber) ||
                other.formattedPhoneNumber == formattedPhoneNumber) &&
            (identical(other.callStatus, callStatus) ||
                other.callStatus == callStatus) &&
            (identical(other.countryCode, countryCode) ||
                other.countryCode == countryCode) &&
            (identical(other.sessionBIll, sessionBIll) ||
                other.sessionBIll == sessionBIll));
  }

  @override
  int get hashCode => Object.hash(runtimeType, lengthOfCall, phoneNumber,
      createdAt, formattedPhoneNumber, callStatus, countryCode, sessionBIll);

  /// Create a copy of RecentCallItemState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RecentCallItemStateImplCopyWith<_$RecentCallItemStateImpl> get copyWith =>
      __$$RecentCallItemStateImplCopyWithImpl<_$RecentCallItemStateImpl>(
          this, _$identity);
}

abstract class _RecentCallItemState extends RecentCallItemState {
  factory _RecentCallItemState(
          {required final String lengthOfCall,
          required final String phoneNumber,
          required final String createdAt,
          final String? formattedPhoneNumber,
          final String? callStatus,
          final String? countryCode,
          final RecentCallsSessionBillModel? sessionBIll}) =
      _$RecentCallItemStateImpl;
  _RecentCallItemState._() : super._();

  @override
  String get lengthOfCall;
  @override
  String get phoneNumber;
  @override
  String get createdAt;
  @override
  String? get formattedPhoneNumber;
  @override
  String? get callStatus;
  @override
  String? get countryCode;
  @override
  RecentCallsSessionBillModel? get sessionBIll;

  /// Create a copy of RecentCallItemState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RecentCallItemStateImplCopyWith<_$RecentCallItemStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
