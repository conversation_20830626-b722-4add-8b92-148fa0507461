part of 'favourite_contacts_bloc.dart';

@freezed
class FavouriteContactsState with _$FavouriteContactsState {
  factory FavouriteContactsState({
    @Default([]) List<FavouriteContactsResponseModel> favouriteContacts,
    @Default([])
    List<FavouriteContactsResponseModel> fiilteredFavouriteContacts,
    @Default(FavouriteContactsStatus.initial) FavouriteContactsStatus status,
    String? message,
    String? searchTerm,
  }) = _FavouriteContactsState;

  factory FavouriteContactsState.fromJson(Map<String, dynamic> json) =>
      _$FavouriteContactsStateFromJson(json);

  factory FavouriteContactsState.initial() => FavouriteContactsState();

  FavouriteContactsState._();

  FavouriteContactsState setSuccess(
    List<FavouriteContactsResponseModel> contacts,
  ) {
    return copyWith(
      favouriteContacts: contacts,
      fiilteredFavouriteContacts: contacts,
      status: FavouriteContactsStatus.success,
    );
  }

  FavouriteContactsState setFailure(String message) {
    return copyWith(
      status: FavouriteContactsStatus.failure,
      message: message,
    );
  }

  FavouriteContactsState addFavouriteContact(
    FavouriteContactsResponseModel contact,
  ) {
    return copyWith(
      // favouriteContact: contact,
      favouriteContacts: {...favouriteContacts, contact}.toList(),
      fiilteredFavouriteContacts:
          {...fiilteredFavouriteContacts, contact}.toSet().toList(),
      status: FavouriteContactsStatus.success,
    );
  }

  FavouriteContactsState deleteFavouriteContact(
    String phoneNumber,
  ) {
    final contact = findContact(phoneNumber);
    if (contact != null) {
      return copyWith(
        favouriteContacts: favouriteContacts
            .where((element) => element.phoneNumber != contact.phoneNumber)
            .toList(),
        fiilteredFavouriteContacts: fiilteredFavouriteContacts
            .where((element) => element.phoneNumber != contact.phoneNumber)
            .toList(),
        status: FavouriteContactsStatus.success,
      );
    }

    return setFailure('Contact Could Not Be Deleted');
  }

  // find contact via phone number
  FavouriteContactsResponseModel? findContact(String phoneNumber) {
    return favouriteContacts.firstWhereOrNull(
      (element) =>
          (element as FavouriteContactsResponseModel).phoneNumber ==
          phoneNumber,
    );
  }

  FavouriteContactsState setLoading() =>
      copyWith(status: FavouriteContactsStatus.inProgress);

  FavouriteContactsState searchIn({String? searchTerm}) {
    if (searchTerm != null && searchTerm.isNotEmpty && searchTerm.length >= 2) {
      final query = searchTerm.toLowerCase().trim();
      final searchResults = favouriteContacts.where((e) {
        return e.name!.toLowerCase().contains(query) ||
            e.phoneNumber!.contains(query);
      }).toList();

      return copyWith(
        fiilteredFavouriteContacts: searchResults,
        searchTerm: searchTerm,
        status: FavouriteContactsStatus.success,
      );
    }

    return copyWith(
      fiilteredFavouriteContacts: favouriteContacts,
      searchTerm: searchTerm,
      status: FavouriteContactsStatus.success,
    );
  }
}

extension on List<FavouriteContactsResponseModel> {
  FavouriteContactsResponseModel? firstWhereOrNull(
    bool Function(dynamic element) param0,
  ) {
    return firstWhere(param0, orElse: FavouriteContactsResponseModel.new);
  }
}
