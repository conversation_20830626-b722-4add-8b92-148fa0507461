// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'favourite_contacts_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$FavouriteContactsEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() reset,
    required TResult Function(String? value) updatedSearchField,
    required TResult Function(
            String phoneNumber, String name, String? id, String? countryCode)
        added,
    required TResult Function(String phoneNumber) deleted,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? reset,
    TResult? Function(String? value)? updatedSearchField,
    TResult? Function(
            String phoneNumber, String name, String? id, String? countryCode)?
        added,
    TResult? Function(String phoneNumber)? deleted,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? reset,
    TResult Function(String? value)? updatedSearchField,
    TResult Function(
            String phoneNumber, String name, String? id, String? countryCode)?
        added,
    TResult Function(String phoneNumber)? deleted,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Initial value) reset,
    required TResult Function(_SearchedFavouriteContact value)
        updatedSearchField,
    required TResult Function(_AddedFavouriteContact value) added,
    required TResult Function(_DeletedFavouriteContact value) deleted,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_SearchedFavouriteContact value)? updatedSearchField,
    TResult? Function(_AddedFavouriteContact value)? added,
    TResult? Function(_DeletedFavouriteContact value)? deleted,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Initial value)? reset,
    TResult Function(_SearchedFavouriteContact value)? updatedSearchField,
    TResult Function(_AddedFavouriteContact value)? added,
    TResult Function(_DeletedFavouriteContact value)? deleted,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FavouriteContactsEventCopyWith<$Res> {
  factory $FavouriteContactsEventCopyWith(FavouriteContactsEvent value,
          $Res Function(FavouriteContactsEvent) then) =
      _$FavouriteContactsEventCopyWithImpl<$Res, FavouriteContactsEvent>;
}

/// @nodoc
class _$FavouriteContactsEventCopyWithImpl<$Res,
        $Val extends FavouriteContactsEvent>
    implements $FavouriteContactsEventCopyWith<$Res> {
  _$FavouriteContactsEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FavouriteContactsEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$StartedImplCopyWith<$Res> {
  factory _$$StartedImplCopyWith(
          _$StartedImpl value, $Res Function(_$StartedImpl) then) =
      __$$StartedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$StartedImplCopyWithImpl<$Res>
    extends _$FavouriteContactsEventCopyWithImpl<$Res, _$StartedImpl>
    implements _$$StartedImplCopyWith<$Res> {
  __$$StartedImplCopyWithImpl(
      _$StartedImpl _value, $Res Function(_$StartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of FavouriteContactsEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$StartedImpl implements _Started {
  const _$StartedImpl();

  @override
  String toString() {
    return 'FavouriteContactsEvent.started()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$StartedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() reset,
    required TResult Function(String? value) updatedSearchField,
    required TResult Function(
            String phoneNumber, String name, String? id, String? countryCode)
        added,
    required TResult Function(String phoneNumber) deleted,
  }) {
    return started();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? reset,
    TResult? Function(String? value)? updatedSearchField,
    TResult? Function(
            String phoneNumber, String name, String? id, String? countryCode)?
        added,
    TResult? Function(String phoneNumber)? deleted,
  }) {
    return started?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? reset,
    TResult Function(String? value)? updatedSearchField,
    TResult Function(
            String phoneNumber, String name, String? id, String? countryCode)?
        added,
    TResult Function(String phoneNumber)? deleted,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Initial value) reset,
    required TResult Function(_SearchedFavouriteContact value)
        updatedSearchField,
    required TResult Function(_AddedFavouriteContact value) added,
    required TResult Function(_DeletedFavouriteContact value) deleted,
  }) {
    return started(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_SearchedFavouriteContact value)? updatedSearchField,
    TResult? Function(_AddedFavouriteContact value)? added,
    TResult? Function(_DeletedFavouriteContact value)? deleted,
  }) {
    return started?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Initial value)? reset,
    TResult Function(_SearchedFavouriteContact value)? updatedSearchField,
    TResult Function(_AddedFavouriteContact value)? added,
    TResult Function(_DeletedFavouriteContact value)? deleted,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started(this);
    }
    return orElse();
  }
}

abstract class _Started implements FavouriteContactsEvent {
  const factory _Started() = _$StartedImpl;
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$FavouriteContactsEventCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of FavouriteContactsEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'FavouriteContactsEvent.reset()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() reset,
    required TResult Function(String? value) updatedSearchField,
    required TResult Function(
            String phoneNumber, String name, String? id, String? countryCode)
        added,
    required TResult Function(String phoneNumber) deleted,
  }) {
    return reset();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? reset,
    TResult? Function(String? value)? updatedSearchField,
    TResult? Function(
            String phoneNumber, String name, String? id, String? countryCode)?
        added,
    TResult? Function(String phoneNumber)? deleted,
  }) {
    return reset?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? reset,
    TResult Function(String? value)? updatedSearchField,
    TResult Function(
            String phoneNumber, String name, String? id, String? countryCode)?
        added,
    TResult Function(String phoneNumber)? deleted,
    required TResult orElse(),
  }) {
    if (reset != null) {
      return reset();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Initial value) reset,
    required TResult Function(_SearchedFavouriteContact value)
        updatedSearchField,
    required TResult Function(_AddedFavouriteContact value) added,
    required TResult Function(_DeletedFavouriteContact value) deleted,
  }) {
    return reset(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_SearchedFavouriteContact value)? updatedSearchField,
    TResult? Function(_AddedFavouriteContact value)? added,
    TResult? Function(_DeletedFavouriteContact value)? deleted,
  }) {
    return reset?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Initial value)? reset,
    TResult Function(_SearchedFavouriteContact value)? updatedSearchField,
    TResult Function(_AddedFavouriteContact value)? added,
    TResult Function(_DeletedFavouriteContact value)? deleted,
    required TResult orElse(),
  }) {
    if (reset != null) {
      return reset(this);
    }
    return orElse();
  }
}

abstract class _Initial implements FavouriteContactsEvent {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$SearchedFavouriteContactImplCopyWith<$Res> {
  factory _$$SearchedFavouriteContactImplCopyWith(
          _$SearchedFavouriteContactImpl value,
          $Res Function(_$SearchedFavouriteContactImpl) then) =
      __$$SearchedFavouriteContactImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? value});
}

/// @nodoc
class __$$SearchedFavouriteContactImplCopyWithImpl<$Res>
    extends _$FavouriteContactsEventCopyWithImpl<$Res,
        _$SearchedFavouriteContactImpl>
    implements _$$SearchedFavouriteContactImplCopyWith<$Res> {
  __$$SearchedFavouriteContactImplCopyWithImpl(
      _$SearchedFavouriteContactImpl _value,
      $Res Function(_$SearchedFavouriteContactImpl) _then)
      : super(_value, _then);

  /// Create a copy of FavouriteContactsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = freezed,
  }) {
    return _then(_$SearchedFavouriteContactImpl(
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$SearchedFavouriteContactImpl implements _SearchedFavouriteContact {
  const _$SearchedFavouriteContactImpl({this.value});

  @override
  final String? value;

  @override
  String toString() {
    return 'FavouriteContactsEvent.updatedSearchField(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchedFavouriteContactImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  /// Create a copy of FavouriteContactsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchedFavouriteContactImplCopyWith<_$SearchedFavouriteContactImpl>
      get copyWith => __$$SearchedFavouriteContactImplCopyWithImpl<
          _$SearchedFavouriteContactImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() reset,
    required TResult Function(String? value) updatedSearchField,
    required TResult Function(
            String phoneNumber, String name, String? id, String? countryCode)
        added,
    required TResult Function(String phoneNumber) deleted,
  }) {
    return updatedSearchField(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? reset,
    TResult? Function(String? value)? updatedSearchField,
    TResult? Function(
            String phoneNumber, String name, String? id, String? countryCode)?
        added,
    TResult? Function(String phoneNumber)? deleted,
  }) {
    return updatedSearchField?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? reset,
    TResult Function(String? value)? updatedSearchField,
    TResult Function(
            String phoneNumber, String name, String? id, String? countryCode)?
        added,
    TResult Function(String phoneNumber)? deleted,
    required TResult orElse(),
  }) {
    if (updatedSearchField != null) {
      return updatedSearchField(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Initial value) reset,
    required TResult Function(_SearchedFavouriteContact value)
        updatedSearchField,
    required TResult Function(_AddedFavouriteContact value) added,
    required TResult Function(_DeletedFavouriteContact value) deleted,
  }) {
    return updatedSearchField(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_SearchedFavouriteContact value)? updatedSearchField,
    TResult? Function(_AddedFavouriteContact value)? added,
    TResult? Function(_DeletedFavouriteContact value)? deleted,
  }) {
    return updatedSearchField?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Initial value)? reset,
    TResult Function(_SearchedFavouriteContact value)? updatedSearchField,
    TResult Function(_AddedFavouriteContact value)? added,
    TResult Function(_DeletedFavouriteContact value)? deleted,
    required TResult orElse(),
  }) {
    if (updatedSearchField != null) {
      return updatedSearchField(this);
    }
    return orElse();
  }
}

abstract class _SearchedFavouriteContact implements FavouriteContactsEvent {
  const factory _SearchedFavouriteContact({final String? value}) =
      _$SearchedFavouriteContactImpl;

  String? get value;

  /// Create a copy of FavouriteContactsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SearchedFavouriteContactImplCopyWith<_$SearchedFavouriteContactImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AddedFavouriteContactImplCopyWith<$Res> {
  factory _$$AddedFavouriteContactImplCopyWith(
          _$AddedFavouriteContactImpl value,
          $Res Function(_$AddedFavouriteContactImpl) then) =
      __$$AddedFavouriteContactImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String phoneNumber, String name, String? id, String? countryCode});
}

/// @nodoc
class __$$AddedFavouriteContactImplCopyWithImpl<$Res>
    extends _$FavouriteContactsEventCopyWithImpl<$Res,
        _$AddedFavouriteContactImpl>
    implements _$$AddedFavouriteContactImplCopyWith<$Res> {
  __$$AddedFavouriteContactImplCopyWithImpl(_$AddedFavouriteContactImpl _value,
      $Res Function(_$AddedFavouriteContactImpl) _then)
      : super(_value, _then);

  /// Create a copy of FavouriteContactsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phoneNumber = null,
    Object? name = null,
    Object? id = freezed,
    Object? countryCode = freezed,
  }) {
    return _then(_$AddedFavouriteContactImpl(
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      countryCode: freezed == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$AddedFavouriteContactImpl implements _AddedFavouriteContact {
  const _$AddedFavouriteContactImpl(
      {required this.phoneNumber,
      required this.name,
      this.id,
      this.countryCode});

  @override
  final String phoneNumber;
  @override
  final String name;
  @override
  final String? id;
  @override
  final String? countryCode;

  @override
  String toString() {
    return 'FavouriteContactsEvent.added(phoneNumber: $phoneNumber, name: $name, id: $id, countryCode: $countryCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddedFavouriteContactImpl &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.countryCode, countryCode) ||
                other.countryCode == countryCode));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, phoneNumber, name, id, countryCode);

  /// Create a copy of FavouriteContactsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddedFavouriteContactImplCopyWith<_$AddedFavouriteContactImpl>
      get copyWith => __$$AddedFavouriteContactImplCopyWithImpl<
          _$AddedFavouriteContactImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() reset,
    required TResult Function(String? value) updatedSearchField,
    required TResult Function(
            String phoneNumber, String name, String? id, String? countryCode)
        added,
    required TResult Function(String phoneNumber) deleted,
  }) {
    return added(phoneNumber, name, id, countryCode);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? reset,
    TResult? Function(String? value)? updatedSearchField,
    TResult? Function(
            String phoneNumber, String name, String? id, String? countryCode)?
        added,
    TResult? Function(String phoneNumber)? deleted,
  }) {
    return added?.call(phoneNumber, name, id, countryCode);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? reset,
    TResult Function(String? value)? updatedSearchField,
    TResult Function(
            String phoneNumber, String name, String? id, String? countryCode)?
        added,
    TResult Function(String phoneNumber)? deleted,
    required TResult orElse(),
  }) {
    if (added != null) {
      return added(phoneNumber, name, id, countryCode);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Initial value) reset,
    required TResult Function(_SearchedFavouriteContact value)
        updatedSearchField,
    required TResult Function(_AddedFavouriteContact value) added,
    required TResult Function(_DeletedFavouriteContact value) deleted,
  }) {
    return added(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_SearchedFavouriteContact value)? updatedSearchField,
    TResult? Function(_AddedFavouriteContact value)? added,
    TResult? Function(_DeletedFavouriteContact value)? deleted,
  }) {
    return added?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Initial value)? reset,
    TResult Function(_SearchedFavouriteContact value)? updatedSearchField,
    TResult Function(_AddedFavouriteContact value)? added,
    TResult Function(_DeletedFavouriteContact value)? deleted,
    required TResult orElse(),
  }) {
    if (added != null) {
      return added(this);
    }
    return orElse();
  }
}

abstract class _AddedFavouriteContact implements FavouriteContactsEvent {
  const factory _AddedFavouriteContact(
      {required final String phoneNumber,
      required final String name,
      final String? id,
      final String? countryCode}) = _$AddedFavouriteContactImpl;

  String get phoneNumber;
  String get name;
  String? get id;
  String? get countryCode;

  /// Create a copy of FavouriteContactsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddedFavouriteContactImplCopyWith<_$AddedFavouriteContactImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeletedFavouriteContactImplCopyWith<$Res> {
  factory _$$DeletedFavouriteContactImplCopyWith(
          _$DeletedFavouriteContactImpl value,
          $Res Function(_$DeletedFavouriteContactImpl) then) =
      __$$DeletedFavouriteContactImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String phoneNumber});
}

/// @nodoc
class __$$DeletedFavouriteContactImplCopyWithImpl<$Res>
    extends _$FavouriteContactsEventCopyWithImpl<$Res,
        _$DeletedFavouriteContactImpl>
    implements _$$DeletedFavouriteContactImplCopyWith<$Res> {
  __$$DeletedFavouriteContactImplCopyWithImpl(
      _$DeletedFavouriteContactImpl _value,
      $Res Function(_$DeletedFavouriteContactImpl) _then)
      : super(_value, _then);

  /// Create a copy of FavouriteContactsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phoneNumber = null,
  }) {
    return _then(_$DeletedFavouriteContactImpl(
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DeletedFavouriteContactImpl implements _DeletedFavouriteContact {
  const _$DeletedFavouriteContactImpl({required this.phoneNumber});

  @override
  final String phoneNumber;

  @override
  String toString() {
    return 'FavouriteContactsEvent.deleted(phoneNumber: $phoneNumber)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeletedFavouriteContactImpl &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber));
  }

  @override
  int get hashCode => Object.hash(runtimeType, phoneNumber);

  /// Create a copy of FavouriteContactsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeletedFavouriteContactImplCopyWith<_$DeletedFavouriteContactImpl>
      get copyWith => __$$DeletedFavouriteContactImplCopyWithImpl<
          _$DeletedFavouriteContactImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() reset,
    required TResult Function(String? value) updatedSearchField,
    required TResult Function(
            String phoneNumber, String name, String? id, String? countryCode)
        added,
    required TResult Function(String phoneNumber) deleted,
  }) {
    return deleted(phoneNumber);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? reset,
    TResult? Function(String? value)? updatedSearchField,
    TResult? Function(
            String phoneNumber, String name, String? id, String? countryCode)?
        added,
    TResult? Function(String phoneNumber)? deleted,
  }) {
    return deleted?.call(phoneNumber);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? reset,
    TResult Function(String? value)? updatedSearchField,
    TResult Function(
            String phoneNumber, String name, String? id, String? countryCode)?
        added,
    TResult Function(String phoneNumber)? deleted,
    required TResult orElse(),
  }) {
    if (deleted != null) {
      return deleted(phoneNumber);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Initial value) reset,
    required TResult Function(_SearchedFavouriteContact value)
        updatedSearchField,
    required TResult Function(_AddedFavouriteContact value) added,
    required TResult Function(_DeletedFavouriteContact value) deleted,
  }) {
    return deleted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_SearchedFavouriteContact value)? updatedSearchField,
    TResult? Function(_AddedFavouriteContact value)? added,
    TResult? Function(_DeletedFavouriteContact value)? deleted,
  }) {
    return deleted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Initial value)? reset,
    TResult Function(_SearchedFavouriteContact value)? updatedSearchField,
    TResult Function(_AddedFavouriteContact value)? added,
    TResult Function(_DeletedFavouriteContact value)? deleted,
    required TResult orElse(),
  }) {
    if (deleted != null) {
      return deleted(this);
    }
    return orElse();
  }
}

abstract class _DeletedFavouriteContact implements FavouriteContactsEvent {
  const factory _DeletedFavouriteContact({required final String phoneNumber}) =
      _$DeletedFavouriteContactImpl;

  String get phoneNumber;

  /// Create a copy of FavouriteContactsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeletedFavouriteContactImplCopyWith<_$DeletedFavouriteContactImpl>
      get copyWith => throw _privateConstructorUsedError;
}

FavouriteContactsState _$FavouriteContactsStateFromJson(
    Map<String, dynamic> json) {
  return _FavouriteContactsState.fromJson(json);
}

/// @nodoc
mixin _$FavouriteContactsState {
  List<FavouriteContactsResponseModel> get favouriteContacts =>
      throw _privateConstructorUsedError;
  List<FavouriteContactsResponseModel> get fiilteredFavouriteContacts =>
      throw _privateConstructorUsedError;
  FavouriteContactsStatus get status => throw _privateConstructorUsedError;
  String? get message => throw _privateConstructorUsedError;
  String? get searchTerm => throw _privateConstructorUsedError;

  /// Serializes this FavouriteContactsState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FavouriteContactsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FavouriteContactsStateCopyWith<FavouriteContactsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FavouriteContactsStateCopyWith<$Res> {
  factory $FavouriteContactsStateCopyWith(FavouriteContactsState value,
          $Res Function(FavouriteContactsState) then) =
      _$FavouriteContactsStateCopyWithImpl<$Res, FavouriteContactsState>;
  @useResult
  $Res call(
      {List<FavouriteContactsResponseModel> favouriteContacts,
      List<FavouriteContactsResponseModel> fiilteredFavouriteContacts,
      FavouriteContactsStatus status,
      String? message,
      String? searchTerm});
}

/// @nodoc
class _$FavouriteContactsStateCopyWithImpl<$Res,
        $Val extends FavouriteContactsState>
    implements $FavouriteContactsStateCopyWith<$Res> {
  _$FavouriteContactsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FavouriteContactsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? favouriteContacts = null,
    Object? fiilteredFavouriteContacts = null,
    Object? status = null,
    Object? message = freezed,
    Object? searchTerm = freezed,
  }) {
    return _then(_value.copyWith(
      favouriteContacts: null == favouriteContacts
          ? _value.favouriteContacts
          : favouriteContacts // ignore: cast_nullable_to_non_nullable
              as List<FavouriteContactsResponseModel>,
      fiilteredFavouriteContacts: null == fiilteredFavouriteContacts
          ? _value.fiilteredFavouriteContacts
          : fiilteredFavouriteContacts // ignore: cast_nullable_to_non_nullable
              as List<FavouriteContactsResponseModel>,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as FavouriteContactsStatus,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      searchTerm: freezed == searchTerm
          ? _value.searchTerm
          : searchTerm // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FavouriteContactsStateImplCopyWith<$Res>
    implements $FavouriteContactsStateCopyWith<$Res> {
  factory _$$FavouriteContactsStateImplCopyWith(
          _$FavouriteContactsStateImpl value,
          $Res Function(_$FavouriteContactsStateImpl) then) =
      __$$FavouriteContactsStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<FavouriteContactsResponseModel> favouriteContacts,
      List<FavouriteContactsResponseModel> fiilteredFavouriteContacts,
      FavouriteContactsStatus status,
      String? message,
      String? searchTerm});
}

/// @nodoc
class __$$FavouriteContactsStateImplCopyWithImpl<$Res>
    extends _$FavouriteContactsStateCopyWithImpl<$Res,
        _$FavouriteContactsStateImpl>
    implements _$$FavouriteContactsStateImplCopyWith<$Res> {
  __$$FavouriteContactsStateImplCopyWithImpl(
      _$FavouriteContactsStateImpl _value,
      $Res Function(_$FavouriteContactsStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of FavouriteContactsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? favouriteContacts = null,
    Object? fiilteredFavouriteContacts = null,
    Object? status = null,
    Object? message = freezed,
    Object? searchTerm = freezed,
  }) {
    return _then(_$FavouriteContactsStateImpl(
      favouriteContacts: null == favouriteContacts
          ? _value._favouriteContacts
          : favouriteContacts // ignore: cast_nullable_to_non_nullable
              as List<FavouriteContactsResponseModel>,
      fiilteredFavouriteContacts: null == fiilteredFavouriteContacts
          ? _value._fiilteredFavouriteContacts
          : fiilteredFavouriteContacts // ignore: cast_nullable_to_non_nullable
              as List<FavouriteContactsResponseModel>,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as FavouriteContactsStatus,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      searchTerm: freezed == searchTerm
          ? _value.searchTerm
          : searchTerm // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FavouriteContactsStateImpl extends _FavouriteContactsState {
  _$FavouriteContactsStateImpl(
      {final List<FavouriteContactsResponseModel> favouriteContacts = const [],
      final List<FavouriteContactsResponseModel> fiilteredFavouriteContacts =
          const [],
      this.status = FavouriteContactsStatus.initial,
      this.message,
      this.searchTerm})
      : _favouriteContacts = favouriteContacts,
        _fiilteredFavouriteContacts = fiilteredFavouriteContacts,
        super._();

  factory _$FavouriteContactsStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$FavouriteContactsStateImplFromJson(json);

  final List<FavouriteContactsResponseModel> _favouriteContacts;
  @override
  @JsonKey()
  List<FavouriteContactsResponseModel> get favouriteContacts {
    if (_favouriteContacts is EqualUnmodifiableListView)
      return _favouriteContacts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_favouriteContacts);
  }

  final List<FavouriteContactsResponseModel> _fiilteredFavouriteContacts;
  @override
  @JsonKey()
  List<FavouriteContactsResponseModel> get fiilteredFavouriteContacts {
    if (_fiilteredFavouriteContacts is EqualUnmodifiableListView)
      return _fiilteredFavouriteContacts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_fiilteredFavouriteContacts);
  }

  @override
  @JsonKey()
  final FavouriteContactsStatus status;
  @override
  final String? message;
  @override
  final String? searchTerm;

  @override
  String toString() {
    return 'FavouriteContactsState(favouriteContacts: $favouriteContacts, fiilteredFavouriteContacts: $fiilteredFavouriteContacts, status: $status, message: $message, searchTerm: $searchTerm)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FavouriteContactsStateImpl &&
            const DeepCollectionEquality()
                .equals(other._favouriteContacts, _favouriteContacts) &&
            const DeepCollectionEquality().equals(
                other._fiilteredFavouriteContacts,
                _fiilteredFavouriteContacts) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.searchTerm, searchTerm) ||
                other.searchTerm == searchTerm));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_favouriteContacts),
      const DeepCollectionEquality().hash(_fiilteredFavouriteContacts),
      status,
      message,
      searchTerm);

  /// Create a copy of FavouriteContactsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FavouriteContactsStateImplCopyWith<_$FavouriteContactsStateImpl>
      get copyWith => __$$FavouriteContactsStateImplCopyWithImpl<
          _$FavouriteContactsStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FavouriteContactsStateImplToJson(
      this,
    );
  }
}

abstract class _FavouriteContactsState extends FavouriteContactsState {
  factory _FavouriteContactsState(
      {final List<FavouriteContactsResponseModel> favouriteContacts,
      final List<FavouriteContactsResponseModel> fiilteredFavouriteContacts,
      final FavouriteContactsStatus status,
      final String? message,
      final String? searchTerm}) = _$FavouriteContactsStateImpl;
  _FavouriteContactsState._() : super._();

  factory _FavouriteContactsState.fromJson(Map<String, dynamic> json) =
      _$FavouriteContactsStateImpl.fromJson;

  @override
  List<FavouriteContactsResponseModel> get favouriteContacts;
  @override
  List<FavouriteContactsResponseModel> get fiilteredFavouriteContacts;
  @override
  FavouriteContactsStatus get status;
  @override
  String? get message;
  @override
  String? get searchTerm;

  /// Create a copy of FavouriteContactsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FavouriteContactsStateImplCopyWith<_$FavouriteContactsStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
