// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'contact_list_item_bloc.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ContactListItemStateImpl _$$ContactListItemStateImplFromJson(
        Map<String, dynamic> json) =>
    _$ContactListItemStateImpl(
      fullName: json['fullName'] as String,
      phoneNumber: json['phoneNumber'] as String,
      formattedPhoneNumber: json['formattedPhoneNumber'] as String?,
      callRate: json['callRate'] == null
          ? null
          : CallRatesAmountModel.fromJson(
              json['callRate'] as Map<String, dynamic>),
      isCallRateLoading: json['isCallRateLoading'] as bool? ?? false,
      isFavourited: json['isFavourited'] as bool? ?? false,
      hasReloadedFavs: json['hasReloadedFavs'] as bool? ?? false,
      callRateErrorMessage: json['callRateErrorMessage'] as String?,
      countryCode: json['countryCode'] as String?,
    );

const _$$ContactListItemStateImplFieldMap = <String, String>{
  'fullName': 'fullName',
  'phoneNumber': 'phoneNumber',
  'formattedPhoneNumber': 'formattedPhoneNumber',
  'callRate': 'callRate',
  'isCallRateLoading': 'isCallRateLoading',
  'isFavourited': 'isFavourited',
  'hasReloadedFavs': 'hasReloadedFavs',
  'callRateErrorMessage': 'callRateErrorMessage',
  'countryCode': 'countryCode',
};

Map<String, dynamic> _$$ContactListItemStateImplToJson(
        _$ContactListItemStateImpl instance) =>
    <String, dynamic>{
      'fullName': instance.fullName,
      'phoneNumber': instance.phoneNumber,
      if (instance.formattedPhoneNumber case final value?)
        'formattedPhoneNumber': value,
      if (instance.callRate?.toJson() case final value?) 'callRate': value,
      'isCallRateLoading': instance.isCallRateLoading,
      'isFavourited': instance.isFavourited,
      'hasReloadedFavs': instance.hasReloadedFavs,
      if (instance.callRateErrorMessage case final value?)
        'callRateErrorMessage': value,
      if (instance.countryCode case final value?) 'countryCode': value,
    };
