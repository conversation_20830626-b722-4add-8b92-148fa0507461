import 'dart:async';

import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:countries/countries.dart';
import 'package:fast_contacts/fast_contacts.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:froggytalk/call_rates/call_rates.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:utils/utils.dart';

part 'contact_list_item_bloc.freezed.dart';
part 'contact_list_item_bloc.g.dart';
part 'contact_list_item_event.dart';
part 'contact_list_item_state.dart';

class ContactListItemBloc
    extends HydratedBloc<ContactListItemEvent, ContactListItemState> {
  ContactListItemBloc({
    required PhoneNumberService phoneNumberService,
    required CallRatesRepository callRatesRepository,
    required FavouriteContactsBloc favouriteContactsBloc,
    required ContactListService contactsService,
  })  : _phoneNumberService = phoneNumberService,
        _callRatesRepository = callRatesRepository,
        _favouriteContactsBloc = favouriteContactsBloc,
        _contactsService = contactsService,
        super(ContactListItemState.initial()) {
    on<_Started>(_onStarted);
    on<_FetchedCallRates>(_onFetchedCallRates, transformer: concurrent());
    on<_AddedToFavourites>(_onAddedToFavourites, transformer: sequential());
    on<_RemovedFromFavourites>(
      _onRemovedFromFavourites,
      transformer: sequential(),
    );
    on<_ToggleAsFavourites>(_onToggleAsFavourites, transformer: sequential());
    on<_FetchedRecentCalls>(_onFetchedRecentCalls, transformer: sequential());
    on<_CheckIfFavourited>(_onCheckIfFavourited, transformer: debounce());
    on<_ProcessPhoneNumber>(_onProcessPhoneNumber, transformer: debounce());
  }

  final PhoneNumberService _phoneNumberService;
  final CallRatesRepository _callRatesRepository;
  final FavouriteContactsBloc _favouriteContactsBloc;
  final ContactListService _contactsService;

  FutureOr<void> _onStarted(
    _Started event,
    Emitter<ContactListItemState> emit,
  ) async {
    final item = event.item;
    if (item is Contact) {
      final phones = item.phones;
      if (phones.isNotEmpty) {
        try {
          // Handle Contact type
          final phoneNumber = phones.first.number.replaceAll('-', '');
          final contactDetails = _processNumber(phoneNumber);
          final formattedPhoneNumber = contactDetails[0];
          final countryCode = contactDetails[1];

          // final formattedPhoneNumber = phoneNumber;
          // const countryCode = 'ng';

          emit(
            state.copyWith(
              fullName: item.displayName,
              phoneNumber: phoneNumber,
              formattedPhoneNumber: formattedPhoneNumber,
              countryCode: countryCode,
              isFavourited: _contactsService
                  .isFavoriteContact(formattedPhoneNumber ?? ''),
            ),
          );
        } catch (e) {
          FroggyLogger.error('Error processing contact: $e');
        }
      }
    } else if (item is ContactModel) {
      // Handle ContactModel type
      final phoneNumber = item.phoneNumber.replaceAll('-', '');
      final contactDetails = _processNumber(phoneNumber);
      final formattedPhoneNumber = contactDetails[0];
      final countryCode = contactDetails[1];

      emit(
        state.copyWith(
          fullName: item.name,
          phoneNumber: phoneNumber,
          formattedPhoneNumber: formattedPhoneNumber,
          countryCode: countryCode,
          isFavourited:
              _contactsService.isFavoriteContact(formattedPhoneNumber ?? ''),
        ),
      );
    } else if (item is FavouriteContactsResponseModel) {
      // Handle FavouriteContactsModel type
      final phoneNumber = (item.phoneNumber ?? '').replaceAll('-', '');
      final contactDetails = _processNumber(phoneNumber);
      final formattedPhoneNumber = contactDetails[0];
      final countryCode = contactDetails[1];

      emit(
        state.copyWith(
          fullName: item.name ?? '',
          phoneNumber: phoneNumber,
          formattedPhoneNumber: formattedPhoneNumber,
          countryCode: countryCode,
          isFavourited:
              _contactsService.isFavoriteContact(formattedPhoneNumber ?? ''),
        ),
      );
    }
  }

  FutureOr<void> _onFetchedCallRates(
    _FetchedCallRates event,
    Emitter<ContactListItemState> emit,
  ) async {
    final countryCode = state.countryCode;
    if (countryCode == null) {
      emit(
        state.copyWith(
          isCallRateLoading: false,
          callRateErrorMessage: 'Country code not found',
          callRate: null,
        ),
      );
      return;
    }

    emit(
      state.copyWith(
        isCallRateLoading: true,
        callRateErrorMessage: null,
      ),
    );

    final response = await _callRatesRepository.execute(
      countryCode: countryCode,
    );

    response.fold(
      (error) {
        emit(
          state.copyWith(
            isCallRateLoading: false,
            callRateErrorMessage: error.message,
            callRate: null,
          ),
        );
      },
      (success) {
        final callRate = success.data;

        final rateMobilePerMinute2 = callRate?.attributes?.rateMobilePerMinute;
        emit(
          state.copyWith(
            isCallRateLoading: false,
            callRate: rateMobilePerMinute2,
          ),
        );
      },
    );
  }

  FutureOr<void> _onAddedToFavourites(
    _AddedToFavourites event,
    Emitter<ContactListItemState> emit,
  ) {
    _favouriteContactsBloc.add(
      FavouriteContactsEvent.added(
        name: state.fullName,
        phoneNumber: state.phoneNumber,
      ),
    );

    emit(state.copyWith(isFavourited: true));
  }

  FutureOr<void> _onRemovedFromFavourites(
    _RemovedFromFavourites event,
    Emitter<ContactListItemState> emit,
  ) {
    _favouriteContactsBloc.add(
      FavouriteContactsEvent.deleted(
        phoneNumber: state.phoneNumber,
      ),
    );

    // check if the contact is already favourited
    emit(state.copyWith(isFavourited: false));
  }

  FutureOr<void> _onToggleAsFavourites(
    _ToggleAsFavourites event,
    Emitter<ContactListItemState> emit,
  ) {
    if (state.isFavourited) {
      _favouriteContactsBloc.add(
        FavouriteContactsEvent.deleted(
          phoneNumber: state.phoneNumber,
        ),
      );
    } else {
      _favouriteContactsBloc.add(
        FavouriteContactsEvent.added(
          name: state.fullName,
          phoneNumber: state.phoneNumber,
        ),
      );
    }

    emit(state.copyWith(isFavourited: !state.isFavourited));
  }

  FutureOr<void> _onFetchedRecentCalls(
    _FetchedRecentCalls event,
    Emitter<ContactListItemState> emit,
  ) {}

  @override
  ContactListItemState? fromJson(Map<String, dynamic> json) {
    try {
      return ContactListItemState.fromJson(json);
    } catch (_) {
      return ContactListItemState.initial();
    }
  }

  @override
  Map<String, dynamic>? toJson(ContactListItemState state) {
    try {
      return state.toJson();
    } catch (_) {
      return null;
    }
  }

  bool hasFetchFavs = false;

  FutureOr<void> _onCheckIfFavourited(
    _CheckIfFavourited event,
    Emitter<ContactListItemState> emit,
  ) {
    try {
      final favouriteContacts = _favouriteContactsBloc.state.favouriteContacts;

      if (favouriteContacts.isEmpty && !state.hasReloadedFavs) {
        _favouriteContactsBloc.add(const FavouriteContactsEvent.started());
        hasFetchFavs = true;
        emit(state.copyWith(hasReloadedFavs: true));
      }

      final isFavourited = favouriteContacts.any(
        (contact) {
          final sanitizedPhoneNumber = state.phoneNumber.replaceAll('-', '');
          // FroggyLogger.debug('Debugging favourites:\n'
          //     'Contact Phone Number: ${contact.phoneNumber}\n'
          //     'Sanitized Phone Number: $sanitizedPhoneNumber');
          return contact.phoneNumber == sanitizedPhoneNumber;
        },
      );

      emit(
        state.copyWith(
          isFavourited: isFavourited,
        ),
      );
    } catch (e) {
      FroggyLogger.error('Error checking if contact is favourited: $e');
      emit(
        state.copyWith(
          isFavourited: false,
        ),
      );
    }
  }

  FutureOr<void> _onProcessPhoneNumber(
    _ProcessPhoneNumber event,
    Emitter<ContactListItemState> emit,
  ) {
    final phoneNumber = state.phoneNumber;
    final contactDetails = _processNumber([_phoneNumberService, phoneNumber]);

    emit(
      state.copyWith(
        formattedPhoneNumber: contactDetails[0],
        countryCode: contactDetails[1],
      ),
    );
  }

  /// Processes a phone number to extract and format it along with its country
  /// flag.
  ///
  /// This method can handle two different input types:
  /// 1. A list containing a [PhoneNumberService] instance and a phone number
  ///    string
  /// 2. A direct phone number string (using the default [_phoneNumberService])
  ///
  /// For both cases, it formats the phone number and retrieves the associated
  /// country flag.
  ///
  /// Parameters:
  /// - [list]: Either a List<dynamic> with [PhoneNumberService] and phone
  ///   number, or a direct phone number string
  ///
  /// Returns a list containing two elements:
  /// - The formatted phone number (or original if formatting fails)
  /// - The country flag code (e.g., 'us' for United States)
  ///
  /// Example return: ['******-567-8900', 'us']
  List<String?> _processNumber(dynamic list) {
    if (list is List<dynamic> && list.length == 2) {
      final phoneNumberService = list[0] as PhoneNumberService;
      final phoneNumber = list[1] as String;
      return [
        phoneNumberService.format(phoneNumber) ?? phoneNumber,
        _getCountryFlagCached(
          phoneNumber,
          phoneNumberService: phoneNumberService,
        ),
      ];
    } else {
      final phoneNumber = list as String;
      return [
        _phoneNumberService.format(phoneNumber) ?? phoneNumber,
        _getCountryFlagCached(
          phoneNumber,
          phoneNumberService: _phoneNumberService,
        ),
      ];
    }
  }

  // Add static cache for flags
  static final Map<String, String> _flagCache = {};

  // Add cache size limit and LRU functionality
  static const _maxCacheSize = 1000;

  /// Retrieves the country flag for a given phone number, using a cache to
  /// improve performance. If the flag is found in the cache, it is returned
  /// immediately. Otherwise, the flag is fetched, stored in the cache, and
  /// then returned.
  ///
  /// The cache uses a simple FIFO (First In, First Out) strategy to manage
  /// its size, removing the oldest entry when the maximum cache size is
  /// reached.
  ///
  /// - Parameters:
  ///   - phoneNumber: The phone number for which to retrieve the country flag.
  /// - Returns: The country flag as a string, or null if the country code
  ///   cannot be determined.
  String? _getCountryFlagCached(
    String phoneNumber, {
    PhoneNumberService? phoneNumberService,
  }) {
    final countryCode = phoneNumberService?.getCountryCode(phoneNumber);
    if (countryCode == null) return null;

    // Check cache first with performance logging
    final startTime = DateTime.now();
    if (_flagCache.containsKey(countryCode)) {
      final duration = DateTime.now().difference(startTime);
      FroggyLogger.debug('Cache hit in ${duration.inMicroseconds}μs');
      return _flagCache[countryCode];
    }

    // Implement cache size management
    if (_flagCache.length >= _maxCacheSize) {
      _flagCache.remove(_flagCache.keys.first); // Simple FIFO
    }

    // Get and cache flag
    final flag =
        FroggyCountries.getInstance().findCountryByCountryCode(countryCode);
    _flagCache[countryCode] = flag?.code ?? '';
    return flag?.code;
  }
}
