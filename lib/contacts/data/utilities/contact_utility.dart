import 'package:countries/countries.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:utils/utils.dart';

class ContactUtility {
  const ContactUtility();

  static Future<void> init() async {
    // await libphonenumber.init();
  }

  static String formatPhoneNumber(String phoneNumber) {
    if (phoneNumber.length <= 7) {
      return phoneNumber;
    }

    return 'hello';

    // return libphonenumber.formatNumberSync(phoneNumber);
  }

  static String _processNumber(String phoneNumber) {
    var formattedPhoneNumber = phoneNumber.replaceAll(RegExp(r'[\s\-()]'), '');

    if (!formattedPhoneNumber.startsWith('+') &&
        !formattedPhoneNumber.startsWith('00')) {
      formattedPhoneNumber = '+$formattedPhoneNumber';
    }

    return formattedPhoneNumber;
  }

  static List<Map<String, List<ContactModel>>> sortByFirstName(
    List<ContactModel> contacts,
  ) {
    final groups = <String, List<ContactModel>>{};

    // Group contacts by first character of their name
    for (final contact in contacts) {
      final firstChar = contact.name[0].toUpperCase(); // Normalize to uppercase
      groups.putIfAbsent(firstChar, () => []).add(contact);
    }

    // Sort the groups alphabetically by their keys (first characters)
    final sortedKeys = groups.keys.toList()..sort();

    // Create a new list to hold the sorted contacts with their groups
    final sortedContactsWithGroups = <Map<String, List<ContactModel>>>[];

    // Add each group to the sorted list
    for (final key in sortedKeys) {
      sortedContactsWithGroups.add({key: groups[key]!});
    }

    return sortedContactsWithGroups;
  }

  static CountryModel? computedCountryFromPhoneNumber(List<dynamic> data) {
    final phoneNumber = data[0] as String?;
    final countries = data[1] as List<CountryModel?>;

    if (phoneNumber == null) {
      FroggyLogger.error('No phone number provided');

      return null;
    }

    if (countries.isEmpty) {
      FroggyLogger.error('No countries loaded');

      return null;
    }

    for (final country in countries) {
      final processNumber2 = _processNumber(phoneNumber);
      if (processNumber2.startsWith(country!.dialingCode!)) {
        return country;
      }
    }

    return null;
  }
}
