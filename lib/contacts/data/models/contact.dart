import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:froggytalk/call_rates/call_rates.dart';

part 'contact.freezed.dart';
part 'contact.g.dart';

@freezed
class ContactModel with _$ContactModel {
  const factory ContactModel({
    required String name,
    required String phoneNumber,
    String? id,
    String? prefix,
    String? countryCode,
    String? dialingCode,
    dynamic avatarUrl,
    String? email,
    CallRatesResponse? callRateInfo,
  }) = _ContactModel;

  factory ContactModel.empty() => const ContactModel(name: '', phoneNumber: '');

  factory ContactModel.fromJson(Map<String, dynamic> json) =>
      _$ContactModelFromJson(json);
}
