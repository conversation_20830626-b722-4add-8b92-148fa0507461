// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'contact.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ContactModelImpl _$$ContactModelImplFromJson(Map<String, dynamic> json) =>
    _$ContactModelImpl(
      name: json['name'] as String,
      phoneNumber: json['phoneNumber'] as String,
      id: json['id'] as String?,
      prefix: json['prefix'] as String?,
      countryCode: json['countryCode'] as String?,
      dialingCode: json['dialingCode'] as String?,
      avatarUrl: json['avatarUrl'],
      email: json['email'] as String?,
      callRateInfo: json['callRateInfo'] == null
          ? null
          : CallRatesResponse.fromJson(
              json['callRateInfo'] as Map<String, dynamic>),
    );

const _$$ContactModelImplFieldMap = <String, String>{
  'name': 'name',
  'phoneNumber': 'phoneNumber',
  'id': 'id',
  'prefix': 'prefix',
  'countryCode': 'countryCode',
  'dialingCode': 'dialingCode',
  'avatarUrl': 'avatarUrl',
  'email': 'email',
  'callRateInfo': 'callRateInfo',
};

Map<String, dynamic> _$$ContactModelImplToJson(_$ContactModelImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'phoneNumber': instance.phoneNumber,
      if (instance.id case final value?) 'id': value,
      if (instance.prefix case final value?) 'prefix': value,
      if (instance.countryCode case final value?) 'countryCode': value,
      if (instance.dialingCode case final value?) 'dialingCode': value,
      if (instance.avatarUrl case final value?) 'avatarUrl': value,
      if (instance.email case final value?) 'email': value,
      if (instance.callRateInfo?.toJson() case final value?)
        'callRateInfo': value,
    };
