import 'package:characters/characters.dart';
import 'package:fast_contacts/fast_contacts.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'trie_node.freezed.dart';

@freezed
class ContactSearchResult with _$ContactSearchResult {
  const factory ContactSearchResult({
    required List<Contact> contacts,
    required String searchTerm,
  }) = _ContactSearchResult;
}

class ContactListTrieNode {
  Map<String, ContactListTrieNode> children = {};
  List<Contact> contacts = [];
  bool isEndOfWord = false;

  void insert(String key, Contact contact, {bool isPhoneNumber = false}) {
    var current = this;
    final processedKey = isPhoneNumber
        ? key.replaceAll(RegExp(r'[^\d]'), '')
        : key.toLowerCase();

    for (final char in processedKey.characters) {
      current.children.putIfAbsent(char, ContactListTrieNode.new);
      current = current.children[char]!;
      if (!current.contacts.contains(contact)) {
        current.contacts.add(contact);
      }
    }
    current.isEndOfWord = true;
  }

  List<Contact> search(String prefix, {bool isPhoneNumber = false}) {
    ContactListTrieNode? current = this;
    final processedPrefix = isPhoneNumber
        ? prefix.replaceAll(RegExp(r'[^\d]'), '')
        : prefix.toLowerCase();

    for (final char in processedPrefix.characters) {
      current = current?.children[char];
      if (current == null) return [];
    }

    return current?.contacts ?? [];
  }
}
