// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'favourite_contacts_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

FavouriteContactsResponseModel _$FavouriteContactsResponseModelFromJson(
    Map<String, dynamic> json) {
  return _FavouriteContactsResponseModel.fromJson(json);
}

/// @nodoc
mixin _$FavouriteContactsResponseModel {
  String? get id => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  @JsonKey(name: 'telephone')
  String? get phoneNumber => throw _privateConstructorUsedError;
  @JsonKey(name: 'country')
  String? get countryCode => throw _privateConstructorUsedError;

  /// Serializes this FavouriteContactsResponseModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FavouriteContactsResponseModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FavouriteContactsResponseModelCopyWith<FavouriteContactsResponseModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FavouriteContactsResponseModelCopyWith<$Res> {
  factory $FavouriteContactsResponseModelCopyWith(
          FavouriteContactsResponseModel value,
          $Res Function(FavouriteContactsResponseModel) then) =
      _$FavouriteContactsResponseModelCopyWithImpl<$Res,
          FavouriteContactsResponseModel>;
  @useResult
  $Res call(
      {String? id,
      String? name,
      @JsonKey(name: 'telephone') String? phoneNumber,
      @JsonKey(name: 'country') String? countryCode});
}

/// @nodoc
class _$FavouriteContactsResponseModelCopyWithImpl<$Res,
        $Val extends FavouriteContactsResponseModel>
    implements $FavouriteContactsResponseModelCopyWith<$Res> {
  _$FavouriteContactsResponseModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FavouriteContactsResponseModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? phoneNumber = freezed,
    Object? countryCode = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      countryCode: freezed == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FavouriteContactsResponseModelImplCopyWith<$Res>
    implements $FavouriteContactsResponseModelCopyWith<$Res> {
  factory _$$FavouriteContactsResponseModelImplCopyWith(
          _$FavouriteContactsResponseModelImpl value,
          $Res Function(_$FavouriteContactsResponseModelImpl) then) =
      __$$FavouriteContactsResponseModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? id,
      String? name,
      @JsonKey(name: 'telephone') String? phoneNumber,
      @JsonKey(name: 'country') String? countryCode});
}

/// @nodoc
class __$$FavouriteContactsResponseModelImplCopyWithImpl<$Res>
    extends _$FavouriteContactsResponseModelCopyWithImpl<$Res,
        _$FavouriteContactsResponseModelImpl>
    implements _$$FavouriteContactsResponseModelImplCopyWith<$Res> {
  __$$FavouriteContactsResponseModelImplCopyWithImpl(
      _$FavouriteContactsResponseModelImpl _value,
      $Res Function(_$FavouriteContactsResponseModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of FavouriteContactsResponseModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? phoneNumber = freezed,
    Object? countryCode = freezed,
  }) {
    return _then(_$FavouriteContactsResponseModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      countryCode: freezed == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FavouriteContactsResponseModelImpl
    implements _FavouriteContactsResponseModel {
  _$FavouriteContactsResponseModelImpl(
      {this.id,
      this.name,
      @JsonKey(name: 'telephone') this.phoneNumber,
      @JsonKey(name: 'country') this.countryCode});

  factory _$FavouriteContactsResponseModelImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$FavouriteContactsResponseModelImplFromJson(json);

  @override
  final String? id;
  @override
  final String? name;
  @override
  @JsonKey(name: 'telephone')
  final String? phoneNumber;
  @override
  @JsonKey(name: 'country')
  final String? countryCode;

  @override
  String toString() {
    return 'FavouriteContactsResponseModel(id: $id, name: $name, phoneNumber: $phoneNumber, countryCode: $countryCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FavouriteContactsResponseModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.countryCode, countryCode) ||
                other.countryCode == countryCode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, name, phoneNumber, countryCode);

  /// Create a copy of FavouriteContactsResponseModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FavouriteContactsResponseModelImplCopyWith<
          _$FavouriteContactsResponseModelImpl>
      get copyWith => __$$FavouriteContactsResponseModelImplCopyWithImpl<
          _$FavouriteContactsResponseModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FavouriteContactsResponseModelImplToJson(
      this,
    );
  }
}

abstract class _FavouriteContactsResponseModel
    implements FavouriteContactsResponseModel {
  factory _FavouriteContactsResponseModel(
          {final String? id,
          final String? name,
          @JsonKey(name: 'telephone') final String? phoneNumber,
          @JsonKey(name: 'country') final String? countryCode}) =
      _$FavouriteContactsResponseModelImpl;

  factory _FavouriteContactsResponseModel.fromJson(Map<String, dynamic> json) =
      _$FavouriteContactsResponseModelImpl.fromJson;

  @override
  String? get id;
  @override
  String? get name;
  @override
  @JsonKey(name: 'telephone')
  String? get phoneNumber;
  @override
  @JsonKey(name: 'country')
  String? get countryCode;

  /// Create a copy of FavouriteContactsResponseModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FavouriteContactsResponseModelImplCopyWith<
          _$FavouriteContactsResponseModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

AddFavouriteContactRequestModel _$AddFavouriteContactRequestModelFromJson(
    Map<String, dynamic> json) {
  return _AddFavouriteContactRequestModel.fromJson(json);
}

/// @nodoc
mixin _$AddFavouriteContactRequestModel {
  String get name => throw _privateConstructorUsedError;
  @JsonKey(name: 'telephone')
  String get phoneNumber => throw _privateConstructorUsedError;
  @JsonKey(name: 'country')
  String? get countryCode => throw _privateConstructorUsedError;

  /// Serializes this AddFavouriteContactRequestModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AddFavouriteContactRequestModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AddFavouriteContactRequestModelCopyWith<AddFavouriteContactRequestModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AddFavouriteContactRequestModelCopyWith<$Res> {
  factory $AddFavouriteContactRequestModelCopyWith(
          AddFavouriteContactRequestModel value,
          $Res Function(AddFavouriteContactRequestModel) then) =
      _$AddFavouriteContactRequestModelCopyWithImpl<$Res,
          AddFavouriteContactRequestModel>;
  @useResult
  $Res call(
      {String name,
      @JsonKey(name: 'telephone') String phoneNumber,
      @JsonKey(name: 'country') String? countryCode});
}

/// @nodoc
class _$AddFavouriteContactRequestModelCopyWithImpl<$Res,
        $Val extends AddFavouriteContactRequestModel>
    implements $AddFavouriteContactRequestModelCopyWith<$Res> {
  _$AddFavouriteContactRequestModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AddFavouriteContactRequestModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? phoneNumber = null,
    Object? countryCode = freezed,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      countryCode: freezed == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AddFavouriteContactRequestModelImplCopyWith<$Res>
    implements $AddFavouriteContactRequestModelCopyWith<$Res> {
  factory _$$AddFavouriteContactRequestModelImplCopyWith(
          _$AddFavouriteContactRequestModelImpl value,
          $Res Function(_$AddFavouriteContactRequestModelImpl) then) =
      __$$AddFavouriteContactRequestModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String name,
      @JsonKey(name: 'telephone') String phoneNumber,
      @JsonKey(name: 'country') String? countryCode});
}

/// @nodoc
class __$$AddFavouriteContactRequestModelImplCopyWithImpl<$Res>
    extends _$AddFavouriteContactRequestModelCopyWithImpl<$Res,
        _$AddFavouriteContactRequestModelImpl>
    implements _$$AddFavouriteContactRequestModelImplCopyWith<$Res> {
  __$$AddFavouriteContactRequestModelImplCopyWithImpl(
      _$AddFavouriteContactRequestModelImpl _value,
      $Res Function(_$AddFavouriteContactRequestModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddFavouriteContactRequestModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? phoneNumber = null,
    Object? countryCode = freezed,
  }) {
    return _then(_$AddFavouriteContactRequestModelImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      countryCode: freezed == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AddFavouriteContactRequestModelImpl
    implements _AddFavouriteContactRequestModel {
  _$AddFavouriteContactRequestModelImpl(
      {required this.name,
      @JsonKey(name: 'telephone') required this.phoneNumber,
      @JsonKey(name: 'country') this.countryCode});

  factory _$AddFavouriteContactRequestModelImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$AddFavouriteContactRequestModelImplFromJson(json);

  @override
  final String name;
  @override
  @JsonKey(name: 'telephone')
  final String phoneNumber;
  @override
  @JsonKey(name: 'country')
  final String? countryCode;

  @override
  String toString() {
    return 'AddFavouriteContactRequestModel(name: $name, phoneNumber: $phoneNumber, countryCode: $countryCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddFavouriteContactRequestModelImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.countryCode, countryCode) ||
                other.countryCode == countryCode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, name, phoneNumber, countryCode);

  /// Create a copy of AddFavouriteContactRequestModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddFavouriteContactRequestModelImplCopyWith<
          _$AddFavouriteContactRequestModelImpl>
      get copyWith => __$$AddFavouriteContactRequestModelImplCopyWithImpl<
          _$AddFavouriteContactRequestModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AddFavouriteContactRequestModelImplToJson(
      this,
    );
  }
}

abstract class _AddFavouriteContactRequestModel
    implements AddFavouriteContactRequestModel {
  factory _AddFavouriteContactRequestModel(
          {required final String name,
          @JsonKey(name: 'telephone') required final String phoneNumber,
          @JsonKey(name: 'country') final String? countryCode}) =
      _$AddFavouriteContactRequestModelImpl;

  factory _AddFavouriteContactRequestModel.fromJson(Map<String, dynamic> json) =
      _$AddFavouriteContactRequestModelImpl.fromJson;

  @override
  String get name;
  @override
  @JsonKey(name: 'telephone')
  String get phoneNumber;
  @override
  @JsonKey(name: 'country')
  String? get countryCode;

  /// Create a copy of AddFavouriteContactRequestModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddFavouriteContactRequestModelImplCopyWith<
          _$AddFavouriteContactRequestModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

DeleteFavouriteContactRequestModel _$DeleteFavouriteContactRequestModelFromJson(
    Map<String, dynamic> json) {
  return _DeleteFavouriteContactRequestModel.fromJson(json);
}

/// @nodoc
mixin _$DeleteFavouriteContactRequestModel {
  @JsonKey(name: 'telephone')
  String get phoneNumber => throw _privateConstructorUsedError;

  /// Serializes this DeleteFavouriteContactRequestModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DeleteFavouriteContactRequestModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DeleteFavouriteContactRequestModelCopyWith<
          DeleteFavouriteContactRequestModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeleteFavouriteContactRequestModelCopyWith<$Res> {
  factory $DeleteFavouriteContactRequestModelCopyWith(
          DeleteFavouriteContactRequestModel value,
          $Res Function(DeleteFavouriteContactRequestModel) then) =
      _$DeleteFavouriteContactRequestModelCopyWithImpl<$Res,
          DeleteFavouriteContactRequestModel>;
  @useResult
  $Res call({@JsonKey(name: 'telephone') String phoneNumber});
}

/// @nodoc
class _$DeleteFavouriteContactRequestModelCopyWithImpl<$Res,
        $Val extends DeleteFavouriteContactRequestModel>
    implements $DeleteFavouriteContactRequestModelCopyWith<$Res> {
  _$DeleteFavouriteContactRequestModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeleteFavouriteContactRequestModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phoneNumber = null,
  }) {
    return _then(_value.copyWith(
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DeleteFavouriteContactRequestModelImplCopyWith<$Res>
    implements $DeleteFavouriteContactRequestModelCopyWith<$Res> {
  factory _$$DeleteFavouriteContactRequestModelImplCopyWith(
          _$DeleteFavouriteContactRequestModelImpl value,
          $Res Function(_$DeleteFavouriteContactRequestModelImpl) then) =
      __$$DeleteFavouriteContactRequestModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({@JsonKey(name: 'telephone') String phoneNumber});
}

/// @nodoc
class __$$DeleteFavouriteContactRequestModelImplCopyWithImpl<$Res>
    extends _$DeleteFavouriteContactRequestModelCopyWithImpl<$Res,
        _$DeleteFavouriteContactRequestModelImpl>
    implements _$$DeleteFavouriteContactRequestModelImplCopyWith<$Res> {
  __$$DeleteFavouriteContactRequestModelImplCopyWithImpl(
      _$DeleteFavouriteContactRequestModelImpl _value,
      $Res Function(_$DeleteFavouriteContactRequestModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeleteFavouriteContactRequestModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phoneNumber = null,
  }) {
    return _then(_$DeleteFavouriteContactRequestModelImpl(
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DeleteFavouriteContactRequestModelImpl
    implements _DeleteFavouriteContactRequestModel {
  _$DeleteFavouriteContactRequestModelImpl(
      {@JsonKey(name: 'telephone') required this.phoneNumber});

  factory _$DeleteFavouriteContactRequestModelImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$DeleteFavouriteContactRequestModelImplFromJson(json);

  @override
  @JsonKey(name: 'telephone')
  final String phoneNumber;

  @override
  String toString() {
    return 'DeleteFavouriteContactRequestModel(phoneNumber: $phoneNumber)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeleteFavouriteContactRequestModelImpl &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, phoneNumber);

  /// Create a copy of DeleteFavouriteContactRequestModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeleteFavouriteContactRequestModelImplCopyWith<
          _$DeleteFavouriteContactRequestModelImpl>
      get copyWith => __$$DeleteFavouriteContactRequestModelImplCopyWithImpl<
          _$DeleteFavouriteContactRequestModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DeleteFavouriteContactRequestModelImplToJson(
      this,
    );
  }
}

abstract class _DeleteFavouriteContactRequestModel
    implements DeleteFavouriteContactRequestModel {
  factory _DeleteFavouriteContactRequestModel(
          {@JsonKey(name: 'telephone') required final String phoneNumber}) =
      _$DeleteFavouriteContactRequestModelImpl;

  factory _DeleteFavouriteContactRequestModel.fromJson(
          Map<String, dynamic> json) =
      _$DeleteFavouriteContactRequestModelImpl.fromJson;

  @override
  @JsonKey(name: 'telephone')
  String get phoneNumber;

  /// Create a copy of DeleteFavouriteContactRequestModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeleteFavouriteContactRequestModelImplCopyWith<
          _$DeleteFavouriteContactRequestModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
