// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'recent_calls_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RecentCallsResponseModel _$RecentCallsResponseModelFromJson(
    Map<String, dynamic> json) {
  return _RecentCallsResponseModel.fromJson(json);
}

/// @nodoc
mixin _$RecentCallsResponseModel {
  @JsonKey(name: 'session_id')
  String? get sessionId => throw _privateConstructorUsedError;
  @JsonKey(name: 'session_bill')
  RecentCallsSessionBillModel? get sessionBill =>
      throw _privateConstructorUsedError;
  @JsonKey(name: 'start_time')
  String? get startTime => throw _privateConstructorUsedError;
  @JsonKey(name: 'end_time')
  String? get endTime => throw _privateConstructorUsedError;
  String? get duration => throw _privateConstructorUsedError;
  String? get status => throw _privateConstructorUsedError;
  @JsonKey(name: 'unique_id')
  String? get uniqueId => throw _privateConstructorUsedError;
  CountryModel? get country => throw _privateConstructorUsedError;
  @JsonKey(name: 'called_number')
  String? get calledNumber => throw _privateConstructorUsedError;

  /// Serializes this RecentCallsResponseModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RecentCallsResponseModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RecentCallsResponseModelCopyWith<RecentCallsResponseModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RecentCallsResponseModelCopyWith<$Res> {
  factory $RecentCallsResponseModelCopyWith(RecentCallsResponseModel value,
          $Res Function(RecentCallsResponseModel) then) =
      _$RecentCallsResponseModelCopyWithImpl<$Res, RecentCallsResponseModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'session_id') String? sessionId,
      @JsonKey(name: 'session_bill') RecentCallsSessionBillModel? sessionBill,
      @JsonKey(name: 'start_time') String? startTime,
      @JsonKey(name: 'end_time') String? endTime,
      String? duration,
      String? status,
      @JsonKey(name: 'unique_id') String? uniqueId,
      CountryModel? country,
      @JsonKey(name: 'called_number') String? calledNumber});

  $RecentCallsSessionBillModelCopyWith<$Res>? get sessionBill;
  $CountryModelCopyWith<$Res>? get country;
}

/// @nodoc
class _$RecentCallsResponseModelCopyWithImpl<$Res,
        $Val extends RecentCallsResponseModel>
    implements $RecentCallsResponseModelCopyWith<$Res> {
  _$RecentCallsResponseModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RecentCallsResponseModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionId = freezed,
    Object? sessionBill = freezed,
    Object? startTime = freezed,
    Object? endTime = freezed,
    Object? duration = freezed,
    Object? status = freezed,
    Object? uniqueId = freezed,
    Object? country = freezed,
    Object? calledNumber = freezed,
  }) {
    return _then(_value.copyWith(
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionBill: freezed == sessionBill
          ? _value.sessionBill
          : sessionBill // ignore: cast_nullable_to_non_nullable
              as RecentCallsSessionBillModel?,
      startTime: freezed == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as String?,
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as String?,
      duration: freezed == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      uniqueId: freezed == uniqueId
          ? _value.uniqueId
          : uniqueId // ignore: cast_nullable_to_non_nullable
              as String?,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as CountryModel?,
      calledNumber: freezed == calledNumber
          ? _value.calledNumber
          : calledNumber // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of RecentCallsResponseModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RecentCallsSessionBillModelCopyWith<$Res>? get sessionBill {
    if (_value.sessionBill == null) {
      return null;
    }

    return $RecentCallsSessionBillModelCopyWith<$Res>(_value.sessionBill!,
        (value) {
      return _then(_value.copyWith(sessionBill: value) as $Val);
    });
  }

  /// Create a copy of RecentCallsResponseModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CountryModelCopyWith<$Res>? get country {
    if (_value.country == null) {
      return null;
    }

    return $CountryModelCopyWith<$Res>(_value.country!, (value) {
      return _then(_value.copyWith(country: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RecentCallsResponseModelImplCopyWith<$Res>
    implements $RecentCallsResponseModelCopyWith<$Res> {
  factory _$$RecentCallsResponseModelImplCopyWith(
          _$RecentCallsResponseModelImpl value,
          $Res Function(_$RecentCallsResponseModelImpl) then) =
      __$$RecentCallsResponseModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'session_id') String? sessionId,
      @JsonKey(name: 'session_bill') RecentCallsSessionBillModel? sessionBill,
      @JsonKey(name: 'start_time') String? startTime,
      @JsonKey(name: 'end_time') String? endTime,
      String? duration,
      String? status,
      @JsonKey(name: 'unique_id') String? uniqueId,
      CountryModel? country,
      @JsonKey(name: 'called_number') String? calledNumber});

  @override
  $RecentCallsSessionBillModelCopyWith<$Res>? get sessionBill;
  @override
  $CountryModelCopyWith<$Res>? get country;
}

/// @nodoc
class __$$RecentCallsResponseModelImplCopyWithImpl<$Res>
    extends _$RecentCallsResponseModelCopyWithImpl<$Res,
        _$RecentCallsResponseModelImpl>
    implements _$$RecentCallsResponseModelImplCopyWith<$Res> {
  __$$RecentCallsResponseModelImplCopyWithImpl(
      _$RecentCallsResponseModelImpl _value,
      $Res Function(_$RecentCallsResponseModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of RecentCallsResponseModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionId = freezed,
    Object? sessionBill = freezed,
    Object? startTime = freezed,
    Object? endTime = freezed,
    Object? duration = freezed,
    Object? status = freezed,
    Object? uniqueId = freezed,
    Object? country = freezed,
    Object? calledNumber = freezed,
  }) {
    return _then(_$RecentCallsResponseModelImpl(
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionBill: freezed == sessionBill
          ? _value.sessionBill
          : sessionBill // ignore: cast_nullable_to_non_nullable
              as RecentCallsSessionBillModel?,
      startTime: freezed == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as String?,
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as String?,
      duration: freezed == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      uniqueId: freezed == uniqueId
          ? _value.uniqueId
          : uniqueId // ignore: cast_nullable_to_non_nullable
              as String?,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as CountryModel?,
      calledNumber: freezed == calledNumber
          ? _value.calledNumber
          : calledNumber // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RecentCallsResponseModelImpl implements _RecentCallsResponseModel {
  _$RecentCallsResponseModelImpl(
      {@JsonKey(name: 'session_id') this.sessionId,
      @JsonKey(name: 'session_bill') this.sessionBill,
      @JsonKey(name: 'start_time') this.startTime,
      @JsonKey(name: 'end_time') this.endTime,
      this.duration,
      this.status,
      @JsonKey(name: 'unique_id') this.uniqueId,
      this.country,
      @JsonKey(name: 'called_number') this.calledNumber});

  factory _$RecentCallsResponseModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$RecentCallsResponseModelImplFromJson(json);

  @override
  @JsonKey(name: 'session_id')
  final String? sessionId;
  @override
  @JsonKey(name: 'session_bill')
  final RecentCallsSessionBillModel? sessionBill;
  @override
  @JsonKey(name: 'start_time')
  final String? startTime;
  @override
  @JsonKey(name: 'end_time')
  final String? endTime;
  @override
  final String? duration;
  @override
  final String? status;
  @override
  @JsonKey(name: 'unique_id')
  final String? uniqueId;
  @override
  final CountryModel? country;
  @override
  @JsonKey(name: 'called_number')
  final String? calledNumber;

  @override
  String toString() {
    return 'RecentCallsResponseModel(sessionId: $sessionId, sessionBill: $sessionBill, startTime: $startTime, endTime: $endTime, duration: $duration, status: $status, uniqueId: $uniqueId, country: $country, calledNumber: $calledNumber)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RecentCallsResponseModelImpl &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.sessionBill, sessionBill) ||
                other.sessionBill == sessionBill) &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime) &&
            (identical(other.endTime, endTime) || other.endTime == endTime) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.uniqueId, uniqueId) ||
                other.uniqueId == uniqueId) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.calledNumber, calledNumber) ||
                other.calledNumber == calledNumber));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, sessionId, sessionBill,
      startTime, endTime, duration, status, uniqueId, country, calledNumber);

  /// Create a copy of RecentCallsResponseModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RecentCallsResponseModelImplCopyWith<_$RecentCallsResponseModelImpl>
      get copyWith => __$$RecentCallsResponseModelImplCopyWithImpl<
          _$RecentCallsResponseModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RecentCallsResponseModelImplToJson(
      this,
    );
  }
}

abstract class _RecentCallsResponseModel implements RecentCallsResponseModel {
  factory _RecentCallsResponseModel(
          {@JsonKey(name: 'session_id') final String? sessionId,
          @JsonKey(name: 'session_bill')
          final RecentCallsSessionBillModel? sessionBill,
          @JsonKey(name: 'start_time') final String? startTime,
          @JsonKey(name: 'end_time') final String? endTime,
          final String? duration,
          final String? status,
          @JsonKey(name: 'unique_id') final String? uniqueId,
          final CountryModel? country,
          @JsonKey(name: 'called_number') final String? calledNumber}) =
      _$RecentCallsResponseModelImpl;

  factory _RecentCallsResponseModel.fromJson(Map<String, dynamic> json) =
      _$RecentCallsResponseModelImpl.fromJson;

  @override
  @JsonKey(name: 'session_id')
  String? get sessionId;
  @override
  @JsonKey(name: 'session_bill')
  RecentCallsSessionBillModel? get sessionBill;
  @override
  @JsonKey(name: 'start_time')
  String? get startTime;
  @override
  @JsonKey(name: 'end_time')
  String? get endTime;
  @override
  String? get duration;
  @override
  String? get status;
  @override
  @JsonKey(name: 'unique_id')
  String? get uniqueId;
  @override
  CountryModel? get country;
  @override
  @JsonKey(name: 'called_number')
  String? get calledNumber;

  /// Create a copy of RecentCallsResponseModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RecentCallsResponseModelImplCopyWith<_$RecentCallsResponseModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}

RecentCallsSessionBillModel _$RecentCallsSessionBillModelFromJson(
    Map<String, dynamic> json) {
  return _RecentCallsSessionBillModel.fromJson(json);
}

/// @nodoc
mixin _$RecentCallsSessionBillModel {
  String? get amount => throw _privateConstructorUsedError;
  String? get currency => throw _privateConstructorUsedError;
  @JsonKey(name: 'currency_code')
  String? get currencyCode => throw _privateConstructorUsedError;

  /// Serializes this RecentCallsSessionBillModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RecentCallsSessionBillModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RecentCallsSessionBillModelCopyWith<RecentCallsSessionBillModel>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RecentCallsSessionBillModelCopyWith<$Res> {
  factory $RecentCallsSessionBillModelCopyWith(
          RecentCallsSessionBillModel value,
          $Res Function(RecentCallsSessionBillModel) then) =
      _$RecentCallsSessionBillModelCopyWithImpl<$Res,
          RecentCallsSessionBillModel>;
  @useResult
  $Res call(
      {String? amount,
      String? currency,
      @JsonKey(name: 'currency_code') String? currencyCode});
}

/// @nodoc
class _$RecentCallsSessionBillModelCopyWithImpl<$Res,
        $Val extends RecentCallsSessionBillModel>
    implements $RecentCallsSessionBillModelCopyWith<$Res> {
  _$RecentCallsSessionBillModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RecentCallsSessionBillModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = freezed,
    Object? currency = freezed,
    Object? currencyCode = freezed,
  }) {
    return _then(_value.copyWith(
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as String?,
      currency: freezed == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String?,
      currencyCode: freezed == currencyCode
          ? _value.currencyCode
          : currencyCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RecentCallsSessionBillModelImplCopyWith<$Res>
    implements $RecentCallsSessionBillModelCopyWith<$Res> {
  factory _$$RecentCallsSessionBillModelImplCopyWith(
          _$RecentCallsSessionBillModelImpl value,
          $Res Function(_$RecentCallsSessionBillModelImpl) then) =
      __$$RecentCallsSessionBillModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? amount,
      String? currency,
      @JsonKey(name: 'currency_code') String? currencyCode});
}

/// @nodoc
class __$$RecentCallsSessionBillModelImplCopyWithImpl<$Res>
    extends _$RecentCallsSessionBillModelCopyWithImpl<$Res,
        _$RecentCallsSessionBillModelImpl>
    implements _$$RecentCallsSessionBillModelImplCopyWith<$Res> {
  __$$RecentCallsSessionBillModelImplCopyWithImpl(
      _$RecentCallsSessionBillModelImpl _value,
      $Res Function(_$RecentCallsSessionBillModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of RecentCallsSessionBillModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = freezed,
    Object? currency = freezed,
    Object? currencyCode = freezed,
  }) {
    return _then(_$RecentCallsSessionBillModelImpl(
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as String?,
      currency: freezed == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String?,
      currencyCode: freezed == currencyCode
          ? _value.currencyCode
          : currencyCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RecentCallsSessionBillModelImpl
    implements _RecentCallsSessionBillModel {
  _$RecentCallsSessionBillModelImpl(
      {this.amount,
      this.currency,
      @JsonKey(name: 'currency_code') this.currencyCode});

  factory _$RecentCallsSessionBillModelImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$RecentCallsSessionBillModelImplFromJson(json);

  @override
  final String? amount;
  @override
  final String? currency;
  @override
  @JsonKey(name: 'currency_code')
  final String? currencyCode;

  @override
  String toString() {
    return 'RecentCallsSessionBillModel(amount: $amount, currency: $currency, currencyCode: $currencyCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RecentCallsSessionBillModelImpl &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.currencyCode, currencyCode) ||
                other.currencyCode == currencyCode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, amount, currency, currencyCode);

  /// Create a copy of RecentCallsSessionBillModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RecentCallsSessionBillModelImplCopyWith<_$RecentCallsSessionBillModelImpl>
      get copyWith => __$$RecentCallsSessionBillModelImplCopyWithImpl<
          _$RecentCallsSessionBillModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RecentCallsSessionBillModelImplToJson(
      this,
    );
  }
}

abstract class _RecentCallsSessionBillModel
    implements RecentCallsSessionBillModel {
  factory _RecentCallsSessionBillModel(
          {final String? amount,
          final String? currency,
          @JsonKey(name: 'currency_code') final String? currencyCode}) =
      _$RecentCallsSessionBillModelImpl;

  factory _RecentCallsSessionBillModel.fromJson(Map<String, dynamic> json) =
      _$RecentCallsSessionBillModelImpl.fromJson;

  @override
  String? get amount;
  @override
  String? get currency;
  @override
  @JsonKey(name: 'currency_code')
  String? get currencyCode;

  /// Create a copy of RecentCallsSessionBillModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RecentCallsSessionBillModelImplCopyWith<_$RecentCallsSessionBillModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
