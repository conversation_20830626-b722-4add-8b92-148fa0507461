// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'trie_node.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ContactSearchResult {
  List<Contact> get contacts => throw _privateConstructorUsedError;
  String get searchTerm => throw _privateConstructorUsedError;

  /// Create a copy of ContactSearchResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ContactSearchResultCopyWith<ContactSearchResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContactSearchResultCopyWith<$Res> {
  factory $ContactSearchResultCopyWith(
          ContactSearchResult value, $Res Function(ContactSearchResult) then) =
      _$ContactSearchResultCopyWithImpl<$Res, ContactSearchResult>;
  @useResult
  $Res call({List<Contact> contacts, String searchTerm});
}

/// @nodoc
class _$ContactSearchResultCopyWithImpl<$Res, $Val extends ContactSearchResult>
    implements $ContactSearchResultCopyWith<$Res> {
  _$ContactSearchResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ContactSearchResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contacts = null,
    Object? searchTerm = null,
  }) {
    return _then(_value.copyWith(
      contacts: null == contacts
          ? _value.contacts
          : contacts // ignore: cast_nullable_to_non_nullable
              as List<Contact>,
      searchTerm: null == searchTerm
          ? _value.searchTerm
          : searchTerm // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ContactSearchResultImplCopyWith<$Res>
    implements $ContactSearchResultCopyWith<$Res> {
  factory _$$ContactSearchResultImplCopyWith(_$ContactSearchResultImpl value,
          $Res Function(_$ContactSearchResultImpl) then) =
      __$$ContactSearchResultImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<Contact> contacts, String searchTerm});
}

/// @nodoc
class __$$ContactSearchResultImplCopyWithImpl<$Res>
    extends _$ContactSearchResultCopyWithImpl<$Res, _$ContactSearchResultImpl>
    implements _$$ContactSearchResultImplCopyWith<$Res> {
  __$$ContactSearchResultImplCopyWithImpl(_$ContactSearchResultImpl _value,
      $Res Function(_$ContactSearchResultImpl) _then)
      : super(_value, _then);

  /// Create a copy of ContactSearchResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contacts = null,
    Object? searchTerm = null,
  }) {
    return _then(_$ContactSearchResultImpl(
      contacts: null == contacts
          ? _value._contacts
          : contacts // ignore: cast_nullable_to_non_nullable
              as List<Contact>,
      searchTerm: null == searchTerm
          ? _value.searchTerm
          : searchTerm // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ContactSearchResultImpl implements _ContactSearchResult {
  const _$ContactSearchResultImpl(
      {required final List<Contact> contacts, required this.searchTerm})
      : _contacts = contacts;

  final List<Contact> _contacts;
  @override
  List<Contact> get contacts {
    if (_contacts is EqualUnmodifiableListView) return _contacts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_contacts);
  }

  @override
  final String searchTerm;

  @override
  String toString() {
    return 'ContactSearchResult(contacts: $contacts, searchTerm: $searchTerm)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ContactSearchResultImpl &&
            const DeepCollectionEquality().equals(other._contacts, _contacts) &&
            (identical(other.searchTerm, searchTerm) ||
                other.searchTerm == searchTerm));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_contacts), searchTerm);

  /// Create a copy of ContactSearchResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ContactSearchResultImplCopyWith<_$ContactSearchResultImpl> get copyWith =>
      __$$ContactSearchResultImplCopyWithImpl<_$ContactSearchResultImpl>(
          this, _$identity);
}

abstract class _ContactSearchResult implements ContactSearchResult {
  const factory _ContactSearchResult(
      {required final List<Contact> contacts,
      required final String searchTerm}) = _$ContactSearchResultImpl;

  @override
  List<Contact> get contacts;
  @override
  String get searchTerm;

  /// Create a copy of ContactSearchResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ContactSearchResultImplCopyWith<_$ContactSearchResultImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
