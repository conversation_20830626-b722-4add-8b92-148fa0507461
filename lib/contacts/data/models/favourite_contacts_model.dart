import 'package:freezed_annotation/freezed_annotation.dart';

part 'favourite_contacts_model.freezed.dart';
part 'favourite_contacts_model.g.dart';

@freezed
class FavouriteContactsResponseModel with _$FavouriteContactsResponseModel {
  factory FavouriteContactsResponseModel({
    String? id,
    String? name,
    @JsonKey(name: 'telephone') String? phoneNumber,
    @JsonKey(name: 'country') String? countryCode,
  }) = _FavouriteContactsResponseModel;

  factory FavouriteContactsResponseModel.fromJson(Map<String, dynamic> json) =>
      _$FavouriteContactsResponseModelFromJson(json);
}

@freezed
class AddFavouriteContactRequestModel with _$AddFavouriteContactRequestModel {
  factory AddFavouriteContactRequestModel({
    required String name,
    @JsonKey(name: 'telephone') required String phoneNumber,
    @JsonKey(name: 'country') String? countryCode,
  }) = _AddFavouriteContactRequestModel;

  factory AddFavouriteContactRequestModel.fromJson(Map<String, dynamic> json) =>
      _$AddFavouriteContactRequestModelFromJson(json);
}

@freezed
class DeleteFavouriteContactRequestModel
    with _$DeleteFavouriteContactRequestModel {
  factory DeleteFavouriteContactRequestModel({
    @JsonKey(name: 'telephone') required String phoneNumber,
  }) = _DeleteFavouriteContactRequestModel;

  factory DeleteFavouriteContactRequestModel.fromJson(
    Map<String, dynamic> json,
  ) =>
      _$DeleteFavouriteContactRequestModelFromJson(json);
}
