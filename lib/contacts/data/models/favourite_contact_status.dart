enum FavouriteContactsStatus {
  initial,
  inProgress,
  success,
  failure,
}

extension FavouriteContactsStatusExtension on FavouriteContactsStatus {
  bool get isInitial => this == FavouriteContactsStatus.initial;
  bool get isInProgress => this == FavouriteContactsStatus.inProgress;
  bool get isSuccess => this == FavouriteContactsStatus.success;
  bool get isFailure => this == FavouriteContactsStatus.failure;
}
