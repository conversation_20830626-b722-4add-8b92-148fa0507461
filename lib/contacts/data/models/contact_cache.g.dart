// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'contact_cache.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ContactCacheImpl _$$ContactCacheImplFromJson(Map<String, dynamic> json) =>
    _$ContactCacheImpl(
      version: json['version'] as String,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      contacts: const ContactListConverter().fromJson(json['contacts'] as List),
    );

const _$$ContactCacheImplFieldMap = <String, String>{
  'version': 'version',
  'lastUpdated': 'lastUpdated',
  'contacts': 'contacts',
};

Map<String, dynamic> _$$ContactCacheImplToJson(_$ContactCacheImpl instance) =>
    <String, dynamic>{
      'version': instance.version,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
      'contacts': const ContactListConverter().toJson(instance.contacts),
    };
