// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'contact.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ContactModel _$ContactModelFromJson(Map<String, dynamic> json) {
  return _ContactModel.fromJson(json);
}

/// @nodoc
mixin _$ContactModel {
  String get name => throw _privateConstructorUsedError;
  String get phoneNumber => throw _privateConstructorUsedError;
  String? get id => throw _privateConstructorUsedError;
  String? get prefix => throw _privateConstructorUsedError;
  String? get countryCode => throw _privateConstructorUsedError;
  String? get dialingCode => throw _privateConstructorUsedError;
  dynamic get avatarUrl => throw _privateConstructorUsedError;
  String? get email => throw _privateConstructorUsedError;
  CallRatesResponse? get callRateInfo => throw _privateConstructorUsedError;

  /// Serializes this ContactModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ContactModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ContactModelCopyWith<ContactModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContactModelCopyWith<$Res> {
  factory $ContactModelCopyWith(
          ContactModel value, $Res Function(ContactModel) then) =
      _$ContactModelCopyWithImpl<$Res, ContactModel>;
  @useResult
  $Res call(
      {String name,
      String phoneNumber,
      String? id,
      String? prefix,
      String? countryCode,
      String? dialingCode,
      dynamic avatarUrl,
      String? email,
      CallRatesResponse? callRateInfo});

  $CallRatesResponseCopyWith<$Res>? get callRateInfo;
}

/// @nodoc
class _$ContactModelCopyWithImpl<$Res, $Val extends ContactModel>
    implements $ContactModelCopyWith<$Res> {
  _$ContactModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ContactModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? phoneNumber = null,
    Object? id = freezed,
    Object? prefix = freezed,
    Object? countryCode = freezed,
    Object? dialingCode = freezed,
    Object? avatarUrl = freezed,
    Object? email = freezed,
    Object? callRateInfo = freezed,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      prefix: freezed == prefix
          ? _value.prefix
          : prefix // ignore: cast_nullable_to_non_nullable
              as String?,
      countryCode: freezed == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String?,
      dialingCode: freezed == dialingCode
          ? _value.dialingCode
          : dialingCode // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarUrl: freezed == avatarUrl
          ? _value.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as dynamic,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      callRateInfo: freezed == callRateInfo
          ? _value.callRateInfo
          : callRateInfo // ignore: cast_nullable_to_non_nullable
              as CallRatesResponse?,
    ) as $Val);
  }

  /// Create a copy of ContactModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CallRatesResponseCopyWith<$Res>? get callRateInfo {
    if (_value.callRateInfo == null) {
      return null;
    }

    return $CallRatesResponseCopyWith<$Res>(_value.callRateInfo!, (value) {
      return _then(_value.copyWith(callRateInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ContactModelImplCopyWith<$Res>
    implements $ContactModelCopyWith<$Res> {
  factory _$$ContactModelImplCopyWith(
          _$ContactModelImpl value, $Res Function(_$ContactModelImpl) then) =
      __$$ContactModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String name,
      String phoneNumber,
      String? id,
      String? prefix,
      String? countryCode,
      String? dialingCode,
      dynamic avatarUrl,
      String? email,
      CallRatesResponse? callRateInfo});

  @override
  $CallRatesResponseCopyWith<$Res>? get callRateInfo;
}

/// @nodoc
class __$$ContactModelImplCopyWithImpl<$Res>
    extends _$ContactModelCopyWithImpl<$Res, _$ContactModelImpl>
    implements _$$ContactModelImplCopyWith<$Res> {
  __$$ContactModelImplCopyWithImpl(
      _$ContactModelImpl _value, $Res Function(_$ContactModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ContactModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? phoneNumber = null,
    Object? id = freezed,
    Object? prefix = freezed,
    Object? countryCode = freezed,
    Object? dialingCode = freezed,
    Object? avatarUrl = freezed,
    Object? email = freezed,
    Object? callRateInfo = freezed,
  }) {
    return _then(_$ContactModelImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      prefix: freezed == prefix
          ? _value.prefix
          : prefix // ignore: cast_nullable_to_non_nullable
              as String?,
      countryCode: freezed == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String?,
      dialingCode: freezed == dialingCode
          ? _value.dialingCode
          : dialingCode // ignore: cast_nullable_to_non_nullable
              as String?,
      avatarUrl: freezed == avatarUrl
          ? _value.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as dynamic,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      callRateInfo: freezed == callRateInfo
          ? _value.callRateInfo
          : callRateInfo // ignore: cast_nullable_to_non_nullable
              as CallRatesResponse?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ContactModelImpl implements _ContactModel {
  const _$ContactModelImpl(
      {required this.name,
      required this.phoneNumber,
      this.id,
      this.prefix,
      this.countryCode,
      this.dialingCode,
      this.avatarUrl,
      this.email,
      this.callRateInfo});

  factory _$ContactModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ContactModelImplFromJson(json);

  @override
  final String name;
  @override
  final String phoneNumber;
  @override
  final String? id;
  @override
  final String? prefix;
  @override
  final String? countryCode;
  @override
  final String? dialingCode;
  @override
  final dynamic avatarUrl;
  @override
  final String? email;
  @override
  final CallRatesResponse? callRateInfo;

  @override
  String toString() {
    return 'ContactModel(name: $name, phoneNumber: $phoneNumber, id: $id, prefix: $prefix, countryCode: $countryCode, dialingCode: $dialingCode, avatarUrl: $avatarUrl, email: $email, callRateInfo: $callRateInfo)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ContactModelImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.prefix, prefix) || other.prefix == prefix) &&
            (identical(other.countryCode, countryCode) ||
                other.countryCode == countryCode) &&
            (identical(other.dialingCode, dialingCode) ||
                other.dialingCode == dialingCode) &&
            const DeepCollectionEquality().equals(other.avatarUrl, avatarUrl) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.callRateInfo, callRateInfo) ||
                other.callRateInfo == callRateInfo));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      name,
      phoneNumber,
      id,
      prefix,
      countryCode,
      dialingCode,
      const DeepCollectionEquality().hash(avatarUrl),
      email,
      callRateInfo);

  /// Create a copy of ContactModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ContactModelImplCopyWith<_$ContactModelImpl> get copyWith =>
      __$$ContactModelImplCopyWithImpl<_$ContactModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ContactModelImplToJson(
      this,
    );
  }
}

abstract class _ContactModel implements ContactModel {
  const factory _ContactModel(
      {required final String name,
      required final String phoneNumber,
      final String? id,
      final String? prefix,
      final String? countryCode,
      final String? dialingCode,
      final dynamic avatarUrl,
      final String? email,
      final CallRatesResponse? callRateInfo}) = _$ContactModelImpl;

  factory _ContactModel.fromJson(Map<String, dynamic> json) =
      _$ContactModelImpl.fromJson;

  @override
  String get name;
  @override
  String get phoneNumber;
  @override
  String? get id;
  @override
  String? get prefix;
  @override
  String? get countryCode;
  @override
  String? get dialingCode;
  @override
  dynamic get avatarUrl;
  @override
  String? get email;
  @override
  CallRatesResponse? get callRateInfo;

  /// Create a copy of ContactModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ContactModelImplCopyWith<_$ContactModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
