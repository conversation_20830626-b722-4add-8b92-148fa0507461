import 'package:fast_contacts/fast_contacts.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:froggytalk/contacts/contacts.dart';

part 'contact_cache.freezed.dart';
part 'contact_cache.g.dart';

@freezed
class ContactCache with _$ContactCache {
  factory ContactCache({
    required String version,
    required DateTime lastUpdated,
    @ContactListConverter() required List<Contact> contacts,
  }) = _ContactCache;

  factory ContactCache.fromJson(Map<String, dynamic> json) =>
      _$ContactCacheFromJson(json);
}
