import 'package:fast_contacts/fast_contacts.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

class ContactListConverter
    implements JsonConverter<List<Contact>, List<dynamic>> {
  const ContactListConverter();

  @override
  List<Contact> fromJson(List<dynamic> json) {
    return json
        .map((e) => Contact.fromMap(e as Map<String, dynamic>))
        .toList(growable: true);
  }

  @override
  List<dynamic> toJson(List<Contact> contacts) {
    return contacts.map((e) => e.toMap()).toList();
  }
}
