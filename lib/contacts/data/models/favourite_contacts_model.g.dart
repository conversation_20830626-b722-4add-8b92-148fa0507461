// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'favourite_contacts_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$FavouriteContactsResponseModelImpl
    _$$FavouriteContactsResponseModelImplFromJson(Map<String, dynamic> json) =>
        _$FavouriteContactsResponseModelImpl(
          id: json['id'] as String?,
          name: json['name'] as String?,
          phoneNumber: json['telephone'] as String?,
          countryCode: json['country'] as String?,
        );

const _$$FavouriteContactsResponseModelImplFieldMap = <String, String>{
  'id': 'id',
  'name': 'name',
  'phoneNumber': 'telephone',
  'countryCode': 'country',
};

Map<String, dynamic> _$$FavouriteContactsResponseModelImplToJson(
        _$FavouriteContactsResponseModelImpl instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'id': value,
      if (instance.name case final value?) 'name': value,
      if (instance.phoneNumber case final value?) 'telephone': value,
      if (instance.countryCode case final value?) 'country': value,
    };

_$AddFavouriteContactRequestModelImpl
    _$$AddFavouriteContactRequestModelImplFromJson(Map<String, dynamic> json) =>
        _$AddFavouriteContactRequestModelImpl(
          name: json['name'] as String,
          phoneNumber: json['telephone'] as String,
          countryCode: json['country'] as String?,
        );

const _$$AddFavouriteContactRequestModelImplFieldMap = <String, String>{
  'name': 'name',
  'phoneNumber': 'telephone',
  'countryCode': 'country',
};

Map<String, dynamic> _$$AddFavouriteContactRequestModelImplToJson(
        _$AddFavouriteContactRequestModelImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'telephone': instance.phoneNumber,
      if (instance.countryCode case final value?) 'country': value,
    };

_$DeleteFavouriteContactRequestModelImpl
    _$$DeleteFavouriteContactRequestModelImplFromJson(
            Map<String, dynamic> json) =>
        _$DeleteFavouriteContactRequestModelImpl(
          phoneNumber: json['telephone'] as String,
        );

const _$$DeleteFavouriteContactRequestModelImplFieldMap = <String, String>{
  'phoneNumber': 'telephone',
};

Map<String, dynamic> _$$DeleteFavouriteContactRequestModelImplToJson(
        _$DeleteFavouriteContactRequestModelImpl instance) =>
    <String, dynamic>{
      'telephone': instance.phoneNumber,
    };
