// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'contact_cache.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ContactCache _$ContactCacheFromJson(Map<String, dynamic> json) {
  return _ContactCache.fromJson(json);
}

/// @nodoc
mixin _$ContactCache {
  String get version => throw _privateConstructorUsedError;
  DateTime get lastUpdated => throw _privateConstructorUsedError;
  @ContactListConverter()
  List<Contact> get contacts => throw _privateConstructorUsedError;

  /// Serializes this ContactCache to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ContactCache
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ContactCacheCopyWith<ContactCache> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ContactCacheCopyWith<$Res> {
  factory $ContactCacheCopyWith(
          ContactCache value, $Res Function(ContactCache) then) =
      _$ContactCacheCopyWithImpl<$Res, ContactCache>;
  @useResult
  $Res call(
      {String version,
      DateTime lastUpdated,
      @ContactListConverter() List<Contact> contacts});
}

/// @nodoc
class _$ContactCacheCopyWithImpl<$Res, $Val extends ContactCache>
    implements $ContactCacheCopyWith<$Res> {
  _$ContactCacheCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ContactCache
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? version = null,
    Object? lastUpdated = null,
    Object? contacts = null,
  }) {
    return _then(_value.copyWith(
      version: null == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as String,
      lastUpdated: null == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime,
      contacts: null == contacts
          ? _value.contacts
          : contacts // ignore: cast_nullable_to_non_nullable
              as List<Contact>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ContactCacheImplCopyWith<$Res>
    implements $ContactCacheCopyWith<$Res> {
  factory _$$ContactCacheImplCopyWith(
          _$ContactCacheImpl value, $Res Function(_$ContactCacheImpl) then) =
      __$$ContactCacheImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String version,
      DateTime lastUpdated,
      @ContactListConverter() List<Contact> contacts});
}

/// @nodoc
class __$$ContactCacheImplCopyWithImpl<$Res>
    extends _$ContactCacheCopyWithImpl<$Res, _$ContactCacheImpl>
    implements _$$ContactCacheImplCopyWith<$Res> {
  __$$ContactCacheImplCopyWithImpl(
      _$ContactCacheImpl _value, $Res Function(_$ContactCacheImpl) _then)
      : super(_value, _then);

  /// Create a copy of ContactCache
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? version = null,
    Object? lastUpdated = null,
    Object? contacts = null,
  }) {
    return _then(_$ContactCacheImpl(
      version: null == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as String,
      lastUpdated: null == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime,
      contacts: null == contacts
          ? _value._contacts
          : contacts // ignore: cast_nullable_to_non_nullable
              as List<Contact>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ContactCacheImpl implements _ContactCache {
  _$ContactCacheImpl(
      {required this.version,
      required this.lastUpdated,
      @ContactListConverter() required final List<Contact> contacts})
      : _contacts = contacts;

  factory _$ContactCacheImpl.fromJson(Map<String, dynamic> json) =>
      _$$ContactCacheImplFromJson(json);

  @override
  final String version;
  @override
  final DateTime lastUpdated;
  final List<Contact> _contacts;
  @override
  @ContactListConverter()
  List<Contact> get contacts {
    if (_contacts is EqualUnmodifiableListView) return _contacts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_contacts);
  }

  @override
  String toString() {
    return 'ContactCache(version: $version, lastUpdated: $lastUpdated, contacts: $contacts)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ContactCacheImpl &&
            (identical(other.version, version) || other.version == version) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated) &&
            const DeepCollectionEquality().equals(other._contacts, _contacts));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, version, lastUpdated,
      const DeepCollectionEquality().hash(_contacts));

  /// Create a copy of ContactCache
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ContactCacheImplCopyWith<_$ContactCacheImpl> get copyWith =>
      __$$ContactCacheImplCopyWithImpl<_$ContactCacheImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ContactCacheImplToJson(
      this,
    );
  }
}

abstract class _ContactCache implements ContactCache {
  factory _ContactCache(
          {required final String version,
          required final DateTime lastUpdated,
          @ContactListConverter() required final List<Contact> contacts}) =
      _$ContactCacheImpl;

  factory _ContactCache.fromJson(Map<String, dynamic> json) =
      _$ContactCacheImpl.fromJson;

  @override
  String get version;
  @override
  DateTime get lastUpdated;
  @override
  @ContactListConverter()
  List<Contact> get contacts;

  /// Create a copy of ContactCache
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ContactCacheImplCopyWith<_$ContactCacheImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
