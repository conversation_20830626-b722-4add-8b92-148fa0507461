import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:froggy_icons/froggy_icons.dart';
import 'package:froggytalk/l10n/l10n.dart';

enum CallLogType {
  outgoing,
  incoming,
  missed,
  busy,
  answered,
  cancel,
  unavailable,
}

extension CallLogTypeExtension on CallLogType {
  String? get name {
    switch (this) {
      case CallLogType.outgoing:
        return 'Outgoing';
      case CallLogType.incoming:
        return 'Incoming';
      case CallLogType.missed:
        return 'Missed';
      case CallLogType.busy:
        return 'Busy';
      case CallLogType.answered:
        return 'Answered';
      case CallLogType.cancel:
        return 'Cancel';
      case CallLogType.unavailable:
        return 'Unavailable';
    }
  }

  Widget get icon {
    switch (this) {
      case CallLogType.answered:
      case CallLogType.outgoing:
        return FroggyIconsList.callOutgoing.toWidget(
          width: 15,
          height: 15,
          color: FroggyColors.primary,
        );
      case CallLogType.incoming:
        return FroggyIconsList.phoneSolid.toWidget(
          width: 15,
          height: 15,
          color: FroggyColors.info,
        );
      case CallLogType.missed:
      case CallLogType.unavailable:
      case CallLogType.cancel:
      case CallLogType.busy:
        return FroggyIconsList.callMissed.toWidget(
          width: 15,
          height: 15,
          color: FroggyColors.froggyError,
        );
    }
  }

  Color? get color {
    switch (this) {
      case CallLogType.answered:
      case CallLogType.outgoing:
        return FroggyColors.primary;
      case CallLogType.incoming:
        return FroggyColors.froggyCream;
      case CallLogType.missed:
      case CallLogType.unavailable:
      case CallLogType.cancel:
      case CallLogType.busy:
        return FroggyColors.froggyRed;
    }
  }

  String get callTypeDescription {
    switch (this) {
      case CallLogType.answered:
      case CallLogType.outgoing:
        return 'Outgoing call';
      case CallLogType.incoming:
        return 'Incoming call';
      case CallLogType.missed:
        return 'Missed call';
      case CallLogType.busy:
        return 'Busy';
      case CallLogType.cancel:
        return 'Cancelled call';
      case CallLogType.unavailable:
        return 'User Unavailable';
    }
  }

  // get localized name
  String getLocalizedName(BuildContext context) {
    switch (this) {
      case CallLogType.answered:
        return context.l10n.callLogTypeAnswered;
      case CallLogType.outgoing:
        return context.l10n.callLogTypeOutgoing;
      case CallLogType.incoming:
        return context.l10n.callLogTypeIncoming;
      case CallLogType.missed:
        return context.l10n.callLogTypeMissed;
      case CallLogType.busy:
        return context.l10n.callLogTypeBusy;
      case CallLogType.cancel:
        return context.l10n.callLogTypeCancel;
      case CallLogType.unavailable:
        return context.l10n.callLogTypeUnavailable;
    }
  }

  String getLocalizedDescription(BuildContext context) {
    switch (this) {
      case CallLogType.answered:
        return context.l10n.callTypeAnswered;
      case CallLogType.outgoing:
        return context.l10n.callLogTypeOutgoing;
      case CallLogType.incoming:
        return context.l10n.callLogTypeIncoming;
      case CallLogType.missed:
        return context.l10n.callLogTypeMissed;
      case CallLogType.busy:
        return context.l10n.callTypeBusy;
      case CallLogType.cancel:
        return context.l10n.callTypeCancel;
      case CallLogType.unavailable:
        return context.l10n.callTypeUnavailable;
    }
  }
}

extension CallLogTypeExtensionList on String {
  CallLogType get toCallLogType {
    switch (toLowerCase()) {
      case 'incoming':
        return CallLogType.incoming;
      case 'busy':
        return CallLogType.busy;
      case 'missed':
        return CallLogType.missed;
      case 'unavailable':
        return CallLogType.unavailable;
      case 'cancel':
        return CallLogType.cancel;
      case 'answered':
        return CallLogType.answered;
      default:
        return CallLogType.outgoing;
    }
  }
}
