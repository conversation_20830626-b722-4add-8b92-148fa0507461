// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recent_calls_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RecentCallsResponseModelImpl _$$RecentCallsResponseModelImplFromJson(
        Map<String, dynamic> json) =>
    _$RecentCallsResponseModelImpl(
      sessionId: json['session_id'] as String?,
      sessionBill: json['session_bill'] == null
          ? null
          : RecentCallsSessionBillModel.fromJson(
              json['session_bill'] as Map<String, dynamic>),
      startTime: json['start_time'] as String?,
      endTime: json['end_time'] as String?,
      duration: json['duration'] as String?,
      status: json['status'] as String?,
      uniqueId: json['unique_id'] as String?,
      country: json['country'] == null
          ? null
          : CountryModel.fromJson(json['country'] as Map<String, dynamic>),
      calledNumber: json['called_number'] as String?,
    );

const _$$RecentCallsResponseModelImplFieldMap = <String, String>{
  'sessionId': 'session_id',
  'sessionBill': 'session_bill',
  'startTime': 'start_time',
  'endTime': 'end_time',
  'duration': 'duration',
  'status': 'status',
  'uniqueId': 'unique_id',
  'country': 'country',
  'calledNumber': 'called_number',
};

Map<String, dynamic> _$$RecentCallsResponseModelImplToJson(
        _$RecentCallsResponseModelImpl instance) =>
    <String, dynamic>{
      if (instance.sessionId case final value?) 'session_id': value,
      if (instance.sessionBill?.toJson() case final value?)
        'session_bill': value,
      if (instance.startTime case final value?) 'start_time': value,
      if (instance.endTime case final value?) 'end_time': value,
      if (instance.duration case final value?) 'duration': value,
      if (instance.status case final value?) 'status': value,
      if (instance.uniqueId case final value?) 'unique_id': value,
      if (instance.country?.toJson() case final value?) 'country': value,
      if (instance.calledNumber case final value?) 'called_number': value,
    };

_$RecentCallsSessionBillModelImpl _$$RecentCallsSessionBillModelImplFromJson(
        Map<String, dynamic> json) =>
    _$RecentCallsSessionBillModelImpl(
      amount: json['amount'] as String?,
      currency: json['currency'] as String?,
      currencyCode: json['currency_code'] as String?,
    );

const _$$RecentCallsSessionBillModelImplFieldMap = <String, String>{
  'amount': 'amount',
  'currency': 'currency',
  'currencyCode': 'currency_code',
};

Map<String, dynamic> _$$RecentCallsSessionBillModelImplToJson(
        _$RecentCallsSessionBillModelImpl instance) =>
    <String, dynamic>{
      if (instance.amount case final value?) 'amount': value,
      if (instance.currency case final value?) 'currency': value,
      if (instance.currencyCode case final value?) 'currency_code': value,
    };
