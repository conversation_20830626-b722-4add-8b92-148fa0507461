import 'package:countries/countries.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'recent_calls_model.freezed.dart';
part 'recent_calls_model.g.dart';

@freezed
class RecentCallsResponseModel with _$RecentCallsResponseModel {
  factory RecentCallsResponseModel({
    @JsonKey(name: 'session_id') String? sessionId,
    @J<PERSON><PERSON><PERSON>(name: 'session_bill') RecentCallsSessionBillModel? sessionBill,
    @Json<PERSON>ey(name: 'start_time') String? startTime,
    @JsonKey(name: 'end_time') String? endTime,
    String? duration,
    String? status,
    @J<PERSON><PERSON>ey(name: 'unique_id') String? uniqueId,
    CountryModel? country,
    @Json<PERSON>ey(name: 'called_number') String? calledNumber,
  }) = _RecentCallsResponseModel;

  factory RecentCallsResponseModel.fromJson(Map<String, dynamic> json) =>
      _$RecentCallsResponseModelFromJson(json);
}

@freezed
class RecentCallsSessionBillModel with _$RecentCallsSessionBillModel {
  factory RecentCallsSessionBillModel({
    String? amount,
    String? currency,
    @Json<PERSON>ey(name: 'currency_code') String? currencyCode,
  }) = _RecentCallsSessionBillModel;

  factory RecentCallsSessionBillModel.fromJson(Map<String, dynamic> json) =>
      _$RecentCallsSessionBillModelFromJson(json);
}
