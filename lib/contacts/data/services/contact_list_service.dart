import 'dart:async';
import 'dart:collection';
import 'dart:convert';
import 'dart:io';

import 'package:fast_contacts/fast_contacts.dart';
import 'package:flutter/material.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:utils/utils.dart';

class ContactListService {
  static const _cacheKey = 'contact_cache';
  static const _cacheVersion = '1.0.0';

  List<Contact> _contacts = [];

  /// A set of favorite contact IDs.
  final HashSet<FavouriteContactsResponseModel> _favoritedContactIds =
      HashSet<FavouriteContactsResponseModel>();

  bool _isInitialized = false;
  final ContactListTrieNode _namesTrie = ContactListTrieNode();
  bool _permissionGranted = false;
  final ContactListTrieNode _phonesTrie = ContactListTrieNode();
  final _storage = FroggyLocalStorage.getInstance();

  bool get isPermissionGranted => _permissionGranted;

  static const int _batchSize = 1000;

  /// Public getter for contacts
  List<Contact> get contacts => List.unmodifiable(_contacts);

  /// Public getter for permission status
  bool get hasPermission => _permissionGranted;

  /// Initialize the contact service and optionally request permissions
  Future<void> ensureInitialized({bool requestForPermission = false}) async {
    try {
      await _handlePermissions(requestForPermission);

      if (_permissionGranted && _contacts.isEmpty) {
        await _loadContactsFromDevice();
      } else {
        await _loadFromCache();
      }
    } catch (e, stackTrace) {
      _handleError('Failed to initialize contact service', e, stackTrace);
      // rethrow;
    }
  }

  /// Handle contact permissions
  Future<void> _handlePermissions(bool requestForPermission) async {
    final status = await Permission.contacts.status;

    switch (status) {
      case PermissionStatus.denied:
        _permissionGranted = false;
        if (requestForPermission) {
          final result = await Permission.contacts.request();
          _permissionGranted = result.isGranted;
        }
      case PermissionStatus.granted:
        _permissionGranted = true;
      case PermissionStatus.permanentlyDenied:
      case PermissionStatus.provisional:
      case PermissionStatus.restricted:
      case PermissionStatus.limited:
        _permissionGranted = false;
    }
  }

  /// Load contacts from device in batches
  Future<void> _loadContactsFromDevice() async {
    final allContacts = await FastContacts.getAllContacts();
    await _processBatches(allContacts);
    await _buildSearchTries();
  }

  /// Process contacts in batches
  Future<void> _processBatches(List<Contact> allContacts) async {
    for (var i = 0; i < allContacts.length; i += _batchSize) {
      final end = (i + _batchSize < allContacts.length)
          ? i + _batchSize
          : allContacts.length;

      await _processContactBatch(allContacts.sublist(i, end));
    }
  }

  /// Process a single batch of contacts
  Future<void> _processContactBatch(List<Contact> contactBatch) async {
    _contacts.addAll(
      contactBatch.where((contact) => contact.phones.isNotEmpty),
    );

    // Allow UI to update between batches
    await Future<void>.delayed(const Duration(milliseconds: 100));
  }

  /// Handle errors with proper logging
  void _handleError(String message, Object error, StackTrace stackTrace) {
    debugPrint('$message: $error\n$stackTrace');
  }

  /// Public method to refresh contacts
  Future<void> refreshContacts() async {
    if (!_permissionGranted) return;

    _contacts.clear();
    await _loadContactsFromDevice();
  }

  /// Public method to check if contacts need refresh
  Future<bool> needsRefresh() async {
    if (!_permissionGranted) return false;

    final deviceContacts = await FastContacts.getAllContacts();
    return deviceContacts.length != _contacts.length;
  }

  Future<void> clearCache() async {
    try {
      _storage.remove(_cacheKey);
      debugPrint('Contact cache cleared');
    } catch (e) {
      debugPrint('Failed to clear contact cache: $e');
    }
  }

  List<Contact> loadContacts({bool requestForPermission = false}) {
    if (!_isInitialized) {
      unawaited(
        ensureInitialized(requestForPermission: requestForPermission)
            .then((_) => _isInitialized = true),
      );
    }

    // if (!_isInitialized && requestForPermission) {
    //   ensureInitialized(requestForPermission: requestForPermission)
    //       .then((_) => _isInitialized = true);
    // }

    return List.unmodifiable(_contacts); // Return immutable copy
  }

  @visibleForTesting
  void reset() {
    _contacts.clear();
    _isInitialized = false;
    _permissionGranted = false;
  }

  // Debug helpers
  void debugPrintTrieStats() {
    assert(
      () {
        final nameNodes = _countNodes(_namesTrie);
        final phoneNodes = _countNodes(_phonesTrie);
        debugPrint('Trie Statistics:');
        debugPrint('Name trie nodes: $nameNodes');
        debugPrint('Phone trie nodes: $phoneNodes');
        return true;
      }(),
      'Failed to print trie statistics',
    );
  }

  /// Adds a contact to the list of favorite contacts.
  ///
  /// This method attempts to add the provided [contactId] to the list of
  /// favorite contact IDs. If an error occurs during this process, it catches
  /// the exception and prints an error message to the debug console.
  ///
  /// [contactId] - The contact to be added to the list of favorite contacts.
  void addFavoriteContact(FavouriteContactsResponseModel contactId) {
    try {
      _favoritedContactIds.add(contactId);
    } catch (e) {
      debugPrint('Error adding favorite contact: $e');
    }
  }

  /// Removes a contact from the list of favorite contacts.
  ///
  /// This method attempts to remove the specified contact from the list of
  /// favorite contacts. If an error occurs during the removal process, it
  /// catches the exception and prints an error message to the debug console.
  ///
  /// [phoneNumber] The contact to be removed from favorite contacts list.
  ///
  /// Example usage:
  /// ```dart
  /// final contact = FavouriteContactsResponseModel(phoneNumber: '087012121');
  /// removeFavoriteContact(contact);
  /// ```
  void removeFavoriteContact(String phoneNumber) {
    try {
      final contactToRemove = _favoritedContactIds
          .firstWhere((contact) => contact.phoneNumber == phoneNumber);
      _favoritedContactIds.remove(contactToRemove);
    } catch (e) {
      debugPrint('Error removing favorite contact: $e');
    }
  }

  /// Checks if the given contact is a favorite.
  ///
  /// This method attempts to determine if the provided contact ID is in the
  /// list of favorited contact IDs. If an error occurs during this process,
  /// it catches the exception, logs an error message, and returns `false`.
  ///
  /// - Parameter phoneNumber: The phoneNumber to check.
  /// - Returns: `true` if the contact is a favorite, `false` otherwise.
  bool isFavoriteContact(String phoneNumber) {
    // quit early if the list is empty
    if (_favoritedContactIds.isEmpty) {
      return false;
    }

    // quit if the phone number is empty
    if (phoneNumber.isEmpty) {
      return false;
    }

    try {
      return _favoritedContactIds.any(
        (contact) => contact.phoneNumber == phoneNumber,
      );
    } catch (e) {
      debugPrint('Error checking if contact is favorite: $e');
      return false;
    }
  }

  ContactSearchResult searchContacts(String query) {
    // if (query.isEmpty) {
    //   _loadFromCache();
    return ContactSearchResult(contacts: _contacts, searchTerm: query);
    // }

    // if (query.length <= 2) {
    //   _loadFromCache();
    //   return ContactSearchResult(contacts: _contacts, searchTerm: query);
    // }

    // final nameResults = _namesTrie.search(query);
    // // final phoneResults = _phonesTrie.search(query, isPhoneNumber: true);

    // // Merge results and remove duplicates
    // final allResults = {
    //   ...nameResults,
    //   // ...phoneResults
    // }.toList()
    //   // Sort by relevance (exact matches first, then partial matches)
    //   ..sort((a, b) {
    //     final aNameMatch =
    //         a.displayName.toLowerCase().contains(query.toLowerCase());
    //     final bNameMatch =
    //         b.displayName.toLowerCase().contains(query.toLowerCase());

    //     if (aNameMatch && !bNameMatch) return -1;
    //     if (!aNameMatch && bNameMatch) return 1;
    //     return a.displayName.compareTo(b.displayName);
    //   });

    // return ContactSearchResult(
    //   contacts: allResults,
    //   searchTerm: query,
    // );
  }

  Future<void> _buildSearchTries() async {
    for (final contact in _contacts) {
      // Insert by name
      if (contact.displayName.isNotEmpty) {
        _namesTrie.insert(contact.displayName, contact);
      }

      // Insert by phone numbers
      for (final phone in contact.phones) {
        _phonesTrie.insert(phone.number, contact, isPhoneNumber: true);
      }
    }
  }

  // ignore: unused_element
  Future<void> _cacheContacts() async {
    try {
      final cache = ContactCache(
        version: _cacheVersion,
        lastUpdated: DateTime.now(),
        contacts: _contacts,
      );

      final json = jsonEncode(cache.toJson());
      final compressed = GZipCodec().encode(utf8.encode(json));

      _storage.set<String>(
        _cacheKey,
        base64Encode(compressed),
      );

      debugPrint('Contacts cached successfully: ${_contacts.length} items');
    } catch (e, stack) {
      debugPrint('Failed to cache contacts: $e\n$stack');
    }
  }

  // ignore: unused_element
  Future<void> _loadFromCache() async {
    try {
      final cached = _storage.get<String>(_cacheKey);
      if (cached == null) return;

      final compressed = base64Decode(cached);
      final json = utf8.decode(GZipCodec().decode(compressed));
      final cache =
          ContactCache.fromJson(jsonDecode(json) as Map<String, dynamic>);

      // Validate cache freshness (24 hours)
      if (DateTime.now().difference(cache.lastUpdated).inHours > 24) {
        debugPrint('Cache expired, refreshing contacts');
        return;
      }

      if (cache.version != _cacheVersion) {
        debugPrint('Cache version mismatch, refreshing contacts');
        return;
      }

      _contacts = cache.contacts;
      debugPrint('Loaded ${_contacts.length} contacts from cache');
    } catch (e, stack) {
      debugPrint('Failed to load contacts from cache: $e\n$stack');
      // Silently fail and let fresh load occur
    }
  }

  int _countNodes(ContactListTrieNode node) {
    var count = 1;
    for (final child in node.children.values) {
      count += _countNodes(child);
    }
    return count;
  }
}
