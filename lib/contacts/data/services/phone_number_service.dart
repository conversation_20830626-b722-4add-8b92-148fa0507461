import 'dart:collection';

import 'package:countries/countries.dart';
import 'package:dlibphonenumber/dlibphonenumber.dart';
import 'package:flutter/material.dart';
import 'package:froggytalk/app/app.dart';

// Cache statistics tracking
class _CacheStats {
  int hits = 0;
  int misses = 0;
  int evictions = 0;
  final stopwatch = Stopwatch();

  void trackLookup(bool isHit) {
    isHit ? hits++ : misses++;
  }

  String get hitRate => '${(hits / (hits + misses) * 100).toStringAsFixed(2)}%';
}

class PhoneNumberService {
  static const _maxCacheSize = 10000;
  static final _countryCache = <String, CountryModel>{};
  static final _lruQueue = Queue<String>();
  static final _cacheStats = _CacheStats();

  /// Lazy initialization to prevent "Bad element" errors during app startup
  PhoneNumberUtil get _phoneUtil {
    try {
      return PhoneNumberUtil.instance;
    } catch (e) {
      // If libphonenumber fails to initialize, we'll return a dummy instance
      // This prevents "Bad element" errors from propagating
      throw Exception('PhoneNumberUtil not available: $e');
    }
  }

  /// Country code in ISO 3166-1 alpha-2 format (e.g., 'US', 'GB', 'NG')
  /// This property is used for phone number formatting and validation
  @protected
  String? _countryCode = AppInstance.getInstance().geoIPInfo.countryCode;

  /// Gets the current country code
  ///
  /// Returns a two-letter ISO country code
  String get countryCode => _countryCode ?? 'CH';

  /// Sets the country code with validation
  ///
  /// [value] must be a valid ISO 3166-1 alpha-2 country code
  /// Throws [ArgumentError] if invalid code provided
  set countryCode(String value) {
    assert(value.isNotEmpty, 'Country code cannot be empty');
    assert(value.length == 2, 'Country code must be 2 characters');

    _countryCode = value.toUpperCase();
  }

  PhoneNumber? parse(
    String phoneNumber, {
    String? countryCode,
  }) {
    try {
      // Validate input before processing
      if (phoneNumber.trim().isEmpty) {
        return null;
      }

      final number =
          phoneNumber.replaceAll(RegExp(r'[\s\-()]'), '').startsWith('00')
              ? phoneNumber.replaceFirst('00', '+')
              : phoneNumber;
      
      // Safe access to phoneUtil with additional error handling
      final phoneUtil = _phoneUtil;
      final phoneFormat = phoneUtil.parse(number, countryCode ?? _countryCode);

      return phoneFormat;
    } on NumberParseException catch (_) {
      // Silently handle NumberParseException to prevent "Bad element" errors
      return null;
    } catch (e) {
      // Catch any other exceptions that might cause "Bad element" errors
      // This specifically handles libphonenumber internal errors like "Bad element"
      return null;
    }
  }

  String? format(String phoneNumber, {String? country}) {
    try {
      // Validate input
      if (phoneNumber.trim().isEmpty) {
        return null;
      }

      final number = parse(phoneNumber, countryCode: country);

      if (number == null) {
        return null;
      }

      final phoneUtil = _phoneUtil;
      return phoneUtil.format(number, PhoneNumberFormat.international);
    } catch (e) {
      // Silently handle any formatting errors to prevent "Bad element" errors
      // This includes libphonenumber internal errors and metadata issues
      return null;
    }
  }

  String? getCountryCallingCode(String phoneNumber, {String? country}) {
    try {
      if (phoneNumber.trim().isEmpty) {
        return null;
      }

      final number = parse(phoneNumber, countryCode: country);
      return number?.countryCode.toString();
    } catch (e) {
      // Silently handle any errors to prevent "Bad element" exceptions
      return null;
    }
  }

  String? getCountryCode(String phoneNumber, {String? country}) {
    try {
      if (phoneNumber.trim().isEmpty) {
        return null;
      }

      final number = parse(phoneNumber, countryCode: country);
      final phoneUtil = _phoneUtil;
      return phoneUtil.getRegionCodeForNumber(number);
    } catch (e) {
      // Silently handle any errors to prevent "Bad element" exceptions
      return null;
    }
  }

  // LRU cache management
  void _updateLRU(String key) {
    _lruQueue
      ..remove(key)
      ..addFirst(key);
  }

  void _evictIfNeeded() {
    while (_countryCache.length >= _maxCacheSize) {
      final oldest = _lruQueue.removeLast();
      _countryCache.remove(oldest);
      _cacheStats.evictions++;
    }
  }

  // Enhanced country lookup with caching
  CountryModel? findCountryFromNumber(String phoneNumber, {String? country}) {
    _cacheStats.stopwatch.reset();
    _cacheStats.stopwatch.start();

    // Validate input
    if (phoneNumber.trim().isEmpty) {
      return null;
    }

    // Generate cache key
    final cacheKey = '$phoneNumber${country ?? ''}';

    // Check cache first
    if (_countryCache.containsKey(cacheKey)) {
      _cacheStats.trackLookup(true);
      _updateLRU(cacheKey);

      // final duration = _cacheStats.stopwatch.elapsedMicroseconds;
      // FroggyLogger.debug(
      //   'Cache hit in $durationμs (Hit rate: ${_cacheStats.hitRate})',
      // );

      return _countryCache[cacheKey];
    }

    // Cache miss - perform lookup
    _cacheStats.trackLookup(false);      try {
        final number = parse(phoneNumber, countryCode: country);
        final phoneUtil = _phoneUtil;
        final regionCode = phoneUtil.getRegionCodeForNumber(number);

        if (regionCode != null) {
          final countryModel =
              FroggyCountries.getInstance().findCountryByCountryCode(regionCode);

          if (countryModel != null) {
            // Update cache
            _evictIfNeeded();
            _countryCache[cacheKey] = countryModel;
            _updateLRU(cacheKey);

            // final duration = _cacheStats.stopwatch.elapsedMicroseconds;
            // FroggyLogger.debug(
            //   'Cache miss - lookup completed in $durationμs '
            //   '(Hit rate: ${_cacheStats.hitRate}, '
            //   'Cache size: ${_countryCache.length})',
            // );

            return countryModel;
          }
        }} catch (e) {
          // Silently handle any errors to prevent "Bad element" exceptions
          // This includes metadata initialization issues and parsing errors
        }

    return null;
  }

  bool isDialablePhoneNumber(String phoneNumber) {
    try {
      if (phoneNumber.trim().isEmpty) {
        return false;
      }
      final phoneUtil = _phoneUtil;
      return phoneUtil.isViablePhoneNumber(phoneNumber);
    } catch (e) {
      // Silently handle any errors to prevent "Bad element" exceptions
      return false;
    }
  }

  bool isPossiblePhoneNumber(String phoneNumber) {
    try {
      if (phoneNumber.trim().isEmpty) {
        return false;
      }

      final number = parse(phoneNumber, countryCode: countryCode);

      if (number == null) {
        return false;
      }

      final phoneUtil = _phoneUtil;
      return phoneUtil.isPossibleNumber(number);
    } catch (e) {
      // Silently handle any errors to prevent "Bad element" exceptions
      return false;
    }
  }

  bool isPhoneNumberValid(String phoneNumber, {String? country}) {
    try {
      if (phoneNumber.trim().isEmpty) {
        return false;
      }

      final number = parse(phoneNumber, countryCode: country);

      if (number == null) {
        return false;
      }

      final phoneUtil = _phoneUtil;
      return phoneUtil.isValidNumber(number);
    } catch (e) {
      // Silently handle any errors to prevent "Bad element" exceptions
      return false;
    }
  }
}
