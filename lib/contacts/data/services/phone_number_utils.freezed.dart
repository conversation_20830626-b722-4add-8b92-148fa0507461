// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'phone_number_utils.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$PhoneNumberParseResult {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String nationalNumber, String countryCode,
            String formattedNumber, bool isValid, CountryModel? country)
        success,
    required TResult Function(String originalInput, String errorMessage,
            PhoneNumberErrorType errorType, String? fallbackValue)
        error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String nationalNumber, String countryCode,
            String formattedNumber, bool isValid, CountryModel? country)?
        success,
    TResult? Function(String originalInput, String errorMessage,
            PhoneNumberErrorType errorType, String? fallbackValue)?
        error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String nationalNumber, String countryCode,
            String formattedNumber, bool isValid, CountryModel? country)?
        success,
    TResult Function(String originalInput, String errorMessage,
            PhoneNumberErrorType errorType, String? fallbackValue)?
        error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PhoneNumberParseSuccess value) success,
    required TResult Function(PhoneNumberParseError value) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PhoneNumberParseSuccess value)? success,
    TResult? Function(PhoneNumberParseError value)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PhoneNumberParseSuccess value)? success,
    TResult Function(PhoneNumberParseError value)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PhoneNumberParseResultCopyWith<$Res> {
  factory $PhoneNumberParseResultCopyWith(PhoneNumberParseResult value,
          $Res Function(PhoneNumberParseResult) then) =
      _$PhoneNumberParseResultCopyWithImpl<$Res, PhoneNumberParseResult>;
}

/// @nodoc
class _$PhoneNumberParseResultCopyWithImpl<$Res,
        $Val extends PhoneNumberParseResult>
    implements $PhoneNumberParseResultCopyWith<$Res> {
  _$PhoneNumberParseResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PhoneNumberParseResult
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$PhoneNumberParseSuccessImplCopyWith<$Res> {
  factory _$$PhoneNumberParseSuccessImplCopyWith(
          _$PhoneNumberParseSuccessImpl value,
          $Res Function(_$PhoneNumberParseSuccessImpl) then) =
      __$$PhoneNumberParseSuccessImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {String nationalNumber,
      String countryCode,
      String formattedNumber,
      bool isValid,
      CountryModel? country});

  $CountryModelCopyWith<$Res>? get country;
}

/// @nodoc
class __$$PhoneNumberParseSuccessImplCopyWithImpl<$Res>
    extends _$PhoneNumberParseResultCopyWithImpl<$Res,
        _$PhoneNumberParseSuccessImpl>
    implements _$$PhoneNumberParseSuccessImplCopyWith<$Res> {
  __$$PhoneNumberParseSuccessImplCopyWithImpl(
      _$PhoneNumberParseSuccessImpl _value,
      $Res Function(_$PhoneNumberParseSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of PhoneNumberParseResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nationalNumber = null,
    Object? countryCode = null,
    Object? formattedNumber = null,
    Object? isValid = null,
    Object? country = freezed,
  }) {
    return _then(_$PhoneNumberParseSuccessImpl(
      nationalNumber: null == nationalNumber
          ? _value.nationalNumber
          : nationalNumber // ignore: cast_nullable_to_non_nullable
              as String,
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String,
      formattedNumber: null == formattedNumber
          ? _value.formattedNumber
          : formattedNumber // ignore: cast_nullable_to_non_nullable
              as String,
      isValid: null == isValid
          ? _value.isValid
          : isValid // ignore: cast_nullable_to_non_nullable
              as bool,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as CountryModel?,
    ));
  }

  /// Create a copy of PhoneNumberParseResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CountryModelCopyWith<$Res>? get country {
    if (_value.country == null) {
      return null;
    }

    return $CountryModelCopyWith<$Res>(_value.country!, (value) {
      return _then(_value.copyWith(country: value));
    });
  }
}

/// @nodoc

class _$PhoneNumberParseSuccessImpl implements PhoneNumberParseSuccess {
  const _$PhoneNumberParseSuccessImpl(
      {required this.nationalNumber,
      required this.countryCode,
      required this.formattedNumber,
      required this.isValid,
      this.country});

  @override
  final String nationalNumber;
  @override
  final String countryCode;
  @override
  final String formattedNumber;
  @override
  final bool isValid;
  @override
  final CountryModel? country;

  @override
  String toString() {
    return 'PhoneNumberParseResult.success(nationalNumber: $nationalNumber, countryCode: $countryCode, formattedNumber: $formattedNumber, isValid: $isValid, country: $country)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PhoneNumberParseSuccessImpl &&
            (identical(other.nationalNumber, nationalNumber) ||
                other.nationalNumber == nationalNumber) &&
            (identical(other.countryCode, countryCode) ||
                other.countryCode == countryCode) &&
            (identical(other.formattedNumber, formattedNumber) ||
                other.formattedNumber == formattedNumber) &&
            (identical(other.isValid, isValid) || other.isValid == isValid) &&
            (identical(other.country, country) || other.country == country));
  }

  @override
  int get hashCode => Object.hash(runtimeType, nationalNumber, countryCode,
      formattedNumber, isValid, country);

  /// Create a copy of PhoneNumberParseResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PhoneNumberParseSuccessImplCopyWith<_$PhoneNumberParseSuccessImpl>
      get copyWith => __$$PhoneNumberParseSuccessImplCopyWithImpl<
          _$PhoneNumberParseSuccessImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String nationalNumber, String countryCode,
            String formattedNumber, bool isValid, CountryModel? country)
        success,
    required TResult Function(String originalInput, String errorMessage,
            PhoneNumberErrorType errorType, String? fallbackValue)
        error,
  }) {
    return success(
        nationalNumber, countryCode, formattedNumber, isValid, country);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String nationalNumber, String countryCode,
            String formattedNumber, bool isValid, CountryModel? country)?
        success,
    TResult? Function(String originalInput, String errorMessage,
            PhoneNumberErrorType errorType, String? fallbackValue)?
        error,
  }) {
    return success?.call(
        nationalNumber, countryCode, formattedNumber, isValid, country);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String nationalNumber, String countryCode,
            String formattedNumber, bool isValid, CountryModel? country)?
        success,
    TResult Function(String originalInput, String errorMessage,
            PhoneNumberErrorType errorType, String? fallbackValue)?
        error,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(
          nationalNumber, countryCode, formattedNumber, isValid, country);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PhoneNumberParseSuccess value) success,
    required TResult Function(PhoneNumberParseError value) error,
  }) {
    return success(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PhoneNumberParseSuccess value)? success,
    TResult? Function(PhoneNumberParseError value)? error,
  }) {
    return success?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PhoneNumberParseSuccess value)? success,
    TResult Function(PhoneNumberParseError value)? error,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(this);
    }
    return orElse();
  }
}

abstract class PhoneNumberParseSuccess implements PhoneNumberParseResult {
  const factory PhoneNumberParseSuccess(
      {required final String nationalNumber,
      required final String countryCode,
      required final String formattedNumber,
      required final bool isValid,
      final CountryModel? country}) = _$PhoneNumberParseSuccessImpl;

  String get nationalNumber;
  String get countryCode;
  String get formattedNumber;
  bool get isValid;
  CountryModel? get country;

  /// Create a copy of PhoneNumberParseResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PhoneNumberParseSuccessImplCopyWith<_$PhoneNumberParseSuccessImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PhoneNumberParseErrorImplCopyWith<$Res> {
  factory _$$PhoneNumberParseErrorImplCopyWith(
          _$PhoneNumberParseErrorImpl value,
          $Res Function(_$PhoneNumberParseErrorImpl) then) =
      __$$PhoneNumberParseErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {String originalInput,
      String errorMessage,
      PhoneNumberErrorType errorType,
      String? fallbackValue});
}

/// @nodoc
class __$$PhoneNumberParseErrorImplCopyWithImpl<$Res>
    extends _$PhoneNumberParseResultCopyWithImpl<$Res,
        _$PhoneNumberParseErrorImpl>
    implements _$$PhoneNumberParseErrorImplCopyWith<$Res> {
  __$$PhoneNumberParseErrorImplCopyWithImpl(_$PhoneNumberParseErrorImpl _value,
      $Res Function(_$PhoneNumberParseErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of PhoneNumberParseResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? originalInput = null,
    Object? errorMessage = null,
    Object? errorType = null,
    Object? fallbackValue = freezed,
  }) {
    return _then(_$PhoneNumberParseErrorImpl(
      originalInput: null == originalInput
          ? _value.originalInput
          : originalInput // ignore: cast_nullable_to_non_nullable
              as String,
      errorMessage: null == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String,
      errorType: null == errorType
          ? _value.errorType
          : errorType // ignore: cast_nullable_to_non_nullable
              as PhoneNumberErrorType,
      fallbackValue: freezed == fallbackValue
          ? _value.fallbackValue
          : fallbackValue // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$PhoneNumberParseErrorImpl implements PhoneNumberParseError {
  const _$PhoneNumberParseErrorImpl(
      {required this.originalInput,
      required this.errorMessage,
      required this.errorType,
      this.fallbackValue});

  @override
  final String originalInput;
  @override
  final String errorMessage;
  @override
  final PhoneNumberErrorType errorType;
  @override
  final String? fallbackValue;

  @override
  String toString() {
    return 'PhoneNumberParseResult.error(originalInput: $originalInput, errorMessage: $errorMessage, errorType: $errorType, fallbackValue: $fallbackValue)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PhoneNumberParseErrorImpl &&
            (identical(other.originalInput, originalInput) ||
                other.originalInput == originalInput) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.errorType, errorType) ||
                other.errorType == errorType) &&
            (identical(other.fallbackValue, fallbackValue) ||
                other.fallbackValue == fallbackValue));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, originalInput, errorMessage, errorType, fallbackValue);

  /// Create a copy of PhoneNumberParseResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PhoneNumberParseErrorImplCopyWith<_$PhoneNumberParseErrorImpl>
      get copyWith => __$$PhoneNumberParseErrorImplCopyWithImpl<
          _$PhoneNumberParseErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String nationalNumber, String countryCode,
            String formattedNumber, bool isValid, CountryModel? country)
        success,
    required TResult Function(String originalInput, String errorMessage,
            PhoneNumberErrorType errorType, String? fallbackValue)
        error,
  }) {
    return error(originalInput, errorMessage, errorType, fallbackValue);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String nationalNumber, String countryCode,
            String formattedNumber, bool isValid, CountryModel? country)?
        success,
    TResult? Function(String originalInput, String errorMessage,
            PhoneNumberErrorType errorType, String? fallbackValue)?
        error,
  }) {
    return error?.call(originalInput, errorMessage, errorType, fallbackValue);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String nationalNumber, String countryCode,
            String formattedNumber, bool isValid, CountryModel? country)?
        success,
    TResult Function(String originalInput, String errorMessage,
            PhoneNumberErrorType errorType, String? fallbackValue)?
        error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(originalInput, errorMessage, errorType, fallbackValue);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PhoneNumberParseSuccess value) success,
    required TResult Function(PhoneNumberParseError value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PhoneNumberParseSuccess value)? success,
    TResult? Function(PhoneNumberParseError value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PhoneNumberParseSuccess value)? success,
    TResult Function(PhoneNumberParseError value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class PhoneNumberParseError implements PhoneNumberParseResult {
  const factory PhoneNumberParseError(
      {required final String originalInput,
      required final String errorMessage,
      required final PhoneNumberErrorType errorType,
      final String? fallbackValue}) = _$PhoneNumberParseErrorImpl;

  String get originalInput;
  String get errorMessage;
  PhoneNumberErrorType get errorType;
  String? get fallbackValue;

  /// Create a copy of PhoneNumberParseResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PhoneNumberParseErrorImplCopyWith<_$PhoneNumberParseErrorImpl>
      get copyWith => throw _privateConstructorUsedError;
}
