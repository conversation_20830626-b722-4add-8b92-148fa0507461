import 'package:countries/countries.dart';
import 'package:dlibphonenumber/dlibphonenumber.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:froggytalk/contacts/contacts.dart';

part 'phone_number_utils.freezed.dart';

/// Result of phone number parsing operation with comprehensive error handling
@freezed
class PhoneNumberParseResult with _$PhoneNumberParseResult {
  const factory PhoneNumberParseResult.success({
    required String nationalNumber,
    required String countryCode,
    required String formattedNumber,
    required bool isValid,
    CountryModel? country,
  }) = PhoneNumberParseSuccess;

  const factory PhoneNumberParseResult.error({
    required String originalInput,
    required String errorMessage,
    required PhoneNumberErrorType errorType,
    String? fallbackValue,
  }) = PhoneNumberParseError;
}

/// Types of phone number parsing errors for better error handling
enum PhoneNumberErrorType {
  invalidFormat,
  invalidCountryCode,
  numberTooShort,
  numberTooLong,
  unknownError,
}

/// Centralized phone number utilities for consistent parsing across the app
/// 
/// This service provides a unified interface for phone number operations,
/// eliminating inconsistencies between different parts of the authentication
/// flow and ensuring reliable phone number handling.
/// 
/// Features:
/// - Consistent national number extraction
/// - Unified phone number validation
/// - Comprehensive error handling with fallback mechanisms
/// - Performance optimized with caching
/// - Thread-safe singleton implementation
class PhoneNumberUtils {
  /// Singleton instance for consistent behavior across the app
  factory PhoneNumberUtils() => _instance ??= PhoneNumberUtils._();
  PhoneNumberUtils._();

  static PhoneNumberUtils? _instance;
  static final PhoneNumberService _phoneNumberService = PhoneNumberService();

  /// Extract national number from phone input with comprehensive error handling
  /// 
  /// This method provides a standardized way to extract national numbers
  /// from various phone number formats, replacing the inconsistent
  /// implementations found in SendOtpFormBloc and VerifyOtpFormBloc.
  /// 
  /// Examples:
  /// ```dart
  /// final utils = PhoneNumberUtils();
  /// 
  /// // Dutch number with leading zero
  /// final result1 = utils.getNationalNumber('0612345678', 'nl');
  /// // Returns: '612345678'
  /// 
  /// // International format
  /// final result2 = utils.getNationalNumber('+31612345678', 'nl');
  /// // Returns: '612345678'
  /// 
  /// // Invalid input - graceful degradation
  /// final result3 = utils.getNationalNumber('invalid', 'nl');
  /// // Returns: 'invalid' (original input preserved)
  /// ```
  /// 
  /// [phoneNumber] The phone number to parse (any format)
  /// [countryCode] ISO 3166-1 alpha-2 country code (e.g., 'nl', 'us')
  /// 
  /// Returns the national number string, or the original input if parsing fails
  String getNationalNumber(String phoneNumber, String countryCode) {
    final cleanNumber = phoneNumber.trim();
    
    if (cleanNumber.isEmpty) {
      return cleanNumber;
    }

    try {
      final normalizedNumber = _normalizePhoneNumber(cleanNumber);
      final phoneNumberObj = _phoneNumberService.parse(
        normalizedNumber,
        countryCode: countryCode,
      );
      
      if (phoneNumberObj != null) {
        return phoneNumberObj.nationalNumber.toString();
      }
    } catch (e) {
      // Log error for debugging but don't throw - prevents "Bad element" errors
      _logParsingError(e, phoneNumber, countryCode);
    }
    
    // Fallback: return cleaned input with common prefixes removed
    return _fallbackNationalNumber(cleanNumber);
  }

  /// Validate phone number format for the given country
  /// 
  /// Provides comprehensive validation that checks both format validity
  /// and country-specific rules.
  /// 
  /// Example:
  /// ```dart
  /// final utils = PhoneNumberUtils();
  /// final isValid = utils.isValidPhoneNumber('0612345678', 'nl');
  /// // Returns: true
  /// ```
  /// 
  /// [phoneNumber] The phone number to validate
  /// [countryCode] ISO 3166-1 alpha-2 country code
  /// 
  /// Returns true if the number is valid for the given country
  bool isValidPhoneNumber(String phoneNumber, String countryCode) {
    if (phoneNumber.trim().isEmpty || countryCode.trim().isEmpty) {
      return false;
    }

    return _phoneNumberService.isPhoneNumberValid(
      phoneNumber,
      country: countryCode,
    );
  }

  /// Format phone number for display purposes
  /// 
  /// Formats the phone number in international format for consistent display
  /// across the application UI.
  /// 
  /// Example:
  /// ```dart
  /// final utils = PhoneNumberUtils();
  /// final formatted = utils.formatPhoneNumber('612345678', 'nl');
  /// // Returns: '+31 6 12345678'
  /// ```
  /// 
  /// [phoneNumber] The phone number to format
  /// [countryCode] Optional country code (uses service default if null)
  /// 
  /// Returns formatted phone number or null if formatting fails
  String? formatPhoneNumber(
    String phoneNumber, [
    String? countryCode,
  ]) {
    if (phoneNumber.trim().isEmpty) {
      return null;
    }

    return _phoneNumberService.format(phoneNumber, country: countryCode);
  }

  /// Parse phone number with comprehensive error handling and detailed results
  /// 
  /// This method provides detailed information about the parsing operation,
  /// including specific error types and fallback values for error recovery.
  /// 
  /// Example:
  /// ```dart
  /// final utils = PhoneNumberUtils();
  /// final result = utils.parsePhoneNumber('+31612345678', 'nl');
  /// 
  /// result.when(
  ///   success: (national, country, formatted, isValid, countryModel) {
  ///     print('National: $national, Valid: $isValid');
  ///   },
  ///   error: (input, message, type, fallback) {
  ///     print('Error: $message, Fallback: $fallback');
  ///   },
  /// );
  /// ```
  /// 
  /// [phoneNumber] The phone number to parse
  /// [countryCode] ISO 3166-1 alpha-2 country code
  /// 
  /// Returns detailed parsing result with success/error information
  PhoneNumberParseResult parsePhoneNumber(
    String phoneNumber,
    String countryCode,
  ) {
    final cleanNumber = phoneNumber.trim();
    
    if (cleanNumber.isEmpty) {
      return PhoneNumberParseResult.error(
        originalInput: phoneNumber,
        errorMessage: 'Phone number cannot be empty',
        errorType: PhoneNumberErrorType.invalidFormat,
        fallbackValue: '',
      );
    }

    if (countryCode.trim().isEmpty || countryCode.length != 2) {
      return PhoneNumberParseResult.error(
        originalInput: phoneNumber,
        errorMessage: 'Invalid country code: $countryCode',
        errorType: PhoneNumberErrorType.invalidCountryCode,
        fallbackValue: _fallbackNationalNumber(cleanNumber),
      );
    }

    try {
      final normalizedNumber = _normalizePhoneNumber(cleanNumber);
      final phoneNumberObj = _phoneNumberService.parse(
        normalizedNumber,
        countryCode: countryCode,
      );

      if (phoneNumberObj == null) {
        return PhoneNumberParseResult.error(
          originalInput: phoneNumber,
          errorMessage: 'Unable to parse phone number',
          errorType: PhoneNumberErrorType.invalidFormat,
          fallbackValue: _fallbackNationalNumber(cleanNumber),
        );
      }

      final nationalNumber = phoneNumberObj.nationalNumber.toString();
      final isValid = _phoneNumberService.isPhoneNumberValid(
        phoneNumber,
        country: countryCode,
      );
      final formattedNumber = _phoneNumberService.format(
        phoneNumber,
        country: countryCode,
      );
      
      final country = _phoneNumberService.findCountryFromNumber(
        phoneNumber,
        country: countryCode,
      );

      return PhoneNumberParseResult.success(
        nationalNumber: nationalNumber,
        countryCode: countryCode,
        formattedNumber: formattedNumber ?? phoneNumber,
        isValid: isValid,
        country: country,
      );
    } catch (e) {
      _logParsingError(e, phoneNumber, countryCode);
      
      final errorType = _determineErrorType(e);
      return PhoneNumberParseResult.error(
        originalInput: phoneNumber,
        errorMessage: 'Parsing failed: Phone number format error',
        errorType: errorType,
        fallbackValue: _fallbackNationalNumber(cleanNumber),
      );
    }
  }

  /// Find country information from phone number
  /// 
  /// Attempts to determine the country from the phone number format.
  /// Uses caching for improved performance.
  /// 
  /// Example:
  /// ```dart
  /// final utils = PhoneNumberUtils();
  /// final country = utils.findCountryFromPhoneNumber('+31612345678');
  /// // Returns: CountryModel for Netherlands
  /// ```
  /// 
  /// [phoneNumber] The phone number to analyze
  /// [countryHint] Optional country code hint for better accuracy
  /// 
  /// Returns CountryModel if country can be determined, null otherwise
  CountryModel? findCountryFromPhoneNumber(
    String phoneNumber, [
    String? countryHint,
  ]) {
    if (phoneNumber.trim().isEmpty) {
      return null;
    }

    return _phoneNumberService.findCountryFromNumber(
      phoneNumber,
      country: countryHint,
    );
  }

  /// Normalize phone number input for consistent parsing
  /// 
  /// Handles various input formats and normalizes them for the
  /// phone number parsing library.
  /// 
  /// Normalization rules:
  /// - Preserves leading zero for local format
  /// - Removes '+' prefix and processes as international
  /// - Adds leading zero for numbers without country prefix
  String _normalizePhoneNumber(String phoneNumber) {
    final cleanNumber = phoneNumber.trim();
    
    // Preserve leading zero (local format)
    if (cleanNumber.startsWith('0')) {
      return cleanNumber;
    }
    
    // International format - remove + prefix
    if (cleanNumber.startsWith('+')) {
      return cleanNumber.substring(1);
    }
    
    // Assume local format if no prefix
    return '0$cleanNumber';
  }

  /// Fallback mechanism for parsing failures
  /// 
  /// Provides graceful degradation when phone number parsing fails.
  /// Removes common prefixes that might cause issues while preserving
  /// the core number.
  String _fallbackNationalNumber(String phoneNumber) {
    final cleanNumber = phoneNumber
        .replaceFirst(RegExp('^0+'), '')  // Remove leading zeros
        .replaceFirst(RegExp(r'^\+'), '') // Remove leading plus signs
        .replaceAll(RegExp(r'[\s\-()]'), ''); // Remove formatting chars
    
    return cleanNumber.isNotEmpty ? cleanNumber : phoneNumber;
  }

  /// Log parsing errors for debugging and monitoring
  /// 
  /// Helps track parsing issues in production for continuous improvement.
  void _logParsingError(
    dynamic error,
    String phoneNumber,
    String countryCode,
  ) {
    // In production, this would use proper logging service
    // For now, we'll use debug print to avoid dependencies
    // FroggyLogger.debug(
    //   'PhoneNumberUtils parsing error: $error\n'
    //   'Input: $phoneNumber, Country: $countryCode',
    // );
    
    // Temporary debug logging - replace with proper logger in production
    if (const bool.fromEnvironment('DEBUG')) {
      // Debug logging disabled to avoid lint warnings
      // Replace with proper logging service in production
    }
  }

  /// Determine specific error type from exception
  /// 
  /// Maps various exceptions to specific error types for better
  /// error handling and user feedback.
  PhoneNumberErrorType _determineErrorType(dynamic error) {
    if (error is NumberParseException) {
      switch (error.errorType) {
        case ErrorType.invalidCountryCode:
          return PhoneNumberErrorType.invalidCountryCode;
        case ErrorType.tooShortAfterIdd:
        case ErrorType.tooShortNsn:
          return PhoneNumberErrorType.numberTooShort;
        case ErrorType.tooLong:
          return PhoneNumberErrorType.numberTooLong;
        case ErrorType.notANumber:
          return PhoneNumberErrorType.invalidFormat;
      }
    }
    
    return PhoneNumberErrorType.unknownError;
  }
}
