import 'package:data/data.dart';
import 'package:either_dart/either.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:utils/utils.dart';

class RecentCallsRepository {
  Future<
          Either<
              GenericErrorResponse,
              PaginatedResponse<
                  List<GenericResponse<RecentCallsResponseModel>>>>>
      execute() async {
    try {
      final request = ApiRequest(
        route: '/v1/mobile/recent-calls',
        requestType: RequestType.get,
      );

      final response =
          await ApiClient.getInstance().executeRequest(request: request);

      if (response.isSuccess) {
        final data = response.collection<dynamic>();
        final paginatedResponse =
            PaginatedResponse<List<GenericResponse<RecentCallsResponseModel>>>(
          status: response.statusToString,
          message: response.successOrErrorMessage,
          data: data
              .map(
                (p01) => GenericResponse<RecentCallsResponseModel>.fromJson(
                    p01 as Map<String, dynamic>, (json) {
                  final mappedData = json as Map<String, dynamic>?;
                  return RecentCallsResponseModel.fromJson(
                    mappedData ?? {},
                  );
                }),
              )
              .toList(),
        );

        FroggyLogger.info('Response: $paginatedResponse');

        return Right(paginatedResponse);
      } else {
        FroggyLogger.error('Error Occurred: ${response.successOrErrorMessage}');

        return Left(
          GenericErrorResponse(
            message: response.successOrErrorMessage ?? 'Unknown error',
          ),
        );
      }
    } catch (e) {
      FroggyLogger.error('Failure Occurred: $e');

      return Left(GenericErrorResponse(message: e.toString()));
    }
  }
}
