import 'package:data/data.dart';
import 'package:either_dart/either.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:utils/utils.dart';

class ListFavouriteContactsRepository {
  Future<
          Either<GenericErrorResponse,
              PaginatedResponse<List<FavouriteContactsResponseModel>>>>
      execute() async {
    try {
      final request = ApiRequest(
        route: '/v1/mobile/favorites',
        requestType: RequestType.get,
      );

      final response =
          await ApiClient.getInstance().executeRequest(request: request);

      if (response.isSuccess) {
        final data = response.collection<dynamic>();
        final paginatedResponse =
            PaginatedResponse<List<FavouriteContactsResponseModel>>(
          status: response.statusToString,
          message: response.successOrErrorMessage,
          data: data.map(
            (p01) {
              final mappedData = p01 as Map<String, dynamic>;
              final favouriteContactsResponseModel =
                  FavouriteContactsResponseModel.fromJson(
                mappedData['attributes'] as Map<String, dynamic>,
              );

              return favouriteContactsResponseModel;
            },
          ).toList(),
        );

        FroggyLogger.info('Response: Debugging ${paginatedResponse.toJson(
          (p0) => p0.toList(),
        )}');

        return Right(paginatedResponse);
      } else {
        FroggyLogger.error('Error Occurred: ${response.successOrErrorMessage}');

        return Left(
          GenericErrorResponse(
            message: response.successOrErrorMessage ?? 'Unknown error',
          ),
        );
      }
    } catch (e) {
      FroggyLogger.error('Failure Occurred: $e');

      return Left(GenericErrorResponse(message: e.toString()));
    }
  }
}
