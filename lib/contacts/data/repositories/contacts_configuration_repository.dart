import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/l10n/l10n.dart';

/// A repository class that provides configuration data for the home screen.
class ContactsConfigurationRepository {
  /// Returns a list of tab names for the home screen.
  ///
  /// The tabs include:
  /// - 'Recent calls'
  /// - 'Contacts'
  /// - 'Favourites'
  ///
  /// Returns:
  ///   A list of strings representing the tab names.
  static List<String> getTabs() {
    final l10n = useLocalizationsWithoutContext();

    return [
      l10n.recentCallsText,
      l10n.contactsAllContactsTabText,
      l10n.contactsFavouriteCallsTabText,
    ];
  }
}

List<String> useContactTabs() {
  final context = useContext();
  final l10n = context.l10n;

  return [
    l10n.recentCallsText,
    l10n.contactsAllContactsTabText,
    l10n.contactsFavouriteCallsTabText,
  ];
}
