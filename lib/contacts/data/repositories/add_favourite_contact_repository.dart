import 'package:data/data.dart';
import 'package:either_dart/either.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:utils/utils.dart';

class AddFavouriteContactRepository {
  Future<
      Either<FormValidatedErrorResponse,
          PaginatedResponse<FavouriteContactsResponseModel>>> execute({
    required String name,
    required String phoneNumber,
  }) async {
    try {
      final body = AddFavouriteContactRequestModel(
        name: name,
        phoneNumber: phoneNumber,
      );

      final request = ApiRequest(
        route: '/v1/mobile/favorites',
        requestType: RequestType.post,
        params: PaginatedRequest<AddFavouriteContactRequestModel>(body: body)
            .toJson((p0) => p0.toJson()),
      );

      final response =
          await ApiClient.getInstance().executeRequest(request: request);

      if (response.isSuccess) {
        final fromJson = FavouriteContactsResponseModel.fromJson(response.data);

        final paginatedResponse =
            PaginatedResponse<FavouriteContactsResponseModel>(
          status: response.statusToString,
          message: response.successOrErrorMessage,
          data: fromJson,
        );

        FroggyLogger.info('Response: $paginatedResponse');

        return Right(paginatedResponse);
      } else if (response.hasFormErrors) {
        final formErrors =
            FormValidatedErrorResponse.fromJson(response.fromJson);

        final formattedErrors = formErrors.copyWith(
          formValidationErrors: {
            ...formErrors.setErrorAttribute(key: 'telephone'),
            ...formErrors.setErrorAttribute(key: 'name'),
            ...formErrors.setErrorAttribute(key: 'country'),
          },
        );

        FroggyLogger.warning('Form errors: $formattedErrors');

        return Left(formattedErrors);
      } else {
        FroggyLogger.error('Error Occurred: ${response.successOrErrorMessage}');

        return Left(
          FormValidatedErrorResponse(
            message: response.successOrErrorMessage ?? 'Unknown error',
          ),
        );
      }
    } catch (e) {
      FroggyLogger.error('Failure Occurred: $e');

      return Left(FormValidatedErrorResponse(message: e.toString()));
    }
  }
}
