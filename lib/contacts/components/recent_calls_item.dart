import 'package:common/common.dart';
import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:froggytalk/dialer/dialer.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:froggytalk/radio_stations/radio_stations.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:permissions/permissions.dart';
import 'package:utils/utils.dart';

class RecentCallItem extends HookWidget {
  const RecentCallItem({
    required this.phoneNumber,
    required this.formattedPhoneNumber,
    required this.countryFlag,
    super.key,
    this.createdAt = 'Jun 10th (Mon) 14:56',
    this.lengthOfCall = '10 mins 15 secs ',
    this.sessionBIll,
    this.status,
  });

  final String countryFlag;
  final String createdAt;
  final String formattedPhoneNumber;
  final String lengthOfCall;
  final String phoneNumber;
  final RecentCallsSessionBillModel? sessionBIll;
  final String? status;

  @override
  Widget build(BuildContext context) {
    final dialerBloc = context.read<DialerBloc>();
    final callService = context.read<CallService>();
    final l10n = context.l10n;

    final radioStationBloc = context.read<RadioStationsBloc>();
    final stopRadioStationsOnCall = useCallback(
      () {
        radioStationBloc.add(
          const RadioStationsEvent.stop(),
        );
      },
      [radioStationBloc],
    );

    /// Initiates a call using the centralized CallService
    /// 
    /// [phoneNumber] - Phone number to call
    /// [voiceOnly] - Whether to make a voice-only call (default: true)
    final makeCall = useCallback(
      (
        String phoneNumber, {
        bool voiceOnly = true,
      }) async {
        if (phoneNumber.isEmpty) {
          return;
        }

        try {
          // Stop radio stations before making a call
          stopRadioStationsOnCall();

          // final trimmedPhoneNumber =
          //     phoneNumber.trim().removeEmptySpaces();

          // // Use the centralized CallService to make the call
          // await callService.makeCall(
          //   number: trimmedPhoneNumber,
          //   voiceOnly: voiceOnly,
          // );

          // Update dialer bloc state
          dialerBloc.add(
            DialerEvent.callStarted(
              phoneNumber: phoneNumber,
            ),
          );
        } catch (e) {
          // Error handling is done in CallService
          // Could add additional UI error handling here if needed
          debugPrint('Error making call: $e');
        }
      },
      [callService, dialerBloc, stopRadioStationsOnCall],
    );

    final showPermissionPermanentlyDeniedErrorModal = useCallback(
      () {
        RequestPermission.showModal(
          context,
          label: l10n.permissionPermanentlyDeniedMessage,
          allowAccessText: l10n.allowAccessButtonText,
          skipText: l10n.permissionButtonSkip,
          onPressed: openAppSettings,
        );
      },
      [],
    );

    final showPermissionDeniedErrorModal = useCallback(
      () {
        RequestPermission.showModal(
          context,
          label: l10n.permissionDeniedMessage,
          allowAccessText: l10n.allowAccessButtonText,
          skipText: l10n.permissionButtonSkip,
          onPressed: openAppSettings,
        );
      },
      [],
    );

    final onCallButtonPressed = useCallback(
      (
        String phoneNumber, {
        bool voiceOnly = true,
      }) async {
        await Permission.microphone.request().then((value) {
          if (value == PermissionStatus.granted) {
            makeCall(phoneNumber);
          } else if (value.isPermanentlyDenied) {
            showPermissionPermanentlyDeniedErrorModal();
          } else {
            showPermissionDeniedErrorModal();
          }
        });
      },
      [
        makeCall,
        showPermissionPermanentlyDeniedErrorModal,
        showPermissionDeniedErrorModal,
      ],
    );

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              FroggyPhoneNumberWithFlag(
                phoneNumber: formattedPhoneNumber,
                isCentered: false,
                fontSize: 16,
                fontWeight: FontWeight.w600,
                flagPath: countryFlag,
              ),
              const Spacer(),
              // if (hasInternetConnection)
              CallListItemButton(
                onPressed: () => onCallButtonPressed(formattedPhoneNumber),
                label: l10n.callButtonText,
                svgVecIconPath: 'ic_phone_solid',
                marginRight: 0,
              ),
            ],
          ),
          Text(
            createdAt,
            style: const TextStyle(
              fontSize: 14,
              color: FroggyColors.black,
              fontWeight: FontWeight.w500,
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Container(
                    margin: const EdgeInsets.only(right: 4),
                    // child: CallLogType.outgoing.icon,
                    child: status?.toCallLogType.icon,
                  ),
                  Text(
                    status?.toCallLogType.getLocalizedDescription(context) ??
                        '',
                    style: const TextStyle(
                      fontSize: 12,
                      color: FroggyColors.froggyGrey2,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
              Text(
                "$lengthOfCall / ${(sessionBIll?.amount ?? '').toCurrency(context, symbol: sessionBIll?.currency ?? '')}",
                style: const TextStyle(
                  fontSize: 12,
                  color: FroggyColors.froggyGrey2,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
