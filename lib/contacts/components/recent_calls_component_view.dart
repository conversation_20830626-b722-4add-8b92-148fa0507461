import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:skeletonizer/skeletonizer.dart';

class RecentCallsComponentView extends HookWidget {
  const RecentCallsComponentView({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final phoneNumberService = context.read<PhoneNumberService>();
    final contactTabsBloc = context.read<ContactTabsBloc>();

    final onEmptyRecentButtonPressed = useCallback(() {
      contactTabsBloc.add(const ContactTabsEvent.tabChanged(1));
    });

    // useEffect(init, const []);

    return BlocBuilder<RecentCallsBloc, RecentCallsState>(
      builder: (context, state) {
        return CustomScrollView(
          slivers: [
            if (state.status.isInProgress)
              const SliverToBoxAdapter(
                child: Align(
                  heightFactor: 1,
                  child: CircularProgressIndicator(),
                ),
              ),
            if (state.filteredRecentCalls.isEmpty && state.status.isSuccess)
              SliverToBoxAdapter(
                child: Align(
                  heightFactor: 1.2,
                  alignment: Alignment.topCenter,
                  child: CallListEmpty(
                    onButtonPressed: onEmptyRecentButtonPressed,
                    message: l10n.contactsNoCallsEmptyMessage,
                    buttonText: l10n.contactsNoContactsButtonText,
                  ),
                ),
              ),
            if (state.filteredRecentCalls.isNotEmpty && state.status.isSuccess)
              SliverList(
                delegate: SliverChildBuilderDelegate(
                  childCount: state.filteredRecentCalls.length,
                  (context, index) {
                    final recentCall = state.filteredRecentCalls[index];

                    return Skeletonizer(
                      enabled: state.status.isInProgress,
                      child: BlocProvider(
                        create: (context) {
                          return RecentCallItemBloc(
                            phoneNumberService: phoneNumberService,
                            recentCallItem: recentCall,
                          )..add(const RecentCallItemEvent.started());
                        },
                        child: BlocBuilder<RecentCallItemBloc,
                            RecentCallItemState>(
                          builder: (context, state) => RecentCallItem(
                            phoneNumber: state.phoneNumber,
                            formattedPhoneNumber:
                                state.formattedPhoneNumber ?? '',
                            lengthOfCall: state.lengthOfCall,
                            createdAt: state.parsedCreatedAt,
                            sessionBIll: state.sessionBIll,
                            status: state.callStatus,
                            countryFlag: state.countryFlag,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
          ],
        );
      },
    );
  }
}
