import 'dart:ui';

import 'package:common/common.dart';
import 'package:fast_contacts/fast_contacts.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:grouped_list/sliver_grouped_list.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:permissions/permissions.dart';
import 'package:sliver_tools/sliver_tools.dart';

class ContactListComponentView extends StatefulHookWidget {
  const ContactListComponentView({super.key});

  @override
  State<ContactListComponentView> createState() =>
      _ContactListComponentViewState();
}

class _ContactListComponentViewState extends State<ContactListComponentView> {
  ContactListBloc? bloc;
  FavouriteContactsBloc? favBloc;
  AppLocalizations? l10n;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    l10n = context.l10n;
  }

  @override
  void initState() {
    super.initState();
    bloc = context.read<ContactListBloc>();
    favBloc = context.read<FavouriteContactsBloc>();

    onInitiated();
  }

  void routeBack() {
    Navigator.of(context).pop();
  }

  void showPermissionRequestModal(int lengthOfContacts) {
    // RequestPermission.showModal(
    //   context,
    //   label: l10n?.permissionForContactListTitle ?? '',
    //   allowAccessText: l10n?.allowAccessButtonText ?? '',
    //   skipText: l10n?.permissionButtonSkip ?? '',
    //   onPressed: () async {
    //     try {
    //       if (bloc == null) {
    //         FroggyLogger.error('ContactListBloc is null');
    //         return;
    //       }

    // Start the bloc
    bloc!.add(const ContactListEvent.started(requestForPermission: true));

    //   // Wait for the state to be updated
    //   await Future<void>.delayed(const Duration(milliseconds: 100));
    // } catch (e, stackTrace) {
    //   FroggyLogger.stackTracedError(
    //     'Error requesting contacts permission',
    //     error: e,
    //     stackTrace: stackTrace,
    //   );
    // } finally {
    //   routeBack();
    // }
    // },
    // );
  }

  void showPermissionPermanentlyDeniedErrorModal() {
    RequestPermission.showModal(
      context,
      label: l10n!.permissionPermanentlyDeniedMessage,
      allowAccessText: l10n!.allowAccessButtonText,
      skipText: l10n!.permissionButtonSkip,
      onPressed: openAppSettings,
    );
  }

  void showPermissionDeniedErrorModal() {
    RequestPermission.showModal(
      context,
      label: l10n!.permissionDeniedMessage,
      allowAccessText: l10n!.allowAccessButtonText,
      skipText: l10n!.permissionButtonSkip,
      onPressed: openAppSettings,
    );
  }

  Future<void> onInitiated() async {
    final lengthOfContacts = bloc?.state.contacts.length ?? 0;
    // const contactPermission = Permission.contacts;
    // final isPermissionsGranted = await contactPermission.isGranted;

    // if (isPermissionsGranted) {
    //   if (lengthOfContacts <= 0) {
    //     bloc?.add(const ContactListEvent.started());
    //   }
    // } else {
    //   showPermissionRequestModal(lengthOfContacts);
    // }

    await Permission.contacts.request().then((value) {
      if (value == PermissionStatus.granted) {
        if (lengthOfContacts <= 0) {
          bloc?.add(const ContactListEvent.started());
        }
      } else if (value.isPermanentlyDenied) {
        showPermissionPermanentlyDeniedErrorModal();
      } else {
        showPermissionDeniedErrorModal();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final hasInternetConnection = useHasConnectivity();
    final contacts = useMemoized(
      () =>
          context.select((ContactListBloc bloc) => bloc.state.filteredContacts),
      [],
    );
    final isStateInProgress = context.select(
      (ContactListBloc bloc) => bloc.state.isInProgress,
    );
    final isStateSuccess = context.select(
      (ContactListBloc bloc) => bloc.state.isSuccess,
    );
    const pageSize = 10;
    final currentPage = useState(0);
    // final scrollController = useScrollController();
    final isLoadingMore = useState(false);
    final hasMoreItems = useState(true);
    final displayedContacts = useState<List<Contact>>(contacts);

    // Initialize first page
    useEffect(
      () {
        if (contacts.isNotEmpty) {
          final initialContacts = contacts.take(pageSize).toList();
          displayedContacts.value = initialContacts;
          currentPage.value = 0;
          hasMoreItems.value = contacts.length > pageSize;
        }
        return null;
      },
      [contacts],
    ); // Reset when contacts change

    // // Scroll listener setup
    // useEffect(
    //   () {
    //     void onScroll() {
    //       if (!scrollController.hasClients) return;

    //       final threshold = scrollController.position.maxScrollExtent - 200;
    //       final currentScroll = scrollController.position.pixels;

    //       if (currentScroll >= threshold &&
    //           !isLoadingMore.value &&
    //           hasMoreItems.value) {
    //         isLoadingMore.value = true;

    //         final nextPage = currentPage.value + 1;
    //         final startIndex = nextPage * pageSize;
    //         final endIndex = min(startIndex + pageSize, contacts.length);

    //         if (startIndex < contacts.length) {
    //           Future.microtask(() {
    //             displayedContacts.value = [
    //               ...displayedContacts.value,
    //               ...contacts.sublist(startIndex, endIndex),
    //             ];
    //             currentPage.value = nextPage;
    //             hasMoreItems.value = endIndex < contacts.length;
    //             isLoadingMore.value = false;
    //           });
    //         } else {
    //           hasMoreItems.value = false;
    //           isLoadingMore.value = false;
    //         }
    //       }
    //     }

    //     scrollController.addListener(onScroll);
    //     return () => scrollController.removeListener(onScroll);
    //   },
    //   [scrollController, contacts],
    // );

    // Cache loading widget
    final loadingWidget = useMemoized(
      () => SliverToBoxAdapter(
        child: Column(
          children: [
            FroggySpacer.y24(),
            const CircularProgressIndicator(),
          ],
        ),
      ),
      [], // Never recompute
    );

    // Cache empty state widget
    final emptyWidget = useMemoized(
      () => SliverToBoxAdapter(
        child: Align(
          heightFactor: 1,
          alignment: Alignment.topCenter,
          child: CallListEmpty(
            message: context.l10n.emptyContactList,
            buttonText: '',
          ),
        ),
      ),
      [], // Never recompute
    );

    return CustomScrollView(
      // controller: scrollController,
      key: useMemoized(() => const PageStorageKey('contact_list')),
      physics: const AlwaysScrollableScrollPhysics(
        parent: BouncingScrollPhysics(),
      ),
      keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
      cacheExtent: 2000,
      slivers: [
        if (isStateInProgress) loadingWidget,
        if (contacts.isEmpty) emptyWidget,
        if (isStateSuccess)
          _CustomGroupedContactList(
            contacts: contacts,
            hasInternetConnection: hasInternetConnection,
          ),
        // if (displayedContacts.value.isNotEmpty)
        //   SliverList(
        //     delegate: SliverChildBuilderDelegate(
        //       (context, index) {
        //         if (index >= displayedContacts.value.length) {
        //           return isLoadingMore.value
        //               ? const Padding(
        //                   padding: EdgeInsets.all(16),
        //                   child: Center(child: CircularProgressIndicator()),
        //                 )
        //               : null;
        //         }
        //         return ContactListItem(
        //           item: displayedContacts.value[index],
        //           hasInternetConnection: hasInternetConnection,
        //         );
        //       },
        //       childCount: displayedContacts.value.length +
        //           (isLoadingMore.value ? 1 : 0),
        //     ),
        //   ),
        if (isLoadingMore.value) loadingWidget,
      ],
    );
  }
}

class _CustomGroupedContactList extends HookWidget {
  const _CustomGroupedContactList({
    required this.contacts,
    this.hasInternetConnection = true,
  });

  final List<Contact> contacts;
  final bool hasInternetConnection;

  Widget _buildErrorWidget(BuildContext context, Object error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Text(
          error.toString(),
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.error,
              ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final results = useMemoized(() => contacts, []);

    // Cache grouped contacts computation
    final groupedContacts = useMemoized(
      () {
        final grouped = <String, List<Contact>>{};
        for (final contact in results) {
          final key = contact.displayName.isEmpty ||
                  contact.displayName.characters.isEmpty
              ? '#'
              : contact.displayName.characters.first.toUpperCase();
          grouped.putIfAbsent(key, () => []).add(contact);
        }
        return grouped;
      },
      [results], // Only recompute when results change
    );

    // Cache sorted keys
    final sortedKeys = useMemoized(
      () => groupedContacts.keys.toList()..sort(),
      [groupedContacts], // Only recompute when groupedContacts change
    );

    // Cache contact list item builder
    final contactItemBuilder = useCallback(
      (Contact contact) => ContactListItem(
        key: ValueKey(contact.id), // Add key for better reconciliation
        item: contact,
        hasInternetConnection: hasInternetConnection,
      ),
      [
        hasInternetConnection,
      ], // Only rebuild when connection state changes
    );

    return MultiSliver(
      pushPinnedChildren: true,
      children: [
        for (final key in sortedKeys) ...[
          if (results.isNotEmpty)
            _ErrorBoundary(
              onError: (error, stackTrace) => _buildErrorWidget(context, error),
              child: SliverPersistentHeader(
                key: ValueKey('header_$key'),
                delegate: _StickyHeaderDelegate(
                  title: key,
                  height: 48,
                ),
              ),
            ),
          if (results.isNotEmpty)
            _ErrorBoundary(
              onError: (error, stackTrace) => _buildErrorWidget(context, error),
              child: SliverAnimatedPaintExtent(
                duration: const Duration(milliseconds: 300),
                child: SliverList(
                  key: ValueKey('list_$key'),
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      final contacts = groupedContacts[key] ?? [];
                      if (index >= contacts.length) return null;
                      return contactItemBuilder(contacts[index]);
                    },
                    childCount: groupedContacts[key]?.length ?? 0,
                  ),
                ),
              ),
            ),
        ],
      ],
    );
  }
}

// ignore: unused_element
class _PkgGroupedContactList extends StatelessWidget {
  const _PkgGroupedContactList({
    required this.contacts,
    // ignore: unused_element
    // this.hasInternetConnection = true,
  });

  final List<Contact> contacts;
  // final bool hasInternetConnection;

  @override
  Widget build(BuildContext context) {
    final results = useMemoized(() => contacts.take(5).toList(), []);

    // Cache contact list item builder
    final contactItemBuilder = useCallback(
      (Contact contact) => ContactListItem(
        key: ValueKey(contact.id), // Add key for better reconciliation
        item: contact,
        hasInternetConnection: true,
      ),
      [
        // hasInternetConnection,
      ], // Only rebuild when connection state changes
    );

    return SliverGroupedListView(
      elements: results,
      indexedItemBuilder: (context, element, index) {
        return contactItemBuilder(element);
      },
      groupBy: (element) {
        final displayName = element.displayName;
        if (displayName.isEmpty) {
          return '#';
        }

        return displayName.characters.first.toUpperCase();
      },
      groupHeaderBuilder: (element) {
        final firstChar = element.displayName.characters.first.toUpperCase();

        return Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 8,
          ),
          child: Text(
            firstChar,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w500,
            ),
          ),
        );
      },
    );
  }
}

class _StickyHeaderDelegate extends SliverPersistentHeaderDelegate {
  const _StickyHeaderDelegate({
    required this.title,
    required this.height,
  }) : assert(height > 0, 'Height must be greater than zero');

  final String title;
  final double height;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: 8,
      ),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  @override
  double get maxExtent => height;

  @override
  double get minExtent => height;

  @override
  bool shouldRebuild(covariant _StickyHeaderDelegate oldDelegate) {
    return title != oldDelegate.title || height != oldDelegate.height;
  }
}

class _ErrorBoundary extends StatefulWidget {
  const _ErrorBoundary({
    required this.child,
    required this.onError,
  });

  final Widget child;
  final Widget Function(Object error, StackTrace stackTrace) onError;

  @override
  State<_ErrorBoundary> createState() => _ErrorBoundaryState();
}

class _ErrorBoundaryState extends State<_ErrorBoundary> {
  Error? _error;
  late StackTrace? _stackTrace;

  @override
  void initState() {
    super.initState();
    // Register error handler for async errors
    PlatformDispatcher.instance.onError = (error, stack) {
      _handleError(error, stack);
      return true;
    };
  }

  void _handleError(Object error, StackTrace stackTrace) {
    if (mounted) {
      setState(() {
        _error = error as Error;
        _stackTrace = stackTrace;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      return widget.onError(_error!, _stackTrace!);
    }

    ErrorWidget.builder = (FlutterErrorDetails details) {
      // Log error in release mode
      if (kReleaseMode) {
        // Add your logging service here
        debugPrint('Error in release mode: ${details.exception}');
        return Text('Error in release mode: ${details.exception}');
      }

      return widget.onError(
        details.exception,
        details.stack ?? StackTrace.current,
      );
    };

    return widget.child;
  }
}
