import 'package:constants/constants.dart';
import 'package:countries/countries.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/call_rates/call_rates.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:froggytalk/dialer/dialer.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:froggytalk/radio_stations/radio_stations.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:permissions/permissions.dart';
import 'package:utils/utils.dart';

class ContactListItem extends HookWidget {
  const ContactListItem({
    required this.item,
    required this.hasInternetConnection,
    this.onFavouriteButtonPressed,
    this.isFavourite,
    super.key,
  });

  final bool hasInternetConnection;
  final bool? isFavourite;
  final dynamic item;
  final VoidCallback? onFavouriteButtonPressed;

  @override
  Widget build(BuildContext context) {
    // Performance tracking
    final buildStartTime = DateTime.now();

    useEffect(
      () {
        FroggyLogger.debug(
          'ContactListItem built in '
          '${DateTime.now().difference(buildStartTime).inMilliseconds}ms',
        );

        return null;
      },
      [],
    );

    return BlocProvider(
      create: (context) => ContactListItemBloc(
        phoneNumberService: context.read<PhoneNumberService>(),
        callRatesRepository: context.read<CallRatesRepository>(),
        favouriteContactsBloc: context.read<FavouriteContactsBloc>(),
        contactsService: context.read<ContactListService>(),
      )..add(ContactListItemEvent.started(item: item)),
      child: _ContactListItemContent(
        hasInternetConnection: hasInternetConnection,
        onFavouriteButtonPressed: onFavouriteButtonPressed,
        isFavourite: isFavourite,
      ),
    );
  }
}

class _ContactListItemContent extends HookWidget {
  const _ContactListItemContent({
    required this.hasInternetConnection,
    this.onFavouriteButtonPressed,
    this.isFavourite,
  });

  final bool hasInternetConnection;
  final bool? isFavourite;
  final VoidCallback? onFavouriteButtonPressed;

  @override
  Widget build(BuildContext context) {
    final toggleDetailedAction = useState<bool>(false);
    final callService = context.read<CallService>();
    final dialerBloc = context.read<DialerBloc>();
    final hasInternet = useHasConnectivity();
    // final authProfile = useAuthUser();
    // final authCurrencySymbol = useAuthUserCurrencyCode();
    // final currency = Currency.create(authCurrencySymbol!, 2);
    final l10n = context.l10n;
    final favouritesContacts = useMemoized(
      () => context
          .select<FavouriteContactsBloc, List<FavouriteContactsResponseModel>>(
        (bloc) => bloc.state.favouriteContacts,
      ),
    );

    // final itemPhoneNumber = useMemoized(() {
    //   return context.select(
    //     (ContactListItemBloc bloc) => bloc.state.formattedPhoneNumber ?? '',
    //   );
    // });

    final isFavourited = useCallback(
      (String phoneNumber) {
        if (isFavourite != null) {
          return isFavourite!;
        }

        return favouritesContacts.any(
          (element) {
            return element.phoneNumber
                    ?.trim()
                    .removeEmptySpaces() ==
                phoneNumber.trim().removeEmptySpaces();
          },
        );
      },
      [isFavourite, favouritesContacts],
    );

    // final accountBalance = useMemoized(() {
    //   final accountBalance2 = authProfile?.accountBalance;
    //   if (accountBalance2 == null) {
    //     return Money.fromIntWithCurrency(0, currency);
    //   }

    //   final bigInt = Fixed.fromNum(num.parse(accountBalance2));
    //   final money2 = Money.fromFixedWithCurrency(bigInt, currency);
    //   return money2;
    // });

    // final callRates = context.select<ContactListItemBloc, Money>(
    //   (bloc) {
    //     final currentCallRates = bloc.state.callRate;
    //     if (currentCallRates == null) {
    //       return Money.fromIntWithCurrency(0, currency);
    //     }

    //     final bigInt =
    //         Fixed.fromNum(num.parse(currentCallRates.amount ?? '0.00'));
    //     final money1 = Money.fromFixedWithCurrency(bigInt, currency);
    //     return money1;
    //   },
    // );

    final radioStationBloc = context.read<RadioStationsBloc>();
    final stopRadioStationsOnCall = useCallback(
      () {
        radioStationBloc.add(
          const RadioStationsEvent.stop(),
        );
      },
      [radioStationBloc],
    );

    /// Initiates a call using the centralized CallService
    /// 
    /// [phoneNumber] - Phone number to call
    /// [voiceOnly] - Whether to make a voice-only call (default: true)
    final makeCall = useCallback(
      (
        String phoneNumber, {
        bool voiceOnly = true,
      }) async {
        if (phoneNumber.isEmpty) {
          return;
        }

        try {
          // Stop radio stations before making a call
          stopRadioStationsOnCall();

          // final trimmedPhoneNumber =
          //     phoneNumber.trim().removeEmptySpaces();

          // // Use the centralized CallService to make the call
          // await callService.makeCall(
          //   number: trimmedPhoneNumber,
          //   voiceOnly: voiceOnly,
          // );

          // Update dialer bloc state
          dialerBloc.add(
            DialerEvent.callStarted(
              phoneNumber: phoneNumber,
            ),
          );
        } catch (e) {
          // Error handling is done in CallService
          // Could add additional UI error handling here if needed
          debugPrint('Error making call: $e');
        }
      },
      [callService, dialerBloc, stopRadioStationsOnCall],
    );

    // final routeBack = useCallback(
    //   () {
    //     Navigator.of(context).pop();
    //   },
    //   [],
    // );

    final showPermissionPermanentlyDeniedErrorModal = useCallback(
      () {
        RequestPermission.showModal(
          context,
          label: l10n.permissionPermanentlyDeniedMessage,
          allowAccessText: l10n.allowAccessButtonText,
          skipText: l10n.permissionButtonSkip,
          onPressed: openAppSettings,
        );
      },
      [],
    );

    final showPermissionDeniedErrorModal = useCallback(
      () {
        RequestPermission.showModal(
          context,
          label: l10n.permissionDeniedMessage,
          allowAccessText: l10n.allowAccessButtonText,
          skipText: l10n.permissionButtonSkip,
          onPressed: openAppSettings,
        );
      },
      [],
    );

    // final showPermissionAlert = useCallback(
    //   (String phoneNumber) {
    //     RequestPermission.showModal(
    //       context,
    //       label: l10n.permissionForMicrophoneTitle,
    //       allowAccessText: l10n.allowAccessButtonText,
    //       skipText: l10n.permissionButtonSkip,
    //       onPressed: () async {
    //         final status = await Permission.contacts.request();

    //         if (status == PermissionStatus.granted) {
    //           routeBack();

    //           WidgetsBinding.instance.addPostFrameCallback(
    //             (_) => makeCall(phoneNumber),
    //           );
    //         } else {
    //           routeBack();
    //           WidgetsBinding.instance.addPostFrameCallback(
    //             (_) => showPermissionPermanentlyDeniedErrorModal(),
    //           );
    //         }
    //       },
    //     );
    //   },
    //   [],
    // );

    final onCallButtonPressed = useCallback(
      (
        String phoneNumber, {
        bool voiceOnly = true,
      }) async {
        // const permissionMic = Permission.microphone;
        // final isGranted = await permissionMic.isGranted;

        // if (!isGranted) {
        //   WidgetsBinding.instance.addPostFrameCallback(
        //     (_) => showPermissionAlert(phoneNumber),
        //   );
        // } else {
        //   await makeCall(phoneNumber);
        // }

        await Permission.microphone.request().then((value) {
          if (value == PermissionStatus.granted) {
            makeCall(phoneNumber);
          } else if (value.isPermanentlyDenied) {
            showPermissionPermanentlyDeniedErrorModal();
          } else {
            showPermissionDeniedErrorModal();
          }
        });
      },
      [
        makeCall,
        showPermissionPermanentlyDeniedErrorModal,
        showPermissionDeniedErrorModal,
      ],
    );

    return BlocBuilder<ContactListItemBloc, ContactListItemState>(
      builder: (context, state) {
        final phoneNumber = state.formattedPhoneNumber ?? state.phoneNumber;

        final isfave = isFavourited(state.formattedPhoneNumber ?? '');

        return ListTile(
          minVerticalPadding: 0,
          minTileHeight: !hasInternetConnection ? null : 100,
          contentPadding: const EdgeInsets.symmetric(vertical: 1),
          onTap: () {
            toggleDetailedAction.value = !toggleDetailedAction.value;
            context.read<ContactListItemBloc>().add(
                  const ContactListItemEvent.fetchedCallRates(),
                );
          },
          title: ContactHeader(
            fullName: state.fullName,
            // isFavourite: isFavourite ?? state.isFavourited,
            isFavourite: isfave,
            onFavouritePressed: onFavouriteButtonPressed,
            hasInternetConnection: hasInternetConnection,
          ),
          subtitle: Column(
            children: [
              ContactInfo(
                countryFlag: state.countryFlag,
                phoneNumber: phoneNumber,
              ),
              if (toggleDetailedAction.value)
                ContactActions(
                  state: state,
                  onCallPressed: !hasInternet
                      ? null
                      : () => onCallButtonPressed(phoneNumber),
                ),
            ],
          ),
        );
      },
    );
  }
}

@visibleForTesting
class ContactHeader extends StatelessWidget {
  const ContactHeader({
    required this.fullName,
    required this.isFavourite,
    required this.hasInternetConnection,
    this.onFavouritePressed,
    super.key,
  });

  final String fullName;
  final bool hasInternetConnection;
  final bool isFavourite;
  final VoidCallback? onFavouritePressed;

  @override
  Widget build(BuildContext context) {
    // final bloc = context.read<ContactListItemBloc>();

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Flexible(
          child: Text(
            fullName,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: FroggyColors.black,
            ),
            textScaler: const TextScaler.linear(1.1),
          ),
        ),
        // if (hasInternetConnection)
        CallListFavouriteButton(
          value: isFavourite,
          onPressed: !(onFavouritePressed == null)
              ? onFavouritePressed
              : () {
                  // if (isFavourite) {
                  //   return bloc.add(
                  //     const ContactListItemEvent.removedFromFavourites(),
                  //   );
                  // } else {
                  //   return bloc.add(
                  //     const ContactListItemEvent.addedToFavourites(),
                  //   );
                  // }
                },
        ),
      ],
    );
  }
}

@visibleForTesting
class ContactInfo extends StatelessWidget {
  const ContactInfo({
    required this.countryFlag,
    required this.phoneNumber,
    super.key,
  });

  final String? countryFlag;
  final String phoneNumber;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        if (countryFlag != null)
          Container(
            margin: const EdgeInsets.only(right: 3, left: 3),
            child: FroggyCountries.showCountryFlag(
              countryFlag!,
              width: 30,
              margin: 0,
            ),
          ),
        Text(
          phoneNumber,
          textDirection: TextDirection.ltr,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.black54,
          ),
        ),
      ],
    );
  }
}

@visibleForTesting
class ContactActions extends StatelessWidget {
  const ContactActions({
    required this.state,
    this.onCallPressed,
    super.key,
  });

  final VoidCallback? onCallPressed;
  final ContactListItemState state;

  String _formatCallRate(CallRatesAmountModel? callRate, BuildContext context) {
    if (callRate?.amount == null) return '0.0';
    return callRate!.amount!.toCurrency(
      context,
      symbol: callRate.currency!,
    );
  }

  // void _navigateToDetails(BuildContext context) {
  //   Navigator.of(context).push(
  //     ContactDetailsPage.route(
  //       bloc: context.read<ContactListItemBloc>(),
  //     ),
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
      margin: const EdgeInsets.only(top: 5),
      child: CallListItemOptions(
        isCallRateAvailable: state.isCallRateLoading,
        callRateForMobile: _formatCallRate(state.callRate, context),
        onCallButtonPressed:
            state.callRateErrorMessage != null ? null : onCallPressed,
        // onViewButtonPressed: () => _navigateToDetails(context),
      ),
    );
  }
}

// class ContactListItem extends HookWidget implements SipUaHelperListener {
//   const ContactListItem({
//     required this.item,
//     required this.hasInternetConnection,
//     this.onFavouriteButtonPressed,
//     this.isFavourite,
//     super.key,
//   });

//   final dynamic item;
//   final bool hasInternetConnection;
//   final VoidCallback? onFavouriteButtonPressed;
//   final bool? isFavourite;

//   @override
//   Widget build(BuildContext context) {
//     final phoneNumberService = context.read<PhoneNumberService>();
//     final callRatesRepository = context.read<CallRatesRepository>();
//     final favBloc = context.read<FavouriteContactsBloc>();
//     final toggleDetailedAction = useState<bool>(false);
//     final dialerBloc = context.read<DialerBloc>();
//     final helper = context.read<SIPUAHelper>();
//     final l10n = context.l10n;

//     final makeCall = useCallback(
//       (
//         String phoneNumber, {
//         bool voiceOnly = true,
//       }) async {
//         if (defaultTargetPlatform == TargetPlatform.android ||
//             defaultTargetPlatform == TargetPlatform.iOS) {
//           await Permission.microphone.request();
//           // await Permission.camera.request();
//         }
//         if (phoneNumber.isEmpty) {
//           // await showDialog<void>(
//           //   context: context,
//           //   barrierDismissible: false,
//           //   builder: (BuildContext context) {
//           //     return AlertDialog(
//           //       title: const Text('Target is empty.'),
//           //       content: const Text('Please enter a SIP URI or username!'),
//           //       actions: <Widget>[
//           //         TextButton(
//           //           child: const Text('Ok'),
//           //           onPressed: () {
//           //             Navigator.of(context).pop();
//           //           },
//           //         ),
//           //       ],
//           //     );
//           //   },
//           // );
//           return null;
//         }

//         final mediaConstraints = <String, dynamic>{
//           'audio': true,
//           'video': {
//             'mandatory': <String, dynamic>{
//               'minWidth': '640',
//               'minHeight': '480',
//               'minFrameRate': '30',
//             },
//             'facingMode': 'user',
//           },
//         };

//         MediaStream mediaStream;

//         if (kIsWeb && !voiceOnly) {
//           mediaStream =
//               await navigator.mediaDevices.getDisplayMedia(mediaConstraints);
//           mediaConstraints['video'] = false;
//           final userStream =
//               await navigator.mediaDevices.getUserMedia(mediaConstraints);
//           final audioTracks = userStream.getAudioTracks();
//           if (audioTracks.isNotEmpty) {
//             await mediaStream.addTrack(audioTracks.first);
//           }
//         } else {
//           if (voiceOnly) {
//             mediaConstraints['video'] = !voiceOnly;
//           }
//           mediaStream =
//               await navigator.mediaDevices.getUserMedia(mediaConstraints);
//         }

//         await helper.call(
//           phoneNumber,
//           voiceOnly: voiceOnly,
//           mediaStream: mediaStream,
//         );

//         dialerBloc.add(
//           DialerEvent.callStarted(
//             phoneNumber: phoneNumber,
//           ),
//         );
//         // _preferences.setString('dest', dest);
//         return null;
//       },
//       [],
//     );

//     final routeBack = useCallback(
//       () {
//         Navigator.of(context).pop();
//       },
//       [],
//     );

//     final showErrorModal = useCallback(
//       () {
//         RequestPermission.showModal(
//           context,
//           label: l10n.permissionPermanentlyDeniedMessage,
//           onPressed: routeBack,
//           allowAccessText: l10n.allowAccessButtonText,
//           skipText: l10n.permissionButtonSkip,
//         );
//       },
//       [],
//     );

//     final showPermissionAlert = useCallback(
//       (String phoneNumber) {
//         RequestPermission.showModal(
//           context,
//           label: l10n.permissionForMicrophoneTitle,
//           allowAccessText: l10n.allowAccessButtonText,
//           skipText: l10n.permissionButtonSkip,
//           onPressed: () async {
//             final status = await Permission.contacts.request();

//             if (status == PermissionStatus.granted) {
//               routeBack();
//               WidgetsBinding.instance.addPostFrameCallback(
//                 (_) => makeCall(phoneNumber),
//               );
//             } else {
//               routeBack();
//               WidgetsBinding.instance.addPostFrameCallback(
//                 (_) => showErrorModal(),
//               );
//             }
//           },
//         );
//       },
//       [],
//     );

//     final onCallButtonPressed = useCallback(
//       (
//         String phoneNumber, {
//         bool voiceOnly = true,
//       }) async {
//         const permissionMic = Permission.microphone;
//         final isGranted = await permissionMic.isGranted;

//         if (!isGranted) {
//           WidgetsBinding.instance.addPostFrameCallback(
//             (_) => showPermissionAlert(phoneNumber),
//           );
//         } else {
//           await makeCall(phoneNumber);
//         }
//       },
//       [],
//     );

//     useEffect(
//       () {
//         helper.addSipUaHelperListener(this);

//         return () {
//           helper.removeSipUaHelperListener(this);
//         };
//       },
//       [helper],
//     );

//     return BlocProvider(
//       create: (context) {
//         final bloc = ContactListItemBloc(
//           phoneNumberService: phoneNumberService,
//           callRatesRepository: callRatesRepository,
//           favouriteContactsBloc: favBloc,
//         )..add(ContactListItemEvent.started(item: item))
//             // ..add(const ContactListItemEvent.processPhoneNumber())
//             ;

//         // if (onFavouriteButtonPressed == null) {
//         //   bloc.add(const ContactListItemEvent.checkIfFavourited());
//         // }

//         return bloc;
//       },
//       child: BlocBuilder<ContactListItemBloc, ContactListItemState>(
//         buildWhen: (previous, current) {
//           return previous.phoneNumber != current.phoneNumber ||
//               previous.isFavourited != current.isFavourited ||
//               previous.callRate != current.callRate;
//         },
//         builder: (context, state) {
//           return ListTile(
//             minVerticalPadding: 0,
//             minTileHeight: !hasInternetConnection ? null : 100,
//             contentPadding: const EdgeInsets.symmetric(
//               vertical: 1,
//             ),
//             onTap: () {
//               toggleDetailedAction.value = !toggleDetailedAction.value;
//               context.read<ContactListItemBloc>().add(
//                     const ContactListItemEvent.fetchedCallRates(),
//                   );
//             },
//             title: Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Flexible(
//                   child: Text(
//                     state.fullName,
//                     style: const TextStyle(
//                       fontSize: 18,
//                       fontWeight: FontWeight.w600,
//                       color: FroggyColors.black,
//                     ),
//                     textScaler: const TextScaler.linear(1.1),
//                   ),
//                 ),
//                 // if (hasInternetConnection)
//                 //   CallListFavouriteButton(
//                 //     value: isFavourite ?? state.isFavourited,
//                 //     onPressed: onFavouriteButtonPressed ??
//                 //         () {
//                 //           context.read<ContactListItemBloc>().add(
//                 //                 const ContactListItemEvent.toggleAsFavourites(),
//                 //               );
//                 //         },
//                 //   ),
//                 // BlocSelector<FavouriteContactsBloc, FavouriteContactsState,
//                 //     bool>(
//                 //   selector: (fstate) {

//                 //     return fstate.cachedFavouriteContacts.any(
//                 //       (element) {
//                 //         return element.phoneNumber == state.phoneNumber;
//                 //       },
//                 //     );
//                 //   },
//                 //   builder: (context, isFavourited) {
//                 //     return CallListFavouriteButton(
//                 //       value: isFavourited,
//                 //       onPressed: () => context
//                 //           .read<ContactListItemBloc>()
//                 //           .add(
//                 //             const ContactListItemEvent.toggleAsFavourites(),
//                 //           ),
//                 //     );
//                 //   },
//                 // ),
//               ],
//             ),
//             subtitle: Column(
//               children: [
//                 Row(
//                   children: [
//                     if (state.countryFlag != null)
//                       Container(
//                         margin: const EdgeInsets.only(right: 3),
//                         child: FroggyCountries.showCountryFlag(
//                           state.countryFlag!,
//                           width: 30,
//                           // height: 30,
//                           margin: 0,
//                         ),
//                       ),
//                     Text(
//                       state.formattedPhoneNumber ?? state.phoneNumber,
//                       style: const TextStyle(
//                         fontSize: 14,
//                         color: Colors.black54,
//                       ),
//                     ),
//                   ],
//                 ),
//                 if (toggleDetailedAction.value)
//                   AnimatedContainer(
//                     duration: const Duration(milliseconds: 500),
//                     curve: Curves.easeInOut,
//                     margin: const EdgeInsets.only(top: 5),
//                     child: Builder(
//                       builder: (context) {
//                         final callRate = state.callRate;
//                         var callRateForMobile = '0.0';
//                         if (callRate != null) {
//                           callRateForMobile = callRate.amount!.toCurrency(
//                             context,
//                             symbol: callRate.currency!,
//                           );
//                         }

//                         return CallListItemOptions(
//                           isCallRateAvailable: state.isCallRateLoading,
//                           callRateForMobile: callRateForMobile,
//                           onCallButtonPressed:
//                               state.callRateErrorMessage != null
//                                   ? null
//                                   : () {
//                                       // context
//                                       //     .read<CallService>()
//                                       //     .makeCall(
//                                       //number: state.phoneNumber,
//                                       // );
//                                       onCallButtonPressed(state.phoneNumber);
//                                       // dialerBloc.add(
//                                       //   DialerEvent.callStarted(
//                                       //     phoneNumber: state.phoneNumber,
//                                       //   ),
//                                       // );
//                                     },
//                           onViewButtonPressed: () {
//                             Navigator.of(context).push(
//                               ContactDetailsPage.route(
//                                 bloc: context.read<ContactListItemBloc>(),
//                               ),
//                             );
//                           },
//                         );
//                       },
//                     ),
//                   ),
//               ],
//             ),
//           );
//         },
//       ),
//     );
//   }

//   @override
//   void callStateChanged(Call call, CallState callState) {
//     // Navigator.of(context).push(CallingUserPage.route());
//     // if (callState.state == CallStateEnum.CALL_INITIATION) {
//     //   Navigator.pushNamed(context, '/callscreen', arguments: call);
//     // }
//   }

//   @override
//   void onNewMessage(SIPMessageRequest msg) {}

//   @override
//   void onNewNotify(Notify ntf) {}

//   @override
//   void onNewReinvite(ReInvite event) {}

//   @override
//   void registrationStateChanged(RegistrationState state) {}

//   @override
//   void transportStateChanged(TransportState state) {}
// }
