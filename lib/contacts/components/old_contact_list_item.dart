import 'package:constants/constants.dart';
import 'package:countries/countries.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/call_rates/call_rates.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:froggytalk/dialer/dialer.dart';
import 'package:utils/utils.dart';

class OldContactListItem extends StatefulHookWidget {
  const OldContactListItem({
    required this.contactName,
    required this.phoneNumber,
    required this.countryCode,
    this.onViewMoreButtonPressed,
    this.onCallButtonPressed,
    this.callRate,
    this.isShowingOptions = false,
    this.isFavourited,
    this.onFavoriteButtonPressed,
    this.onPressed,
    this.totalCallMins,
    this.totalCallCost,
    super.key,
  });

  final String contactName;
  final String phoneNumber;
  final String countryCode;

  final bool? isFavourited;
  final bool isShowingOptions;

  final void Function(bool isFavourited)? onFavoriteButtonPressed;
  final VoidCallback? onPressed;
  final VoidCallback? onViewMoreButtonPressed;
  final VoidCallback? onCallButtonPressed;

  final String? totalCallMins;
  final String? totalCallCost;
  final String? callRate;

  @override
  State<OldContactListItem> createState() => _ContactListItemState();
}

class _ContactListItemState extends State<OldContactListItem> {
  DialerBloc? _dialerBloc;

  @override
  void initState() {
    super.initState();
    _dialerBloc = context.read<DialerBloc>();
  }

  void onTappedCall(int index) {
    final formattedPhoneNumber =
        ContactUtility.formatPhoneNumber(widget.phoneNumber);
    _dialerBloc?.add(
      DialerEvent.callStarted(
        phoneNumber: formattedPhoneNumber,
      ),
    );

    // _dialerBloc?.add(ContactListEvent.updatedTabbedValue(value: index));
  }

  @override
  Widget build(BuildContext context) {
    final toggelShowOptions = useState(false);
    final isCallRatesLoading = useState(false);
    final callRatesMobile = useState('0.0');
    final dialerBloc = context.read<DialerBloc>();
    final hasInternetConnection = useHasConnectivity();
    final phoneNumberService = context.read<PhoneNumberService>();
    // final authCountryCode = useAuthUserCountryCode();
    // final phoneUtil = dlibphonenumber.PhoneNumberUtil.instance;

    final formattedPhoneNumber = useMemoized(
      () {
        return phoneNumberService.format(widget.phoneNumber) ??
            widget.phoneNumber;
      },
      [widget.phoneNumber],
    );

    final countryFlag = useMemoized(
      () {
        return widget.countryCode.isNotEmpty
            ? 'flags/${widget.countryCode.toLowerCase()}.svg'
            : null;
      },
      [widget.countryCode],
    );

    return RepositoryProvider(
      create: (context) => CallRatesRepository(),
      child: Builder(
        builder: (context) {
          // final rates = context.read<CallRatesRepository>();

          return ListTile(
            onTap: !hasInternetConnection
                ? null
                : () async {
                    widget.onPressed?.call();
                    toggelShowOptions.value = !toggelShowOptions.value;
                    isCallRatesLoading.value = true;
                    // final results =
                    //     await rates.execute(countryCode: widget.countryCode);
                    // if (results.isRight) {
                    //   final data2 = results.right.data;
                    //   final attributes = data2?.attributes;
                    //   callRatesMobile.value =
                    //       (attributes?.rateMobilePerMinute?.amount ?? '0.0')
                    //           .toCurrency(
                    //     context,
                    //     symbol: attributes!.rateMobilePerMinute!.currency!,
                    //   );
                    //   isCallRatesLoading.value = false;
                    //   FroggyLogger.info(
                    //     'the country code result: $data2',
                    //   );
                    // } else {
                    //   isCallRatesLoading.value = false;
                    //   callRatesMobile.value = '0.0';
                    //   FroggyLogger.error('Failed to fetch call rates');
                    // }
                  },
            minVerticalPadding: 0,
            minTileHeight: !hasInternetConnection ? null : 100,
            contentPadding: const EdgeInsets.symmetric(
              // horizontal: 1,
              vertical: 1,
            ),
            title: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Text(
                    widget.contactName,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: FroggyColors.black,
                    ),
                    textScaler: const TextScaler.linear(1.1),
                  ),
                ),
                if (hasInternetConnection)
                  if (widget.onFavoriteButtonPressed != null)
                    BlocBuilder<FavouriteContactsBloc, FavouriteContactsState>(
                      builder: (context, state) {
                        final isSelectedAsFavourite =
                            state.favouriteContacts.any(
                          (contact) =>
                              contact.phoneNumber == widget.phoneNumber,
                        );

                        return CallListFavouriteButton(
                          value: widget.isFavourited ?? isSelectedAsFavourite,
                          onPressed: () => widget.onFavoriteButtonPressed
                              ?.call(isSelectedAsFavourite),
                        );
                      },
                    ),
              ],
            ),
            subtitle: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        if (countryFlag != null)
                          Container(
                            margin: const EdgeInsets.only(right: 3),
                            child: FroggyCountries.showCountryFlag(
                              countryFlag,
                              width: 30,
                              // height: 30,
                              margin: 0,
                            ),
                          ),
                        Text(
                          formattedPhoneNumber,
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.black54,
                          ),
                        ),
                      ],
                    ),
                    Text.rich(
                      TextSpan(
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.black54,
                          fontWeight: FontWeight.w600,
                        ),
                        text: widget.totalCallMins != null
                            ? '${widget.totalCallMins} mins'
                            : null,
                        children: [
                          const TextSpan(
                            text: ' ',
                          ),
                          TextSpan(
                            text: widget.totalCallCost != null
                                ? '(${widget.totalCallCost?.toCurrency(
                                    context,
                                  )})'
                                : null,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                // if (isShowingOptions)
                if (toggelShowOptions.value)
                  Container(
                    margin: const EdgeInsets.only(top: 5),
                    child: Builder(
                      builder: (context) {
                        return CallListItemOptions(
                          isCallRateAvailable: isCallRatesLoading.value,
                          callRateForMobile: callRatesMobile.value,
                          onCallButtonPressed: () {
                            widget.onCallButtonPressed?.call();
                            dialerBloc.add(
                              DialerEvent.callStarted(
                                phoneNumber: widget.phoneNumber,
                              ),
                            );
                          },
                          // onViewButtonPressed: () {
                          //   widget.onViewMoreButtonPressed?.call();
                          //   Navigator.of(context).push(
                          //     ContactDetailsPage.route(
                          //       callRates: callRatesMobile.value,
                          //       contact: ContactModel(
                          //         name: widget.contactName,
                          //         phoneNumber: formattedPhoneNumber,
                          //         countryCode: widget.countryCode,
                          //         // dialingCode:
                          //       ),
                          //     ),
                          //   );
                          // },
                        );
                      },
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }
}
