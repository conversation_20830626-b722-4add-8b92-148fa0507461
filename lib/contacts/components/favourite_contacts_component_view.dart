import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:froggytalk/l10n/l10n.dart';

class FavouriteContactsComponentView extends HookWidget {
  const FavouriteContactsComponentView({super.key});

  @override
  Widget build(BuildContext context) {
    final bloc = context.read<FavouriteContactsBloc>();
    // final phoneNumberService = context.read<PhoneNumberService>();
    // final callRatesRepository = context.read<CallRatesRepository>();
    final hasInternetConnection = useHasConnectivity();
    final l10n = AppLocalizations.of(context);

    return BlocBuilder<FavouriteContactsBloc, FavouriteContactsState>(
      builder: (context, state) {
        return CustomScrollView(
          slivers: [
            if (state.status.isInProgress)
              const SliverToBoxAdapter(
                child: Align(
                  heightFactor: 1,
                  child: CircularProgressIndicator(),
                ),
              ),
            if (state.fiilteredFavouriteContacts.isEmpty)
              SliverToBoxAdapter(
                child: Align(
                  heightFactor: 1,
                  alignment: Alignment.topCenter,
                  child: CallListEmpty(
                    // onButtonPressed: onEmptyRecentButtonPressed,
                    message: l10n.noFavoriteContactsMessage,
                    buttonText: '',
                  ),
                ),
              ),
            if (state.fiilteredFavouriteContacts.isNotEmpty &&
                state.status.isSuccess)
              SliverList(
                delegate: SliverChildBuilderDelegate(
                  childCount: state.fiilteredFavouriteContacts.length,
                  (context, index) {
                    final contact = state.fiilteredFavouriteContacts[index];
                    final phoneNumber = contact.phoneNumber;

                    return ContactListItem(
                      item: contact,
                      hasInternetConnection: hasInternetConnection,
                      isFavourite: true,
                      onFavouriteButtonPressed: () {
                        bloc.add(
                          FavouriteContactsEvent.deleted(
                            phoneNumber: phoneNumber ?? '',
                          ),
                        );
                      },
                    );
                  },
                ),
              ),
          ],
        );

        // return Builder(
        //   builder: (context) {
        //     if (state.favouriteContacts.isEmpty) {
        //       return Align(
        //         heightFactor: 1,
        //         alignment: Alignment.topCenter,
        //         child: CallListEmpty(
        //           // onButtonPressed: onEmptyRecentButtonPressed,
        //           message: l10n.noFavoriteContactsMessage,
        //           buttonText: '',
        //         ),
        //       );
        //     }

        //     return ListView.builder(
        //       itemCount: state.favouriteContacts.length,
        //       itemBuilder: (context, index) {
        //         final contact = state.favouriteContacts[index];
        //         final phoneNumber = contact.phoneNumber;

        //         return ContactListItem(
        //           item: contact,
        //           hasInternetConnection: hasInternetConnection,
        //           isFavourite: true,
        //           onFavouriteButtonPressed: () {
        //             bloc.add(
        //               FavouriteContactsEvent.deleted(
        //                 phoneNumber: phoneNumber ?? '',
        //               ),
        //             );
        //           },
        //         );

        //         // return BlocProvider(
        //         //   create: (context) => ContactListItemBloc(
        //         //     phoneNumberService: phoneNumberService,
        //         //     callRatesRepository: callRatesRepository,
        //         //     favouriteContactsBloc: bloc,
        //         //   )..add(
        //         //       ContactListItemEvent.started(
        //         //         item: contact,
        //         //       ),
        //         //     ),
        //         //   child:
        //         //       BlocBuilder<ContactListItemBloc, ContactListItemState>(
        //         //     builder: (context, state) {
        //         //       return OldContactListItem(
        //         //         isFavourited: true,
        //         //         contactName: state.fullName,
        //         //         phoneNumber: state.formattedPhoneNumber ?? '',
        //         //         countryCode: state.countryCode ?? '',
        //         //         onFavoriteButtonPressed: (_) {
        //         //           bloc.add(
        //         //             FavouriteContactsEvent.deleted(
        //         //               phoneNumber: phoneNumber ?? '',
        //         //             ),
        //         //           );
        //         //         },
        //         //       );
        //         //     },
        //         //   ),
        //         // );
        //       },
        //     );
        //   },
        // );
      },
    );
  }
}
