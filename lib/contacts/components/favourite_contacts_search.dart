import 'package:common/common.dart';
import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggy_icons/froggy_icons.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:froggytalk/l10n/l10n.dart';

class FavouriteContactsSearch extends HookWidget {
  const FavouriteContactsSearch({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final bloc = context.read<FavouriteContactsBloc>();

    final defaultSearchTerm = context.select(
      (FavouriteContactsBloc bloc) => bloc.state.searchTerm,
    );

    final searchController = useTextEditingController(text: defaultSearchTerm);

    useEffect(
      () {
        void onSearchChanged() {
          bloc.add(
            FavouriteContactsEvent.updatedSearchField(
              value: searchController.text,
            ),
          );
        }

        searchController.addListener(onSearchChanged);

        return () {
          searchController.removeListener(onSearchChanged);
        };
      },
      [searchController, bloc],
    );

    return Container(
      margin: const EdgeInsets.only(top: 10),
      child: FroggyTextFormFieldContainer(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        borderRadius: 300,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(300),
          child: SearchBar(
            controller: searchController,
            hintText: l10n.searchFavouriteContactMessage,
            hintStyle: const WidgetStatePropertyAll(
              TextStyle(
                color: FroggyColors.froggyGrey35,
              ),
            ),
            textStyle: const WidgetStatePropertyAll(
              TextStyle(
                color: FroggyColors.froggyBlack,
              ),
            ),
            leading: FroggyIconsList.searchOutline.toWidget(
              color: FroggyColors.froggyGrey2,
            ),
            backgroundColor: const WidgetStatePropertyAll(
              FroggyColors.froggyGrey5,
            ),
            elevation: const WidgetStatePropertyAll(0),
          ),
        ),
      ),
    );
  }
}
