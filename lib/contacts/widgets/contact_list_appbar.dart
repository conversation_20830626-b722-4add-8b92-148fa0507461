import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/svg.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:vector_graphics/vector_graphics.dart';

class ContactListAppbar extends HookWidget implements PreferredSizeWidget {
  const ContactListAppbar({super.key});

  double get height => 80;

  @override
  Widget build(BuildContext context) {
    final searchController = useSearchController();
    final l10n = AppLocalizations.of(context);

    final bloc = context.read<ContactListBloc>();

    useEffect(() {
      void onSearchChanged() {
        bloc.add(
          ContactListEvent.updatedSearchField(value: searchController.text),
        );
      }

      searchController.addListener(onSearchChanged);

      return () {
        searchController.removeListener(onSearchChanged);
      };
    });

    return AppBar(
      toolbarHeight: height,
      automaticallyImplyLeading: false,
      actions: const [CloseButton()],
      title: Container(
        margin: const EdgeInsets.only(top: 3),
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
        height: 50,
        decoration: BoxDecoration(
          color: FroggyColors.froggyGrey5,
          borderRadius: BorderRadius.circular(25),
        ),
        child: SearchBar(
          controller: searchController,
          hintText: l10n.contactsSearchContactsPlaceholder,
          textStyle: const WidgetStatePropertyAll(
            TextStyle(
              color: FroggyColors.froggyBlack,
            ),
          ),
          leading: const SvgPicture(
            AssetBytesLoader(
              'packages/froggy_icons/icons/ic_search_outline.svg.vec',
            ),
          ),
          backgroundColor: const WidgetStatePropertyAll(
            FroggyColors.froggyGrey5,
          ),
          elevation: const WidgetStatePropertyAll(0),
        ),
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(height);
}
