import 'package:flutter/material.dart';
import 'package:froggytalk/contacts/contacts.dart';

class CallLists extends StatelessWidget {
  const CallLists({this.isDemoFavouriteButtonVisible = true, super.key});

  final bool isDemoFavouriteButtonVisible;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 300,
      child: ListView(
        children: const [
          CallListItem(
            contactName: 'Fe<PERSON>',
            phoneNumber: '+2348094907255',
            hasDemoFavouriteButton: true,
          ),
          CallListItem(
            contactName: 'Esther Umoke',
            phoneNumber: '+2348094907255',
            hasDemoFavouriteButton: true,
          ),
          CallListItem(
            contactName: 'Micheal Adefalana',
            phoneNumber: '+2348094907255',
            hasDemoFavouriteButton: true,
          ),
          CallListItem(
            contactName: '<PERSON>',
            phoneNumber: '+2348094907255',
            hasDemoFavouriteButton: true,
          ),
          CallListItem(
            contactName: '<PERSON>',
            phoneNumber: '+2348094907255',
            hasDemoFavouriteButton: true,
          ),
          CallListItem(
            contactName: 'Femi Kayode',
            phoneNumber: '+2348094907255',
            hasDemoFavouriteButton: true,
          ),
          CallListItem(
            contactName: 'Femi Kayode',
            phoneNumber: '+2348094907255',
            hasDemoFavouriteButton: true,
          ),
        ],
      ),
    );
  }
}
