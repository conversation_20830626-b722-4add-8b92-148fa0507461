import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:froggy_icons/froggy_icons.dart';

class CallListEmpty extends StatelessWidget {
  const CallListEmpty({
    super.key,
    this.message = 'You do not have any recent calls',
    this.buttonText = 'View Contacts',
    this.onButtonPressed,
    this.isLoading = false,
  });

  final String message;
  final String buttonText;
  final VoidCallback? onButtonPressed;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 30),
      decoration: BoxDecoration(
        color: FroggyColors.froggyCream,
        borderRadius: BorderRadius.circular(15),
        // border: Border.all(
        //   color: FroggyColors.froggyGrey35,
        // ),
      ),
      child: Builder(
        builder: (context) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              FroggyIconsList.womanHoldingPhone.toWidget(
                width: 80,
                height: 80,
              ),
              const SizedBox(height: 10),
              Text(
                message,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),

              // Button section
              if (buttonText.isNotEmpty)
                ElevatedButton(
                  onPressed: onButtonPressed,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: FroggyColors.primary, // Dark green color
                    padding: const EdgeInsets.symmetric(
                      horizontal: 60,
                      vertical: 16,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                  child: Text(
                    buttonText,
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.white,
                    ),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }
}
