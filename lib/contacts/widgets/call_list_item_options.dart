import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:skeletonizer/skeletonizer.dart';

class CallListItemOptions extends StatelessWidget {
  const CallListItemOptions({
    super.key,
    this.onViewButtonPressed,
    this.onCallButtonPressed,
    this.callRateForMobile,
    this.isCallRateAvailable = false,
  });

  final VoidCallback? onViewButtonPressed;
  final VoidCallback? onCallButtonPressed;
  final bool isCallRateAvailable;

  final String? callRateForMobile;

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;

    return Container(
      padding: const EdgeInsets.all(5),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        color: FroggyColors.froggyCream,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Skeletonizer(
            enabled: isCallRateAvailable,
            enableSwitchAnimation: true,
            child: Text(
              // '${(callRateForMobile ?? "0").toCurrency(context, decimalDigits: 0)}/min',
              // '${callRateForMobile ?? "0"}/min',
              l10n.perMinRate(callRateForMobile ?? '0'),
              style: const TextStyle(
                fontSize: 12,
                color: FroggyColors.froggyGrey2,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
          Row(
            children: [
              if (onViewButtonPressed != null)
                CallListItemButton(
                  onPressed: onViewButtonPressed,
                  label: l10n.viewButtonText,
                  svgVecIconPath: 'ic_info',
                  marginRight: 10,
                  backgroundColor: Colors.transparent,
                  color: FroggyColors.froggyGrey2,
                ),
              if (onCallButtonPressed != null)
                CallListItemButton(
                  onPressed: onCallButtonPressed,
                  label: l10n.callButtonText,
                  svgVecIconPath: 'ic_phone_solid',
                  marginRight: 0,
                ),
            ],
          ),
        ],
      ),
    );
  }
}
