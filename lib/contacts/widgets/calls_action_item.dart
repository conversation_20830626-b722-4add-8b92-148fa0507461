import 'package:flutter/material.dart';

class CallActionBar extends StatelessWidget {
  const CallActionBar({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Text(
          r'$12/min',
          style: TextStyle(
            fontSize: 16,
            color: Colors.black87,
          ),
        ),
        Row(
          children: [
            const Icon(
              Icons.info_outline,
              color: Colors.grey,
            ),
            const SizedBox(width: 4),
            TextButton(
              onPressed: () {},
              child: const Text(
                'View',
                style: TextStyle(
                  color: Colors.black87,
                  fontSize: 16,
                ),
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton.icon(
              onPressed: () {},
              icon: const Icon(
                Icons.call,
                color: Colors.green,
              ),
              label: const Text('Call'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green.withAlpha((0.1 * 255).toInt()),
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
