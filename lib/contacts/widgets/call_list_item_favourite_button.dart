import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggy_icons/froggy_icons.dart';
import 'package:utils/utils.dart';

class CallListFavouriteButton extends HookWidget {
  const CallListFavouriteButton({
    this.value = false,
    this.onPressed,
    super.key,
  });

  final bool value;
  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    final outlineIcon = useMemoized(
      () => FroggyIconsList.starOutline.toWidget(width: 20, height: 20),
      const [],
    );

    final solidIcon = useMemoized(
      () => FroggyIconsList.starSolid.toWidget(width: 20, height: 20),
      const [],
    );

    // Performance tracking
    final buildStartTime = DateTime.now();

    useEffect(
      () {
        FroggyLogger.debug(
          'ContactListItem Favourite Button built in '
          '${DateTime.now().difference(buildStartTime).inMilliseconds}ms',
        );

        return null;
      },
      [],
    );

    return IconButton(
      icon: !value ? outlineIcon : solidIcon,
      onPressed: onPressed,
    );
  }
}
