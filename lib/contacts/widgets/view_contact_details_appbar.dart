import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:froggytalk/contacts/contacts.dart';

class ViewContactDetailsAppBar extends StatelessWidget
    implements PreferredSizeWidget {
  const ViewContactDetailsAppBar({
    super.key,
    this.onFavouriteButtonPressed,
    this.isFavourite = false,
  });

  double get height => 80;

  final bool isFavourite;
  final VoidCallback? onFavouriteButtonPressed;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 1),
      child: AppBar(
        toolbarHeight: height,
        title: const Text(
          'Contact details',
          style: TextStyle(
            color: FroggyColors.black,
            fontSize: 24,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          if (onFavouriteButtonPressed != null)
            CallListFavouriteButton(
              value: isFavourite,
              onPressed: onFavouriteButtonPressed ?? () {},
            ),
        ],
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(height);
}
