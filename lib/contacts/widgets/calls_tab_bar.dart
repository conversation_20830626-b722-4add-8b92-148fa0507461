import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

class CallsTabBar extends HookWidget {
  const CallsTabBar({
    required this.tabs,
    this.tabValue = 0,
    this.onTabChanged,
    super.key,
  });

  final void Function(int index)? onTabChanged;
  final int tabValue;
  final List<String> tabs;

  @override
  Widget build(BuildContext context) {
    final iTabs = useMemoized<List<String>>(
      () => tabs,
    );

    return SizedBox(
      height: 35,
      child: ListView.builder(
        clipBehavior: Clip.antiAlias,
        itemCount: iTabs.length,
        scrollDirection: Axis.horizontal,
        itemBuilder: (context, index) {
          return _buildTab(
            iTabs[index],
            tabValue == index,
            onTap: () {
              onTabChanged?.call(index);
            },
          );
        },
      ),
    );
  }

  Widget _buildTab(String title, bool isSelected, {VoidCallback? onTap}) {
    return InkWell(
      onTap: onTap,
      splashColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
        margin: const EdgeInsets.only(right: 5),
        decoration: BoxDecoration(
          color: isSelected
              ? FroggyColors.hexOpacityToColor(
                  '#167047',
                  20,
                )
              : Colors.grey[300],
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          title,
          style: TextStyle(
            fontSize: 14,
            color: isSelected ? FroggyColors.primary : FroggyColors.froggyGrey2,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
