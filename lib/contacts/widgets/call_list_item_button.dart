import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggy_icons/froggy_icons.dart';

class CallListItemButton extends HookWidget {
  const CallListItemButton({
    required this.label,
    required this.svgVecIconPath,
    this.marginRight = 5,
    this.onPressed,
    this.backgroundColor = const Color(0x33167047),
    this.color = FroggyColors.primary,
    super.key,
  });

  final VoidCallback? onPressed;
  final String label;
  final String svgVecIconPath;
  final double marginRight;
  final Color backgroundColor;
  final Color color;

  // final defaultBgColor = FroggyColors.hexOpacityToColor(
  //   '#167047',
  //   20,
  // );

  @override
  Widget build(BuildContext context) {
    final iconWidget = useMemoized(() {
      return FroggyIcon(
        svgVecIconPath,
        width: 15,
        height: 15,
        color: color,
      );
    });

    // return ElevatedButton.icon(
    //   onPressed: onPressed,
    //   icon: iconWidget,
    //   label: Text(
    //     label,
    //     style: TextStyle(
    //       fontSize: 14,
    //       color: color,
    //       fontWeight: FontWeight.w600,
    //     ),
    //   ),
    //   style: ElevatedButton.styleFrom(
    //     elevation: 0,
    //     iconColor: color,
    //     backgroundColor: backgroundColor,
    //     splashFactory: NoSplash.splashFactory,
    //     enableFeedback: false,
    //     padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
    //     fixedSize: const Size(80, 20),
    //   ),
    // );

    return InkWell(
      onTap: onPressed,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 10,
          vertical: 5,
        ),
        margin: EdgeInsets.only(right: marginRight),
        decoration: BoxDecoration(
          color: onPressed != null
              ? backgroundColor
              : backgroundColor.withAlpha((0.3 * 255).round()),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          children: [
            iconWidget,
            Container(
              margin: const EdgeInsets.only(left: 4),
              child: Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  color: onPressed != null
                      ? color
                      : color.withAlpha((0.3 * 255).round()),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
