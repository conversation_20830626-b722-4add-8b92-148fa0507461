import 'package:constants/constants.dart';
import 'package:countries/countries.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:utils/utils.dart';

class CallListItem extends HookWidget {
  const CallListItem({
    required this.contactName,
    required this.phoneNumber,
    this.onViewMoreButtonPressed,
    this.onCallButtonPressed,
    this.callRate,
    this.isShowingOptions = false,
    this.isFavourited = false,
    this.countryFlag = 'flags/ng.svg',
    this.onFavoriteButtonPressed,
    this.onPressed,
    this.totalCallMins,
    this.totalCallCost,
    this.hasDemoFavouriteButton = false,
    super.key,
  });

  final String contactName;
  final String phoneNumber;

  final bool isFavourited;
  final bool isShowingOptions;

  final VoidCallback? onFavoriteButtonPressed;
  final VoidCallback? onPressed;
  final VoidCallback? onViewMoreButtonPressed;
  final VoidCallback? onCallButtonPressed;

  final String? totalCallMins;
  final String? totalCallCost;
  final String? callRate;
  final String? countryFlag;

  final bool hasDemoFavouriteButton;

  @override
  Widget build(BuildContext context) {
    final demoFavoriteState = useState(isFavourited);

    return ListTile(
      onTap: onPressed,
      minVerticalPadding: 0,
      minTileHeight: 100,
      contentPadding: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 1,
      ),
      title: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            child: Text(
              contactName,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: FroggyColors.black,
              ),
              textScaler: const TextScaler.linear(1.1),
            ),
          ),
          Builder(
            builder: (context) {
              if (onFavoriteButtonPressed != null) {
                return CallListFavouriteButton(
                  value: isFavourited,
                  onPressed: onFavoriteButtonPressed ?? () {},
                );
              }

              return CallListFavouriteButton(
                value: demoFavoriteState.value,
                onPressed: () =>
                    demoFavoriteState.value = !demoFavoriteState.value,
              );
            },
          ),
        ],
      ),
      subtitle: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  if (countryFlag != null)
                    Container(
                      margin: const EdgeInsets.only(right: 3),
                      child: FroggyCountries.showCountryFlag(
                        countryFlag!,
                        width: 30,
                        // height: 30,
                        margin: 0,
                      ),
                    ),
                  Text(
                    phoneNumber,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.black54,
                    ),
                  ),
                ],
              ),
              Text.rich(
                TextSpan(
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.black54,
                    fontWeight: FontWeight.w600,
                  ),
                  text: totalCallMins != null ? '$totalCallMins mins' : null,
                  children: [
                    const TextSpan(
                      text: ' ',
                    ),
                    TextSpan(
                      text: totalCallCost != null
                          ? '(${totalCallCost?.toCurrency(context)})'
                          : null,
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (isShowingOptions)
            Container(
              margin: const EdgeInsets.only(top: 5),
              child: CallListItemOptions(
                onCallButtonPressed: onCallButtonPressed,
                callRateForMobile: callRate,
                onViewButtonPressed: onViewMoreButtonPressed,
              ),
            ),
        ],
      ),
    );
  }
}
