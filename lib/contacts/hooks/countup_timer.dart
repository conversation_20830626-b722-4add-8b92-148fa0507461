import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

// Model class with timer control methods
class CountupTimerModel {
  const CountupTimerModel({
    required this.elapsed,
    required this.start,
    required this.stop,
    required this.isRunning,
  });

  final Duration elapsed;
  final void Function() start;
  final void Function() stop;
  final bool isRunning;
}

// Custom Hook implementation
class _CountUpTimerHook extends Hook<CountupTimerModel> {
  const _CountUpTimerHook();

  @override
  _CountUpTimerState createState() => _CountUpTimerState();
}

class _CountUpTimerState
    extends HookState<CountupTimerModel, _CountUpTimerHook> {
  late Stopwatch _stopwatch;
  Timer? _timer;
  bool _isRunning = false;

  @override
  void initHook() {
    super.initHook();
    _stopwatch = Stopwatch();
    _setupTimer();
  }

  void _setupTimer() {
    _timer = Timer.periodic(const Duration(milliseconds: 100), (_) {
      if (_isRunning) {
        setState(() {}); // Trigger rebuild to update elapsed time
      }
    });
  }

  void start() {
    if (!_isRunning) {
      _stopwatch.start();
      _isRunning = true;
      setState(() {});
    }
  }

  void stop() {
    if (_isRunning) {
      _stopwatch.stop();
      _isRunning = false;
      setState(() {});
    }
  }

  @override
  CountupTimerModel build(BuildContext context) {
    return CountupTimerModel(
      elapsed: _stopwatch.elapsed,
      start: start,
      stop: stop,
      isRunning: _isRunning,
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    _stopwatch.stop();
    super.dispose();
  }
}

CountupTimerModel useCountUpTimer() {
  return use(const _CountUpTimerHook());
}
