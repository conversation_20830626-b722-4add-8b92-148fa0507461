import 'package:common/common.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/contact_list/views/contact_list_view.dart';
import 'package:froggytalk/contacts/contacts.dart';

class ContactsOverviewPage extends HookWidget {
  const ContactsOverviewPage({
    this.showFavouriteContactsAndRecentCallsTab = true,
    super.key,
  });

  final bool showFavouriteContactsAndRecentCallsTab;

  static Route<Object?> modalRoute() {
    return MaterialPageRoute<Object?>(
      builder: (_) => const ContactsOverviewPage(
        showFavouriteContactsAndRecentCallsTab: false,
      ),
      barrierDismissible: true,
    );
  }

  static String routeName = '/contact-list';

  @override
  Widget build(BuildContext context) {
    return Builder(
      builder: (context) {
        if (showFavouriteContactsAndRecentCallsTab) {
          return const _BuildTabbedContent();
        }

        return const Scaffold(
          appBar: ContactListAppbar(),
          body: Padding(
            padding: EdgeInsets.symmetric(horizontal: 15),
            child: ContactListComponentView(),
          ),
        );
      },
    );
  }
}

class _BuildTabbedContent extends StatefulHookWidget {
  const _BuildTabbedContent();

  @override
  State<_BuildTabbedContent> createState() => _BuildTabbedContentState();
}

class _BuildTabbedContentState extends State<_BuildTabbedContent> {
  void onTabChanged(int index) {
    context.read<ContactTabsBloc>().add(ContactTabsEvent.tabChanged(index));
  }

  @override
  Widget build(BuildContext context) {
    final tabs = useContactTabs();

    return BlocBuilder<ContactTabsBloc, ContactTabsState>(
      builder: (context, tabState) {
        return CustomScrollView(
          shrinkWrap: true,
          slivers: [
            SliverToBoxAdapter(
              child: CallsTabBar(
                tabs: tabs,
                tabValue: tabState.selectedIndex,
                onTabChanged: onTabChanged,
              ),
            ),
            if (tabState.selectedIndex == 0)
              const SliverToBoxAdapter(child: RecentCallsSearch()),
            if (tabState.selectedIndex == 1)
              const SliverToBoxAdapter(child: ContactListSearch()),
            if (tabState.selectedIndex == 2)
              const SliverToBoxAdapter(child: FavouriteContactsSearch()),
            SliverToBoxAdapter(child: FroggySpacer.y16()),
            SliverToBoxAdapter(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.sizeOf(context).height * 0.45,
                ),
                child: Builder(
                  builder: (context) {
                    if (tabState.selectedIndex == 0) {
                      return const RecentCallsComponentView();
                    }

                    if (tabState.selectedIndex == 1) {
                      return const ContactListPv2age();
                      // return const ContactListComponentView();
                    }

                    return const FavouriteContactsComponentView();
                  },
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
