import 'package:common/common.dart';
import 'package:constants/constants.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:froggy_icons/froggy_icons.dart';
import 'package:froggytalk/call_rates/call_rates.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:froggytalk/dialer/dialer.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:permissions/permissions.dart';
import 'package:sip_ua/sip_ua.dart' as sip_ua;
import 'package:utils/utils.dart';

class ContactDetailsPage extends StatefulHookWidget {
  const ContactDetailsPage({
    super.key,
  });

  static String routeName = '/view_contact_details';

  @override
  State<ContactDetailsPage> createState() => _ContactDetailsPageState();

  static Route<Object?> route({
    required ContactListItemBloc bloc,
  }) {
    return MaterialPageRoute<void>(
      builder: (_) => BlocProvider.value(
        value: bloc,
        child: const ContactDetailsPage(),
      ),
    );
  }
}

class _ContactDetailsPageState extends State<ContactDetailsPage>
    implements sip_ua.SipUaHelperListener {
  final callRates = CallRatesRepository();

  @override
  void initState() {
    super.initState();
    context.read<sip_ua.SIPUAHelper>().addSipUaHelperListener(this);
  }

  @override
  void deactivate() {
    super.deactivate();
    context.read<sip_ua.SIPUAHelper>().removeSipUaHelperListener(this);
  }

  void navigateToCallingPage() {
    // Navigator.of(context).push(CallingUserPage.route());
    context.read<CallService>().makeCall(
          number:
              context.read<ContactListItemBloc>().state.formattedPhoneNumber,
        );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = useLocalizations();
    final dialerBloc = context.read<DialerBloc>();
    final contact = context.select(
      (ContactListItemBloc bloc) => bloc.state,
    );
    final callRate = contact.callRate;
    var callRateForMobile = '0.0';
    if (callRate != null) {
      callRateForMobile = callRate.amount!.toCurrency(
        context,
        symbol: callRate.currency!,
      );
    }

    final helper = context.read<sip_ua.SIPUAHelper>();

    final makeCall = useCallback(
      (
        String phoneNumber, {
        bool voiceOnly = true,
      }) async {
        if (defaultTargetPlatform == TargetPlatform.android ||
            defaultTargetPlatform == TargetPlatform.iOS) {
          await Permission.microphone.request();
          // await Permission.camera.request();
        }
        if (phoneNumber.isEmpty) {
          // await showDialog<void>(
          //   context: context,
          //   barrierDismissible: false,
          //   builder: (BuildContext context) {
          //     return AlertDialog(
          //       title: const Text('Target is empty.'),
          //       content: const Text('Please enter a SIP URI or username!'),
          //       actions: <Widget>[
          //         TextButton(
          //           child: const Text('Ok'),
          //           onPressed: () {
          //             Navigator.of(context).pop();
          //           },
          //         ),
          //       ],
          //     );
          //   },
          // );
          return null;
        }

        final mediaConstraints = <String, dynamic>{
          'audio': true,
          'video': {
            'mandatory': <String, dynamic>{
              'minWidth': '640',
              'minHeight': '480',
              'minFrameRate': '30',
            },
            'facingMode': 'user',
          },
        };

        MediaStream mediaStream;

        if (kIsWeb && !voiceOnly) {
          mediaStream =
              await navigator.mediaDevices.getDisplayMedia(mediaConstraints);
          mediaConstraints['video'] = false;
          final userStream =
              await navigator.mediaDevices.getUserMedia(mediaConstraints);
          final audioTracks = userStream.getAudioTracks();
          if (audioTracks.isNotEmpty) {
            await mediaStream.addTrack(audioTracks.first);
          }
        } else {
          if (voiceOnly) {
            mediaConstraints['video'] = !voiceOnly;
          }
          mediaStream =
              await navigator.mediaDevices.getUserMedia(mediaConstraints);
        }

        await helper.call(
          phoneNumber,
          voiceOnly: voiceOnly,
          mediaStream: mediaStream,
        );

        dialerBloc.add(
          DialerEvent.callStarted(
            phoneNumber: phoneNumber,
          ),
        );
        // _preferences.setString('dest', dest);
        return null;
      },
      [],
    );

    final showPermissionPermanentlyDeniedErrorModal = useCallback(
      () {
        RequestPermission.showModal(
          context,
          label: l10n.permissionPermanentlyDeniedMessage,
          allowAccessText: l10n.allowAccessButtonText,
          skipText: l10n.permissionButtonSkip,
          onPressed: openAppSettings,
        );
      },
      [],
    );

    final showPermissionDeniedErrorModal = useCallback(
      () {
        RequestPermission.showModal(
          context,
          label: l10n.permissionDeniedMessage,
          allowAccessText: l10n.allowAccessButtonText,
          skipText: l10n.permissionButtonSkip,
          onPressed: openAppSettings,
        );
      },
      [],
    );

    // final showPermissionAlert = useCallback(
    //   (String phoneNumber) {
    //     RequestPermission.showModal(
    //       context,
    //       label: l10n.permissionForMicrophoneTitle,
    //       allowAccessText: l10n.allowAccessButtonText,
    //       skipText: l10n.permissionButtonSkip,
    //       onPressed: () async {
    //         final status = await Permission.contacts.request();

    //         if (status == PermissionStatus.granted) {
    //           routeBack();
    //           WidgetsBinding.instance.addPostFrameCallback(
    //             (_) => makeCall(phoneNumber),
    //           );
    //         } else {
    //           routeBack();
    //           WidgetsBinding.instance.addPostFrameCallback(
    //             (_) => showErrorModal(),
    //           );
    //         }
    //       },
    //     );
    //   },
    //   [],
    // );

    final onCallButtonPressed = useCallback(
      (
        String phoneNumber, {
        bool voiceOnly = true,
      }) async {
        // const permissionMic = Permission.microphone;
        // final isGranted = await permissionMic.isGranted;

        // if (!isGranted) {
        //   WidgetsBinding.instance.addPostFrameCallback(
        //     (_) => showPermissionAlert(phoneNumber),
        //   );
        // } else {
        //   await makeCall(phoneNumber);
        // }

        await Permission.microphone.request().then((value) {
          if (value == PermissionStatus.granted) {
            makeCall(phoneNumber);
          } else if (value.isPermanentlyDenied) {
            showPermissionPermanentlyDeniedErrorModal();
          } else {
            showPermissionDeniedErrorModal();
          }
        });
      },
      [],
    );

    return BlocListener<DialerBloc, DialerState>(
      listener: (context, state) {
        if (state.status == DialerStatus.connecting) {
          // navigateToCallingPage();
        }
      },
      child: Scaffold(
        appBar: ViewContactDetailsAppBar(
          isFavourite: contact.isFavourited,
          onFavouriteButtonPressed: () =>
              context.read<ContactListItemBloc>().add(
                    const ContactListItemEvent.toggleAsFavourites(),
                  ),
        ),
        body: ListView(
          children: [
            FroggyAvatarWithName(
              name: contact.fullName,
              phoneNumber: contact.formattedPhoneNumber ??
                  contact.phoneNumber,
              flag: contact.countryFlag,
            ),
            // if (contact.isCallRateLoading)
            _ViewContactDetailsCallMinutes(
              callMinutes: callRateForMobile,
            ),
            _ViewContactDetailsActions(
              // onBuyCreditPressed: () =>
              //     Navigator.of(context).push(BuyCreditPage.route()),
              onCallPressed: callRateForMobile == '0.0'
                  ? null
                  : () {
                      onCallButtonPressed(
                        contact.formattedPhoneNumber ?? '',
                      );
                      // dialerBloc.add(
                      //   DialerEvent.callStarted(
                      //     phoneNumber: contact.formattedPhoneNumber ?? '',
                      //   ),
                      // );
                    },
            ),
            // const FroggyContainerWithCreamBg(
            //   label: 'Recent calls',
            // ),
            // Container(
            //   margin: const EdgeInsets.symmetric(horizontal: 15),
            //   child: LimitedBox(
            //     maxHeight: 500,
            //     child: ListView.builder(
            //       itemCount: 15,
            //       shrinkWrap: true,
            //       itemBuilder: (context, index) {
            //         final random = Random(index);
            //         final randomNumber = random.nextInt(100);

            //         return RecentCallLogItem(
            //           callLogType: randomNumber % 2 != 0
            //               ? CallLogType.missed
            //               : CallLogType.outgoing,
            //         );
            //       },
            //     ),
            //   ),
            // ),
          ],
        ),
      ),
    );
  }

  @override
  void callStateChanged(sip_ua.Call call, sip_ua.CallState state) {
    // Navigation is now handled centrally by CallService
    // Just log the state change for debugging
    FroggyLogger.debug(
      'ContactDetails: '
      'Call state changed to ${state.state} '
      'for call ${call.id}',
    );
  }

  @override
  void onNewMessage(sip_ua.SIPMessageRequest msg) {}

  @override
  void onNewNotify(sip_ua.Notify ntf) {}

  @override
  void onNewReinvite(sip_ua.ReInvite event) {}

  @override
  void registrationStateChanged(sip_ua.RegistrationState state) {}

  @override
  void transportStateChanged(sip_ua.TransportState state) {}
}

class _ViewContactDetailsCallMinutes extends StatelessWidget {
  const _ViewContactDetailsCallMinutes({required this.callMinutes});

  final String callMinutes;

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 3),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            margin: const EdgeInsets.only(right: 5),
            child: FroggyIconsList.phoneSolid.toWidget(
              color: FroggyColors.froggyGrey2,
              width: 15,
              height: 15,
            ),
          ),
          Text(
            l10n.perMinuteSlashLabel(callMinutes),
            style: const TextStyle(
              fontSize: 12,
              color: FroggyColors.froggyGrey2,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }
}

class _ViewContactDetailsActions extends HookWidget {
  const _ViewContactDetailsActions({
    this.onCallPressed,
  });

  // final VoidCallback? onBuyCreditPressed;
  final VoidCallback? onCallPressed;

  @override
  Widget build(BuildContext context) {
    // final buyCreditIcon = useMemoized(
    //   () {
    //     return FroggyIconsList.buyCredit.toWidget(
    //       color: FroggyColors.primary,
    //     );
    //   },
    // );
    final l10n = context.l10n;

    final callIcon = useMemoized(
      () => FroggyIconsList.phoneSolid.toWidget(
        color: FroggyColors.white,
      ),
    );

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 3),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Container(
          //   margin: const EdgeInsets.only(right: 10),
          //   child: OutlinedButton.icon(
          //     onPressed: onBuyCreditPressed,
          //     icon: buyCreditIcon,
          //     label: const Text(
          //       'Send credit',
          //       style: TextStyle(
          //         color: FroggyColors.primary,
          //         fontWeight: FontWeight.w600,
          //         fontSize: 16,
          //       ),
          //     ),
          //     style: OutlinedButton.styleFrom(
          //       elevation: 0,
          //       iconColor: FroggyColors.primary,
          //       backgroundColor: FroggyColors.froggyLighterGreen,
          //       side: BorderSide.none,
          //       padding:
          //           const EdgeInsets.symmetric(horizontal: 18, vertical: 10),
          //     ),
          //   ),
          // ),
          ElevatedButton.icon(
            onPressed: onCallPressed,
            icon: callIcon,
            label: Text(
              l10n.callButtonText,
              style: const TextStyle(
                color: FroggyColors.white,
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
            style: ElevatedButton.styleFrom(
              elevation: 0,
              padding: const EdgeInsets.symmetric(
                horizontal: 18,
                vertical: 10,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class RecentCallLogItem extends StatelessWidget {
  const RecentCallLogItem({
    this.totalCallRates = '2',
    this.callLogType = CallLogType.outgoing,
    this.createdAt = 'Jun 10th (Mon) 14:56',
    this.lengthOfCall = '10 mins 15 secs ',
    super.key,
  });

  final CallLogType callLogType;
  final String createdAt;
  final String lengthOfCall;
  final String totalCallRates;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            createdAt,
            style: const TextStyle(
              fontSize: 14,
              color: FroggyColors.black,
              fontWeight: FontWeight.w500,
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Container(
                    margin: const EdgeInsets.only(right: 4),
                    child: callLogType.icon,
                  ),
                  const Text(
                    'Outgoing call',
                    style: TextStyle(
                      fontSize: 12,
                      color: FroggyColors.froggyGrey2,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  Text(
                    lengthOfCall,
                    style: const TextStyle(
                      fontSize: 12,
                      color: FroggyColors.froggyGrey2,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  Text(
                    '(${totalCallRates.toCurrency(context, decimalDigits: 0)})',
                    style: const TextStyle(
                      fontSize: 12,
                      color: FroggyColors.froggyGrey2,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}
