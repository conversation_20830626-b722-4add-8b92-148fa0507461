part of 'contact_list_bloc.dart';

enum ContactListStatus { initial, loading, success, error }

extension ContactListStatusX on ContactListStatus {
  bool get isInitial => this == ContactListStatus.initial;
  bool get isLoading => this == ContactListStatus.loading;
  bool get isSuccess => this == ContactListStatus.success;
  bool get isError => this == ContactListStatus.error;
}

@freezed
class ContactListState with _$ContactListState {
  // const factory ContactListState.initial() = _Initial;
  // const factory ContactListState.loading() = ContactListLoading;
  // const factory ContactListState.error({String? errorMessage}) =
  //     ContactListError;
  // const factory ContactListState.success({
  //   @Default([]) List<Contact> contacts,
  // }) = ContactListSuccess;

  factory ContactListState({
    @Default([]) List<ContactModel> contacts,
    @Default([]) List<ContactModel> searchedContacts,
    String? errorMessage,
    @Default(ContactListStatus.initial) ContactListStatus status,
  }) = _ContactListState;

  const ContactListState._();

  // initial state
  factory ContactListState.initial() => ContactListState();

  // loading
  ContactListState loading() => copyWith(status: ContactListStatus.loading);

  // error
  ContactListState error({required String errorMessage}) =>
      copyWith(status: ContactListStatus.error, errorMessage: errorMessage);

  // success
  ContactListState success({required List<ContactModel> contacts}) => copyWith(
        status: ContactListStatus.success,
        contacts: contacts,
        searchedContacts: contacts,
      );

  ContactListState searchSuccess({required List<ContactModel> contacts}) =>
      copyWith(
        status: ContactListStatus.success,
        searchedContacts: contacts,
      );
}
