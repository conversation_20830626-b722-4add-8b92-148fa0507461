import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:fast_contacts/fast_contacts.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:froggytalk/contact_list/contact_list.dart';
import 'package:froggytalk/contacts/data/models/contact.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:utils/utils.dart';

part 'contact_list_bloc.freezed.dart';
part 'contact_list_event.dart';
part 'contact_list_state.dart';

class ContactListBlocV2 extends Bloc<ContactListEvent, ContactListState> {
  ContactListBlocV2() : super(ContactListState.initial()) {
    on<_Started>(_onStarted);
    on<_Searched>(
      _onSearched,
      transformer: debounce(duration: const Duration(milliseconds: 500)),
    );
  }

  FutureOr<void> _onStarted(
    _Started event,
    Emitter<ContactListState> emit,
  ) async {
    emit(state.loading());

    const permission = Permission.contacts;
    final status = await permission.request();

    if (status == PermissionStatus.granted) {
      final contacts = await FastContacts.getAllContacts(
        batchSize: 100,
      );

      final sortedContactsService = ContactSortService();
      final sortedContacts = await sortedContactsService.sortContacts(contacts);

      emit(state.success(contacts: sortedContacts));
    } else {
      emit(state.error(errorMessage: 'Permission not granted'));
    }
  }

  FutureOr<void> _onSearched(
    _Searched event,
    Emitter<ContactListState> emit,
  ) async {
    emit(state.loading());

    final contacts = state.contacts;

    final value = event.value;
    if (value == null) return;
    final lowerCasedSearchTerm = value.toLowerCase();
    emit(state.loading());
    // search contacts
    final results = contacts.where((contact) {
      return contact.name.toLowerCase().contains(lowerCasedSearchTerm) ||
          contact.phoneNumber.contains(lowerCasedSearchTerm);
    }).toList();

    emit(state.searchSuccess(contacts: results));
  }
}
