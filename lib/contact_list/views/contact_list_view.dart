import 'package:common/common.dart';
import 'package:constants/constants.dart';
import 'package:countries/countries.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggy_icons/froggy_icons.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/call_rates/data/models/call_rates_response.dart';
import 'package:froggytalk/call_rates/data/repositories/call_rates_repository.dart';
import 'package:froggytalk/contact_list/contact_list.dart';
import 'package:froggytalk/contacts/bloc/contact_list_item_bloc.dart';
import 'package:froggytalk/contacts/components/components.dart';
import 'package:froggytalk/contacts/data/models/call_log_type.dart';
import 'package:froggytalk/contacts/data/models/contact.dart';
import 'package:froggytalk/contacts/data/repositories/repositories.dart';
import 'package:froggytalk/contacts/data/services/services.dart';
import 'package:froggytalk/contacts/widgets/call_list_empty.dart';
import 'package:froggytalk/contacts/widgets/call_list_item_options.dart';
import 'package:froggytalk/contacts/widgets/view_contact_details_appbar.dart';
import 'package:froggytalk/dialer/bloc/dialer_bloc.dart';
import 'package:froggytalk/dialer/data/models/dialer_status.dart';
import 'package:froggytalk/dialer/services/call_service.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:permissions/permissions.dart';
import 'package:utils/utils.dart';

class ContactListPv2age extends StatelessWidget {
  const ContactListPv2age({super.key, this.showSearchBar = false});

  final bool showSearchBar;

  static Route<Object?> modalRoute() {
    return MaterialPageRoute<Object?>(
      builder: (_) => const ContactListPv2age(
        showSearchBar: true,
      ),
      barrierDismissible: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    final bloc = context.read<ContactListBlocV2>();

    return Scaffold(
      // appBar: AppBar(
      //   title: const Text('Contacts'),
      // ),
      body: SafeArea(
        child: BlocProvider.value(
          value: bloc..add(const ContactListEvent.started()),
          child: _ContactListView(
            showSearchBar: showSearchBar,
          ),
        ),
      ),
    );
  }
}

class _ContactListView extends HookWidget {
  const _ContactListView({this.showSearchBar = false});

  final bool showSearchBar;

  @override
  Widget build(BuildContext context) {
    // final hasInternetConnection = useHasConnectivity();
    final pageStorageKey =
        useMemoized(() => const PageStorageKey('contact_list'));

    return BlocBuilder<ContactListBlocV2, ContactListState>(
      builder: (context, state) {
        return CustomScrollView(
          key: pageStorageKey,
          physics: const AlwaysScrollableScrollPhysics(
            parent: BouncingScrollPhysics(),
          ),
          keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
          cacheExtent: 2000,
          slivers: [
            if (showSearchBar)
              const SliverAppBar(
                title: Padding(
                  padding: EdgeInsets.all(8),
                  child: ContactListSearchBarV2(),
                ),
                leading: CloseButton(),
                floating: true,
                snap: true,
                toolbarHeight: 80,
              ),
            if (state.status.isLoading)
              const SliverToBoxAdapter(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
              ),
            if (state.status.isSuccess && state.searchedContacts.isEmpty)
              SliverToBoxAdapter(
                child: Align(
                  heightFactor: 1,
                  alignment: Alignment.topCenter,
                  child: CallListEmpty(
                    message: context.l10n.emptyContactList,
                    buttonText: '',
                  ),
                ),
              ),
            if (state.status.isSuccess && state.searchedContacts.isNotEmpty)
              SliverList.builder(
                itemCount: state.searchedContacts.length,
                itemBuilder: (context, index) {
                  final contact = state.searchedContacts[index];

                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    // child: ContactListItem(
                    //   key: ValueKey(
                    //     contact.id,
                    //   ), // Add key for better reconciliation
                    //   item: contact,
                    //   hasInternetConnection: hasInternetConnection,
                    //   // hasInternetConnection: true,
                    // ),
                    child: _ContactListItem(
                      key: ValueKey(
                        contact.id,
                      ), // Add key for better reconciliation
                      item: contact,
                      // hasInternetConnection: hasInternetConnection,
                      // hasInternetConnection: true,
                    ),
                  );
                },
              ),
          ],
        );
      },
    );
  }
}

class ContactListSearchBarV2 extends HookWidget {
  const ContactListSearchBarV2({super.key});

  @override
  Widget build(BuildContext context) {
    final searchController = useSearchController();
    final l10n = AppLocalizations.of(context);
    final bloc = context.read<ContactListBlocV2>();

    // final defaultSearchTerm = context.select(
    //   (ContactListBloc bloc) => bloc.state.searchTerm,
    // );

    useEffect(
      () {
        void onSearchChanged() {
          bloc.add(
            ContactListEvent.searched(value: searchController.text),
          );
        }

        searchController.addListener(onSearchChanged);

        return () {
          searchController.removeListener(onSearchChanged);
        };
      },
      [bloc, searchController],
    );

    return Container(
      margin: const EdgeInsets.only(top: 10),
      child: FroggyTextFormFieldContainer(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        borderRadius: 300,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(300),
          child: SearchBar(
            controller: searchController,
            hintText: l10n.contactsSearchContactsPlaceholder,
            hintStyle: const WidgetStatePropertyAll(
              TextStyle(
                color: FroggyColors.froggyGrey35,
              ),
            ),
            textStyle: const WidgetStatePropertyAll(
              TextStyle(
                color: FroggyColors.froggyBlack,
              ),
            ),
            leading: FroggyIconsList.searchOutline.toWidget(
              color: FroggyColors.froggyGrey2,
            ),
            backgroundColor: const WidgetStatePropertyAll(
              FroggyColors.froggyGrey5,
            ),
            elevation: const WidgetStatePropertyAll(0),
          ),
        ),
      ),
    );
  }
}

class _ContactListItem extends StatefulHookWidget {
  const _ContactListItem({required this.item, super.key});

  final ContactModel item;

  @override
  State<_ContactListItem> createState() => _ContactListItemState();

  static final Map<String, String> _flagCache = {};

  // Add cache size limit and LRU functionality
  static const _maxCacheSize = 1000;
}

class _ContactListItemState extends State<_ContactListItem> {

  String? _countryCode;
  CallRatesAmountModel? _callRates;
  String? _callRateErrorMessage;
  bool _toggleDetailedAction = false;
  bool _callRatesLoading = false;
  // final bool _isFavourited = false;

  final _listFavouriteContactsRepository = ListFavouriteContactsRepository();
  final _addFavouriteContactsRepository = AddFavouriteContactRepository();
  final _deleteFavouriteContactsRepository = DeleteFavouriteContactRepository();

  @override
  void initState() {
    super.initState();
    _fetchCountryCode();
    // _checkFavouriteContact();
  }

  Future<void> _fetchCountryCode() async {
    final results = _getCountryFlagCached(
      widget.item.phoneNumber,
      phoneNumberService: context.read<PhoneNumberService>(),
    );
    setState(() {
      _countryCode = results;
    });
  }

  Future<void> _fetchCallRates() async {
    if (_countryCode == null) {
      setState(() {
        _callRatesLoading = false;
        _callRateErrorMessage = 'error found';
      });

      return;
    } else {
      setState(() {
        _callRatesLoading = true;
        _callRates = null;
        _callRateErrorMessage = null;
      });

      final repository = context.read<CallRatesRepository>();
      final response = await repository.execute(
        countryCode: _countryCode!,
      );

      response.fold((error) {
        setState(() {
          _callRatesLoading = false;
          _callRates = null;
          _callRateErrorMessage = error.message;
        });
      }, (success) {
        final callRate = success.data;

        final rateMobilePerMinute2 = callRate?.attributes?.rateMobilePerMinute;
        setState(() {
          _callRatesLoading = false;
          _callRateErrorMessage = null;
          _callRates = rateMobilePerMinute2;
        });
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final hasInternetConnection = useHasConnectivity();
    final callService = context.read<CallService>();
    final l10n = context.l10n;
    final dialerBloc = context.read<DialerBloc>();

    /// Initiates a call using the centralized CallService
    /// 
    /// [phoneNumber] - Phone number to call
    /// [voiceOnly] - Whether to make a voice-only call (default: true)
    final makeCall = useCallback(
      (
        String phoneNumber, {
        bool voiceOnly = true,
      }) async {
        if (phoneNumber.isEmpty) {
          return;
        }

        try {
          final trimmedPhoneNumber = phoneNumber.trim().removeEmptySpaces();

          // Use the centralized CallService to make the call
          await callService.makeCall(
            number: trimmedPhoneNumber,
            voiceOnly: voiceOnly,
          );

          // Update dialer bloc state
          dialerBloc.add(
            DialerEvent.callStarted(
              phoneNumber: phoneNumber,
            ),
          );
        } catch (e) {
          // Error handling is done in CallService
          // Could add additional UI error handling here if needed
          FroggyLogger.error('Error making call: $e');
        }
      },
      [callService, dialerBloc],
    );

    // final routeBack = useCallback(
    //   () {
    //     Navigator.of(context).pop();
    //   },
    //   [],
    // );

    final showPermissionPermanentlyDeniedErrorModal = useCallback(
      () {
        RequestPermission.showModal(
          context,
          label: l10n.permissionPermanentlyDeniedMessage,
          allowAccessText: l10n.allowAccessButtonText,
          skipText: l10n.permissionButtonSkip,
          onPressed: openAppSettings,
        );
      },
      [],
    );

    final showPermissionDeniedErrorModal = useCallback(
      () {
        RequestPermission.showModal(
          context,
          label: l10n.permissionDeniedMessage,
          allowAccessText: l10n.allowAccessButtonText,
          skipText: l10n.permissionButtonSkip,
          onPressed: openAppSettings,
        );
      },
      [],
    );

    // final showPermissionAlert = useCallback(
    //   (String phoneNumber) {
    //     RequestPermission.showModal(
    //       context,
    //       label: l10n.permissionForMicrophoneTitle,
    //       allowAccessText: l10n.allowAccessButtonText,
    //       skipText: l10n.permissionButtonSkip,
    //       onPressed: () async {
    //         final status = await Permission.contacts.request();

    //         if (status == PermissionStatus.granted) {
    //           routeBack();

    //           WidgetsBinding.instance.addPostFrameCallback(
    //             (_) => makeCall(phoneNumber),
    //           );
    //         } else {
    //           routeBack();
    //           WidgetsBinding.instance.addPostFrameCallback(
    //             (_) => showPermissionPermanentlyDeniedErrorModal(),
    //           );
    //         }
    //       },
    //     );
    //   },
    //   [],
    // );

    final onCallButtonPressed = useCallback(
      (
        String phoneNumber, {
        bool voiceOnly = true,
      }) async {
        // const permissionMic = Permission.microphone;
        // final isGranted = await permissionMic.isGranted;

        // if (!isGranted) {
        //   WidgetsBinding.instance.addPostFrameCallback(
        //     (_) => showPermissionAlert(phoneNumber),
        //   );
        // } else {
        //   await makeCall(phoneNumber);
        // }

        await Permission.microphone.request().then((value) {
          if (value == PermissionStatus.granted) {
            makeCall(phoneNumber);
          } else if (value.isPermanentlyDenied) {
            showPermissionPermanentlyDeniedErrorModal();
          } else {
            showPermissionDeniedErrorModal();
          }
        });
      },
      [],
    );

    final isFavourited = useState(false);

    final checkFavouriteContact = useCallback(
      () async {
        final response = await _listFavouriteContactsRepository.execute();

        if (response.isRight) {
          final favouriteContacts = response.right.data ?? [];
          final phoneNumber2 = widget.item.phoneNumber;
          final isFavourite = favouriteContacts
              .any((contact) => contact.phoneNumber == phoneNumber2);
          isFavourited.value = isFavourite;
          // setState(() {
          //   _isFavourited = isFavourite;
          // });
        }
      },
      [],
    );

    final toggleFavouriteButtonPressed = useCallback(
      () async {
        if (isFavourited.value) {
          final response = await _deleteFavouriteContactsRepository.execute(
            phoneNumber: widget.item.phoneNumber,
          );

          if (response.isRight) {
            isFavourited.value = false;
          } else {}
        } else {
          final response = await _addFavouriteContactsRepository.execute(
            name: widget.item.name,
            phoneNumber: widget.item.phoneNumber,
          );

          if (response.isRight) {
            isFavourited.value = true;
          } else {}
        }
      },
      [],
    );

    useEffect(() {
      checkFavouriteContact();
      return null;
    }, [checkFavouriteContact],);

    return ListTile(
      minVerticalPadding: 0,
      // minTileHeight: !hasInternetConnection ? null : 100,
      contentPadding: const EdgeInsets.symmetric(vertical: 1),
      onTap: () {
        _fetchCallRates();
        setState(() {
          _toggleDetailedAction = !_toggleDetailedAction;
        });
      },
      title: ContactHeader(
        fullName: widget.item.name,
        isFavourite: isFavourited.value,
        onFavouritePressed: toggleFavouriteButtonPressed,
        hasInternetConnection: hasInternetConnection,
      ),
      subtitle: Column(
        children: [
          ContactInfo(
            countryFlag:
                _countryCode != null ? 'flags/$_countryCode.svg' : null,
            phoneNumber: widget.item.phoneNumber,
          ),
          if (_toggleDetailedAction)
            _ContactActions(
              contact: widget.item.copyWith(
                countryCode: _countryCode,
                callRateInfo: CallRatesResponse(
                  id: '',
                  attributes: CallRatesAttributesModel(
                    rateMobilePerMinute: _callRates,
                  ),
                ),
              ),
              isCallRateLoading: _callRatesLoading,
              callRate: _callRates,
              callRateErrorMessage: _callRateErrorMessage,
              onCallPressed:
                  // hasInternetConnection
                  //     ? null
                  //     :
                  () => onCallButtonPressed(widget.item.phoneNumber),
            ),
        ],
      ),
    );
  }

  /// Retrieves the country flag for a given phone number, using a cache to
  /// improve performance. If the flag is found in the cache, it is returned
  /// immediately. Otherwise, the flag is fetched, stored in the cache, and
  /// then returned.
  ///
  /// The cache uses a simple FIFO (First In, First Out) strategy to manage
  /// its size, removing the oldest entry when the maximum cache size is
  /// reached.
  ///
  /// - Parameters:
  ///   - phoneNumber: The phone number for which to retrieve the country flag.
  /// - Returns: The country flag as a string, or null if the country code
  ///   cannot be determined.
  String? _getCountryFlagCached(
    String phoneNumber, {
    PhoneNumberService? phoneNumberService,
  }) {
    final countryCode = phoneNumberService?.getCountryCode(phoneNumber);
    if (countryCode == null) return null;

    // Check cache first with performance logging
    final startTime = DateTime.now();
    if (_ContactListItem._flagCache.containsKey(countryCode)) {
      final duration = DateTime.now().difference(startTime);
      FroggyLogger.debug('Cache hit in ${duration.inMicroseconds}μs');
      return _ContactListItem._flagCache[countryCode];
    }

    // Implement cache size management
    if (_ContactListItem._flagCache.length >= _ContactListItem._maxCacheSize) {
      _ContactListItem._flagCache
          .remove(_ContactListItem._flagCache.keys.first); // Simple FIFO
    }

    // Get and cache flag
    final flag =
        FroggyCountries.getInstance().findCountryByCountryCode(countryCode);
    _ContactListItem._flagCache[countryCode] = flag?.code ?? '';
    return flag?.code;
  }
}

class _ContactActions extends StatelessWidget {
  const _ContactActions({
    required this.isCallRateLoading,
    required this.callRate,
    this.onCallPressed,
    this.contact,
    this.callRateErrorMessage,
  });

  final VoidCallback? onCallPressed;
  final bool isCallRateLoading;
  final CallRatesAmountModel? callRate;
  final String? callRateErrorMessage;
  final ContactModel? contact;

  String _formatCallRate(CallRatesAmountModel? callRate, BuildContext context) {
    if (callRate?.amount == null) return '0.0';
    return callRate!.amount!.toCurrency(
      context,
      symbol: callRate.currency!,
    );
  }

  void _navigateToDetails(BuildContext context) {
    Navigator.of(context).push(
      _ContactDetailsPage.route(
        contact: contact!,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
      margin: const EdgeInsets.only(top: 5),
      child: CallListItemOptions(
        isCallRateAvailable: isCallRateLoading,
        callRateForMobile: _formatCallRate(callRate, context),
        onCallButtonPressed:
            callRateErrorMessage != null ? null : onCallPressed,
        onViewButtonPressed: () => _navigateToDetails(context),
      ),
    );
  }
}

class _ContactDetailsPage extends StatefulHookWidget {
  const _ContactDetailsPage({
    required this.item,
  });

  final ContactModel item;

  @override
  State<_ContactDetailsPage> createState() => _ContactDetailsPageState();

  static Route<Object?> route({
    required ContactModel contact,
  }) {
    return MaterialPageRoute<void>(
      builder: (_) => _ContactDetailsPage(
        item: contact,
      ),
    );
  }
}

class _ContactDetailsPageState extends State<_ContactDetailsPage> {
  // final callRates = CallRatesRepository();

  @override
  void initState() {
    super.initState();
  }

  @override
  void deactivate() {
    super.deactivate();
  }

  void navigateToCallingPage() {
    // Navigator.of(context).push(CallingUserPage.route());
    context.read<CallService>().makeCall(
          number:
              context.read<ContactListItemBloc>().state.formattedPhoneNumber,
        );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = useLocalizations();
    final dialerBloc = context.read<DialerBloc>();
    final callService = context.read<CallService>();
    final contact = widget.item;
    final callRate = contact.callRateInfo?.attributes?.rateMobilePerMinute;
    var callRateForMobile = '0.0';
    if (callRate != null) {
      callRateForMobile = callRate.amount!.toCurrency(
        context,
        symbol: callRate.currency!,
      );
    }

    /// Initiates a call using the centralized CallService
    /// 
    /// [phoneNumber] - Phone number to call
    /// [voiceOnly] - Whether to make a voice-only call (default: true)
    final makeCall = useCallback(
      (
        String phoneNumber, {
        bool voiceOnly = true,
      }) async {
        if (phoneNumber.isEmpty) {
          return;
        }

        try {
          // Use the centralized CallService to make the call
          await callService.makeCall(
            number: phoneNumber,
            voiceOnly: voiceOnly,
          );

          // Update dialer bloc state
          dialerBloc.add(
            DialerEvent.callStarted(
              phoneNumber: phoneNumber,
            ),
          );
        } catch (e) {
          // Error handling is done in CallService
          // Could add additional UI error handling here if needed
          FroggyLogger.error('Error making call: $e');
        }
      },
      [callService, dialerBloc],
    );

    final showPermissionPermanentlyDeniedErrorModal = useCallback(
      () {
        RequestPermission.showModal(
          context,
          label: l10n.permissionPermanentlyDeniedMessage,
          allowAccessText: l10n.allowAccessButtonText,
          skipText: l10n.permissionButtonSkip,
          onPressed: openAppSettings,
        );
      },
      [],
    );

    final showPermissionDeniedErrorModal = useCallback(
      () {
        RequestPermission.showModal(
          context,
          label: l10n.permissionDeniedMessage,
          allowAccessText: l10n.allowAccessButtonText,
          skipText: l10n.permissionButtonSkip,
          onPressed: openAppSettings,
        );
      },
      [],
    );

    // final showPermissionAlert = useCallback(
    //   (String phoneNumber) {
    //     RequestPermission.showModal(
    //       context,
    //       label: l10n.permissionForMicrophoneTitle,
    //       allowAccessText: l10n.allowAccessButtonText,
    //       skipText: l10n.permissionButtonSkip,
    //       onPressed: () async {
    //         final status = await Permission.contacts.request();

    //         if (status == PermissionStatus.granted) {
    //           routeBack();
    //           WidgetsBinding.instance.addPostFrameCallback(
    //             (_) => makeCall(phoneNumber),
    //           );
    //         } else {
    //           routeBack();
    //           WidgetsBinding.instance.addPostFrameCallback(
    //             (_) => showErrorModal(),
    //           );
    //         }
    //       },
    //     );
    //   },
    //   [],
    // );

    final onCallButtonPressed = useCallback(
      (
        String phoneNumber, {
        bool voiceOnly = true,
      }) async {
        // const permissionMic = Permission.microphone;
        // final isGranted = await permissionMic.isGranted;

        // if (!isGranted) {
        //   WidgetsBinding.instance.addPostFrameCallback(
        //     (_) => showPermissionAlert(phoneNumber),
        //   );
        // } else {
        //   await makeCall(phoneNumber);
        // }

        await Permission.microphone.request().then((value) {
          if (value == PermissionStatus.granted) {
            makeCall(phoneNumber);
          } else if (value.isPermanentlyDenied) {
            showPermissionPermanentlyDeniedErrorModal();
          } else {
            showPermissionDeniedErrorModal();
          }
        });
      },
      [],
    );

    return BlocListener<DialerBloc, DialerState>(
      listener: (context, state) {
        if (state.status == DialerStatus.connecting) {
          // navigateToCallingPage();
        }
      },
      child: Scaffold(
        appBar: ViewContactDetailsAppBar(
          isFavourite: true,
          onFavouriteButtonPressed: () =>
              context.read<ContactListItemBloc>().add(
                    const ContactListItemEvent.toggleAsFavourites(),
                  ),
        ),
        body: ListView(
          children: [
            FroggyAvatarWithName(
              name: contact.name,
              phoneNumber: contact.phoneNumber,
              flag: 'flags/${contact.countryCode}.svg',
            ),
            // if (contact.isCallRateLoading)
            _ViewContactDetailsCallMinutes(
              callMinutes: callRateForMobile,
            ),
            _ViewContactDetailsActions(
              // onBuyCreditPressed: () =>
              //     Navigator.of(context).push(BuyCreditPage.route()),
              onCallPressed: callRateForMobile == '0.0'
                  ? null
                  : () {
                      onCallButtonPressed(contact.phoneNumber);
                      // dialerBloc.add(
                      //   DialerEvent.callStarted(
                      //     phoneNumber: contact.formattedPhoneNumber ?? '',
                      //   ),
                      // );
                    },
            ),
            // const FroggyContainerWithCreamBg(
            //   label: 'Recent calls',
            // ),
            // Container(
            //   margin: const EdgeInsets.symmetric(horizontal: 15),
            //   child: LimitedBox(
            //     maxHeight: 500,
            //     child: ListView.builder(
            //       itemCount: 15,
            //       shrinkWrap: true,
            //       itemBuilder: (context, index) {
            //         final random = Random(index);
            //         final randomNumber = random.nextInt(100);

            //         return RecentCallLogItem(
            //           callLogType: randomNumber % 2 != 0
            //               ? CallLogType.missed
            //               : CallLogType.outgoing,
            //         );
            //       },
            //     ),
            //   ),
            // ),
          ],
        ),
      ),
    );
  }
}

class _ViewContactDetailsCallMinutes extends StatelessWidget {
  const _ViewContactDetailsCallMinutes({required this.callMinutes});

  final String callMinutes;

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 3),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            margin: const EdgeInsets.only(right: 5),
            child: FroggyIconsList.phoneSolid.toWidget(
              color: FroggyColors.froggyGrey2,
              width: 15,
              height: 15,
            ),
          ),
          Text(
            l10n.perMinuteSlashLabel(callMinutes),
            style: const TextStyle(
              fontSize: 12,
              color: FroggyColors.froggyGrey2,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }
}

class _ViewContactDetailsActions extends HookWidget {
  const _ViewContactDetailsActions({
    this.onCallPressed,
  });

  // final VoidCallback? onBuyCreditPressed;
  final VoidCallback? onCallPressed;

  @override
  Widget build(BuildContext context) {
    // final buyCreditIcon = useMemoized(
    //   () {
    //     return FroggyIconsList.buyCredit.toWidget(
    //       color: FroggyColors.primary,
    //     );
    //   },
    // );
    final l10n = context.l10n;

    final callIcon = useMemoized(
      () => FroggyIconsList.phoneSolid.toWidget(
        color: FroggyColors.white,
      ),
    );

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 3),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Container(
          //   margin: const EdgeInsets.only(right: 10),
          //   child: OutlinedButton.icon(
          //     onPressed: onBuyCreditPressed,
          //     icon: buyCreditIcon,
          //     label: const Text(
          //       'Send credit',
          //       style: TextStyle(
          //         color: FroggyColors.primary,
          //         fontWeight: FontWeight.w600,
          //         fontSize: 16,
          //       ),
          //     ),
          //     style: OutlinedButton.styleFrom(
          //       elevation: 0,
          //       iconColor: FroggyColors.primary,
          //       backgroundColor: FroggyColors.froggyLighterGreen,
          //       side: BorderSide.none,
          //       padding:
          //           const EdgeInsets.symmetric(horizontal: 18, vertical: 10),
          //     ),
          //   ),
          // ),
          ElevatedButton.icon(
            onPressed: onCallPressed,
            icon: callIcon,
            label: Text(
              l10n.callButtonText,
              style: const TextStyle(
                color: FroggyColors.white,
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
            style: ElevatedButton.styleFrom(
              elevation: 0,
              padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 10),
            ),
          ),
        ],
      ),
    );
  }
}

class RecentCallLogItem extends StatelessWidget {
  const RecentCallLogItem({
    this.totalCallRates = '2',
    this.callLogType = CallLogType.outgoing,
    this.createdAt = 'Jun 10th (Mon) 14:56',
    this.lengthOfCall = '10 mins 15 secs ',
    super.key,
  });

  final CallLogType callLogType;
  final String createdAt;
  final String lengthOfCall;
  final String totalCallRates;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            createdAt,
            style: const TextStyle(
              fontSize: 14,
              color: FroggyColors.black,
              fontWeight: FontWeight.w500,
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Container(
                    margin: const EdgeInsets.only(right: 4),
                    child: callLogType.icon,
                  ),
                  const Text(
                    'Outgoing call',
                    style: TextStyle(
                      fontSize: 12,
                      color: FroggyColors.froggyGrey2,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  Text(
                    lengthOfCall,
                    style: const TextStyle(
                      fontSize: 12,
                      color: FroggyColors.froggyGrey2,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  Text(
                    '(${totalCallRates.toCurrency(context, decimalDigits: 0)})',
                    style: const TextStyle(
                      fontSize: 12,
                      color: FroggyColors.froggyGrey2,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}
