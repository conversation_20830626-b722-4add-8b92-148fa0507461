import 'dart:isolate';

import 'package:fast_contacts/fast_contacts.dart';
import 'package:froggytalk/contacts/data/models/contact.dart';
import 'package:froggytalk/contacts/data/services/phone_number_service.dart';

class ContactSortService {
  /// Entry point for the isolate
  static void _sortContacts(SendPort sendPort) {
    // Listen for messages from the main thread
    final port = ReceivePort();
    sendPort.send(port.sendPort);

    port.listen((dynamic message) {
      final messageList = message as List<dynamic>;
      final data = messageList[0] as List<Contact>;
      final replyTo = messageList[1] as SendPort;
      final phoneService = messageList[2] as PhoneNumberService;

      // Remove contacts with empty phone numbers and sort them
      final filteredData = data
          .where((contact) => contact.phones.isNotEmpty)
          .toList()
          ..sort((a, b) => a.displayName.compareTo(b.displayName));

      final mappedContact = filteredData.map(
        (contact) {
          final number2 = contact.phones.first.number;
          final number1 = number2.length >= 4
              ? phoneService.format(number2, country: '') ?? number2
              : number2;
          // final countryCode = _getCountryFlagCached(
          //   number1,
          //   phoneNumberService: phoneService,
          // );

          return ContactModel(
            id: contact.id,
            name: contact.displayName,
            phoneNumber: number1,
            // countryCode: countryCode,
            // dialingCode:
          );
        },
      ).toList();

      // Send sorted list back to the main thread
      replyTo.send(mappedContact);
    });
  }

  /// Sort the contacts using an isolate
  Future<List<ContactModel>> sortContacts(List<Contact> contacts) async {
    final receivePort = ReceivePort();
    final isolate = await Isolate.spawn(_sortContacts, receivePort.sendPort);

    // Get the SendPort of the isolate
    final sendPort = await receivePort.first as SendPort;

    final response = ReceivePort();
    final phoneService = PhoneNumberService();
    sendPort.send([contacts, response.sendPort, phoneService]);

    // Wait for the sorted list from the isolate
    final sortedContacts = await response.first as List<ContactModel>;

    // Kill the isolate after the work is done
    isolate.kill();

    return sortedContacts;
  }
}
