import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/app/app.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:utils/utils.dart';

/// A custom hook that provides the current connectivity status by listening
/// to the `ConnectivityBloc`'s stream.
///
/// This hook uses the `useContext` hook to get the current `BuildContext`
/// and accesses the `ConnectivityBloc` from the context. It then uses the
/// `useStream` hook to listen to the `ConnectivityBloc`'s stream and get
/// the current connectivity status.
///
/// Returns the current `ConnectivityStatus`, which can be either connected
/// or disconnected.
ConnectivityStatus useConnectivityStatus() {
  // Get the current BuildContext
  final context = useContext();

  // Access the ConnectivityBloc from the context
  final bloc = BlocProvider.of<ConnectivityBloc>(context);

  // Use the useStream hook to listen to the ConnectivityBloc's stream
  // and get the current connectivity status
  final stream = useStream<ConnectivityStatus>(
    bloc.stream.map((state) => state.status),
    initialData: bloc.state.status,
  );

  // Return the current connectivity status
  return stream.data ?? ConnectivityStatus.connected;
}

/// A hook that monitors device connectivity status.
/// Returns true if the device has internet connectivity via mobile or WiFi.
bool useConnectivityPlus() {
  // Cache Connectivity instance
  final connectivity = useMemoized(Connectivity.new);

  // State for connection status
  final status = useState(false);

  useEffect(
    () {
      var isSubscribed = true;

      // Check initial connection status
      connectivity.checkConnectivity().then(
        (results) {
          if (isSubscribed) {
            final hasInternet = results.any(
              (result) =>
                  result == ConnectivityResult.mobile ||
                  result == ConnectivityResult.wifi,
            );
            FroggyLogger.debug('Initial connectivity status: $hasInternet');
            status.value = hasInternet;
          }
        },
        onError: (Object error) {
          FroggyLogger.debug('Error checking initial connectivity: $error');
        },
      );

      // Listen to connectivity changes
      final subscription = connectivity.onConnectivityChanged.distinct().listen(
        (results) {
          if (isSubscribed) {
            final hasInternet = results.any(
              (result) =>
                  result == ConnectivityResult.mobile ||
                  result == ConnectivityResult.wifi,
            );
            FroggyLogger.debug('Connectivity changed: $hasInternet');
            status.value = hasInternet;
          }
        },
        onError: (Object error) {
          FroggyLogger.debug('Connectivity stream error: $error');
        },
      );

      return () {
        isSubscribed = false;
        subscription.cancel();
      };
    },
    [connectivity],
  );

  return status.value;
}

/// A hook that checks the current internet connectivity status.
///
/// This hook uses the `ConnectivityManager` to determine the current
/// connectivity status and checks if the device has internet access.
///
/// Returns `true` if the device has internet access, otherwise `false`.
///
/// - Returns: A boolean indicating whether the device has internet access.
bool useHasInternet() {
  final context = useContext();
  // Use the get the current connectivity status
  final connectivityManager = context.read<ConnectivityManager>();
  final connectivityStatus = useStream(
    connectivityManager.hasInternetAccess,
  );

  if (connectivityStatus.hasError) {
    // Handle error
    return false;
  }

  final status = connectivityStatus.data;
  return status ?? false;
}

bool useHasConnectivity() {
// Cache Connectivity instance
  final connectivity = useMemoized(InternetConnection.new);

  // State for connection status
  final status = useState(false);

  useEffect(
    () {
      var isSubscribed = true;

      // Check initial connection status
      connectivity.hasInternetAccess.then(
        (bool results) {
          if (isSubscribed) {
            final hasInternet = results;
            FroggyLogger.debug('Initial connectivity status: $hasInternet');
            status.value = hasInternet;
          }
        },
        onError: (Object error) {
          FroggyLogger.debug('Error checking initial connectivity: $error');
        },
      );

      // Listen to connectivity changes
      final subscription = connectivity.onStatusChange.listen(
        (results) {
          if (isSubscribed) {
            final hasInternet = results == InternetStatus.connected;
            FroggyLogger.debug('Connectivity changed: $hasInternet');
            status.value = hasInternet;
          }
        },
        onError: (Object error) {
          FroggyLogger.debug('Connectivity stream error: $error');
        },
      );

      return () {
        isSubscribed = false;
        subscription.cancel();
      };
    },
    [connectivity],
  );

  return status.value;
}
