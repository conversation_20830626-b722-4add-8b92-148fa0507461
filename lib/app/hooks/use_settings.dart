import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/app/app.dart';

/// A custom hook that provides access to app settings from [AppBloc].
///
/// This hook efficiently manages settings state and provides convenient methods
/// to access specific settings values.
///
/// Example usage:
/// ```dart
/// final settings = useSettings();
/// final stringSetting = settings.getSetting<String>('key_name');
/// final intSetting = settings.getSetting<int>('number_key');
/// final boolSetting = settings.getSetting<bool>('flag_key');
/// ```
///
/// Returns a [UseSettingsState] object that contains settings data and utility
/// methods.
UseSettingsState useSettings() {
  final context = useContext();
  final state = context.watch<AppBloc>().state;
  final settings = state.settings;

  return useMemoized(
    () => UseSettingsState(settings),
    [settings],
  );
}

/// Represents the state and utility methods for handling settings.
class UseSettingsState {
  const UseSettingsState(this._settings);

  final List<SettingsResponse> _settings;

  /// Returns all available settings.
  List<SettingsResponse> get allSettings => _settings;

  /// Gets the value of a specific setting with type safety.
  ///
  /// Example:
  /// ```dart
  /// final stringValue = getSetting<String>('key');
  /// final intValue = getSetting<int>('number_key');
  /// final boolValue = getSetting<bool>('flag_key');
  /// ```
  ///
  /// Returns null if the setting is not found or cannot be converted to type T.
  T? getSetting<T>(String key) {
    final value = getSettingValue(key);
    if (value == null) return null;

    try {
      if (T == String) return value as T;
      if (T == bool) {
        return (value == '1' || (value as String).toLowerCase() == 'true') as T;
      }
      if (T == int) return int.parse(value as String) as T;
      if (T == double) return double.parse(value as String) as T;
      throw Exception('Unsupported type: $T');
    } catch (_) {
      return null;
    }
  }

  /// Gets the raw string value of a specific setting.
  ///
  /// Returns null if the setting is not found.
  dynamic getSettingValue(String key) {
    final setting = _settings.firstWhere(
      (setting) => setting.key == key,
      orElse: () => SettingsResponse(key: key, value: null),
    );

    return setting.value;
  }

  /// Checks if a specific setting exists.
  bool hasSetting(String key) {
    return _settings.any((setting) => setting.key == key);
  }
}
