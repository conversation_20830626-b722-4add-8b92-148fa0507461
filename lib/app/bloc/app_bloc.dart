import 'dart:async';

import 'package:facebook_app_events/facebook_app_events.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:froggytalk/app/app.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:seeip_client/seeip_client.dart';
import 'package:utils/utils.dart';

part 'app_bloc.freezed.dart';
part 'app_bloc.g.dart';
part 'app_event.dart';
part 'app_state.dart';

class AppBloc extends HydratedBloc<AppEvent, AppState> {
  AppBloc({
    required this.facebookAppEvents,
    required this.connectivityBloc,
    required this.settings,
  }) : super(AppState.initial()) {
    on<_Started>(_onStarted);
    on<_UpdatedMessage>(_onUpdatedMessage);
  }

  final FacebookAppEvents facebookAppEvents;
  final ConnectivityBloc connectivityBloc;
  final FetchSettingsRepository settings;

  @override
  AppState? fromJson(Map<String, dynamic> json) {
    try {
      return AppState.fromJson(json);
    } catch (_) {
      return AppState.initial();
    }
  }

  @override
  Map<String, dynamic>? toJson(AppState state) {
    try {
      return {
        // 'status': state.status,
        // 'deviceCountryCode': state.deviceCountryCode,
        // 'deviceId': state.deviceId,
        'bundleId': state.bundleId,
        'bundleVersion': state.bundleVersion,
        'buildNumber': state.buildNumber,
        // 'message': state.message,
      };
      // return state.toJson();
    } catch (_) {
      return null;
    }
  }

  Future<FutureOr<void>> _onStarted(
    _Started event,
    Emitter<AppState> emit,
  ) async {
    // if first time, then show the splash screen
    final deviceId = await getDeviceIdentifier();
    final version = await getAppVersion();

    String appCountryCode;

    try {
      appCountryCode = AppInstance.getInstance().geoIPInfo.countryCode;

      await Stripe.instance.applySettings();
    } catch (e) {
      appCountryCode = '';
    }

    /// get the device information from their IP address
    try {
      // get device ip information
      final geoIpInfo = await SeeipClient().getGeoIP();
      AppInstance.getInstance().geoIPInfo = geoIpInfo;
    } catch (e) {
      FroggyLogger.error('SeeIP Client Error: $e');
    }

    // try {
    // FroggyLogger.info('Settings Executingzzz');
    final settingsResult = await settings.execute();

    if (settingsResult.isRight) {
      FroggyLogger.info('Settings Success: ${settingsResult.right}');
      emit(state.copyWith(settings: settingsResult.right.data ?? []));
    } else {
      FroggyLogger.error('Settings Error: ${settingsResult.left.message}');
    }

    // settingsResult.fold(
    //   (error) {
    //     FroggyLogger.error('Settings Error: ${error.message}');
    //   },
    //   (response) {
    //     final settingsData = response.data ?? [];

    //     print('the settings data is $settingsData');
    //     // var iosSettings = SettingsResponse(
    //     //   key: 'ios_buy_credit_button',
    //     //   value: '0',
    //     // );

    //     // for (final setting in settingsData) {
    //     //   if (setting.key == 'ios_buy_credit_button') {
    //     //     iosSettings = setting;
    //     //     break;
    //     //   }
    //     // }

    //     emit(state.copyWith(settings: settingsData));
    //   },
    // );
    // } catch (e) {
    //   FroggyLogger.error('App Settings Error: $e');
    // }

    // initialize the local notifications
    // FroggyLocalNotifications.ensureInitialized();

    // Profile mode configurations
    if (kProfileMode) {
      FroggyLogger.info('App is running in Profile Mode');
    }

    emit(
      state.copyWith(
        status: AppStateStatus.initial,
        deviceCountryCode: appCountryCode,
        deviceId: deviceId,
        bundleVersion: version,
        bundleId: await getPackageId(),
      ),
    );
  }

  FutureOr<void> _onUpdatedMessage(
    _UpdatedMessage event,
    Emitter<AppState> emit,
  ) {
    emit(state.copyWith(message: event.message));
  }
}
