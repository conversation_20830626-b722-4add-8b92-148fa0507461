part of 'connectivity_bloc.dart';

@freezed
class ConnectivityState with _$ConnectivityState {
  // Default state constructor
  const factory ConnectivityState({
    @Default(false) bool isRetrying,
    @Default(ConnectivityStatus.connected) ConnectivityStatus status,
  }) = _Initial;

  // Private constructor for shared functionality
  const ConnectivityState._();

  // Union cases
  const factory ConnectivityState.connected({
    @Default(false) bool isRetrying,
    @Default(ConnectivityStatus.connected) ConnectivityStatus status,
  }) = Connected;

  const factory ConnectivityState.disconnected({
    @Default(false) bool isRetrying,
    @Default(ConnectivityStatus.disconnected) ConnectivityStatus status,
  }) = Disconnected;

  const factory ConnectivityState.reconnected({
    @Default(false) bool isRetrying,
    @Default(ConnectivityStatus.reconnected) ConnectivityStatus status,
  }) = Reconnected;

  const factory ConnectivityState.probablyNoNetwork({
    @Default(false) bool isRetrying,
    @Default(ConnectivityStatus.connectedButProbablyNoNetwork)
    ConnectivityStatus status,
  }) = ProbablyNoNetwork;

  /// Represents state when actively retrying to connect
  const factory ConnectivityState.retrying({
    @Default(true) bool isRetrying,
    @Default(ConnectivityStatus.disconnected) ConnectivityStatus status,
  }) = Retrying;

  // Helper factory
  factory ConnectivityState.fromStatus(ConnectivityStatus status) {
    switch (status) {
      case ConnectivityStatus.connected:
        return const ConnectivityState.connected();
      case ConnectivityStatus.disconnected:
        return const ConnectivityState.disconnected();
      case ConnectivityStatus.reconnected:
        return const ConnectivityState.reconnected();
      case ConnectivityStatus.connectedButProbablyNoNetwork:
        return const ConnectivityState.probablyNoNetwork();
      case ConnectivityStatus.none:
        return const ConnectivityState.disconnected();
    }
  }

  // Shared getters defined in private constructor
  bool get hasConnection => status.hasConnection;
  bool get isConnected => status.isConnected;
  bool get isDisconnected => status.isDisconnected;
  bool get isReconnected => status.isReconnected;
  bool get isProbablyNoNetwork => status.maybeConnected;
}
