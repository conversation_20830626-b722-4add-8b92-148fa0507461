part of 'app_bloc.dart';

@freezed
class AppState with _$AppState {
  const factory AppState({
    @Default(AppStateStatus.initial) AppStateStatus status,
    String? deviceCountryCode,
    String? deviceId,
    String? bundleId,
    String? bundleVersion,
    String? buildNumber,
    String? message,
    @Default([]) List<SettingsResponse> settings,
  }) = _AppState;

  factory AppState.fromJson(Map<String, dynamic> json) =>
      _$AppStateFromJson(json);

  factory AppState.initial() => const AppState();

  const AppState._();

  bool get isLoading => status == AppStateStatus.loading;

  bool get isSuccess => status == AppStateStatus.success;

  bool get isError => status == AppStateStatus.error;

  bool get hasMessage => message != null;

  AppState setLoading() {
    return copyWith(status: AppStateStatus.loading);
  }
}
