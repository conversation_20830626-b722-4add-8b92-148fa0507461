import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:froggytalk/app/data/services/push_notification_service.dart';

part 'push_notification_bloc.freezed.dart';
part 'push_notification_event.dart';
part 'push_notification_state.dart';

class PushNotificationBloc
    extends Bloc<PushNotificationEvent, PushNotificationState> {
  PushNotificationBloc({required this.service}) : super(const _Initial()) {
    on<_Started>(_onStarted);
  }

  final PushNotificationService service;

  FutureOr<void> _onStarted(
    _Started event,
    Emitter<PushNotificationState> emit,
  ) async {
    emit(const PushNotificationState.loading());
    try {
      // await service.initialize();
      emit(const PushNotificationState.initialized());
    } catch (e) {
      emit(PushNotificationState.failure(e.toString()));
    }
  }
}
