// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_bloc.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AppStateImpl _$$AppStateImplFromJson(Map<String, dynamic> json) =>
    _$AppStateImpl(
      status: $enumDecodeNullable(_$AppStateStatusEnumMap, json['status']) ??
          AppStateStatus.initial,
      deviceCountryCode: json['deviceCountryCode'] as String?,
      deviceId: json['deviceId'] as String?,
      bundleId: json['bundleId'] as String?,
      bundleVersion: json['bundleVersion'] as String?,
      buildNumber: json['buildNumber'] as String?,
      message: json['message'] as String?,
      settings: (json['settings'] as List<dynamic>?)
              ?.map((e) => SettingsResponse.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

const _$$AppStateImplFieldMap = <String, String>{
  'status': 'status',
  'deviceCountryCode': 'deviceCountryCode',
  'deviceId': 'deviceId',
  'bundleId': 'bundleId',
  'bundleVersion': 'bundleVersion',
  'buildNumber': 'buildNumber',
  'message': 'message',
  'settings': 'settings',
};

Map<String, dynamic> _$$AppStateImplToJson(_$AppStateImpl instance) =>
    <String, dynamic>{
      'status': _$AppStateStatusEnumMap[instance.status]!,
      if (instance.deviceCountryCode case final value?)
        'deviceCountryCode': value,
      if (instance.deviceId case final value?) 'deviceId': value,
      if (instance.bundleId case final value?) 'bundleId': value,
      if (instance.bundleVersion case final value?) 'bundleVersion': value,
      if (instance.buildNumber case final value?) 'buildNumber': value,
      if (instance.message case final value?) 'message': value,
      'settings': instance.settings.map((e) => e.toJson()).toList(),
    };

const _$AppStateStatusEnumMap = {
  AppStateStatus.initial: 'initial',
  AppStateStatus.loading: 'loading',
  AppStateStatus.success: 'success',
  AppStateStatus.error: 'error',
};
