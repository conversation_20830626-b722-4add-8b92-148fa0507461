import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:froggytalk/app/app.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:utils/utils.dart';

part 'connectivity_bloc.freezed.dart';
part 'connectivity_event.dart';
part 'connectivity_state.dart';

/// A Bloc that handles the connectivity events and states.
///
/// The `ConnectivityBloc` is responsible for managing the state of the
/// application's connectivity. It listens for `ConnectivityEvent`s and
/// updates the `ConnectivityState` accordingly.
///
/// Example usage:
/// ```dart
/// ConnectivityBloc connectivityBloc = ConnectivityBloc();
/// ```
///
/// See also:
/// - [ConnectivityEvent]
/// - [ConnectivityState]
class ConnectivityBloc extends Bloc<ConnectivityEvent, ConnectivityState> {
  ConnectivityBloc({required ConnectivityManager connectivityManager})
      : _connectivityManager = connectivityManager,
        super(const _Initial()) {
    on<ConnectivityEvent>(_onEvent);

    _connectivitySubscription =
        _connectivityManager.connectionStatusStream.listen(
      _updateConnectionStatus,
    );
  }

  final ConnectivityManager _connectivityManager;
  late final StreamSubscription<ConnectivityResult> _connectivitySubscription;
  ConnectivityStatus? _previousStatus;

  /// Closes the connectivity bloc and releases any allocated resources.
  ///
  /// This method is called when the bloc is no longer needed
  /// and should be disposed of.
  /// It ensures proper cleanup of any
  /// subscriptions, streams, or other resources
  /// managed by this bloc.
  ///
  /// Returns a [Future] that completes when all resources have been released.
  @override
  Future<void> close() {
    _connectivitySubscription.cancel();
    _connectivityManager.dispose();
    return super.close();
  }

  /// Updates the internet connectivity status
  /// Updates the connection status based on the provided [ConnectivityResult].
  ///
  /// Handles changes in network connectivity
  /// and updates the application's state
  /// accordingly to reflect the current connection status.
  Future<void> _updateConnectionStatus(ConnectivityResult result) async {
    var newStatus = ConnectivityStatus.none;

    if (result == ConnectivityResult.none) {
      // No internet connection
      newStatus = ConnectivityStatus.disconnected;
    } else {
      if (result == ConnectivityResult.wifi ||
          result == ConnectivityResult.mobile) {
        final canPingGoogle = await _connectivityManager.canPingGoogle();

        if (canPingGoogle) {
          newStatus = ConnectivityStatus.connected;
        } else {
          newStatus = ConnectivityStatus.connectedButProbablyNoNetwork;
        }
      }
    }

    // Only emit if status actually changed
    if (newStatus != _previousStatus) {
      _previousStatus = newStatus;
      add(ConnectivityEvent.statusUpdated(newStatus));
    }
  }

  FutureOr<void> _onEvent(
    ConnectivityEvent event,
    Emitter<ConnectivityState> emit,
  ) async {
    event.when(
      retryStatusUpdated: (isRetrying) {
        // emit(ConnectivityState.fromStatus())
      },
      statusUpdated: (status, isRetrying) {
        emit(
          ConnectivityState.fromStatus(status),
        );
      },
    );
  }
}
