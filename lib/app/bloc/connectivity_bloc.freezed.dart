// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'connectivity_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ConnectivityEvent {
  bool get isRetrying => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool isRetrying) retryStatusUpdated,
    required TResult Function(ConnectivityStatus status, bool isRetrying)
        statusUpdated,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool isRetrying)? retryStatusUpdated,
    TResult? Function(ConnectivityStatus status, bool isRetrying)?
        statusUpdated,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool isRetrying)? retryStatusUpdated,
    TResult Function(ConnectivityStatus status, bool isRetrying)? statusUpdated,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_RetryStatusUpdated value) retryStatusUpdated,
    required TResult Function(_StatusUpdated value) statusUpdated,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_RetryStatusUpdated value)? retryStatusUpdated,
    TResult? Function(_StatusUpdated value)? statusUpdated,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_RetryStatusUpdated value)? retryStatusUpdated,
    TResult Function(_StatusUpdated value)? statusUpdated,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of ConnectivityEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ConnectivityEventCopyWith<ConnectivityEvent> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ConnectivityEventCopyWith<$Res> {
  factory $ConnectivityEventCopyWith(
          ConnectivityEvent value, $Res Function(ConnectivityEvent) then) =
      _$ConnectivityEventCopyWithImpl<$Res, ConnectivityEvent>;
  @useResult
  $Res call({bool isRetrying});
}

/// @nodoc
class _$ConnectivityEventCopyWithImpl<$Res, $Val extends ConnectivityEvent>
    implements $ConnectivityEventCopyWith<$Res> {
  _$ConnectivityEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ConnectivityEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isRetrying = null,
  }) {
    return _then(_value.copyWith(
      isRetrying: null == isRetrying
          ? _value.isRetrying
          : isRetrying // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RetryStatusUpdatedImplCopyWith<$Res>
    implements $ConnectivityEventCopyWith<$Res> {
  factory _$$RetryStatusUpdatedImplCopyWith(_$RetryStatusUpdatedImpl value,
          $Res Function(_$RetryStatusUpdatedImpl) then) =
      __$$RetryStatusUpdatedImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isRetrying});
}

/// @nodoc
class __$$RetryStatusUpdatedImplCopyWithImpl<$Res>
    extends _$ConnectivityEventCopyWithImpl<$Res, _$RetryStatusUpdatedImpl>
    implements _$$RetryStatusUpdatedImplCopyWith<$Res> {
  __$$RetryStatusUpdatedImplCopyWithImpl(_$RetryStatusUpdatedImpl _value,
      $Res Function(_$RetryStatusUpdatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of ConnectivityEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isRetrying = null,
  }) {
    return _then(_$RetryStatusUpdatedImpl(
      isRetrying: null == isRetrying
          ? _value.isRetrying
          : isRetrying // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$RetryStatusUpdatedImpl implements _RetryStatusUpdated {
  const _$RetryStatusUpdatedImpl({this.isRetrying = false});

  @override
  @JsonKey()
  final bool isRetrying;

  @override
  String toString() {
    return 'ConnectivityEvent.retryStatusUpdated(isRetrying: $isRetrying)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RetryStatusUpdatedImpl &&
            (identical(other.isRetrying, isRetrying) ||
                other.isRetrying == isRetrying));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isRetrying);

  /// Create a copy of ConnectivityEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RetryStatusUpdatedImplCopyWith<_$RetryStatusUpdatedImpl> get copyWith =>
      __$$RetryStatusUpdatedImplCopyWithImpl<_$RetryStatusUpdatedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool isRetrying) retryStatusUpdated,
    required TResult Function(ConnectivityStatus status, bool isRetrying)
        statusUpdated,
  }) {
    return retryStatusUpdated(isRetrying);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool isRetrying)? retryStatusUpdated,
    TResult? Function(ConnectivityStatus status, bool isRetrying)?
        statusUpdated,
  }) {
    return retryStatusUpdated?.call(isRetrying);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool isRetrying)? retryStatusUpdated,
    TResult Function(ConnectivityStatus status, bool isRetrying)? statusUpdated,
    required TResult orElse(),
  }) {
    if (retryStatusUpdated != null) {
      return retryStatusUpdated(isRetrying);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_RetryStatusUpdated value) retryStatusUpdated,
    required TResult Function(_StatusUpdated value) statusUpdated,
  }) {
    return retryStatusUpdated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_RetryStatusUpdated value)? retryStatusUpdated,
    TResult? Function(_StatusUpdated value)? statusUpdated,
  }) {
    return retryStatusUpdated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_RetryStatusUpdated value)? retryStatusUpdated,
    TResult Function(_StatusUpdated value)? statusUpdated,
    required TResult orElse(),
  }) {
    if (retryStatusUpdated != null) {
      return retryStatusUpdated(this);
    }
    return orElse();
  }
}

abstract class _RetryStatusUpdated implements ConnectivityEvent {
  const factory _RetryStatusUpdated({final bool isRetrying}) =
      _$RetryStatusUpdatedImpl;

  @override
  bool get isRetrying;

  /// Create a copy of ConnectivityEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RetryStatusUpdatedImplCopyWith<_$RetryStatusUpdatedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$StatusUpdatedImplCopyWith<$Res>
    implements $ConnectivityEventCopyWith<$Res> {
  factory _$$StatusUpdatedImplCopyWith(
          _$StatusUpdatedImpl value, $Res Function(_$StatusUpdatedImpl) then) =
      __$$StatusUpdatedImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({ConnectivityStatus status, bool isRetrying});
}

/// @nodoc
class __$$StatusUpdatedImplCopyWithImpl<$Res>
    extends _$ConnectivityEventCopyWithImpl<$Res, _$StatusUpdatedImpl>
    implements _$$StatusUpdatedImplCopyWith<$Res> {
  __$$StatusUpdatedImplCopyWithImpl(
      _$StatusUpdatedImpl _value, $Res Function(_$StatusUpdatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of ConnectivityEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? isRetrying = null,
  }) {
    return _then(_$StatusUpdatedImpl(
      null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ConnectivityStatus,
      isRetrying: null == isRetrying
          ? _value.isRetrying
          : isRetrying // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$StatusUpdatedImpl implements _StatusUpdated {
  const _$StatusUpdatedImpl(this.status, {this.isRetrying = false});

  @override
  final ConnectivityStatus status;
  @override
  @JsonKey()
  final bool isRetrying;

  @override
  String toString() {
    return 'ConnectivityEvent.statusUpdated(status: $status, isRetrying: $isRetrying)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StatusUpdatedImpl &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.isRetrying, isRetrying) ||
                other.isRetrying == isRetrying));
  }

  @override
  int get hashCode => Object.hash(runtimeType, status, isRetrying);

  /// Create a copy of ConnectivityEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StatusUpdatedImplCopyWith<_$StatusUpdatedImpl> get copyWith =>
      __$$StatusUpdatedImplCopyWithImpl<_$StatusUpdatedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool isRetrying) retryStatusUpdated,
    required TResult Function(ConnectivityStatus status, bool isRetrying)
        statusUpdated,
  }) {
    return statusUpdated(status, isRetrying);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool isRetrying)? retryStatusUpdated,
    TResult? Function(ConnectivityStatus status, bool isRetrying)?
        statusUpdated,
  }) {
    return statusUpdated?.call(status, isRetrying);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool isRetrying)? retryStatusUpdated,
    TResult Function(ConnectivityStatus status, bool isRetrying)? statusUpdated,
    required TResult orElse(),
  }) {
    if (statusUpdated != null) {
      return statusUpdated(status, isRetrying);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_RetryStatusUpdated value) retryStatusUpdated,
    required TResult Function(_StatusUpdated value) statusUpdated,
  }) {
    return statusUpdated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_RetryStatusUpdated value)? retryStatusUpdated,
    TResult? Function(_StatusUpdated value)? statusUpdated,
  }) {
    return statusUpdated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_RetryStatusUpdated value)? retryStatusUpdated,
    TResult Function(_StatusUpdated value)? statusUpdated,
    required TResult orElse(),
  }) {
    if (statusUpdated != null) {
      return statusUpdated(this);
    }
    return orElse();
  }
}

abstract class _StatusUpdated implements ConnectivityEvent {
  const factory _StatusUpdated(final ConnectivityStatus status,
      {final bool isRetrying}) = _$StatusUpdatedImpl;

  ConnectivityStatus get status;
  @override
  bool get isRetrying;

  /// Create a copy of ConnectivityEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StatusUpdatedImplCopyWith<_$StatusUpdatedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$ConnectivityState {
  bool get isRetrying => throw _privateConstructorUsedError;
  ConnectivityStatus get status => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(bool isRetrying, ConnectivityStatus status) $default, {
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        connected,
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        disconnected,
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        reconnected,
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        probablyNoNetwork,
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        retrying,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(bool isRetrying, ConnectivityStatus status)? $default, {
    TResult? Function(bool isRetrying, ConnectivityStatus status)? connected,
    TResult? Function(bool isRetrying, ConnectivityStatus status)? disconnected,
    TResult? Function(bool isRetrying, ConnectivityStatus status)? reconnected,
    TResult? Function(bool isRetrying, ConnectivityStatus status)?
        probablyNoNetwork,
    TResult? Function(bool isRetrying, ConnectivityStatus status)? retrying,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(bool isRetrying, ConnectivityStatus status)? $default, {
    TResult Function(bool isRetrying, ConnectivityStatus status)? connected,
    TResult Function(bool isRetrying, ConnectivityStatus status)? disconnected,
    TResult Function(bool isRetrying, ConnectivityStatus status)? reconnected,
    TResult Function(bool isRetrying, ConnectivityStatus status)?
        probablyNoNetwork,
    TResult Function(bool isRetrying, ConnectivityStatus status)? retrying,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_Initial value) $default, {
    required TResult Function(Connected value) connected,
    required TResult Function(Disconnected value) disconnected,
    required TResult Function(Reconnected value) reconnected,
    required TResult Function(ProbablyNoNetwork value) probablyNoNetwork,
    required TResult Function(Retrying value) retrying,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_Initial value)? $default, {
    TResult? Function(Connected value)? connected,
    TResult? Function(Disconnected value)? disconnected,
    TResult? Function(Reconnected value)? reconnected,
    TResult? Function(ProbablyNoNetwork value)? probablyNoNetwork,
    TResult? Function(Retrying value)? retrying,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_Initial value)? $default, {
    TResult Function(Connected value)? connected,
    TResult Function(Disconnected value)? disconnected,
    TResult Function(Reconnected value)? reconnected,
    TResult Function(ProbablyNoNetwork value)? probablyNoNetwork,
    TResult Function(Retrying value)? retrying,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of ConnectivityState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ConnectivityStateCopyWith<ConnectivityState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ConnectivityStateCopyWith<$Res> {
  factory $ConnectivityStateCopyWith(
          ConnectivityState value, $Res Function(ConnectivityState) then) =
      _$ConnectivityStateCopyWithImpl<$Res, ConnectivityState>;
  @useResult
  $Res call({bool isRetrying, ConnectivityStatus status});
}

/// @nodoc
class _$ConnectivityStateCopyWithImpl<$Res, $Val extends ConnectivityState>
    implements $ConnectivityStateCopyWith<$Res> {
  _$ConnectivityStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ConnectivityState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isRetrying = null,
    Object? status = null,
  }) {
    return _then(_value.copyWith(
      isRetrying: null == isRetrying
          ? _value.isRetrying
          : isRetrying // ignore: cast_nullable_to_non_nullable
              as bool,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ConnectivityStatus,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res>
    implements $ConnectivityStateCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isRetrying, ConnectivityStatus status});
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$ConnectivityStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of ConnectivityState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isRetrying = null,
    Object? status = null,
  }) {
    return _then(_$InitialImpl(
      isRetrying: null == isRetrying
          ? _value.isRetrying
          : isRetrying // ignore: cast_nullable_to_non_nullable
              as bool,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ConnectivityStatus,
    ));
  }
}

/// @nodoc

class _$InitialImpl extends _Initial {
  const _$InitialImpl(
      {this.isRetrying = false, this.status = ConnectivityStatus.connected})
      : super._();

  @override
  @JsonKey()
  final bool isRetrying;
  @override
  @JsonKey()
  final ConnectivityStatus status;

  @override
  String toString() {
    return 'ConnectivityState(isRetrying: $isRetrying, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InitialImpl &&
            (identical(other.isRetrying, isRetrying) ||
                other.isRetrying == isRetrying) &&
            (identical(other.status, status) || other.status == status));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isRetrying, status);

  /// Create a copy of ConnectivityState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$InitialImplCopyWith<_$InitialImpl> get copyWith =>
      __$$InitialImplCopyWithImpl<_$InitialImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(bool isRetrying, ConnectivityStatus status) $default, {
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        connected,
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        disconnected,
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        reconnected,
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        probablyNoNetwork,
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        retrying,
  }) {
    return $default(isRetrying, status);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(bool isRetrying, ConnectivityStatus status)? $default, {
    TResult? Function(bool isRetrying, ConnectivityStatus status)? connected,
    TResult? Function(bool isRetrying, ConnectivityStatus status)? disconnected,
    TResult? Function(bool isRetrying, ConnectivityStatus status)? reconnected,
    TResult? Function(bool isRetrying, ConnectivityStatus status)?
        probablyNoNetwork,
    TResult? Function(bool isRetrying, ConnectivityStatus status)? retrying,
  }) {
    return $default?.call(isRetrying, status);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(bool isRetrying, ConnectivityStatus status)? $default, {
    TResult Function(bool isRetrying, ConnectivityStatus status)? connected,
    TResult Function(bool isRetrying, ConnectivityStatus status)? disconnected,
    TResult Function(bool isRetrying, ConnectivityStatus status)? reconnected,
    TResult Function(bool isRetrying, ConnectivityStatus status)?
        probablyNoNetwork,
    TResult Function(bool isRetrying, ConnectivityStatus status)? retrying,
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(isRetrying, status);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_Initial value) $default, {
    required TResult Function(Connected value) connected,
    required TResult Function(Disconnected value) disconnected,
    required TResult Function(Reconnected value) reconnected,
    required TResult Function(ProbablyNoNetwork value) probablyNoNetwork,
    required TResult Function(Retrying value) retrying,
  }) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_Initial value)? $default, {
    TResult? Function(Connected value)? connected,
    TResult? Function(Disconnected value)? disconnected,
    TResult? Function(Reconnected value)? reconnected,
    TResult? Function(ProbablyNoNetwork value)? probablyNoNetwork,
    TResult? Function(Retrying value)? retrying,
  }) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_Initial value)? $default, {
    TResult Function(Connected value)? connected,
    TResult Function(Disconnected value)? disconnected,
    TResult Function(Reconnected value)? reconnected,
    TResult Function(ProbablyNoNetwork value)? probablyNoNetwork,
    TResult Function(Retrying value)? retrying,
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }
}

abstract class _Initial extends ConnectivityState {
  const factory _Initial(
      {final bool isRetrying, final ConnectivityStatus status}) = _$InitialImpl;
  const _Initial._() : super._();

  @override
  bool get isRetrying;
  @override
  ConnectivityStatus get status;

  /// Create a copy of ConnectivityState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$InitialImplCopyWith<_$InitialImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ConnectedImplCopyWith<$Res>
    implements $ConnectivityStateCopyWith<$Res> {
  factory _$$ConnectedImplCopyWith(
          _$ConnectedImpl value, $Res Function(_$ConnectedImpl) then) =
      __$$ConnectedImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isRetrying, ConnectivityStatus status});
}

/// @nodoc
class __$$ConnectedImplCopyWithImpl<$Res>
    extends _$ConnectivityStateCopyWithImpl<$Res, _$ConnectedImpl>
    implements _$$ConnectedImplCopyWith<$Res> {
  __$$ConnectedImplCopyWithImpl(
      _$ConnectedImpl _value, $Res Function(_$ConnectedImpl) _then)
      : super(_value, _then);

  /// Create a copy of ConnectivityState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isRetrying = null,
    Object? status = null,
  }) {
    return _then(_$ConnectedImpl(
      isRetrying: null == isRetrying
          ? _value.isRetrying
          : isRetrying // ignore: cast_nullable_to_non_nullable
              as bool,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ConnectivityStatus,
    ));
  }
}

/// @nodoc

class _$ConnectedImpl extends Connected {
  const _$ConnectedImpl(
      {this.isRetrying = false, this.status = ConnectivityStatus.connected})
      : super._();

  @override
  @JsonKey()
  final bool isRetrying;
  @override
  @JsonKey()
  final ConnectivityStatus status;

  @override
  String toString() {
    return 'ConnectivityState.connected(isRetrying: $isRetrying, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConnectedImpl &&
            (identical(other.isRetrying, isRetrying) ||
                other.isRetrying == isRetrying) &&
            (identical(other.status, status) || other.status == status));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isRetrying, status);

  /// Create a copy of ConnectivityState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ConnectedImplCopyWith<_$ConnectedImpl> get copyWith =>
      __$$ConnectedImplCopyWithImpl<_$ConnectedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(bool isRetrying, ConnectivityStatus status) $default, {
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        connected,
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        disconnected,
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        reconnected,
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        probablyNoNetwork,
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        retrying,
  }) {
    return connected(isRetrying, status);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(bool isRetrying, ConnectivityStatus status)? $default, {
    TResult? Function(bool isRetrying, ConnectivityStatus status)? connected,
    TResult? Function(bool isRetrying, ConnectivityStatus status)? disconnected,
    TResult? Function(bool isRetrying, ConnectivityStatus status)? reconnected,
    TResult? Function(bool isRetrying, ConnectivityStatus status)?
        probablyNoNetwork,
    TResult? Function(bool isRetrying, ConnectivityStatus status)? retrying,
  }) {
    return connected?.call(isRetrying, status);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(bool isRetrying, ConnectivityStatus status)? $default, {
    TResult Function(bool isRetrying, ConnectivityStatus status)? connected,
    TResult Function(bool isRetrying, ConnectivityStatus status)? disconnected,
    TResult Function(bool isRetrying, ConnectivityStatus status)? reconnected,
    TResult Function(bool isRetrying, ConnectivityStatus status)?
        probablyNoNetwork,
    TResult Function(bool isRetrying, ConnectivityStatus status)? retrying,
    required TResult orElse(),
  }) {
    if (connected != null) {
      return connected(isRetrying, status);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_Initial value) $default, {
    required TResult Function(Connected value) connected,
    required TResult Function(Disconnected value) disconnected,
    required TResult Function(Reconnected value) reconnected,
    required TResult Function(ProbablyNoNetwork value) probablyNoNetwork,
    required TResult Function(Retrying value) retrying,
  }) {
    return connected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_Initial value)? $default, {
    TResult? Function(Connected value)? connected,
    TResult? Function(Disconnected value)? disconnected,
    TResult? Function(Reconnected value)? reconnected,
    TResult? Function(ProbablyNoNetwork value)? probablyNoNetwork,
    TResult? Function(Retrying value)? retrying,
  }) {
    return connected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_Initial value)? $default, {
    TResult Function(Connected value)? connected,
    TResult Function(Disconnected value)? disconnected,
    TResult Function(Reconnected value)? reconnected,
    TResult Function(ProbablyNoNetwork value)? probablyNoNetwork,
    TResult Function(Retrying value)? retrying,
    required TResult orElse(),
  }) {
    if (connected != null) {
      return connected(this);
    }
    return orElse();
  }
}

abstract class Connected extends ConnectivityState {
  const factory Connected(
      {final bool isRetrying,
      final ConnectivityStatus status}) = _$ConnectedImpl;
  const Connected._() : super._();

  @override
  bool get isRetrying;
  @override
  ConnectivityStatus get status;

  /// Create a copy of ConnectivityState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ConnectedImplCopyWith<_$ConnectedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DisconnectedImplCopyWith<$Res>
    implements $ConnectivityStateCopyWith<$Res> {
  factory _$$DisconnectedImplCopyWith(
          _$DisconnectedImpl value, $Res Function(_$DisconnectedImpl) then) =
      __$$DisconnectedImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isRetrying, ConnectivityStatus status});
}

/// @nodoc
class __$$DisconnectedImplCopyWithImpl<$Res>
    extends _$ConnectivityStateCopyWithImpl<$Res, _$DisconnectedImpl>
    implements _$$DisconnectedImplCopyWith<$Res> {
  __$$DisconnectedImplCopyWithImpl(
      _$DisconnectedImpl _value, $Res Function(_$DisconnectedImpl) _then)
      : super(_value, _then);

  /// Create a copy of ConnectivityState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isRetrying = null,
    Object? status = null,
  }) {
    return _then(_$DisconnectedImpl(
      isRetrying: null == isRetrying
          ? _value.isRetrying
          : isRetrying // ignore: cast_nullable_to_non_nullable
              as bool,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ConnectivityStatus,
    ));
  }
}

/// @nodoc

class _$DisconnectedImpl extends Disconnected {
  const _$DisconnectedImpl(
      {this.isRetrying = false, this.status = ConnectivityStatus.disconnected})
      : super._();

  @override
  @JsonKey()
  final bool isRetrying;
  @override
  @JsonKey()
  final ConnectivityStatus status;

  @override
  String toString() {
    return 'ConnectivityState.disconnected(isRetrying: $isRetrying, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DisconnectedImpl &&
            (identical(other.isRetrying, isRetrying) ||
                other.isRetrying == isRetrying) &&
            (identical(other.status, status) || other.status == status));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isRetrying, status);

  /// Create a copy of ConnectivityState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DisconnectedImplCopyWith<_$DisconnectedImpl> get copyWith =>
      __$$DisconnectedImplCopyWithImpl<_$DisconnectedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(bool isRetrying, ConnectivityStatus status) $default, {
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        connected,
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        disconnected,
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        reconnected,
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        probablyNoNetwork,
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        retrying,
  }) {
    return disconnected(isRetrying, status);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(bool isRetrying, ConnectivityStatus status)? $default, {
    TResult? Function(bool isRetrying, ConnectivityStatus status)? connected,
    TResult? Function(bool isRetrying, ConnectivityStatus status)? disconnected,
    TResult? Function(bool isRetrying, ConnectivityStatus status)? reconnected,
    TResult? Function(bool isRetrying, ConnectivityStatus status)?
        probablyNoNetwork,
    TResult? Function(bool isRetrying, ConnectivityStatus status)? retrying,
  }) {
    return disconnected?.call(isRetrying, status);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(bool isRetrying, ConnectivityStatus status)? $default, {
    TResult Function(bool isRetrying, ConnectivityStatus status)? connected,
    TResult Function(bool isRetrying, ConnectivityStatus status)? disconnected,
    TResult Function(bool isRetrying, ConnectivityStatus status)? reconnected,
    TResult Function(bool isRetrying, ConnectivityStatus status)?
        probablyNoNetwork,
    TResult Function(bool isRetrying, ConnectivityStatus status)? retrying,
    required TResult orElse(),
  }) {
    if (disconnected != null) {
      return disconnected(isRetrying, status);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_Initial value) $default, {
    required TResult Function(Connected value) connected,
    required TResult Function(Disconnected value) disconnected,
    required TResult Function(Reconnected value) reconnected,
    required TResult Function(ProbablyNoNetwork value) probablyNoNetwork,
    required TResult Function(Retrying value) retrying,
  }) {
    return disconnected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_Initial value)? $default, {
    TResult? Function(Connected value)? connected,
    TResult? Function(Disconnected value)? disconnected,
    TResult? Function(Reconnected value)? reconnected,
    TResult? Function(ProbablyNoNetwork value)? probablyNoNetwork,
    TResult? Function(Retrying value)? retrying,
  }) {
    return disconnected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_Initial value)? $default, {
    TResult Function(Connected value)? connected,
    TResult Function(Disconnected value)? disconnected,
    TResult Function(Reconnected value)? reconnected,
    TResult Function(ProbablyNoNetwork value)? probablyNoNetwork,
    TResult Function(Retrying value)? retrying,
    required TResult orElse(),
  }) {
    if (disconnected != null) {
      return disconnected(this);
    }
    return orElse();
  }
}

abstract class Disconnected extends ConnectivityState {
  const factory Disconnected(
      {final bool isRetrying,
      final ConnectivityStatus status}) = _$DisconnectedImpl;
  const Disconnected._() : super._();

  @override
  bool get isRetrying;
  @override
  ConnectivityStatus get status;

  /// Create a copy of ConnectivityState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DisconnectedImplCopyWith<_$DisconnectedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ReconnectedImplCopyWith<$Res>
    implements $ConnectivityStateCopyWith<$Res> {
  factory _$$ReconnectedImplCopyWith(
          _$ReconnectedImpl value, $Res Function(_$ReconnectedImpl) then) =
      __$$ReconnectedImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isRetrying, ConnectivityStatus status});
}

/// @nodoc
class __$$ReconnectedImplCopyWithImpl<$Res>
    extends _$ConnectivityStateCopyWithImpl<$Res, _$ReconnectedImpl>
    implements _$$ReconnectedImplCopyWith<$Res> {
  __$$ReconnectedImplCopyWithImpl(
      _$ReconnectedImpl _value, $Res Function(_$ReconnectedImpl) _then)
      : super(_value, _then);

  /// Create a copy of ConnectivityState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isRetrying = null,
    Object? status = null,
  }) {
    return _then(_$ReconnectedImpl(
      isRetrying: null == isRetrying
          ? _value.isRetrying
          : isRetrying // ignore: cast_nullable_to_non_nullable
              as bool,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ConnectivityStatus,
    ));
  }
}

/// @nodoc

class _$ReconnectedImpl extends Reconnected {
  const _$ReconnectedImpl(
      {this.isRetrying = false, this.status = ConnectivityStatus.reconnected})
      : super._();

  @override
  @JsonKey()
  final bool isRetrying;
  @override
  @JsonKey()
  final ConnectivityStatus status;

  @override
  String toString() {
    return 'ConnectivityState.reconnected(isRetrying: $isRetrying, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReconnectedImpl &&
            (identical(other.isRetrying, isRetrying) ||
                other.isRetrying == isRetrying) &&
            (identical(other.status, status) || other.status == status));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isRetrying, status);

  /// Create a copy of ConnectivityState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ReconnectedImplCopyWith<_$ReconnectedImpl> get copyWith =>
      __$$ReconnectedImplCopyWithImpl<_$ReconnectedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(bool isRetrying, ConnectivityStatus status) $default, {
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        connected,
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        disconnected,
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        reconnected,
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        probablyNoNetwork,
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        retrying,
  }) {
    return reconnected(isRetrying, status);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(bool isRetrying, ConnectivityStatus status)? $default, {
    TResult? Function(bool isRetrying, ConnectivityStatus status)? connected,
    TResult? Function(bool isRetrying, ConnectivityStatus status)? disconnected,
    TResult? Function(bool isRetrying, ConnectivityStatus status)? reconnected,
    TResult? Function(bool isRetrying, ConnectivityStatus status)?
        probablyNoNetwork,
    TResult? Function(bool isRetrying, ConnectivityStatus status)? retrying,
  }) {
    return reconnected?.call(isRetrying, status);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(bool isRetrying, ConnectivityStatus status)? $default, {
    TResult Function(bool isRetrying, ConnectivityStatus status)? connected,
    TResult Function(bool isRetrying, ConnectivityStatus status)? disconnected,
    TResult Function(bool isRetrying, ConnectivityStatus status)? reconnected,
    TResult Function(bool isRetrying, ConnectivityStatus status)?
        probablyNoNetwork,
    TResult Function(bool isRetrying, ConnectivityStatus status)? retrying,
    required TResult orElse(),
  }) {
    if (reconnected != null) {
      return reconnected(isRetrying, status);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_Initial value) $default, {
    required TResult Function(Connected value) connected,
    required TResult Function(Disconnected value) disconnected,
    required TResult Function(Reconnected value) reconnected,
    required TResult Function(ProbablyNoNetwork value) probablyNoNetwork,
    required TResult Function(Retrying value) retrying,
  }) {
    return reconnected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_Initial value)? $default, {
    TResult? Function(Connected value)? connected,
    TResult? Function(Disconnected value)? disconnected,
    TResult? Function(Reconnected value)? reconnected,
    TResult? Function(ProbablyNoNetwork value)? probablyNoNetwork,
    TResult? Function(Retrying value)? retrying,
  }) {
    return reconnected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_Initial value)? $default, {
    TResult Function(Connected value)? connected,
    TResult Function(Disconnected value)? disconnected,
    TResult Function(Reconnected value)? reconnected,
    TResult Function(ProbablyNoNetwork value)? probablyNoNetwork,
    TResult Function(Retrying value)? retrying,
    required TResult orElse(),
  }) {
    if (reconnected != null) {
      return reconnected(this);
    }
    return orElse();
  }
}

abstract class Reconnected extends ConnectivityState {
  const factory Reconnected(
      {final bool isRetrying,
      final ConnectivityStatus status}) = _$ReconnectedImpl;
  const Reconnected._() : super._();

  @override
  bool get isRetrying;
  @override
  ConnectivityStatus get status;

  /// Create a copy of ConnectivityState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ReconnectedImplCopyWith<_$ReconnectedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ProbablyNoNetworkImplCopyWith<$Res>
    implements $ConnectivityStateCopyWith<$Res> {
  factory _$$ProbablyNoNetworkImplCopyWith(_$ProbablyNoNetworkImpl value,
          $Res Function(_$ProbablyNoNetworkImpl) then) =
      __$$ProbablyNoNetworkImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isRetrying, ConnectivityStatus status});
}

/// @nodoc
class __$$ProbablyNoNetworkImplCopyWithImpl<$Res>
    extends _$ConnectivityStateCopyWithImpl<$Res, _$ProbablyNoNetworkImpl>
    implements _$$ProbablyNoNetworkImplCopyWith<$Res> {
  __$$ProbablyNoNetworkImplCopyWithImpl(_$ProbablyNoNetworkImpl _value,
      $Res Function(_$ProbablyNoNetworkImpl) _then)
      : super(_value, _then);

  /// Create a copy of ConnectivityState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isRetrying = null,
    Object? status = null,
  }) {
    return _then(_$ProbablyNoNetworkImpl(
      isRetrying: null == isRetrying
          ? _value.isRetrying
          : isRetrying // ignore: cast_nullable_to_non_nullable
              as bool,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ConnectivityStatus,
    ));
  }
}

/// @nodoc

class _$ProbablyNoNetworkImpl extends ProbablyNoNetwork {
  const _$ProbablyNoNetworkImpl(
      {this.isRetrying = false,
      this.status = ConnectivityStatus.connectedButProbablyNoNetwork})
      : super._();

  @override
  @JsonKey()
  final bool isRetrying;
  @override
  @JsonKey()
  final ConnectivityStatus status;

  @override
  String toString() {
    return 'ConnectivityState.probablyNoNetwork(isRetrying: $isRetrying, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProbablyNoNetworkImpl &&
            (identical(other.isRetrying, isRetrying) ||
                other.isRetrying == isRetrying) &&
            (identical(other.status, status) || other.status == status));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isRetrying, status);

  /// Create a copy of ConnectivityState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProbablyNoNetworkImplCopyWith<_$ProbablyNoNetworkImpl> get copyWith =>
      __$$ProbablyNoNetworkImplCopyWithImpl<_$ProbablyNoNetworkImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(bool isRetrying, ConnectivityStatus status) $default, {
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        connected,
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        disconnected,
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        reconnected,
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        probablyNoNetwork,
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        retrying,
  }) {
    return probablyNoNetwork(isRetrying, status);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(bool isRetrying, ConnectivityStatus status)? $default, {
    TResult? Function(bool isRetrying, ConnectivityStatus status)? connected,
    TResult? Function(bool isRetrying, ConnectivityStatus status)? disconnected,
    TResult? Function(bool isRetrying, ConnectivityStatus status)? reconnected,
    TResult? Function(bool isRetrying, ConnectivityStatus status)?
        probablyNoNetwork,
    TResult? Function(bool isRetrying, ConnectivityStatus status)? retrying,
  }) {
    return probablyNoNetwork?.call(isRetrying, status);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(bool isRetrying, ConnectivityStatus status)? $default, {
    TResult Function(bool isRetrying, ConnectivityStatus status)? connected,
    TResult Function(bool isRetrying, ConnectivityStatus status)? disconnected,
    TResult Function(bool isRetrying, ConnectivityStatus status)? reconnected,
    TResult Function(bool isRetrying, ConnectivityStatus status)?
        probablyNoNetwork,
    TResult Function(bool isRetrying, ConnectivityStatus status)? retrying,
    required TResult orElse(),
  }) {
    if (probablyNoNetwork != null) {
      return probablyNoNetwork(isRetrying, status);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_Initial value) $default, {
    required TResult Function(Connected value) connected,
    required TResult Function(Disconnected value) disconnected,
    required TResult Function(Reconnected value) reconnected,
    required TResult Function(ProbablyNoNetwork value) probablyNoNetwork,
    required TResult Function(Retrying value) retrying,
  }) {
    return probablyNoNetwork(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_Initial value)? $default, {
    TResult? Function(Connected value)? connected,
    TResult? Function(Disconnected value)? disconnected,
    TResult? Function(Reconnected value)? reconnected,
    TResult? Function(ProbablyNoNetwork value)? probablyNoNetwork,
    TResult? Function(Retrying value)? retrying,
  }) {
    return probablyNoNetwork?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_Initial value)? $default, {
    TResult Function(Connected value)? connected,
    TResult Function(Disconnected value)? disconnected,
    TResult Function(Reconnected value)? reconnected,
    TResult Function(ProbablyNoNetwork value)? probablyNoNetwork,
    TResult Function(Retrying value)? retrying,
    required TResult orElse(),
  }) {
    if (probablyNoNetwork != null) {
      return probablyNoNetwork(this);
    }
    return orElse();
  }
}

abstract class ProbablyNoNetwork extends ConnectivityState {
  const factory ProbablyNoNetwork(
      {final bool isRetrying,
      final ConnectivityStatus status}) = _$ProbablyNoNetworkImpl;
  const ProbablyNoNetwork._() : super._();

  @override
  bool get isRetrying;
  @override
  ConnectivityStatus get status;

  /// Create a copy of ConnectivityState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProbablyNoNetworkImplCopyWith<_$ProbablyNoNetworkImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RetryingImplCopyWith<$Res>
    implements $ConnectivityStateCopyWith<$Res> {
  factory _$$RetryingImplCopyWith(
          _$RetryingImpl value, $Res Function(_$RetryingImpl) then) =
      __$$RetryingImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isRetrying, ConnectivityStatus status});
}

/// @nodoc
class __$$RetryingImplCopyWithImpl<$Res>
    extends _$ConnectivityStateCopyWithImpl<$Res, _$RetryingImpl>
    implements _$$RetryingImplCopyWith<$Res> {
  __$$RetryingImplCopyWithImpl(
      _$RetryingImpl _value, $Res Function(_$RetryingImpl) _then)
      : super(_value, _then);

  /// Create a copy of ConnectivityState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isRetrying = null,
    Object? status = null,
  }) {
    return _then(_$RetryingImpl(
      isRetrying: null == isRetrying
          ? _value.isRetrying
          : isRetrying // ignore: cast_nullable_to_non_nullable
              as bool,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as ConnectivityStatus,
    ));
  }
}

/// @nodoc

class _$RetryingImpl extends Retrying {
  const _$RetryingImpl(
      {this.isRetrying = true, this.status = ConnectivityStatus.disconnected})
      : super._();

  @override
  @JsonKey()
  final bool isRetrying;
  @override
  @JsonKey()
  final ConnectivityStatus status;

  @override
  String toString() {
    return 'ConnectivityState.retrying(isRetrying: $isRetrying, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RetryingImpl &&
            (identical(other.isRetrying, isRetrying) ||
                other.isRetrying == isRetrying) &&
            (identical(other.status, status) || other.status == status));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isRetrying, status);

  /// Create a copy of ConnectivityState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RetryingImplCopyWith<_$RetryingImpl> get copyWith =>
      __$$RetryingImplCopyWithImpl<_$RetryingImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(bool isRetrying, ConnectivityStatus status) $default, {
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        connected,
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        disconnected,
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        reconnected,
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        probablyNoNetwork,
    required TResult Function(bool isRetrying, ConnectivityStatus status)
        retrying,
  }) {
    return retrying(isRetrying, status);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(bool isRetrying, ConnectivityStatus status)? $default, {
    TResult? Function(bool isRetrying, ConnectivityStatus status)? connected,
    TResult? Function(bool isRetrying, ConnectivityStatus status)? disconnected,
    TResult? Function(bool isRetrying, ConnectivityStatus status)? reconnected,
    TResult? Function(bool isRetrying, ConnectivityStatus status)?
        probablyNoNetwork,
    TResult? Function(bool isRetrying, ConnectivityStatus status)? retrying,
  }) {
    return retrying?.call(isRetrying, status);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(bool isRetrying, ConnectivityStatus status)? $default, {
    TResult Function(bool isRetrying, ConnectivityStatus status)? connected,
    TResult Function(bool isRetrying, ConnectivityStatus status)? disconnected,
    TResult Function(bool isRetrying, ConnectivityStatus status)? reconnected,
    TResult Function(bool isRetrying, ConnectivityStatus status)?
        probablyNoNetwork,
    TResult Function(bool isRetrying, ConnectivityStatus status)? retrying,
    required TResult orElse(),
  }) {
    if (retrying != null) {
      return retrying(isRetrying, status);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_Initial value) $default, {
    required TResult Function(Connected value) connected,
    required TResult Function(Disconnected value) disconnected,
    required TResult Function(Reconnected value) reconnected,
    required TResult Function(ProbablyNoNetwork value) probablyNoNetwork,
    required TResult Function(Retrying value) retrying,
  }) {
    return retrying(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_Initial value)? $default, {
    TResult? Function(Connected value)? connected,
    TResult? Function(Disconnected value)? disconnected,
    TResult? Function(Reconnected value)? reconnected,
    TResult? Function(ProbablyNoNetwork value)? probablyNoNetwork,
    TResult? Function(Retrying value)? retrying,
  }) {
    return retrying?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_Initial value)? $default, {
    TResult Function(Connected value)? connected,
    TResult Function(Disconnected value)? disconnected,
    TResult Function(Reconnected value)? reconnected,
    TResult Function(ProbablyNoNetwork value)? probablyNoNetwork,
    TResult Function(Retrying value)? retrying,
    required TResult orElse(),
  }) {
    if (retrying != null) {
      return retrying(this);
    }
    return orElse();
  }
}

abstract class Retrying extends ConnectivityState {
  const factory Retrying(
      {final bool isRetrying,
      final ConnectivityStatus status}) = _$RetryingImpl;
  const Retrying._() : super._();

  @override
  bool get isRetrying;
  @override
  ConnectivityStatus get status;

  /// Create a copy of ConnectivityState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RetryingImplCopyWith<_$RetryingImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
