import 'dart:async';
import 'dart:io';
import 'dart:ui';

import 'package:customer_io/customer_io.dart';
import 'package:customer_io/customer_io_enums.dart';
import 'package:dynamic_app_icons/dynamic_app_icons.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/app/data/services/event_tracker.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:froggytalk/dialer/dialer.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:froggytalk/notifications/notifications.dart';
import 'package:froggytalk/profile/profile.dart';
// import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:permission_handler/permission_handler.dart';
// import 'package:singular_flutter_sdk/singular.dart';
// import 'package:singular_flutter_sdk/singular_config.dart';
import 'package:sip_ua/sip_ua.dart' as sip_ua;
import 'package:utils/utils.dart';

class AppDashboardPage extends StatefulHookWidget {
  const AppDashboardPage({
    this.childAppBar,
    this.backgroundColor,
    this.child,
    this.onNavbarChanged,
    this.initialNavbarDestination = 0,
    this.isNavbarEnabled = true,
    super.key,
  });

  static String routeName = '/app_dashboard';

  final void Function(Map<String, dynamic> currentTab)? onNavbarChanged;
  final Color? backgroundColor;
  final Widget? child;
  final PreferredSizeWidget? childAppBar;
  final int initialNavbarDestination;
  final bool isNavbarEnabled;

  @override
  State<AppDashboardPage> createState() => _AppDashboardPageState();

  static Route<Object?> route() {
    return MaterialPageRoute<Object?>(
      builder: (_) => const AppDashboardPage(),
    );
  }
}

class _AppDashboardPageState extends State<AppDashboardPage>
    implements sip_ua.SipUaHelperListener, WidgetsBindingObserver {
  // AppLocalizations? l10n;
  DialerBloc? dialerBloc;

  // Mixpanel mixpanel;

  // ignore: unused_field
  bool isDialable = true;

  KeypadBloc? keypadBloc;

  EventTrackerService? _eventTrackerService;
  sip_ua.SIPUAHelper? _helper;
  // void navigateToCallingPage() {
  //   Navigator.of(context).push(CallingUserPage.route());
  // }

  final _upgrader = FroggyUpgrader(
    debugLogging: true,
  );

  @override
  void callStateChanged(sip_ua.Call call, sip_ua.CallState state) {
    // Navigation is now handled centrally by CallService
    // Just log the state change for debugging
    FroggyLogger.debug(
      'AppDashboard: Call state changed to ${state.state} for call ${call.id}',
    );
  }

  @override
  void didChangeAccessibilityFeatures() {}

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.resumed:
      // if (!_helper!.registered) {
      //   _helper!.register();
      // }
      case AppLifecycleState.paused:
      // if (!_helper!.registered) {
      //   _helper!.register();
      // }
      case AppLifecycleState.inactive:
      // if (!_helper!.registered) {
      //   _helper!.register();
      // }
      case AppLifecycleState.detached:
      // if (!_helper!.registered) {
      //   _helper!.register();
      // }
      case AppLifecycleState.hidden:
      // if (!_helper!.registered) {
      //   _helper!.register();
      // }
    }
  }

  @override
  void didChangeLocales(List<Locale>? locales) {}

  @override
  void didChangeMetrics() {}

  @override
  void didChangePlatformBrightness() {}

  @override
  void didChangeTextScaleFactor() {}

  @override
  void didChangeViewFocus(ViewFocusEvent event) {}

  @override
  void didHaveMemoryPressure() {}

  @override
  Future<bool> didPopRoute() async {
    // Handle the pop route action
    return false; // Return true if the pop route was handled
  }

  @override
  Future<bool> didPushRoute(String route) async {
    // Handle the push route action
    return false; // Return true if the push route was handled
  }

  @override
  Future<bool> didPushRouteInformation(
    RouteInformation routeInformation,
  ) async {
    // Handle the push route information action
    return false; // Return true if the push route information was handled
  }

  @override
  Future<AppExitResponse> didRequestAppExit() async {
    // Handle the app exit request
    return AppExitResponse.cancel; // Return the appropriate response
  }

  @override
  void dispose() {
    super.dispose();
    // OneSignal.Notifications.removeClickListener(onNotificationListener);
    _helper?.removeSipUaHelperListener(this);
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  void handleCancelBackGesture() {
    // Handle the cancellation of the back gesture
    debugPrint('Back gesture cancelled');
  }

  @override
  void handleCommitBackGesture() {
    // Handle the commitment of the back gesture
    debugPrint('Back gesture committed');
  }

  @override
  bool handleStartBackGesture(PredictiveBackEvent backEvent) {
    // Handle the start of the back gesture
    debugPrint('Back gesture started: $backEvent');
    return true; // Return true if the gesture is handled
  }

  @override
  void handleUpdateBackGestureProgress(PredictiveBackEvent backEvent) {
    // Handle the update of the back gesture progress
    debugPrint('Back gesture progress updated: $backEvent');
  }

  @override
  void initState() {
    super.initState();
    dialerBloc = context.read<DialerBloc>();
    keypadBloc = context.read<KeypadBloc>();
    _helper = context.read<sip_ua.SIPUAHelper>();
    _helper?.addSipUaHelperListener(this);
    _eventTrackerService = context.read<EventTrackerService>();
    // FroggyCountries.getInstance().reload();

    // initializeOneSignal();

    // Future.microtask(initializeOneSignal);
    // Future.microtask(initBackgroundDynamicIcon);
    Future.microtask(initializeTrackingAnalytics);
    Future.microtask(checkPendingNotificationClick);
    // Future.microtask(initializePushNotifications);
  }

  @override
  void onNewMessage(sip_ua.SIPMessageRequest msg) {
    //Save the incoming message to DB
    dialerBloc?.add(
      // ignore: avoid_dynamic_calls
      DialerEvent.sendErrorMessage(message: msg.request.body as String?),
    );
  }

  @override
  void onNewNotify(sip_ua.Notify ntf) {}

  @override
  void onNewReinvite(sip_ua.ReInvite event) {}

  @override
  void registrationStateChanged(sip_ua.RegistrationState state) {
    final registrationState = state.state;
    if (registrationState != null) {
      dialerBloc?.add(
        DialerEvent.updatedRegistrationStatus(status: registrationState),
      );

      // if (registrationState == RegistrationStateEnum.REGISTRATION_FAILED ||
      //     registrationState == RegistrationStateEnum.UNREGISTERED) {
      //   // if (authUser != null) {
      //   // dialerBloc?.add(
      //   //   const DialerEvent.reloadAsterisk(),
      //   // );
      //   // }
      //   // dialerBloc?.add(
      //   //   DialerEvent.updatedRegistrationStatus(status: registrationState),
      //   // );
      // }
    }
  }

  @override
  void transportStateChanged(sip_ua.TransportState state) {}

  Future<void> initializePushNotifications() async {
    final pushNotificationService = context.read<PushNotificationService>();
    await pushNotificationService.identify(
      externalId: context.select<AuthenticationBloc, String>(
        (bloc) => bloc.state.user?.oneSignalPassKey ?? '',
      ),
      currentUser: context.select<AuthenticationBloc, UserAttributesModel?>(
        (bloc) => bloc.state.user,
      ),
    );
  }

  Future<void> initializeTrackingAnalytics() async {
    // Capture auth bloc before the async gap
    final authBloc = context.read<AuthenticationBloc>();
    final userPassKey = authBloc.state.user?.oneSignalPassKey ?? '';

    await _eventTrackerService?.logEvent(
      schema: 'App on Dashboard',
      description: 'User is on the dashboard',
    );

    // Check if widget is still mounted after the async gap
    if (!mounted) return;

    unawaited(
      _eventTrackerService?.identify(
        userId: userPassKey,
        passKey: userPassKey,
      ),
    );
    unawaited(Permission.appTrackingTransparency.request());
  }

  // Future<void> initializeOneSignal() async {
  //   // try {
  //   //   final oneSignalAppId =
  //   //       AppInstance.getInstance().environmentType.isProduction
  //   //           ? '21044cb2-8a7a-4506-97f9-76a0cbbfaab9'
  //   //           : 'd20eafc4-56ce-457e-976f-421c45e4a1c3';

  //   //   OneSignal.initialize(oneSignalAppId);
  //   //   // await OneSignal.InAppMessages.paused(false);
  //   //   // Additional OneSignal configuration if needed

  //   //   // final canShowNotifcations = OneSignal.Notifications.permission;
  //   //   // if (!canShowNotifcations) {
  //   //   await OneSignal.Notifications.requestPermission(true);
  //   //   // }

  //   //   final optedIn2 = OneSignalPushSubscription().optedIn;
  //   //   if (optedIn2 == null && optedIn2 == false) {
  //   //     await OneSignalPushSubscription().optIn();

  //   //     // await OneSignal.User.addTagWithKey('phone_number', 'value');
  //   //   }
  //   //   await OneSignal.User.addTagWithKey(
  //   //     'device_language',
  //   //     getDeviceLanguageCode(),
  //   //   );
  //   // } catch (e) {
  //   //   FroggyLogger.error('OneSignal Initialization Error: $e');
  //   // }

  //   // OneSignal.Notifications.addClickListener(onNotificationListener);
  // }

  // void onNotificationListener(OSNotificationClickEvent result) {
  //   final id = result.notification.notificationId;
  //   final title = result.notification.title;
  //   final body = result.notification.body;
  //   final image = result.notification.bigPicture;

  //   final notification = NotificationResponse(
  //     id: id,
  //     title: title,
  //     body: body,
  //     createdAt: DateTime.now().toString(),
  //     bigPicture: image,
  //   );

  //   FroggyRouter.pushAndRemoveUntil(const AppDashboardPage());
  //   // Navigator.pushNamed(context, AppDashboardPage.routeName);
  //   Navigator.push(
  //     // Navigator.push(
  //     FroggyRouter.navigatorKey.currentState!.context,
  //     MaterialPageRoute<void>(
  //       builder: (context) => Scaffold(
  //         appBar: AppBar(
  //           title: Text(context.l10n.notificationsAppBarTitle),
  //           leading: BackButton(
  //             onPressed: () {
  //               FroggyRouter.pushAndRemoveUntil(const AppDashboardPage());
  //               // Navigator.of(context).pop();
  //             },
  //           ),
  //         ),
  //         body: BuildNofitifcationContentWidget(
  //           notification: notification,
  //           data: result.notification.additionalData,
  //         ),
  //       ),
  //     ),
  //     // Keep route in stack if it's the AppDashboard
  //     // (_) => false,
  //     // (route) => route.settings.name == AppDashboardPage.routeName,
  //   );
  // }

  // Initialize background service
  Future<void> initBackgroundDynamicIcon() async {
    try {
      if (Platform.isAndroid) {
        await FroggyDynamicIcons.ensureInitializedWithBackgroundService();
      }
    } catch (e) {
      FroggyLogger.error('Dynamic Icons Initialization Error: $e');
    }
  }

  void routeBack() {
    Navigator.of(context).maybePop();
  }

  Future<void> checkPendingNotificationClick() async {
    try {
      if (FroggyLocalStorage.isInitialized()) {
        final pendingNotification = FroggyLocalStorage.getInstance()
            .get<Map<String, dynamic>>(
          'pending_notification_click',
        );
        if (pendingNotification != null) {
          // Clear the stored notification
          FroggyLocalStorage.getInstance().remove(
            'pending_notification_click',
          );

          // Process the notification click
          final notification = NotificationResponse(
            id: pendingNotification['messageId'] as String? ?? '',
            title: pendingNotification['title'] as String? ?? '',
            body: pendingNotification['body'] as String? ?? '',
            createdAt: pendingNotification['timestamp'] as String? ??
                DateTime.now().toString(),
            bigPicture: pendingNotification['imageUrl'] as String?,
          );

          // Navigate to notification details page
          if (mounted) {
            unawaited(
              Navigator.push(
                context,
                MaterialPageRoute<void>(
                  builder: (
                    context,
                  ) => Scaffold(
                    appBar: AppBar(
                      title: Text(
                        context.l10n.notificationsAppBarTitle,
                      ),
                      leading: BackButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                      ),
                    ),
                    body: NotificationDetailsPage(notification),
                  ),
                ),
              ),
            );
          }

          // Track the notification opened event
          final data = pendingNotification['data'] as Map<String, dynamic>? ??
              {};
          final deliveryId = data['delivery_id'] as String?;
          if (deliveryId != null) {
            try {
              final deviceToken = await FirebaseMessaging.instance.getToken();
              if (deviceToken != null) {
                CustomerIO.instance.trackMetric(
                  deliveryID: deliveryId,
                  deviceToken: deviceToken,
                  event: MetricEvent.opened,
                );
              }
            } catch (e) {
              FroggyLogger.error(
                '[Dashboard] Error tracking notification metrics: $e',
              );
            }
          }
        }
      }
    } catch (e) {
      FroggyLogger.error(
        '[Dashboard] Error checking pending notification click: $e',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final authenticationBloc = context.read<AuthenticationBloc>();
    final activeItem = useState(
      widget.initialNavbarDestination,
    );

    final currentPageWidget = useMemoized(
      () => AppConfigurationRepository.getNavigationDestinations,
      [activeItem],
    );

    final currentPageAppBar = useMemoized(
      () => AppConfigurationRepository.getNavigationDestinationsAppBars,
      [activeItem],
    );

    return FroggyUpgradeAlert(
      upgrader: _upgrader,
      child: MultiBlocListener(
        listeners: [
          BlocListener<ConnectivityBloc, ConnectivityState>(
            listener: (
              context,
              state,
            ) {
              if (state.status.hasNoInternetConnection) {
                FroggyToast.showErrorToast(
                  context,
                  l10n.internetConnectionAlertTextError,
                );
              } else if (state.status.isReconnected) {
                authenticationBloc.add(
                  const AuthenticationEvent.refreshProfile(),
                );
              } else if (state.status.isConnected) {
                // FroggyToast.showSuccessToast(
                //   context,
                //   l10n.internetConnectionAlertTextSuccess,
                // );
              }
            },
          ),
          BlocListener<DialerBloc, DialerState>(
            listener: (
              context,
              state,
            ) {
              // if (state.status == DialerStatus.initiating) {
              //   navigateToCallingPage();
              // }
            },
          ),
        ],
        child: Scaffold(
          appBar: widget.child != null
              ? widget.childAppBar
              : currentPageAppBar[activeItem.value],
          body: widget.child ?? currentPageWidget[activeItem.value],
          backgroundColor: widget.backgroundColor,
          bottomNavigationBar: AppDashboardNavbar(
            initialIndex: widget.initialNavbarDestination,
            isEnabled: widget.isNavbarEnabled,
            onChanged: !(widget.child != null)
                ? (
                    activeIndex,
                  ) => activeItem.value = activeIndex
                : null,
          ),
        ),
      ),
    );
  }
}
