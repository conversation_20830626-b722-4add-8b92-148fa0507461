import 'package:constants/constants.dart';
import 'package:countries/countries.dart';
import 'package:customer_io/customer_io.dart';
import 'package:facebook_app_events/facebook_app_events.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/app/data/services/event_tracker.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:froggytalk/buy_credit/buy_credit.dart';
import 'package:froggytalk/call_rates/call_rates.dart';
import 'package:froggytalk/contact_list/contact_list.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:froggytalk/dialer/dialer.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:froggytalk/language/language.dart';
import 'package:froggytalk/notifications/notifications.dart';
import 'package:froggytalk/onboarding/onboarding.dart';
import 'package:froggytalk/profile/profile.dart';
import 'package:froggytalk/radio_stations/radio_stations.dart';
import 'package:froggytalk/referral/referral.dart';
import 'package:froggytalk/shared/bloc/local_notification_bloc.dart';
import 'package:froggytalk/shared/services/local_notification_service.dart';
import 'package:froggytalk/voucher/voucher.dart';
import 'package:just_audio/just_audio.dart';
import 'package:mixpanel_flutter/mixpanel_flutter.dart';
import 'package:navigation/navigation.dart';
import 'package:overlay_support/overlay_support.dart';
import 'package:sip_ua/sip_ua.dart';
import 'package:theme/theme.dart';
import 'package:utils/utils.dart';

class FroggyApp extends StatelessWidget {
  const FroggyApp({required AudioPlayer audioPlayer, super.key})
      : _audioPlayer = audioPlayer;

  final AudioPlayer _audioPlayer;

  @override
  Widget build(BuildContext context) {
    return MultiRepositoryProvider(
      providers: [
        RepositoryProvider(
          create: (context) => RadioPlayerService(
            audioPlayer: _audioPlayer,
          ),
        ),
        RepositoryProvider(
          create: (context) => FacebookAppEvents(),
        ),
        RepositoryProvider(
          create: (context) => FroggyLocalStorage.getInstance(),
        ),
        RepositoryProvider(
          create: (context) => FroggyCountries.getInstance(),
        ),
        RepositoryProvider(
          create: (context) => SIPUAHelper(
            customLogger: FroggyLogger(),
          ),
        ),
        RepositoryProvider(
          create: (context) => ConnectivityManager()..onInit(),
        ),
        RepositoryProvider(
          create: (context) => AuthProfileRepository(),
        ),
        RepositoryProvider(
          create: (context) => CallService(
            helper: context.read<SIPUAHelper>(),
          ),
        ),
        RepositoryProvider(
          create: (context) => LogoutRepository(),
        ),
        RepositoryProvider(
          create: (context) => CallRatesRepository(),
        ),
        RepositoryProvider(
          create: (context) => FetchSettingsRepository(),
        ),
        RepositoryProvider(
          create: (context) => PushNotificationService(),
        ),
        RepositoryProvider(
          create: (context) => AppInstance.getInstance(),
        ),
        RepositoryProvider(
          create: (context) => PhoneNumberService(),
        ),
        RepositoryProvider(
          create: (context) => ContactListService(),
        ),
        RepositoryProvider(
          create: (context) =>
              Mixpanel('53dd3138b1f46e58a8805eea429fc99b')..optInTracking(),
        ),
        RepositoryProvider(
          create: (context) => EventTrackerService()
            ..initialize(
              mixpanel: context.read<Mixpanel>(),
              facebookAppEvents: context.read<FacebookAppEvents>(),
            ),
        ),
        RepositoryProvider(
          create: (context) => LocalNotificationService.instance,
        ),
      ],
      child: MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) => ConnectivityBloc(
              connectivityManager: context.read<ConnectivityManager>(),
            ),
            lazy: false,
          ),
          BlocProvider(
            create: (context) => LocalNotificationBloc(
              notificationService: context.read<LocalNotificationService>(),
            ),
            // lazy: false,
          ),
          BlocProvider(
            create: (context) => AppBloc(
              facebookAppEvents: context.read<FacebookAppEvents>(),
              connectivityBloc: context.read<ConnectivityBloc>(),
              settings: context.read<FetchSettingsRepository>(),
            )..add(
                const AppEvent.started(),
              ),
            // lazy: false,
          ),
          BlocProvider(
            create: (context) => OnboardingBloc()
              ..add(
                const OnboardingEvent.started(),
              ),
            lazy: false,
          ),
          BlocProvider(
            create: (context) => PushNotificationBloc(
              service: context.read<PushNotificationService>(),
            )..add(
                const PushNotificationEvent.started(),
              ),
            // lazy: false,
          ),
          BlocProvider(
            create: (context) => AuthenticationBloc(
              pushNotificationService: context.read<PushNotificationService>(),
            )..add(
                const AuthenticationEvent.refreshProfile(),
              ),
            lazy: false,
          ),
          BlocProvider(
            create: (context) => SendOtpFormBloc()
              ..add(
                const SendOtpFormEvent.started(),
              ),
          ),
          // Specialized blocs for better separation of concerns
          BlocProvider(
            create: (context) => AudioBloc(
              callService: context.read<CallService>(),
            ),
          ),
          BlocProvider(
            create: (context) => RegistrationBloc(
              sipHelper: context.read<SIPUAHelper>(),
              callService: context.read<CallService>(),
            ),
          ),
          BlocProvider(
            create: (context) => CallTimerBloc(),
          ),
          BlocProvider(
            create: (context) => DialerBloc(
              phoneNumberService: context.read<PhoneNumberService>(),
              sipHelper: context.read<SIPUAHelper>(),
              callService: context.read<CallService>(),
              // Inject specialized blocs for delegation
              audioBloc: context.read<AudioBloc>(),
              registrationBloc: context.read<RegistrationBloc>(),
              callTimerBloc: context.read<CallTimerBloc>(),
            ),
          ),
          BlocProvider(
            create: (context) => KeypadBloc(
              phoneNumberService: context.read<PhoneNumberService>(),
              callRatesRepository: context.read<CallRatesRepository>(),
            ),
          ),
          BlocProvider(
            create: (context) => LoadVoucherBloc(),
          ),
          BlocProvider(
            create: (context) => ReferralBloc(),
          ),
          BlocProvider(
            create: (context) => ContactListBlocV2(),
          ),
          BlocProvider(
            create: (context) => ContactListBloc(
              context.read<ContactListService>(),
            ),
            lazy: false,
          ),
          BlocProvider(
            create: (context) => FavouriteContactsBloc(
              contactsService: context.read<ContactListService>(),
              phoneNumberService: context.read<PhoneNumberService>(),
            ),
          ),
          BlocProvider(
            create: (context) => RecentCallsBloc(),
          ),
          BlocProvider(
            create: (context) => RatesBloc(),
          ),
          BlocProvider(
            create: (context) => RadioStationsBloc(
              playerService: context.read<RadioPlayerService>(),
            )..add(
                const RadioStationsEvent.started(),
              ),
            // lazy: false,
          ),
          BlocProvider(
            lazy: false,
            create: (context) => LanguageBloc(
              radioStationsBloc: context.read<RadioStationsBloc>(),
            )..add(
                const LanguageEvent.started(),
              ),
          ),
          BlocProvider(
            create: (context) => EditProfileBloc(),
          ),
          BlocProvider(
            create: (context) => DeleteProfileBloc(),
          ),
          BlocProvider(
            create: (context) => NotificationsBloc(),
            // lazy: false,
          ),
          BlocProvider(
            create: (context) => AllNotificationsBloc(),
            // lazy: false,
          ),
          BlocProvider(
            create: (context) => UnreadNotificationsBloc()
              ..add(
                const UnreadNotificationsEvent.started(),
              ),
            // lazy: false,
          ),
          BlocProvider(
            create: (context) => ContactTabsBloc(),
            // lazy: false,
          ),
          BlocProvider(
            create: (context) => CheckoutBloc(),
            // lazy: false,
          ),
        ],
        child: BlocSelector<LanguageBloc, LanguageState, Language?>(
          selector: (state) => state.savedLanguage,
          builder: (context, savedLanguage) {
            return OverlaySupport.global(
              child: MaterialApp(
                color: FroggyColors.primary,
                themeMode: ThemeMode.light,
                theme: FroggyAppTheme.lightTheme,
                // darkTheme: FroggyAppTheme.darkTheme,
                // debugShowMaterialGrid: true,
                debugShowCheckedModeBanner: false,
                navigatorKey: FroggyRouter.navigatorKey,
                routes: AppConfigurationRepository.getRoutesConfiguration,
                locale: Locale(savedLanguage?.code ?? 'en'),
                localizationsDelegates: const [
                  AppLocalizations.delegate,
                  TigrinyaMaterialLocalizationsDelegate(),
                  GlobalMaterialLocalizations.delegate,
                  GlobalWidgetsLocalizations.delegate,
                  GlobalCupertinoLocalizations.delegate,
                  DinkaMaterialLocalizationsDelegate(),
                ],
                supportedLocales: AppLocalizations.supportedLocales,
                navigatorObservers: [CustomerIoRouteObserver()],
                home: BlocBuilder<OnboardingBloc, OnboardingState>(
                  builder: (context, state) {
                    if (!state.isCompleted) {
                      return const OnboardingPage();
                    }

                    return const AppLoadingPage();
                  },
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

class CustomerIoRouteObserver extends RouteObserver<PageRoute<dynamic>> {
  /// Sends screen view event to CustomerIO analytics
  ///
  /// This method attempts to extract the actual widget type being displayed
  /// for more accurate screen tracking. If the route name is set, it uses that.
  /// Otherwise, it tries to get the runtimeType of the widget from the route's
  /// builder.
  /// Falls back to 'Unknown' if extraction fails.
  void _sendScreenView(PageRoute<dynamic> route) {
    var screenName = 'Unknown';
    try {
      // Use the route name if available and meaningful
      if (route.settings.name != null && route.settings.name!.isNotEmpty) {
        screenName = route.settings.name!;
      } else if (route is MaterialPageRoute) {
        // Use the widget's runtimeType directly for analytics
        final widget = route.builder(route.navigator!.context);
        final type = widget.runtimeType.toString();
        // Filter out BLoC provider wrappers from analytics
        if (type.contains('BlocProvider') ||
            type.contains('MultiBlocProvider')) {
          // Try to get the child widget's type 
          // if possible (commonly .child or .children)
          final child = _extractChildWidget(widget);
          screenName = child?.runtimeType.toString() ?? 'Unknown';
        } else {
          screenName = type;
        }
      } else if (route is PageRouteBuilder) {
        final widget = route.pageBuilder(
          route.navigator!.context,
          route.animation!,
          route.secondaryAnimation!,
        );
        final type = widget.runtimeType.toString();
        if (type.contains('BlocProvider') ||
            type.contains('MultiBlocProvider')) {
          final child = _extractChildWidget(widget);
          screenName = child?.runtimeType.toString() ?? 'Unknown';
        } else {
          screenName = type;
        }
      }
    } catch (e, stack) {
      debugPrint('CustomerIO screen tracking error: $e');
      debugPrintStack(stackTrace: stack);
    }
    // Track screen view in CustomerIO
    CustomerIO.instance.screen(title: screenName);
  }

  /// Attempts to extract the child widget from a provider wrapper.
  /// Returns null if not found or not accessible.
  Widget? _extractChildWidget(dynamic widget) {
    try {
      // Handle MultiBlocProvider specifically
      if (widget is MultiBlocProvider) {
        // MultiBlocProvider doesn't expose child as a getter
        // Return the widget type itself for tracking
        return widget;
      }
      // Handle BlocProvider specifically  
      if (widget is BlocProvider) {
        // BlocProvider also doesn't expose child as a getter
        // Return the widget type itself for tracking
        return widget;
      }
      // For other provider types, return the widget itself
      if (widget is Widget) {
        return widget;
      }
    } catch (_) {
      // Ignore errors and return null
    }
    return null;
  }

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
    if (route is PageRoute) {
      _sendScreenView(route);
    }
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
    if (newRoute is PageRoute) {
      _sendScreenView(newRoute);
    }
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
    if (previousRoute is PageRoute && route is PageRoute) {
      _sendScreenView(previousRoute);
    }
  }
}
