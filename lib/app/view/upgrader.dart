import 'package:flutter/material.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:upgrader/upgrader.dart';
import 'package:utils/utils.dart';

class FroggyUpgrader extends Upgrader {
  FroggyUpgrader({super.debugLogging});

  @override
  bool isUpdateAvailable() {
    final storeVersion = currentAppStoreVersion;
    final installedVersion = currentInstalledVersion;
    FroggyLogger.debug('storeVersion=$storeVersion');
    FroggyLogger.debug('installedVersion=$installedVersion');
    return super.isUpdateAvailable();
  }
}

class FroggyUpgradeAlert extends UpgradeAlert {
  FroggyUpgradeAlert({super.key, super.upgrader, super.child});

  /// Override the [createState] method to provide a custom class
  /// with overridden methods.
  @override
  UpgradeAlertState createState() => FroggyUpgradeAlertState();
}

class FroggyUpgradeAlertState extends UpgradeAlertState {
  @override
  void showTheDialog({
    required BuildContext context,
    required String? title,
    required String message,
    required String? releaseNotes,
    required bool barrierDismissible,
    required UpgraderMessages messages,
    Key? key,
  }) {
    final l10n = context.l10n;

    showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          key: key,
          title: Text(l10n.upgradeDialogTitle),
          content: SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                Text(l10n.upgradeDialogMessage),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: Text(l10n.upgradeDialogButtonNo),
              onPressed: () {
                onUserIgnored(context, true);
              },
            ),
            TextButton(
              child: Text(l10n.upgradeDialogButtonYes),
              onPressed: () {
                onUserUpdated(context, !widget.upgrader.blocked());
              },
            ),
          ],
        );
      },
    );
  }
}
