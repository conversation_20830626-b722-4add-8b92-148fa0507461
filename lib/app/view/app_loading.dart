import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:froggytalk/call_rates/call_rates.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:froggytalk/dialer/dialer.dart';
import 'package:froggytalk/language/language.dart';
import 'package:froggytalk/notifications/notifications.dart';
import 'package:froggytalk/profile/profile.dart';
import 'package:froggytalk/referral/referral.dart';
import 'package:froggytalk/voucher/voucher.dart';
import 'package:navigation/navigation.dart';
import 'package:permission_handler/permission_handler.dart';

class AppLoadingPage extends StatefulHookWidget {
  const AppLoadingPage({
    super.key,
    this.isLoading,
    this.isLarge = false,
  });

  final bool isLarge;
  final bool? isLoading;

  @override
  State<AppLoadingPage> createState() => _AppLoadingPageState();
}

class _AppLoadingPageState extends State<AppLoadingPage> {

  @override
  void initState() {
    super.initState();
  }
  
  
  @override
  Widget build(BuildContext context) {
    // final internetState = useHasInternet();
    final authenticationBloc = context.read<AuthenticationBloc>();
    final favContactsBloc = context.read<FavouriteContactsBloc>();
    final recentCallsBloc = context.read<RecentCallsBloc>();
    final dialerBloc = context.read<DialerBloc>();
    final referralBloc = context.read<ReferralBloc>();
    final contactBloc = context.read<ContactListBloc>();
    final keypadBloc = context.read<KeypadBloc>();
    final notificationBloc = context.read<NotificationsBloc>();
    final unreadnotificationBloc = context.read<UnreadNotificationsBloc>();
    final allnotificationBloc = context.read<AllNotificationsBloc>();
    final callRatesBloc = context.read<RatesBloc>();
    final languageBloc = context.read<LanguageBloc>();
    final profileBloc = context.read<EditProfileBloc>();
    final deleteProfileBloc = context.read<DeleteProfileBloc>();
    final voucherBloc = context.read<LoadVoucherBloc>();
    final authStateMessage =
        context.select((AuthenticationBloc bloc) => bloc.state.message);

    // final goToOnboardingPage = useCallback(
    //   () => FroggyRouter.pushAndRemoveUntil(const OnboardingPage()),
    //   [],
    // );

    final goToDashboardPage = useCallback(
      ({String? username, String? password}) async {
        final status = await Permission.contacts.isGranted;

        final page = MultiBlocProvider(
          providers: [
            BlocProvider.value(
              value: dialerBloc,
            ),
            BlocProvider.value(
              value: recentCallsBloc..add(const RecentCallsEvent.started()),
            ),
            BlocProvider.value(
              value: favContactsBloc
                ..add(const FavouriteContactsEvent.started()),
            ),
            BlocProvider.value(
              value: referralBloc..add(const ReferralEvent.started()),
            ),
            BlocProvider.value(
              value: allnotificationBloc
                ..add(const AllNotificationsEvent.started()),
            ),
            BlocProvider.value(
              value: unreadnotificationBloc
                ..add(const UnreadNotificationsEvent.started()),
            ),
            BlocProvider.value(
              value: dialerBloc
                ..add(
                  DialerEvent.register(
                    sipUsername: username?.replaceFirst('+', '') ?? '',
                    sipPassword: password,
                  ),
                ),
            ),
          ],
          child: const AppDashboardPage(),
        );

        if (status) {
          contactBloc.add(const ContactListEvent.started());
        }

        FroggyRouter.pushAndRemoveUntil(page);
      },
      [],
    );

    final goToSignInPage = useCallback(() {
      final page = MultiBlocProvider(
        providers: [
          BlocProvider.value(
            value: dialerBloc..add(const DialerEvent.reset()),
          ),
          BlocProvider.value(
            value: recentCallsBloc..add(const RecentCallsEvent.reset()),
          ),
          BlocProvider.value(
            value: favContactsBloc..add(const FavouriteContactsEvent.reset()),
          ),
          BlocProvider.value(
            value: referralBloc..add(const ReferralEvent.reset()),
          ),
          BlocProvider.value(
            value: notificationBloc..add(const NotificationsEvent.reset()),
          ),
          BlocProvider.value(
            value: authenticationBloc..add(const AuthenticationEvent.reset()),
          ),
          // BlocProvider.value(
          //   value: contactBloc..add(const ContactListEvent.reset()),
          // ),
          BlocProvider.value(
            value: keypadBloc..add(const KeypadEvent.reset()),
          ),
          BlocProvider.value(
            value: callRatesBloc..add(const RatesEvent.reset()),
          ),
          BlocProvider.value(
            value: languageBloc..add(const LanguageEvent.reset()),
          ),
          BlocProvider.value(
            value: profileBloc..add(const EditProfileEvent.reset()),
          ),
          BlocProvider.value(
            value: deleteProfileBloc..add(const DeleteProfileEvent.reset()),
          ),
          BlocProvider.value(
            value: voucherBloc..add(const LoadVoucherEvent.reset()),
          ),
        ],
        child: const SendOtpPage(),
      );

      FroggyRouter.pushAndRemoveUntil(page);
    });

    // useEffect(
    //   () {
    //     authenticationBloc.add(const AuthenticationEvent.refreshProfile());
    //     return null;
    //   },
    //   [],
    // );

    return BlocProvider.value(
      value: authenticationBloc
        ..add(const AuthenticationEvent.refreshProfile()),
      child: BlocListener<AuthenticationBloc, AuthenticationState>(
        listener: (context, state) {
          if (state.user == null && state.status.isUnauthenticated) {
            goToSignInPage();
          }

          if (state.isAuthenticated) {
            final processedUsername =
                state.user!.formattedPhoneNumber?.replaceFirst('+', '');
            final processedPassword = state.user!.asteriskPassword;
            goToDashboardPage(
              username: processedUsername,
              password: processedPassword,
            );
          }
        },
        child: Scaffold(
          body: AppSplashscreen(
            message: authStateMessage,
            isLarge: true,
          ),
        ),
      ),
    );
  }
}
