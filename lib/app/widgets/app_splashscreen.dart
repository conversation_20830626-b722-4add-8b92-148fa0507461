import 'package:common/common.dart';
import 'package:constants/constants.dart';
import 'package:entry/entry.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/l10n/l10n.dart';

class AppSplashscreen extends HookWidget {
  const AppSplashscreen({
    super.key,
    this.isLoading = false,
    this.isLarge = false,
    this.progressValue,
    this.message,
    this.onPressedDemoAction,
    this.demoActionButtonText,
  });

  final bool isLoading;
  final bool isLarge;
  final double? progressValue;
  final String? demoActionButtonText;
  final VoidCallback? onPressedDemoAction;

  final String? message;

  @override
  Widget build(BuildContext context) {
    final l10n = useLocale();

    return Column(
      children: [
        Expanded(
          child: AppLogo(
            showEnvText: true,
            showLabel: true,
            isLarge: isLarge,
          ),
        ),
        if (isLoading)
          LinearProgressIndicator(
            backgroundColor: Theme.of(context).colorScheme.secondary,
            minHeight: 5,
            value: progressValue,
            borderRadius: BorderRadius.circular(5),
          ),
        if (message != null)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 10),
            child: Text(
              message ?? '',
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ),
        if (onPressedDemoAction != null)
          Container(
            margin: const EdgeInsets.only(top: 20),
            padding: const EdgeInsets.symmetric(horizontal: 20),
            width: double.infinity,
            height: 48,
            child: ElevatedButton(
              onPressed: onPressedDemoAction,
              style: ElevatedButton.styleFrom(
                side: BorderSide.none,
              ),
              child: Text(
                demoActionButtonText ?? 'Demo Action',
                style: const TextStyle(
                  color: FroggyColors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ),
          ),
        if (onPressedDemoAction != null) FroggySpacer.y8(),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 10),
          child: Entry.offset(
            delay: const Duration(microseconds: 700),
            child: Text(
              l10n.appBootingTextBelow,
            ),
          ),
        ),
      ],
    );
  }
}
