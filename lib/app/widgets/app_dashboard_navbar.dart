import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggy_icons/froggy_icons.dart';
import 'package:froggytalk/app/app.dart';

class AppDashboardNavbar extends HookWidget {
  const AppDashboardNavbar({
    this.initialIndex = 0,
    this.isEnabled = true,
    this.onChanged,
    super.key,
  });

  final void Function(int activeIndex)? onChanged;
  final bool isEnabled;
  final int initialIndex;

  @override
  Widget build(BuildContext context) {
    final currentIndex = useState(initialIndex);
    // final navigationItems = useMemoized<List<Map<String, String>>>(
    //   () => AppConfigurationRepository.getNavigationItems,
    //   [],
    // );
    final navigationItems = useNavigationItems();

    final onDestinationSelected = useCallback(
      (int index) {
        currentIndex.value = index;
      },
      [currentIndex.value],
    );

    useEffect(
      () {
        void currentIndexListener() {
          onChanged?.call(currentIndex.value);
        }

        currentIndex.addListener(currentIndexListener);

        return () {
          currentIndex.removeListener(currentIndexListener);
        };
      },
      [currentIndex.value],
    );

    return ClipRRect(
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
      ),
      child: NavigationBar(
        height: 64.11,
        selectedIndex: currentIndex.value,
        onDestinationSelected: onDestinationSelected,
        animationDuration: const Duration(milliseconds: 500),
        destinations: navigationItems
            .map(
              (destination) => NavigationDestination(
                key: ValueKey('${destination['label']}'),
                label: '${destination['label']}',
                tooltip: '${destination['label']}',
                enabled: isEnabled,
                icon: FroggyIcon(
                  '${destination['icon']}_outline',
                  height: 24,
                  width: 24,
                ),
                selectedIcon: FroggyIcon(
                  '${destination['icon']}_solid',
                  height: 24,
                  width: 24,
                ),
              ),
            )
            .toList(),
      ),
    );
  }
}
