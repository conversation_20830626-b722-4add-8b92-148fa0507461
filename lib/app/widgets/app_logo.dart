import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggy_icons/froggy_icons.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/l10n/l10n.dart';

class AppLogo extends HookWidget {
  const AppLogo({
    super.key,
    this.height = 115,
    this.showEnvText = false,
    this.showLabel = false,
    this.isLarge = false,
  });

  final double height;
  final bool showEnvText;
  final bool showLabel;
  final bool isLarge;

  @override
  Widget build(BuildContext context) {
    final l10n = useLocale();
    final environmentType = AppInstance.getInstance().environmentType;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Center(
          child: SizedBox(
            width: isLarge ? 200 * 1.5 : 200,
            height: isLarge ? 100 * 1.5 : 100,
            child: Visibility(
              visible: !environmentType.isProduction && showEnvText,
              replacement: _buildOptimizedLogo(),
              child: Stack(
                children: [
                  if (showEnvText) _constructEnvironmentText(environmentType),
                  _buildOptimizedLogo(),
                ],
              ),
            ),
          ),
        ),
        if (showLabel)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 10),
            child: Text(
              l10n.appTitle,
              style: Theme.of(context).textTheme.displayLarge?.copyWith(
                    fontSize: 40,
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ),
      ],
    );
  }

  Positioned _constructEnvironmentText(EnvironmentType environmentType) {
    final shortName = environmentType.toShortName;

    return Positioned(
      top: 80,
      left: 10,
      width: 80,
      child: Transform.rotate(
        angle: 270 * (3.1415926535897932 / 180),
        child: Builder(
          builder: (context) {
            return Text(
              '[$shortName]',
              style: Theme.of(context).textTheme.displayLarge?.copyWith(
                    color: FroggyColors.froggyBlack,
                    backgroundColor: FroggyColors.transparent,
                    fontWeight: FontWeight.bold,
                    fontSize: 25,
                  ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildOptimizedLogo() {
    return Center(
      child: FroggyIcon(
        'packages/froggy_icons/logos/froggytalk.svg.vec',
        height: isLarge ? 115 * 1.5 : 115,
        width: isLarge ? 122 * 1.5 : 122,
      ),
    );
  }
}
