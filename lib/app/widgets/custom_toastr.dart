import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:froggytalk/app/app.dart';
import 'package:google_fonts/google_fonts.dart';

class FroggyToast extends StatelessWidget {
  const FroggyToast({
    this.message,
    this.color = Colors.white,
    this.bgColor,
    this.trailing,
    this.leading,
    this.fontWeight = FontWeight.w500,
    this.fontSize = 16,
    this.mode = FroggyToastMode.success,
    this.locale = const Locale('en'),
    super.key,
  });

  final Color? bgColor;
  final Color color;
  final String? message;
  final Widget? leading;
  final Widget? trailing;
  final FontWeight fontWeight;
  final double fontSize;
  final FroggyToastMode mode;
  final Locale locale;

  static void showSuccessToast(
    BuildContext context,
    String message, {
    Locale locale = const Locale('en'),
  }) {
    final overlay = Overlay.of(context);
    final overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).size.height * 0.05,
        left: MediaQuery.of(context).size.width * 0.05,
        right: MediaQuery.of(context).size.width * 0.05,
        child: FroggyToast(
          message: message,
          bgColor: FroggyColors.froggyLighterGreen,
          locale: locale,
        ),
      ),
    );

    // Insert the toast into the overlay
    overlay.insert(overlayEntry);

    // Remove the toast after 3 seconds
    Future<void>.delayed(const Duration(seconds: 3)).then((_) {
      overlayEntry.remove();
    });
  }

  static void showErrorToast(
    BuildContext context,
    String message, {
    Locale locale = const Locale('en'),
  }) {
    final overlay = Overlay.of(context);
    final overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).size.height * 0.05,
        left: MediaQuery.of(context).size.width * 0.05,
        right: MediaQuery.of(context).size.width * 0.05,
        child: FroggyToast(
          message: message,
          bgColor: FroggyColors.fromHexToColor('#FBD9D9'),
          mode: FroggyToastMode.error,
          locale: locale,
        ),
      ),
    );

    // Insert the toast into the overlay
    overlay.insert(overlayEntry);

    // Remove the toast after 3 seconds
    Future<void>.delayed(const Duration(seconds: 3)).then((_) {
      overlayEntry.remove();
    });
  }

  static void showCustomToast(
    BuildContext context,
    String message, {
    Color? bgColor,
    Color? color,
    Locale locale = const Locale('en'),
  }) {
    final overlay = Overlay.of(context);
    final overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).size.height * 0.05,
        left: MediaQuery.of(context).size.width * 0.05,
        right: MediaQuery.of(context).size.width * 0.05,
        child: FroggyToast(
          message: message,
          bgColor: bgColor,
          color: color ?? Colors.white,
          mode: FroggyToastMode.custom,
          locale: locale,
        ),
      ),
    );

    // Insert the toast into the overlay
    overlay.insert(overlayEntry);

    // Remove the toast after 3 seconds
    Future<void>.delayed(const Duration(seconds: 3)).then((_) {
      overlayEntry.remove();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Localizations.override(
      context: context,
      locale: locale,
      child: Container(
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: BorderRadius.circular(8),
          boxShadow: const [
            BoxShadow(
              color: Colors.black26,
              blurRadius: 4,
              spreadRadius: 1,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            leading ??
                Visibility(
                  visible: mode.isSuccess,
                  replacement: SvgPicture.asset(
                    'assets/logos/froggy-sad-x32.svg',
                    width: 45,
                    height: 40,
                  ),
                  child: SvgPicture.asset(
                    'assets/logos/froggy-happy-x32.svg',
                    width: 45,
                    height: 40,
                  ),
                ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                message ?? '',
                style: TextStyle(
                  color: Colors.black87,
                  fontSize: fontSize,
                  fontFamily: GoogleFonts.inter().fontFamily,
                  fontWeight: fontWeight,
                  decoration: TextDecoration.none,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            if (trailing != null) trailing!,
          ],
        ),
      ),
    );
  }
}
