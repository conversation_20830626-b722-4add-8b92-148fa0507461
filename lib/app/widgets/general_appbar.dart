import 'package:constants/constants.dart';
import 'package:flutter/material.dart';

class FroggyAppBar extends StatelessWidget implements PreferredSizeWidget {
  const FroggyAppBar({
    required this.label,
    this.labelFontSize = 24,
    this.useCloseButton = false,
    super.key,
  });

  final double labelFontSize;

  double get height => 60;

  final String label;

  final bool useCloseButton;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 1),
      child: AppBar(
        leading: useCloseButton ? const CloseButton() : null,
        toolbarHeight: height,
        title: Text(
          label,
          style: TextStyle(
            color: FroggyColors.black,
            fontSize: labelFontSize,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(height);
}
