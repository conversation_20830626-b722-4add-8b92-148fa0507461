import 'package:badges/badges.dart' as badges;
import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggy_icons/froggy_icons.dart';
import 'package:froggytalk/notifications/notifications.dart';

class AppNotificationBell extends HookWidget {
  const AppNotificationBell({
    this.notificationCount = 0,
    super.key,
    this.enabled = true,
  });

  final double notificationCount;
  final bool enabled;

  @override
  Widget build(BuildContext context) {
    final notificationAsString = useMemoized(
      () {
        return notificationCount.toInt().toString();
      },
      [notificationCount],
    );

    final onNotificationBellPressed = useCallback(() {
      Navigator.push(context, NotificationPage.route());
    });

    return Padding(
      padding: EdgeInsets.all(notificationCount <= 0 ? 0 : 10),
      child: InkWell(
        onTap: enabled ? onNotificationBellPressed : null,
        child: Builder(
          builder: (context) {
            if (notificationCount <= 0) {
              return _buildIcon();
            }

            return badges.Badge(
              position: !(notificationCount >= 1 && notificationCount <= 9)
                  ? null
                  : badges.BadgePosition.custom(
                      top: -8,
                      start: 15,
                    ),
              badgeContent: Text(
                notificationAsString,
                style: const TextStyle(
                  color: FroggyColors.froggyWhite,
                  fontSize: 8,
                  fontWeight: FontWeight.w600,
                ),
              ),
              badgeStyle: const badges.BadgeStyle(
                badgeColor: FroggyColors.froggyError,
              ),
              child: _buildIcon(),
            );
          },
        ),
      ),
    );
  }

  Widget _buildIcon() {
    return FroggyIconsList.notificationBellEmpty.toWidget(
      color: FroggyColors.black,
    );
  }
}
