// Example: Integrating iOS CallKit service into existing call flow
// This example shows how to integrate the new enhanced call
// notification service to fix the "call notification service
// unavailable" error on iOS

import 'package:flutter/material.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/dialer/dialer.dart';

/// Example integration of enhanced call notification service
/// This replaces the problematic foreground service approach on iOS
class ExampleCallIntegration extends StatefulWidget {
  const ExampleCallIntegration({super.key});

  @override
  State<ExampleCallIntegration> createState() => _ExampleCallIntegrationState();
}

class _ExampleCallIntegrationState extends State<ExampleCallIntegration> {
  late final EnhancedCallNotificationService _callNotificationService;
  late final DialerBloc _dialerBloc;
  
  @override
  void initState() {
    super.initState();
    
    // Initialize enhanced call notification service
    _callNotificationService = EnhancedCallNotificationService();
    
    // Get DialerBloc from context (assuming it's provided higher up)
    // _dialerBloc = DialerBloc(/* your dependencies */);
    
    // Connect the service to the DialerBloc
    _callNotificationService.setDialerBloc(_dialerBloc);
    
    // Check if call notification service is available
    _checkServiceAvailability();
  }

  void _checkServiceAvailability() {
    if (!_callNotificationService.isCallNotificationAvailable) {
      // Handle service unavailability
      debugPrint(
        'Call notification service unavailable: '
        '${_callNotificationService.platformSpecificError}',
      );
      // Show user-friendly message or use fallback
      if (_callNotificationService.hasNotificationServiceIssues) {
        _showServiceUnavailableDialog();
      }
    } else {
      debugPrint('Call notification service available and ready');
    }
  }

  /// Example: Starting an outgoing call with proper iOS integration
  Future<void> _startOutgoingCall(String phoneNumber) async {
    try {
      // This will use CallKit on iOS and foreground service on Android
      await _callNotificationService.startOutgoingCall(
        phoneNumber: phoneNumber,
        displayName: 'Contact Name', // Optional
      );
      // Continue with your existing call logic
      _dialerBloc.add(
        DialerEvent.callStarted(phoneNumber: phoneNumber),
      );
    } catch (e) {
      debugPrint(
        'Error starting outgoing call: $e',
      );
      // Fallback: proceed with call without system integration
      _startCallWithoutSystemIntegration(phoneNumber);
    }
  }

  /// Example: Handling incoming call with proper iOS integration
  Future<void> _handleIncomingCall({
    required String phoneNumber,
    String? callerName,
  }) async {
    try {
      // This will show native iOS call screen or Android notification
      await _callNotificationService.reportIncomingCall(
        phoneNumber: phoneNumber,
        callerName: callerName,
      );
    } catch (e) {
      debugPrint(
        'Error reporting incoming call: $e',
      );
      // Fallback: show in-app call screen
      _showInAppCallScreen(phoneNumber, callerName);
    }
  }

  /// Example: Updating call state (when call connects)
  Future<void> _onCallConnected() async {
    try {
      // Report to iOS CallKit that call is connected
      await _callNotificationService.reportCallConnected();
      // Update your UI accordingly
      setState(() {
        // Update call state
      });
    } catch (e) {
      debugPrint(
        'Error reporting call connected: $e',
      );
      // Continue normally, this is not critical
    }
  }

  /// Example: Ending call with proper cleanup
  Future<void> _endCall() async {
    try {
      // This will properly end the CallKit call on iOS
      await _callNotificationService.endCall();
      // Continue with your existing call termination logic
      _dialerBloc.add(
        const DialerEvent.hangedup(),
      );
    } catch (e) {
      debugPrint(
        'Error ending call: $e',
      );
      // Still end the call, just without system integration
      _dialerBloc.add(
        const DialerEvent.hangedup(),
      );
    }
  }

  /// Example: Handling mute/hold with iOS integration
  Future<void> _toggleMute(bool muted) async {
    try {
      // This will update iOS CallKit mute state
      await _callNotificationService.setCallMuted(muted);
      // Continue with your existing mute logic
      if (muted) {
        _dialerBloc.add(
          const DialerEvent.muteOn(),
        );
      } else {
        _dialerBloc.add(
          const DialerEvent.muteOff(),
        );
      }
    } catch (e) {
      debugPrint(
        'Error setting mute state: $e',
      );
      // Continue with mute without system integration
    }
  }

  Future<void> _toggleHold(bool onHold) async {
    try {
      // This will update iOS CallKit hold state
      await _callNotificationService.setCallOnHold(onHold);
      // Continue with your existing hold logic
      if (onHold) {
        _dialerBloc.add(
          const DialerEvent.holdCall(),
        );
      } else {
        _dialerBloc.add(
          const DialerEvent.unholdCall(),
        );
      }
    } catch (e) {
      debugPrint(
        'Error setting hold state: $e',
      );
      // Continue with hold without system integration
    }
  }

  /// Fallback method when system integration is not available
  void _startCallWithoutSystemIntegration(String phoneNumber) {
    // Use your existing call logic without system integration
    _dialerBloc.add(
      DialerEvent.callStarted(phoneNumber: phoneNumber),
    );
    // Optionally show a message to user about limited functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text(
          'Limited call functionality: system integration '
          'unavailable.',
        ),
        duration: Duration(seconds: 2),
      ),
    );
  }

  /// Show in-app call screen for incoming calls when CallKit fails
  void _showInAppCallScreen(String phoneNumber, String? callerName) {
    // Navigate to your existing incoming call screen
    // This is your fallback when iOS CallKit is not available
    Navigator.of(context).pushNamed(
      '/incoming-call',
      arguments: {
        'phoneNumber': phoneNumber,
        'callerName': callerName,
      },
    );
  }

  /// Show dialog when call notification service is unavailable
  void _showServiceUnavailableDialog() {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Call Service Notice'),
        content: const Text(
          'Call notification service is not available. Calls will work but '
          'may not integrate with system call interface.\n\nDetails: '
          // ignore: lines_longer_than_80_chars
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Enhanced Call Integration'),
      ),
      body: Column(
        children: [
          // Service status indicator
          Container(
            padding: const EdgeInsets.all(16),
            color: _callNotificationService.isCallNotificationAvailable
                ? Colors.green.shade100
                : Colors.orange.shade100,
            child: Row(
              children: [
                Icon(
                  _callNotificationService.isCallNotificationAvailable
                      ? Icons.check_circle
                      : Icons.warning,
                  color: _callNotificationService.isCallNotificationAvailable
                      ? Colors.green
                      : Colors.orange,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _callNotificationService.isCallNotificationAvailable
                        ? 'Service available'
                        : 'Service unavailable',
                  ),
                ),
              ],
            ),
          ),
          
          // Example call buttons
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ElevatedButton(
                    onPressed: () => _startOutgoingCall('+1234567890'),
                    child: const Text('Start Outgoing Call'),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => _handleIncomingCall(
                      phoneNumber: '+0987654321',
                      callerName: 'John Doe',
                    ),
                    child: const Text('Simulate Incoming Call'),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _endCall,
                    child: const Text('End Call'),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Integration Notes:
/// 
/// 1. **Service Initialization**: Initialize
///    EnhancedCallNotificationService early in your app.
/// 2. **DialerBloc Connection**: Connect the service to your DialerBloc
///    for proper integration.
/// 3. **Error Handling**: Always wrap service calls in try-catch for
///    graceful degradation.
/// 4. **Platform Awareness**: The service automatically handles iOS vs
///    Android differences.
/// 5. **Fallback Support**: Always provide fallback functionality for
///    when service is unavailable.
/// 
/// This approach eliminates the
/// "call notification service unavailable" error on iOS while maintaining
/// full functionality and providing proper system integration.
