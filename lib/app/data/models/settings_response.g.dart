// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'settings_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SettingsResponseImpl _$$SettingsResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$SettingsResponseImpl(
      key: json['key'] as String,
      value: json['value'],
      type: json['type'] as String? ?? 'string',
    );

const _$$SettingsResponseImplFieldMap = <String, String>{
  'key': 'key',
  'value': 'value',
  'type': 'type',
};

Map<String, dynamic> _$$SettingsResponseImplToJson(
        _$SettingsResponseImpl instance) =>
    <String, dynamic>{
      'key': instance.key,
      if (instance.value case final value?) 'value': value,
      'type': instance.type,
    };
