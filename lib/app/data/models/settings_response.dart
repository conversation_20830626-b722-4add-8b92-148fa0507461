import 'package:freezed_annotation/freezed_annotation.dart';

part 'settings_response.freezed.dart';
part 'settings_response.g.dart';

@freezed
class SettingsResponse with _$SettingsResponse {
  factory SettingsResponse({
    required String key,
    required dynamic value,
    // String? id,
    @Default('string') String type,
    // @J<PERSON><PERSON><PERSON>(name: 'created_at') DateTime? createdAt,
    // @Json<PERSON>ey(name: 'updated_at') DateTime? updatedAt,
  }) = _SettingsResponse;

  factory SettingsResponse.fromJson(Map<String, Object?> json) =>
      _$SettingsResponseFromJson(json);
}
