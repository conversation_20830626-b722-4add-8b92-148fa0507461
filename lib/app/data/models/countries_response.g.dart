// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'countries_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CountriesResponseImpl _$$CountriesResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$CountriesResponseImpl(
      id: json['id'] as String,
      type: json['type'] as String? ?? 'country',
      attributes: json['attributes'] == null
          ? null
          : CountryModel.fromJson(json['attributes'] as Map<String, dynamic>),
    );

const _$$CountriesResponseImplFieldMap = <String, String>{
  'id': 'id',
  'type': 'type',
  'attributes': 'attributes',
};

Map<String, dynamic> _$$CountriesResponseImplToJson(
        _$CountriesResponseImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      if (instance.attributes?.toJson() case final value?) 'attributes': value,
    };
