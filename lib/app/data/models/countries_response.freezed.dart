// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'countries_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CountriesResponse _$CountriesResponseFromJson(Map<String, dynamic> json) {
  return _CountriesResponse.fromJson(json);
}

/// @nodoc
mixin _$CountriesResponse {
  String get id => throw _privateConstructorUsedError;
  String get type => throw _privateConstructorUsedError;
  CountryModel? get attributes => throw _privateConstructorUsedError;

  /// Serializes this CountriesResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CountriesResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CountriesResponseCopyWith<CountriesResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CountriesResponseCopyWith<$Res> {
  factory $CountriesResponseCopyWith(
          CountriesResponse value, $Res Function(CountriesResponse) then) =
      _$CountriesResponseCopyWithImpl<$Res, CountriesResponse>;
  @useResult
  $Res call({String id, String type, CountryModel? attributes});

  $CountryModelCopyWith<$Res>? get attributes;
}

/// @nodoc
class _$CountriesResponseCopyWithImpl<$Res, $Val extends CountriesResponse>
    implements $CountriesResponseCopyWith<$Res> {
  _$CountriesResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CountriesResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? type = null,
    Object? attributes = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      attributes: freezed == attributes
          ? _value.attributes
          : attributes // ignore: cast_nullable_to_non_nullable
              as CountryModel?,
    ) as $Val);
  }

  /// Create a copy of CountriesResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CountryModelCopyWith<$Res>? get attributes {
    if (_value.attributes == null) {
      return null;
    }

    return $CountryModelCopyWith<$Res>(_value.attributes!, (value) {
      return _then(_value.copyWith(attributes: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CountriesResponseImplCopyWith<$Res>
    implements $CountriesResponseCopyWith<$Res> {
  factory _$$CountriesResponseImplCopyWith(_$CountriesResponseImpl value,
          $Res Function(_$CountriesResponseImpl) then) =
      __$$CountriesResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, String type, CountryModel? attributes});

  @override
  $CountryModelCopyWith<$Res>? get attributes;
}

/// @nodoc
class __$$CountriesResponseImplCopyWithImpl<$Res>
    extends _$CountriesResponseCopyWithImpl<$Res, _$CountriesResponseImpl>
    implements _$$CountriesResponseImplCopyWith<$Res> {
  __$$CountriesResponseImplCopyWithImpl(_$CountriesResponseImpl _value,
      $Res Function(_$CountriesResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of CountriesResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? type = null,
    Object? attributes = freezed,
  }) {
    return _then(_$CountriesResponseImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      attributes: freezed == attributes
          ? _value.attributes
          : attributes // ignore: cast_nullable_to_non_nullable
              as CountryModel?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CountriesResponseImpl implements _CountriesResponse {
  _$CountriesResponseImpl(
      {required this.id, this.type = 'country', this.attributes});

  factory _$CountriesResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$CountriesResponseImplFromJson(json);

  @override
  final String id;
  @override
  @JsonKey()
  final String type;
  @override
  final CountryModel? attributes;

  @override
  String toString() {
    return 'CountriesResponse(id: $id, type: $type, attributes: $attributes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CountriesResponseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.attributes, attributes) ||
                other.attributes == attributes));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, type, attributes);

  /// Create a copy of CountriesResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CountriesResponseImplCopyWith<_$CountriesResponseImpl> get copyWith =>
      __$$CountriesResponseImplCopyWithImpl<_$CountriesResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CountriesResponseImplToJson(
      this,
    );
  }
}

abstract class _CountriesResponse implements CountriesResponse {
  factory _CountriesResponse(
      {required final String id,
      final String type,
      final CountryModel? attributes}) = _$CountriesResponseImpl;

  factory _CountriesResponse.fromJson(Map<String, dynamic> json) =
      _$CountriesResponseImpl.fromJson;

  @override
  String get id;
  @override
  String get type;
  @override
  CountryModel? get attributes;

  /// Create a copy of CountriesResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CountriesResponseImplCopyWith<_$CountriesResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
