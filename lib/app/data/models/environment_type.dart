enum EnvironmentType {
  development,
  production,
  staging,
}

extension EnvironmentTypeX on EnvironmentType {
  String get name {
    switch (this) {
      case EnvironmentType.development:
        return 'Development';
      case EnvironmentType.production:
        return 'Production';
      case EnvironmentType.staging:
        return 'Staging';
    }
  }

  String get toShortName {
    switch (this) {
      case EnvironmentType.development:
        return 'DEV';
      case EnvironmentType.production:
        return 'PRD';
      case EnvironmentType.staging:
        return 'STG';
    }
  }

  bool get isDevelopment => this == EnvironmentType.development;
  bool get isProduction => this == EnvironmentType.production;
  bool get isStaging => this == EnvironmentType.staging;
  bool get isNotProduction => !isProduction;
  bool get isNotStaging => !isStaging;
}
