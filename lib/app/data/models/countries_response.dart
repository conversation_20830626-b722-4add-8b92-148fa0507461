import 'package:countries/countries.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'countries_response.freezed.dart';
part 'countries_response.g.dart';

@freezed
class CountriesResponse with _$CountriesResponse {
  factory CountriesResponse({
    required String id,
    @Default('country') String type,
    CountryModel? attributes,
  }) = _CountriesResponse;

  factory CountriesResponse.fromJson(Map<String, Object?> json) =>
      _$CountriesResponseFromJson(json);
}
