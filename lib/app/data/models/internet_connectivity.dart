enum ConnectivityStatus {
  none,
  disconnected,
  connected,
  connectedButProbablyNoNetwork,
  reconnected,
}

extension InternetConnectivityExtension on ConnectivityStatus {
  bool get hasNoInternetConnection => this == ConnectivityStatus.none;

  bool get isConnected => this == ConnectivityStatus.connected;

  bool get isDisconnected => this == ConnectivityStatus.disconnected;

  bool get maybeConnected =>
      this == ConnectivityStatus.connectedButProbablyNoNetwork;

  bool get hasConnection => maybeConnected || isConnected || isReconnected;

  bool get isReconnected => this == ConnectivityStatus.reconnected;

  bool get hasLostConnection {
    final status = this;
    if (status.hasConnection) {
      return false;
    } else if (status.isDisconnected) {
      return false;
    } else if (status.hasNoInternetConnection) {
      return false;
    }

    return true;
  }
}
