// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'settings_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SettingsResponse _$SettingsResponseFromJson(Map<String, dynamic> json) {
  return _SettingsResponse.fromJson(json);
}

/// @nodoc
mixin _$SettingsResponse {
  String get key => throw _privateConstructorUsedError;
  dynamic get value => throw _privateConstructorUsedError; // String? id,
  String get type => throw _privateConstructorUsedError;

  /// Serializes this SettingsResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SettingsResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SettingsResponseCopyWith<SettingsResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SettingsResponseCopyWith<$Res> {
  factory $SettingsResponseCopyWith(
          SettingsResponse value, $Res Function(SettingsResponse) then) =
      _$SettingsResponseCopyWithImpl<$Res, SettingsResponse>;
  @useResult
  $Res call({String key, dynamic value, String type});
}

/// @nodoc
class _$SettingsResponseCopyWithImpl<$Res, $Val extends SettingsResponse>
    implements $SettingsResponseCopyWith<$Res> {
  _$SettingsResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SettingsResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? key = null,
    Object? value = freezed,
    Object? type = null,
  }) {
    return _then(_value.copyWith(
      key: null == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as dynamic,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SettingsResponseImplCopyWith<$Res>
    implements $SettingsResponseCopyWith<$Res> {
  factory _$$SettingsResponseImplCopyWith(_$SettingsResponseImpl value,
          $Res Function(_$SettingsResponseImpl) then) =
      __$$SettingsResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String key, dynamic value, String type});
}

/// @nodoc
class __$$SettingsResponseImplCopyWithImpl<$Res>
    extends _$SettingsResponseCopyWithImpl<$Res, _$SettingsResponseImpl>
    implements _$$SettingsResponseImplCopyWith<$Res> {
  __$$SettingsResponseImplCopyWithImpl(_$SettingsResponseImpl _value,
      $Res Function(_$SettingsResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of SettingsResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? key = null,
    Object? value = freezed,
    Object? type = null,
  }) {
    return _then(_$SettingsResponseImpl(
      key: null == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as dynamic,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SettingsResponseImpl implements _SettingsResponse {
  _$SettingsResponseImpl(
      {required this.key, required this.value, this.type = 'string'});

  factory _$SettingsResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$SettingsResponseImplFromJson(json);

  @override
  final String key;
  @override
  final dynamic value;
// String? id,
  @override
  @JsonKey()
  final String type;

  @override
  String toString() {
    return 'SettingsResponse(key: $key, value: $value, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SettingsResponseImpl &&
            (identical(other.key, key) || other.key == key) &&
            const DeepCollectionEquality().equals(other.value, value) &&
            (identical(other.type, type) || other.type == type));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, key, const DeepCollectionEquality().hash(value), type);

  /// Create a copy of SettingsResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SettingsResponseImplCopyWith<_$SettingsResponseImpl> get copyWith =>
      __$$SettingsResponseImplCopyWithImpl<_$SettingsResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SettingsResponseImplToJson(
      this,
    );
  }
}

abstract class _SettingsResponse implements SettingsResponse {
  factory _SettingsResponse(
      {required final String key,
      required final dynamic value,
      final String type}) = _$SettingsResponseImpl;

  factory _SettingsResponse.fromJson(Map<String, dynamic> json) =
      _$SettingsResponseImpl.fromJson;

  @override
  String get key;
  @override
  dynamic get value; // String? id,
  @override
  String get type;

  /// Create a copy of SettingsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SettingsResponseImplCopyWith<_$SettingsResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
