import 'package:data/data.dart';
import 'package:either_dart/either.dart';
import 'package:froggytalk/app/app.dart';
import 'package:utils/utils.dart';

class FetchSettingsRepository {
  Future<
      Either<GenericErrorResponse,
          PaginatedResponse<List<SettingsResponse>>>> execute() async {
    try {
      final request = ApiRequest(
        route: '/v1/admin/settings',
        requestType: RequestType.get,
      );

      final response =
          await ApiClient.getInstance().executeRequest(request: request);

      if (response.isSuccess) {
        final data = response.collection<dynamic>();
        FroggyLogger.info('Settings Response Primary: $data');
        final paginatedResponse = PaginatedResponse<List<SettingsResponse>>(
          status: response.statusToString,
          message: response.successOrErrorMessage,
          data: data.map(
            (p01) {
              final p012 = p01 as Map<String, dynamic>;

              final settingsResponse = SettingsResponse(
                // id: p012['id'],
                key: p012['key'] as String,
                value: p012['value'],
                type: p012['type'] as String,
                // createdAt: p012['created_at'] as DateTime?,
                // updatedAt: p012['updated_at'] as DateTime?,
              );

              FroggyLogger.info(
                'Settings Response Internal: $settingsResponse',
              );

              return settingsResponse;
            },
          ).toList(),
        );

        FroggyLogger.info('Settings Response: $data');

        return Right(paginatedResponse);
      } else {
        FroggyLogger.error('Error Occurred: ${response.successOrErrorMessage}');

        return Left(
          GenericErrorResponse(
            message: response.successOrErrorMessage ?? 'Unknown error',
          ),
        );
      }
    } catch (e) {
      FroggyLogger.error('Failure Occurred: $e');

      return Left(GenericErrorResponse(message: e.toString()));
    }
  }
}
