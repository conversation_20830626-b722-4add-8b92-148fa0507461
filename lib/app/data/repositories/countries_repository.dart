import 'package:data/data.dart';
import 'package:either_dart/either.dart';
import 'package:froggytalk/app/app.dart';
import 'package:utils/utils.dart';

class CountriesRepository {
  Future<
      Either<GenericErrorResponse,
          PaginatedResponse<List<CountriesResponse>>>> execute({
    String? searchByCountryName,
    String? searchByCountryCode,
  }) async {
    try {
      var queryParameters = '';

      if (searchByCountryName != null && searchByCountryName.isNotEmpty) {
        queryParameters += 'filter[name]=$searchByCountryName';
      }

      if (searchByCountryCode != null && searchByCountryCode.isNotEmpty) {
        queryParameters += 'filter[code]=$searchByCountryCode';
      }

      final request = ApiRequest(
        route:
            '/countries?${queryParameters.isNotEmpty ? '?$queryParameters' : ''}',
        requestType: RequestType.get,
      );

      final response =
          await ApiClient.getInstance().executeRequest(request: request);

      if (response.isSuccess) {
        final paginatedResponse =
            PaginatedResponse<List<CountriesResponse>>.fromJson(
          response.data,
          (data) => (data! as List)
              .map((e) => CountriesResponse.fromJson(e as Map<String, dynamic>))
              .toList(),
        );

        FroggyLogger.info('Response: ${response.data}');

        return Right(paginatedResponse);
      } else {
        FroggyLogger.error('Error Occurred: ${response.successOrErrorMessage}');

        return Left(
          GenericErrorResponse(
            message: response.successOrErrorMessage ?? 'Unknown error',
          ),
        );
      }
    } catch (e) {
      FroggyLogger.error('Failure Occurred: $e');

      return Left(GenericErrorResponse(message: e.toString()));
    }
  }
}
