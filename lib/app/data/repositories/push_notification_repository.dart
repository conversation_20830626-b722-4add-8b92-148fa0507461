import 'package:data/data.dart';
import 'package:either_dart/either.dart';
import 'package:utils/utils.dart';

/// Repository for managing push notification token registration with the server
///
/// This repository handles sending push notification tokens to the backend
/// for user notification targeting and device registration.
///
/// **Features:**
/// - Sends token via PUT request for idempotent registration
/// - Handles Customer.io, OneSignal, and FCM tokens
/// - Provides comprehensive error handling
/// - Logs all operations for debugging
///
/// **Usage Example:**
/// ```dart
/// final repository = PushNotificationRepository();
/// final result = await repository.registerToken(
///   token: 'fcm_token_here',
///   platform: 'iOS',
///   userId: 'user_123',
/// );
/// ```
class PushNotificationRepository {
  /// Registers a push notification token with the server
  ///
  /// **Parameters:**
  /// - [token]: The push notification token (required)
  /// - [platform]: Device platform (iOS/Android) (required)
  /// - [deviceId]: Unique device identifier (optional)
  /// - [userId]: User identifier for token association (optional)
  /// - [appVersion]: Current app version (optional)
  /// - [tokenProvider]: Token source (customer_io, onesignal, fcm)
  ///
  /// **Returns:**
  /// - [Right(String)]: Success message from server
  /// - [Left(GenericErrorResponse)]: Error details if registration fails
  ///
  /// **HTTP Method:** PUT (idempotent for token updates)
  /// **Endpoint:** `/v1/mobile/push-notifications/tokens`
  ///
  /// **Error Handling:**
  /// - Network errors are caught and wrapped in GenericErrorResponse
  /// - Server errors (4xx, 5xx) are parsed and returned
  /// - All operations are logged for debugging
  Future<Either<GenericErrorResponse, String>> registerToken({
    required String token,
    required String platform,
    String? deviceId,
    String? userId,
    String? appVersion,
    String tokenProvider = 'customer_io',
  }) async {
    try {
      FroggyLogger.info(
        '[PushRepo] Registering push token for '
        '$platform with provider: $tokenProvider',
      );

      // Create the request body as a simple map since we don't expect response
      final body = {
        'token': token,
        'platform': platform,
        if (deviceId != null) 'device_id': deviceId,
        if (userId != null) 'user_id': userId,
        if (appVersion != null) 'app_version': appVersion,
        'token_provider': tokenProvider,
        'timestamp': DateTime.now().toIso8601String(),
      };

      // Create the API request with PUT method for idempotent operation
      final request = ApiRequest(
        route: '/v1/mobile/push-notifications/tokens',
        requestType: RequestType.put,
        params: body,
      );

      FroggyLogger.info(
        '[PushRepo] Sending PUT request to ${request.route} '
        'with token: ${token.substring(0, 20)}...',
      );

      // Execute the API request
      final response = await ApiClient.getInstance().executeRequest(
        request: request,
      );

      if (response.isSuccess) {
        final successMessage =
            response.successOrErrorMessage ?? 'Token registered successfully';

        FroggyLogger.info(
          '[PushRepo] Token registration successful: $successMessage',
        );

        return Right(successMessage);
      } else {
        final errorMessage =
            response.successOrErrorMessage ?? 'Token registration failed';

        FroggyLogger.error(
          '[PushRepo] Token registration failed: $errorMessage',
        );

        return Left(
          GenericErrorResponse(
            message: errorMessage,
          ),
        );
      }
    } catch (e) {
      FroggyLogger.error(
        '[PushRepo] Exception during token registration: $e',
      );

      return Left(
        GenericErrorResponse(
          message: 'Network error during token registration: $e',
        ),
      );
    }
  }

  /// Updates an existing push notification token
  ///
  /// This is an alias for [registerToken] since PUT requests are idempotent
  /// and handle both create and update operations.
  ///
  /// **Parameters:** Same as [registerToken]
  /// **Returns:** Same as [registerToken]
  Future<Either<GenericErrorResponse, String>> updateToken({
    required String token,
    required String platform,
    String? deviceId,
    String? userId,
    String? appVersion,
    String tokenProvider = 'customer_io',
  }) async {
    FroggyLogger.info('[PushRepo] Updating push token (alias for register)');

    return registerToken(
      token: token,
      platform: platform,
      deviceId: deviceId,
      userId: userId,
      appVersion: appVersion,
      tokenProvider: tokenProvider,
    );
  }

  /// Removes a push notification token from the server
  ///
  /// **Parameters:**
  /// - [token]: The push notification token to remove
  /// - [platform]: Device platform (iOS/Android)
  /// - [userId]: User identifier (optional)
  ///
  /// **Returns:**
  /// - [Right(String)]: Success message from server
  /// - [Left(GenericErrorResponse)]: Error details if removal fails
  ///
  /// **HTTP Method:** DELETE
  /// **Endpoint:** `/v1/mobile/push-notifications/tokens/{token}`
  Future<Either<GenericErrorResponse, String>> removeToken({
    required String token,
    required String platform,
    String? userId,
  }) async {
    try {
      FroggyLogger.info(
        '[PushRepo] Removing push token for $platform',
      );

      // Create the API request with DELETE method
      final request = ApiRequest(
        route: '/v1/mobile/push-notifications/tokens/$token',
        requestType: RequestType.delete,
        params: {
          'platform': platform,
          if (userId != null) 'user_id': userId,
        },
      );

      FroggyLogger.info(
        '[PushRepo] Sending DELETE request to ${request.route}',
      );

      // Execute the API request
      final response = await ApiClient.getInstance().executeRequest(
        request: request,
      );

      if (response.isSuccess) {
        final successMessage =
            response.successOrErrorMessage ?? 'Token removed successfully';

        FroggyLogger.info(
          '[PushRepo] Token removal successful: $successMessage',
        );

        return Right(successMessage);
      } else {
        final errorMessage =
            response.successOrErrorMessage ?? 'Token removal failed';

        FroggyLogger.error(
          '[PushRepo] Token removal failed: $errorMessage',
        );

        return Left(
          GenericErrorResponse(
            message: errorMessage,
          ),
        );
      }
    } catch (e) {
      FroggyLogger.error(
        '[PushRepo] Exception during token removal: $e',
      );

      return Left(
        GenericErrorResponse(
          message: 'Network error during token removal: $e',
        ),
      );
    }
  }
}
