import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:froggytalk/buy_credit/buy_credit.dart';
import 'package:froggytalk/call_rates/call_rates.dart';
import 'package:froggytalk/dialer/dialer.dart';
import 'package:froggytalk/home/<USER>';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:froggytalk/language/language.dart';
import 'package:froggytalk/more/more.dart';
import 'package:froggytalk/notifications/notifications.dart';
import 'package:froggytalk/onboarding/onboarding.dart';
import 'package:froggytalk/profile/profile.dart';
import 'package:froggytalk/referral/referral.dart';

/// Returns a list of navigation items with their respective icons and labels.
///
/// Each item in the list is a Map containing:
/// - 'icon': String identifier for the navigation item's icon
/// - 'label': Localized string for the navigation item's label
///
/// Uses the current context's l10n to provide localized labels for:
/// - Home
/// - Rates
/// - Keypad
/// - More
List<Map<String, String>> useNavigationItems() {
  final l10n = useContext().l10n;

  return [
    {
      'icon': 'ic_home',
      'label': l10n.homeNavigationBarText,
    },
    {
      'icon': 'ic_rates',
      'label': l10n.ratesNavigationBarText,
    },
    {
      'icon': 'ic_keypad',
      'label': l10n.keypadNavigationBarText,
    },
    {
      'icon': 'ic_more',
      'label': l10n.moreNavigationBarText,
    },
  ];
}

class AppConfigurationRepository {
  AppConfigurationRepository._();

  static final AppConfigurationRepository instance =
      AppConfigurationRepository._();

  // Returns the navigation destinations for the app.
  // The navigation destinations are used to navigate to the specified page.
  static List<Widget> get getNavigationDestinations {
    return [
      const HomePage(),
      const CallRatesPage(),
      const DialerPage(),
      MorePage(),
    ];
  }

  // Returns the app bar widgets for each destination.
  // The app bar widgets are used to display the app bar for each destination.
  static List<PreferredSizeWidget?> get getNavigationDestinationsAppBars {
    return [
      const HomePageAppBar(),
      const CallRatesPageAppBar(),
      const DialerPageAppBar(),
      const MorePageAppBar(),
    ];
  }

  /// Returns the file path of the app configuration repository.
  ///
  /// The file path is used to locate the app_configuration_repository.dart file
  /// in the specified directory.
  ///
  /// Returns:
  ///   A string representing the file path of the app configuration repository.
  //
  static Map<String, Widget Function(BuildContext)> get getRoutesConfiguration {
    return {
      HomePage.routeName: (_) => const HomePage(),
      CallRatesPage.routeName: (_) => const CallRatesPage(),
      DialerPage.routeName: (_) => const DialerPage(),
      MorePage.routeName: (_) => MorePage(),
      AppDashboardPage.routeName: (_) => const AppDashboardPage(),
      SendOtpPage.routeName: (_) => const SendOtpPage(),
      OnboardingPage.routeName: (_) => const OnboardingPage(),
      BuyCreditPage.routeName: (_) => const BuyCreditPage(),
      // RadioPage.routeName: (_) => const RadioPage(),
      FreeCreditPage.routeName: (_) => const FreeCreditPage(),
      EditProfilePage.routeName: (_) => const EditProfilePage(),
      HelpCenterPage.routeName: (_) => const HelpCenterPage(),
      LanguagePage.routeName: (_) => const LanguagePage(),
      NotificationPage.routeName: (_) => const NotificationPage(),
    };
  }
}
