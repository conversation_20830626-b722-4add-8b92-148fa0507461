import 'dart:io';

import 'package:customer_io/customer_io.dart';
import 'package:facebook_app_events/facebook_app_events.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:froggytalk/shared/services/passkey_manager.dart';
import 'package:froggytalk/shared/services/purchase_attribution_service.dart';
import 'package:mixpanel_flutter/mixpanel_flutter.dart';
// import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:singular_flutter_sdk/singular.dart';
import 'package:utils/utils.dart';

class EventTrackerService {
  // Factory constructor to provide the instance
  factory EventTrackerService() {
    return _instance;
  }
  EventTrackerService._();

  Mixpanel? _mixpanel;
  FacebookAppEvents? _facebookAppEvents;
  final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;
  final CustomerIO _customerIO = CustomerIO.instance;
  final PassKeyManager _passKeyManager = PassKeyManager.instance;
  // OneSignal? _oneSignal;

  Future<void> initialize({
    required Mixpanel mixpanel,
    required FacebookAppEvents facebookAppEvents,
  }) async {
    _mixpanel = mixpanel;
    _facebookAppEvents = facebookAppEvents;

    // Initialize the passkey manager
    await _passKeyManager.initialize();

    // If a passkey is available, set it for Singular
    final passKey = _passKeyManager.getPassKey();
    if (passKey != null && passKey.isNotEmpty) {
      Singular.setCustomUserId(passKey);
      FroggyLogger.info('Restored passkey for tracking: $passKey');
    }
  }

  // Static instance of the singleton
  static final EventTrackerService _instance = EventTrackerService._();

  static EventTrackerService getInstance() => _instance;

  // on user logs in
  /// Identifies a user across multiple analytics platforms
  ///
  /// This method is called when a user logs in and establishes the user's
  /// identity across all integrated analytics and tracking services.
  ///
  /// @param userId The unique identifier for the user
  /// @param passKey The user's passkey for consistent tracking
  Future<void> identify({required String userId, String? passKey}) async {
    // Store the passkey if provided
    if (passKey != null && passKey.isNotEmpty) {
      await _passKeyManager.setPassKey(passKey);
      FroggyLogger.info('Stored passkey for user: $passKey');
    }

    // Get the passkey (either the one just set or previously stored)
    final trackingId = _passKeyManager.getPassKey() ?? userId;

    // Identify user in Mixpanel
    await _instance._mixpanel!.identify(userId);

    // Set user ID for Facebook App Events
    await _instance._facebookAppEvents!.setUserID(trackingId);

    // Set user ID for Firebase Analytics
    await _analytics.setUserId(id: userId);

    // Set user identifier for Firebase Crashlytics
    await FirebaseCrashlytics.instance.setUserIdentifier(userId);

    // Set custom user ID for Singular - using passkey for better attribution
    Singular.setCustomUserId(trackingId);
    FroggyLogger.info('Set Singular tracking ID: $trackingId');

    // Identify user in CustomerIO with basic identification
    // Note: Full profile information should be set in PushNotificationService
    _customerIO.identify(
      userId: userId,
      traits: {
        'identified_at': DateTime.now().toIso8601String(),
        'platform': Platform.operatingSystem,
        'tracking_id': trackingId,
      },
    );

    // Track the identification event
    await logEvent(
      schema: 'user_identified',
      description: 'User has been identified across analytics services',
      data: {'user_id': userId},
    );
  }

  /// Handles user logout across all analytics platforms
  ///
  /// This method should be called whenever a user logs out to ensure
  /// that their session is properly ended in all tracking systems.
  Future<void> onUserLogsOut() async {
    // Track the logout event before clearing identity data
    await logEvent(
      schema: 'User_Logged_Out',
      description: 'User Has Logged Out',
    );

    // Clear the passkey from storage
    await _passKeyManager.clearPassKey();

    // Clear Singular identity
    Singular.unsetCustomUserId();

    // Clear user identity from Customer.io
    // This ensures new events won't be associated with the logged out user
    try {
      // Track a final event for this user before clearing identity
      _customerIO
        ..track(
          name: 'user_session_completed',
          properties: {
            'session_end_time': DateTime.now().toIso8601String(),
            'logout_type': 'user_initiated',
          },
        )

        // Clear Customer.io identity
        ..clearIdentify();
    } catch (e) {
      // Log any errors during logout process
      FroggyLogger.error('Error during Customer.io logout: $e');
    }
  }

  Future<void> logEvent({
    required String schema,
    required String description,
    Map<String, dynamic>? data,
  }) async {
    final schemaName = schema.replaceAll(' ', '_').toLowerCase();

    await getInstance()._mixpanel?.track(
      schemaName,
      properties: {
        'schema': schemaName,
        'description': description,
        ...?data,
      },
    );

    await getInstance()._facebookAppEvents?.logEvent(
      name: schemaName,
      parameters: {
        'schema': schemaName,
        'description': description,
        ...?data,
      },
    );

    Singular.eventWithArgs(schema, {
      'schema': schemaName,
      'description': description,
      ...?data,
    });

    await _analytics.logEvent(
      name: schemaName,
      parameters: data?.cast<String, Object>(),
    );
    // Handle different data types for Firebase Analytics
    Map<String, Object>? firebaseParams;
    if (data != null) {
      firebaseParams = data.cast<String, Object>();
    }

    await _analytics.logEvent(
      name: schemaName,
      parameters: firebaseParams,
    );
    await _analytics.logScreenView(
      screenName: schema,
    );

    // Enhanced Customer.io event tracking with additional context
    try {
      final appVersion = await getAppVersion();

      _customerIO.track(
        name: schema,
        properties: {
          'schema': schemaName,
          'description': description,
          'timestamp': DateTime.now().toIso8601String(),
          'platform': Platform.operatingSystem,
          'app_version': appVersion,
          'event_id': '${schemaName}_${DateTime.now().millisecondsSinceEpoch}',
          ...?data,
        },
      );
    } catch (e) {
      // Fallback if getting app version fails
      _customerIO.track(
        name: schema,
        properties: {
          'schema': schemaName,
          'description': description,
          'timestamp': DateTime.now().toIso8601String(),
          'platform': Platform.operatingSystem,
          'event_id': '${schemaName}_${DateTime.now().millisecondsSinceEpoch}',
          ...?data,
        },
      );
    }

    // final token2 = OneSignal.User.pushSubscription.token;
    // if (token2 != null) {
    //   await OneSignal.User.addTagWithKey(schemaName, description);
    // }
  }

  Future<void> onSelectedBuyCreditOption({
    required String name,
    required double price,
    required String currency,
    required String sessionId,
    String? couponCode,
    dynamic data,
  }) async {
    Singular.eventWithArgs('selected_buy_credit_option', {
      'schema': 'selected_buy_credit_option',
      'description': 'User selected a buy credit option',
    });

    await getInstance()._facebookAppEvents?.logAddToCart(
          id: sessionId,
          type: 'buy_credit',
          currency: currency,
          price: price,
        );

    await _analytics.logRemoveFromCart();

    await _analytics.logAddToCart(
      value: price,
      currency: currency,
      items: [
        AnalyticsEventItem(
          itemName: name,
          itemId: sessionId,
          price: price,
        ),
      ],
      parameters: data as Map<String, Object>?,
    );

    _customerIO.track(
      name: 'add-to-cart',
      properties: {
        'schema': 'add-to-cart',
        'description': 'User selected a buy credit option',
        'name': name,
        'price': price,
        'currency': currency,
        'sessionId': sessionId,
        'couponCode': couponCode,
      },
    );
  }

  Future<void> onBuyCreditButtonClick(
    String message, {
    dynamic data,
  }) async {}

  Future<void> beginCheckout({
    required String name,
    required double price,
    required String currency,
    required String sessionId,
    String? couponCode,
    dynamic data,
  }) async {
    await _analytics.logBeginCheckout(
      value: price,
      currency: currency,
      items: [
        AnalyticsEventItem(
          itemName: name,
          itemId: sessionId,
          price: price,
        ),
      ],
      coupon: couponCode,
      parameters: data as Map<String, Object>?,
    );

    await getInstance()._facebookAppEvents?.logInitiatedCheckout(
          contentId: sessionId,
          numItems: 1,
          contentType: 'buy_credit',
          currency: currency,
          totalPrice: price,
        );

    _customerIO.track(
      name: 'begin-checkout',
      properties: {
        'schema': 'begin-checkout',
        'description': 'User started the checkout process',
        'name': name,
        'price': price,
        'currency': currency,
        'sessionId': sessionId,
        'couponCode': couponCode,
      },
    );

    // await _analytics.logSelectItem()
  }

  // on credit purchase success
  Future<void> onCreditPurchaseSuccess({
    required String name,
    required double price,
    required String currency,
    required String sessionId,
    String? couponCode,
    dynamic data,
  }) async {
    // Use PurchaseAttributionService for consistent purchase tracking
    // across all platforms
    final attributionService = PurchaseAttributionService();
    await attributionService.trackPurchase(
      eventName: 'purchase_successful',
      productId: name,
      price: price,
      currency: currency,
      transactionId: sessionId,
      couponCode: couponCode,
      additionalParams: data is Map<String, dynamic> ? data : null,
    );

    // Track with Customer.io separately since it's not in the
    // attribution service
    _customerIO.track(
      name: 'purchase_successful',
      properties: {
        'schema': 'purchase_successful',
        'description': 'User Purchase Successful',
        'name': name,
        'price': price,
        'currency': currency,
        'sessionId': sessionId,
        'couponCode': couponCode,
      },
    );
  }

  // on credit purchase failure
  Future<void> onCreditPurchaseFailure({
    required String name,
    required double price,
    required String currency,
    required String sessionId,
  }) async {
    await logEvent(
      schema: 'purchase_failed',
      description: 'User Purchase Failed',
    );
  }
}
