// ignore_for_file: avoid_print

import 'dart:io';

import 'package:utils/utils.dart';

/// Flutter service for integrating with iOS CallKit
/// Provides proper VoIP call handling on iOS with system integration
/// 
/// TEMPORARILY DISABLED: CallKit integration is disabled until 
/// CallKitService.swift is properly added to the Xcode project
class CallKitService {

  /// Singleton instance
  factory CallKitService() {
    _instance ??= CallKitService._internal();
    return _instance!;
  }

  CallKitService._internal();
  
  static CallKitService? _instance;

  /// Callback functions for CallKit events (stubbed)
  void Function(String uuid)? onCallAnswered;
  void Function(String uuid)? onCallEnded;
  void Function(String uuid)? onCallStarted;
  void Function(String uuid, bool onHold)? onCallHoldChanged;
  void Function(String uuid, bool muted)? onCallMuteChanged;
  void Function()? onProviderReset;
  void Function(String uuid, String handle)? onShouldStartCall;
  void Function(String uuid)? onShouldAnswerCall;
  void Function(String uuid)? onShouldEndCall;
  void Function(String uuid, bool onHold)? onShouldSetCallHold;
  void Function(String uuid, bool muted)? onShouldSetCallMute;
  void Function()? onAudioSessionActivated;
  void Function()? onAudioSessionDeactivated;

  /// Report an incoming call (stubbed)
  Future<void> reportIncomingCall({
    required String uuid,
    required String phoneNumber,
    String? contactName,
    bool hasVideo = false,
  }) async {
    if (!Platform.isIOS) return;
    
    FroggyLogger.warning(
      '[CallKitService] STUBBED: reportIncomingCall called but '
      'CallKit is temporarily disabled',
    );
  }

  /// Start an outgoing call (stubbed)
  Future<void> startOutgoingCall({
    required String uuid,
    required String phoneNumber,
    String? contactName,
    bool hasVideo = false,
  }) async {
    if (!Platform.isIOS) return;
    
    FroggyLogger.warning(
      '[CallKitService] STUBBED: startOutgoingCall called but '
      'CallKit is temporarily disabled',
    );
  }

  /// Answer a call (stubbed)
  Future<void> answerCall(String uuid) async {
    if (!Platform.isIOS) return;
    
    FroggyLogger.warning(
      '[CallKitService] STUBBED: answerCall called but '
      'CallKit is temporarily disabled',
    );
  }

  /// End a call (stubbed)
  Future<void> endCall(String uuid) async {
    if (!Platform.isIOS) return;
    
    FroggyLogger.warning(
      '[CallKitService] STUBBED: endCall called but '
      'CallKit is temporarily disabled',
    );
  }

  /// Set call on hold status (stubbed)
  Future<void> setCallOnHold(String uuid, {required bool onHold}) async {
    if (!Platform.isIOS) return;
    
    FroggyLogger.warning(
      '[CallKitService] STUBBED: setCallOnHold called but '
      'CallKit is temporarily disabled',
    );
  }

  /// Set call muted status (stubbed)
  Future<void> setCallMuted(String uuid, {required bool muted}) async {
    if (!Platform.isIOS) return;
    
    FroggyLogger.warning(
      '[CallKitService] STUBBED: setCallMuted called but '
      'CallKit is temporarily disabled',
    );
  }

  /// Report call as connected (stubbed)
  Future<void> reportCallConnected(String uuid) async {
    if (!Platform.isIOS) return;
    
    FroggyLogger.warning(
      '[CallKitService] STUBBED: reportCallConnected called but '
      'CallKit is temporarily disabled',
    );
  }

  /// Check if CallKit is available (always returns false while stubbed)
  bool get isCallKitAvailable => false;
}
