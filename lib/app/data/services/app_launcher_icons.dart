import 'package:flutter/services.dart';
import 'package:flutter_dynamic_icon_plus/flutter_dynamic_icon_plus.dart';
import 'package:utils/utils.dart';

class FroggyLauncherIcon {
  Future<void> initialize() async {
    try {
      if (await FlutterDynamicIconPlus.supportsAlternateIcons) {
        await FlutterDynamicIconPlus.setAlternateIconName(
          iconName: 'icon_name',
          blacklistBrands: ['Redmi'],
          blacklistManufactures: ['Xiaomi'],
          blacklistModels: ['Redmi 200A'],
        );
        FroggyLogger.info('App icon change successful');
        return;
      }
    } on PlatformException {
      /// Error
    } catch (e) {
      /// Error handling
    }
  }

  Future<void> setBadgeNumber(int badgeNumber) async {
    try {
      await FlutterDynamicIconPlus.setApplicationIconBadgeNumber(badgeNumber);
      FroggyLogger.info('Badge number set successful');
    } on PlatformException {
      /// Error
    } catch (e) {
      /// Error handling
    }
  }

  // get current badge number
  Future<int?> getBadgeNumber() async {
    try {
      final badgeNumber =
          await FlutterDynamicIconPlus.applicationIconBadgeNumber;
      FroggyLogger.info('Current badge number: $badgeNumber');
      return badgeNumber;
    } on PlatformException {
      /// Error
    } catch (e) {
      /// Error handling
    }
    return null;
  }

  // get current icon
  Future<String?> getIconName() async {
    try {
      final iconName = await FlutterDynamicIconPlus.alternateIconName;
      FroggyLogger.info('Current icon name: $iconName');
      return iconName;
    } on PlatformException {
      /// Error
    } catch (e) {
      /// Error handling
    }
    return null;
  }
}
