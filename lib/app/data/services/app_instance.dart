import 'package:froggytalk/app/app.dart';
import 'package:seeip_client/seeip_client.dart';

class AppInstance {
  // Factory method to return the singleton instance
  factory AppInstance({
    required String version,
    required String baseApiUrl,
    required String baseApiVersion,
    required String buildNumber,
    EnvironmentType environmentType = EnvironmentType.development,
    EnvironmentType flavour = EnvironmentType.development,
    String? sipUri,
    String? sipWebsocketUrl,
    String? sipUserAgent,
    GeoIP? geoIpInfo,
  }) {
    // Ensure only one instance is created
    _instance ??= AppInstance._internal(
      environmentType: environmentType,
      flavour: flavour,
      version: version,
      baseApiUrl: baseApiUrl,
      baseApiVersion: baseApiVersion,
      sipUri: sipUri,
      sipWebsocketUrl: sipWebsocketUrl,
      sipUserAgent: sipUserAgent,
      geoIpInfo: geoIpInfo,
      buildNumber: buildNumber,
    );

    return _instance!;
  }

  // Private constructor
  AppInstance._internal({
    required this.environmentType,
    required this.flavour,
    required this.version,
    required this.buildNumber,
    required this.baseApiUrl,
    required this.baseApiVersion,
    this.sipUri,
    this.sipWebsocketUrl,
    this.sipUserAgent,
    GeoIP? geoIpInfo,
  }) : _geoIpInfo = geoIpInfo;

  // Properties
  EnvironmentType environmentType;
  EnvironmentType flavour;
  final String version;
  final String buildNumber;
  final String baseApiUrl;
  final String baseApiVersion;
  final String? sipUri;
  final String? sipWebsocketUrl;
  final String? sipUserAgent;
  GeoIP? _geoIpInfo;

  // Private static variable to hold the singleton instance
  static AppInstance? _instance;

  // Example of using the class in a main function
  static void initialize({
    required String version,
    required String baseApiUrl,
    required String baseApiVersion,
    EnvironmentType environmentType = EnvironmentType.development,
    EnvironmentType flavour = EnvironmentType.development,
    String? sipUri,
    String? sipWebsocketUrl,
    String? sipUserAgent,
    String? buildNumber,
  }) {
    if (_instance == null) {
      _instance = AppInstance(
        environmentType: environmentType,
        flavour: flavour,
        version: version,
        baseApiUrl: baseApiUrl,
        baseApiVersion: baseApiVersion,
        sipUri: sipUri,
        sipWebsocketUrl: sipWebsocketUrl,
        sipUserAgent: sipUserAgent,
        buildNumber: buildNumber ?? '',
      );
    } else {
      throw Exception('AppInstance has already been initialized.');
    }
  }

  // Method to get the instance (if already initialized)
  static AppInstance getInstance() {
    if (_instance == null) {
      throw Exception(
        'AppInstance is not initialized. Call initialize() first.',
      );
    }
    return _instance!;
  }

  // Method to get the base API URL
  String get getApiUrl {
    // return '$baseApiUrl/$baseApiVersion';
    return baseApiUrl;
  }

  // set the development environment type
  void setDevelopmentEnv() {
    _instance!.environmentType = EnvironmentType.development;
    _instance!.flavour = EnvironmentType.development;
  }

  // set the staging environment type
  void setStagingEnv() {
    _instance!.environmentType = EnvironmentType.staging;
    _instance!.flavour = EnvironmentType.staging;
  }

  // set the production environment type
  void setProductionEnv() {
    _instance!.environmentType = EnvironmentType.production;
    _instance!.flavour = EnvironmentType.production;
  }

  set geoIPInfo(GeoIP geoIpInfo) {
    _instance!._geoIpInfo = geoIpInfo;
  }

  GeoIP get geoIPInfo {
    final instanz = _instance;
    if (instanz != null) {
      final geo = instanz._geoIpInfo;
      if (geo != null) {
        return _instance!._geoIpInfo!;
      }
    }

    return GeoIP();
  }
}
