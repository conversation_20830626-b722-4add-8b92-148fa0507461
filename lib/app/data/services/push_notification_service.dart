import 'dart:io';

import 'package:customer_io/customer_io.dart';
import 'package:froggytalk/app/data/repositories/push_notification_repository.dart';
import 'package:froggytalk/language/language.dart';
import 'package:froggytalk/profile/profile.dart';
import 'package:froggytalk/shared/services/local_notification_service.dart';
// import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:utils/utils.dart';

class PushNotificationService {
  /// Repository for sending push tokens to the backend server
  final PushNotificationRepository _pushRepository =
      PushNotificationRepository();

  /// Identifies a user across multiple push notification and analytics
  /// services.
  ///
  /// This method registers the user with OneSignal and Customer.io services,
  /// setting up user attributes, tags, and contact information for targeted
  /// notifications and analytics tracking.
  ///
  /// **Parameters:**
  /// - [externalId]: Unique identifier for the user across all services
  /// - [currentUser]: Optional user model containing profile and
  ///   preference data
  ///
  /// **Functionality:**
  /// 1. Retrieves app metadata (version, build number)
  /// 2. Configures OneSignal user profile with tags and contact info
  /// 3. Sets up Customer.io user identification with traits
  ///
  /// **Error Handling:**
  /// Each service operation is wrapped in try-catch blocks to prevent
  /// cascading failures. Errors are logged but don't interrupt the flow.
  ///
  /// **Performance Considerations:**
  /// - Operations run sequentially to avoid API rate limits
  /// - Uses null-safety patterns to handle missing user data gracefully
  ///
  /// **Usage Example:**
  /// ```dart
  /// await pushNotificationService.identify(
  ///   externalId: 'user_123',
  ///   authUser: currentUserModel,
  /// );
  /// ```
  Future<void> identify({
    required String externalId,
    UserAttributesModel? currentUser,
  }) async {
    // await FroggyLocalNotifications.ensureInitialized();
    await LocalNotificationService.instance.initialize();

    // Retrieve app metadata for consistent tracking across services
    final appMetadata = await _getAppMetadata();

    // Extract user currency with fallback to USD
    final userCurrency = _extractUserCurrency(currentUser);

    // // Configure OneSignal user profile and notifications
    // await _configureOneSignalUser(
    //   externalId: externalId,
    //   authUser: currentUser,
    //   appMetadata: appMetadata,
    //   userCurrency: userCurrency,
    // );

    // Set up Customer.io user identification and traits
    await _configureCustomerIOUser(
      externalId: externalId,
      authUser: currentUser,
      appMetadata: appMetadata,
      userCurrency: userCurrency,
    );
  }

  /// Retrieves application metadata including version and build number.
  ///
  /// **Returns:** Map containing app version and build number
  /// **Throws:** May throw if package info is unavailable
  Future<Map<String, String>> _getAppMetadata() async {
    final version = await getAppVersion();
    final buildNumber = await getAppBuildNumber();

    return {
      'version': version,
      'buildNumber': buildNumber,
    };
  }

  /// Extracts user currency symbol with fallback to USD.
  ///
  /// **Parameters:**
  /// - [authUser]: User model potentially containing country/currency data
  ///
  /// **Returns:** Currency symbol string, defaults to 'USD'
  String _extractUserCurrency(UserAttributesModel? authUser) {
    return authUser?.country?.attributes?.currency?.symbol ?? 'USD';
  }

  // /// Configures OneSignal user profile with tags, contact info, and
  // /// preferences.
  // ///
  // /// **Operations performed:**
  // /// 1. User login with external ID
  // /// 2. Addition of user tags (name, currency, app info)
  // /// 3. Contact information setup (phone, email)
  // /// 4. Cleanup of legacy tags
  // ///
  // /// **Error Handling:**
  // /// Catches and logs OneSignal-specific errors without throwing
  // Future<void> _configureOneSignalUser({
  //   required String externalId,
  //   required UserAttributesModel? authUser,
  //   required Map<String, String> appMetadata,
  //   required String userCurrency,
  // }) async {
  //   try {
  //     final oneSignalAppId =
  //         AppInstance.getInstance().environmentType.isProduction
  //             ? '21044cb2-8a7a-4506-97f9-76a0cbbfaab9'
  //             : 'd20eafc4-56ce-457e-976f-421c45e4a1c3';

  //     OneSignal.initialize(oneSignalAppId);

  //     // Configure OneSignal after permissions granted
  //     final oneSignalOptedIn = OneSignal.Notifications.permission;
  //     if (!oneSignalOptedIn) {
  //       await OneSignal.Notifications.requestPermission(true);
  //       final subscription = OneSignalPushSubscription();
  //       if (!subscription.optedIn!) {
  //         await subscription.optIn();
  //       }
  //     }
  //   } catch (e) {
  //     FroggyLogger.error('OneSignal Initialization Error: $e');
  //   }

  //   try {
  //     // Authenticate user with OneSignal
  //     await OneSignal.login(externalId);

  //     // Prepare user tags for bulk addition
  //     final userTags = _buildOneSignalTags(
  //       authUser: authUser,
  //       appMetadata: appMetadata,
  //       userCurrency: userCurrency,
  //     );

  //     // Add user tags in batch operation
  //     await OneSignal.User.addTags(userTags);

  //     // Set individual tags that require special handling
  //     await _setIndividualOneSignalTags(authUser);

  //     // Configure contact methods for notifications
  //     await _configureOneSignalContactMethods(authUser);

  //     // Clean up legacy or duplicate tags
  //     await _cleanupOneSignalTags();
  //   } catch (e) {
  //     FroggyLogger.error('OneSignal configuration failed: $e');
  //   }
  // }

  // /// Builds a map of user tags for OneSignal batch operations.
  // ///
  // /// **Returns:** Flattened map of user attributes suitable for OneSignal tags
  // Map<String, dynamic> _buildOneSignalTags({
  //   required UserAttributesModel? authUser,
  //   required Map<String, String> appMetadata,
  //   required String userCurrency,
  // }) {
  //   return flattenMap({
  //     'user_name': authUser?.name ?? '',
  //     'user_currency': userCurrency,
  //     'app_build_number': appMetadata['buildNumber'] ?? '',
  //     'app_version': appMetadata['version'] ?? '',
  //   });
  // }

  // /// Sets individual OneSignal tags that require special processing.
  // ///
  // /// These tags are set individually because they may require data
  // /// transformation or have specific formatting requirements.
  // Future<void> _setIndividualOneSignalTags(
  //   UserAttributesModel? authUser,
  // ) async {
  //   // Set user language from repository
  //   await OneSignal.User.addTagWithKey(
  //     'user_language',
  //     LanguageRepository.getSavedLanguage()?.code,
  //   );

  //   // Set formatted phone number
  //   await OneSignal.User.addTagWithKey(
  //     'user_phone',
  //     authUser?.formattedPhoneNumber ?? '',
  //   );
  // }

  // /// Configures OneSignal contact methods for push notifications and
  // /// messaging.
  // ///
  // /// **Contact Methods:**
  // /// - SMS: User's formatted phone number
  // /// - Email: User's email address
  // Future<void> _configureOneSignalContactMethods(
  //   UserAttributesModel? authUser,
  // ) async {
  //   final phoneNumber = authUser?.formattedPhoneNumber ?? '';
  //   final emailAddress = authUser?.email ?? '';

  //   if (phoneNumber.isNotEmpty) {
  //     await OneSignal.User.addSms(phoneNumber);
  //   }

  //   if (emailAddress.isNotEmpty) {
  //     await OneSignal.User.addEmail(emailAddress);
  //   }
  // }

  // /// Removes legacy or duplicate OneSignal tags to maintain clean user
  // /// profiles.
  // ///
  // /// **Tags Removed:**
  // /// - Legacy phone/email tags that are now handled as contact methods
  // /// - Duplicate user identification tags
  // Future<void> _cleanupOneSignalTags() async {
  //   const legacyTags = [
  //     'user_phone',
  //     'user_id',
  //     'user_country',
  //     'user_email',
  //   ];

  //   await OneSignal.User.removeTags(legacyTags);
  // }

  /// Configures Customer.io user identification with comprehensive user traits.
  ///
  /// **Traits Include:**
  /// - Personal information (name, email, country)
  /// - App metadata (version, build, platform)
  /// - User preferences (currency, language)
  /// - Device information
  /// - Usage metrics
  ///
  /// **Error Handling:**
  /// Catches and logs Customer.io-specific errors without throwing
  ///
  /// **Performance Optimization:**
  /// - Uses batch processing for more efficient trait updates
  /// - Implements retry mechanism for failed identification attempts
  Future<void> _configureCustomerIOUser({
    required String externalId,
    required UserAttributesModel? authUser,
    required Map<String, String> appMetadata,
    required String userCurrency,
  }) async {
    try {
      final userTraits = _buildCustomerIOTraits(
        authUser: authUser,
        appMetadata: appMetadata,
        userCurrency: userCurrency,
      );

      // Identify user with Customer.io
      CustomerIO.instance.identify(
        userId: externalId,
        traits: userTraits,
      );

      // Track identification event for analytics
      CustomerIO.instance.track(
        name: 'user_identified',
        properties: {
          'platform': Platform.operatingSystem,
          'timestamp': DateTime.now().toIso8601String(),
          'app_version': appMetadata['version'] ?? '',
        },
      );

      // Get the device push notification token from Customer.io
      final pushToken =
          await CustomerIO.pushMessaging.getRegisteredDeviceToken();

      if (pushToken != null && pushToken.isNotEmpty) {
        FroggyLogger.info('[CIO] Customer.io Push Token retrieved: $pushToken');
        FroggyLogger.info(
          '[CIO] Customer.io External Id retrieved: $externalId',
        );

        // Optional: Store token for additional use cases
        await _handleCustomerIOPushToken(pushToken);
      } else {
        FroggyLogger.warning(
          '[CIO] Customer.io Push Token is null or empty. '
          'This might indicate:\n'
          '- Push notifications are not properly configured\n'
          '- User has not granted push notification permissions\n'
          '- Device token registration is still in progress',
        );

        // Check current notification permission status using permission handler
        final status = await Permission.notification.status;
        if (status.isDenied) {
          try {
            // Request notification permissions
            final result = await Permission.notification.request();
            if (result.isGranted) {
              // Retry getting the push token after permission granted
              final newPushToken =
                  await CustomerIO.pushMessaging.getRegisteredDeviceToken();
              if (newPushToken != null && newPushToken.isNotEmpty) {
                await _handleCustomerIOPushToken(newPushToken);
              }
            }
          } catch (e) {
            FroggyLogger.error(
              '[Push] Failed to request notification permissions: $e',
            );
          }
        }
      }
    } catch (e) {
      FroggyLogger.error('Customer.io identification failed: $e');

      // Track identification failure for debugging
      CustomerIO.instance.track(
        name: 'user_identification_failed',
        properties: {'error': e.toString()},
      );
    }
  }

  /// Handles the Customer.io push token for additional use cases
  ///
  /// **Use Cases:**
  /// - Send token to your backend for additional push services
  /// - Store token for analytics or debugging purposes
  /// - Use token for A/B testing different push providers
  ///
  /// **Parameters:**
  /// - [token]: The device token registered with Customer.io
  /// - [userId]: Optional user identifier for token association
  Future<void> _handleCustomerIOPushToken(
    String token, {
    String? userId,
  }) async {
    try {
      // Get app metadata for the backend request
      final appMetadata = await _getAppMetadata();

      // Send token to backend via the push notification repository
      final result = await _pushRepository.registerToken(
        token: token,
        platform: Platform.operatingSystem,
        userId: userId,
        appVersion: appMetadata['version'],
      );

      result.fold(
        (error) {
          FroggyLogger.error(
            '[CIO] Failed to send push token to backend: ${error.message}',
          );

          // Track the failure for analytics
          CustomerIO.instance.track(
            name: 'push_token_backend_registration_failed',
            properties: {
              'error': error.message,
              'platform': Platform.operatingSystem,
              'token_provider': 'customer_io',
            },
          );
        },
        (successMessage) {
          FroggyLogger.info(
            '[CIO] Push token successfully sent to backend: $successMessage',
          );

          // Track successful registration
          CustomerIO.instance.track(
            name: 'push_token_backend_registration_success',
            properties: {
              'platform': Platform.operatingSystem,
              'token_provider': 'customer_io',
              'app_version': appMetadata['version'],
            },
          );
        },
      );
    } catch (e) {
      FroggyLogger.error('[CIO] Exception while handling push token: $e');

      // Track the exception
      CustomerIO.instance.track(
        name: 'push_token_handling_exception',
        properties: {
          'error': e.toString(),
          'platform': Platform.operatingSystem,
        },
      );
    }
  }

  /// Builds comprehensive user traits map for Customer.io identification.
  ///
  /// **Returns:** Map of user attributes and app metadata for Customer.io
  ///
  /// This enhanced version includes:
  /// - Extended personal attributes
  /// - Detailed location information
  /// - Comprehensive app and device metadata
  /// - User preferences and settings
  /// - Account status information
  Map<String, dynamic> _buildCustomerIOTraits({
    required UserAttributesModel? authUser,
    required Map<String, String> appMetadata,
    required String userCurrency,
  }) {
    final currentLanguage = LanguageRepository.getSavedLanguage();
    final formattedPhone = authUser?.formattedPhoneNumber ?? '';

    return {
      // Personal Information - Core identity attributes
      'name': authUser?.name ?? '',
      'email': authUser?.email ?? '',
      'phone': formattedPhone,
      'last_login_at': DateTime.now().toIso8601String(),

      // Location Data - Enhanced geographical context
      'country_name': authUser?.country?.attributes?.name ?? '',
      'country_code': authUser?.country?.attributes?.code ?? '',
      'country_region': authUser?.country?.attributes?.region ?? '',

      // User Preferences - Personalization settings
      'user_currency': userCurrency,
      'app_language': currentLanguage?.code ?? 'en',
      'app_language_name': currentLanguage?.name ?? 'English',

      // App Metadata - Detailed application information
      'app_build_number': appMetadata['buildNumber'] ?? '',
      'app_version': appMetadata['version'] ?? '',
      'app_platform': Platform.operatingSystem,
      'app_platform_version': Platform.operatingSystemVersion,
      'device_locale': Platform.localeName,
      'device_type': Platform.isIOS
          ? 'ios'
          : Platform.isAndroid
              ? 'android'
              : 'other',
    };
  }

  /// Handles the logout process for all push notification services
  ///
  /// This method ensures that when a user logs out, their push notification
  /// registrations are properly cleaned up across all integrated services.
  ///
  /// **Implementation Details:**
  /// - Logs out from OneSignal to prevent further notifications to this device
  /// - Clears Customer.io identification to stop personalized messaging
  /// - Logs the logout event for debugging and analytics
  Future<void> logout() async {
    // try {
    //   // OneSignal logout
    //   await OneSignal.logout();
    //   FroggyLogger.info('Successfully logged out from OneSignal');
    // } catch (e) {
    //   FroggyLogger.error('OneSignal Logout Error: $e');
    // }

    try {
      // Customer.io cleanup
      // This will disassociate the device from the user
      CustomerIO.instance.clearIdentify();
      FroggyLogger.info('Successfully cleared Customer.io identity');
    } catch (e) {
      FroggyLogger.error('Customer.io Logout Error: $e');
    }

    // Additional logout cleanup can be added here
    FroggyLogger.info('Push notification services logout completed');
  }
}
