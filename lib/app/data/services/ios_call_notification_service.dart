import 'dart:io';

import 'package:froggytalk/app/data/services/callkit_service.dart';
import 'package:froggytalk/dialer/dialer.dart';
import 'package:utils/utils.dart';

/// iOS-specific call notification service that integrates with CallKit
/// This service handles the "call notification service unavailable" error
/// by providing proper iOS system integration
class IOSCallNotificationService {

  factory IOSCallNotificationService() {
    _instance ??= IOSCallNotificationService._internal();
    return _instance!;
  }

  IOSCallNotificationService._internal() {
    if (Platform.isIOS) {
      _initializeCallKit();
    }
  }
  static IOSCallNotificationService? _instance;
  static final _uuid = getUuid; // Replace with actual UUID generator);

  final CallKitService _callKitService = CallKitService();
  DialerBloc? _dialerBloc;
  String? _currentCallUUID;
  bool _isInitialized = false;

  /// Initialize CallKit integration for iOS
  void _initializeCallKit() {
    if (_isInitialized) return;

    try {
      // Set up CallKit event handlers
      _callKitService.onShouldStartCall = _handleShouldStartCall;
      _callKitService.onShouldAnswerCall = _handleShouldAnswerCall;
      _callKitService.onShouldEndCall = _handleShouldEndCall;
      _callKitService.onShouldSetCallHold = _handleShouldSetCallHold;
      _callKitService.onShouldSetCallMute = _handleShouldSetCallMute;
      _callKitService.onCallStarted = _handleCallStarted;
      _callKitService.onCallAnswered = _handleCallAnswered;
      _callKitService.onCallEnded = _handleCallEnded;
      _callKitService.onAudioSessionActivated = _handleAudioSessionActivated;
      _callKitService.onAudioSessionDeactivated = _handleAudioSessionDeactivated;

      _isInitialized = true;
      FroggyLogger.info(
        '[IOSCallNotificationService] CallKit initialized '
        'successfully',
      );
    } catch (e) {
      FroggyLogger.error(
        '[IOSCallNotificationService] Failed to initialize CallKit: $e',
      );
    }
  }

  /// Set the DialerBloc for call management
  void setDialerBloc(DialerBloc dialerBloc) {
    _dialerBloc = dialerBloc;
  }

  /// Check if iOS CallKit is available and properly initialized
  bool get isAvailable => Platform.isIOS && _isInitialized;

  /// Start an outgoing call with CallKit integration
  Future<void> startOutgoingCall({
    required String phoneNumber,
    String? displayName,
    bool hasVideo = false,
  }) async {
    if (!isAvailable) {
      FroggyLogger.warning(
        '[IOSCallNotificationService] CallKit not available',
      );
      return;
    }

    try {
      _currentCallUUID = _uuid.v4();
      
      await _callKitService.startOutgoingCall(
        uuid: _currentCallUUID!,
        phoneNumber: phoneNumber,
        hasVideo: hasVideo,
      );

      FroggyLogger.info(
        '[IOSCallNotificationService] Started outgoing call to '
        ' 24phoneNumber',
      );
    } catch (e) {
      FroggyLogger.error(
        '[IOSCallNotificationService] Error starting outgoing call: $e',
      );
      _currentCallUUID = null;
      rethrow;
    }
  }

  /// Report an incoming call to CallKit
  Future<void> reportIncomingCall({
    required String phoneNumber,
    String? callerName,
    bool hasVideo = false,
  }) async {
    if (!isAvailable) {
      FroggyLogger.warning(
        '[IOSCallNotificationService] CallKit not available',
      );
      return;
    }

    try {
      _currentCallUUID = _uuid.v4();
      
      await _callKitService.reportIncomingCall(
        uuid: _currentCallUUID!,
        phoneNumber: phoneNumber,
        contactName: callerName,
        hasVideo: hasVideo,
      );

      FroggyLogger.info(
        '[IOSCallNotificationService] Reported incoming call from '
        ' 24phoneNumber',
      );
    } catch (e) {
      FroggyLogger.error(
        '[IOSCallNotificationService] Error reporting incoming call: $e',
      );
      _currentCallUUID = null;
      rethrow;
    }
  }

  /// Report call as connected to CallKit
  Future<void> reportCallConnected() async {
    if (!isAvailable || _currentCallUUID == null) return;

    try {
      await _callKitService.reportCallConnected(_currentCallUUID!);
      FroggyLogger.info(
        '[IOSCallNotificationService] Reported call connected',
      );
    } catch (e) {
      FroggyLogger.error(
        '[IOSCallNotificationService] Error reporting call connected: $e',
      );
    }
  }

  /// End the current call
  Future<void> endCall() async {
    if (!isAvailable || _currentCallUUID == null) return;

    try {
      await _callKitService.endCall(_currentCallUUID!);
      FroggyLogger.info(
        '[IOSCallNotificationService] Ended call',
      );
    } catch (e) {
      FroggyLogger.error(
        '[IOSCallNotificationService] Error ending call: $e',
      );
    } finally {
      _currentCallUUID = null;
    }
  }

  /// Set call on hold
  Future<void> setCallOnHold(bool onHold) async {
    if (!isAvailable || _currentCallUUID == null) return;

    try {
      await _callKitService.setCallOnHold(_currentCallUUID!, onHold: onHold);
      FroggyLogger.info(
        '[IOSCallNotificationService] Set call hold: $onHold',
      );
    } catch (e) {
      FroggyLogger.error(
        '[IOSCallNotificationService] Error setting call hold: $e',
      );
    }
  }

  /// Set call muted
  Future<void> setCallMuted(bool muted) async {
    if (!isAvailable || _currentCallUUID == null) return;

    try {
      await _callKitService.setCallMuted(_currentCallUUID!, muted: muted);
      FroggyLogger.info(
        '[IOSCallNotificationService] Set call muted: $muted',
      );
    } catch (e) {
      FroggyLogger.error(
        '[IOSCallNotificationService] Error setting call muted: $e',
      );
    }
  }

  // CallKit event handlers

  void _handleShouldStartCall(String uuid, String handle) {
    FroggyLogger.info(
      '[IOSCallNotificationService] CallKit requested start call to '
      ' 24handle',
    );
    
    if (_dialerBloc != null) {
      _dialerBloc!.add(DialerEvent.callStarted(phoneNumber: handle));
    }
  }

  void _handleShouldAnswerCall(String uuid) {
    FroggyLogger.info(
      '[IOSCallNotificationService] CallKit requested answer call',
    );
    
    // Note: Answering through CallKit - the actual SIP answer logic should be handled 
    // by the existing call handling system
  }

  void _handleShouldEndCall(String uuid) {
    FroggyLogger.info(
      '[IOSCallNotificationService] CallKit requested end call',
    );
    
    if (_dialerBloc != null) {
      _dialerBloc!.add(const DialerEvent.hangedup());
    }
    
    _currentCallUUID = null;
  }

  void _handleShouldSetCallHold(String uuid, bool onHold) {
    FroggyLogger.info(
      '[IOSCallNotificationService] CallKit requested set call hold: '
      ' 24onHold',
    );
    
    if (_dialerBloc != null) {
      if (onHold) {
        _dialerBloc!.add(const DialerEvent.holdCall());
      } else {
        _dialerBloc!.add(const DialerEvent.unholdCall());
      }
    }
  }

  void _handleShouldSetCallMute(String uuid, bool muted) {
    FroggyLogger.info(
      '[IOSCallNotificationService] CallKit requested set call mute: '
      ' 24muted',
    );
    
    if (_dialerBloc != null) {
      if (muted) {
        _dialerBloc!.add(const DialerEvent.muteOn());
      } else {
        _dialerBloc!.add(const DialerEvent.muteOff());
      }
    }
  }

  void _handleCallStarted(String uuid) {
    FroggyLogger.info(
      '[IOSCallNotificationService] CallKit call started: $uuid',
    );
  }

  void _handleCallAnswered(String uuid) {
    FroggyLogger.info(
      '[IOSCallNotificationService] CallKit call answered: $uuid',
    );
  }

  void _handleCallEnded(String uuid) {
    FroggyLogger.info(
      '[IOSCallNotificationService] CallKit call ended: $uuid',
    );
    _currentCallUUID = null;
  }

  void _handleAudioSessionActivated() {
    FroggyLogger.info(
      '[IOSCallNotificationService] Audio session activated',
    );
  }

  void _handleAudioSessionDeactivated() {
    FroggyLogger.info(
      '[IOSCallNotificationService] Audio session deactivated',
    );
  }

  /// Get current call UUID
  String? get currentCallUUID => _currentCallUUID;
}
