import 'dart:io';

import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/dialer/dialer.dart';
import 'package:utils/utils.dart';

/// Enhanced call service that properly handles iOS call notifications
/// This service replaces the problematic foreground service approach with
/// CallKit integration for iOS while maintaining Android functionality
class EnhancedCallNotificationService {

  factory EnhancedCallNotificationService() {
    _instance ??= EnhancedCallNotificationService._internal();
    return _instance!;
  }

  EnhancedCallNotificationService._internal() {
    _initializePlatformSpecificServices();
  }
  static EnhancedCallNotificationService? _instance;

  IOSCallNotificationService? _iosCallService;
  bool _isInitialized = false;

  /// Initialize platform-specific call notification services
  void _initializePlatformSpecificServices() {
    if (_isInitialized) return;

    try {
      if (Platform.isIOS) {
        _iosCallService = IOSCallNotificationService();
        FroggyLogger.info(
          '[EnhancedCallNotificationService] iOS CallKit service '
          'initialized',
        );
      } else if (Platform.isAndroid) {
        // Android foreground service implementation remains the same
        FroggyLogger.info(
          '[EnhancedCallNotificationService] Android foreground service '
          'available',
        );
      }

      _isInitialized = true;
    } catch (e) {
      FroggyLogger.error(
        '[EnhancedCallNotificationService] Failed to initialize: $e',
      );
    }
  }

  /// Set DialerBloc for call management
  void setDialerBloc(DialerBloc dialerBloc) {
    if (Platform.isIOS && _iosCallService != null) {
      _iosCallService!.setDialerBloc(dialerBloc);
    }
  }

  /// Check if call notification service is available for the current platform
  bool get isCallNotificationAvailable {
    if (Platform.isIOS) {
      return _iosCallService?.isAvailable ?? false;
    } else if (Platform.isAndroid) {
      // Android foreground service is generally available
      return true;
    }
    return false;
  }

  /// Start outgoing call with proper platform notification
  Future<void> startOutgoingCall({
    required String phoneNumber,
    String? displayName,
    bool hasVideo = false,
  }) async {
    if (!_isInitialized) {
      throw StateError(
        'EnhancedCallNotificationService not initialized',
      );
    }

    try {
      if (Platform.isIOS && _iosCallService != null) {
        await _iosCallService!.startOutgoingCall(
          phoneNumber: phoneNumber,
          displayName: displayName,
          hasVideo: hasVideo,
        );
      } else if (Platform.isAndroid) {
        // For Android, the existing foreground service handles this
        // This method provides a unified interface
        FroggyLogger.info(
          '[EnhancedCallNotificationService] Android outgoing call: '
          ' 24phoneNumber',
        );
      }
    } catch (e) {
      FroggyLogger.error(
        '[EnhancedCallNotificationService] Error starting outgoing call: '
        ' 24e',
      );
      
      // If CallKit fails on iOS, we should still allow the call to proceed
      // The app will handle the call without system integration
      if (Platform.isIOS) {
        FroggyLogger.warning(
          '[EnhancedCallNotificationService] CallKit unavailable, '
          'proceeding without system integration',
        );
      } else {
        rethrow;
      }
    }
  }

  /// Report incoming call with proper platform notification
  Future<void> reportIncomingCall({
    required String phoneNumber,
    String? callerName,
    bool hasVideo = false,
  }) async {
    if (!_isInitialized) {
      throw StateError('EnhancedCallNotificationService not initialized');
    }

    try {
      if (Platform.isIOS && _iosCallService != null) {
        await _iosCallService!.reportIncomingCall(
          phoneNumber: phoneNumber,
          callerName: callerName,
          hasVideo: hasVideo,
        );
      } else if (Platform.isAndroid) {
        // Android handles incoming calls through existing system
        FroggyLogger.info(
          '[EnhancedCallNotificationService] Android incoming call: '
          ' 24phoneNumber',
        );
      }
    } catch (e) {
      FroggyLogger.error(
        '[EnhancedCallNotificationService] Error reporting incoming call: '
        ' 24e',
      );
      
      // If CallKit fails on iOS, we should still show the call in-app
      if (Platform.isIOS) {
        FroggyLogger.warning(
          '[EnhancedCallNotificationService] CallKit unavailable, showing '
          'in-app call',
        );
      } else {
        rethrow;
      }
    }
  }

  /// Report call as connected
  Future<void> reportCallConnected() async {
    if (!_isInitialized) return;

    try {
      if (Platform.isIOS && _iosCallService != null) {
        await _iosCallService!.reportCallConnected();
      }
      // Android doesn't need this call
    } catch (e) {
      FroggyLogger.error(
        '[EnhancedCallNotificationService] Error reporting call connected: '
        ' 24e',
      );
    }
  }

  /// End the current call
  Future<void> endCall() async {
    if (!_isInitialized) return;

    try {
      if (Platform.isIOS && _iosCallService != null) {
        await _iosCallService!.endCall();
      }
      // Android call ending is handled by existing system
    } catch (e) {
      FroggyLogger.error(
        '[EnhancedCallNotificationService] Error ending call: $e',
      );
    }
  }

  /// Set call on hold
  Future<void> setCallOnHold(bool onHold) async {
    if (!_isInitialized) return;

    try {
      if (Platform.isIOS && _iosCallService != null) {
        await _iosCallService!.setCallOnHold(onHold);
      }
      // Android hold state is handled by existing system
    } catch (e) {
      FroggyLogger.error(
        '[EnhancedCallNotificationService] Error setting call hold: $e',
      );
    }
  }

  /// Set call muted
  Future<void> setCallMuted(bool muted) async {
    if (!_isInitialized) return;

    try {
      if (Platform.isIOS && _iosCallService != null) {
        await _iosCallService!.setCallMuted(muted);
      }
      // Android mute state is handled by existing system
    } catch (e) {
      FroggyLogger.error(
        '[EnhancedCallNotificationService] Error setting call muted: $e',
      );
    }
  }

  /// Get current call UUID (iOS only)
  String? get currentCallUUID {
    if (Platform.isIOS && _iosCallService != null) {
      return _iosCallService!.currentCallUUID;
    }
    return null;
  }

  /// Check if the service has encountered issues that might cause 
  /// "call notification service unavailable" errors
  bool get hasNotificationServiceIssues {
    if (Platform.isIOS) {
      return !(_iosCallService?.isAvailable ?? false);
    }
    return false;
  }

  /// Get platform-specific error message for debugging
  String get platformSpecificError {
    if (Platform.isIOS && hasNotificationServiceIssues) {
      return 'iOS CallKit service not available. Check app permissions and '
          'system settings.';
    } else if (Platform.isAndroid) {
      return 'Android foreground service may not be properly configured.';
    }
    return 'Unknown platform or service not initialized.';
  }
}
