import 'dart:async';
import 'dart:ui';

import 'package:dynamic_app_icons/dynamic_app_icons.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:utils/utils.dart';

class FroggyBackgroundService {
  factory FroggyBackgroundService() {
    return _instance;
  }

  FroggyBackgroundService._internal();

  static final FroggyBackgroundService _instance =
      FroggyBackgroundService._internal();

  /// Provides access to the singleton instance of [FroggyBackgroundService].
  static FroggyBackgroundService getInstance() {
    return _instance;
  }

  Future<void> ensureInitialized() async {
    final service = FlutterBackgroundService();

    await service.configure(
      iosConfiguration: IosConfiguration(
        onForeground: onStart,
        onBackground: onIosBackground,
      ),
      androidConfiguration: AndroidConfiguration(
        onStart: onStart,
        isForegroundMode: false,
      ),
    );

    await service.startService();
  }

  // void start() {
  //   FlutterBackgroundService().startService();
  // }

  void stop() {
    FlutterBackgroundService().invoke('stop');
  }

  @pragma('vm:entry-point')
  static Future<bool> onIosBackground(ServiceInstance service) async {
    WidgetsFlutterBinding.ensureInitialized();
    DartPluginRegistrant.ensureInitialized();
    return true;
  }

  @pragma('vm:entry-point')
  static Future<void> onStart(ServiceInstance service) async {
    // Initialize the local storage utility
    await FroggyLocalStorage.ensureInitialized();
    await FroggyDynamicIcons.ensureInitialized(
      duration: const Duration(minutes: 1),
    );

    service.on('start').listen((event) {
      FroggyLogger.info('froggy background process is now started');
    });

    service.on('stop').listen((event) {
      service.stopSelf();
      FroggyLogger.info('froggy background process is now stopped');
    });

    // Timer.periodic(const Duration(seconds: 1), (timer) {
    //   FroggyLogger.info(
    //     'service is successfully running ${DateTime.now().second}',
    //   );

    //   FroggyDynamicIcons().checkElapsedTime();
    // });
  }
}
