import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/dialer/dialer.dart';
import 'package:froggytalk/l10n/l10n.dart';

class NumPad extends HookWidget {
  const NumPad({
    required this.onNumpadPressed,
    // required this.onDeleteButtonPressed,
    // this.onDialButtonPressed,
    this.showDeleteButton = true,
    this.hideActionButtons = true,
    super.key,
  });

  // final VoidCallback? onDialButtonPressed;
  // final VoidCallback onDeleteButtonPressed;
  final void Function(String number) onNumpadPressed;
  final bool showDeleteButton;
  final bool hideActionButtons;

  @override
  Widget build(BuildContext context) {
    final l10n = useLocalizationsWithoutContext();
    final itemCount = useMemoized<int>(() {
      if (hideActionButtons) {
        return 12;
      }

      // if (!showDeleteButton) {
      //   return 14;
      // }

      return 15;
    });

    return Directionality(
      textDirection: TextDirection.ltr,
      child: LayoutBuilder(
        builder: (context, constraints) {
          final maxHeight = constraints.maxHeight;

          final buttonSize =
              constraints.maxWidth / 3 - 3.0; // Adjust for padding
          // final buttonHeight =
          //     constraints.maxHeight / (hideActionButtons ? 4 : 5) -
          //         3.0; // Adjust for padding
          final buttonHeight =
              constraints.maxHeight / 5 - 3.0; // Adjust for padding

          return GridView.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              childAspectRatio: buttonSize / buttonHeight,
              crossAxisSpacing: 10,
              mainAxisSpacing: 10,
            ),
            itemCount: itemCount,
            // itemCount: showDeleteButton ? 15 : 14,
            cacheExtent: 0,
            semanticChildCount: 15,
            padding: const EdgeInsets.all(10),
            physics: const NeverScrollableScrollPhysics(),
            // Disable scrolling
            itemBuilder: (context, index) {
              if (index == 0) {
                return NumPadButton(
                  '${index + 1}',
                  onPressed: onNumpadPressed,
                  width: buttonSize,
                  height: buttonHeight,
                );
              } else if (index == 1) {
                return NumPadButton(
                  '${index + 1}',
                  onPressed: onNumpadPressed,
                  width: buttonSize,
                  height: buttonHeight,
                  label: l10n.dialerKeypadABC,
                );
              } else if (index == 2) {
                return NumPadButton(
                  '${index + 1}',
                  onPressed: onNumpadPressed,
                  width: buttonSize,
                  height: buttonHeight,
                  label: l10n.dialerKeypadDEF,
                );
              } else if (index == 3) {
                return NumPadButton(
                  '${index + 1}',
                  label: l10n.dialerKeypadGHI,
                  onPressed: onNumpadPressed,
                  width: buttonSize,
                  height: buttonHeight,
                );
              } else if (index == 4) {
                return NumPadButton(
                  '${index + 1}',
                  label: l10n.dialerKeypadJKL,
                  onPressed: onNumpadPressed,
                  width: buttonSize,
                  height: buttonHeight,
                );
              } else if (index == 5) {
                return NumPadButton(
                  '${index + 1}',
                  label: l10n.dialerKeypadMNO,
                  onPressed: onNumpadPressed,
                  width: buttonSize,
                  height: buttonHeight,
                );
              } else if (index == 6) {
                return NumPadButton(
                  '${index + 1}',
                  label: l10n.dialerKeypadPQRS,
                  onPressed: onNumpadPressed,
                  width: buttonSize,
                  height: buttonHeight,
                );
              } else if (index == 7) {
                return NumPadButton(
                  '${index + 1}',
                  label: l10n.dialerKeypadTUV,
                  onPressed: onNumpadPressed,
                  width: buttonSize,
                  height: buttonHeight,
                );
              } else if (index == 8) {
                return NumPadButton(
                  '${index + 1}',
                  label: l10n.dialerKeypadWXYZ,
                  onPressed: onNumpadPressed,
                  width: buttonSize,
                  height: buttonHeight,
                );
              } else if (index == 10) {
                return NumPadButton(
                  '0',
                  onPressed: onNumpadPressed,
                  onLongPressed: () => onNumpadPressed('+'),
                  width: buttonSize,
                  label: '+',
                  labelFontSize: !(maxHeight <= 500) ? 30 : 20,
                );
              } else if (index == 11) {
                return NumPadButton(
                  '#',
                  onPressed: onNumpadPressed,
                  width: buttonSize,
                  height: buttonHeight,
                  showLabel: false,
                );
              } else if (index == 12) {
                return NumPadButton.spacer(
                  width: buttonSize,
                  height: buttonHeight,
                );
              } else if (index == 13) {
                // return NumPadButton.icon(
                //   icon: FroggyIconsList.phoneSolid.toWidget(
                //     width: 30,
                //   ),
                //   backgroundColor: FroggyColors.primary,
                //   onPressed: onDialButtonPressed,
                //   width: buttonSize + 10,
                //   height: buttonHeight + 10,
                // );
                return DialerButton(
                  // onPressed: onDialButtonPressed,
                  width: buttonSize + 10,
                  height: buttonHeight + 10,
                );
              } else if (index == 14) {
                return DeleteButton(
                  width: buttonSize + 10,
                  height: buttonHeight + 10,
                );
                // return NumPadButton.icon(
                //   icon: FroggyIconsList.backspace.toWidget(
                //     color: FroggyColors.black,
                //     width: 30,
                //   ),
                //   backgroundColor: FroggyColors.froggyCream,
                //   onLongPress: () => onNumpadPressed('wipe-x'),
                //   onPressed: onDeleteButtonPressed,
                //   borderColor: FroggyColors.froggyGrey4,
                //   width: buttonSize,
                //   height: buttonHeight,
                // );
              } else {
                return NumPadButton(
                  '*',
                  onPressed: onNumpadPressed,
                  width: buttonSize,
                  height: buttonHeight,
                  showLabel: false,
                  paddingTop: 10,
                  // fontSize: 60,
                );
              }
            },
          );
        },
      ),
    );
  }

// // ignore: unused_element
// LayoutBuilder _generateAlternativePrimaryPad() {
//   return LayoutBuilder(
//     builder: (context, constraints) {
//       // final size = (MediaQuery.of(context).size.width / 3) - 10.0;
//       final size = constraints.maxWidth / 3 - 10.0; // Adjust for padding
//       // final buttonHeight =
//       //     constraints.maxHeight / 5 - 10.0; // Adjust for padding
//       const gap = 10.0;

//       return Wrap(
//         spacing: gap,
//         runSpacing: gap,
//         alignment: WrapAlignment.spaceEvenly,
//         crossAxisAlignment: WrapCrossAlignment.center,
//         runAlignment: WrapAlignment.spaceEvenly,
//         children: [
//           NumPadButton(
//             '1',
//             onPressed: onNumpadPressed,
//             width: size,
//           ),
//           NumPadButton(
//             '2',
//             onPressed: onNumpadPressed,
//             width: size,
//             label: 'ABC',
//           ),
//           NumPadButton(
//             '3',
//             onPressed: onNumpadPressed,
//             width: size,
//             label: 'DEF',
//           ),
//           NumPadButton(
//             '4',
//             onPressed: onNumpadPressed,
//             width: size,
//             label: 'GHI',
//           ),
//           NumPadButton(
//             '5',
//             onPressed: onNumpadPressed,
//             width: size,
//             label: 'JKL',
//           ),
//           NumPadButton(
//             '6',
//             onPressed: onNumpadPressed,
//             width: size,
//             label: 'MNO',
//           ),
//           NumPadButton(
//             '7',
//             onPressed: onNumpadPressed,
//             width: size,
//             label: 'PQRS',
//           ),
//           NumPadButton(
//             '8',
//             onPressed: onNumpadPressed,
//             width: size,
//             label: 'TUV',
//           ),
//           NumPadButton(
//             '9',
//             onPressed: onNumpadPressed,
//             width: size,
//             label: 'WXYZ',
//           ),
//           NumPadButton(
//             '*',
//             onPressed: onNumpadPressed,
//             width: size,
//             // fontSize: 30,
//           ),
//           NumPadButton(
//             '0',
//             onPressed: onNumpadPressed,
//             onLongPressed: () => onNumpadPressed('+'),
//             width: size,
//             label: '+',
//             labelFontSize: 18,
//             labelFontWeight: FontWeight.w600,
//           ),
//           NumPadButton(
//             '#',
//             onPressed: onNumpadPressed,
//             width: size,
//           ),
//           NumPadButton.spacer(width: size),
//           NumPadButton.icon(
//             icon: FroggyIconsList.phoneSolid.toWidget(
//               width: 30,
//             ),
//             backgroundColor: FroggyColors.primary,
//             onPressed: onDialButtonPressed,
//             width: size,
//             height: size - 20,
//           ),
//           NumPadButton.icon(
//             icon: FroggyIconsList.backspace.toWidget(
//               color: FroggyColors.black,
//               width: 30,
//             ),
//             backgroundColor: FroggyColors.froggyCream,
//             onLongPress: () => onNumpadPressed('wipe-x'),
//             onPressed: onDeleteButtonPressed,
//             borderColor: FroggyColors.froggyGrey4,
//             width: size,
//             height: size - 20,
//           ),
//         ],
//       );
//     },
//   );
// }
}
