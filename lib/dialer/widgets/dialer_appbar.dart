import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggy_icons/froggy_icons.dart';
import 'package:froggytalk/contact_list/contact_list.dart';
import 'package:froggytalk/l10n/l10n.dart';

class DialerPageAppBar extends HookWidget implements PreferredSizeWidget {
  const DialerPageAppBar({
    super.key,
  });

  double get height => 60;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    final onPhoneBookIconPressed = useCallback(() {
      Navigator.of(context).push(
        // ContactsOverviewPage.modalRoute(),
        ContactListPv2age.modalRoute(),
      );
    });

    return AppBar(
      toolbarHeight: height,
      title: Padding(
        padding: const EdgeInsets.only(left: 10, top: 10),
        child: Text(
          l10n.dialerAppBarTitle,
          style: const TextStyle(
            color: FroggyColors.black,
            fontSize: 24,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      actions: [
        // AppNotificationBell(),
        Padding(
          padding: const EdgeInsets.only(right: 10, top: 10),
          child: IconButton.filledTonal(
            onPressed: onPhoneBookIconPressed,
            icon: FroggyIconsList.phoneBookOutline.toWidget(
              width: 30,
              height: 30,
              color: FroggyColors.primary,
            ),
          ),
        ),
      ],
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(height);
}
