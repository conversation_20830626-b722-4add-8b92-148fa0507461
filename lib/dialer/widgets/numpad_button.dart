import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

class NumPadButton extends HookWidget {
  const NumPadButton(
    this.number, {
    required this.onPressed,
    this.label,
    this.labelFontSize,
    // this.fontSize,
    this.labelFontWeight,
    this.width,
    this.height,
    this.onLongPressed,
    this.showLabel = true,
    this.paddingTop = 0,
    super.key,
  });

  static Widget spacer({
    required double width,
    double? height,
  }) {
    return SizedBox(
      width: width,
      height: height,
    );
  }

  static Widget icon({
    required Widget icon,
    required Color backgroundColor,
    VoidCallback? onPressed,
    VoidCallback? onLongPress,
    VoidCallback? onDoubleTap,
    Color? borderColor,
    double? width,
    double? height,
  }) {
    return InkWell(
      onDoubleTap: onDoubleTap,
      onLongPress: onLongPress,
      child: IconButton.filledTonal(
        style: IconButton.styleFrom(
          fixedSize: Size(width ?? double.infinity, height ?? double.infinity),
          shape: CircleBorder(
            side: borderColor == null
                ? BorderSide.none
                : BorderSide(
                    color: borderColor,
                  ),
          ),
          backgroundColor: backgroundColor,
          padding: const EdgeInsets.all(5),
        ),
        onPressed: onPressed,
        icon: icon,
      ),
    );
  }

  final void Function(String number) onPressed;
  final bool showLabel;
  final String? label;
  final double? labelFontSize;
  // final double? fontSize;
  final FontWeight? labelFontWeight;
  final double? width;
  final double? height;
  final VoidCallback? onLongPressed;
  final String number;
  final double paddingTop;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final mediaHeight = MediaQuery.sizeOf(context).height;
        final adjustedFontSize = mediaHeight <= 600 ? 30 : 50;
        final adjustedLabelFontSize = mediaHeight <= 600 ? 15 : 18;

        return MaterialButton(
          onPressed: () => onPressed(number),
          onLongPress: onLongPressed,
          color: FroggyColors.froggyCream,
          shape: const CircleBorder(
            side: BorderSide(
              color: FroggyColors.froggyGrey4,
            ),
          ),
          padding: EdgeInsets.zero,
          elevation: 0,
          minWidth: width,
          height: height,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisSize: MainAxisSize.min,
            children: [
              const Spacer(),
              if (paddingTop > 0) SizedBox(height: paddingTop),
              Text(
                number,
                textDirection: TextDirection.ltr,
                style: TextStyle(
                  fontSize: adjustedFontSize.toDouble(),
                  fontWeight: FontWeight.w500,
                  color: FroggyColors.black,
                ),
                textAlign: TextAlign.center,
                textScaler: const TextScaler.linear(0.6),
              ),
              if (showLabel)
                Container(
                  alignment: Alignment.center,
                  child: Text(
                    label ?? '',
                    style: TextStyle(
                      fontSize:
                          labelFontSize ?? adjustedLabelFontSize.toDouble(),
                      fontWeight: FontWeight.w400,
                      color: FroggyColors.black,
                    ),
                    textAlign: TextAlign.center,
                    textScaler: const TextScaler.linear(0.6),
                  ),
                ),
              const Spacer(),
            ],
          ),
        );
      },
    );
  }
}
