import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:froggy_icons/froggy_icons.dart';

class CallingUserButton extends StatelessWidget {
  const CallingUserButton({
    required this.icon,
    required this.onPressed,
    this.changeIconColorToRed = false,
    this.isActive = true,
    this.backgroundColor,
    this.onLongPress,
    super.key,
  });

  final FroggyIconsList icon;
  final void Function(bool value)? onPressed;
  final bool changeIconColorToRed;
  final bool isActive;
  final Color? backgroundColor;
  final VoidCallback? onLongPress;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      key: Key(icon.hashCode.toString()),
      onPressed: () => onPressed?.call(isActive),
      style: ButtonStyle(
        backgroundColor: WidgetStateProperty.all(
          backgroundColor ??
              (!isActive ? FroggyColors.froggyGrey1 : FroggyColors.white),
        ),
        elevation: WidgetStateProperty.all(0),
        shape: WidgetStateProperty.all(const CircleBorder()),
        padding: WidgetStateProperty.all(const EdgeInsets.all(20)),
      ),
      onLongPress: onLongPress,
      child: icon.toWidget(
        color: isActive
            ? changeIconColorToRed
                ? FroggyColors.froggyError
                : FroggyColors.froggyBlack
            : FroggyColors.white,
      ),
    );
  }
}
