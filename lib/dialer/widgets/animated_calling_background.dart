// ignore_for_file: public_member_api_docs

import 'dart:math' as math;
import 'dart:ui';

import 'package:constants/constants.dart';
import 'package:flutter/material.dart';

/// AnimatedCallingBackground
///
/// A Maestro-inspired animated gradient background for the call screen.
/// Replaces the static PNG with a dynamic, fluid gradient animation.
///
/// - Uses AnimationController for smooth, explicit animation.
/// - Animates gradient stops and colors for a lively effect.
/// - Accessible: does not interfere with semantics or contrast.
/// - Performance: optimized for mobile, avoids excessive rebuilds.
///
/// Debugging: If animation stutters, profile with Flutter DevTools and
/// check for jank in the timeline.
class AnimatedCallingBackground extends StatefulWidget {
  const AnimatedCallingBackground({super.key});

  @override
  State<AnimatedCallingBackground> createState() =>
      _AnimatedCallingBackgroundState();
}

class _AnimatedCallingBackgroundState extends State<AnimatedCallingBackground>
    with TickerProviderStateMixin {
  late final AnimationController _controller;
  // Animation for the floating face movement
  late final Animation<double> _floatingAnimation;
  // Animation for the red glow opacity
  late final AnimationController _redGlowController;
  late final Animation<double> _redGlowOpacity;

  @override
  void initState() {
    super.initState();

    // Initialize the controller first
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 8),
    );

    // Initialize the floating animation
    _floatingAnimation = Tween<double>(begin: 0, end: 1)
        .animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ),);

    // Start the animation after everything is initialized
    _controller.repeat(reverse: true);

    // Red glow opacity controller (3s period, repeats forever)
    _redGlowController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3),
    );
    _redGlowOpacity = Tween<double>(begin: 0.18, end: 0.38).animate(
      CurvedAnimation(
        parent: _redGlowController,
        curve: Curves.easeInOut,
      ),
    );
    _redGlowController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    _redGlowController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_controller, _redGlowController]),
      builder: (context, child) {
        final t = _controller.value;
        final width = MediaQuery.of(context).size.width;
        final height = MediaQuery.of(context).size.height;
        // Calculate floating movement
        final floatOffset = 20.0 * math.sin(_floatingAnimation.value * math.pi);

        return Stack(
          children: [
            ColoredBox(
              color: FroggyColors.froggyBlack,
              child: Stack(
                children: [
                  // Main radial spots
                  // Green glow
                  Positioned(
                    left: width * 0.1 - width * 0.3,
                    top: height * 0.25 - width * 0.3,
                    child: _AnimatedRadialBlur(
                      diameter: width * 0.6,
                      color: FroggyColors.froggyGreen.withValues(alpha: 0.45),
                      blurSigma: 80,
                    ),
                  ),
                  // Cream/yellow glow
                  Positioned(
                    left: width * 0.75 - width * 0.25,
                    top: height * 0.5 - width * 0.25,
                    child: _AnimatedRadialBlur(
                      diameter: width * 0.5,
                      color: FroggyColors.froggyCream.withValues(alpha: 0.35),
                      blurSigma: 70,
                    ),
                  ),
                  // Red glow (animated opacity)
                  Positioned(
                    left: width * 0.3 - width * 0.22,
                    top: height * 0.8 - width * 0.22,
                    child: _AnimatedRadialBlur(
                      diameter: width * 0.44,
                      color: FroggyColors.froggyRed.withValues(alpha: _redGlowOpacity.value),
                      blurSigma: 60,
                    ),
                  ),
                  // Surprise: floating blue orb
                  if (t > 0.7 && t < 0.8)
                    Positioned(
                      left: width * 0.5 - width * 0.12,
                      top: height * 0.4 - width * 0.12,
                      child: _AnimatedRadialBlur(
                        diameter: width * 0.24,
                        color: FroggyColors.froggyError.withValues(alpha: 0.22),
                        blurSigma: 40,
                      ),
                    ),

                  // Animated face image at bottom left with subtle floating animation
                  Positioned(
                    bottom: 100 + (floatOffset * 0.5), // Apply subtle floating motion
                    height: 350,
                    child: SizedBox(
                      width: width * 0.25,
                      height: width * 0.25,
                      child: Image.asset(
                        'assets/images/calling_bg_item4.png',
                        fit: BoxFit.contain,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}

/// Private widget for a blurred radial spot
class _AnimatedRadialBlur extends StatelessWidget {
  const _AnimatedRadialBlur({
    required this.diameter,
    required this.color,
    required this.blurSigma,
  });

  final double diameter;
  final Color color;
  final double blurSigma;

  @override
  Widget build(BuildContext context) {
    return ImageFiltered(
      imageFilter: ImageFilter.blur(sigmaX: blurSigma, sigmaY: blurSigma),
      child: Container(
        width: diameter,
        height: diameter,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: RadialGradient(
            colors: [
              color,
              color.withValues(alpha: 0),
            ],
            stops: const [0.0, 1.0],
          ),
        ),
      ),
    );
  }
}

/// Maestro's Notes:
/// - This widget animates both the colors and stops of a linear gradient,
///   creating a living, breathing background.
/// - The animation is subtle to avoid distracting from call content, but
///   lively enough to feel dynamic.
/// - For more realism, you can add blur, noise, or overlay SVG/Lottie effects.
/// - Profile with Flutter DevTools if you see performance issues.
