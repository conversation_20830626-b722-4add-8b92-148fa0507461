import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:froggytalk/dialer/dialer.dart';

/// Enhanced calling controls widget that demonstrates the integration
/// of specialized blocs with the refactored DialerBloc
///
/// This widget shows how the DialerBloc now delegates to specialized blocs:
/// - Audio controls delegate to AudioBloc
/// - Registration status delegates to RegistrationBloc
/// - Timer functionality delegates to CallTimerBloc
/// while maintaining backward compatibility with existing functionality.
class EnhancedCallingControls extends StatelessWidget {
  const EnhancedCallingControls({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Enhanced Calling Controls',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Registration Status (now delegates to RegistrationBloc)
            BlocBuilder<RegistrationBloc, RegistrationState>(
              builder: (context, registrationState) {
                return Row(
                  children: [
                    Icon(
                      registrationState.isRegistered
                          ? Icons.check_circle
                          : Icons.error,
                      color: registrationState.isRegistered
                          ? Colors.green
                          : Colors.red,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'SIP Status: ${registrationState.status.name}',
                      style: TextStyle(
                        color: registrationState.isRegistered
                            ? Colors.green
                            : Colors.red,
                      ),
                    ),
                  ],
                );
              },
            ),

            const SizedBox(height: 16),

            // Audio Controls (now delegates to AudioBloc)
            BlocBuilder<AudioBloc, AudioState>(
              builder: (context, audioState) {
                return Row(
                  children: [
                    // Speaker Toggle
                    ElevatedButton.icon(
                      onPressed: () {
                        context.read<DialerBloc>().add(
                              audioState.isSpeakerEnabled
                                  ? const DialerEvent.disableSpeakerPhone()
                                  : const DialerEvent.switchSpeakerPhoneOn(),
                            );
                      },
                      icon: Icon(
                        audioState.isSpeakerEnabled
                            ? Icons.volume_up
                            : Icons.volume_down,
                      ),
                      label: Text(
                        audioState.isSpeakerEnabled
                            ? 'Speaker Off'
                            : 'Speaker On',
                      ),
                    ),
                    const SizedBox(width: 8),
                    // Mute Toggle
                    ElevatedButton.icon(
                      onPressed: () {
                        context.read<DialerBloc>().add(
                              audioState.isMuted
                                  ? const DialerEvent.muteOff()
                                  : const DialerEvent.muteOn(),
                            );
                      },
                      icon: Icon(
                        audioState.isMuted ? Icons.mic_off : Icons.mic,
                      ),
                      label: Text(audioState.isMuted ? 'Unmute' : 'Mute'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor:
                            audioState.isMuted ? Colors.red.shade100 : null,
                      ),
                    ),
                  ],
                );
              },
            ),

            const SizedBox(height: 16),

            // Call Timer (now delegates to CallTimerBloc)
            BlocBuilder<CallTimerBloc, CallTimerState>(
              builder: (context, timerState) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Call Duration: ${timerState.formattedTime}',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        ElevatedButton(
                          onPressed: () {
                            context.read<DialerBloc>().add(
                                  const DialerEvent.timerStarted(),
                                );
                          },
                          child: const Text('Start Timer'),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: () {
                            context.read<DialerBloc>().add(
                                  const DialerEvent.timerStopped(),
                                );
                          },
                          child: const Text('Stop Timer'),
                        ),
                      ],
                    ),
                  ],
                );
              },
            ),

            const SizedBox(height: 16),

            // Core Dialer Status (remains in DialerBloc)
            BlocBuilder<DialerBloc, DialerState>(
              builder: (context, dialerState) {
                return Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Core Dialer Status:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      Text('Status: ${dialerState.status.name}'),
                      Text('Phone: ${dialerState.phoneNumber ?? 'None'}'),
                      if (dialerState.message?.isNotEmpty ?? false)
                        Text(
                          'Message: ${dialerState.message}',
                          style: const TextStyle(color: Colors.orange),
                        ),
                    ],
                  ),
                );
              },
            ),

            const SizedBox(height: 16),

            const Text(
              '✅ Benefits of this approach:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const Text(
              '• Audio controls delegate to AudioBloc for better audio '
              'management\n'
              '• Registration status uses RegistrationBloc for SIP handling\n'
              '• Timer functionality delegates to CallTimerBloc for precise '
              'timing\n'
              '• DialerBloc maintains backward compatibility\n'
              '• Each bloc can be tested independently\n'
              '• UI components can listen to specific blocs for targeted '
              'updates',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }
}
