// Enhanced error boundary and crash prevention for calling screen
// This widget wraps the calling screen to catch and handle crashes gracefully

import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:utils/utils.dart';

/// Enhanced error boundary specifically designed to prevent crashes
/// during call termination scenarios
class CallScreenErrorBoundary extends StatefulWidget {
  const CallScreenErrorBoundary({
    required this.child,
    this.onError,
    super.key,
  });

  final Widget child;
  final void Function(FlutterErrorDetails)? onError;

  @override
  State<CallScreenErrorBoundary> createState() =>
      _CallScreenErrorBoundaryState();
}

class _CallScreenErrorBoundaryState extends State<CallScreenErrorBoundary> {
  bool _hasError = false;
  String _errorMessage = '';
  FlutterErrorDetails? _errorDetails;

  @override
  void initState() {
    super.initState();

    // Set up error handling for the widget tree
    FlutterError.onError = (FlutterErrorDetails details) {
      _handleError(details);
      widget.onError?.call(details);
    };
  }

  /// Handle errors gracefully to prevent app crashes
  void _handleError(FlutterErrorDetails details) {
    FroggyLogger.error(
      '[CallErrorBoundary] Caught error: ${details.exception}',
    );

    // Check if this is a call termination related error
    final isCallTerminationError =
        _isCallTerminationRelatedError(details);

    if (isCallTerminationError) {
      FroggyLogger.warning(
        '[CallErrorBoundary] Call termination error detected - '
        'preventing crash',
      );
    }

    // Update UI to show error state instead of crashing
    if (mounted) {
      setState(() {
        _hasError = true;
        _errorMessage = _getErrorMessage(details);
        _errorDetails = details;
      });

      // Auto-navigate back after showing error briefly
      Timer(const Duration(seconds: 2), () {
        if (mounted) {
          _navigateBack();
        }
      });
    }
  }

  /// Check if an error is related to media streams, WebRTC,
  /// or call termination
  bool _isCallTerminationRelatedError(FlutterErrorDetails details) {
    final exception = details.exception;
    final exceptionString = exception.toString().toLowerCase();

    // Check for WebRTC media stream errors
    if (exceptionString.contains('mediastream') ||
        exceptionString.contains('media stream') ||
        exceptionString.contains('getusermedia') ||
        exceptionString.contains('webrtc') ||
        exceptionString.contains('permission') &&
            exceptionString.contains('denied') ||
        exceptionString.contains('audio') &&
            exceptionString.contains('track')) {

      _errorMessage = 'Media stream error: Please check microphone '
          'permissions and try again';
      return true;
    }

    // Check for common call termination errors
    return exceptionString.contains('call') &&
            exceptionString.contains('terminate') ||
        exceptionString.contains('sip') ||
        exceptionString.contains('session') &&
            exceptionString.contains('close') ||
        exceptionString.contains('peer connection') ||
        exceptionString.contains('rtc');
  }

  /// Get user-friendly error message
  String _getErrorMessage(FlutterErrorDetails details) {
    final exception = details.exception;

    if (exception is StateError) {
      return 'Call session ended unexpectedly';
    } else if (exception is ArgumentError) {
      return 'Invalid call state encountered';
    } else if (exception.toString().toLowerCase().contains('disposed')) {
      return 'Call resources were cleaned up';
    } else {
      return 'Call ended with an error';
    }
  }

  /// Navigate back safely
  void _navigateBack() {
    try {
      if (mounted && context.mounted) {
        Navigator.of(context).maybePop();
      }
    } catch (e) {
      FroggyLogger.error('[CallErrorBoundary] Error navigating back: $e');
      // Force exit the app if navigation fails
      SystemNavigator.pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_hasError) {
      return _buildErrorWidget();
    }

    return widget.child;
  }

  /// Build error UI instead of crashing
  Widget _buildErrorWidget() {
    return Scaffold(
      backgroundColor: Colors.black87,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.call_end,
              color: Colors.white,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            const Text(
              'Returning to previous screen...',
              style: TextStyle(
                color: Colors.white70,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            if (kDebugMode) ...[
              const SizedBox(height: 24),
              TextButton(
                onPressed: () {
                  // In debug mode, allow viewing error details
                  showDialog<void>(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('Error Details'),
                      content: SingleChildScrollView(
                        child: Text(
                          _errorDetails?.toString() ?? 'No details',
                        ),
                      ),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('Close'),
                        ),
                      ],
                    ),
                  );
                },
                child: const Text(
                  'View Details (Debug)',
                  style: TextStyle(color: Colors.white54),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Zone error handler for catching async errors during call termination
class CallTerminationZoneErrorHandler {
  static void run(void Function() body) {
    runZonedGuarded(
      body,
      (error, stackTrace) {
        FroggyLogger.error(
          '[CallZoneHandler] Async error caught: $error',
        );

        // Handle specific call termination errors
        if (_isCallTerminationError(error)) {
          FroggyLogger.warning(
            '[CallZoneHandler] Call termination async error '
            '- handled gracefully',
          );
          // Don't let the error propagate and crash the app
          return;
        }

        // Re-throw other errors
        // ignore: only_throw_errors
        throw error;
      },
    );
  }

  static bool _isCallTerminationError(dynamic error) {
    final errorString = error.toString().toLowerCase();

    final patterns = [
      'rtcsession',
      'call ended',
      'call failed',
      'sip',
      'terminate',
      'hangup',
      'bye',
      'webrtc',
      'mediastream',
      'peerconnection',
      'disposed',
      'state error',
    ];

    return patterns.any(errorString.contains);
  }
}
