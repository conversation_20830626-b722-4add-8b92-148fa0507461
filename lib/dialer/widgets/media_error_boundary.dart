import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:froggytalk/dialer/services/media_error_handler_service.dart';

/// Global error boundary widget specifically for media operations
/// 
/// Wraps any widget tree and catches media-related errors to:
/// - Report errors to Firebase Crashlytics
/// - Show user-friendly error messages
/// - Provide recovery options for users
/// - Prevent app crashes due to media issues
class MediaErrorBoundary extends StatefulWidget {
  const MediaErrorBoundary({
    required this.child,
    this.onError,
    this.showErrorWidget = true,
    this.customErrorWidget,
    super.key,
  });

  /// The child widget to protect with error boundary
  final Widget child;

  /// Optional callback for when a media error occurs
  final void Function(FlutterErrorDetails)? onError;

  /// Whether to show an error widget when media errors occur
  final bool showErrorWidget;

  /// Custom error widget to show instead of default
  final Widget Function(FlutterErrorDetails)? customErrorWidget;

  @override
  State<MediaErrorBoundary> createState() => _MediaErrorBoundaryState();
}

class _MediaErrorBoundaryState extends State<MediaErrorBoundary> {
  FlutterErrorDetails? _errorDetails;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    
    // Set up error handling for this boundary
    _setupErrorBoundary();
  }

  void _setupErrorBoundary() {
    // Store the original error handler
    final originalOnError = FlutterError.onError;

    // Set up custom error handling
    FlutterError.onError = (FlutterErrorDetails details) {
      // Check if this is a media-related error
      if (_isMediaRelatedError(details)) {
        _handleMediaError(details);
      } else {
        // Call original handler for non-media errors
        originalOnError?.call(details);
      }
    };
  }

  bool _isMediaRelatedError(FlutterErrorDetails details) {
    final errorString = details.exception.toString().toLowerCase();
    const mediaKeywords = [
      'webrtc',
      'mediastream',
      'getusermedia',
      'microphone',
      'camera',
      'audio',
      'video',
      'permission',
      'rtcpeerconnection',
      'iceconnection',
      'dtmf',
      'media_device',
      'audioplayer',
      'audiorecorder',
    ];

    return mediaKeywords.any(errorString.contains);
  }

  void _handleMediaError(FlutterErrorDetails details) {
    // Report to error handler service
    MediaErrorHandlerService.reportMediaError(
      Exception(details.exception),
      operation: 'ui_error_boundary',
      mediaType: 'widget_error',
      stackTrace: details.stack,
      additionalData: {
        'widget_context': details.context?.toString(),
        'error_summary': details.summary.toString(),
      },
    );

    // Call custom error callback if provided
    widget.onError?.call(details);

    // Update state to show error UI if enabled
    if (widget.showErrorWidget && mounted) {
      setState(() {
        _errorDetails = details;
        _hasError = true;
      });
    }
  }

  void _retry() {
    if (mounted) {
      setState(() {
        _errorDetails = null;
        _hasError = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_hasError && _errorDetails != null) {
      // Show custom error widget if provided
      if (widget.customErrorWidget != null) {
        return widget.customErrorWidget!(_errorDetails!);
      }

      // Show default media error UI
      return _buildDefaultErrorWidget(context);
    }

    // Normal child widget when no errors
    return widget.child;
  }

  Widget _buildDefaultErrorWidget(BuildContext context) {
    final theme = Theme.of(context);
    final errorMessage = MediaErrorHandlerService.getUserFriendlyErrorMessage(
      Exception(_errorDetails!.exception),
      'media',
    );

    return Material(
      child: Container(
        padding: const EdgeInsets.all(24),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Error icon
              Icon(
                Icons.error_outline,
                size: 64,
                color: theme.colorScheme.error,
              ),
              const SizedBox(height: 16),

              // Error title
              Text(
                'Media Error',
                style: theme.textTheme.headlineSmall?.copyWith(
                  color: theme.colorScheme.error,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),

              // Error message
              Text(
                errorMessage,
                style: theme.textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),

              // Action buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Retry button
                  ElevatedButton.icon(
                    onPressed: _retry,
                    icon: const Icon(Icons.refresh),
                    label: const Text('Try Again'),
                  ),
                  const SizedBox(width: 12),

                  // Go back button
                  OutlinedButton.icon(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.arrow_back),
                    label: const Text('Go Back'),
                  ),
                ],
              ),

              // Debug info (only in debug mode)
              if (kDebugMode) ...[
                const SizedBox(height: 24),
                ExpansionTile(
                  title: const Text('Debug Info'),
                  children: [
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        _errorDetails!.exception.toString(),
                        style: theme.textTheme.bodySmall?.copyWith(
                          fontFamily: 'monospace',
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

/// Convenience wrapper for wrapping call UI with media error boundary
class CallUIErrorBoundary extends StatelessWidget {
  const CallUIErrorBoundary({
    required this.child,
    this.onMediaError,
    super.key,
  });

  final Widget child;
  final void Function(FlutterErrorDetails)? onMediaError;

  @override
  Widget build(BuildContext context) {
    return MediaErrorBoundary(
      onError: (details) {
        // Log call-specific error
        MediaErrorHandlerService.reportMediaError(
          Exception(details.exception),
          operation: 'call_ui_error',
          mediaType: 'call_screen',
          additionalData: {
            'screen': 'calling_user',
            'context': 'call_ui_boundary',
          },
        );

        onMediaError?.call(details);
      },
      child: child,
    );
  }
}

/// Specialized error boundary for WebRTC operations
class WebRTCErrorBoundary extends StatelessWidget {
  const WebRTCErrorBoundary({
    required this.child,
    this.callId,
    this.onWebRTCError,
    super.key,
  });

  final Widget child;
  final String? callId;
  final void Function(FlutterErrorDetails)? onWebRTCError;

  @override
  Widget build(BuildContext context) {
    return MediaErrorBoundary(
      onError: (details) {
        // Log WebRTC-specific error
        MediaErrorHandlerService.reportMediaError(
          Exception(details.exception),
          operation: 'webrtc_ui_error',
          mediaType: 'webrtc',
          additionalData: {
            'call_id': callId,
            'context': 'webrtc_boundary',
          },
        );

        onWebRTCError?.call(details);
      },
      customErrorWidget: (details) => _buildWebRTCErrorWidget(context, details),
      child: child,
    );
  }

  Widget _buildWebRTCErrorWidget(
    BuildContext context,
    FlutterErrorDetails details,
  ) {
    final theme = Theme.of(context);

    return Material(
      child: Container(
        padding: const EdgeInsets.all(24),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.call_end,
                size: 64,
                color: theme.colorScheme.error,
              ),
              const SizedBox(height: 16),
              Text(
                'Call Connection Issue',
                style: theme.textTheme.headlineSmall?.copyWith(
                  color: theme.colorScheme.error,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              const Text(
                'There was a problem with the call connection. '
                'The call may have been disconnected.',
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('End Call'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
