import 'package:common/common.dart';
import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/contacts/contacts.dart';

class QuickContactOptions extends HookWidget {
  const QuickContactOptions({
    super.key,
    this.contacts = const [],
    this.displayedContact,
    this.viewMoreLength = 0,
    this.onViewMorePressed,
  });

  final List<ContactModel> contacts;
  final int viewMoreLength;
  final ContactModel? displayedContact;
  final VoidCallback? onViewMorePressed;

  @override
  Widget build(BuildContext context) {
    final firstContact = useMemoized<ContactModel>(
        () => displayedContact != null ? displayedContact! : contacts.first, [
      displayedContact,
      contacts,
    ]);

    final otherContacts = useMemoized<int>(
        () => viewMoreLength != 0
            ? viewMoreLength
            : contacts.skip(1).toList().length,
        [
          contacts,
          viewMoreLength,
        ]);

    return ListTile(
      title: Text(firstContact.name),
      minVerticalPadding: 0,
      horizontalTitleGap: 0,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16),
      subtitle: FroggyPhoneNumberWithFlag(
        isCentered: false,
        phoneNumber: '${firstContact.prefix}${firstContact.phoneNumber}',
      ),
      trailing: (otherContacts <= 0)
          ? null
          : InkWell(
              onTap: onViewMorePressed,
              child: RichText(
                strutStyle: const StrutStyle(
                  fontSize: 14,
                  height: 1.5,
                  forceStrutHeight: true,
                ),
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: '($otherContacts)',
                      style: const TextStyle(
                        color: FroggyColors.froggyGrey2,
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const WidgetSpan(
                      child: Icon(
                        Icons.keyboard_arrow_down_sharp,
                        color: FroggyColors.froggyGrey2,
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
