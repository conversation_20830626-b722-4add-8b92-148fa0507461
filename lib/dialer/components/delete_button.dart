import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:froggy_icons/froggy_icons.dart';
import 'package:froggytalk/dialer/dialer.dart';

class DeleteButton extends StatelessWidget {
  const DeleteButton({required this.width, required this.height, super.key});

  final double width;
  final double height;

  @override
  Widget build(BuildContext context) {
    final keypadBloc = context.read<KeypadBloc>();

    return NumPadButton.icon(
      icon: FroggyIconsList.backspace.toWidget(
        color: FroggyColors.black,
        width: 30,
      ),
      backgroundColor: FroggyColors.froggyCream,
      onLongPress: () {
        keypadBloc
          ..add(const KeypadEvent.reset())
          ..add(const KeypadEvent.searchedByDialingCode())
          ..add(const KeypadEvent.searchedContact());
      },
      onPressed: () {
        keypadBloc
          ..add(const KeypadEvent.deleteButtonPressed())
          ..add(const KeypadEvent.searchedByDialingCode())
          ..add(const KeypadEvent.searchedContact());
      },
      borderColor: FroggyColors.froggyGrey4,
      width: width,
      height: height,
    );
  }
}
