import 'package:common/common.dart';
import 'package:constants/constants.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:froggy_icons/froggy_icons.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/app/data/services/event_tracker.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:froggytalk/buy_credit/buy_credit.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:froggytalk/dialer/dialer.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:froggytalk/radio_stations/radio_stations.dart';
import 'package:money2/money2.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:permissions/permissions.dart';
import 'package:sip_ua/sip_ua.dart'  as sip_ua;
import 'package:utils/utils.dart';

class DialerButton extends StatefulHookWidget {
  const DialerButton({
    required this.width,
    required this.height,
    super.key,
    // this.onPressed,
  });

  final double width;
  final double height;
  // final VoidCallback? onPressed;

  @override
  State<DialerButton> createState() => _DialerButtonState();
}

class _DialerButtonState extends State<DialerButton>
    implements sip_ua.SipUaHelperListener {
  // AppLocalizations? l10n;
  DialerBloc? dialerBloc;
  KeypadBloc? keypadBloc;
  // ignore: unused_field
  bool isDialable = true;
  RecentCallsBloc? recentCallsBloc;
  sip_ua.SIPUAHelper? _helper;

  @override
  void initState() {
    super.initState();
    dialerBloc = context.read<DialerBloc>();
    keypadBloc = context.read<KeypadBloc>();
    recentCallsBloc = context.read<RecentCallsBloc>();

    _helper = context.read<sip_ua.SIPUAHelper>();
    _helper?.addSipUaHelperListener(this);
  }

  @override
  void deactivate() {
    super.deactivate();
    _helper?.removeSipUaHelperListener(this);
  }

  @override
  void callStateChanged(sip_ua.Call call, sip_ua.CallState state) {}

  @override
  void onNewMessage(sip_ua.SIPMessageRequest msg) {}

  @override
  void onNewNotify(sip_ua.Notify ntf) {}

  @override
  void onNewReinvite(sip_ua.ReInvite event) {}

  @override
  void registrationStateChanged(sip_ua.RegistrationState state) {}

  @override
  void transportStateChanged(sip_ua.TransportState state) {}

  void initialize() {
    final number = keypadBloc?.state.phoneNumber.value;

    if (!(number != null && number.isNotEmpty)) {
      setDialable(false);
    }
  }

  void setDialable(bool value) {
    setState(() {
      isDialable = value;
    });
  }

  String? phoneNumber;
  void setPhoneNumber(String phone) {
    setState(() {
      phoneNumber = phone;
    });

    if (phone.isEmpty) {
      setDialable(false);
    }
  }

  void routeBack() {
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    final helper = context.read<sip_ua.SIPUAHelper>();
    final hasInternet = useHasConnectivity();
    final authProfile = useAuthUser();
    final authCurrencySymbol = useAuthUserCurrencyCode();
    final currency = Currency.create(authCurrencySymbol!, 2);
    final l10n = context.l10n;
    
    final radioStationBloc = context.read<RadioStationsBloc>();
    final stopRadioStationsOnCall = useCallback(
      () {
        radioStationBloc.add(
          const RadioStationsEvent.stop(),
        );
      },
      [radioStationBloc],
    );
    
    final accountBalance = useMemoized(() {
      final accountBalance2 = authProfile?.accountBalance;
      if (accountBalance2 == null) {
        return Money.fromIntWithCurrency(0, currency);
      }

      final bigInt = Fixed.fromNum(num.parse(accountBalance2));
      final money2 = Money.fromFixedWithCurrency(bigInt, currency);
      return money2;
    });

    final currentPhoneNumberState = 
        context.select<KeypadBloc, DialerPadInput>(
      (bloc) {
        return bloc.state.phoneNumber;
      },
    );

    final callRates = context.select<KeypadBloc, Money>(
      (bloc) {
        final currentCallRates = bloc.state.callRates;
        if (currentCallRates == null) {
          return Money.fromIntWithCurrency(0, currency);
        }

        final bigInt = Fixed.fromNum(num.parse(currentCallRates));
        final money1 = Money.fromFixedWithCurrency(bigInt, currency);
        return money1;
      },
    );

    // final callRateErrorMessage = context.select<KeypadBloc, String?>(
    //   (bloc) {
    //     return bloc.state.callRatesErrorMessage;
    //   },
    // );

    final makeCall = useCallback(
      (
        String phoneNumber, {
        bool voiceOnly = true,
      }) async {
        try {
          // 1. Stop any existing audio (like radio stations)
          stopRadioStationsOnCall();
          
          // 2. Properly check permissions before proceeding
          if (defaultTargetPlatform == TargetPlatform.android ||
              defaultTargetPlatform == TargetPlatform.iOS) {
            final status = await Permission.microphone.request();
            
            if (status != PermissionStatus.granted) {
              // Handle permission denial with proper user feedback
              dialerBloc?.add(
                const DialerEvent.sendErrorMessage(
                  message: 'Microphone permission is required for calls',
                ),
              );
              return;
            }
          }

          // 3. Configure media constraints with better defaults
          final mediaConstraints = <String, dynamic>{
            'audio': true,
            'video': voiceOnly ? false : {
              'mandatory': <String, dynamic>{
                'minWidth': '640',
                'minHeight': '480',
                'minFrameRate': '30',
              },
              'facingMode': 'user',
            },
          };

          // 4. Get media stream with proper error handling
          MediaStream mediaStream;
          try {
            if (kIsWeb && !voiceOnly) {
              mediaStream = await navigator.mediaDevices
                  .getDisplayMedia(mediaConstraints);
              mediaConstraints['video'] = false;
              final userStream = await navigator.mediaDevices
                  .getUserMedia(mediaConstraints);
              final audioTracks = userStream.getAudioTracks();
              if (audioTracks.isNotEmpty) {
                await mediaStream.addTrack(audioTracks.first);
              }
            } else {
              mediaStream = await navigator.mediaDevices
                  .getUserMedia(mediaConstraints);
            }
          } on Exception catch (e) {
            // Log detailed error
            FroggyLogger.error('Media stream acquisition failed: $e');
            dialerBloc?.add(
              const DialerEvent.sendErrorMessage(
                message: 'Could not access microphone. '
                    'Please check your device settings.',
              ),
            );
            return;
          }

          // 5. Ensure phone number is properly formatted
          final trimmedPhoneNumber = 
              phoneNumber.trim().removeEmptySpaces();
          if (trimmedPhoneNumber.isEmpty) {
            dialerBloc?.add(
              const DialerEvent.sendErrorMessage(
                message: 'Please enter a valid phone number',
              ),
            );
            return;
          }

          // 6. Make the call with proper error handling
          try {
            await helper.call(
              trimmedPhoneNumber,
              voiceOnly: voiceOnly,
              mediaStream: mediaStream,
            );

            // 7. Update the dialer state
            dialerBloc?.add(
              DialerEvent.callStarted(phoneNumber: phoneNumber),
            );
          } catch (e) {
            // Handle call setup failure
            FroggyLogger.error('Call setup failed: $e');
            
            // Cleanup resources in case of failure
            mediaStream.getTracks()
                .forEach((track) => track.stop());
            
            dialerBloc?.add(
              const DialerEvent.sendErrorMessage(
                message: 'Call setup failed. Please try again.',
              ),
            );
          }
        } catch (e) {
          // Global error handler
          FroggyLogger.error('Unexpected error in makeCall: $e');
          dialerBloc?.add(
            const DialerEvent.sendErrorMessage(
              message: 'An unexpected error occurred. Please try again.',
            ),
          );
        }
      },
      [],
    );

    final showPermissionPermanentlyDeniedErrorModal = useCallback(
      () {
        RequestPermission.showModal(
          context,
          label: l10n.permissionPermanentlyDeniedMessage,
          allowAccessText: l10n.allowAccessButtonText,
          skipText: l10n.permissionButtonSkip,
          onPressed: openAppSettings,
        );
      },
      [],
    );

    final showPermissionDeniedErrorModal = useCallback(
      () {
        RequestPermission.showModal(
          context,
          label: l10n.permissionDeniedMessage,
          allowAccessText: l10n.allowAccessButtonText,
          skipText: l10n.permissionButtonSkip,
          onPressed: openAppSettings,
        );
      },
      [],
    );

    // final showPermissionAlert = useCallback(
    //   () {
    //     RequestPermission.showModal(
    //       context,
    //       label: l10n.permissionForMicrophoneTitle,
    //       allowAccessText: l10n.allowAccessButtonText,
    //       skipText: l10n.permissionButtonSkip,
    //       onPressed: () async {
    //         final status = await Permission.microphone.request();

    //         if (status == PermissionStatus.granted) {
    //           routeBack();
    //           WidgetsBinding.instance.addPostFrameCallback(
    //             (_) => makeCall(phoneNumber ?? ''),
    //           );
    //         } else {
    //           routeBack();
    //           WidgetsBinding.instance.addPostFrameCallback(
    //             (_) => showErrorModal(),
    //           );
    //         }
    //       },
    //     );
    //   },
    //   [],
    // );

    final onCallButtonPressed = useCallback(
      (
        String phoneNumber, {
        bool voiceOnly = true,
      }) async {
        // const permissionMic = Permission.microphone;
        // final isGranted = await permissionMic.isGranted;
        // final isPermdenied = await permissionMic.isPermanentlyDenied;

        await Permission.microphone.request().then((value) {
          if (value == PermissionStatus.granted) {
            makeCall(phoneNumber);
          } else if (value.isPermanentlyDenied) {
            showPermissionPermanentlyDeniedErrorModal();
          } else {
            showPermissionDeniedErrorModal();
          }
        });

        // if (isGranted) {
        //   await makeCall(phoneNumber);

        //   // WidgetsBinding.instance.addPostFrameCallback(
        //   //   (_) => showPermissionAlert(),
        //   // );
        // } else if (isPermdenied) {
        //   WidgetsBinding.instance.addPostFrameCallback(
        //     (_) => showErrorModal(),
        //   );
        // } else {
        //   WidgetsBinding.instance.addPostFrameCallback(
        //     (_) async => Permission.microphone.request(),
        //   );
        // }
      },
      [],
    );

    useEffect(() {
      context.read<EventTrackerService>().logEvent(
            schema: 'dialer_button_click',
            description: 'User is clicking the dial phone number button',
          );

      return null;
    });

    return BlocSelector<DialerBloc, DialerState, List<dynamic>>(
      selector: (state) {
        if (state.sipRegistrationStatus.isUnregistered) {
          // return [false, FroggyColors.black];
          return [false, FroggyColors.error];
        }

        if (state.sipRegistrationStatus.isFailed) {
          return [false, FroggyColors.error];
        }

        if (state.sipRegistrationStatus.isUnknown) {
          // return [false, FroggyColors.froggyGrey2];
          return [false, FroggyColors.froggyGrey2];
        }

        return [true, FroggyColors.primary];
      },
      builder: (context, sipRegistrationResults) {
        return BlocSelector<RecentCallsBloc, RecentCallsState, String>(
          selector: (state) {
            return '+${state.getLastDialedNumber.calledNumber}';
          },
          builder: (context, lastDialedNumber) {
            final isDialable = sipRegistrationResults[0] as bool;
            final dialerButtonColor = sipRegistrationResults[1] as Color;

            return Builder(
              builder: (context) {
                return NumPadButton.icon(
                  icon: FroggyIconsList.phoneSolid.toWidget(
                    width: 30,
                  ),
                  backgroundColor: dialerButtonColor,
                  onPressed: !hasInternet
                      ? null
                      : () {
                          if (currentPhoneNumberState.value == '') {
                            keypadBloc
                              ?..add(
                                KeypadEvent.setPhoneNumber(
                                  value: lastDialedNumber,
                                ),
                              )
                              ..add(const KeypadEvent.searchedContact());
                          } else {
                            /// Validates if user has
                            /// sufficient balance for the call based on:
                            /// 1. Call rate availability (must be > 0)
                            /// 2. User balance >= required call rate
                            final callRateInDecimal = callRates.toDecimal();
                            final balanceAsDecimal = 
                                accountBalance.toDecimal();

                            // First check if we have a
                            // valid call rate for this number
                            final hasValidCallRate = callRateInDecimal >
                                Fixed.fromInt(0).toDecimal();

                            // Then check if user has sufficient balance
                            final hasPerMinCredit = hasValidCallRate &&
                                balanceAsDecimal >= callRateInDecimal;

                            FroggyLogger.debug('Account Balance Check:\n'
                                '  Currency: $authCurrencySymbol\n'
                                '  Balance: $balanceAsDecimal\n'
                                '  Call Rate: $callRateInDecimal\n'
                                '  Has Valid Rate: $hasValidCallRate\n'
                                '  Has 1 min Credit: $hasPerMinCredit\n');

                            if (balanceAsDecimal <=
                                Fixed.fromInt(0).toDecimal()) {
                              showModal(message: l10n.outOfCreditLabel);
                            } else if (!hasPerMinCredit) {
                              showModal(
                                message: l10n.notEnoughCreditMessage,
                              );
                            } else {
                              final qPhoneNumber =
                                  currentPhoneNumberState.value;

                              if (isDialable &&
                                  currentPhoneNumberState.errorMessage ==
                                      null) {
                                FroggyLogger.debug('Dialing: $qPhoneNumber');
                                onCallButtonPressed(qPhoneNumber);
                              }
                            }
                          }
                        },
                  width: widget.width,
                  height: widget.height,
                );
              },
            );
          },
        );
      },
    );
  }

  void showModal({String message = 'Out of call credit'}) {
    showDialog<void>(
      context: context,
      builder: (context) => _BuildNoCreditModal(
        message: message,
      ),
    );
  }
}

class _BuildNoCreditModal extends StatelessWidget {
  const _BuildNoCreditModal({this.message = 'Out of call credit'});

  final String message;

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;

    return AlertDialog(
      title: FroggyIconsList.outOfCredit.toWidget(
        width: 100,
      ),
      content: Text(
        message,
        textAlign: TextAlign.center,
        style: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
      ),
      actions: <Widget>[
        SizedBox(
          width: double.infinity,
          child: TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: ElevatedButton.styleFrom(
              fixedSize: const Size.fromHeight(50),
            ),
            child: Text(l10n.cancelButtonText),
          ),
        ),
        FroggySpacer.y4(),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () {
              // Navigator.of(context).pop();
              Navigator.push(
                context,
                BuyCreditPage.route(bloc: context.read<CheckoutBloc>()),
              );
            },
            style: ElevatedButton.styleFrom(
              fixedSize: const Size.fromHeight(50),
            ),
            child: Text(l10n.buyCreditAppBarTitle),
          ),
        ),
      ],
    );
  }
}
