import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggy_icons/froggy_icons.dart';
import 'package:froggytalk/app/data/services/event_tracker.dart';
import 'package:froggytalk/dialer/dialer.dart';

class BuildCallingActionButtons extends HookWidget {
  const BuildCallingActionButtons({
    super.key,
    this.onEndCall,
    this.onToggleMute,
    this.onKeyPad,
    this.onSpeaker,
    this.isMuted,
    this.isSpeakerOn,
  });

  final VoidCallback? onEndCall;
  final VoidCallback? onToggleMute;
  final VoidCallback? onKeyPad;
  final void Function(bool value)? onSpeaker;
  final bool? isMuted;
  final bool? isSpeakerOn;

  @override
  Widget build(BuildContext context) {
    final bloc = context.read<DialerBloc>();
    // final menuController = useRef<MenuController>(MenuController());
    // final isOutputTypeOpened = useState<bool>(true);

    // final openMenu = useCallback(() {
    //   isOutputTypeOpened.value = true;
    //   menuController.value.open();
    // });

    // final closeMenu = useCallback(() {
    //   isOutputTypeOpened.value = false;
    //   menuController.value.close();
    // });

    // when call end button is pressed
    final onCallEndButtonPressed = useCallback(() {
      context.read<EventTrackerService>().logEvent(
            schema: 'call_end',
            description: 'User is ending call',
          );

      if (onEndCall != null) {
        onEndCall?.call();
      } else {
        bloc.add(const DialerEvent.hangedup());
      }
    });

    // when apps button is pressed
    final onAppsButtonPressed = useCallback(() {
      if (onKeyPad != null) {
        onKeyPad?.call();
      } else {
        Navigator.push(
          context,
          DtmfNumPad.route(),
        );
      }
    });

    final onMuteButtonPressed = useCallback(() {
      bloc.add(const DialerEvent.muteOn());
    });

    final onUnmuteButtonPressed = useCallback(() {
      bloc.add(const DialerEvent.muteOff());
    });

    // when speaker button is pressed
    final onSpeakerButtonPressed = useCallback(() {
      if (onSpeaker != null) {
        onSpeaker?.call(true);
      } else {
        bloc.add(const DialerEvent.switchSpeakerPhoneOn());
      }
    });

    final onEarpieceButtonPressed = useCallback(() {
      if (onSpeaker != null) {
        onSpeaker?.call(false);
      } else {
        bloc.add(const DialerEvent.disableSpeakerPhone());
      }
    });

    // ignore: unused_local_variable
    final onBluetoothButtonPressed = useCallback(() {
      bloc.add(const DialerEvent.switchToBluetoothHeadset());
      // closeMenu();
    });

    // Use MultiBlocBuilder to listen to both DialerBloc and AudioBloc
    // AudioBloc is the single source of truth for audio state
    return BlocBuilder<AudioBloc, AudioState>(
      builder: (context, audioState) {
        return Wrap(
          alignment: WrapAlignment.center,
          crossAxisAlignment: WrapCrossAlignment.center,
          runAlignment: WrapAlignment.center,
          runSpacing: 20,
          spacing: 20,
          children: [
            // if (state.isRinging)
            CallingUserButton(
              icon: FroggyIconsList.apps,
              onPressed: (_) => onAppsButtonPressed(),
              isActive: false,
            ),
            // if (state.isRinging)
            CallingUserButton(
              icon: FroggyIconsList.mute,
              onPressed: (_) {
                if (onToggleMute != null) {
                  onToggleMute?.call();
                } else {
                  (!audioState.isMuted)
                      ? onUnmuteButtonPressed()
                      : onMuteButtonPressed();
                }
              },
              changeIconColorToRed: true,
              isActive: isMuted ?? audioState.isMuted,
              // onSelectedPressed: onMuteButtonPressed,
              // onUnSelectedPressed: onUnmuteButtonPressed,
            ),
            CallingUserButton(
              icon: FroggyIconsList.speaker,
              onPressed: (isActive) =>
                  (isSpeakerOn ?? audioState.isSpeakerEnabled)
                      ? onEarpieceButtonPressed()
                      : onSpeakerButtonPressed(),
              // changeIconColorToRed: false,
              isActive: isSpeakerOn ?? audioState.isSpeakerEnabled,
              // onSelectedPressed: onSpeakerButtonPressed,
              // onUnSelectedPressed: onEarpieceButtonPressed,
            ),
            // if (state.isRinging)
            // MenuAnchor(
            //   style: const MenuStyle(
            //     elevation: WidgetStatePropertyAll(0),
            //     backgroundColor:
            //         WidgetStatePropertyAll(FroggyColors.froggyGrey1),
            //   ),
            //   controller: menuController.value,
            //   consumeOutsideTap: true,
            //   alignmentOffset: const Offset(-20, 5),
            //   menuChildren: [
            //     if (state.audioTrackMode.isSpeakerDisabled)
            //       MenuItemButton(
            //         onPressed: onSpeakerButtonPressed,
            //         leadingIcon: FroggyIconsList.speaker.toWidget(
            //           color: FroggyColors.white,
            //           height: 16,
            //           width: 16,
            //         ),
            //         child: const Text(
            //           'Speaker',
            //           style: TextStyle(
            //             color: FroggyColors.white,
            //             fontSize: 12,
            //             fontWeight: FontWeight.w400,
            //           ),
            //         ),
            //       ),
            //     if (state.audioTrackMode.isSpeakerEnabled)
            //       MenuItemButton(
            //         onPressed: onEarpieceButtonPressed,
            //         leadingIcon: FroggyIconsList.phoneSolid.toWidget(
            //           color: FroggyColors.white,
            //           height: 16,
            //           width: 16,
            //         ),
            //         child: const Text(
            //           'Phone',
            //           style: TextStyle(
            //             color: FroggyColors.white,
            //             fontSize: 12,
            //             fontWeight: FontWeight.w400,
            //           ),
            //         ),
            //       ),
            //     // if (!state.audioTrackMode.isBluetoothAudioEnabled)
            //     //   MenuItemButton(
            //     //     onPressed: onBluetoothButtonPressed,
            //     //     leadingIcon: FroggyIconsList.bluetooth.toWidget(
            //     //       color: FroggyColors.white,
            //     //       height: 16,
            //     //       width: 16,
            //     //     ),
            //     //     child: const Text(
            //     //       'Bluetooth',
            //     //       style: TextStyle(
            //     //         color: FroggyColors.white,
            //     //         fontSize: 12,
            //     //         fontWeight: FontWeight.w400,
            //     //       ),
            //     //     ),
            //     //   ),
            //   ],
            //   child: IconButton(
            //     icon: Builder(
            //       builder: (context) {
            //         if (state.audioTrackMode.isSpeakerEnabled) {
            //           return FroggyIconsList.phoneSolid.toWidget(
            //             color: isOutputTypeOpened.value
            //                 ? FroggyColors.white
            //                 : FroggyColors.froggyGrey1,
            //           );
            //         }

            //         if (state.audioTrackMode.isBluetoothAudioEnabled) {
            //           return FroggyIconsList.bluetooth.toWidget(
            //             color: isOutputTypeOpened.value
            //                 ? FroggyColors.white
            //                 : FroggyColors.froggyGrey1,
            //           );
            //         }

            //         if (state.audioTrackMode.isWiredHeadsetEnabled) {
            //           return Icon(
            //             Icons.headphones,
            //             size: 24,
            //             color: isOutputTypeOpened.value
            //                 ? FroggyColors.white
            //                 : FroggyColors.froggyGrey1,
            //           );
            //         }

            //         return FroggyIconsList.speaker.toWidget(
            //           color: isOutputTypeOpened.value
            //               ? FroggyColors.white
            //               : FroggyColors.froggyGrey1,
            //         );
            //       },
            //     ),
            //     style: ButtonStyle(
            //       backgroundColor: WidgetStatePropertyAll(
            //         isOutputTypeOpened.value
            //             ? FroggyColors.froggyGrey1
            //             : FroggyColors.white,
            //       ),
            //       padding: const WidgetStatePropertyAll(
            //         EdgeInsets.all(20),
            //       ),
            //     ),
            //     onPressed: openMenu,
            //   ),
            // ),
            CallingUserButton(
              isActive: false,
              icon: FroggyIconsList.callEnd,
              backgroundColor: FroggyColors.froggyError,
              onPressed: (_) {
                onCallEndButtonPressed.call();
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }
}
