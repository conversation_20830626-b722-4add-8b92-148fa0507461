import 'package:constants/constants.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggy_icons/froggy_icons.dart';
import 'package:froggytalk/dialer/dialer.dart';

/// Optimized calling action buttons using BlocSelector for granular
/// state selection.
/// This component only rebuilds when the specific audio state
/// properties change,
/// significantly reducing unnecessary rebuilds during call state
/// changes.
///
/// **Performance optimizations:**
/// - Uses BlocSelector to select only required audio state
/// - Leverages flutter_hooks for memoized callbacks
/// - Implements immutable value classes for state comparison
/// - Cached stateless widgets where possible
class OptimizedCallingActionButtons extends HookWidget {
  const OptimizedCallingActionButtons({
    super.key,
    this.onEndCall,
    this.onToggleMute,
    this.onKeyPad,
    this.onSpeaker,
    this.isMuted,
    this.isSpeakerOn,
  });

  final VoidCallback? onEndCall;
  final VoidCallback? onToggleMute;
  final VoidCallback? onKeyPad;
  final void Function(bool value)? onSpeaker;
  final bool? isMuted;
  final bool? isSpeakerOn;

  @override
  Widget build(BuildContext context) {
    final bloc = context.read<DialerBloc>();

    // Memoized callbacks to prevent unnecessary rebuilds
    final onMuteButtonPressed = useCallback(() {
      bloc.add(const DialerEvent.muteOn());
    }, [bloc],);

    final onUnmuteButtonPressed = useCallback(() {
      bloc.add(const DialerEvent.muteOff());
    }, [bloc],);

    final onSpeakerButtonPressed = useCallback(() {
      if (onSpeaker != null) {
        onSpeaker?.call(true);
      } else {
        bloc.add(const DialerEvent.switchSpeakerPhoneOn());
      }
    }, [bloc, onSpeaker],);

    final onEarpieceButtonPressed = useCallback(() {
      if (onSpeaker != null) {
        onSpeaker?.call(false);
      } else {
        bloc.add(const DialerEvent.disableSpeakerPhone());
      }
    }, [bloc, onSpeaker],);

    final onAppsButtonPressed = useCallback(() {
      onKeyPad?.call();
    }, [onKeyPad],);

    // Use BlocSelector to select only the audio state information
    // This prevents rebuilds when other parts of DialerState change
    return BlocSelector<DialerBloc, DialerState, AudioStateInfo>(
      selector: (state) => state.audioStateInfo,
      builder: (context, audioState) {
        return _CallingButtonsLayout(
          audioState: audioState,
          onMutePressed: () {
            if (onToggleMute != null) {
              onToggleMute?.call();
            } else {
              audioState.isMuted
                  ? onUnmuteButtonPressed()
                  : onMuteButtonPressed();
            }
          },
          onSpeakerPressed: (isActive) =>
              (isSpeakerOn ?? !audioState.isSpeakerEnabled)
                  ? onSpeakerButtonPressed()
                  : onEarpieceButtonPressed(),
          onAppsPressed: onAppsButtonPressed,
          onEndCallPressed: onEndCall,
          isMutedOverride: isMuted,
          isSpeakerOnOverride: isSpeakerOn,
        );
      },
    );
  }
}

/// Internal layout widget that's stateless for optimal performance.
/// This widget is cached by Flutter since it's stateless and only
/// rebuilds when its parameters change.
class _CallingButtonsLayout extends StatelessWidget {
  const _CallingButtonsLayout({
    required this.audioState,
    required this.onMutePressed,
    required this.onSpeakerPressed,
    required this.onAppsPressed,
    this.onEndCallPressed,
    this.isMutedOverride,
    this.isSpeakerOnOverride,
  });

  final AudioStateInfo audioState;
  final VoidCallback onMutePressed;
  final void Function(bool isActive) onSpeakerPressed;
  final VoidCallback onAppsPressed;
  final VoidCallback? onEndCallPressed;
  final bool? isMutedOverride;
  final bool? isSpeakerOnOverride;

  @override
  Widget build(BuildContext context) {
    return Wrap(
      alignment: WrapAlignment.center,
      crossAxisAlignment: WrapCrossAlignment.center,
      runAlignment: WrapAlignment.center,
      runSpacing: 20,
      spacing: 20,
      children: [
        // Apps/Keypad button
        CallingUserButton(
          icon: FroggyIconsList.apps,
          onPressed: (_) => onAppsPressed(),
          isActive: false,
        ),
        
        // Mute button - optimized with override support
        CallingUserButton(
          icon: FroggyIconsList.mute,
          onPressed: (_) => onMutePressed(),
          changeIconColorToRed: true,
          isActive: isMutedOverride ?? audioState.isMuted,
        ),
        
        // Speaker button - optimized with override support
        CallingUserButton(
          icon: FroggyIconsList.speaker,
          onPressed: onSpeakerPressed,
          isActive: isSpeakerOnOverride ?? audioState.isSpeakerEnabled,
        ),
        
        // End call button
        if (onEndCallPressed != null)
          CallingUserButton(
            icon: FroggyIconsList.callEnd,
            onPressed: (_) => onEndCallPressed?.call(),
            changeIconColorToRed: true,
            isActive: false,
          ),
      ],
    );
  }
}

/// Optimized call timer widget that only rebuilds when timer changes.
/// Uses BlocSelector to select only the connection info containing
/// the timer.
class OptimizedCallTimer extends StatelessWidget {
  const OptimizedCallTimer({
    super.key,
    this.style,
  });

  final TextStyle? style;

  @override
  Widget build(BuildContext context) {
    return BlocSelector<DialerBloc, DialerState, CallConnectionInfo>(
      selector: (state) => state.connectionInfo,
      builder: (context, connectionInfo) {
        // Only show timer when call is connected
        if (!connectionInfo.isConnected) {
          return const SizedBox.shrink();
        }

        return Center(
          child: Text(
            connectionInfo.formattedDuration,
            style: style ??
                const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                ),
          ),
        );
      },
    );
  }
}

/// Optimized call status display that only rebuilds when status
/// changes.
class OptimizedCallStatusDisplay extends StatelessWidget {
  const OptimizedCallStatusDisplay({
    super.key,
    this.style,
  });

  final TextStyle? style;

  @override
  Widget build(BuildContext context) {
    return BlocSelector<DialerBloc, DialerState, CallDisplayInfo>(
      selector: (state) => state.displayInfo,
      builder: (context, displayInfo) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (displayInfo.phoneNumber != null)
              Text(
                displayInfo.phoneNumber!,
                style: style ??
                    const TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                    ),
              ),
            const SizedBox(height: 8),
            Text(
              displayInfo.displayStatus,
              style: (style ?? const TextStyle()).copyWith(
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        );
      },
    );
  }
}

/// Optimized registration status indicator for dialer button.
/// Only rebuilds when registration state changes.
class OptimizedRegistrationIndicator extends StatelessWidget {
  const OptimizedRegistrationIndicator({
    required this.child, super.key,
  });

  final Widget Function(bool canMakeCall, Color indicatorColor) child;

  @override
  Widget build(BuildContext context) {
    return BlocSelector<DialerBloc, DialerState, RegistrationInfo>(
      selector: (state) => state.registrationInfo,
      builder: (context, registrationInfo) {
        Color indicatorColor;
        
        if (registrationInfo.isFailed) {
          indicatorColor = FroggyColors.error;
        } else if (registrationInfo.isPending) {
          indicatorColor = FroggyColors.froggyGrey2;
        } else if (registrationInfo.isRegistered) {
          indicatorColor = FroggyColors.primary;
        } else {
          indicatorColor = FroggyColors.froggyGrey2;
        }

        return child(registrationInfo.canMakeCall, indicatorColor);
      },
    );
  }
}

/// Performance monitoring widget for development.
/// Helps identify unnecessary rebuilds during development.
@visibleForTesting
class PerformanceMonitor extends StatelessWidget {
  const PerformanceMonitor({
    required this.name, required this.child, super.key,
  });

  final String name;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      print('Rebuilding $name at ${DateTime.now()}');
    }
    return child;
  }
}
