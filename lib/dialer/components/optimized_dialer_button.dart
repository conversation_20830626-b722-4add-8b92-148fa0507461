import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggy_icons/froggy_icons.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:froggytalk/dialer/dialer.dart';

/// Optimized dialer button that only rebuilds when registration
/// state changes.
/// 
/// This component uses BlocSelector with computed properties to
/// minimize rebuilds,
/// improving performance during SIP registration state changes and
/// other unrelated state updates.
/// 
/// **Performance optimizations:**
/// - BlocSelector for registration state only
/// - Cached button colors and states
/// - Memoized callbacks with flutter_hooks
/// - Immutable value comparison
class OptimizedDialerButton extends HookWidget {
  const OptimizedDialerButton({
    required this.width, required this.height, super.key,
    this.onPressed,
  });

  final double width;
  final double height;
  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    // Memoized callback to prevent unnecessary rebuilds
    final handlePressed = useCallback(() {
      onPressed?.call();
    }, [onPressed],);

    return SizedBox(
      width: width,
      height: height,
      child: OptimizedRegistrationIndicator(
        child: (canMakeCall, indicatorColor) {
          return BlocSelector<RecentCallsBloc, RecentCallsState, String>(
            selector: (state) =>
                '+ 24{state.getLastDialedNumber.calledNumber}',
            builder: (context, lastDialedNumber) {
              return _DialerButtonLayout(
                canMakeCall: canMakeCall,
                indicatorColor: indicatorColor,
                lastDialedNumber: lastDialedNumber,
                onPressed: handlePressed,
              );
            },
          );
        },
      ),
    );
  }
}

/// Internal layout widget for the dialer button.
/// Stateless for optimal caching and performance.
class _DialerButtonLayout extends StatelessWidget {
  const _DialerButtonLayout({
    required this.canMakeCall,
    required this.indicatorColor,
    required this.lastDialedNumber,
    this.onPressed,
  });

  final bool canMakeCall;
  final Color indicatorColor;
  final String lastDialedNumber;
  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: indicatorColor,
        boxShadow: [
          BoxShadow(
            color: indicatorColor.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: canMakeCall ? onPressed : null,
          borderRadius: BorderRadius.circular(50),
          child: Container(
            padding: const EdgeInsets.all(20),
            child: Center(
              child: FroggyIconsList.phoneSolid.toWidget(
                color: canMakeCall ? Colors.white : Colors.grey,
                height: 24,
                width: 24,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// Optimized keypad input widget that uses BlocSelector for phone
/// number state.
class OptimizedKeypadInput extends HookWidget {
  const OptimizedKeypadInput({
    super.key,
    this.onChanged,
  });

  final ValueChanged<String>? onChanged;

  @override
  Widget build(BuildContext context) {
    final textController = useTextEditingController();
    final scrollController = useScrollController();
    final focusNode = useFocusNode();

    return BlocSelector<KeypadBloc, KeypadState, String>(
      selector: (state) => state.phoneNumber.value,
      builder: (context, phoneNumber) {
        // Update controller when phone number changes
        useEffect(() {
          if (textController.text != phoneNumber) {
            textController.text = phoneNumber;
            textController.selection = TextSelection.fromPosition(
              TextPosition(offset: phoneNumber.length),
            );
          }
          
          // Auto-scroll for long numbers
          if (phoneNumber.length > 10) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (scrollController.hasClients) {
                scrollController.animateTo(
                  scrollController.position.maxScrollExtent,
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeOut,
                );
              }
            });
          }
          return null;
        }, [phoneNumber],);

        return SizedBox(
          width: 350,
          child: TextField(
            controller: textController,
            focusNode: focusNode,
            scrollController: scrollController,
            textDirection: TextDirection.ltr,
            style: const TextStyle(fontSize: 35),
            decoration: const InputDecoration(
              contentPadding: EdgeInsets.symmetric(
                horizontal: 2,
                vertical: 10,
              ),
            ),
            enabled: true,
            readOnly: true,
            keyboardType: TextInputType.none,
            enableInteractiveSelection: true,
            textAlign: TextAlign.center,
            scrollPhysics: const BouncingScrollPhysics(),
            textAlignVertical: TextAlignVertical.center,
            scrollPadding: EdgeInsets.zero,
          ),
        );
      },
    );
  }
}

/// Optimized error display widget for keypad.
class OptimizedKeypadError extends StatelessWidget {
  const OptimizedKeypadError({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<KeypadBloc, KeypadState, String?>(
      selector: (state) => state.callRatesErrorMessage,
      builder: (context, errorMessage) {
        if (errorMessage == null || errorMessage.isEmpty) {
          return const SizedBox.shrink();
        }

        return Container(
          margin: const EdgeInsets.symmetric(
            horizontal: 20, vertical: 8,
          ),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: FroggyColors.error.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: FroggyColors.error.withOpacity(0.3),
            ),
          ),
          child: Row(
            children: [
              const Icon(
                Icons.error_outline,
                color: FroggyColors.error,
                size: 16,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  errorMessage,
                  style: const TextStyle(
                    color: FroggyColors.error,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

/// Optimized country info display for dialer.
class OptimizedCountryInfo extends StatelessWidget {
  const OptimizedCountryInfo({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<KeypadBloc, KeypadState, dynamic>(
      selector: (state) => state.country,
      builder: (context, country) {
        if (country == null) return const SizedBox.shrink();

        return SizedBox.fromSize(
          size: const Size.fromHeight(30),
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 16, vertical: 4,
            ),
            decoration: BoxDecoration(
              color: FroggyColors.froggyGrey1.withOpacity(0.3),
              borderRadius: BorderRadius.circular(15),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Flag placeholder
                Container(
                  width: 20,
                  height: 14,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(2),
                    color: Colors.grey.withOpacity(0.5),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  country.code != null
                      ? '(${country.dialingCode}) ${country.name}'
                      : '(+31) Netherlands',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
