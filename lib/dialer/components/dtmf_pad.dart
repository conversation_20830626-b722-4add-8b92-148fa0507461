import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_dtmf/dtmf.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/dialer/dialer.dart';
import 'package:utils/utils.dart';

class DtmfNumPad extends HookWidget {
  const DtmfNumPad({super.key, this.onChanged, this.onBackPressed});

  final void Function(String number)? onChanged;
  final VoidCallback? onBackPressed;

  static Route<Object?> route() {
    return MaterialPageRoute<Object?>(
      builder: (_) => const DtmfNumPad(),
      barrierDismissible: true,
      fullscreenDialog: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    final bloc = context.read<DialerBloc>();
    final controller = useTextEditingController();
    final focusNode = useFocusNode();
    final mediaHeight = MediaQuery.sizeOf(context).height;
    final scrollController = useScrollController();
    final dynamicFontSize = useState<double?>(null);

    // write a call back that scrolls the number input to the right
    // so that the cursor is visible
    final scrollNumberInput = useCallback(
      () {
        if ((mediaHeight >= 700 && controller.text.length >= 17) ||
            (mediaHeight <= 700 && controller.text.length >= 14)) {
          scrollController.animateTo(
            scrollController.position.maxScrollExtent + 20,
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeInOut,
          );
        }
      },
      [],
    );

    // create a call back that will adjust font size based on the screen height
    final adjustFontSize = useCallback(
      (String number) {
        // when the screen size is large
        if (mediaHeight >= 700) {
          if (number.length >= 10 && number.length < 20) {
            dynamicFontSize.value = 30;
          }

          if (number.length < 10) {
            dynamicFontSize.value = 40;
          }
        } else {
          dynamicFontSize.value = 20;
        }
      },
      [],
    );

    final onNumpadPressed = useCallback(
      (String number) async {
        // play dtmf sound
        await Dtmf.playTone(
          digits: number,
          samplingRate: 80000,
          forceMaxVolume: true,
        );

        final currentText = controller.text;
        if (number == '' || number == 'wipe-x') {
          controller.text = '';
          dynamicFontSize.value = mediaHeight >= 700 ? 40 : 20;
          return;
        }

        final currentSelection = controller.selection;
        final insertOffset = currentSelection.baseOffset > 0
            ? currentSelection.baseOffset
            : currentText.length;
        final newText = currentText.substring(0, insertOffset) +
            number +
            currentText.substring(insertOffset);

        controller
          // update the input text
          ..text = newText
          // scroll the number input to the right
          ..selection = TextSelection.collapsed(
            offset: insertOffset + number.length,
          );

        scrollNumberInput();
        adjustFontSize(newText);
        onChanged?.call(newText);
      },
      [controller.text],
    );

    // final routeBack = useCallback(
    //   () {
    //     Navigator.of(context).pop();
    //   },
    //   [],
    // );

    // final showErrorModal = useCallback(
    //   () {
    //     RequestPermission.showErrorModal(
    //       context,
    //       label: 'Permission denied',
    //       onPressed: () {},
    //     );
    //   },
    //   [],
    // );

    useEffect(
      () {
        void listenOnNumPadValueChanged() {
          if (controller.text.isNotEmpty) {
            bloc.add(DialerEvent.sendDTMF(value: controller.text));
          }
        }

        controller.addListener(listenOnNumPadValueChanged);

        return () {
          controller.removeListener(listenOnNumPadValueChanged);
        };
      },
      [controller.text],
    );

    return BlocConsumer<DialerBloc, DialerState>(
      listener: (context, state) {
        // if (state.status == DialerStatus.connecting) {
        //   WidgetsBinding.instance.addPostFrameCallback((_) {
        //     setTimeout(
        //       computation: () => Navigator.push(
        //         context,
        //         CallingUserPage.route(),
        //       ),
        //       seconds: 2,
        //     );
        //   });
        // }
      },
      builder: (context, state) => Center(
        child: Scaffold(
          appBar: AppBar(
            leading: CloseButton(
              onPressed: onBackPressed,
            ),
          ),
          body: Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Spacer(),
                SizedBox(
                  width: 300,
                  child: GestureDetector(
                    onTap: () => hideKeyboard(context),
                    child: TextField(
                      controller: controller,
                      focusNode: focusNode,
                      style: TextStyle(
                        fontSize: dynamicFontSize.value ??
                            (mediaHeight >= 700 ? 40 : 20),
                      ),
                      decoration: const InputDecoration(
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 2,
                          vertical: 10,
                        ),
                      ),
                      enabled: true,
                      readOnly: true,
                      keyboardType: TextInputType.none,
                      enableInteractiveSelection: true,
                      textAlign: TextAlign.center,
                      scrollPhysics: const BouncingScrollPhysics(),
                      textAlignVertical: TextAlignVertical.center,
                      scrollPadding: EdgeInsets.zero,
                      scrollController:
                          scrollController, // Allows manual scrolling
                    ),
                  ),
                ),
                Flexible(
                  flex: 8,
                  child: FractionallySizedBox(
                    widthFactor: mediaHeight >= 700 ? 0.8 : 0.6,
                    child: NumPad(
                      onNumpadPressed: onNumpadPressed,
                      // onDialButtonPressed: () {},
                      // onDeleteButtonPressed: () {},
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
