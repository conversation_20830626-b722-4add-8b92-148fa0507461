import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:froggytalk/dialer/dialer.dart';

/// Demo widget showing how to use the specialized blocs for better
/// separation of concerns
/// This demonstrates the refactored architecture where:
/// - AudioBloc handles audio routing and speaker/microphone controls
/// - RegistrationBloc manages SIP registration state
/// - CallTimerBloc handles call duration timing
/// - DialerBloc focuses on core call initiation and management
class SpecializedBlocsDemo extends StatelessWidget {
  const SpecializedBlocsDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Specialized Blocs Demo'),
      ),
      body: const Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Refactored Architecture Demo',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            <PERSON><PERSON><PERSON><PERSON>(height: 20),
            _RegistrationSection(),
            <PERSON><PERSON><PERSON><PERSON>(height: 20),
            _AudioControlsSection(),
            <PERSON><PERSON><PERSON><PERSON>(height: 20),
            _CallTimerSection(),
            <PERSON><PERSON><PERSON><PERSON>(height: 20),
            _DialerStatusSection(),
          ],
        ),
      ),
    );
  }
}

/// Registration status using the specialized RegistrationBloc
class _RegistrationSection extends StatelessWidget {
  const _RegistrationSection();

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'SIP Registration (RegistrationBloc)',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            BlocBuilder<RegistrationBloc, RegistrationState>(
              builder: (context, state) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Status: ${state.status.name}'),
                    Text('Username: ${state.sipUsername ?? 'Not set'}'),
                    if (state.lastError != null)
                      Text(
                        'Error: ${state.lastError}',
                        style: const TextStyle(color: Colors.red),
                      ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: () {
                        // Example registration with demo credentials
                        context.read<RegistrationBloc>().add(
                              const RegistrationEvent.registerRequested(
                                username: 'demo_user',
                                password: 'demo_pass',
                              ),
                            );
                      },
                      child: const Text('Register'),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

/// Audio controls using the specialized AudioBloc
class _AudioControlsSection extends StatelessWidget {
  const _AudioControlsSection();

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Audio Controls (AudioBloc)',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            BlocBuilder<AudioBloc, AudioState>(
              builder: (context, state) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Audio Mode: ${state.audioTrackMode.name}'),
                    Text('Muted: ${state.isMuted}'),
                    Text('Speaker: ${state.isSpeakerEnabled}'),
                    Text('Output: ${state.audioOutput}'),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        ElevatedButton(
                          onPressed: () {
                            context.read<AudioBloc>().add(
                                  const AudioEvent.speakerToggled(),
                                );
                          },
                          child: Text(
                            state.isSpeakerEnabled
                                ? 'Disable Speaker'
                                : 'Enable Speaker',
                          ),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: () {
                            context.read<AudioBloc>().add(
                                  const AudioEvent.muteToggled(),
                                );
                          },
                          child: Text(
                            state.isMuted ? 'Unmute' : 'Mute',
                          ),
                        ),
                      ],
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

/// Call timer using the specialized CallTimerBloc
class _CallTimerSection extends StatelessWidget {
  const _CallTimerSection();

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Call Timer (CallTimerBloc)',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            BlocBuilder<CallTimerBloc, CallTimerState>(
              builder: (context, state) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Duration: ${state.formattedTime}'),
                    Text('Running: ${state.isRunning}'),
                    Text('Paused: ${state.isPaused}'),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        ElevatedButton(
                          onPressed: () {
                            context.read<CallTimerBloc>().add(
                                  const CallTimerEvent.timerStarted(),
                                );
                          },
                          child: const Text('Start Timer'),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: () {
                            context.read<CallTimerBloc>().add(
                                  const CallTimerEvent.timerPaused(),
                                );
                          },
                          child: const Text('Pause'),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: () {
                            context.read<CallTimerBloc>().add(
                                  const CallTimerEvent.timerStopped(),
                                );
                          },
                          child: const Text('Stop'),
                        ),
                      ],
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

/// Core dialer status still using DialerBloc but with reduced
/// responsibilities
class _DialerStatusSection extends StatelessWidget {
  const _DialerStatusSection();

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Dialer Status (DialerBloc - Simplified)',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            BlocBuilder<DialerBloc, DialerState>(
              builder: (context, state) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Status: ${state.status.name}'),
                    Text(
                      'Phone Number: ${state.phoneNumber ?? 'None'}',
                    ),
                    Text(
                      'Country Code: ${state.countryCode ?? 'None'}',
                    ),
                    if (state.message?.isNotEmpty ?? false)
                      Text(
                        'Message: ${state.message}',
                        style: const TextStyle(color: Colors.orange),
                      ),
                    const SizedBox(height: 8),
                    const Text(
                      'Note: Timer, Audio, and Registration logic '
                      'are now handled by specialized blocs above.',
                      style: TextStyle(
                        fontSize: 12,
                        fontStyle: FontStyle.italic,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
