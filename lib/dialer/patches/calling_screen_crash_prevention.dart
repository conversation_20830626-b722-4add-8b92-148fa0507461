// Enhanced calling screen implementation with crash prevention
// This patch demonstrates how to integrate the crash prevention measures
// into the existing calling_user.dart file

import 'package:flutter/material.dart';
import 'package:froggytalk/dialer/utils/call_termination_debugger.dart';
import 'package:froggytalk/dialer/widgets/call_screen_error_boundary.dart';
import 'package:sip_ua/sip_ua.dart';

/// Enhanced patch for the calling screen with crash prevention
/// Apply these changes to your existing calling_user.dart file
mixin CallingScreenCrashPrevention<T extends StatefulWidget> on State<T>
    implements SipUaHelperListener {
  final CallTerminationDebugger _debugger = CallTerminationDebugger();
  bool _isDisposed = false;
  bool _crashPreventionActive = false;

  /// Enhanced initialization with crash prevention
  void initializeWithCrashPrevention(String phoneNumber) {
    _debugger.startCallSession(phoneNumber);
    _crashPreventionActive = true;

    // Set up crash detection
    _setupCrashDetection();
  }

  /// Set up crash detection mechanisms
  void _setupCrashDetection() {
    // Override Flutter error handling
    final originalOnError = FlutterError.onError;
    FlutterError.onError = (FlutterErrorDetails details) {
      _handleFlutterError(details);
      originalOnError?.call(details);
    };
  }

  /// Handle Flutter errors specifically during call sessions
  void _handleFlutterError(FlutterErrorDetails details) {
    if (!_crashPreventionActive) return;

    _debugger.trackException('FlutterError', details.exception, details.stack);

    // Check if this is a call-related error that could cause a crash
    if (_isCallRelatedError(details)) {
      _preventCrash(details);
    }
  }

  /// Check if error is call-related
  bool _isCallRelatedError(FlutterErrorDetails details) {
    final errorString = details.exception.toString().toLowerCase();
    final stackString = details.stack.toString().toLowerCase();

    final crashPatterns = [
      'rtcsession',
      'call ended',
      'call failed',
      'sipuahelper',
      'webrtc',
      'mediastream',
      'peerconnection',
      'state error',
      'disposed',
      'invalid state',
    ];

    return crashPatterns.any(
      (pattern) =>
          errorString.contains(pattern) || stackString.contains(pattern),
    );
  }

  /// Prevent crash by handling error gracefully
  void _preventCrash(FlutterErrorDetails details) {
    debugPrint(
      '[CrashPrevention] Preventing potential crash: ${details.exception}',
    );

    // Schedule safe cleanup and exit
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && !_isDisposed) {
        _safeCleanupAndExit('Error prevented crash');
      }
    });
  }

  /// Enhanced callStateChanged with crash prevention
  @override
  void callStateChanged(Call call, CallState callState) {
    if (_isDisposed) {
      debugPrint('[CrashPrevention] Ignoring state change - widget disposed');
      return;
    }

    try {
      _debugger.trackCallStateChange(
        callState.state.toString(),
        callState.originator,
        callState.cause?.cause,
      );

      // Special handling for termination states
      switch (callState.state) {
        case CallStateEnum.ENDED:
        case CallStateEnum.FAILED:
          _handleTerminationStateSafely(call, callState);
        // ignore: no_default_cases
        default:
          _handleNormalState(call, callState);
      }
    } catch (e, stackTrace) {
      _debugger.trackException('callStateChanged', e, stackTrace);
      _safeCleanupAndExit('Exception in call state change');
    }
  }

  /// Handle termination states with extra safety
  void _handleTerminationStateSafely(Call call, CallState callState) {
    debugPrint(
      '[CrashPrevention] Handling termination state: ${callState.state}',
    );

    // Check if this is a receiver-initiated termination
    if (callState.originator == 'remote') {
      debugPrint(
        '[CrashPrevention] Remote termination detected - '
        'using crash prevention',
      );
      _handleReceiverTermination(callState);
    } else {
      _handleUserTermination(callState);
    }
  }

  /// Handle receiver dropping the call (main crash scenario)
  void _handleReceiverTermination(CallState callState) {
    debugPrint('[CrashPrevention] Receiver dropped call - preventing crash');

    // Delay processing to avoid race conditions
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted && !_isDisposed) {
        _safeCleanupAndExit('Call ended by receiver');
      }
    });
  }

  /// Handle user ending the call
  void _handleUserTermination(CallState callState) {
    debugPrint('[CrashPrevention] User ended call');
    _safeCleanupAndExit('Call ended by user');
  }

  /// Handle normal call states
  void _handleNormalState(Call call, CallState callState) {
    // Implement your existing call state handling here
    // This is where you'd call your original callStateChanged logic
    _handleOriginalCallState(call, callState);
  }

  /// Safe cleanup and exit
  void _safeCleanupAndExit(String reason) {
    if (_isDisposed) return;

    debugPrint('[CrashPrevention] Safe cleanup: $reason');
    _isDisposed = true;
    _crashPreventionActive = false;

    try {
      // Perform cleanup operations
      _performCleanup();

      // Navigate back safely
      _navigateBackSafely();
    } catch (e) {
      debugPrint('[CrashPrevention] Error during cleanup: $e');
      // Force navigation even if cleanup fails
      _forceNavigateBack();
    }
  }

  /// Perform cleanup operations
  void _performCleanup() {
    // Add your cleanup logic here:
    // - Stop media streams
    // - Dispose renderers
    // - Cancel timers
    // - Remove listeners
  }

  /// Navigate back safely
  void _navigateBackSafely() {
    if (mounted && context.mounted) {
      Navigator.of(context).maybePop().catchError((dynamic error) {
        debugPrint('[CrashPrevention] Navigation error: $error');
        _forceNavigateBack();
        return false;
      });
    }
  }

  /// Force navigation back as last resort
  void _forceNavigateBack() {
    try {
      if (mounted && context.mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      debugPrint('[CrashPrevention] Force navigation error: $e');
    }
  }

  /// Placeholder for original call state handling
  void _handleOriginalCallState(Call call, CallState callState) {
    // This is where you'd put your existing callStateChanged implementation
    // from the calling_user.dart file

    // Example structure:
    /*
    switch (callState.state) {
      case CallStateEnum.CONNECTING:
        // Handle connecting
        break;
      case CallStateEnum.PROGRESS:
        // Handle progress  
        break;
      case CallStateEnum.ACCEPTED:
      case CallStateEnum.CONFIRMED:
        // Handle connected
        break;
      case CallStateEnum.STREAM:
        // Handle streams
        break;
      // ... other states
    }
    */
  }

  @override
  void dispose() {
    _safeCleanupAndExit('Widget disposed');
    super.dispose();
  }
}

/// Wrapper widget that applies crash prevention to calling screen
class CrashPreventedCallingScreen extends StatelessWidget {
  const CrashPreventedCallingScreen({
    required this.child,
    super.key,
  });

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return CallScreenErrorBoundary(
      onError: (FlutterErrorDetails details) {
        debugPrint('[CallScreen] Error boundary caught: ${details.exception}');
      },
      // ignore: cast_nullable_to_non_nullable
      child: CallTerminationZoneErrorHandler.run(() => child) as Widget,
    );
  }
}
