enum CallAudioTracksMode {
  none,
  speaker,
  phone,
  bluetooth,
  wiredHeadset,
}

extension CallAudioTracksModeExtension on CallAudioTracksMode {
  String get name {
    switch (this) {
      case CallAudioTracksMode.none:
        return 'none';
      case CallAudioTracksMode.speaker:
        return 'Speaker Phone';
      case CallAudioTracksMode.phone:
        return 'Phone';
      case CallAudioTracksMode.bluetooth:
        return 'Bluetooth Audio';
      case CallAudioTracksMode.wiredHeadset:
        return 'Wired Headset';
    }
  }
}

extension CallAudioTracksModeX on CallAudioTracksMode {
  bool get isNone => this == CallAudioTracksMode.none;
  bool get isSpeakerEnabled => this == CallAudioTracksMode.speaker;
  bool get isSpeakerDisabled => this == CallAudioTracksMode.phone;
  bool get isBluetoothAudioEnabled => this == CallAudioTracksMode.bluetooth;
  bool get isWiredHeadsetEnabled => this == CallAudioTracksMode.wiredHeadset;
}
