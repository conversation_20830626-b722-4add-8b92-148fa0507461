enum PaymentMethods {
  stripe,
  applePay,
}

extension PaymentMethodsExtension on PaymentMethods {
  String get name {
    switch (this) {
      case PaymentMethods.stripe:
        return 'Stripe';
      case PaymentMethods.applePay:
        return 'Apple Pay';
    }
  }

  String get icon {
    switch (this) {
      case PaymentMethods.stripe:
        return 'assets/images/stripe_credit_card.svg';
      case PaymentMethods.applePay:
        return 'assets/images/apple_pay.svg';
    }
  }
}
