// enum SipRegistrationStatus {
//   unknown,
//   registered,
//   unregistered,
//   pending,
//   registrationError,
// }

// extension SipRegStatusExtension on SipRegistrationStatus {
//   String get value {
//     switch (this) {
//       case SipRegistrationStatus.unknown:
//         return 'unknown';
//       case SipRegistrationStatus.registered:
//         return 'registered';
//       case SipRegistrationStatus.unregistered:
//         return 'unregistered';
//       case SipRegistrationStatus.pending:
//         return 'pending';
//       case SipRegistrationStatus.registrationError:
//         return 'error';
//     }
//   }
// }

import 'package:sip_ua/sip_ua.dart';

extension SipRegStatusX on RegistrationStateEnum {
  bool get isUnknown => this == RegistrationStateEnum.NONE;
  bool get isRegistered => this == RegistrationStateEnum.REGISTERED;
  bool get isUnregistered => this == RegistrationStateEnum.UNREGISTERED;
  bool get isFailed => this == RegistrationStateEnum.REGISTRATION_FAILED;
}
