import 'package:flutter/widgets.dart';
import 'package:formz/formz.dart';
import 'package:froggytalk/l10n/l10n.dart';

// Define input validation errors
enum DialerPadInputError {
  invalid,
  invalidInternationlPhoneFormat,
  mustStartWithPlusOrZero
}

extension DialerPadInputErrorX on DialerPadInputError {
  String getLocalizedMessage(BuildContext context) {
    switch (this) {
      case DialerPadInputError.invalid:
        return AppLocalizations.of(context).invalidPhoneNumber;
      case DialerPadInputError.invalidInternationlPhoneFormat:
        return AppLocalizations.of(context).invalidInternationalPhoneFormat;
      case DialerPadInputError.mustStartWithPlusOrZero:
        return AppLocalizations.of(context).phoneNumberStartRule;
    }
  }

  bool get isInvalid => this == DialerPadInputError.invalid;
  bool get isInvalidInternationlPhoneFormat =>
      this == DialerPadInputError.invalidInternationlPhoneFormat;
  bool get isMustStartWithPlusOrZero =>
      this == DialerPadInputError.mustStartWithPlusOrZero;
}

// Extend FormzInput and provide the input type and error type.
class DialerPadInput extends FormzInput<String, DialerPadInputError> {
  // Call super.pure to represent an unmodified form input.
  const DialerPadInput.pure() : super.pure('');

  // Call super.dirty to represent a modified form input.
  const DialerPadInput.dirty(super.value) : super.dirty();

  // Override validator to handle validating a given input value.
  @override
  DialerPadInputError? validator(String? value) {
    try {
      final length = (value ?? '').length;
      // final maxCharacters = length >= 1 && length <= 30;

      // Allow empty values
      if (value == null || value.trim().isEmpty) {
        return null;
      }

      // if (value != null &&
      //     (value.length > 2 && value.length <= 200) &&
      //     !(value.startsWith('+') || value.startsWith('00'))) {
      //   return DialerPadInputError.mustStartWithPlusOrZero;
      // }

      if (value.isNotEmpty &&
          (value.length > 3) &&
          !(value.startsWith('+') || value.startsWith('00'))) {
        return DialerPadInputError.mustStartWithPlusOrZero;
      }

      // final phoneRegExp =
      //     RegExp(r'^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{3,10}$');

      // if (!phoneRegExp.hasMatch(value!)) {
      //   return DialerPadInputError.invalidInternationlPhoneFormat;
      // }

      // final pns = PhoneNumberService()..countryCode = 'GB';
      // if (pns.isPossiblePhoneNumber(value ?? '')) {
      //   return DialerPadInputError.invalidInternationlPhoneFormat;
      // }

      if (length >= 1 && length < 4) return DialerPadInputError.invalid;
      return null;
    } catch (e) {
      // Catch any validation errors to prevent app crashes and "Bad element" errors
      return DialerPadInputError.invalid;
    }
  }

  // get the error message
  String? get errorMessage {
    switch (error) {
      case DialerPadInputError.invalidInternationlPhoneFormat:
        return 'Invalid International Phone Number Format';
      case DialerPadInputError.mustStartWithPlusOrZero:
        return 'Please start your entry with either + or 00';
      case DialerPadInputError.invalid:
        // return 'Please enter a valid Phone number';
        return '';
      // ignore: no_default_cases
      default:
        return null;
    }
  }
}
