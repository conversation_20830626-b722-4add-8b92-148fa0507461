import 'package:flutter/widgets.dart';
import 'package:froggytalk/l10n/l10n.dart';

enum DialerStatus {
  unknown,
  initial,
  initiating,
  connecting,
  connected,
  error,
  ringing,
  ended,
  failedToConnect,
  hold,
}

enum DialerMuteStatus {
  muted,
  unmuted,
}

extension DialerStatusExtension on DialerStatus {
  String get value {
    switch (this) {
      case DialerStatus.unknown:
        return 'unknown';
      case DialerStatus.initial:
        return 'Calling';
      case DialerStatus.initiating:
        return 'Calling';
      case DialerStatus.connecting:
        return 'Calling';
      case DialerStatus.connected:
        return 'Connected';
      case DialerStatus.error:
        return 'Error Occurred';
      case DialerStatus.ringing:
        return 'Ringing';
      case DialerStatus.ended:
        return 'Ended';
      case DialerStatus.failedToConnect:
        return 'Connect Failed';
      case DialerStatus.hold:
        return 'On Hold';
    }
  }

  String getLocalizedMessage(BuildContext context) {
    switch (this) {
      case DialerStatus.unknown:
        return AppLocalizations.of(context).dialerStatusUnknown;
      case DialerStatus.initial:
        return AppLocalizations.of(context).dialerStatusInitial;
      case DialerStatus.initiating:
        return AppLocalizations.of(context).dialerStatusInitiating;
      case DialerStatus.connecting:
        return AppLocalizations.of(context).dialerStatusConnecting;
      case DialerStatus.connected:
        return AppLocalizations.of(context).dialerStatusConnected;
      case DialerStatus.error:
        return AppLocalizations.of(context).dialerStatusError;
      case DialerStatus.ringing:
        return AppLocalizations.of(context).dialerStatusRinging;
      case DialerStatus.ended:
        return AppLocalizations.of(context).dialerStatusEnded;
      case DialerStatus.failedToConnect:
        return AppLocalizations.of(context).dialerStatusFailedToConnect;
      case DialerStatus.hold:
        return AppLocalizations.of(context).dialerStatusHold;
    }
  }
}

extension DialerMuteStatusExtension on DialerMuteStatus {
  bool get isMuted => this == DialerMuteStatus.muted;
  bool get isUnmuted => this == DialerMuteStatus.unmuted;

  String get value {
    switch (this) {
      case DialerMuteStatus.muted:
        return 'Muted';
      case DialerMuteStatus.unmuted:
        return 'Un-Muted';
    }
  }

  String getLocalizedMessage(BuildContext context) {
    switch (this) {
      case DialerMuteStatus.muted:
        return AppLocalizations.of(context).dialerMuteStatusMuted;
      case DialerMuteStatus.unmuted:
        return AppLocalizations.of(context).dialerMuteStatusUnmuted;
    }
  }
}

extension DialerStatusX on DialerStatus {
  bool get isUnknown => this == DialerStatus.unknown;
  bool get isInitial => this == DialerStatus.initial;
  bool get isInitiating => this == DialerStatus.initiating;
  bool get isConnecting => this == DialerStatus.connecting;
  bool get isConnected => this == DialerStatus.connected;
  bool get isError => this == DialerStatus.error;
  bool get isRinging => this == DialerStatus.ringing;
  bool get isEnded => this == DialerStatus.ended;
  bool get isFailedToConnect => this == DialerStatus.failedToConnect;
  bool get isOnHold => this == DialerStatus.hold;
  // bool get isMuted => this == DialerStatus.muted;
  // bool get isUnmuted => this == DialerStatus.unmuted;
}
