// Enhanced error tracking for call termination crashes
// This file provides debugging utilities to help identify why the app
// crashes when a receiver drops the call

import 'dart:async';

import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:utils/utils.dart';

/// Enhanced error tracking specifically for call termination scenarios
class CallTerminationDebugger {
  factory CallTerminationDebugger() => _instance;
  CallTerminationDebugger._internal();
  static final CallTerminationDebugger _instance =
      CallTerminationDebugger._internal();

  /// Stack trace tracking for debugging
  final List<String> _callHistory = [];
  Timer? _crashDetectionTimer;
  bool _isCallActive = false;
  DateTime? _callStartTime;

  /// Initialize debugging for call session
  void startCallSession(String phoneNumber) {
    _isCallActive = true;
    _callStartTime = DateTime.now();
    _addToHistory('CALL_STARTED: $phoneNumber at ${DateTime.now()}');

    // Set up crash detection timer
    _crashDetectionTimer = Timer.periodic(
      const Duration(seconds: 1),
      (_) => _checkForCrash(),
    );

    FroggyLogger.debug('[CallDebugger] Call session started: $phoneNumber');
  }

  /// Track call state changes with detailed logging (DEVELOPER DEBUGGING ONLY)
  void trackCallStateChange(String state, String? originator, String? cause) {
    final timestamp = DateTime.now();
    final message = 'STATE_CHANGE: $state from $originator '
        'cause: $cause at $timestamp';
    _addToHistory(message);

    // DEVELOPER DEBUGGING ONLY - Not for marketing analytics
    FroggyLogger.debug('[CallDebugger] $message');

    // Special handling for termination states
    if (state == 'ENDED' || state == 'FAILED') {
      _handleCallTermination(state, originator, cause);
    }
  }

  /// Track exceptions during call handling (DEVELOPER DEBUGGING ONLY)
  void trackException(
    String context,
    dynamic exception,
    StackTrace? stackTrace,
  ) {
    final timestamp = DateTime.now();
    final message = 'EXCEPTION: $context - $exception at $timestamp';
    _addToHistory(message);

    FroggyLogger.error('[CallDebugger] Exception in $context: $exception');

    // If this is during an active call, it might be our crash cause
    if (_isCallActive) {
      _addToHistory('POTENTIAL_CRASH_CAUSE: Exception during active call');
      _logPotentialCrashData(context, exception, stackTrace);
    }
  }

  /// Track media stream errors specifically
  void trackMediaStreamError(Object error, {StackTrace? stackTrace}) {
    _addToHistory('MEDIA_STREAM_ERROR: $error at ${DateTime.now()}');

    if (stackTrace != null) {
      _addToHistory('MEDIA_STREAM_STACK_TRACE: $stackTrace');
    }

    // Check for device-specific issues
    final errorString = error.toString().toLowerCase();
    if (errorString.contains('permission') && errorString.contains('denied')) {
      _addToHistory('PERMISSION_ISSUE_DETECTED: Microphone access denied');
    } else if (errorString.contains('busy') || errorString.contains('in use')) {
      _addToHistory('DEVICE_BUSY: Microphone may be in use by another app');
    }

    // Prevent normal crash detection from triggering for media stream errors
    // as these are handled separately
    _isMediaStreamIssue = true;

    // Log to analytics and crashlytics with special tag to track frequency
    FroggyLogger.error('[MediaStreamError] $error');
    FirebaseCrashlytics.instance.recordError(
      error,
      stackTrace,
      reason: 'MediaStreamError',
    );
  }

  /// Check if we've detected recurring media stream errors
  /// which might indicate a device-specific issue
  bool _isMediaStreamIssue = false;

  bool get hasMediaStreamIssues => _isMediaStreamIssue;

  /// Get diagnostic info specifically for media stream errors
  Map<String, dynamic> getMediaStreamDiagnostics() {
    return {
      'last_error_time': DateTime.now().toString(),
      'call_history': _callHistory
          .where(
            (entry) =>
                entry.contains('MEDIA_STREAM') || entry.contains('PERMISSION'),
          )
          .toList(),
      'device_info': _collectDeviceInfo(),
    };
  }

  /// Collect information about the device that might be relevant
  /// to media stream issues
  Map<String, String> _collectDeviceInfo() {
    // This would be enhanced with actual device info in a real implementation
    return {
      'platform': defaultTargetPlatform.toString(),
      'time': DateTime.now().toString(),
    };
  }

  /// Handle call termination with enhanced logging
  void _handleCallTermination(
    String state,
    String? originator,
    String? cause,
  ) {
    final callDuration = _callStartTime != null
        ? DateTime.now().difference(_callStartTime!).inSeconds
        : 0;

    _addToHistory(
      'CALL_TERMINATION: $state from $originator, '
      'duration: ${callDuration}s',
    );

    // Check if this termination could cause a crash
    if (originator == 'remote' && (state == 'ENDED' || state == 'FAILED')) {
      _addToHistory(
        'REMOTE_TERMINATION_DETECTED: This could be our crash scenario',
      );
      FroggyLogger.warning(
        '[CallDebugger] Remote termination detected - monitoring for crash',
      );

      // Enhanced monitoring for the next 5 seconds
      Timer(const Duration(seconds: 5), () {
        if (_isCallActive) {
          _addToHistory('SURVIVED_REMOTE_TERMINATION: App did not crash');
          FroggyLogger.info('[CallDebugger] App survived remote termination');
        }
      });
    }

    _endCallSession();
  }

  /// End call session and clean up
  /// End call debugging session
  void endCallSession() {
    _endCallSession();
  }

  /// Internal method to end call session
  void _endCallSession() {
    _isCallActive = false;
    _crashDetectionTimer?.cancel();
    _crashDetectionTimer = null;

    _addToHistory('CALL_SESSION_ENDED: ${DateTime.now()}');
    FroggyLogger.debug('[CallDebugger] Call session ended normally');
  }

  /// Check for potential app crash scenarios
  void _checkForCrash() {
    if (!_isCallActive) return;

    // Monitor memory usage, widget state, etc.
    // This is a placeholder for more sophisticated crash detection
    if (kDebugMode) {
      // In debug mode, we can be more verbose
      _addToHistory(
        'HEALTH_CHECK: ${DateTime.now()} - App responding normally',
      );
    }
  }

  /// Log potential crash data for analysis
  void _logPotentialCrashData(
    String context,
    dynamic exception,
    StackTrace? stackTrace,
  ) {
    final crashData = {
      'context': context,
      'exception': exception.toString(),
      'stack_trace': stackTrace?.toString() ?? 'No stack trace',
      'call_history': _callHistory.take(20).toList(), // Last 20 events
      'call_duration': _callStartTime != null
          ? DateTime.now().difference(_callStartTime!).inSeconds
          : 0,
      'timestamp': DateTime.now().toIso8601String(),
    };

    FroggyLogger.error('[CallDebugger] POTENTIAL CRASH DATA: $crashData');
  }

  /// Add entry to call history with size limit
  void _addToHistory(String entry) {
    _callHistory.add(entry);

    // Keep only last 100 entries to prevent memory issues
    if (_callHistory.length > 100) {
      _callHistory.removeAt(0);
    }
  }

  /// Get call history for debugging
  List<String> get callHistory => List.unmodifiable(_callHistory);

  /// Force log current state for debugging
  void dumpDebugInfo() {
    FroggyLogger.debug('[CallDebugger] === CALL DEBUG INFO ===');
    FroggyLogger.debug('[CallDebugger] Call Active: $_isCallActive');
    FroggyLogger.debug('[CallDebugger] Call Start Time: $_callStartTime');
    FroggyLogger.debug('[CallDebugger] History (last 20):');

    for (final entry in _callHistory.take(20)) {
      FroggyLogger.debug('[CallDebugger] $entry');
    }

    FroggyLogger.debug('[CallDebugger] === END DEBUG INFO ===');
  }
}
