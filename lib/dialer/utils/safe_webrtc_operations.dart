import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:froggytalk/dialer/services/media_error_handler_service.dart';

/// Safe wrappers for WebRTC operations with comprehensive error handling
/// 
/// These functions wrap all WebRTC and media operations in try-catch blocks
/// and report errors to Firebase Crashlytics while providing fallback behavior
class SafeWebRTCOperations {
  SafeWebRTCOperations._();

  /// Safely get user media with permission handling
  static Future<MediaStream?> safeGetUserMedia({
    required Map<String, dynamic> constraints,
    String? callId,
  }) async {
    return MediaErrorHandlerService.safeMediaStreamOperation(
      () async {
        MediaErrorHandlerService.logMediaOperationStart(
          'getUserMedia',
          {'constraints': constraints, 'call_id': callId},
        );

        final stream = await navigator.mediaDevices.getUserMedia(constraints);
        
        MediaErrorHandlerService.logMediaOperationComplete(
          'getUserMedia',
          {'stream_id': stream.id, 'active': stream.active},
        );
        
        return stream;
      },
      'get_user_media',
      streamId: callId,
      isLocal: true,
    );
  }

  /// Safely get display media for screen sharing
  static Future<MediaStream?> safeGetDisplayMedia({
    Map<String, dynamic>? constraints,
    String? callId,
  }) async {
    return MediaErrorHandlerService.safeMediaStreamOperation(
      () async {
        MediaErrorHandlerService.logMediaOperationStart(
          'getDisplayMedia',
          {'constraints': constraints, 'call_id': callId},
        );

        final stream = await navigator.mediaDevices
            .getDisplayMedia(constraints ?? {});
        
        MediaErrorHandlerService.logMediaOperationComplete(
          'getDisplayMedia',
          {'stream_id': stream.id, 'active': stream.active},
        );
        
        return stream;
      },
      'get_display_media',
      streamId: callId,
      isLocal: true,
    );
  }

  /// Safely create RTCPeerConnection
  static Future<RTCPeerConnection?> safeCreatePeerConnection({
    required Map<String, dynamic> configuration,
    Map<String, dynamic>? constraints,
    String? callId,
  }) async {
    return MediaErrorHandlerService.safeWebRTCOperation(
      () async {
        MediaErrorHandlerService.logMediaOperationStart(
          'createPeerConnection',
          {'configuration': configuration, 'call_id': callId},
        );

        final peerConnection = await createPeerConnection(
          configuration,
          constraints ?? {},
        );
        
        MediaErrorHandlerService.logMediaOperationComplete(
          'createPeerConnection',
          {'connection_state': peerConnection.connectionState?.name},
        );
        
        return peerConnection;
      },
      'create_peer_connection',
      callId: callId,
    );
  }

  /// Safely add stream to peer connection
  static Future<bool> safeAddStream({
    required RTCPeerConnection peerConnection,
    required MediaStream stream,
    String? callId,
  }) async {
    final result = await MediaErrorHandlerService.safeWebRTCOperation(
      () async {
        MediaErrorHandlerService.logMediaOperationStart(
          'addStream',
          {'stream_id': stream.id, 'call_id': callId},
        );

        await peerConnection.addStream(stream);
        
        MediaErrorHandlerService.logMediaOperationComplete(
          'addStream',
          {'success': true},
        );
        
        return true;
      },
      'add_stream',
      callId: callId,
    );
    
    return result ?? false;
  }

  /// Safely create offer
  static Future<RTCSessionDescription?> safeCreateOffer({
    required RTCPeerConnection peerConnection,
    Map<String, dynamic>? constraints,
    String? callId,
  }) async {
    return MediaErrorHandlerService.safeWebRTCOperation(
      () async {
        MediaErrorHandlerService.logMediaOperationStart(
          'createOffer',
          {'constraints': constraints, 'call_id': callId},
        );

        final offer = await peerConnection.createOffer(constraints ?? {});
        
        MediaErrorHandlerService.logMediaOperationComplete(
          'createOffer',
          {'type': offer.type, 'sdp_length': offer.sdp?.length},
        );
        
        return offer;
      },
      'create_offer',
      callId: callId,
    );
  }

  /// Safely create answer
  static Future<RTCSessionDescription?> safeCreateAnswer({
    required RTCPeerConnection peerConnection,
    Map<String, dynamic>? constraints,
    String? callId,
  }) async {
    return MediaErrorHandlerService.safeWebRTCOperation(
      () async {
        MediaErrorHandlerService.logMediaOperationStart(
          'createAnswer',
          {'constraints': constraints, 'call_id': callId},
        );

        final answer = await peerConnection.createAnswer(constraints ?? {});
        
        MediaErrorHandlerService.logMediaOperationComplete(
          'createAnswer',
          {'type': answer.type, 'sdp_length': answer.sdp?.length},
        );
        
        return answer;
      },
      'create_answer',
      callId: callId,
    );
  }

  /// Safely set local description
  static Future<bool> safeSetLocalDescription({
    required RTCPeerConnection peerConnection,
    required RTCSessionDescription description,
    String? callId,
  }) async {
    final result = await MediaErrorHandlerService.safeWebRTCOperation(
      () async {
        MediaErrorHandlerService.logMediaOperationStart(
          'setLocalDescription',
          {'type': description.type, 'call_id': callId},
        );

        await peerConnection.setLocalDescription(description);
        
        MediaErrorHandlerService.logMediaOperationComplete(
          'setLocalDescription',
          {'success': true},
        );
        
        return true;
      },
      'set_local_description',
      callId: callId,
    );
    
    return result ?? false;
  }

  /// Safely set remote description
  static Future<bool> safeSetRemoteDescription({
    required RTCPeerConnection peerConnection,
    required RTCSessionDescription description,
    String? callId,
  }) async {
    final result = await MediaErrorHandlerService.safeWebRTCOperation(
      () async {
        MediaErrorHandlerService.logMediaOperationStart(
          'setRemoteDescription',
          {'type': description.type, 'call_id': callId},
        );

        await peerConnection.setRemoteDescription(description);
        
        MediaErrorHandlerService.logMediaOperationComplete(
          'setRemoteDescription',
          {'success': true},
        );
        
        return true;
      },
      'set_remote_description',
      callId: callId,
    );
    
    return result ?? false;
  }

  /// Safely add ICE candidate
  static Future<bool> safeAddIceCandidate({
    required RTCPeerConnection peerConnection,
    required RTCIceCandidate candidate,
    String? callId,
  }) async {
    final result = await MediaErrorHandlerService.safeWebRTCOperation(
      () async {
        MediaErrorHandlerService.logMediaOperationStart(
          'addIceCandidate',
          {'candidate': candidate.candidate, 'call_id': callId},
        );

        await peerConnection.addCandidate(candidate);
        
        MediaErrorHandlerService.logMediaOperationComplete(
          'addIceCandidate',
          {'success': true},
        );
        
        return true;
      },
      'add_ice_candidate',
      callId: callId,
    );
    
    return result ?? false;
  }

  /// Safely close peer connection
  static Future<void> safeClosePeerConnection({
    required RTCPeerConnection peerConnection,
    String? callId,
  }) async {
    await MediaErrorHandlerService.safeWebRTCOperation(
      () async {
        MediaErrorHandlerService.logMediaOperationStart(
          'closePeerConnection',
          {'call_id': callId},
        );

        await peerConnection.close();
        
        MediaErrorHandlerService.logMediaOperationComplete(
          'closePeerConnection',
          {'success': true},
        );
        
        return true;
      },
      'close_peer_connection',
      callId: callId,
    );
  }

  /// Safely dispose media stream
  static Future<void> safeDisposeMediaStream({
    required MediaStream stream,
    String? callId,
  }) async {
    await MediaErrorHandlerService.safeMediaStreamOperation(
      () async {
        MediaErrorHandlerService.logMediaOperationStart(
          'disposeMediaStream',
          {'stream_id': stream.id, 'call_id': callId},
        );

        // Stop all tracks
        for (final track in stream.getTracks()) {
          await track.stop();
        }
        
        // Dispose the stream
        await stream.dispose();
        
        MediaErrorHandlerService.logMediaOperationComplete(
          'disposeMediaStream',
          {'success': true},
        );
        
        return true;
      },
      'dispose_media_stream',
      streamId: stream.id,
    );
  }

  /// Safely enable/disable audio track
  static Future<bool> safeSetAudioEnabled({
    required MediaStream stream,
    required bool enabled,
    String? callId,
  }) async {
    final result = await MediaErrorHandlerService.safeMediaStreamOperation(
      () async {
        MediaErrorHandlerService.logMediaOperationStart(
          'setAudioEnabled',
          {'enabled': enabled, 'stream_id': stream.id, 'call_id': callId},
        );

        final audioTracks = stream.getAudioTracks();
        for (final track in audioTracks) {
          track.enabled = enabled;
        }
        
        MediaErrorHandlerService.logMediaOperationComplete(
          'setAudioEnabled',
          {'enabled': enabled, 'tracks_count': audioTracks.length},
        );
        
        return true;
      },
      'set_audio_enabled',
      streamId: stream.id,
    );
    
    return result ?? false;
  }

  /// Safely enable/disable video track
  static Future<bool> safeSetVideoEnabled({
    required MediaStream stream,
    required bool enabled,
    String? callId,
  }) async {
    final result = await MediaErrorHandlerService.safeMediaStreamOperation(
      () async {
        MediaErrorHandlerService.logMediaOperationStart(
          'setVideoEnabled',
          {'enabled': enabled, 'stream_id': stream.id, 'call_id': callId},
        );

        final videoTracks = stream.getVideoTracks();
        for (final track in videoTracks) {
          track.enabled = enabled;
        }
        
        MediaErrorHandlerService.logMediaOperationComplete(
          'setVideoEnabled',
          {'enabled': enabled, 'tracks_count': videoTracks.length},
        );
        
        return true;
      },
      'set_video_enabled',
      streamId: stream.id,
    );
    
    return result ?? false;
  }

  /// Safely enable/disable speaker mode for audio tracks
  static Future<bool> safeSetSpeakerEnabled({
    required MediaStream stream,
    required bool enabled,
    String? callId,
  }) async {
    final result = await MediaErrorHandlerService.safeMediaStreamOperation(
      () async {
        MediaErrorHandlerService.logMediaOperationStart(
          'setSpeakerEnabled',
          {'enabled': enabled, 'stream_id': stream.id, 'call_id': callId},
        );

        final audioTracks = stream.getAudioTracks();
        for (final track in audioTracks) {
          track.enableSpeakerphone(enabled);
        }
        
        MediaErrorHandlerService.logMediaOperationComplete(
          'setSpeakerEnabled',
          {'enabled': enabled, 'tracks_count': audioTracks.length},
        );
        
        return true;
      },
      'set_speaker_enabled',
      streamId: stream.id,
    );
    
    return result ?? false;
  }

  /// Get available media devices safely
  static Future<List<MediaDeviceInfo>?> safeGetMediaDevices() async {
    return MediaErrorHandlerService.safeMediaStreamOperation(
      () async {
        MediaErrorHandlerService.logMediaOperationStart('getMediaDevices');

        final devices = await navigator.mediaDevices.enumerateDevices();
        
        MediaErrorHandlerService.logMediaOperationComplete(
          'getMediaDevices',
          {'devices_count': devices.length},
        );
        
        return devices;
      },
      'get_media_devices',
    );
  }

  /// Safely initialize RTCVideoRenderer
  static Future<bool> safeInitializeRenderer(RTCVideoRenderer renderer) async {
    final result = await MediaErrorHandlerService.safeWebRTCOperation(
      () async {
        MediaErrorHandlerService.logMediaOperationStart(
          'initializeRenderer',
          {'renderer_id': renderer.hashCode},
        );

        await renderer.initialize();
        
        MediaErrorHandlerService.logMediaOperationComplete(
          'initializeRenderer',
          {'renderer_initialized': true},
        );
        
        return true;
      },
      'initialize_renderer',
    );
    
    return result ?? false;
  }

  /// Safely dispose RTCVideoRenderer
  static Future<bool> safeDisposeRenderer(RTCVideoRenderer renderer) async {
    final result = await MediaErrorHandlerService.safeWebRTCOperation(
      () async {
        MediaErrorHandlerService.logMediaOperationStart(
          'disposeRenderer',
          {'renderer_id': renderer.hashCode},
        );

        await renderer.dispose();
        
        MediaErrorHandlerService.logMediaOperationComplete(
          'disposeRenderer',
          {'renderer_disposed': true},
        );
        
        return true;
      },
      'dispose_renderer',
    );
    
    return result ?? false;
  }

  /// Safely set source object for renderer
  static Future<bool> safeSetRendererSource(
    RTCVideoRenderer renderer,
    MediaStream? stream,
  ) async {
    final result = await MediaErrorHandlerService.safeWebRTCOperation(
      () async {
        MediaErrorHandlerService.logMediaOperationStart(
          'setRendererSource',
          {
            'renderer_id': renderer.hashCode,
            'stream_id': stream?.id,
            'stream_active': stream?.active,
          },
        );

        renderer.srcObject = stream;
        
        MediaErrorHandlerService.logMediaOperationComplete(
          'setRendererSource',
          {'source_set': true},
        );
        
        return true;
      },
      'set_renderer_source',
    );
    
    return result ?? false;
  }
}
