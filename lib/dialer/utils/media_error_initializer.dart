import 'dart:developer' as developer;

import 'package:froggytalk/dialer/services/media_error_handler_service.dart';

/// Initialize the media error handling system in your app's main function
///
/// Add this to your main.dart file before running the app:
///
/// ```dart
/// void main() async {
///   WidgetsFlutterBinding.ensureInitialized();
///
///   // Initialize media error handling
///   MediaErrorInitializer.initialize();
///
///   runApp(MyApp());
/// }
/// ```
class MediaErrorInitializer {
  MediaErrorInitializer._();

  /// Initialize the global media error handling system
  static void initialize() {
    // Initialize the media error handler service
    MediaErrorHandlerService.initialize();
    developer.log(
      'Production-grade media error handling initialized',
      name: 'MediaErrorInitializer',
    );
  }
}

/// Example of how to wrap your app with media error boundary
/// 
/// Use this in your main app widget:
/// 
/// ```dart
/// class MyApp extends StatelessWidget {
///   @override
///   Widget build(BuildContext context) {
///     return MediaErrorBoundary(
///       child: MaterialApp(
///         title: 'FroggyTalk',
///         // ... rest of your app
///       ),
///     );
///   }
/// }
/// ```
