// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'registration_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$RegistrationEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String username, String password)
        registerRequested,
    required TResult Function() reconnectRequested,
    required TResult Function() unregisterRequested,
    required TResult Function(
            sip_ua.RegistrationStateEnum status, String? cause)
        registrationStatusUpdated,
    required TResult Function() registrationReset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String username, String password)? registerRequested,
    TResult? Function()? reconnectRequested,
    TResult? Function()? unregisterRequested,
    TResult? Function(sip_ua.RegistrationStateEnum status, String? cause)?
        registrationStatusUpdated,
    TResult? Function()? registrationReset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String username, String password)? registerRequested,
    TResult Function()? reconnectRequested,
    TResult Function()? unregisterRequested,
    TResult Function(sip_ua.RegistrationStateEnum status, String? cause)?
        registrationStatusUpdated,
    TResult Function()? registrationReset,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_RegisterRequested value) registerRequested,
    required TResult Function(_ReconnectRequested value) reconnectRequested,
    required TResult Function(_UnregisterRequested value) unregisterRequested,
    required TResult Function(_RegistrationStatusUpdated value)
        registrationStatusUpdated,
    required TResult Function(_RegistrationReset value) registrationReset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_RegisterRequested value)? registerRequested,
    TResult? Function(_ReconnectRequested value)? reconnectRequested,
    TResult? Function(_UnregisterRequested value)? unregisterRequested,
    TResult? Function(_RegistrationStatusUpdated value)?
        registrationStatusUpdated,
    TResult? Function(_RegistrationReset value)? registrationReset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_RegisterRequested value)? registerRequested,
    TResult Function(_ReconnectRequested value)? reconnectRequested,
    TResult Function(_UnregisterRequested value)? unregisterRequested,
    TResult Function(_RegistrationStatusUpdated value)?
        registrationStatusUpdated,
    TResult Function(_RegistrationReset value)? registrationReset,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RegistrationEventCopyWith<$Res> {
  factory $RegistrationEventCopyWith(
          RegistrationEvent value, $Res Function(RegistrationEvent) then) =
      _$RegistrationEventCopyWithImpl<$Res, RegistrationEvent>;
}

/// @nodoc
class _$RegistrationEventCopyWithImpl<$Res, $Val extends RegistrationEvent>
    implements $RegistrationEventCopyWith<$Res> {
  _$RegistrationEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RegistrationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$RegisterRequestedImplCopyWith<$Res> {
  factory _$$RegisterRequestedImplCopyWith(_$RegisterRequestedImpl value,
          $Res Function(_$RegisterRequestedImpl) then) =
      __$$RegisterRequestedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String username, String password});
}

/// @nodoc
class __$$RegisterRequestedImplCopyWithImpl<$Res>
    extends _$RegistrationEventCopyWithImpl<$Res, _$RegisterRequestedImpl>
    implements _$$RegisterRequestedImplCopyWith<$Res> {
  __$$RegisterRequestedImplCopyWithImpl(_$RegisterRequestedImpl _value,
      $Res Function(_$RegisterRequestedImpl) _then)
      : super(_value, _then);

  /// Create a copy of RegistrationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? username = null,
    Object? password = null,
  }) {
    return _then(_$RegisterRequestedImpl(
      username: null == username
          ? _value.username
          : username // ignore: cast_nullable_to_non_nullable
              as String,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$RegisterRequestedImpl implements _RegisterRequested {
  const _$RegisterRequestedImpl(
      {required this.username, required this.password});

  @override
  final String username;
  @override
  final String password;

  @override
  String toString() {
    return 'RegistrationEvent.registerRequested(username: $username, password: $password)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RegisterRequestedImpl &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.password, password) ||
                other.password == password));
  }

  @override
  int get hashCode => Object.hash(runtimeType, username, password);

  /// Create a copy of RegistrationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RegisterRequestedImplCopyWith<_$RegisterRequestedImpl> get copyWith =>
      __$$RegisterRequestedImplCopyWithImpl<_$RegisterRequestedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String username, String password)
        registerRequested,
    required TResult Function() reconnectRequested,
    required TResult Function() unregisterRequested,
    required TResult Function(
            sip_ua.RegistrationStateEnum status, String? cause)
        registrationStatusUpdated,
    required TResult Function() registrationReset,
  }) {
    return registerRequested(username, password);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String username, String password)? registerRequested,
    TResult? Function()? reconnectRequested,
    TResult? Function()? unregisterRequested,
    TResult? Function(sip_ua.RegistrationStateEnum status, String? cause)?
        registrationStatusUpdated,
    TResult? Function()? registrationReset,
  }) {
    return registerRequested?.call(username, password);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String username, String password)? registerRequested,
    TResult Function()? reconnectRequested,
    TResult Function()? unregisterRequested,
    TResult Function(sip_ua.RegistrationStateEnum status, String? cause)?
        registrationStatusUpdated,
    TResult Function()? registrationReset,
    required TResult orElse(),
  }) {
    if (registerRequested != null) {
      return registerRequested(username, password);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_RegisterRequested value) registerRequested,
    required TResult Function(_ReconnectRequested value) reconnectRequested,
    required TResult Function(_UnregisterRequested value) unregisterRequested,
    required TResult Function(_RegistrationStatusUpdated value)
        registrationStatusUpdated,
    required TResult Function(_RegistrationReset value) registrationReset,
  }) {
    return registerRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_RegisterRequested value)? registerRequested,
    TResult? Function(_ReconnectRequested value)? reconnectRequested,
    TResult? Function(_UnregisterRequested value)? unregisterRequested,
    TResult? Function(_RegistrationStatusUpdated value)?
        registrationStatusUpdated,
    TResult? Function(_RegistrationReset value)? registrationReset,
  }) {
    return registerRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_RegisterRequested value)? registerRequested,
    TResult Function(_ReconnectRequested value)? reconnectRequested,
    TResult Function(_UnregisterRequested value)? unregisterRequested,
    TResult Function(_RegistrationStatusUpdated value)?
        registrationStatusUpdated,
    TResult Function(_RegistrationReset value)? registrationReset,
    required TResult orElse(),
  }) {
    if (registerRequested != null) {
      return registerRequested(this);
    }
    return orElse();
  }
}

abstract class _RegisterRequested implements RegistrationEvent {
  const factory _RegisterRequested(
      {required final String username,
      required final String password}) = _$RegisterRequestedImpl;

  String get username;
  String get password;

  /// Create a copy of RegistrationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RegisterRequestedImplCopyWith<_$RegisterRequestedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ReconnectRequestedImplCopyWith<$Res> {
  factory _$$ReconnectRequestedImplCopyWith(_$ReconnectRequestedImpl value,
          $Res Function(_$ReconnectRequestedImpl) then) =
      __$$ReconnectRequestedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ReconnectRequestedImplCopyWithImpl<$Res>
    extends _$RegistrationEventCopyWithImpl<$Res, _$ReconnectRequestedImpl>
    implements _$$ReconnectRequestedImplCopyWith<$Res> {
  __$$ReconnectRequestedImplCopyWithImpl(_$ReconnectRequestedImpl _value,
      $Res Function(_$ReconnectRequestedImpl) _then)
      : super(_value, _then);

  /// Create a copy of RegistrationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ReconnectRequestedImpl implements _ReconnectRequested {
  const _$ReconnectRequestedImpl();

  @override
  String toString() {
    return 'RegistrationEvent.reconnectRequested()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ReconnectRequestedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String username, String password)
        registerRequested,
    required TResult Function() reconnectRequested,
    required TResult Function() unregisterRequested,
    required TResult Function(
            sip_ua.RegistrationStateEnum status, String? cause)
        registrationStatusUpdated,
    required TResult Function() registrationReset,
  }) {
    return reconnectRequested();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String username, String password)? registerRequested,
    TResult? Function()? reconnectRequested,
    TResult? Function()? unregisterRequested,
    TResult? Function(sip_ua.RegistrationStateEnum status, String? cause)?
        registrationStatusUpdated,
    TResult? Function()? registrationReset,
  }) {
    return reconnectRequested?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String username, String password)? registerRequested,
    TResult Function()? reconnectRequested,
    TResult Function()? unregisterRequested,
    TResult Function(sip_ua.RegistrationStateEnum status, String? cause)?
        registrationStatusUpdated,
    TResult Function()? registrationReset,
    required TResult orElse(),
  }) {
    if (reconnectRequested != null) {
      return reconnectRequested();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_RegisterRequested value) registerRequested,
    required TResult Function(_ReconnectRequested value) reconnectRequested,
    required TResult Function(_UnregisterRequested value) unregisterRequested,
    required TResult Function(_RegistrationStatusUpdated value)
        registrationStatusUpdated,
    required TResult Function(_RegistrationReset value) registrationReset,
  }) {
    return reconnectRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_RegisterRequested value)? registerRequested,
    TResult? Function(_ReconnectRequested value)? reconnectRequested,
    TResult? Function(_UnregisterRequested value)? unregisterRequested,
    TResult? Function(_RegistrationStatusUpdated value)?
        registrationStatusUpdated,
    TResult? Function(_RegistrationReset value)? registrationReset,
  }) {
    return reconnectRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_RegisterRequested value)? registerRequested,
    TResult Function(_ReconnectRequested value)? reconnectRequested,
    TResult Function(_UnregisterRequested value)? unregisterRequested,
    TResult Function(_RegistrationStatusUpdated value)?
        registrationStatusUpdated,
    TResult Function(_RegistrationReset value)? registrationReset,
    required TResult orElse(),
  }) {
    if (reconnectRequested != null) {
      return reconnectRequested(this);
    }
    return orElse();
  }
}

abstract class _ReconnectRequested implements RegistrationEvent {
  const factory _ReconnectRequested() = _$ReconnectRequestedImpl;
}

/// @nodoc
abstract class _$$UnregisterRequestedImplCopyWith<$Res> {
  factory _$$UnregisterRequestedImplCopyWith(_$UnregisterRequestedImpl value,
          $Res Function(_$UnregisterRequestedImpl) then) =
      __$$UnregisterRequestedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UnregisterRequestedImplCopyWithImpl<$Res>
    extends _$RegistrationEventCopyWithImpl<$Res, _$UnregisterRequestedImpl>
    implements _$$UnregisterRequestedImplCopyWith<$Res> {
  __$$UnregisterRequestedImplCopyWithImpl(_$UnregisterRequestedImpl _value,
      $Res Function(_$UnregisterRequestedImpl) _then)
      : super(_value, _then);

  /// Create a copy of RegistrationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$UnregisterRequestedImpl implements _UnregisterRequested {
  const _$UnregisterRequestedImpl();

  @override
  String toString() {
    return 'RegistrationEvent.unregisterRequested()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UnregisterRequestedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String username, String password)
        registerRequested,
    required TResult Function() reconnectRequested,
    required TResult Function() unregisterRequested,
    required TResult Function(
            sip_ua.RegistrationStateEnum status, String? cause)
        registrationStatusUpdated,
    required TResult Function() registrationReset,
  }) {
    return unregisterRequested();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String username, String password)? registerRequested,
    TResult? Function()? reconnectRequested,
    TResult? Function()? unregisterRequested,
    TResult? Function(sip_ua.RegistrationStateEnum status, String? cause)?
        registrationStatusUpdated,
    TResult? Function()? registrationReset,
  }) {
    return unregisterRequested?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String username, String password)? registerRequested,
    TResult Function()? reconnectRequested,
    TResult Function()? unregisterRequested,
    TResult Function(sip_ua.RegistrationStateEnum status, String? cause)?
        registrationStatusUpdated,
    TResult Function()? registrationReset,
    required TResult orElse(),
  }) {
    if (unregisterRequested != null) {
      return unregisterRequested();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_RegisterRequested value) registerRequested,
    required TResult Function(_ReconnectRequested value) reconnectRequested,
    required TResult Function(_UnregisterRequested value) unregisterRequested,
    required TResult Function(_RegistrationStatusUpdated value)
        registrationStatusUpdated,
    required TResult Function(_RegistrationReset value) registrationReset,
  }) {
    return unregisterRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_RegisterRequested value)? registerRequested,
    TResult? Function(_ReconnectRequested value)? reconnectRequested,
    TResult? Function(_UnregisterRequested value)? unregisterRequested,
    TResult? Function(_RegistrationStatusUpdated value)?
        registrationStatusUpdated,
    TResult? Function(_RegistrationReset value)? registrationReset,
  }) {
    return unregisterRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_RegisterRequested value)? registerRequested,
    TResult Function(_ReconnectRequested value)? reconnectRequested,
    TResult Function(_UnregisterRequested value)? unregisterRequested,
    TResult Function(_RegistrationStatusUpdated value)?
        registrationStatusUpdated,
    TResult Function(_RegistrationReset value)? registrationReset,
    required TResult orElse(),
  }) {
    if (unregisterRequested != null) {
      return unregisterRequested(this);
    }
    return orElse();
  }
}

abstract class _UnregisterRequested implements RegistrationEvent {
  const factory _UnregisterRequested() = _$UnregisterRequestedImpl;
}

/// @nodoc
abstract class _$$RegistrationStatusUpdatedImplCopyWith<$Res> {
  factory _$$RegistrationStatusUpdatedImplCopyWith(
          _$RegistrationStatusUpdatedImpl value,
          $Res Function(_$RegistrationStatusUpdatedImpl) then) =
      __$$RegistrationStatusUpdatedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({sip_ua.RegistrationStateEnum status, String? cause});
}

/// @nodoc
class __$$RegistrationStatusUpdatedImplCopyWithImpl<$Res>
    extends _$RegistrationEventCopyWithImpl<$Res,
        _$RegistrationStatusUpdatedImpl>
    implements _$$RegistrationStatusUpdatedImplCopyWith<$Res> {
  __$$RegistrationStatusUpdatedImplCopyWithImpl(
      _$RegistrationStatusUpdatedImpl _value,
      $Res Function(_$RegistrationStatusUpdatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of RegistrationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? cause = freezed,
  }) {
    return _then(_$RegistrationStatusUpdatedImpl(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as sip_ua.RegistrationStateEnum,
      cause: freezed == cause
          ? _value.cause
          : cause // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$RegistrationStatusUpdatedImpl implements _RegistrationStatusUpdated {
  const _$RegistrationStatusUpdatedImpl({required this.status, this.cause});

  @override
  final sip_ua.RegistrationStateEnum status;
  @override
  final String? cause;

  @override
  String toString() {
    return 'RegistrationEvent.registrationStatusUpdated(status: $status, cause: $cause)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RegistrationStatusUpdatedImpl &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.cause, cause) || other.cause == cause));
  }

  @override
  int get hashCode => Object.hash(runtimeType, status, cause);

  /// Create a copy of RegistrationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RegistrationStatusUpdatedImplCopyWith<_$RegistrationStatusUpdatedImpl>
      get copyWith => __$$RegistrationStatusUpdatedImplCopyWithImpl<
          _$RegistrationStatusUpdatedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String username, String password)
        registerRequested,
    required TResult Function() reconnectRequested,
    required TResult Function() unregisterRequested,
    required TResult Function(
            sip_ua.RegistrationStateEnum status, String? cause)
        registrationStatusUpdated,
    required TResult Function() registrationReset,
  }) {
    return registrationStatusUpdated(status, cause);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String username, String password)? registerRequested,
    TResult? Function()? reconnectRequested,
    TResult? Function()? unregisterRequested,
    TResult? Function(sip_ua.RegistrationStateEnum status, String? cause)?
        registrationStatusUpdated,
    TResult? Function()? registrationReset,
  }) {
    return registrationStatusUpdated?.call(status, cause);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String username, String password)? registerRequested,
    TResult Function()? reconnectRequested,
    TResult Function()? unregisterRequested,
    TResult Function(sip_ua.RegistrationStateEnum status, String? cause)?
        registrationStatusUpdated,
    TResult Function()? registrationReset,
    required TResult orElse(),
  }) {
    if (registrationStatusUpdated != null) {
      return registrationStatusUpdated(status, cause);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_RegisterRequested value) registerRequested,
    required TResult Function(_ReconnectRequested value) reconnectRequested,
    required TResult Function(_UnregisterRequested value) unregisterRequested,
    required TResult Function(_RegistrationStatusUpdated value)
        registrationStatusUpdated,
    required TResult Function(_RegistrationReset value) registrationReset,
  }) {
    return registrationStatusUpdated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_RegisterRequested value)? registerRequested,
    TResult? Function(_ReconnectRequested value)? reconnectRequested,
    TResult? Function(_UnregisterRequested value)? unregisterRequested,
    TResult? Function(_RegistrationStatusUpdated value)?
        registrationStatusUpdated,
    TResult? Function(_RegistrationReset value)? registrationReset,
  }) {
    return registrationStatusUpdated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_RegisterRequested value)? registerRequested,
    TResult Function(_ReconnectRequested value)? reconnectRequested,
    TResult Function(_UnregisterRequested value)? unregisterRequested,
    TResult Function(_RegistrationStatusUpdated value)?
        registrationStatusUpdated,
    TResult Function(_RegistrationReset value)? registrationReset,
    required TResult orElse(),
  }) {
    if (registrationStatusUpdated != null) {
      return registrationStatusUpdated(this);
    }
    return orElse();
  }
}

abstract class _RegistrationStatusUpdated implements RegistrationEvent {
  const factory _RegistrationStatusUpdated(
      {required final sip_ua.RegistrationStateEnum status,
      final String? cause}) = _$RegistrationStatusUpdatedImpl;

  sip_ua.RegistrationStateEnum get status;
  String? get cause;

  /// Create a copy of RegistrationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RegistrationStatusUpdatedImplCopyWith<_$RegistrationStatusUpdatedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RegistrationResetImplCopyWith<$Res> {
  factory _$$RegistrationResetImplCopyWith(_$RegistrationResetImpl value,
          $Res Function(_$RegistrationResetImpl) then) =
      __$$RegistrationResetImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$RegistrationResetImplCopyWithImpl<$Res>
    extends _$RegistrationEventCopyWithImpl<$Res, _$RegistrationResetImpl>
    implements _$$RegistrationResetImplCopyWith<$Res> {
  __$$RegistrationResetImplCopyWithImpl(_$RegistrationResetImpl _value,
      $Res Function(_$RegistrationResetImpl) _then)
      : super(_value, _then);

  /// Create a copy of RegistrationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$RegistrationResetImpl implements _RegistrationReset {
  const _$RegistrationResetImpl();

  @override
  String toString() {
    return 'RegistrationEvent.registrationReset()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$RegistrationResetImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String username, String password)
        registerRequested,
    required TResult Function() reconnectRequested,
    required TResult Function() unregisterRequested,
    required TResult Function(
            sip_ua.RegistrationStateEnum status, String? cause)
        registrationStatusUpdated,
    required TResult Function() registrationReset,
  }) {
    return registrationReset();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String username, String password)? registerRequested,
    TResult? Function()? reconnectRequested,
    TResult? Function()? unregisterRequested,
    TResult? Function(sip_ua.RegistrationStateEnum status, String? cause)?
        registrationStatusUpdated,
    TResult? Function()? registrationReset,
  }) {
    return registrationReset?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String username, String password)? registerRequested,
    TResult Function()? reconnectRequested,
    TResult Function()? unregisterRequested,
    TResult Function(sip_ua.RegistrationStateEnum status, String? cause)?
        registrationStatusUpdated,
    TResult Function()? registrationReset,
    required TResult orElse(),
  }) {
    if (registrationReset != null) {
      return registrationReset();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_RegisterRequested value) registerRequested,
    required TResult Function(_ReconnectRequested value) reconnectRequested,
    required TResult Function(_UnregisterRequested value) unregisterRequested,
    required TResult Function(_RegistrationStatusUpdated value)
        registrationStatusUpdated,
    required TResult Function(_RegistrationReset value) registrationReset,
  }) {
    return registrationReset(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_RegisterRequested value)? registerRequested,
    TResult? Function(_ReconnectRequested value)? reconnectRequested,
    TResult? Function(_UnregisterRequested value)? unregisterRequested,
    TResult? Function(_RegistrationStatusUpdated value)?
        registrationStatusUpdated,
    TResult? Function(_RegistrationReset value)? registrationReset,
  }) {
    return registrationReset?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_RegisterRequested value)? registerRequested,
    TResult Function(_ReconnectRequested value)? reconnectRequested,
    TResult Function(_UnregisterRequested value)? unregisterRequested,
    TResult Function(_RegistrationStatusUpdated value)?
        registrationStatusUpdated,
    TResult Function(_RegistrationReset value)? registrationReset,
    required TResult orElse(),
  }) {
    if (registrationReset != null) {
      return registrationReset(this);
    }
    return orElse();
  }
}

abstract class _RegistrationReset implements RegistrationEvent {
  const factory _RegistrationReset() = _$RegistrationResetImpl;
}

/// @nodoc
mixin _$RegistrationState {
  /// Current SIP username (normalized format)
  String? get sipUsername => throw _privateConstructorUsedError;

  /// Current SIP password
  String? get sipPassword => throw _privateConstructorUsedError;

  /// Current registration status
  sip_ua.RegistrationStateEnum get status => throw _privateConstructorUsedError;

  /// Last error message if registration failed
  String? get lastError => throw _privateConstructorUsedError;

  /// Create a copy of RegistrationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RegistrationStateCopyWith<RegistrationState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RegistrationStateCopyWith<$Res> {
  factory $RegistrationStateCopyWith(
          RegistrationState value, $Res Function(RegistrationState) then) =
      _$RegistrationStateCopyWithImpl<$Res, RegistrationState>;
  @useResult
  $Res call(
      {String? sipUsername,
      String? sipPassword,
      sip_ua.RegistrationStateEnum status,
      String? lastError});
}

/// @nodoc
class _$RegistrationStateCopyWithImpl<$Res, $Val extends RegistrationState>
    implements $RegistrationStateCopyWith<$Res> {
  _$RegistrationStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RegistrationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sipUsername = freezed,
    Object? sipPassword = freezed,
    Object? status = null,
    Object? lastError = freezed,
  }) {
    return _then(_value.copyWith(
      sipUsername: freezed == sipUsername
          ? _value.sipUsername
          : sipUsername // ignore: cast_nullable_to_non_nullable
              as String?,
      sipPassword: freezed == sipPassword
          ? _value.sipPassword
          : sipPassword // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as sip_ua.RegistrationStateEnum,
      lastError: freezed == lastError
          ? _value.lastError
          : lastError // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RegistrationStateImplCopyWith<$Res>
    implements $RegistrationStateCopyWith<$Res> {
  factory _$$RegistrationStateImplCopyWith(_$RegistrationStateImpl value,
          $Res Function(_$RegistrationStateImpl) then) =
      __$$RegistrationStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? sipUsername,
      String? sipPassword,
      sip_ua.RegistrationStateEnum status,
      String? lastError});
}

/// @nodoc
class __$$RegistrationStateImplCopyWithImpl<$Res>
    extends _$RegistrationStateCopyWithImpl<$Res, _$RegistrationStateImpl>
    implements _$$RegistrationStateImplCopyWith<$Res> {
  __$$RegistrationStateImplCopyWithImpl(_$RegistrationStateImpl _value,
      $Res Function(_$RegistrationStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of RegistrationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sipUsername = freezed,
    Object? sipPassword = freezed,
    Object? status = null,
    Object? lastError = freezed,
  }) {
    return _then(_$RegistrationStateImpl(
      sipUsername: freezed == sipUsername
          ? _value.sipUsername
          : sipUsername // ignore: cast_nullable_to_non_nullable
              as String?,
      sipPassword: freezed == sipPassword
          ? _value.sipPassword
          : sipPassword // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as sip_ua.RegistrationStateEnum,
      lastError: freezed == lastError
          ? _value.lastError
          : lastError // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$RegistrationStateImpl extends _RegistrationState {
  const _$RegistrationStateImpl(
      {this.sipUsername,
      this.sipPassword,
      this.status = sip_ua.RegistrationStateEnum.NONE,
      this.lastError})
      : super._();

  /// Current SIP username (normalized format)
  @override
  final String? sipUsername;

  /// Current SIP password
  @override
  final String? sipPassword;

  /// Current registration status
  @override
  @JsonKey()
  final sip_ua.RegistrationStateEnum status;

  /// Last error message if registration failed
  @override
  final String? lastError;

  @override
  String toString() {
    return 'RegistrationState(sipUsername: $sipUsername, sipPassword: $sipPassword, status: $status, lastError: $lastError)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RegistrationStateImpl &&
            (identical(other.sipUsername, sipUsername) ||
                other.sipUsername == sipUsername) &&
            (identical(other.sipPassword, sipPassword) ||
                other.sipPassword == sipPassword) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.lastError, lastError) ||
                other.lastError == lastError));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, sipUsername, sipPassword, status, lastError);

  /// Create a copy of RegistrationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RegistrationStateImplCopyWith<_$RegistrationStateImpl> get copyWith =>
      __$$RegistrationStateImplCopyWithImpl<_$RegistrationStateImpl>(
          this, _$identity);
}

abstract class _RegistrationState extends RegistrationState {
  const factory _RegistrationState(
      {final String? sipUsername,
      final String? sipPassword,
      final sip_ua.RegistrationStateEnum status,
      final String? lastError}) = _$RegistrationStateImpl;
  const _RegistrationState._() : super._();

  /// Current SIP username (normalized format)
  @override
  String? get sipUsername;

  /// Current SIP password
  @override
  String? get sipPassword;

  /// Current registration status
  @override
  sip_ua.RegistrationStateEnum get status;

  /// Last error message if registration failed
  @override
  String? get lastError;

  /// Create a copy of RegistrationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RegistrationStateImplCopyWith<_$RegistrationStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
