part of 'call_timer_bloc.dart';

/// Call timer events for the CallTimerBloc
/// 
/// These events represent all possible timer-related actions that can occur
/// during a call, including start, stop, pause, resume, and update operations.
@freezed
class CallTimerEvent with _$CallTimerEvent {
  /// Start the call timer
  const factory CallTimerEvent.timerStarted() = _TimerStarted;

  /// Start the call timer with a specific start time for synchronization
  const factory CallTimerEvent.timerStartedWithTime({
    required DateTime startTime,
  }) = _TimerStartedWithTime;

  /// Stop the call timer
  const factory CallTimerEvent.timerStopped() = _TimerStopped;

  /// Pause the call timer
  const factory CallTimerEvent.timerPaused() = _TimerPaused;

  /// Resume the call timer
  const factory CallTimerEvent.timerResumed() = _TimerResumed;

  /// Restart the call timer from zero
  const factory CallTimerEvent.timerRestarted() = _TimerRestarted;

  /// Update timer with new elapsed time
  const factory CallTimerEvent.timerUpdated({
    @Default(Duration.zero) Duration elapsed,
  }) = _TimerUpdated;

  /// Reset timer state to initial
  const factory CallTimerEvent.timerReset() = _TimerReset;

  /// Synchronize timer from foreground service updates
  const factory CallTimerEvent.syncFromForegroundService({
    required Duration duration,
    required DateTime timestamp,
  }) = _SyncFromForegroundService;
}
