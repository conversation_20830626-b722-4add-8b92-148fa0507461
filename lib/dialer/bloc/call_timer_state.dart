part of 'call_timer_bloc.dart';

/// Call timer state representing timer status and elapsed time
/// 
/// Contains timer state information including elapsed time, running status,
/// pause status, and error handling for comprehensive timer management.
@freezed
class CallTimerState with _$CallTimerState {
  const factory CallTimerState({
    /// Current elapsed time since timer started
    @Default(Duration.zero) Duration elapsedTime,
    
    /// Whether the timer is currently running
    @Default(false) bool isRunning,
    
    /// Whether the timer is currently paused
    @Default(false) bool isPaused,
    
    /// Last error message if any timer operation failed
    String? lastError,
  }) = _CallTimerState;

  /// Creates an initial timer state with default values
  factory CallTimerState.initial() => const CallTimerState();

  const CallTimerState._();

  /// Whether the timer is currently stopped
  bool get isStopped => !isRunning && !isPaused;

  /// Whether the timer is currently active (running and not paused)
  bool get isActive => isRunning && !isPaused;

  /// Whether there is an error in the current timer state
  bool get hasError => lastError != null;

  /// Formatted timer display string in MM:SS format
  String get formattedTime => [
        elapsedTime.inMinutes,
        elapsedTime.inSeconds,
      ].map((seg) => seg.remainder(60).toString().padLeft(2, '0')).join(':');

  /// Total elapsed seconds
  int get totalSeconds => elapsedTime.inSeconds;

  /// Total elapsed minutes
  int get totalMinutes => elapsedTime.inMinutes;

  /// Whether timer has been running for more than zero time
  bool get hasElapsedTime => elapsedTime > Duration.zero;

  /// Human-readable timer status
  String get statusDescription {
    if (isActive) return 'Running';
    if (isPaused) return 'Paused';
    if (isStopped) return 'Stopped';
    return 'Unknown';
  }
}
