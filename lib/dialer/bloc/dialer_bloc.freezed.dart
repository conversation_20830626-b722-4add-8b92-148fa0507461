// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'dialer_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$DialerEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? sipUsername, String? sipPassword)
        register,
    required TResult Function() reset,
    required TResult Function() reloadAsterisk,
    required TResult Function(String phoneNumber) callStarted,
    required TResult Function() callStopped,
    required TResult Function() hangedup,
    required TResult Function(sip_ua.CallState event) handledCallStream,
    required TResult Function(DialerStatus status) updatedDialerStatus,
    required TResult Function(sip_ua.Call? call) updatedCall,
    required TResult Function(bool isOnHold, String? onHoldOriginator)
        updatedOnHoldStatus,
    required TResult Function() forceEndCurrentCall,
    required TResult Function(sip_ua.CallStateEnum callStateEnum)
        updatedCallStateEnum,
    required TResult Function(sip_ua.RegistrationStateEnum status)
        updatedRegistrationStatus,
    required TResult Function(String? message) sendErrorMessage,
    required TResult Function() timerStarted,
    required TResult Function(int value) timerPaused,
    required TResult Function() timerStopped,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() holdCall,
    required TResult Function() unholdCall,
    required TResult Function() switchToBluetoothHeadset,
    required TResult Function() switchToWiredHeadet,
    required TResult Function() disableSpeakerPhone,
    required TResult Function() switchSpeakerPhoneOn,
    required TResult Function() muteOn,
    required TResult Function() muteOff,
    required TResult Function() handledAllHeadsetStreams,
    required TResult Function(String value) sendDTMF,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? sipUsername, String? sipPassword)? register,
    TResult? Function()? reset,
    TResult? Function()? reloadAsterisk,
    TResult? Function(String phoneNumber)? callStarted,
    TResult? Function()? callStopped,
    TResult? Function()? hangedup,
    TResult? Function(sip_ua.CallState event)? handledCallStream,
    TResult? Function(DialerStatus status)? updatedDialerStatus,
    TResult? Function(sip_ua.Call? call)? updatedCall,
    TResult? Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult? Function()? forceEndCurrentCall,
    TResult? Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult? Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult? Function(String? message)? sendErrorMessage,
    TResult? Function()? timerStarted,
    TResult? Function(int value)? timerPaused,
    TResult? Function()? timerStopped,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? holdCall,
    TResult? Function()? unholdCall,
    TResult? Function()? switchToBluetoothHeadset,
    TResult? Function()? switchToWiredHeadet,
    TResult? Function()? disableSpeakerPhone,
    TResult? Function()? switchSpeakerPhoneOn,
    TResult? Function()? muteOn,
    TResult? Function()? muteOff,
    TResult? Function()? handledAllHeadsetStreams,
    TResult? Function(String value)? sendDTMF,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? sipUsername, String? sipPassword)? register,
    TResult Function()? reset,
    TResult Function()? reloadAsterisk,
    TResult Function(String phoneNumber)? callStarted,
    TResult Function()? callStopped,
    TResult Function()? hangedup,
    TResult Function(sip_ua.CallState event)? handledCallStream,
    TResult Function(DialerStatus status)? updatedDialerStatus,
    TResult Function(sip_ua.Call? call)? updatedCall,
    TResult Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult Function()? forceEndCurrentCall,
    TResult Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult Function(String? message)? sendErrorMessage,
    TResult Function()? timerStarted,
    TResult Function(int value)? timerPaused,
    TResult Function()? timerStopped,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? holdCall,
    TResult Function()? unholdCall,
    TResult Function()? switchToBluetoothHeadset,
    TResult Function()? switchToWiredHeadet,
    TResult Function()? disableSpeakerPhone,
    TResult Function()? switchSpeakerPhoneOn,
    TResult Function()? muteOn,
    TResult Function()? muteOff,
    TResult Function()? handledAllHeadsetStreams,
    TResult Function(String value)? sendDTMF,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) register,
    required TResult Function(_Initial value) reset,
    required TResult Function(_RegisteredAgain value) reloadAsterisk,
    required TResult Function(_CallStarted value) callStarted,
    required TResult Function(_CallStopped value) callStopped,
    required TResult Function(_HangedUp value) hangedup,
    required TResult Function(_HandledCallStream value) handledCallStream,
    required TResult Function(_UpdatedDialerStatus value) updatedDialerStatus,
    required TResult Function(_UpdatedCall value) updatedCall,
    required TResult Function(_UpdatedOnHoldStatus value) updatedOnHoldStatus,
    required TResult Function(_ForceEndCurrentCall value) forceEndCurrentCall,
    required TResult Function(_UpdatedCallStateEnum value) updatedCallStateEnum,
    required TResult Function(_UpdatedRegistrationStatus value)
        updatedRegistrationStatus,
    required TResult Function(_HandledMessageRequest value) sendErrorMessage,
    required TResult Function(_StartedTimer value) timerStarted,
    required TResult Function(_PausedTimer value) timerPaused,
    required TResult Function(_StoppedTimer value) timerStopped,
    required TResult Function(_UpdatedTimer value) timerUpdated,
    required TResult Function(_HoldCall value) holdCall,
    required TResult Function(_UnholdCall value) unholdCall,
    required TResult Function(_SwitchedToBluetooth value)
        switchToBluetoothHeadset,
    required TResult Function(_SwitchedToWiredHeadsetz value)
        switchToWiredHeadet,
    required TResult Function(_SwitchedSpeakerOff value) disableSpeakerPhone,
    required TResult Function(_SwitchSpeakerphoneOn value) switchSpeakerPhoneOn,
    required TResult Function(_MuteOn value) muteOn,
    required TResult Function(_MuteOff value) muteOff,
    required TResult Function(_HandledAllHeadsetStreams value)
        handledAllHeadsetStreams,
    required TResult Function(_SendDTMFValue value) sendDTMF,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? register,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_RegisteredAgain value)? reloadAsterisk,
    TResult? Function(_CallStarted value)? callStarted,
    TResult? Function(_CallStopped value)? callStopped,
    TResult? Function(_HangedUp value)? hangedup,
    TResult? Function(_HandledCallStream value)? handledCallStream,
    TResult? Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult? Function(_UpdatedCall value)? updatedCall,
    TResult? Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult? Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult? Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult? Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult? Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult? Function(_StartedTimer value)? timerStarted,
    TResult? Function(_PausedTimer value)? timerPaused,
    TResult? Function(_StoppedTimer value)? timerStopped,
    TResult? Function(_UpdatedTimer value)? timerUpdated,
    TResult? Function(_HoldCall value)? holdCall,
    TResult? Function(_UnholdCall value)? unholdCall,
    TResult? Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult? Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult? Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult? Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult? Function(_MuteOn value)? muteOn,
    TResult? Function(_MuteOff value)? muteOff,
    TResult? Function(_HandledAllHeadsetStreams value)?
        handledAllHeadsetStreams,
    TResult? Function(_SendDTMFValue value)? sendDTMF,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? register,
    TResult Function(_Initial value)? reset,
    TResult Function(_RegisteredAgain value)? reloadAsterisk,
    TResult Function(_CallStarted value)? callStarted,
    TResult Function(_CallStopped value)? callStopped,
    TResult Function(_HangedUp value)? hangedup,
    TResult Function(_HandledCallStream value)? handledCallStream,
    TResult Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult Function(_UpdatedCall value)? updatedCall,
    TResult Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult Function(_StartedTimer value)? timerStarted,
    TResult Function(_PausedTimer value)? timerPaused,
    TResult Function(_StoppedTimer value)? timerStopped,
    TResult Function(_UpdatedTimer value)? timerUpdated,
    TResult Function(_HoldCall value)? holdCall,
    TResult Function(_UnholdCall value)? unholdCall,
    TResult Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult Function(_MuteOn value)? muteOn,
    TResult Function(_MuteOff value)? muteOff,
    TResult Function(_HandledAllHeadsetStreams value)? handledAllHeadsetStreams,
    TResult Function(_SendDTMFValue value)? sendDTMF,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DialerEventCopyWith<$Res> {
  factory $DialerEventCopyWith(
          DialerEvent value, $Res Function(DialerEvent) then) =
      _$DialerEventCopyWithImpl<$Res, DialerEvent>;
}

/// @nodoc
class _$DialerEventCopyWithImpl<$Res, $Val extends DialerEvent>
    implements $DialerEventCopyWith<$Res> {
  _$DialerEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$StartedImplCopyWith<$Res> {
  factory _$$StartedImplCopyWith(
          _$StartedImpl value, $Res Function(_$StartedImpl) then) =
      __$$StartedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? sipUsername, String? sipPassword});
}

/// @nodoc
class __$$StartedImplCopyWithImpl<$Res>
    extends _$DialerEventCopyWithImpl<$Res, _$StartedImpl>
    implements _$$StartedImplCopyWith<$Res> {
  __$$StartedImplCopyWithImpl(
      _$StartedImpl _value, $Res Function(_$StartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sipUsername = freezed,
    Object? sipPassword = freezed,
  }) {
    return _then(_$StartedImpl(
      sipUsername: freezed == sipUsername
          ? _value.sipUsername
          : sipUsername // ignore: cast_nullable_to_non_nullable
              as String?,
      sipPassword: freezed == sipPassword
          ? _value.sipPassword
          : sipPassword // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$StartedImpl with DiagnosticableTreeMixin implements _Started {
  const _$StartedImpl({this.sipUsername, this.sipPassword});

  @override
  final String? sipUsername;
  @override
  final String? sipPassword;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DialerEvent.register(sipUsername: $sipUsername, sipPassword: $sipPassword)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'DialerEvent.register'))
      ..add(DiagnosticsProperty('sipUsername', sipUsername))
      ..add(DiagnosticsProperty('sipPassword', sipPassword));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StartedImpl &&
            (identical(other.sipUsername, sipUsername) ||
                other.sipUsername == sipUsername) &&
            (identical(other.sipPassword, sipPassword) ||
                other.sipPassword == sipPassword));
  }

  @override
  int get hashCode => Object.hash(runtimeType, sipUsername, sipPassword);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StartedImplCopyWith<_$StartedImpl> get copyWith =>
      __$$StartedImplCopyWithImpl<_$StartedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? sipUsername, String? sipPassword)
        register,
    required TResult Function() reset,
    required TResult Function() reloadAsterisk,
    required TResult Function(String phoneNumber) callStarted,
    required TResult Function() callStopped,
    required TResult Function() hangedup,
    required TResult Function(sip_ua.CallState event) handledCallStream,
    required TResult Function(DialerStatus status) updatedDialerStatus,
    required TResult Function(sip_ua.Call? call) updatedCall,
    required TResult Function(bool isOnHold, String? onHoldOriginator)
        updatedOnHoldStatus,
    required TResult Function() forceEndCurrentCall,
    required TResult Function(sip_ua.CallStateEnum callStateEnum)
        updatedCallStateEnum,
    required TResult Function(sip_ua.RegistrationStateEnum status)
        updatedRegistrationStatus,
    required TResult Function(String? message) sendErrorMessage,
    required TResult Function() timerStarted,
    required TResult Function(int value) timerPaused,
    required TResult Function() timerStopped,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() holdCall,
    required TResult Function() unholdCall,
    required TResult Function() switchToBluetoothHeadset,
    required TResult Function() switchToWiredHeadet,
    required TResult Function() disableSpeakerPhone,
    required TResult Function() switchSpeakerPhoneOn,
    required TResult Function() muteOn,
    required TResult Function() muteOff,
    required TResult Function() handledAllHeadsetStreams,
    required TResult Function(String value) sendDTMF,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
  }) {
    return register(sipUsername, sipPassword);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? sipUsername, String? sipPassword)? register,
    TResult? Function()? reset,
    TResult? Function()? reloadAsterisk,
    TResult? Function(String phoneNumber)? callStarted,
    TResult? Function()? callStopped,
    TResult? Function()? hangedup,
    TResult? Function(sip_ua.CallState event)? handledCallStream,
    TResult? Function(DialerStatus status)? updatedDialerStatus,
    TResult? Function(sip_ua.Call? call)? updatedCall,
    TResult? Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult? Function()? forceEndCurrentCall,
    TResult? Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult? Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult? Function(String? message)? sendErrorMessage,
    TResult? Function()? timerStarted,
    TResult? Function(int value)? timerPaused,
    TResult? Function()? timerStopped,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? holdCall,
    TResult? Function()? unholdCall,
    TResult? Function()? switchToBluetoothHeadset,
    TResult? Function()? switchToWiredHeadet,
    TResult? Function()? disableSpeakerPhone,
    TResult? Function()? switchSpeakerPhoneOn,
    TResult? Function()? muteOn,
    TResult? Function()? muteOff,
    TResult? Function()? handledAllHeadsetStreams,
    TResult? Function(String value)? sendDTMF,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
  }) {
    return register?.call(sipUsername, sipPassword);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? sipUsername, String? sipPassword)? register,
    TResult Function()? reset,
    TResult Function()? reloadAsterisk,
    TResult Function(String phoneNumber)? callStarted,
    TResult Function()? callStopped,
    TResult Function()? hangedup,
    TResult Function(sip_ua.CallState event)? handledCallStream,
    TResult Function(DialerStatus status)? updatedDialerStatus,
    TResult Function(sip_ua.Call? call)? updatedCall,
    TResult Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult Function()? forceEndCurrentCall,
    TResult Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult Function(String? message)? sendErrorMessage,
    TResult Function()? timerStarted,
    TResult Function(int value)? timerPaused,
    TResult Function()? timerStopped,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? holdCall,
    TResult Function()? unholdCall,
    TResult Function()? switchToBluetoothHeadset,
    TResult Function()? switchToWiredHeadet,
    TResult Function()? disableSpeakerPhone,
    TResult Function()? switchSpeakerPhoneOn,
    TResult Function()? muteOn,
    TResult Function()? muteOff,
    TResult Function()? handledAllHeadsetStreams,
    TResult Function(String value)? sendDTMF,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (register != null) {
      return register(sipUsername, sipPassword);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) register,
    required TResult Function(_Initial value) reset,
    required TResult Function(_RegisteredAgain value) reloadAsterisk,
    required TResult Function(_CallStarted value) callStarted,
    required TResult Function(_CallStopped value) callStopped,
    required TResult Function(_HangedUp value) hangedup,
    required TResult Function(_HandledCallStream value) handledCallStream,
    required TResult Function(_UpdatedDialerStatus value) updatedDialerStatus,
    required TResult Function(_UpdatedCall value) updatedCall,
    required TResult Function(_UpdatedOnHoldStatus value) updatedOnHoldStatus,
    required TResult Function(_ForceEndCurrentCall value) forceEndCurrentCall,
    required TResult Function(_UpdatedCallStateEnum value) updatedCallStateEnum,
    required TResult Function(_UpdatedRegistrationStatus value)
        updatedRegistrationStatus,
    required TResult Function(_HandledMessageRequest value) sendErrorMessage,
    required TResult Function(_StartedTimer value) timerStarted,
    required TResult Function(_PausedTimer value) timerPaused,
    required TResult Function(_StoppedTimer value) timerStopped,
    required TResult Function(_UpdatedTimer value) timerUpdated,
    required TResult Function(_HoldCall value) holdCall,
    required TResult Function(_UnholdCall value) unholdCall,
    required TResult Function(_SwitchedToBluetooth value)
        switchToBluetoothHeadset,
    required TResult Function(_SwitchedToWiredHeadsetz value)
        switchToWiredHeadet,
    required TResult Function(_SwitchedSpeakerOff value) disableSpeakerPhone,
    required TResult Function(_SwitchSpeakerphoneOn value) switchSpeakerPhoneOn,
    required TResult Function(_MuteOn value) muteOn,
    required TResult Function(_MuteOff value) muteOff,
    required TResult Function(_HandledAllHeadsetStreams value)
        handledAllHeadsetStreams,
    required TResult Function(_SendDTMFValue value) sendDTMF,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
  }) {
    return register(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? register,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_RegisteredAgain value)? reloadAsterisk,
    TResult? Function(_CallStarted value)? callStarted,
    TResult? Function(_CallStopped value)? callStopped,
    TResult? Function(_HangedUp value)? hangedup,
    TResult? Function(_HandledCallStream value)? handledCallStream,
    TResult? Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult? Function(_UpdatedCall value)? updatedCall,
    TResult? Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult? Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult? Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult? Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult? Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult? Function(_StartedTimer value)? timerStarted,
    TResult? Function(_PausedTimer value)? timerPaused,
    TResult? Function(_StoppedTimer value)? timerStopped,
    TResult? Function(_UpdatedTimer value)? timerUpdated,
    TResult? Function(_HoldCall value)? holdCall,
    TResult? Function(_UnholdCall value)? unholdCall,
    TResult? Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult? Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult? Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult? Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult? Function(_MuteOn value)? muteOn,
    TResult? Function(_MuteOff value)? muteOff,
    TResult? Function(_HandledAllHeadsetStreams value)?
        handledAllHeadsetStreams,
    TResult? Function(_SendDTMFValue value)? sendDTMF,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
  }) {
    return register?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? register,
    TResult Function(_Initial value)? reset,
    TResult Function(_RegisteredAgain value)? reloadAsterisk,
    TResult Function(_CallStarted value)? callStarted,
    TResult Function(_CallStopped value)? callStopped,
    TResult Function(_HangedUp value)? hangedup,
    TResult Function(_HandledCallStream value)? handledCallStream,
    TResult Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult Function(_UpdatedCall value)? updatedCall,
    TResult Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult Function(_StartedTimer value)? timerStarted,
    TResult Function(_PausedTimer value)? timerPaused,
    TResult Function(_StoppedTimer value)? timerStopped,
    TResult Function(_UpdatedTimer value)? timerUpdated,
    TResult Function(_HoldCall value)? holdCall,
    TResult Function(_UnholdCall value)? unholdCall,
    TResult Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult Function(_MuteOn value)? muteOn,
    TResult Function(_MuteOff value)? muteOff,
    TResult Function(_HandledAllHeadsetStreams value)? handledAllHeadsetStreams,
    TResult Function(_SendDTMFValue value)? sendDTMF,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (register != null) {
      return register(this);
    }
    return orElse();
  }
}

abstract class _Started implements DialerEvent {
  const factory _Started(
      {final String? sipUsername, final String? sipPassword}) = _$StartedImpl;

  String? get sipUsername;
  String? get sipPassword;

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StartedImplCopyWith<_$StartedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$DialerEventCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl with DiagnosticableTreeMixin implements _Initial {
  const _$InitialImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DialerEvent.reset()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'DialerEvent.reset'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? sipUsername, String? sipPassword)
        register,
    required TResult Function() reset,
    required TResult Function() reloadAsterisk,
    required TResult Function(String phoneNumber) callStarted,
    required TResult Function() callStopped,
    required TResult Function() hangedup,
    required TResult Function(sip_ua.CallState event) handledCallStream,
    required TResult Function(DialerStatus status) updatedDialerStatus,
    required TResult Function(sip_ua.Call? call) updatedCall,
    required TResult Function(bool isOnHold, String? onHoldOriginator)
        updatedOnHoldStatus,
    required TResult Function() forceEndCurrentCall,
    required TResult Function(sip_ua.CallStateEnum callStateEnum)
        updatedCallStateEnum,
    required TResult Function(sip_ua.RegistrationStateEnum status)
        updatedRegistrationStatus,
    required TResult Function(String? message) sendErrorMessage,
    required TResult Function() timerStarted,
    required TResult Function(int value) timerPaused,
    required TResult Function() timerStopped,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() holdCall,
    required TResult Function() unholdCall,
    required TResult Function() switchToBluetoothHeadset,
    required TResult Function() switchToWiredHeadet,
    required TResult Function() disableSpeakerPhone,
    required TResult Function() switchSpeakerPhoneOn,
    required TResult Function() muteOn,
    required TResult Function() muteOff,
    required TResult Function() handledAllHeadsetStreams,
    required TResult Function(String value) sendDTMF,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
  }) {
    return reset();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? sipUsername, String? sipPassword)? register,
    TResult? Function()? reset,
    TResult? Function()? reloadAsterisk,
    TResult? Function(String phoneNumber)? callStarted,
    TResult? Function()? callStopped,
    TResult? Function()? hangedup,
    TResult? Function(sip_ua.CallState event)? handledCallStream,
    TResult? Function(DialerStatus status)? updatedDialerStatus,
    TResult? Function(sip_ua.Call? call)? updatedCall,
    TResult? Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult? Function()? forceEndCurrentCall,
    TResult? Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult? Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult? Function(String? message)? sendErrorMessage,
    TResult? Function()? timerStarted,
    TResult? Function(int value)? timerPaused,
    TResult? Function()? timerStopped,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? holdCall,
    TResult? Function()? unholdCall,
    TResult? Function()? switchToBluetoothHeadset,
    TResult? Function()? switchToWiredHeadet,
    TResult? Function()? disableSpeakerPhone,
    TResult? Function()? switchSpeakerPhoneOn,
    TResult? Function()? muteOn,
    TResult? Function()? muteOff,
    TResult? Function()? handledAllHeadsetStreams,
    TResult? Function(String value)? sendDTMF,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
  }) {
    return reset?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? sipUsername, String? sipPassword)? register,
    TResult Function()? reset,
    TResult Function()? reloadAsterisk,
    TResult Function(String phoneNumber)? callStarted,
    TResult Function()? callStopped,
    TResult Function()? hangedup,
    TResult Function(sip_ua.CallState event)? handledCallStream,
    TResult Function(DialerStatus status)? updatedDialerStatus,
    TResult Function(sip_ua.Call? call)? updatedCall,
    TResult Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult Function()? forceEndCurrentCall,
    TResult Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult Function(String? message)? sendErrorMessage,
    TResult Function()? timerStarted,
    TResult Function(int value)? timerPaused,
    TResult Function()? timerStopped,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? holdCall,
    TResult Function()? unholdCall,
    TResult Function()? switchToBluetoothHeadset,
    TResult Function()? switchToWiredHeadet,
    TResult Function()? disableSpeakerPhone,
    TResult Function()? switchSpeakerPhoneOn,
    TResult Function()? muteOn,
    TResult Function()? muteOff,
    TResult Function()? handledAllHeadsetStreams,
    TResult Function(String value)? sendDTMF,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (reset != null) {
      return reset();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) register,
    required TResult Function(_Initial value) reset,
    required TResult Function(_RegisteredAgain value) reloadAsterisk,
    required TResult Function(_CallStarted value) callStarted,
    required TResult Function(_CallStopped value) callStopped,
    required TResult Function(_HangedUp value) hangedup,
    required TResult Function(_HandledCallStream value) handledCallStream,
    required TResult Function(_UpdatedDialerStatus value) updatedDialerStatus,
    required TResult Function(_UpdatedCall value) updatedCall,
    required TResult Function(_UpdatedOnHoldStatus value) updatedOnHoldStatus,
    required TResult Function(_ForceEndCurrentCall value) forceEndCurrentCall,
    required TResult Function(_UpdatedCallStateEnum value) updatedCallStateEnum,
    required TResult Function(_UpdatedRegistrationStatus value)
        updatedRegistrationStatus,
    required TResult Function(_HandledMessageRequest value) sendErrorMessage,
    required TResult Function(_StartedTimer value) timerStarted,
    required TResult Function(_PausedTimer value) timerPaused,
    required TResult Function(_StoppedTimer value) timerStopped,
    required TResult Function(_UpdatedTimer value) timerUpdated,
    required TResult Function(_HoldCall value) holdCall,
    required TResult Function(_UnholdCall value) unholdCall,
    required TResult Function(_SwitchedToBluetooth value)
        switchToBluetoothHeadset,
    required TResult Function(_SwitchedToWiredHeadsetz value)
        switchToWiredHeadet,
    required TResult Function(_SwitchedSpeakerOff value) disableSpeakerPhone,
    required TResult Function(_SwitchSpeakerphoneOn value) switchSpeakerPhoneOn,
    required TResult Function(_MuteOn value) muteOn,
    required TResult Function(_MuteOff value) muteOff,
    required TResult Function(_HandledAllHeadsetStreams value)
        handledAllHeadsetStreams,
    required TResult Function(_SendDTMFValue value) sendDTMF,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
  }) {
    return reset(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? register,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_RegisteredAgain value)? reloadAsterisk,
    TResult? Function(_CallStarted value)? callStarted,
    TResult? Function(_CallStopped value)? callStopped,
    TResult? Function(_HangedUp value)? hangedup,
    TResult? Function(_HandledCallStream value)? handledCallStream,
    TResult? Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult? Function(_UpdatedCall value)? updatedCall,
    TResult? Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult? Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult? Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult? Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult? Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult? Function(_StartedTimer value)? timerStarted,
    TResult? Function(_PausedTimer value)? timerPaused,
    TResult? Function(_StoppedTimer value)? timerStopped,
    TResult? Function(_UpdatedTimer value)? timerUpdated,
    TResult? Function(_HoldCall value)? holdCall,
    TResult? Function(_UnholdCall value)? unholdCall,
    TResult? Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult? Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult? Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult? Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult? Function(_MuteOn value)? muteOn,
    TResult? Function(_MuteOff value)? muteOff,
    TResult? Function(_HandledAllHeadsetStreams value)?
        handledAllHeadsetStreams,
    TResult? Function(_SendDTMFValue value)? sendDTMF,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
  }) {
    return reset?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? register,
    TResult Function(_Initial value)? reset,
    TResult Function(_RegisteredAgain value)? reloadAsterisk,
    TResult Function(_CallStarted value)? callStarted,
    TResult Function(_CallStopped value)? callStopped,
    TResult Function(_HangedUp value)? hangedup,
    TResult Function(_HandledCallStream value)? handledCallStream,
    TResult Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult Function(_UpdatedCall value)? updatedCall,
    TResult Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult Function(_StartedTimer value)? timerStarted,
    TResult Function(_PausedTimer value)? timerPaused,
    TResult Function(_StoppedTimer value)? timerStopped,
    TResult Function(_UpdatedTimer value)? timerUpdated,
    TResult Function(_HoldCall value)? holdCall,
    TResult Function(_UnholdCall value)? unholdCall,
    TResult Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult Function(_MuteOn value)? muteOn,
    TResult Function(_MuteOff value)? muteOff,
    TResult Function(_HandledAllHeadsetStreams value)? handledAllHeadsetStreams,
    TResult Function(_SendDTMFValue value)? sendDTMF,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (reset != null) {
      return reset(this);
    }
    return orElse();
  }
}

abstract class _Initial implements DialerEvent {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$RegisteredAgainImplCopyWith<$Res> {
  factory _$$RegisteredAgainImplCopyWith(_$RegisteredAgainImpl value,
          $Res Function(_$RegisteredAgainImpl) then) =
      __$$RegisteredAgainImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$RegisteredAgainImplCopyWithImpl<$Res>
    extends _$DialerEventCopyWithImpl<$Res, _$RegisteredAgainImpl>
    implements _$$RegisteredAgainImplCopyWith<$Res> {
  __$$RegisteredAgainImplCopyWithImpl(
      _$RegisteredAgainImpl _value, $Res Function(_$RegisteredAgainImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$RegisteredAgainImpl
    with DiagnosticableTreeMixin
    implements _RegisteredAgain {
  const _$RegisteredAgainImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DialerEvent.reloadAsterisk()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'DialerEvent.reloadAsterisk'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$RegisteredAgainImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? sipUsername, String? sipPassword)
        register,
    required TResult Function() reset,
    required TResult Function() reloadAsterisk,
    required TResult Function(String phoneNumber) callStarted,
    required TResult Function() callStopped,
    required TResult Function() hangedup,
    required TResult Function(sip_ua.CallState event) handledCallStream,
    required TResult Function(DialerStatus status) updatedDialerStatus,
    required TResult Function(sip_ua.Call? call) updatedCall,
    required TResult Function(bool isOnHold, String? onHoldOriginator)
        updatedOnHoldStatus,
    required TResult Function() forceEndCurrentCall,
    required TResult Function(sip_ua.CallStateEnum callStateEnum)
        updatedCallStateEnum,
    required TResult Function(sip_ua.RegistrationStateEnum status)
        updatedRegistrationStatus,
    required TResult Function(String? message) sendErrorMessage,
    required TResult Function() timerStarted,
    required TResult Function(int value) timerPaused,
    required TResult Function() timerStopped,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() holdCall,
    required TResult Function() unholdCall,
    required TResult Function() switchToBluetoothHeadset,
    required TResult Function() switchToWiredHeadet,
    required TResult Function() disableSpeakerPhone,
    required TResult Function() switchSpeakerPhoneOn,
    required TResult Function() muteOn,
    required TResult Function() muteOff,
    required TResult Function() handledAllHeadsetStreams,
    required TResult Function(String value) sendDTMF,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
  }) {
    return reloadAsterisk();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? sipUsername, String? sipPassword)? register,
    TResult? Function()? reset,
    TResult? Function()? reloadAsterisk,
    TResult? Function(String phoneNumber)? callStarted,
    TResult? Function()? callStopped,
    TResult? Function()? hangedup,
    TResult? Function(sip_ua.CallState event)? handledCallStream,
    TResult? Function(DialerStatus status)? updatedDialerStatus,
    TResult? Function(sip_ua.Call? call)? updatedCall,
    TResult? Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult? Function()? forceEndCurrentCall,
    TResult? Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult? Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult? Function(String? message)? sendErrorMessage,
    TResult? Function()? timerStarted,
    TResult? Function(int value)? timerPaused,
    TResult? Function()? timerStopped,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? holdCall,
    TResult? Function()? unholdCall,
    TResult? Function()? switchToBluetoothHeadset,
    TResult? Function()? switchToWiredHeadet,
    TResult? Function()? disableSpeakerPhone,
    TResult? Function()? switchSpeakerPhoneOn,
    TResult? Function()? muteOn,
    TResult? Function()? muteOff,
    TResult? Function()? handledAllHeadsetStreams,
    TResult? Function(String value)? sendDTMF,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
  }) {
    return reloadAsterisk?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? sipUsername, String? sipPassword)? register,
    TResult Function()? reset,
    TResult Function()? reloadAsterisk,
    TResult Function(String phoneNumber)? callStarted,
    TResult Function()? callStopped,
    TResult Function()? hangedup,
    TResult Function(sip_ua.CallState event)? handledCallStream,
    TResult Function(DialerStatus status)? updatedDialerStatus,
    TResult Function(sip_ua.Call? call)? updatedCall,
    TResult Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult Function()? forceEndCurrentCall,
    TResult Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult Function(String? message)? sendErrorMessage,
    TResult Function()? timerStarted,
    TResult Function(int value)? timerPaused,
    TResult Function()? timerStopped,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? holdCall,
    TResult Function()? unholdCall,
    TResult Function()? switchToBluetoothHeadset,
    TResult Function()? switchToWiredHeadet,
    TResult Function()? disableSpeakerPhone,
    TResult Function()? switchSpeakerPhoneOn,
    TResult Function()? muteOn,
    TResult Function()? muteOff,
    TResult Function()? handledAllHeadsetStreams,
    TResult Function(String value)? sendDTMF,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (reloadAsterisk != null) {
      return reloadAsterisk();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) register,
    required TResult Function(_Initial value) reset,
    required TResult Function(_RegisteredAgain value) reloadAsterisk,
    required TResult Function(_CallStarted value) callStarted,
    required TResult Function(_CallStopped value) callStopped,
    required TResult Function(_HangedUp value) hangedup,
    required TResult Function(_HandledCallStream value) handledCallStream,
    required TResult Function(_UpdatedDialerStatus value) updatedDialerStatus,
    required TResult Function(_UpdatedCall value) updatedCall,
    required TResult Function(_UpdatedOnHoldStatus value) updatedOnHoldStatus,
    required TResult Function(_ForceEndCurrentCall value) forceEndCurrentCall,
    required TResult Function(_UpdatedCallStateEnum value) updatedCallStateEnum,
    required TResult Function(_UpdatedRegistrationStatus value)
        updatedRegistrationStatus,
    required TResult Function(_HandledMessageRequest value) sendErrorMessage,
    required TResult Function(_StartedTimer value) timerStarted,
    required TResult Function(_PausedTimer value) timerPaused,
    required TResult Function(_StoppedTimer value) timerStopped,
    required TResult Function(_UpdatedTimer value) timerUpdated,
    required TResult Function(_HoldCall value) holdCall,
    required TResult Function(_UnholdCall value) unholdCall,
    required TResult Function(_SwitchedToBluetooth value)
        switchToBluetoothHeadset,
    required TResult Function(_SwitchedToWiredHeadsetz value)
        switchToWiredHeadet,
    required TResult Function(_SwitchedSpeakerOff value) disableSpeakerPhone,
    required TResult Function(_SwitchSpeakerphoneOn value) switchSpeakerPhoneOn,
    required TResult Function(_MuteOn value) muteOn,
    required TResult Function(_MuteOff value) muteOff,
    required TResult Function(_HandledAllHeadsetStreams value)
        handledAllHeadsetStreams,
    required TResult Function(_SendDTMFValue value) sendDTMF,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
  }) {
    return reloadAsterisk(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? register,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_RegisteredAgain value)? reloadAsterisk,
    TResult? Function(_CallStarted value)? callStarted,
    TResult? Function(_CallStopped value)? callStopped,
    TResult? Function(_HangedUp value)? hangedup,
    TResult? Function(_HandledCallStream value)? handledCallStream,
    TResult? Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult? Function(_UpdatedCall value)? updatedCall,
    TResult? Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult? Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult? Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult? Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult? Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult? Function(_StartedTimer value)? timerStarted,
    TResult? Function(_PausedTimer value)? timerPaused,
    TResult? Function(_StoppedTimer value)? timerStopped,
    TResult? Function(_UpdatedTimer value)? timerUpdated,
    TResult? Function(_HoldCall value)? holdCall,
    TResult? Function(_UnholdCall value)? unholdCall,
    TResult? Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult? Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult? Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult? Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult? Function(_MuteOn value)? muteOn,
    TResult? Function(_MuteOff value)? muteOff,
    TResult? Function(_HandledAllHeadsetStreams value)?
        handledAllHeadsetStreams,
    TResult? Function(_SendDTMFValue value)? sendDTMF,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
  }) {
    return reloadAsterisk?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? register,
    TResult Function(_Initial value)? reset,
    TResult Function(_RegisteredAgain value)? reloadAsterisk,
    TResult Function(_CallStarted value)? callStarted,
    TResult Function(_CallStopped value)? callStopped,
    TResult Function(_HangedUp value)? hangedup,
    TResult Function(_HandledCallStream value)? handledCallStream,
    TResult Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult Function(_UpdatedCall value)? updatedCall,
    TResult Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult Function(_StartedTimer value)? timerStarted,
    TResult Function(_PausedTimer value)? timerPaused,
    TResult Function(_StoppedTimer value)? timerStopped,
    TResult Function(_UpdatedTimer value)? timerUpdated,
    TResult Function(_HoldCall value)? holdCall,
    TResult Function(_UnholdCall value)? unholdCall,
    TResult Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult Function(_MuteOn value)? muteOn,
    TResult Function(_MuteOff value)? muteOff,
    TResult Function(_HandledAllHeadsetStreams value)? handledAllHeadsetStreams,
    TResult Function(_SendDTMFValue value)? sendDTMF,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (reloadAsterisk != null) {
      return reloadAsterisk(this);
    }
    return orElse();
  }
}

abstract class _RegisteredAgain implements DialerEvent {
  const factory _RegisteredAgain() = _$RegisteredAgainImpl;
}

/// @nodoc
abstract class _$$CallStartedImplCopyWith<$Res> {
  factory _$$CallStartedImplCopyWith(
          _$CallStartedImpl value, $Res Function(_$CallStartedImpl) then) =
      __$$CallStartedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String phoneNumber});
}

/// @nodoc
class __$$CallStartedImplCopyWithImpl<$Res>
    extends _$DialerEventCopyWithImpl<$Res, _$CallStartedImpl>
    implements _$$CallStartedImplCopyWith<$Res> {
  __$$CallStartedImplCopyWithImpl(
      _$CallStartedImpl _value, $Res Function(_$CallStartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phoneNumber = null,
  }) {
    return _then(_$CallStartedImpl(
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$CallStartedImpl with DiagnosticableTreeMixin implements _CallStarted {
  const _$CallStartedImpl({required this.phoneNumber});

  @override
  final String phoneNumber;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DialerEvent.callStarted(phoneNumber: $phoneNumber)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'DialerEvent.callStarted'))
      ..add(DiagnosticsProperty('phoneNumber', phoneNumber));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CallStartedImpl &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber));
  }

  @override
  int get hashCode => Object.hash(runtimeType, phoneNumber);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CallStartedImplCopyWith<_$CallStartedImpl> get copyWith =>
      __$$CallStartedImplCopyWithImpl<_$CallStartedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? sipUsername, String? sipPassword)
        register,
    required TResult Function() reset,
    required TResult Function() reloadAsterisk,
    required TResult Function(String phoneNumber) callStarted,
    required TResult Function() callStopped,
    required TResult Function() hangedup,
    required TResult Function(sip_ua.CallState event) handledCallStream,
    required TResult Function(DialerStatus status) updatedDialerStatus,
    required TResult Function(sip_ua.Call? call) updatedCall,
    required TResult Function(bool isOnHold, String? onHoldOriginator)
        updatedOnHoldStatus,
    required TResult Function() forceEndCurrentCall,
    required TResult Function(sip_ua.CallStateEnum callStateEnum)
        updatedCallStateEnum,
    required TResult Function(sip_ua.RegistrationStateEnum status)
        updatedRegistrationStatus,
    required TResult Function(String? message) sendErrorMessage,
    required TResult Function() timerStarted,
    required TResult Function(int value) timerPaused,
    required TResult Function() timerStopped,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() holdCall,
    required TResult Function() unholdCall,
    required TResult Function() switchToBluetoothHeadset,
    required TResult Function() switchToWiredHeadet,
    required TResult Function() disableSpeakerPhone,
    required TResult Function() switchSpeakerPhoneOn,
    required TResult Function() muteOn,
    required TResult Function() muteOff,
    required TResult Function() handledAllHeadsetStreams,
    required TResult Function(String value) sendDTMF,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
  }) {
    return callStarted(phoneNumber);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? sipUsername, String? sipPassword)? register,
    TResult? Function()? reset,
    TResult? Function()? reloadAsterisk,
    TResult? Function(String phoneNumber)? callStarted,
    TResult? Function()? callStopped,
    TResult? Function()? hangedup,
    TResult? Function(sip_ua.CallState event)? handledCallStream,
    TResult? Function(DialerStatus status)? updatedDialerStatus,
    TResult? Function(sip_ua.Call? call)? updatedCall,
    TResult? Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult? Function()? forceEndCurrentCall,
    TResult? Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult? Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult? Function(String? message)? sendErrorMessage,
    TResult? Function()? timerStarted,
    TResult? Function(int value)? timerPaused,
    TResult? Function()? timerStopped,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? holdCall,
    TResult? Function()? unholdCall,
    TResult? Function()? switchToBluetoothHeadset,
    TResult? Function()? switchToWiredHeadet,
    TResult? Function()? disableSpeakerPhone,
    TResult? Function()? switchSpeakerPhoneOn,
    TResult? Function()? muteOn,
    TResult? Function()? muteOff,
    TResult? Function()? handledAllHeadsetStreams,
    TResult? Function(String value)? sendDTMF,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
  }) {
    return callStarted?.call(phoneNumber);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? sipUsername, String? sipPassword)? register,
    TResult Function()? reset,
    TResult Function()? reloadAsterisk,
    TResult Function(String phoneNumber)? callStarted,
    TResult Function()? callStopped,
    TResult Function()? hangedup,
    TResult Function(sip_ua.CallState event)? handledCallStream,
    TResult Function(DialerStatus status)? updatedDialerStatus,
    TResult Function(sip_ua.Call? call)? updatedCall,
    TResult Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult Function()? forceEndCurrentCall,
    TResult Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult Function(String? message)? sendErrorMessage,
    TResult Function()? timerStarted,
    TResult Function(int value)? timerPaused,
    TResult Function()? timerStopped,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? holdCall,
    TResult Function()? unholdCall,
    TResult Function()? switchToBluetoothHeadset,
    TResult Function()? switchToWiredHeadet,
    TResult Function()? disableSpeakerPhone,
    TResult Function()? switchSpeakerPhoneOn,
    TResult Function()? muteOn,
    TResult Function()? muteOff,
    TResult Function()? handledAllHeadsetStreams,
    TResult Function(String value)? sendDTMF,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (callStarted != null) {
      return callStarted(phoneNumber);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) register,
    required TResult Function(_Initial value) reset,
    required TResult Function(_RegisteredAgain value) reloadAsterisk,
    required TResult Function(_CallStarted value) callStarted,
    required TResult Function(_CallStopped value) callStopped,
    required TResult Function(_HangedUp value) hangedup,
    required TResult Function(_HandledCallStream value) handledCallStream,
    required TResult Function(_UpdatedDialerStatus value) updatedDialerStatus,
    required TResult Function(_UpdatedCall value) updatedCall,
    required TResult Function(_UpdatedOnHoldStatus value) updatedOnHoldStatus,
    required TResult Function(_ForceEndCurrentCall value) forceEndCurrentCall,
    required TResult Function(_UpdatedCallStateEnum value) updatedCallStateEnum,
    required TResult Function(_UpdatedRegistrationStatus value)
        updatedRegistrationStatus,
    required TResult Function(_HandledMessageRequest value) sendErrorMessage,
    required TResult Function(_StartedTimer value) timerStarted,
    required TResult Function(_PausedTimer value) timerPaused,
    required TResult Function(_StoppedTimer value) timerStopped,
    required TResult Function(_UpdatedTimer value) timerUpdated,
    required TResult Function(_HoldCall value) holdCall,
    required TResult Function(_UnholdCall value) unholdCall,
    required TResult Function(_SwitchedToBluetooth value)
        switchToBluetoothHeadset,
    required TResult Function(_SwitchedToWiredHeadsetz value)
        switchToWiredHeadet,
    required TResult Function(_SwitchedSpeakerOff value) disableSpeakerPhone,
    required TResult Function(_SwitchSpeakerphoneOn value) switchSpeakerPhoneOn,
    required TResult Function(_MuteOn value) muteOn,
    required TResult Function(_MuteOff value) muteOff,
    required TResult Function(_HandledAllHeadsetStreams value)
        handledAllHeadsetStreams,
    required TResult Function(_SendDTMFValue value) sendDTMF,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
  }) {
    return callStarted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? register,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_RegisteredAgain value)? reloadAsterisk,
    TResult? Function(_CallStarted value)? callStarted,
    TResult? Function(_CallStopped value)? callStopped,
    TResult? Function(_HangedUp value)? hangedup,
    TResult? Function(_HandledCallStream value)? handledCallStream,
    TResult? Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult? Function(_UpdatedCall value)? updatedCall,
    TResult? Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult? Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult? Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult? Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult? Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult? Function(_StartedTimer value)? timerStarted,
    TResult? Function(_PausedTimer value)? timerPaused,
    TResult? Function(_StoppedTimer value)? timerStopped,
    TResult? Function(_UpdatedTimer value)? timerUpdated,
    TResult? Function(_HoldCall value)? holdCall,
    TResult? Function(_UnholdCall value)? unholdCall,
    TResult? Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult? Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult? Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult? Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult? Function(_MuteOn value)? muteOn,
    TResult? Function(_MuteOff value)? muteOff,
    TResult? Function(_HandledAllHeadsetStreams value)?
        handledAllHeadsetStreams,
    TResult? Function(_SendDTMFValue value)? sendDTMF,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
  }) {
    return callStarted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? register,
    TResult Function(_Initial value)? reset,
    TResult Function(_RegisteredAgain value)? reloadAsterisk,
    TResult Function(_CallStarted value)? callStarted,
    TResult Function(_CallStopped value)? callStopped,
    TResult Function(_HangedUp value)? hangedup,
    TResult Function(_HandledCallStream value)? handledCallStream,
    TResult Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult Function(_UpdatedCall value)? updatedCall,
    TResult Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult Function(_StartedTimer value)? timerStarted,
    TResult Function(_PausedTimer value)? timerPaused,
    TResult Function(_StoppedTimer value)? timerStopped,
    TResult Function(_UpdatedTimer value)? timerUpdated,
    TResult Function(_HoldCall value)? holdCall,
    TResult Function(_UnholdCall value)? unholdCall,
    TResult Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult Function(_MuteOn value)? muteOn,
    TResult Function(_MuteOff value)? muteOff,
    TResult Function(_HandledAllHeadsetStreams value)? handledAllHeadsetStreams,
    TResult Function(_SendDTMFValue value)? sendDTMF,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (callStarted != null) {
      return callStarted(this);
    }
    return orElse();
  }
}

abstract class _CallStarted implements DialerEvent {
  const factory _CallStarted({required final String phoneNumber}) =
      _$CallStartedImpl;

  String get phoneNumber;

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CallStartedImplCopyWith<_$CallStartedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CallStoppedImplCopyWith<$Res> {
  factory _$$CallStoppedImplCopyWith(
          _$CallStoppedImpl value, $Res Function(_$CallStoppedImpl) then) =
      __$$CallStoppedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CallStoppedImplCopyWithImpl<$Res>
    extends _$DialerEventCopyWithImpl<$Res, _$CallStoppedImpl>
    implements _$$CallStoppedImplCopyWith<$Res> {
  __$$CallStoppedImplCopyWithImpl(
      _$CallStoppedImpl _value, $Res Function(_$CallStoppedImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CallStoppedImpl with DiagnosticableTreeMixin implements _CallStopped {
  const _$CallStoppedImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DialerEvent.callStopped()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'DialerEvent.callStopped'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CallStoppedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? sipUsername, String? sipPassword)
        register,
    required TResult Function() reset,
    required TResult Function() reloadAsterisk,
    required TResult Function(String phoneNumber) callStarted,
    required TResult Function() callStopped,
    required TResult Function() hangedup,
    required TResult Function(sip_ua.CallState event) handledCallStream,
    required TResult Function(DialerStatus status) updatedDialerStatus,
    required TResult Function(sip_ua.Call? call) updatedCall,
    required TResult Function(bool isOnHold, String? onHoldOriginator)
        updatedOnHoldStatus,
    required TResult Function() forceEndCurrentCall,
    required TResult Function(sip_ua.CallStateEnum callStateEnum)
        updatedCallStateEnum,
    required TResult Function(sip_ua.RegistrationStateEnum status)
        updatedRegistrationStatus,
    required TResult Function(String? message) sendErrorMessage,
    required TResult Function() timerStarted,
    required TResult Function(int value) timerPaused,
    required TResult Function() timerStopped,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() holdCall,
    required TResult Function() unholdCall,
    required TResult Function() switchToBluetoothHeadset,
    required TResult Function() switchToWiredHeadet,
    required TResult Function() disableSpeakerPhone,
    required TResult Function() switchSpeakerPhoneOn,
    required TResult Function() muteOn,
    required TResult Function() muteOff,
    required TResult Function() handledAllHeadsetStreams,
    required TResult Function(String value) sendDTMF,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
  }) {
    return callStopped();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? sipUsername, String? sipPassword)? register,
    TResult? Function()? reset,
    TResult? Function()? reloadAsterisk,
    TResult? Function(String phoneNumber)? callStarted,
    TResult? Function()? callStopped,
    TResult? Function()? hangedup,
    TResult? Function(sip_ua.CallState event)? handledCallStream,
    TResult? Function(DialerStatus status)? updatedDialerStatus,
    TResult? Function(sip_ua.Call? call)? updatedCall,
    TResult? Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult? Function()? forceEndCurrentCall,
    TResult? Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult? Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult? Function(String? message)? sendErrorMessage,
    TResult? Function()? timerStarted,
    TResult? Function(int value)? timerPaused,
    TResult? Function()? timerStopped,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? holdCall,
    TResult? Function()? unholdCall,
    TResult? Function()? switchToBluetoothHeadset,
    TResult? Function()? switchToWiredHeadet,
    TResult? Function()? disableSpeakerPhone,
    TResult? Function()? switchSpeakerPhoneOn,
    TResult? Function()? muteOn,
    TResult? Function()? muteOff,
    TResult? Function()? handledAllHeadsetStreams,
    TResult? Function(String value)? sendDTMF,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
  }) {
    return callStopped?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? sipUsername, String? sipPassword)? register,
    TResult Function()? reset,
    TResult Function()? reloadAsterisk,
    TResult Function(String phoneNumber)? callStarted,
    TResult Function()? callStopped,
    TResult Function()? hangedup,
    TResult Function(sip_ua.CallState event)? handledCallStream,
    TResult Function(DialerStatus status)? updatedDialerStatus,
    TResult Function(sip_ua.Call? call)? updatedCall,
    TResult Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult Function()? forceEndCurrentCall,
    TResult Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult Function(String? message)? sendErrorMessage,
    TResult Function()? timerStarted,
    TResult Function(int value)? timerPaused,
    TResult Function()? timerStopped,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? holdCall,
    TResult Function()? unholdCall,
    TResult Function()? switchToBluetoothHeadset,
    TResult Function()? switchToWiredHeadet,
    TResult Function()? disableSpeakerPhone,
    TResult Function()? switchSpeakerPhoneOn,
    TResult Function()? muteOn,
    TResult Function()? muteOff,
    TResult Function()? handledAllHeadsetStreams,
    TResult Function(String value)? sendDTMF,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (callStopped != null) {
      return callStopped();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) register,
    required TResult Function(_Initial value) reset,
    required TResult Function(_RegisteredAgain value) reloadAsterisk,
    required TResult Function(_CallStarted value) callStarted,
    required TResult Function(_CallStopped value) callStopped,
    required TResult Function(_HangedUp value) hangedup,
    required TResult Function(_HandledCallStream value) handledCallStream,
    required TResult Function(_UpdatedDialerStatus value) updatedDialerStatus,
    required TResult Function(_UpdatedCall value) updatedCall,
    required TResult Function(_UpdatedOnHoldStatus value) updatedOnHoldStatus,
    required TResult Function(_ForceEndCurrentCall value) forceEndCurrentCall,
    required TResult Function(_UpdatedCallStateEnum value) updatedCallStateEnum,
    required TResult Function(_UpdatedRegistrationStatus value)
        updatedRegistrationStatus,
    required TResult Function(_HandledMessageRequest value) sendErrorMessage,
    required TResult Function(_StartedTimer value) timerStarted,
    required TResult Function(_PausedTimer value) timerPaused,
    required TResult Function(_StoppedTimer value) timerStopped,
    required TResult Function(_UpdatedTimer value) timerUpdated,
    required TResult Function(_HoldCall value) holdCall,
    required TResult Function(_UnholdCall value) unholdCall,
    required TResult Function(_SwitchedToBluetooth value)
        switchToBluetoothHeadset,
    required TResult Function(_SwitchedToWiredHeadsetz value)
        switchToWiredHeadet,
    required TResult Function(_SwitchedSpeakerOff value) disableSpeakerPhone,
    required TResult Function(_SwitchSpeakerphoneOn value) switchSpeakerPhoneOn,
    required TResult Function(_MuteOn value) muteOn,
    required TResult Function(_MuteOff value) muteOff,
    required TResult Function(_HandledAllHeadsetStreams value)
        handledAllHeadsetStreams,
    required TResult Function(_SendDTMFValue value) sendDTMF,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
  }) {
    return callStopped(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? register,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_RegisteredAgain value)? reloadAsterisk,
    TResult? Function(_CallStarted value)? callStarted,
    TResult? Function(_CallStopped value)? callStopped,
    TResult? Function(_HangedUp value)? hangedup,
    TResult? Function(_HandledCallStream value)? handledCallStream,
    TResult? Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult? Function(_UpdatedCall value)? updatedCall,
    TResult? Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult? Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult? Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult? Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult? Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult? Function(_StartedTimer value)? timerStarted,
    TResult? Function(_PausedTimer value)? timerPaused,
    TResult? Function(_StoppedTimer value)? timerStopped,
    TResult? Function(_UpdatedTimer value)? timerUpdated,
    TResult? Function(_HoldCall value)? holdCall,
    TResult? Function(_UnholdCall value)? unholdCall,
    TResult? Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult? Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult? Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult? Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult? Function(_MuteOn value)? muteOn,
    TResult? Function(_MuteOff value)? muteOff,
    TResult? Function(_HandledAllHeadsetStreams value)?
        handledAllHeadsetStreams,
    TResult? Function(_SendDTMFValue value)? sendDTMF,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
  }) {
    return callStopped?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? register,
    TResult Function(_Initial value)? reset,
    TResult Function(_RegisteredAgain value)? reloadAsterisk,
    TResult Function(_CallStarted value)? callStarted,
    TResult Function(_CallStopped value)? callStopped,
    TResult Function(_HangedUp value)? hangedup,
    TResult Function(_HandledCallStream value)? handledCallStream,
    TResult Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult Function(_UpdatedCall value)? updatedCall,
    TResult Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult Function(_StartedTimer value)? timerStarted,
    TResult Function(_PausedTimer value)? timerPaused,
    TResult Function(_StoppedTimer value)? timerStopped,
    TResult Function(_UpdatedTimer value)? timerUpdated,
    TResult Function(_HoldCall value)? holdCall,
    TResult Function(_UnholdCall value)? unholdCall,
    TResult Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult Function(_MuteOn value)? muteOn,
    TResult Function(_MuteOff value)? muteOff,
    TResult Function(_HandledAllHeadsetStreams value)? handledAllHeadsetStreams,
    TResult Function(_SendDTMFValue value)? sendDTMF,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (callStopped != null) {
      return callStopped(this);
    }
    return orElse();
  }
}

abstract class _CallStopped implements DialerEvent {
  const factory _CallStopped() = _$CallStoppedImpl;
}

/// @nodoc
abstract class _$$HangedUpImplCopyWith<$Res> {
  factory _$$HangedUpImplCopyWith(
          _$HangedUpImpl value, $Res Function(_$HangedUpImpl) then) =
      __$$HangedUpImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$HangedUpImplCopyWithImpl<$Res>
    extends _$DialerEventCopyWithImpl<$Res, _$HangedUpImpl>
    implements _$$HangedUpImplCopyWith<$Res> {
  __$$HangedUpImplCopyWithImpl(
      _$HangedUpImpl _value, $Res Function(_$HangedUpImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$HangedUpImpl with DiagnosticableTreeMixin implements _HangedUp {
  const _$HangedUpImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DialerEvent.hangedup()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'DialerEvent.hangedup'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$HangedUpImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? sipUsername, String? sipPassword)
        register,
    required TResult Function() reset,
    required TResult Function() reloadAsterisk,
    required TResult Function(String phoneNumber) callStarted,
    required TResult Function() callStopped,
    required TResult Function() hangedup,
    required TResult Function(sip_ua.CallState event) handledCallStream,
    required TResult Function(DialerStatus status) updatedDialerStatus,
    required TResult Function(sip_ua.Call? call) updatedCall,
    required TResult Function(bool isOnHold, String? onHoldOriginator)
        updatedOnHoldStatus,
    required TResult Function() forceEndCurrentCall,
    required TResult Function(sip_ua.CallStateEnum callStateEnum)
        updatedCallStateEnum,
    required TResult Function(sip_ua.RegistrationStateEnum status)
        updatedRegistrationStatus,
    required TResult Function(String? message) sendErrorMessage,
    required TResult Function() timerStarted,
    required TResult Function(int value) timerPaused,
    required TResult Function() timerStopped,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() holdCall,
    required TResult Function() unholdCall,
    required TResult Function() switchToBluetoothHeadset,
    required TResult Function() switchToWiredHeadet,
    required TResult Function() disableSpeakerPhone,
    required TResult Function() switchSpeakerPhoneOn,
    required TResult Function() muteOn,
    required TResult Function() muteOff,
    required TResult Function() handledAllHeadsetStreams,
    required TResult Function(String value) sendDTMF,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
  }) {
    return hangedup();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? sipUsername, String? sipPassword)? register,
    TResult? Function()? reset,
    TResult? Function()? reloadAsterisk,
    TResult? Function(String phoneNumber)? callStarted,
    TResult? Function()? callStopped,
    TResult? Function()? hangedup,
    TResult? Function(sip_ua.CallState event)? handledCallStream,
    TResult? Function(DialerStatus status)? updatedDialerStatus,
    TResult? Function(sip_ua.Call? call)? updatedCall,
    TResult? Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult? Function()? forceEndCurrentCall,
    TResult? Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult? Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult? Function(String? message)? sendErrorMessage,
    TResult? Function()? timerStarted,
    TResult? Function(int value)? timerPaused,
    TResult? Function()? timerStopped,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? holdCall,
    TResult? Function()? unholdCall,
    TResult? Function()? switchToBluetoothHeadset,
    TResult? Function()? switchToWiredHeadet,
    TResult? Function()? disableSpeakerPhone,
    TResult? Function()? switchSpeakerPhoneOn,
    TResult? Function()? muteOn,
    TResult? Function()? muteOff,
    TResult? Function()? handledAllHeadsetStreams,
    TResult? Function(String value)? sendDTMF,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
  }) {
    return hangedup?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? sipUsername, String? sipPassword)? register,
    TResult Function()? reset,
    TResult Function()? reloadAsterisk,
    TResult Function(String phoneNumber)? callStarted,
    TResult Function()? callStopped,
    TResult Function()? hangedup,
    TResult Function(sip_ua.CallState event)? handledCallStream,
    TResult Function(DialerStatus status)? updatedDialerStatus,
    TResult Function(sip_ua.Call? call)? updatedCall,
    TResult Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult Function()? forceEndCurrentCall,
    TResult Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult Function(String? message)? sendErrorMessage,
    TResult Function()? timerStarted,
    TResult Function(int value)? timerPaused,
    TResult Function()? timerStopped,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? holdCall,
    TResult Function()? unholdCall,
    TResult Function()? switchToBluetoothHeadset,
    TResult Function()? switchToWiredHeadet,
    TResult Function()? disableSpeakerPhone,
    TResult Function()? switchSpeakerPhoneOn,
    TResult Function()? muteOn,
    TResult Function()? muteOff,
    TResult Function()? handledAllHeadsetStreams,
    TResult Function(String value)? sendDTMF,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (hangedup != null) {
      return hangedup();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) register,
    required TResult Function(_Initial value) reset,
    required TResult Function(_RegisteredAgain value) reloadAsterisk,
    required TResult Function(_CallStarted value) callStarted,
    required TResult Function(_CallStopped value) callStopped,
    required TResult Function(_HangedUp value) hangedup,
    required TResult Function(_HandledCallStream value) handledCallStream,
    required TResult Function(_UpdatedDialerStatus value) updatedDialerStatus,
    required TResult Function(_UpdatedCall value) updatedCall,
    required TResult Function(_UpdatedOnHoldStatus value) updatedOnHoldStatus,
    required TResult Function(_ForceEndCurrentCall value) forceEndCurrentCall,
    required TResult Function(_UpdatedCallStateEnum value) updatedCallStateEnum,
    required TResult Function(_UpdatedRegistrationStatus value)
        updatedRegistrationStatus,
    required TResult Function(_HandledMessageRequest value) sendErrorMessage,
    required TResult Function(_StartedTimer value) timerStarted,
    required TResult Function(_PausedTimer value) timerPaused,
    required TResult Function(_StoppedTimer value) timerStopped,
    required TResult Function(_UpdatedTimer value) timerUpdated,
    required TResult Function(_HoldCall value) holdCall,
    required TResult Function(_UnholdCall value) unholdCall,
    required TResult Function(_SwitchedToBluetooth value)
        switchToBluetoothHeadset,
    required TResult Function(_SwitchedToWiredHeadsetz value)
        switchToWiredHeadet,
    required TResult Function(_SwitchedSpeakerOff value) disableSpeakerPhone,
    required TResult Function(_SwitchSpeakerphoneOn value) switchSpeakerPhoneOn,
    required TResult Function(_MuteOn value) muteOn,
    required TResult Function(_MuteOff value) muteOff,
    required TResult Function(_HandledAllHeadsetStreams value)
        handledAllHeadsetStreams,
    required TResult Function(_SendDTMFValue value) sendDTMF,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
  }) {
    return hangedup(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? register,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_RegisteredAgain value)? reloadAsterisk,
    TResult? Function(_CallStarted value)? callStarted,
    TResult? Function(_CallStopped value)? callStopped,
    TResult? Function(_HangedUp value)? hangedup,
    TResult? Function(_HandledCallStream value)? handledCallStream,
    TResult? Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult? Function(_UpdatedCall value)? updatedCall,
    TResult? Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult? Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult? Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult? Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult? Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult? Function(_StartedTimer value)? timerStarted,
    TResult? Function(_PausedTimer value)? timerPaused,
    TResult? Function(_StoppedTimer value)? timerStopped,
    TResult? Function(_UpdatedTimer value)? timerUpdated,
    TResult? Function(_HoldCall value)? holdCall,
    TResult? Function(_UnholdCall value)? unholdCall,
    TResult? Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult? Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult? Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult? Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult? Function(_MuteOn value)? muteOn,
    TResult? Function(_MuteOff value)? muteOff,
    TResult? Function(_HandledAllHeadsetStreams value)?
        handledAllHeadsetStreams,
    TResult? Function(_SendDTMFValue value)? sendDTMF,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
  }) {
    return hangedup?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? register,
    TResult Function(_Initial value)? reset,
    TResult Function(_RegisteredAgain value)? reloadAsterisk,
    TResult Function(_CallStarted value)? callStarted,
    TResult Function(_CallStopped value)? callStopped,
    TResult Function(_HangedUp value)? hangedup,
    TResult Function(_HandledCallStream value)? handledCallStream,
    TResult Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult Function(_UpdatedCall value)? updatedCall,
    TResult Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult Function(_StartedTimer value)? timerStarted,
    TResult Function(_PausedTimer value)? timerPaused,
    TResult Function(_StoppedTimer value)? timerStopped,
    TResult Function(_UpdatedTimer value)? timerUpdated,
    TResult Function(_HoldCall value)? holdCall,
    TResult Function(_UnholdCall value)? unholdCall,
    TResult Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult Function(_MuteOn value)? muteOn,
    TResult Function(_MuteOff value)? muteOff,
    TResult Function(_HandledAllHeadsetStreams value)? handledAllHeadsetStreams,
    TResult Function(_SendDTMFValue value)? sendDTMF,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (hangedup != null) {
      return hangedup(this);
    }
    return orElse();
  }
}

abstract class _HangedUp implements DialerEvent {
  const factory _HangedUp() = _$HangedUpImpl;
}

/// @nodoc
abstract class _$$HandledCallStreamImplCopyWith<$Res> {
  factory _$$HandledCallStreamImplCopyWith(_$HandledCallStreamImpl value,
          $Res Function(_$HandledCallStreamImpl) then) =
      __$$HandledCallStreamImplCopyWithImpl<$Res>;
  @useResult
  $Res call({sip_ua.CallState event});
}

/// @nodoc
class __$$HandledCallStreamImplCopyWithImpl<$Res>
    extends _$DialerEventCopyWithImpl<$Res, _$HandledCallStreamImpl>
    implements _$$HandledCallStreamImplCopyWith<$Res> {
  __$$HandledCallStreamImplCopyWithImpl(_$HandledCallStreamImpl _value,
      $Res Function(_$HandledCallStreamImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? event = null,
  }) {
    return _then(_$HandledCallStreamImpl(
      event: null == event
          ? _value.event
          : event // ignore: cast_nullable_to_non_nullable
              as sip_ua.CallState,
    ));
  }
}

/// @nodoc

class _$HandledCallStreamImpl
    with DiagnosticableTreeMixin
    implements _HandledCallStream {
  const _$HandledCallStreamImpl({required this.event});

  @override
  final sip_ua.CallState event;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DialerEvent.handledCallStream(event: $event)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'DialerEvent.handledCallStream'))
      ..add(DiagnosticsProperty('event', event));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HandledCallStreamImpl &&
            (identical(other.event, event) || other.event == event));
  }

  @override
  int get hashCode => Object.hash(runtimeType, event);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HandledCallStreamImplCopyWith<_$HandledCallStreamImpl> get copyWith =>
      __$$HandledCallStreamImplCopyWithImpl<_$HandledCallStreamImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? sipUsername, String? sipPassword)
        register,
    required TResult Function() reset,
    required TResult Function() reloadAsterisk,
    required TResult Function(String phoneNumber) callStarted,
    required TResult Function() callStopped,
    required TResult Function() hangedup,
    required TResult Function(sip_ua.CallState event) handledCallStream,
    required TResult Function(DialerStatus status) updatedDialerStatus,
    required TResult Function(sip_ua.Call? call) updatedCall,
    required TResult Function(bool isOnHold, String? onHoldOriginator)
        updatedOnHoldStatus,
    required TResult Function() forceEndCurrentCall,
    required TResult Function(sip_ua.CallStateEnum callStateEnum)
        updatedCallStateEnum,
    required TResult Function(sip_ua.RegistrationStateEnum status)
        updatedRegistrationStatus,
    required TResult Function(String? message) sendErrorMessage,
    required TResult Function() timerStarted,
    required TResult Function(int value) timerPaused,
    required TResult Function() timerStopped,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() holdCall,
    required TResult Function() unholdCall,
    required TResult Function() switchToBluetoothHeadset,
    required TResult Function() switchToWiredHeadet,
    required TResult Function() disableSpeakerPhone,
    required TResult Function() switchSpeakerPhoneOn,
    required TResult Function() muteOn,
    required TResult Function() muteOff,
    required TResult Function() handledAllHeadsetStreams,
    required TResult Function(String value) sendDTMF,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
  }) {
    return handledCallStream(event);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? sipUsername, String? sipPassword)? register,
    TResult? Function()? reset,
    TResult? Function()? reloadAsterisk,
    TResult? Function(String phoneNumber)? callStarted,
    TResult? Function()? callStopped,
    TResult? Function()? hangedup,
    TResult? Function(sip_ua.CallState event)? handledCallStream,
    TResult? Function(DialerStatus status)? updatedDialerStatus,
    TResult? Function(sip_ua.Call? call)? updatedCall,
    TResult? Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult? Function()? forceEndCurrentCall,
    TResult? Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult? Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult? Function(String? message)? sendErrorMessage,
    TResult? Function()? timerStarted,
    TResult? Function(int value)? timerPaused,
    TResult? Function()? timerStopped,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? holdCall,
    TResult? Function()? unholdCall,
    TResult? Function()? switchToBluetoothHeadset,
    TResult? Function()? switchToWiredHeadet,
    TResult? Function()? disableSpeakerPhone,
    TResult? Function()? switchSpeakerPhoneOn,
    TResult? Function()? muteOn,
    TResult? Function()? muteOff,
    TResult? Function()? handledAllHeadsetStreams,
    TResult? Function(String value)? sendDTMF,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
  }) {
    return handledCallStream?.call(event);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? sipUsername, String? sipPassword)? register,
    TResult Function()? reset,
    TResult Function()? reloadAsterisk,
    TResult Function(String phoneNumber)? callStarted,
    TResult Function()? callStopped,
    TResult Function()? hangedup,
    TResult Function(sip_ua.CallState event)? handledCallStream,
    TResult Function(DialerStatus status)? updatedDialerStatus,
    TResult Function(sip_ua.Call? call)? updatedCall,
    TResult Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult Function()? forceEndCurrentCall,
    TResult Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult Function(String? message)? sendErrorMessage,
    TResult Function()? timerStarted,
    TResult Function(int value)? timerPaused,
    TResult Function()? timerStopped,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? holdCall,
    TResult Function()? unholdCall,
    TResult Function()? switchToBluetoothHeadset,
    TResult Function()? switchToWiredHeadet,
    TResult Function()? disableSpeakerPhone,
    TResult Function()? switchSpeakerPhoneOn,
    TResult Function()? muteOn,
    TResult Function()? muteOff,
    TResult Function()? handledAllHeadsetStreams,
    TResult Function(String value)? sendDTMF,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (handledCallStream != null) {
      return handledCallStream(event);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) register,
    required TResult Function(_Initial value) reset,
    required TResult Function(_RegisteredAgain value) reloadAsterisk,
    required TResult Function(_CallStarted value) callStarted,
    required TResult Function(_CallStopped value) callStopped,
    required TResult Function(_HangedUp value) hangedup,
    required TResult Function(_HandledCallStream value) handledCallStream,
    required TResult Function(_UpdatedDialerStatus value) updatedDialerStatus,
    required TResult Function(_UpdatedCall value) updatedCall,
    required TResult Function(_UpdatedOnHoldStatus value) updatedOnHoldStatus,
    required TResult Function(_ForceEndCurrentCall value) forceEndCurrentCall,
    required TResult Function(_UpdatedCallStateEnum value) updatedCallStateEnum,
    required TResult Function(_UpdatedRegistrationStatus value)
        updatedRegistrationStatus,
    required TResult Function(_HandledMessageRequest value) sendErrorMessage,
    required TResult Function(_StartedTimer value) timerStarted,
    required TResult Function(_PausedTimer value) timerPaused,
    required TResult Function(_StoppedTimer value) timerStopped,
    required TResult Function(_UpdatedTimer value) timerUpdated,
    required TResult Function(_HoldCall value) holdCall,
    required TResult Function(_UnholdCall value) unholdCall,
    required TResult Function(_SwitchedToBluetooth value)
        switchToBluetoothHeadset,
    required TResult Function(_SwitchedToWiredHeadsetz value)
        switchToWiredHeadet,
    required TResult Function(_SwitchedSpeakerOff value) disableSpeakerPhone,
    required TResult Function(_SwitchSpeakerphoneOn value) switchSpeakerPhoneOn,
    required TResult Function(_MuteOn value) muteOn,
    required TResult Function(_MuteOff value) muteOff,
    required TResult Function(_HandledAllHeadsetStreams value)
        handledAllHeadsetStreams,
    required TResult Function(_SendDTMFValue value) sendDTMF,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
  }) {
    return handledCallStream(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? register,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_RegisteredAgain value)? reloadAsterisk,
    TResult? Function(_CallStarted value)? callStarted,
    TResult? Function(_CallStopped value)? callStopped,
    TResult? Function(_HangedUp value)? hangedup,
    TResult? Function(_HandledCallStream value)? handledCallStream,
    TResult? Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult? Function(_UpdatedCall value)? updatedCall,
    TResult? Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult? Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult? Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult? Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult? Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult? Function(_StartedTimer value)? timerStarted,
    TResult? Function(_PausedTimer value)? timerPaused,
    TResult? Function(_StoppedTimer value)? timerStopped,
    TResult? Function(_UpdatedTimer value)? timerUpdated,
    TResult? Function(_HoldCall value)? holdCall,
    TResult? Function(_UnholdCall value)? unholdCall,
    TResult? Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult? Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult? Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult? Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult? Function(_MuteOn value)? muteOn,
    TResult? Function(_MuteOff value)? muteOff,
    TResult? Function(_HandledAllHeadsetStreams value)?
        handledAllHeadsetStreams,
    TResult? Function(_SendDTMFValue value)? sendDTMF,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
  }) {
    return handledCallStream?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? register,
    TResult Function(_Initial value)? reset,
    TResult Function(_RegisteredAgain value)? reloadAsterisk,
    TResult Function(_CallStarted value)? callStarted,
    TResult Function(_CallStopped value)? callStopped,
    TResult Function(_HangedUp value)? hangedup,
    TResult Function(_HandledCallStream value)? handledCallStream,
    TResult Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult Function(_UpdatedCall value)? updatedCall,
    TResult Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult Function(_StartedTimer value)? timerStarted,
    TResult Function(_PausedTimer value)? timerPaused,
    TResult Function(_StoppedTimer value)? timerStopped,
    TResult Function(_UpdatedTimer value)? timerUpdated,
    TResult Function(_HoldCall value)? holdCall,
    TResult Function(_UnholdCall value)? unholdCall,
    TResult Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult Function(_MuteOn value)? muteOn,
    TResult Function(_MuteOff value)? muteOff,
    TResult Function(_HandledAllHeadsetStreams value)? handledAllHeadsetStreams,
    TResult Function(_SendDTMFValue value)? sendDTMF,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (handledCallStream != null) {
      return handledCallStream(this);
    }
    return orElse();
  }
}

abstract class _HandledCallStream implements DialerEvent {
  const factory _HandledCallStream({required final sip_ua.CallState event}) =
      _$HandledCallStreamImpl;

  sip_ua.CallState get event;

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HandledCallStreamImplCopyWith<_$HandledCallStreamImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdatedDialerStatusImplCopyWith<$Res> {
  factory _$$UpdatedDialerStatusImplCopyWith(_$UpdatedDialerStatusImpl value,
          $Res Function(_$UpdatedDialerStatusImpl) then) =
      __$$UpdatedDialerStatusImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DialerStatus status});
}

/// @nodoc
class __$$UpdatedDialerStatusImplCopyWithImpl<$Res>
    extends _$DialerEventCopyWithImpl<$Res, _$UpdatedDialerStatusImpl>
    implements _$$UpdatedDialerStatusImplCopyWith<$Res> {
  __$$UpdatedDialerStatusImplCopyWithImpl(_$UpdatedDialerStatusImpl _value,
      $Res Function(_$UpdatedDialerStatusImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
  }) {
    return _then(_$UpdatedDialerStatusImpl(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as DialerStatus,
    ));
  }
}

/// @nodoc

class _$UpdatedDialerStatusImpl
    with DiagnosticableTreeMixin
    implements _UpdatedDialerStatus {
  const _$UpdatedDialerStatusImpl({this.status = DialerStatus.unknown});

  @override
  @JsonKey()
  final DialerStatus status;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DialerEvent.updatedDialerStatus(status: $status)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'DialerEvent.updatedDialerStatus'))
      ..add(DiagnosticsProperty('status', status));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdatedDialerStatusImpl &&
            (identical(other.status, status) || other.status == status));
  }

  @override
  int get hashCode => Object.hash(runtimeType, status);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdatedDialerStatusImplCopyWith<_$UpdatedDialerStatusImpl> get copyWith =>
      __$$UpdatedDialerStatusImplCopyWithImpl<_$UpdatedDialerStatusImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? sipUsername, String? sipPassword)
        register,
    required TResult Function() reset,
    required TResult Function() reloadAsterisk,
    required TResult Function(String phoneNumber) callStarted,
    required TResult Function() callStopped,
    required TResult Function() hangedup,
    required TResult Function(sip_ua.CallState event) handledCallStream,
    required TResult Function(DialerStatus status) updatedDialerStatus,
    required TResult Function(sip_ua.Call? call) updatedCall,
    required TResult Function(bool isOnHold, String? onHoldOriginator)
        updatedOnHoldStatus,
    required TResult Function() forceEndCurrentCall,
    required TResult Function(sip_ua.CallStateEnum callStateEnum)
        updatedCallStateEnum,
    required TResult Function(sip_ua.RegistrationStateEnum status)
        updatedRegistrationStatus,
    required TResult Function(String? message) sendErrorMessage,
    required TResult Function() timerStarted,
    required TResult Function(int value) timerPaused,
    required TResult Function() timerStopped,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() holdCall,
    required TResult Function() unholdCall,
    required TResult Function() switchToBluetoothHeadset,
    required TResult Function() switchToWiredHeadet,
    required TResult Function() disableSpeakerPhone,
    required TResult Function() switchSpeakerPhoneOn,
    required TResult Function() muteOn,
    required TResult Function() muteOff,
    required TResult Function() handledAllHeadsetStreams,
    required TResult Function(String value) sendDTMF,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
  }) {
    return updatedDialerStatus(status);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? sipUsername, String? sipPassword)? register,
    TResult? Function()? reset,
    TResult? Function()? reloadAsterisk,
    TResult? Function(String phoneNumber)? callStarted,
    TResult? Function()? callStopped,
    TResult? Function()? hangedup,
    TResult? Function(sip_ua.CallState event)? handledCallStream,
    TResult? Function(DialerStatus status)? updatedDialerStatus,
    TResult? Function(sip_ua.Call? call)? updatedCall,
    TResult? Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult? Function()? forceEndCurrentCall,
    TResult? Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult? Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult? Function(String? message)? sendErrorMessage,
    TResult? Function()? timerStarted,
    TResult? Function(int value)? timerPaused,
    TResult? Function()? timerStopped,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? holdCall,
    TResult? Function()? unholdCall,
    TResult? Function()? switchToBluetoothHeadset,
    TResult? Function()? switchToWiredHeadet,
    TResult? Function()? disableSpeakerPhone,
    TResult? Function()? switchSpeakerPhoneOn,
    TResult? Function()? muteOn,
    TResult? Function()? muteOff,
    TResult? Function()? handledAllHeadsetStreams,
    TResult? Function(String value)? sendDTMF,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
  }) {
    return updatedDialerStatus?.call(status);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? sipUsername, String? sipPassword)? register,
    TResult Function()? reset,
    TResult Function()? reloadAsterisk,
    TResult Function(String phoneNumber)? callStarted,
    TResult Function()? callStopped,
    TResult Function()? hangedup,
    TResult Function(sip_ua.CallState event)? handledCallStream,
    TResult Function(DialerStatus status)? updatedDialerStatus,
    TResult Function(sip_ua.Call? call)? updatedCall,
    TResult Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult Function()? forceEndCurrentCall,
    TResult Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult Function(String? message)? sendErrorMessage,
    TResult Function()? timerStarted,
    TResult Function(int value)? timerPaused,
    TResult Function()? timerStopped,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? holdCall,
    TResult Function()? unholdCall,
    TResult Function()? switchToBluetoothHeadset,
    TResult Function()? switchToWiredHeadet,
    TResult Function()? disableSpeakerPhone,
    TResult Function()? switchSpeakerPhoneOn,
    TResult Function()? muteOn,
    TResult Function()? muteOff,
    TResult Function()? handledAllHeadsetStreams,
    TResult Function(String value)? sendDTMF,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (updatedDialerStatus != null) {
      return updatedDialerStatus(status);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) register,
    required TResult Function(_Initial value) reset,
    required TResult Function(_RegisteredAgain value) reloadAsterisk,
    required TResult Function(_CallStarted value) callStarted,
    required TResult Function(_CallStopped value) callStopped,
    required TResult Function(_HangedUp value) hangedup,
    required TResult Function(_HandledCallStream value) handledCallStream,
    required TResult Function(_UpdatedDialerStatus value) updatedDialerStatus,
    required TResult Function(_UpdatedCall value) updatedCall,
    required TResult Function(_UpdatedOnHoldStatus value) updatedOnHoldStatus,
    required TResult Function(_ForceEndCurrentCall value) forceEndCurrentCall,
    required TResult Function(_UpdatedCallStateEnum value) updatedCallStateEnum,
    required TResult Function(_UpdatedRegistrationStatus value)
        updatedRegistrationStatus,
    required TResult Function(_HandledMessageRequest value) sendErrorMessage,
    required TResult Function(_StartedTimer value) timerStarted,
    required TResult Function(_PausedTimer value) timerPaused,
    required TResult Function(_StoppedTimer value) timerStopped,
    required TResult Function(_UpdatedTimer value) timerUpdated,
    required TResult Function(_HoldCall value) holdCall,
    required TResult Function(_UnholdCall value) unholdCall,
    required TResult Function(_SwitchedToBluetooth value)
        switchToBluetoothHeadset,
    required TResult Function(_SwitchedToWiredHeadsetz value)
        switchToWiredHeadet,
    required TResult Function(_SwitchedSpeakerOff value) disableSpeakerPhone,
    required TResult Function(_SwitchSpeakerphoneOn value) switchSpeakerPhoneOn,
    required TResult Function(_MuteOn value) muteOn,
    required TResult Function(_MuteOff value) muteOff,
    required TResult Function(_HandledAllHeadsetStreams value)
        handledAllHeadsetStreams,
    required TResult Function(_SendDTMFValue value) sendDTMF,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
  }) {
    return updatedDialerStatus(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? register,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_RegisteredAgain value)? reloadAsterisk,
    TResult? Function(_CallStarted value)? callStarted,
    TResult? Function(_CallStopped value)? callStopped,
    TResult? Function(_HangedUp value)? hangedup,
    TResult? Function(_HandledCallStream value)? handledCallStream,
    TResult? Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult? Function(_UpdatedCall value)? updatedCall,
    TResult? Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult? Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult? Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult? Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult? Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult? Function(_StartedTimer value)? timerStarted,
    TResult? Function(_PausedTimer value)? timerPaused,
    TResult? Function(_StoppedTimer value)? timerStopped,
    TResult? Function(_UpdatedTimer value)? timerUpdated,
    TResult? Function(_HoldCall value)? holdCall,
    TResult? Function(_UnholdCall value)? unholdCall,
    TResult? Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult? Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult? Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult? Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult? Function(_MuteOn value)? muteOn,
    TResult? Function(_MuteOff value)? muteOff,
    TResult? Function(_HandledAllHeadsetStreams value)?
        handledAllHeadsetStreams,
    TResult? Function(_SendDTMFValue value)? sendDTMF,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
  }) {
    return updatedDialerStatus?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? register,
    TResult Function(_Initial value)? reset,
    TResult Function(_RegisteredAgain value)? reloadAsterisk,
    TResult Function(_CallStarted value)? callStarted,
    TResult Function(_CallStopped value)? callStopped,
    TResult Function(_HangedUp value)? hangedup,
    TResult Function(_HandledCallStream value)? handledCallStream,
    TResult Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult Function(_UpdatedCall value)? updatedCall,
    TResult Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult Function(_StartedTimer value)? timerStarted,
    TResult Function(_PausedTimer value)? timerPaused,
    TResult Function(_StoppedTimer value)? timerStopped,
    TResult Function(_UpdatedTimer value)? timerUpdated,
    TResult Function(_HoldCall value)? holdCall,
    TResult Function(_UnholdCall value)? unholdCall,
    TResult Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult Function(_MuteOn value)? muteOn,
    TResult Function(_MuteOff value)? muteOff,
    TResult Function(_HandledAllHeadsetStreams value)? handledAllHeadsetStreams,
    TResult Function(_SendDTMFValue value)? sendDTMF,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (updatedDialerStatus != null) {
      return updatedDialerStatus(this);
    }
    return orElse();
  }
}

abstract class _UpdatedDialerStatus implements DialerEvent {
  const factory _UpdatedDialerStatus({final DialerStatus status}) =
      _$UpdatedDialerStatusImpl;

  DialerStatus get status;

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdatedDialerStatusImplCopyWith<_$UpdatedDialerStatusImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdatedCallImplCopyWith<$Res> {
  factory _$$UpdatedCallImplCopyWith(
          _$UpdatedCallImpl value, $Res Function(_$UpdatedCallImpl) then) =
      __$$UpdatedCallImplCopyWithImpl<$Res>;
  @useResult
  $Res call({sip_ua.Call? call});
}

/// @nodoc
class __$$UpdatedCallImplCopyWithImpl<$Res>
    extends _$DialerEventCopyWithImpl<$Res, _$UpdatedCallImpl>
    implements _$$UpdatedCallImplCopyWith<$Res> {
  __$$UpdatedCallImplCopyWithImpl(
      _$UpdatedCallImpl _value, $Res Function(_$UpdatedCallImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? call = freezed,
  }) {
    return _then(_$UpdatedCallImpl(
      call: freezed == call
          ? _value.call
          : call // ignore: cast_nullable_to_non_nullable
              as sip_ua.Call?,
    ));
  }
}

/// @nodoc

class _$UpdatedCallImpl with DiagnosticableTreeMixin implements _UpdatedCall {
  const _$UpdatedCallImpl({this.call});

  @override
  final sip_ua.Call? call;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DialerEvent.updatedCall(call: $call)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'DialerEvent.updatedCall'))
      ..add(DiagnosticsProperty('call', call));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdatedCallImpl &&
            (identical(other.call, call) || other.call == call));
  }

  @override
  int get hashCode => Object.hash(runtimeType, call);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdatedCallImplCopyWith<_$UpdatedCallImpl> get copyWith =>
      __$$UpdatedCallImplCopyWithImpl<_$UpdatedCallImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? sipUsername, String? sipPassword)
        register,
    required TResult Function() reset,
    required TResult Function() reloadAsterisk,
    required TResult Function(String phoneNumber) callStarted,
    required TResult Function() callStopped,
    required TResult Function() hangedup,
    required TResult Function(sip_ua.CallState event) handledCallStream,
    required TResult Function(DialerStatus status) updatedDialerStatus,
    required TResult Function(sip_ua.Call? call) updatedCall,
    required TResult Function(bool isOnHold, String? onHoldOriginator)
        updatedOnHoldStatus,
    required TResult Function() forceEndCurrentCall,
    required TResult Function(sip_ua.CallStateEnum callStateEnum)
        updatedCallStateEnum,
    required TResult Function(sip_ua.RegistrationStateEnum status)
        updatedRegistrationStatus,
    required TResult Function(String? message) sendErrorMessage,
    required TResult Function() timerStarted,
    required TResult Function(int value) timerPaused,
    required TResult Function() timerStopped,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() holdCall,
    required TResult Function() unholdCall,
    required TResult Function() switchToBluetoothHeadset,
    required TResult Function() switchToWiredHeadet,
    required TResult Function() disableSpeakerPhone,
    required TResult Function() switchSpeakerPhoneOn,
    required TResult Function() muteOn,
    required TResult Function() muteOff,
    required TResult Function() handledAllHeadsetStreams,
    required TResult Function(String value) sendDTMF,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
  }) {
    return updatedCall(call);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? sipUsername, String? sipPassword)? register,
    TResult? Function()? reset,
    TResult? Function()? reloadAsterisk,
    TResult? Function(String phoneNumber)? callStarted,
    TResult? Function()? callStopped,
    TResult? Function()? hangedup,
    TResult? Function(sip_ua.CallState event)? handledCallStream,
    TResult? Function(DialerStatus status)? updatedDialerStatus,
    TResult? Function(sip_ua.Call? call)? updatedCall,
    TResult? Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult? Function()? forceEndCurrentCall,
    TResult? Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult? Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult? Function(String? message)? sendErrorMessage,
    TResult? Function()? timerStarted,
    TResult? Function(int value)? timerPaused,
    TResult? Function()? timerStopped,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? holdCall,
    TResult? Function()? unholdCall,
    TResult? Function()? switchToBluetoothHeadset,
    TResult? Function()? switchToWiredHeadet,
    TResult? Function()? disableSpeakerPhone,
    TResult? Function()? switchSpeakerPhoneOn,
    TResult? Function()? muteOn,
    TResult? Function()? muteOff,
    TResult? Function()? handledAllHeadsetStreams,
    TResult? Function(String value)? sendDTMF,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
  }) {
    return updatedCall?.call(call);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? sipUsername, String? sipPassword)? register,
    TResult Function()? reset,
    TResult Function()? reloadAsterisk,
    TResult Function(String phoneNumber)? callStarted,
    TResult Function()? callStopped,
    TResult Function()? hangedup,
    TResult Function(sip_ua.CallState event)? handledCallStream,
    TResult Function(DialerStatus status)? updatedDialerStatus,
    TResult Function(sip_ua.Call? call)? updatedCall,
    TResult Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult Function()? forceEndCurrentCall,
    TResult Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult Function(String? message)? sendErrorMessage,
    TResult Function()? timerStarted,
    TResult Function(int value)? timerPaused,
    TResult Function()? timerStopped,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? holdCall,
    TResult Function()? unholdCall,
    TResult Function()? switchToBluetoothHeadset,
    TResult Function()? switchToWiredHeadet,
    TResult Function()? disableSpeakerPhone,
    TResult Function()? switchSpeakerPhoneOn,
    TResult Function()? muteOn,
    TResult Function()? muteOff,
    TResult Function()? handledAllHeadsetStreams,
    TResult Function(String value)? sendDTMF,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (updatedCall != null) {
      return updatedCall(call);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) register,
    required TResult Function(_Initial value) reset,
    required TResult Function(_RegisteredAgain value) reloadAsterisk,
    required TResult Function(_CallStarted value) callStarted,
    required TResult Function(_CallStopped value) callStopped,
    required TResult Function(_HangedUp value) hangedup,
    required TResult Function(_HandledCallStream value) handledCallStream,
    required TResult Function(_UpdatedDialerStatus value) updatedDialerStatus,
    required TResult Function(_UpdatedCall value) updatedCall,
    required TResult Function(_UpdatedOnHoldStatus value) updatedOnHoldStatus,
    required TResult Function(_ForceEndCurrentCall value) forceEndCurrentCall,
    required TResult Function(_UpdatedCallStateEnum value) updatedCallStateEnum,
    required TResult Function(_UpdatedRegistrationStatus value)
        updatedRegistrationStatus,
    required TResult Function(_HandledMessageRequest value) sendErrorMessage,
    required TResult Function(_StartedTimer value) timerStarted,
    required TResult Function(_PausedTimer value) timerPaused,
    required TResult Function(_StoppedTimer value) timerStopped,
    required TResult Function(_UpdatedTimer value) timerUpdated,
    required TResult Function(_HoldCall value) holdCall,
    required TResult Function(_UnholdCall value) unholdCall,
    required TResult Function(_SwitchedToBluetooth value)
        switchToBluetoothHeadset,
    required TResult Function(_SwitchedToWiredHeadsetz value)
        switchToWiredHeadet,
    required TResult Function(_SwitchedSpeakerOff value) disableSpeakerPhone,
    required TResult Function(_SwitchSpeakerphoneOn value) switchSpeakerPhoneOn,
    required TResult Function(_MuteOn value) muteOn,
    required TResult Function(_MuteOff value) muteOff,
    required TResult Function(_HandledAllHeadsetStreams value)
        handledAllHeadsetStreams,
    required TResult Function(_SendDTMFValue value) sendDTMF,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
  }) {
    return updatedCall(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? register,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_RegisteredAgain value)? reloadAsterisk,
    TResult? Function(_CallStarted value)? callStarted,
    TResult? Function(_CallStopped value)? callStopped,
    TResult? Function(_HangedUp value)? hangedup,
    TResult? Function(_HandledCallStream value)? handledCallStream,
    TResult? Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult? Function(_UpdatedCall value)? updatedCall,
    TResult? Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult? Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult? Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult? Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult? Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult? Function(_StartedTimer value)? timerStarted,
    TResult? Function(_PausedTimer value)? timerPaused,
    TResult? Function(_StoppedTimer value)? timerStopped,
    TResult? Function(_UpdatedTimer value)? timerUpdated,
    TResult? Function(_HoldCall value)? holdCall,
    TResult? Function(_UnholdCall value)? unholdCall,
    TResult? Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult? Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult? Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult? Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult? Function(_MuteOn value)? muteOn,
    TResult? Function(_MuteOff value)? muteOff,
    TResult? Function(_HandledAllHeadsetStreams value)?
        handledAllHeadsetStreams,
    TResult? Function(_SendDTMFValue value)? sendDTMF,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
  }) {
    return updatedCall?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? register,
    TResult Function(_Initial value)? reset,
    TResult Function(_RegisteredAgain value)? reloadAsterisk,
    TResult Function(_CallStarted value)? callStarted,
    TResult Function(_CallStopped value)? callStopped,
    TResult Function(_HangedUp value)? hangedup,
    TResult Function(_HandledCallStream value)? handledCallStream,
    TResult Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult Function(_UpdatedCall value)? updatedCall,
    TResult Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult Function(_StartedTimer value)? timerStarted,
    TResult Function(_PausedTimer value)? timerPaused,
    TResult Function(_StoppedTimer value)? timerStopped,
    TResult Function(_UpdatedTimer value)? timerUpdated,
    TResult Function(_HoldCall value)? holdCall,
    TResult Function(_UnholdCall value)? unholdCall,
    TResult Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult Function(_MuteOn value)? muteOn,
    TResult Function(_MuteOff value)? muteOff,
    TResult Function(_HandledAllHeadsetStreams value)? handledAllHeadsetStreams,
    TResult Function(_SendDTMFValue value)? sendDTMF,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (updatedCall != null) {
      return updatedCall(this);
    }
    return orElse();
  }
}

abstract class _UpdatedCall implements DialerEvent {
  const factory _UpdatedCall({final sip_ua.Call? call}) = _$UpdatedCallImpl;

  sip_ua.Call? get call;

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdatedCallImplCopyWith<_$UpdatedCallImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdatedOnHoldStatusImplCopyWith<$Res> {
  factory _$$UpdatedOnHoldStatusImplCopyWith(_$UpdatedOnHoldStatusImpl value,
          $Res Function(_$UpdatedOnHoldStatusImpl) then) =
      __$$UpdatedOnHoldStatusImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool isOnHold, String? onHoldOriginator});
}

/// @nodoc
class __$$UpdatedOnHoldStatusImplCopyWithImpl<$Res>
    extends _$DialerEventCopyWithImpl<$Res, _$UpdatedOnHoldStatusImpl>
    implements _$$UpdatedOnHoldStatusImplCopyWith<$Res> {
  __$$UpdatedOnHoldStatusImplCopyWithImpl(_$UpdatedOnHoldStatusImpl _value,
      $Res Function(_$UpdatedOnHoldStatusImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isOnHold = null,
    Object? onHoldOriginator = freezed,
  }) {
    return _then(_$UpdatedOnHoldStatusImpl(
      isOnHold: null == isOnHold
          ? _value.isOnHold
          : isOnHold // ignore: cast_nullable_to_non_nullable
              as bool,
      onHoldOriginator: freezed == onHoldOriginator
          ? _value.onHoldOriginator
          : onHoldOriginator // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$UpdatedOnHoldStatusImpl
    with DiagnosticableTreeMixin
    implements _UpdatedOnHoldStatus {
  const _$UpdatedOnHoldStatusImpl(
      {required this.isOnHold, this.onHoldOriginator});

  @override
  final bool isOnHold;
  @override
  final String? onHoldOriginator;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DialerEvent.updatedOnHoldStatus(isOnHold: $isOnHold, onHoldOriginator: $onHoldOriginator)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'DialerEvent.updatedOnHoldStatus'))
      ..add(DiagnosticsProperty('isOnHold', isOnHold))
      ..add(DiagnosticsProperty('onHoldOriginator', onHoldOriginator));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdatedOnHoldStatusImpl &&
            (identical(other.isOnHold, isOnHold) ||
                other.isOnHold == isOnHold) &&
            (identical(other.onHoldOriginator, onHoldOriginator) ||
                other.onHoldOriginator == onHoldOriginator));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isOnHold, onHoldOriginator);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdatedOnHoldStatusImplCopyWith<_$UpdatedOnHoldStatusImpl> get copyWith =>
      __$$UpdatedOnHoldStatusImplCopyWithImpl<_$UpdatedOnHoldStatusImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? sipUsername, String? sipPassword)
        register,
    required TResult Function() reset,
    required TResult Function() reloadAsterisk,
    required TResult Function(String phoneNumber) callStarted,
    required TResult Function() callStopped,
    required TResult Function() hangedup,
    required TResult Function(sip_ua.CallState event) handledCallStream,
    required TResult Function(DialerStatus status) updatedDialerStatus,
    required TResult Function(sip_ua.Call? call) updatedCall,
    required TResult Function(bool isOnHold, String? onHoldOriginator)
        updatedOnHoldStatus,
    required TResult Function() forceEndCurrentCall,
    required TResult Function(sip_ua.CallStateEnum callStateEnum)
        updatedCallStateEnum,
    required TResult Function(sip_ua.RegistrationStateEnum status)
        updatedRegistrationStatus,
    required TResult Function(String? message) sendErrorMessage,
    required TResult Function() timerStarted,
    required TResult Function(int value) timerPaused,
    required TResult Function() timerStopped,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() holdCall,
    required TResult Function() unholdCall,
    required TResult Function() switchToBluetoothHeadset,
    required TResult Function() switchToWiredHeadet,
    required TResult Function() disableSpeakerPhone,
    required TResult Function() switchSpeakerPhoneOn,
    required TResult Function() muteOn,
    required TResult Function() muteOff,
    required TResult Function() handledAllHeadsetStreams,
    required TResult Function(String value) sendDTMF,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
  }) {
    return updatedOnHoldStatus(isOnHold, onHoldOriginator);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? sipUsername, String? sipPassword)? register,
    TResult? Function()? reset,
    TResult? Function()? reloadAsterisk,
    TResult? Function(String phoneNumber)? callStarted,
    TResult? Function()? callStopped,
    TResult? Function()? hangedup,
    TResult? Function(sip_ua.CallState event)? handledCallStream,
    TResult? Function(DialerStatus status)? updatedDialerStatus,
    TResult? Function(sip_ua.Call? call)? updatedCall,
    TResult? Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult? Function()? forceEndCurrentCall,
    TResult? Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult? Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult? Function(String? message)? sendErrorMessage,
    TResult? Function()? timerStarted,
    TResult? Function(int value)? timerPaused,
    TResult? Function()? timerStopped,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? holdCall,
    TResult? Function()? unholdCall,
    TResult? Function()? switchToBluetoothHeadset,
    TResult? Function()? switchToWiredHeadet,
    TResult? Function()? disableSpeakerPhone,
    TResult? Function()? switchSpeakerPhoneOn,
    TResult? Function()? muteOn,
    TResult? Function()? muteOff,
    TResult? Function()? handledAllHeadsetStreams,
    TResult? Function(String value)? sendDTMF,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
  }) {
    return updatedOnHoldStatus?.call(isOnHold, onHoldOriginator);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? sipUsername, String? sipPassword)? register,
    TResult Function()? reset,
    TResult Function()? reloadAsterisk,
    TResult Function(String phoneNumber)? callStarted,
    TResult Function()? callStopped,
    TResult Function()? hangedup,
    TResult Function(sip_ua.CallState event)? handledCallStream,
    TResult Function(DialerStatus status)? updatedDialerStatus,
    TResult Function(sip_ua.Call? call)? updatedCall,
    TResult Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult Function()? forceEndCurrentCall,
    TResult Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult Function(String? message)? sendErrorMessage,
    TResult Function()? timerStarted,
    TResult Function(int value)? timerPaused,
    TResult Function()? timerStopped,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? holdCall,
    TResult Function()? unholdCall,
    TResult Function()? switchToBluetoothHeadset,
    TResult Function()? switchToWiredHeadet,
    TResult Function()? disableSpeakerPhone,
    TResult Function()? switchSpeakerPhoneOn,
    TResult Function()? muteOn,
    TResult Function()? muteOff,
    TResult Function()? handledAllHeadsetStreams,
    TResult Function(String value)? sendDTMF,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (updatedOnHoldStatus != null) {
      return updatedOnHoldStatus(isOnHold, onHoldOriginator);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) register,
    required TResult Function(_Initial value) reset,
    required TResult Function(_RegisteredAgain value) reloadAsterisk,
    required TResult Function(_CallStarted value) callStarted,
    required TResult Function(_CallStopped value) callStopped,
    required TResult Function(_HangedUp value) hangedup,
    required TResult Function(_HandledCallStream value) handledCallStream,
    required TResult Function(_UpdatedDialerStatus value) updatedDialerStatus,
    required TResult Function(_UpdatedCall value) updatedCall,
    required TResult Function(_UpdatedOnHoldStatus value) updatedOnHoldStatus,
    required TResult Function(_ForceEndCurrentCall value) forceEndCurrentCall,
    required TResult Function(_UpdatedCallStateEnum value) updatedCallStateEnum,
    required TResult Function(_UpdatedRegistrationStatus value)
        updatedRegistrationStatus,
    required TResult Function(_HandledMessageRequest value) sendErrorMessage,
    required TResult Function(_StartedTimer value) timerStarted,
    required TResult Function(_PausedTimer value) timerPaused,
    required TResult Function(_StoppedTimer value) timerStopped,
    required TResult Function(_UpdatedTimer value) timerUpdated,
    required TResult Function(_HoldCall value) holdCall,
    required TResult Function(_UnholdCall value) unholdCall,
    required TResult Function(_SwitchedToBluetooth value)
        switchToBluetoothHeadset,
    required TResult Function(_SwitchedToWiredHeadsetz value)
        switchToWiredHeadet,
    required TResult Function(_SwitchedSpeakerOff value) disableSpeakerPhone,
    required TResult Function(_SwitchSpeakerphoneOn value) switchSpeakerPhoneOn,
    required TResult Function(_MuteOn value) muteOn,
    required TResult Function(_MuteOff value) muteOff,
    required TResult Function(_HandledAllHeadsetStreams value)
        handledAllHeadsetStreams,
    required TResult Function(_SendDTMFValue value) sendDTMF,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
  }) {
    return updatedOnHoldStatus(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? register,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_RegisteredAgain value)? reloadAsterisk,
    TResult? Function(_CallStarted value)? callStarted,
    TResult? Function(_CallStopped value)? callStopped,
    TResult? Function(_HangedUp value)? hangedup,
    TResult? Function(_HandledCallStream value)? handledCallStream,
    TResult? Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult? Function(_UpdatedCall value)? updatedCall,
    TResult? Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult? Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult? Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult? Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult? Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult? Function(_StartedTimer value)? timerStarted,
    TResult? Function(_PausedTimer value)? timerPaused,
    TResult? Function(_StoppedTimer value)? timerStopped,
    TResult? Function(_UpdatedTimer value)? timerUpdated,
    TResult? Function(_HoldCall value)? holdCall,
    TResult? Function(_UnholdCall value)? unholdCall,
    TResult? Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult? Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult? Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult? Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult? Function(_MuteOn value)? muteOn,
    TResult? Function(_MuteOff value)? muteOff,
    TResult? Function(_HandledAllHeadsetStreams value)?
        handledAllHeadsetStreams,
    TResult? Function(_SendDTMFValue value)? sendDTMF,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
  }) {
    return updatedOnHoldStatus?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? register,
    TResult Function(_Initial value)? reset,
    TResult Function(_RegisteredAgain value)? reloadAsterisk,
    TResult Function(_CallStarted value)? callStarted,
    TResult Function(_CallStopped value)? callStopped,
    TResult Function(_HangedUp value)? hangedup,
    TResult Function(_HandledCallStream value)? handledCallStream,
    TResult Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult Function(_UpdatedCall value)? updatedCall,
    TResult Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult Function(_StartedTimer value)? timerStarted,
    TResult Function(_PausedTimer value)? timerPaused,
    TResult Function(_StoppedTimer value)? timerStopped,
    TResult Function(_UpdatedTimer value)? timerUpdated,
    TResult Function(_HoldCall value)? holdCall,
    TResult Function(_UnholdCall value)? unholdCall,
    TResult Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult Function(_MuteOn value)? muteOn,
    TResult Function(_MuteOff value)? muteOff,
    TResult Function(_HandledAllHeadsetStreams value)? handledAllHeadsetStreams,
    TResult Function(_SendDTMFValue value)? sendDTMF,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (updatedOnHoldStatus != null) {
      return updatedOnHoldStatus(this);
    }
    return orElse();
  }
}

abstract class _UpdatedOnHoldStatus implements DialerEvent {
  const factory _UpdatedOnHoldStatus(
      {required final bool isOnHold,
      final String? onHoldOriginator}) = _$UpdatedOnHoldStatusImpl;

  bool get isOnHold;
  String? get onHoldOriginator;

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdatedOnHoldStatusImplCopyWith<_$UpdatedOnHoldStatusImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ForceEndCurrentCallImplCopyWith<$Res> {
  factory _$$ForceEndCurrentCallImplCopyWith(_$ForceEndCurrentCallImpl value,
          $Res Function(_$ForceEndCurrentCallImpl) then) =
      __$$ForceEndCurrentCallImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ForceEndCurrentCallImplCopyWithImpl<$Res>
    extends _$DialerEventCopyWithImpl<$Res, _$ForceEndCurrentCallImpl>
    implements _$$ForceEndCurrentCallImplCopyWith<$Res> {
  __$$ForceEndCurrentCallImplCopyWithImpl(_$ForceEndCurrentCallImpl _value,
      $Res Function(_$ForceEndCurrentCallImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ForceEndCurrentCallImpl
    with DiagnosticableTreeMixin
    implements _ForceEndCurrentCall {
  const _$ForceEndCurrentCallImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DialerEvent.forceEndCurrentCall()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'DialerEvent.forceEndCurrentCall'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ForceEndCurrentCallImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? sipUsername, String? sipPassword)
        register,
    required TResult Function() reset,
    required TResult Function() reloadAsterisk,
    required TResult Function(String phoneNumber) callStarted,
    required TResult Function() callStopped,
    required TResult Function() hangedup,
    required TResult Function(sip_ua.CallState event) handledCallStream,
    required TResult Function(DialerStatus status) updatedDialerStatus,
    required TResult Function(sip_ua.Call? call) updatedCall,
    required TResult Function(bool isOnHold, String? onHoldOriginator)
        updatedOnHoldStatus,
    required TResult Function() forceEndCurrentCall,
    required TResult Function(sip_ua.CallStateEnum callStateEnum)
        updatedCallStateEnum,
    required TResult Function(sip_ua.RegistrationStateEnum status)
        updatedRegistrationStatus,
    required TResult Function(String? message) sendErrorMessage,
    required TResult Function() timerStarted,
    required TResult Function(int value) timerPaused,
    required TResult Function() timerStopped,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() holdCall,
    required TResult Function() unholdCall,
    required TResult Function() switchToBluetoothHeadset,
    required TResult Function() switchToWiredHeadet,
    required TResult Function() disableSpeakerPhone,
    required TResult Function() switchSpeakerPhoneOn,
    required TResult Function() muteOn,
    required TResult Function() muteOff,
    required TResult Function() handledAllHeadsetStreams,
    required TResult Function(String value) sendDTMF,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
  }) {
    return forceEndCurrentCall();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? sipUsername, String? sipPassword)? register,
    TResult? Function()? reset,
    TResult? Function()? reloadAsterisk,
    TResult? Function(String phoneNumber)? callStarted,
    TResult? Function()? callStopped,
    TResult? Function()? hangedup,
    TResult? Function(sip_ua.CallState event)? handledCallStream,
    TResult? Function(DialerStatus status)? updatedDialerStatus,
    TResult? Function(sip_ua.Call? call)? updatedCall,
    TResult? Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult? Function()? forceEndCurrentCall,
    TResult? Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult? Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult? Function(String? message)? sendErrorMessage,
    TResult? Function()? timerStarted,
    TResult? Function(int value)? timerPaused,
    TResult? Function()? timerStopped,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? holdCall,
    TResult? Function()? unholdCall,
    TResult? Function()? switchToBluetoothHeadset,
    TResult? Function()? switchToWiredHeadet,
    TResult? Function()? disableSpeakerPhone,
    TResult? Function()? switchSpeakerPhoneOn,
    TResult? Function()? muteOn,
    TResult? Function()? muteOff,
    TResult? Function()? handledAllHeadsetStreams,
    TResult? Function(String value)? sendDTMF,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
  }) {
    return forceEndCurrentCall?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? sipUsername, String? sipPassword)? register,
    TResult Function()? reset,
    TResult Function()? reloadAsterisk,
    TResult Function(String phoneNumber)? callStarted,
    TResult Function()? callStopped,
    TResult Function()? hangedup,
    TResult Function(sip_ua.CallState event)? handledCallStream,
    TResult Function(DialerStatus status)? updatedDialerStatus,
    TResult Function(sip_ua.Call? call)? updatedCall,
    TResult Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult Function()? forceEndCurrentCall,
    TResult Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult Function(String? message)? sendErrorMessage,
    TResult Function()? timerStarted,
    TResult Function(int value)? timerPaused,
    TResult Function()? timerStopped,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? holdCall,
    TResult Function()? unholdCall,
    TResult Function()? switchToBluetoothHeadset,
    TResult Function()? switchToWiredHeadet,
    TResult Function()? disableSpeakerPhone,
    TResult Function()? switchSpeakerPhoneOn,
    TResult Function()? muteOn,
    TResult Function()? muteOff,
    TResult Function()? handledAllHeadsetStreams,
    TResult Function(String value)? sendDTMF,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (forceEndCurrentCall != null) {
      return forceEndCurrentCall();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) register,
    required TResult Function(_Initial value) reset,
    required TResult Function(_RegisteredAgain value) reloadAsterisk,
    required TResult Function(_CallStarted value) callStarted,
    required TResult Function(_CallStopped value) callStopped,
    required TResult Function(_HangedUp value) hangedup,
    required TResult Function(_HandledCallStream value) handledCallStream,
    required TResult Function(_UpdatedDialerStatus value) updatedDialerStatus,
    required TResult Function(_UpdatedCall value) updatedCall,
    required TResult Function(_UpdatedOnHoldStatus value) updatedOnHoldStatus,
    required TResult Function(_ForceEndCurrentCall value) forceEndCurrentCall,
    required TResult Function(_UpdatedCallStateEnum value) updatedCallStateEnum,
    required TResult Function(_UpdatedRegistrationStatus value)
        updatedRegistrationStatus,
    required TResult Function(_HandledMessageRequest value) sendErrorMessage,
    required TResult Function(_StartedTimer value) timerStarted,
    required TResult Function(_PausedTimer value) timerPaused,
    required TResult Function(_StoppedTimer value) timerStopped,
    required TResult Function(_UpdatedTimer value) timerUpdated,
    required TResult Function(_HoldCall value) holdCall,
    required TResult Function(_UnholdCall value) unholdCall,
    required TResult Function(_SwitchedToBluetooth value)
        switchToBluetoothHeadset,
    required TResult Function(_SwitchedToWiredHeadsetz value)
        switchToWiredHeadet,
    required TResult Function(_SwitchedSpeakerOff value) disableSpeakerPhone,
    required TResult Function(_SwitchSpeakerphoneOn value) switchSpeakerPhoneOn,
    required TResult Function(_MuteOn value) muteOn,
    required TResult Function(_MuteOff value) muteOff,
    required TResult Function(_HandledAllHeadsetStreams value)
        handledAllHeadsetStreams,
    required TResult Function(_SendDTMFValue value) sendDTMF,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
  }) {
    return forceEndCurrentCall(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? register,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_RegisteredAgain value)? reloadAsterisk,
    TResult? Function(_CallStarted value)? callStarted,
    TResult? Function(_CallStopped value)? callStopped,
    TResult? Function(_HangedUp value)? hangedup,
    TResult? Function(_HandledCallStream value)? handledCallStream,
    TResult? Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult? Function(_UpdatedCall value)? updatedCall,
    TResult? Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult? Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult? Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult? Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult? Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult? Function(_StartedTimer value)? timerStarted,
    TResult? Function(_PausedTimer value)? timerPaused,
    TResult? Function(_StoppedTimer value)? timerStopped,
    TResult? Function(_UpdatedTimer value)? timerUpdated,
    TResult? Function(_HoldCall value)? holdCall,
    TResult? Function(_UnholdCall value)? unholdCall,
    TResult? Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult? Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult? Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult? Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult? Function(_MuteOn value)? muteOn,
    TResult? Function(_MuteOff value)? muteOff,
    TResult? Function(_HandledAllHeadsetStreams value)?
        handledAllHeadsetStreams,
    TResult? Function(_SendDTMFValue value)? sendDTMF,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
  }) {
    return forceEndCurrentCall?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? register,
    TResult Function(_Initial value)? reset,
    TResult Function(_RegisteredAgain value)? reloadAsterisk,
    TResult Function(_CallStarted value)? callStarted,
    TResult Function(_CallStopped value)? callStopped,
    TResult Function(_HangedUp value)? hangedup,
    TResult Function(_HandledCallStream value)? handledCallStream,
    TResult Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult Function(_UpdatedCall value)? updatedCall,
    TResult Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult Function(_StartedTimer value)? timerStarted,
    TResult Function(_PausedTimer value)? timerPaused,
    TResult Function(_StoppedTimer value)? timerStopped,
    TResult Function(_UpdatedTimer value)? timerUpdated,
    TResult Function(_HoldCall value)? holdCall,
    TResult Function(_UnholdCall value)? unholdCall,
    TResult Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult Function(_MuteOn value)? muteOn,
    TResult Function(_MuteOff value)? muteOff,
    TResult Function(_HandledAllHeadsetStreams value)? handledAllHeadsetStreams,
    TResult Function(_SendDTMFValue value)? sendDTMF,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (forceEndCurrentCall != null) {
      return forceEndCurrentCall(this);
    }
    return orElse();
  }
}

abstract class _ForceEndCurrentCall implements DialerEvent {
  const factory _ForceEndCurrentCall() = _$ForceEndCurrentCallImpl;
}

/// @nodoc
abstract class _$$UpdatedCallStateEnumImplCopyWith<$Res> {
  factory _$$UpdatedCallStateEnumImplCopyWith(_$UpdatedCallStateEnumImpl value,
          $Res Function(_$UpdatedCallStateEnumImpl) then) =
      __$$UpdatedCallStateEnumImplCopyWithImpl<$Res>;
  @useResult
  $Res call({sip_ua.CallStateEnum callStateEnum});
}

/// @nodoc
class __$$UpdatedCallStateEnumImplCopyWithImpl<$Res>
    extends _$DialerEventCopyWithImpl<$Res, _$UpdatedCallStateEnumImpl>
    implements _$$UpdatedCallStateEnumImplCopyWith<$Res> {
  __$$UpdatedCallStateEnumImplCopyWithImpl(_$UpdatedCallStateEnumImpl _value,
      $Res Function(_$UpdatedCallStateEnumImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? callStateEnum = null,
  }) {
    return _then(_$UpdatedCallStateEnumImpl(
      callStateEnum: null == callStateEnum
          ? _value.callStateEnum
          : callStateEnum // ignore: cast_nullable_to_non_nullable
              as sip_ua.CallStateEnum,
    ));
  }
}

/// @nodoc

class _$UpdatedCallStateEnumImpl
    with DiagnosticableTreeMixin
    implements _UpdatedCallStateEnum {
  const _$UpdatedCallStateEnumImpl({required this.callStateEnum});

  @override
  final sip_ua.CallStateEnum callStateEnum;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DialerEvent.updatedCallStateEnum(callStateEnum: $callStateEnum)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'DialerEvent.updatedCallStateEnum'))
      ..add(DiagnosticsProperty('callStateEnum', callStateEnum));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdatedCallStateEnumImpl &&
            (identical(other.callStateEnum, callStateEnum) ||
                other.callStateEnum == callStateEnum));
  }

  @override
  int get hashCode => Object.hash(runtimeType, callStateEnum);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdatedCallStateEnumImplCopyWith<_$UpdatedCallStateEnumImpl>
      get copyWith =>
          __$$UpdatedCallStateEnumImplCopyWithImpl<_$UpdatedCallStateEnumImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? sipUsername, String? sipPassword)
        register,
    required TResult Function() reset,
    required TResult Function() reloadAsterisk,
    required TResult Function(String phoneNumber) callStarted,
    required TResult Function() callStopped,
    required TResult Function() hangedup,
    required TResult Function(sip_ua.CallState event) handledCallStream,
    required TResult Function(DialerStatus status) updatedDialerStatus,
    required TResult Function(sip_ua.Call? call) updatedCall,
    required TResult Function(bool isOnHold, String? onHoldOriginator)
        updatedOnHoldStatus,
    required TResult Function() forceEndCurrentCall,
    required TResult Function(sip_ua.CallStateEnum callStateEnum)
        updatedCallStateEnum,
    required TResult Function(sip_ua.RegistrationStateEnum status)
        updatedRegistrationStatus,
    required TResult Function(String? message) sendErrorMessage,
    required TResult Function() timerStarted,
    required TResult Function(int value) timerPaused,
    required TResult Function() timerStopped,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() holdCall,
    required TResult Function() unholdCall,
    required TResult Function() switchToBluetoothHeadset,
    required TResult Function() switchToWiredHeadet,
    required TResult Function() disableSpeakerPhone,
    required TResult Function() switchSpeakerPhoneOn,
    required TResult Function() muteOn,
    required TResult Function() muteOff,
    required TResult Function() handledAllHeadsetStreams,
    required TResult Function(String value) sendDTMF,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
  }) {
    return updatedCallStateEnum(callStateEnum);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? sipUsername, String? sipPassword)? register,
    TResult? Function()? reset,
    TResult? Function()? reloadAsterisk,
    TResult? Function(String phoneNumber)? callStarted,
    TResult? Function()? callStopped,
    TResult? Function()? hangedup,
    TResult? Function(sip_ua.CallState event)? handledCallStream,
    TResult? Function(DialerStatus status)? updatedDialerStatus,
    TResult? Function(sip_ua.Call? call)? updatedCall,
    TResult? Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult? Function()? forceEndCurrentCall,
    TResult? Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult? Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult? Function(String? message)? sendErrorMessage,
    TResult? Function()? timerStarted,
    TResult? Function(int value)? timerPaused,
    TResult? Function()? timerStopped,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? holdCall,
    TResult? Function()? unholdCall,
    TResult? Function()? switchToBluetoothHeadset,
    TResult? Function()? switchToWiredHeadet,
    TResult? Function()? disableSpeakerPhone,
    TResult? Function()? switchSpeakerPhoneOn,
    TResult? Function()? muteOn,
    TResult? Function()? muteOff,
    TResult? Function()? handledAllHeadsetStreams,
    TResult? Function(String value)? sendDTMF,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
  }) {
    return updatedCallStateEnum?.call(callStateEnum);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? sipUsername, String? sipPassword)? register,
    TResult Function()? reset,
    TResult Function()? reloadAsterisk,
    TResult Function(String phoneNumber)? callStarted,
    TResult Function()? callStopped,
    TResult Function()? hangedup,
    TResult Function(sip_ua.CallState event)? handledCallStream,
    TResult Function(DialerStatus status)? updatedDialerStatus,
    TResult Function(sip_ua.Call? call)? updatedCall,
    TResult Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult Function()? forceEndCurrentCall,
    TResult Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult Function(String? message)? sendErrorMessage,
    TResult Function()? timerStarted,
    TResult Function(int value)? timerPaused,
    TResult Function()? timerStopped,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? holdCall,
    TResult Function()? unholdCall,
    TResult Function()? switchToBluetoothHeadset,
    TResult Function()? switchToWiredHeadet,
    TResult Function()? disableSpeakerPhone,
    TResult Function()? switchSpeakerPhoneOn,
    TResult Function()? muteOn,
    TResult Function()? muteOff,
    TResult Function()? handledAllHeadsetStreams,
    TResult Function(String value)? sendDTMF,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (updatedCallStateEnum != null) {
      return updatedCallStateEnum(callStateEnum);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) register,
    required TResult Function(_Initial value) reset,
    required TResult Function(_RegisteredAgain value) reloadAsterisk,
    required TResult Function(_CallStarted value) callStarted,
    required TResult Function(_CallStopped value) callStopped,
    required TResult Function(_HangedUp value) hangedup,
    required TResult Function(_HandledCallStream value) handledCallStream,
    required TResult Function(_UpdatedDialerStatus value) updatedDialerStatus,
    required TResult Function(_UpdatedCall value) updatedCall,
    required TResult Function(_UpdatedOnHoldStatus value) updatedOnHoldStatus,
    required TResult Function(_ForceEndCurrentCall value) forceEndCurrentCall,
    required TResult Function(_UpdatedCallStateEnum value) updatedCallStateEnum,
    required TResult Function(_UpdatedRegistrationStatus value)
        updatedRegistrationStatus,
    required TResult Function(_HandledMessageRequest value) sendErrorMessage,
    required TResult Function(_StartedTimer value) timerStarted,
    required TResult Function(_PausedTimer value) timerPaused,
    required TResult Function(_StoppedTimer value) timerStopped,
    required TResult Function(_UpdatedTimer value) timerUpdated,
    required TResult Function(_HoldCall value) holdCall,
    required TResult Function(_UnholdCall value) unholdCall,
    required TResult Function(_SwitchedToBluetooth value)
        switchToBluetoothHeadset,
    required TResult Function(_SwitchedToWiredHeadsetz value)
        switchToWiredHeadet,
    required TResult Function(_SwitchedSpeakerOff value) disableSpeakerPhone,
    required TResult Function(_SwitchSpeakerphoneOn value) switchSpeakerPhoneOn,
    required TResult Function(_MuteOn value) muteOn,
    required TResult Function(_MuteOff value) muteOff,
    required TResult Function(_HandledAllHeadsetStreams value)
        handledAllHeadsetStreams,
    required TResult Function(_SendDTMFValue value) sendDTMF,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
  }) {
    return updatedCallStateEnum(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? register,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_RegisteredAgain value)? reloadAsterisk,
    TResult? Function(_CallStarted value)? callStarted,
    TResult? Function(_CallStopped value)? callStopped,
    TResult? Function(_HangedUp value)? hangedup,
    TResult? Function(_HandledCallStream value)? handledCallStream,
    TResult? Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult? Function(_UpdatedCall value)? updatedCall,
    TResult? Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult? Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult? Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult? Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult? Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult? Function(_StartedTimer value)? timerStarted,
    TResult? Function(_PausedTimer value)? timerPaused,
    TResult? Function(_StoppedTimer value)? timerStopped,
    TResult? Function(_UpdatedTimer value)? timerUpdated,
    TResult? Function(_HoldCall value)? holdCall,
    TResult? Function(_UnholdCall value)? unholdCall,
    TResult? Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult? Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult? Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult? Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult? Function(_MuteOn value)? muteOn,
    TResult? Function(_MuteOff value)? muteOff,
    TResult? Function(_HandledAllHeadsetStreams value)?
        handledAllHeadsetStreams,
    TResult? Function(_SendDTMFValue value)? sendDTMF,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
  }) {
    return updatedCallStateEnum?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? register,
    TResult Function(_Initial value)? reset,
    TResult Function(_RegisteredAgain value)? reloadAsterisk,
    TResult Function(_CallStarted value)? callStarted,
    TResult Function(_CallStopped value)? callStopped,
    TResult Function(_HangedUp value)? hangedup,
    TResult Function(_HandledCallStream value)? handledCallStream,
    TResult Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult Function(_UpdatedCall value)? updatedCall,
    TResult Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult Function(_StartedTimer value)? timerStarted,
    TResult Function(_PausedTimer value)? timerPaused,
    TResult Function(_StoppedTimer value)? timerStopped,
    TResult Function(_UpdatedTimer value)? timerUpdated,
    TResult Function(_HoldCall value)? holdCall,
    TResult Function(_UnholdCall value)? unholdCall,
    TResult Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult Function(_MuteOn value)? muteOn,
    TResult Function(_MuteOff value)? muteOff,
    TResult Function(_HandledAllHeadsetStreams value)? handledAllHeadsetStreams,
    TResult Function(_SendDTMFValue value)? sendDTMF,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (updatedCallStateEnum != null) {
      return updatedCallStateEnum(this);
    }
    return orElse();
  }
}

abstract class _UpdatedCallStateEnum implements DialerEvent {
  const factory _UpdatedCallStateEnum(
          {required final sip_ua.CallStateEnum callStateEnum}) =
      _$UpdatedCallStateEnumImpl;

  sip_ua.CallStateEnum get callStateEnum;

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdatedCallStateEnumImplCopyWith<_$UpdatedCallStateEnumImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdatedRegistrationStatusImplCopyWith<$Res> {
  factory _$$UpdatedRegistrationStatusImplCopyWith(
          _$UpdatedRegistrationStatusImpl value,
          $Res Function(_$UpdatedRegistrationStatusImpl) then) =
      __$$UpdatedRegistrationStatusImplCopyWithImpl<$Res>;
  @useResult
  $Res call({sip_ua.RegistrationStateEnum status});
}

/// @nodoc
class __$$UpdatedRegistrationStatusImplCopyWithImpl<$Res>
    extends _$DialerEventCopyWithImpl<$Res, _$UpdatedRegistrationStatusImpl>
    implements _$$UpdatedRegistrationStatusImplCopyWith<$Res> {
  __$$UpdatedRegistrationStatusImplCopyWithImpl(
      _$UpdatedRegistrationStatusImpl _value,
      $Res Function(_$UpdatedRegistrationStatusImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
  }) {
    return _then(_$UpdatedRegistrationStatusImpl(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as sip_ua.RegistrationStateEnum,
    ));
  }
}

/// @nodoc

class _$UpdatedRegistrationStatusImpl
    with DiagnosticableTreeMixin
    implements _UpdatedRegistrationStatus {
  const _$UpdatedRegistrationStatusImpl(
      {this.status = sip_ua.RegistrationStateEnum.NONE});

  @override
  @JsonKey()
  final sip_ua.RegistrationStateEnum status;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DialerEvent.updatedRegistrationStatus(status: $status)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(
          DiagnosticsProperty('type', 'DialerEvent.updatedRegistrationStatus'))
      ..add(DiagnosticsProperty('status', status));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdatedRegistrationStatusImpl &&
            (identical(other.status, status) || other.status == status));
  }

  @override
  int get hashCode => Object.hash(runtimeType, status);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdatedRegistrationStatusImplCopyWith<_$UpdatedRegistrationStatusImpl>
      get copyWith => __$$UpdatedRegistrationStatusImplCopyWithImpl<
          _$UpdatedRegistrationStatusImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? sipUsername, String? sipPassword)
        register,
    required TResult Function() reset,
    required TResult Function() reloadAsterisk,
    required TResult Function(String phoneNumber) callStarted,
    required TResult Function() callStopped,
    required TResult Function() hangedup,
    required TResult Function(sip_ua.CallState event) handledCallStream,
    required TResult Function(DialerStatus status) updatedDialerStatus,
    required TResult Function(sip_ua.Call? call) updatedCall,
    required TResult Function(bool isOnHold, String? onHoldOriginator)
        updatedOnHoldStatus,
    required TResult Function() forceEndCurrentCall,
    required TResult Function(sip_ua.CallStateEnum callStateEnum)
        updatedCallStateEnum,
    required TResult Function(sip_ua.RegistrationStateEnum status)
        updatedRegistrationStatus,
    required TResult Function(String? message) sendErrorMessage,
    required TResult Function() timerStarted,
    required TResult Function(int value) timerPaused,
    required TResult Function() timerStopped,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() holdCall,
    required TResult Function() unholdCall,
    required TResult Function() switchToBluetoothHeadset,
    required TResult Function() switchToWiredHeadet,
    required TResult Function() disableSpeakerPhone,
    required TResult Function() switchSpeakerPhoneOn,
    required TResult Function() muteOn,
    required TResult Function() muteOff,
    required TResult Function() handledAllHeadsetStreams,
    required TResult Function(String value) sendDTMF,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
  }) {
    return updatedRegistrationStatus(status);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? sipUsername, String? sipPassword)? register,
    TResult? Function()? reset,
    TResult? Function()? reloadAsterisk,
    TResult? Function(String phoneNumber)? callStarted,
    TResult? Function()? callStopped,
    TResult? Function()? hangedup,
    TResult? Function(sip_ua.CallState event)? handledCallStream,
    TResult? Function(DialerStatus status)? updatedDialerStatus,
    TResult? Function(sip_ua.Call? call)? updatedCall,
    TResult? Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult? Function()? forceEndCurrentCall,
    TResult? Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult? Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult? Function(String? message)? sendErrorMessage,
    TResult? Function()? timerStarted,
    TResult? Function(int value)? timerPaused,
    TResult? Function()? timerStopped,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? holdCall,
    TResult? Function()? unholdCall,
    TResult? Function()? switchToBluetoothHeadset,
    TResult? Function()? switchToWiredHeadet,
    TResult? Function()? disableSpeakerPhone,
    TResult? Function()? switchSpeakerPhoneOn,
    TResult? Function()? muteOn,
    TResult? Function()? muteOff,
    TResult? Function()? handledAllHeadsetStreams,
    TResult? Function(String value)? sendDTMF,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
  }) {
    return updatedRegistrationStatus?.call(status);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? sipUsername, String? sipPassword)? register,
    TResult Function()? reset,
    TResult Function()? reloadAsterisk,
    TResult Function(String phoneNumber)? callStarted,
    TResult Function()? callStopped,
    TResult Function()? hangedup,
    TResult Function(sip_ua.CallState event)? handledCallStream,
    TResult Function(DialerStatus status)? updatedDialerStatus,
    TResult Function(sip_ua.Call? call)? updatedCall,
    TResult Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult Function()? forceEndCurrentCall,
    TResult Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult Function(String? message)? sendErrorMessage,
    TResult Function()? timerStarted,
    TResult Function(int value)? timerPaused,
    TResult Function()? timerStopped,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? holdCall,
    TResult Function()? unholdCall,
    TResult Function()? switchToBluetoothHeadset,
    TResult Function()? switchToWiredHeadet,
    TResult Function()? disableSpeakerPhone,
    TResult Function()? switchSpeakerPhoneOn,
    TResult Function()? muteOn,
    TResult Function()? muteOff,
    TResult Function()? handledAllHeadsetStreams,
    TResult Function(String value)? sendDTMF,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (updatedRegistrationStatus != null) {
      return updatedRegistrationStatus(status);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) register,
    required TResult Function(_Initial value) reset,
    required TResult Function(_RegisteredAgain value) reloadAsterisk,
    required TResult Function(_CallStarted value) callStarted,
    required TResult Function(_CallStopped value) callStopped,
    required TResult Function(_HangedUp value) hangedup,
    required TResult Function(_HandledCallStream value) handledCallStream,
    required TResult Function(_UpdatedDialerStatus value) updatedDialerStatus,
    required TResult Function(_UpdatedCall value) updatedCall,
    required TResult Function(_UpdatedOnHoldStatus value) updatedOnHoldStatus,
    required TResult Function(_ForceEndCurrentCall value) forceEndCurrentCall,
    required TResult Function(_UpdatedCallStateEnum value) updatedCallStateEnum,
    required TResult Function(_UpdatedRegistrationStatus value)
        updatedRegistrationStatus,
    required TResult Function(_HandledMessageRequest value) sendErrorMessage,
    required TResult Function(_StartedTimer value) timerStarted,
    required TResult Function(_PausedTimer value) timerPaused,
    required TResult Function(_StoppedTimer value) timerStopped,
    required TResult Function(_UpdatedTimer value) timerUpdated,
    required TResult Function(_HoldCall value) holdCall,
    required TResult Function(_UnholdCall value) unholdCall,
    required TResult Function(_SwitchedToBluetooth value)
        switchToBluetoothHeadset,
    required TResult Function(_SwitchedToWiredHeadsetz value)
        switchToWiredHeadet,
    required TResult Function(_SwitchedSpeakerOff value) disableSpeakerPhone,
    required TResult Function(_SwitchSpeakerphoneOn value) switchSpeakerPhoneOn,
    required TResult Function(_MuteOn value) muteOn,
    required TResult Function(_MuteOff value) muteOff,
    required TResult Function(_HandledAllHeadsetStreams value)
        handledAllHeadsetStreams,
    required TResult Function(_SendDTMFValue value) sendDTMF,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
  }) {
    return updatedRegistrationStatus(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? register,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_RegisteredAgain value)? reloadAsterisk,
    TResult? Function(_CallStarted value)? callStarted,
    TResult? Function(_CallStopped value)? callStopped,
    TResult? Function(_HangedUp value)? hangedup,
    TResult? Function(_HandledCallStream value)? handledCallStream,
    TResult? Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult? Function(_UpdatedCall value)? updatedCall,
    TResult? Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult? Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult? Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult? Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult? Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult? Function(_StartedTimer value)? timerStarted,
    TResult? Function(_PausedTimer value)? timerPaused,
    TResult? Function(_StoppedTimer value)? timerStopped,
    TResult? Function(_UpdatedTimer value)? timerUpdated,
    TResult? Function(_HoldCall value)? holdCall,
    TResult? Function(_UnholdCall value)? unholdCall,
    TResult? Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult? Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult? Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult? Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult? Function(_MuteOn value)? muteOn,
    TResult? Function(_MuteOff value)? muteOff,
    TResult? Function(_HandledAllHeadsetStreams value)?
        handledAllHeadsetStreams,
    TResult? Function(_SendDTMFValue value)? sendDTMF,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
  }) {
    return updatedRegistrationStatus?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? register,
    TResult Function(_Initial value)? reset,
    TResult Function(_RegisteredAgain value)? reloadAsterisk,
    TResult Function(_CallStarted value)? callStarted,
    TResult Function(_CallStopped value)? callStopped,
    TResult Function(_HangedUp value)? hangedup,
    TResult Function(_HandledCallStream value)? handledCallStream,
    TResult Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult Function(_UpdatedCall value)? updatedCall,
    TResult Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult Function(_StartedTimer value)? timerStarted,
    TResult Function(_PausedTimer value)? timerPaused,
    TResult Function(_StoppedTimer value)? timerStopped,
    TResult Function(_UpdatedTimer value)? timerUpdated,
    TResult Function(_HoldCall value)? holdCall,
    TResult Function(_UnholdCall value)? unholdCall,
    TResult Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult Function(_MuteOn value)? muteOn,
    TResult Function(_MuteOff value)? muteOff,
    TResult Function(_HandledAllHeadsetStreams value)? handledAllHeadsetStreams,
    TResult Function(_SendDTMFValue value)? sendDTMF,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (updatedRegistrationStatus != null) {
      return updatedRegistrationStatus(this);
    }
    return orElse();
  }
}

abstract class _UpdatedRegistrationStatus implements DialerEvent {
  const factory _UpdatedRegistrationStatus(
          {final sip_ua.RegistrationStateEnum status}) =
      _$UpdatedRegistrationStatusImpl;

  sip_ua.RegistrationStateEnum get status;

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdatedRegistrationStatusImplCopyWith<_$UpdatedRegistrationStatusImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$HandledMessageRequestImplCopyWith<$Res> {
  factory _$$HandledMessageRequestImplCopyWith(
          _$HandledMessageRequestImpl value,
          $Res Function(_$HandledMessageRequestImpl) then) =
      __$$HandledMessageRequestImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? message});
}

/// @nodoc
class __$$HandledMessageRequestImplCopyWithImpl<$Res>
    extends _$DialerEventCopyWithImpl<$Res, _$HandledMessageRequestImpl>
    implements _$$HandledMessageRequestImplCopyWith<$Res> {
  __$$HandledMessageRequestImplCopyWithImpl(_$HandledMessageRequestImpl _value,
      $Res Function(_$HandledMessageRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = freezed,
  }) {
    return _then(_$HandledMessageRequestImpl(
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$HandledMessageRequestImpl
    with DiagnosticableTreeMixin
    implements _HandledMessageRequest {
  const _$HandledMessageRequestImpl({this.message});

  @override
  final String? message;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DialerEvent.sendErrorMessage(message: $message)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'DialerEvent.sendErrorMessage'))
      ..add(DiagnosticsProperty('message', message));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HandledMessageRequestImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HandledMessageRequestImplCopyWith<_$HandledMessageRequestImpl>
      get copyWith => __$$HandledMessageRequestImplCopyWithImpl<
          _$HandledMessageRequestImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? sipUsername, String? sipPassword)
        register,
    required TResult Function() reset,
    required TResult Function() reloadAsterisk,
    required TResult Function(String phoneNumber) callStarted,
    required TResult Function() callStopped,
    required TResult Function() hangedup,
    required TResult Function(sip_ua.CallState event) handledCallStream,
    required TResult Function(DialerStatus status) updatedDialerStatus,
    required TResult Function(sip_ua.Call? call) updatedCall,
    required TResult Function(bool isOnHold, String? onHoldOriginator)
        updatedOnHoldStatus,
    required TResult Function() forceEndCurrentCall,
    required TResult Function(sip_ua.CallStateEnum callStateEnum)
        updatedCallStateEnum,
    required TResult Function(sip_ua.RegistrationStateEnum status)
        updatedRegistrationStatus,
    required TResult Function(String? message) sendErrorMessage,
    required TResult Function() timerStarted,
    required TResult Function(int value) timerPaused,
    required TResult Function() timerStopped,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() holdCall,
    required TResult Function() unholdCall,
    required TResult Function() switchToBluetoothHeadset,
    required TResult Function() switchToWiredHeadet,
    required TResult Function() disableSpeakerPhone,
    required TResult Function() switchSpeakerPhoneOn,
    required TResult Function() muteOn,
    required TResult Function() muteOff,
    required TResult Function() handledAllHeadsetStreams,
    required TResult Function(String value) sendDTMF,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
  }) {
    return sendErrorMessage(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? sipUsername, String? sipPassword)? register,
    TResult? Function()? reset,
    TResult? Function()? reloadAsterisk,
    TResult? Function(String phoneNumber)? callStarted,
    TResult? Function()? callStopped,
    TResult? Function()? hangedup,
    TResult? Function(sip_ua.CallState event)? handledCallStream,
    TResult? Function(DialerStatus status)? updatedDialerStatus,
    TResult? Function(sip_ua.Call? call)? updatedCall,
    TResult? Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult? Function()? forceEndCurrentCall,
    TResult? Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult? Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult? Function(String? message)? sendErrorMessage,
    TResult? Function()? timerStarted,
    TResult? Function(int value)? timerPaused,
    TResult? Function()? timerStopped,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? holdCall,
    TResult? Function()? unholdCall,
    TResult? Function()? switchToBluetoothHeadset,
    TResult? Function()? switchToWiredHeadet,
    TResult? Function()? disableSpeakerPhone,
    TResult? Function()? switchSpeakerPhoneOn,
    TResult? Function()? muteOn,
    TResult? Function()? muteOff,
    TResult? Function()? handledAllHeadsetStreams,
    TResult? Function(String value)? sendDTMF,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
  }) {
    return sendErrorMessage?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? sipUsername, String? sipPassword)? register,
    TResult Function()? reset,
    TResult Function()? reloadAsterisk,
    TResult Function(String phoneNumber)? callStarted,
    TResult Function()? callStopped,
    TResult Function()? hangedup,
    TResult Function(sip_ua.CallState event)? handledCallStream,
    TResult Function(DialerStatus status)? updatedDialerStatus,
    TResult Function(sip_ua.Call? call)? updatedCall,
    TResult Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult Function()? forceEndCurrentCall,
    TResult Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult Function(String? message)? sendErrorMessage,
    TResult Function()? timerStarted,
    TResult Function(int value)? timerPaused,
    TResult Function()? timerStopped,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? holdCall,
    TResult Function()? unholdCall,
    TResult Function()? switchToBluetoothHeadset,
    TResult Function()? switchToWiredHeadet,
    TResult Function()? disableSpeakerPhone,
    TResult Function()? switchSpeakerPhoneOn,
    TResult Function()? muteOn,
    TResult Function()? muteOff,
    TResult Function()? handledAllHeadsetStreams,
    TResult Function(String value)? sendDTMF,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (sendErrorMessage != null) {
      return sendErrorMessage(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) register,
    required TResult Function(_Initial value) reset,
    required TResult Function(_RegisteredAgain value) reloadAsterisk,
    required TResult Function(_CallStarted value) callStarted,
    required TResult Function(_CallStopped value) callStopped,
    required TResult Function(_HangedUp value) hangedup,
    required TResult Function(_HandledCallStream value) handledCallStream,
    required TResult Function(_UpdatedDialerStatus value) updatedDialerStatus,
    required TResult Function(_UpdatedCall value) updatedCall,
    required TResult Function(_UpdatedOnHoldStatus value) updatedOnHoldStatus,
    required TResult Function(_ForceEndCurrentCall value) forceEndCurrentCall,
    required TResult Function(_UpdatedCallStateEnum value) updatedCallStateEnum,
    required TResult Function(_UpdatedRegistrationStatus value)
        updatedRegistrationStatus,
    required TResult Function(_HandledMessageRequest value) sendErrorMessage,
    required TResult Function(_StartedTimer value) timerStarted,
    required TResult Function(_PausedTimer value) timerPaused,
    required TResult Function(_StoppedTimer value) timerStopped,
    required TResult Function(_UpdatedTimer value) timerUpdated,
    required TResult Function(_HoldCall value) holdCall,
    required TResult Function(_UnholdCall value) unholdCall,
    required TResult Function(_SwitchedToBluetooth value)
        switchToBluetoothHeadset,
    required TResult Function(_SwitchedToWiredHeadsetz value)
        switchToWiredHeadet,
    required TResult Function(_SwitchedSpeakerOff value) disableSpeakerPhone,
    required TResult Function(_SwitchSpeakerphoneOn value) switchSpeakerPhoneOn,
    required TResult Function(_MuteOn value) muteOn,
    required TResult Function(_MuteOff value) muteOff,
    required TResult Function(_HandledAllHeadsetStreams value)
        handledAllHeadsetStreams,
    required TResult Function(_SendDTMFValue value) sendDTMF,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
  }) {
    return sendErrorMessage(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? register,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_RegisteredAgain value)? reloadAsterisk,
    TResult? Function(_CallStarted value)? callStarted,
    TResult? Function(_CallStopped value)? callStopped,
    TResult? Function(_HangedUp value)? hangedup,
    TResult? Function(_HandledCallStream value)? handledCallStream,
    TResult? Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult? Function(_UpdatedCall value)? updatedCall,
    TResult? Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult? Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult? Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult? Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult? Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult? Function(_StartedTimer value)? timerStarted,
    TResult? Function(_PausedTimer value)? timerPaused,
    TResult? Function(_StoppedTimer value)? timerStopped,
    TResult? Function(_UpdatedTimer value)? timerUpdated,
    TResult? Function(_HoldCall value)? holdCall,
    TResult? Function(_UnholdCall value)? unholdCall,
    TResult? Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult? Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult? Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult? Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult? Function(_MuteOn value)? muteOn,
    TResult? Function(_MuteOff value)? muteOff,
    TResult? Function(_HandledAllHeadsetStreams value)?
        handledAllHeadsetStreams,
    TResult? Function(_SendDTMFValue value)? sendDTMF,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
  }) {
    return sendErrorMessage?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? register,
    TResult Function(_Initial value)? reset,
    TResult Function(_RegisteredAgain value)? reloadAsterisk,
    TResult Function(_CallStarted value)? callStarted,
    TResult Function(_CallStopped value)? callStopped,
    TResult Function(_HangedUp value)? hangedup,
    TResult Function(_HandledCallStream value)? handledCallStream,
    TResult Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult Function(_UpdatedCall value)? updatedCall,
    TResult Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult Function(_StartedTimer value)? timerStarted,
    TResult Function(_PausedTimer value)? timerPaused,
    TResult Function(_StoppedTimer value)? timerStopped,
    TResult Function(_UpdatedTimer value)? timerUpdated,
    TResult Function(_HoldCall value)? holdCall,
    TResult Function(_UnholdCall value)? unholdCall,
    TResult Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult Function(_MuteOn value)? muteOn,
    TResult Function(_MuteOff value)? muteOff,
    TResult Function(_HandledAllHeadsetStreams value)? handledAllHeadsetStreams,
    TResult Function(_SendDTMFValue value)? sendDTMF,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (sendErrorMessage != null) {
      return sendErrorMessage(this);
    }
    return orElse();
  }
}

abstract class _HandledMessageRequest implements DialerEvent {
  const factory _HandledMessageRequest({final String? message}) =
      _$HandledMessageRequestImpl;

  String? get message;

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HandledMessageRequestImplCopyWith<_$HandledMessageRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$StartedTimerImplCopyWith<$Res> {
  factory _$$StartedTimerImplCopyWith(
          _$StartedTimerImpl value, $Res Function(_$StartedTimerImpl) then) =
      __$$StartedTimerImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$StartedTimerImplCopyWithImpl<$Res>
    extends _$DialerEventCopyWithImpl<$Res, _$StartedTimerImpl>
    implements _$$StartedTimerImplCopyWith<$Res> {
  __$$StartedTimerImplCopyWithImpl(
      _$StartedTimerImpl _value, $Res Function(_$StartedTimerImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$StartedTimerImpl with DiagnosticableTreeMixin implements _StartedTimer {
  const _$StartedTimerImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DialerEvent.timerStarted()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'DialerEvent.timerStarted'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$StartedTimerImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? sipUsername, String? sipPassword)
        register,
    required TResult Function() reset,
    required TResult Function() reloadAsterisk,
    required TResult Function(String phoneNumber) callStarted,
    required TResult Function() callStopped,
    required TResult Function() hangedup,
    required TResult Function(sip_ua.CallState event) handledCallStream,
    required TResult Function(DialerStatus status) updatedDialerStatus,
    required TResult Function(sip_ua.Call? call) updatedCall,
    required TResult Function(bool isOnHold, String? onHoldOriginator)
        updatedOnHoldStatus,
    required TResult Function() forceEndCurrentCall,
    required TResult Function(sip_ua.CallStateEnum callStateEnum)
        updatedCallStateEnum,
    required TResult Function(sip_ua.RegistrationStateEnum status)
        updatedRegistrationStatus,
    required TResult Function(String? message) sendErrorMessage,
    required TResult Function() timerStarted,
    required TResult Function(int value) timerPaused,
    required TResult Function() timerStopped,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() holdCall,
    required TResult Function() unholdCall,
    required TResult Function() switchToBluetoothHeadset,
    required TResult Function() switchToWiredHeadet,
    required TResult Function() disableSpeakerPhone,
    required TResult Function() switchSpeakerPhoneOn,
    required TResult Function() muteOn,
    required TResult Function() muteOff,
    required TResult Function() handledAllHeadsetStreams,
    required TResult Function(String value) sendDTMF,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
  }) {
    return timerStarted();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? sipUsername, String? sipPassword)? register,
    TResult? Function()? reset,
    TResult? Function()? reloadAsterisk,
    TResult? Function(String phoneNumber)? callStarted,
    TResult? Function()? callStopped,
    TResult? Function()? hangedup,
    TResult? Function(sip_ua.CallState event)? handledCallStream,
    TResult? Function(DialerStatus status)? updatedDialerStatus,
    TResult? Function(sip_ua.Call? call)? updatedCall,
    TResult? Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult? Function()? forceEndCurrentCall,
    TResult? Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult? Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult? Function(String? message)? sendErrorMessage,
    TResult? Function()? timerStarted,
    TResult? Function(int value)? timerPaused,
    TResult? Function()? timerStopped,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? holdCall,
    TResult? Function()? unholdCall,
    TResult? Function()? switchToBluetoothHeadset,
    TResult? Function()? switchToWiredHeadet,
    TResult? Function()? disableSpeakerPhone,
    TResult? Function()? switchSpeakerPhoneOn,
    TResult? Function()? muteOn,
    TResult? Function()? muteOff,
    TResult? Function()? handledAllHeadsetStreams,
    TResult? Function(String value)? sendDTMF,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
  }) {
    return timerStarted?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? sipUsername, String? sipPassword)? register,
    TResult Function()? reset,
    TResult Function()? reloadAsterisk,
    TResult Function(String phoneNumber)? callStarted,
    TResult Function()? callStopped,
    TResult Function()? hangedup,
    TResult Function(sip_ua.CallState event)? handledCallStream,
    TResult Function(DialerStatus status)? updatedDialerStatus,
    TResult Function(sip_ua.Call? call)? updatedCall,
    TResult Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult Function()? forceEndCurrentCall,
    TResult Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult Function(String? message)? sendErrorMessage,
    TResult Function()? timerStarted,
    TResult Function(int value)? timerPaused,
    TResult Function()? timerStopped,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? holdCall,
    TResult Function()? unholdCall,
    TResult Function()? switchToBluetoothHeadset,
    TResult Function()? switchToWiredHeadet,
    TResult Function()? disableSpeakerPhone,
    TResult Function()? switchSpeakerPhoneOn,
    TResult Function()? muteOn,
    TResult Function()? muteOff,
    TResult Function()? handledAllHeadsetStreams,
    TResult Function(String value)? sendDTMF,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (timerStarted != null) {
      return timerStarted();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) register,
    required TResult Function(_Initial value) reset,
    required TResult Function(_RegisteredAgain value) reloadAsterisk,
    required TResult Function(_CallStarted value) callStarted,
    required TResult Function(_CallStopped value) callStopped,
    required TResult Function(_HangedUp value) hangedup,
    required TResult Function(_HandledCallStream value) handledCallStream,
    required TResult Function(_UpdatedDialerStatus value) updatedDialerStatus,
    required TResult Function(_UpdatedCall value) updatedCall,
    required TResult Function(_UpdatedOnHoldStatus value) updatedOnHoldStatus,
    required TResult Function(_ForceEndCurrentCall value) forceEndCurrentCall,
    required TResult Function(_UpdatedCallStateEnum value) updatedCallStateEnum,
    required TResult Function(_UpdatedRegistrationStatus value)
        updatedRegistrationStatus,
    required TResult Function(_HandledMessageRequest value) sendErrorMessage,
    required TResult Function(_StartedTimer value) timerStarted,
    required TResult Function(_PausedTimer value) timerPaused,
    required TResult Function(_StoppedTimer value) timerStopped,
    required TResult Function(_UpdatedTimer value) timerUpdated,
    required TResult Function(_HoldCall value) holdCall,
    required TResult Function(_UnholdCall value) unholdCall,
    required TResult Function(_SwitchedToBluetooth value)
        switchToBluetoothHeadset,
    required TResult Function(_SwitchedToWiredHeadsetz value)
        switchToWiredHeadet,
    required TResult Function(_SwitchedSpeakerOff value) disableSpeakerPhone,
    required TResult Function(_SwitchSpeakerphoneOn value) switchSpeakerPhoneOn,
    required TResult Function(_MuteOn value) muteOn,
    required TResult Function(_MuteOff value) muteOff,
    required TResult Function(_HandledAllHeadsetStreams value)
        handledAllHeadsetStreams,
    required TResult Function(_SendDTMFValue value) sendDTMF,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
  }) {
    return timerStarted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? register,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_RegisteredAgain value)? reloadAsterisk,
    TResult? Function(_CallStarted value)? callStarted,
    TResult? Function(_CallStopped value)? callStopped,
    TResult? Function(_HangedUp value)? hangedup,
    TResult? Function(_HandledCallStream value)? handledCallStream,
    TResult? Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult? Function(_UpdatedCall value)? updatedCall,
    TResult? Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult? Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult? Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult? Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult? Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult? Function(_StartedTimer value)? timerStarted,
    TResult? Function(_PausedTimer value)? timerPaused,
    TResult? Function(_StoppedTimer value)? timerStopped,
    TResult? Function(_UpdatedTimer value)? timerUpdated,
    TResult? Function(_HoldCall value)? holdCall,
    TResult? Function(_UnholdCall value)? unholdCall,
    TResult? Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult? Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult? Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult? Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult? Function(_MuteOn value)? muteOn,
    TResult? Function(_MuteOff value)? muteOff,
    TResult? Function(_HandledAllHeadsetStreams value)?
        handledAllHeadsetStreams,
    TResult? Function(_SendDTMFValue value)? sendDTMF,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
  }) {
    return timerStarted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? register,
    TResult Function(_Initial value)? reset,
    TResult Function(_RegisteredAgain value)? reloadAsterisk,
    TResult Function(_CallStarted value)? callStarted,
    TResult Function(_CallStopped value)? callStopped,
    TResult Function(_HangedUp value)? hangedup,
    TResult Function(_HandledCallStream value)? handledCallStream,
    TResult Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult Function(_UpdatedCall value)? updatedCall,
    TResult Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult Function(_StartedTimer value)? timerStarted,
    TResult Function(_PausedTimer value)? timerPaused,
    TResult Function(_StoppedTimer value)? timerStopped,
    TResult Function(_UpdatedTimer value)? timerUpdated,
    TResult Function(_HoldCall value)? holdCall,
    TResult Function(_UnholdCall value)? unholdCall,
    TResult Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult Function(_MuteOn value)? muteOn,
    TResult Function(_MuteOff value)? muteOff,
    TResult Function(_HandledAllHeadsetStreams value)? handledAllHeadsetStreams,
    TResult Function(_SendDTMFValue value)? sendDTMF,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (timerStarted != null) {
      return timerStarted(this);
    }
    return orElse();
  }
}

abstract class _StartedTimer implements DialerEvent {
  const factory _StartedTimer() = _$StartedTimerImpl;
}

/// @nodoc
abstract class _$$PausedTimerImplCopyWith<$Res> {
  factory _$$PausedTimerImplCopyWith(
          _$PausedTimerImpl value, $Res Function(_$PausedTimerImpl) then) =
      __$$PausedTimerImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int value});
}

/// @nodoc
class __$$PausedTimerImplCopyWithImpl<$Res>
    extends _$DialerEventCopyWithImpl<$Res, _$PausedTimerImpl>
    implements _$$PausedTimerImplCopyWith<$Res> {
  __$$PausedTimerImplCopyWithImpl(
      _$PausedTimerImpl _value, $Res Function(_$PausedTimerImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$PausedTimerImpl(
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$PausedTimerImpl with DiagnosticableTreeMixin implements _PausedTimer {
  const _$PausedTimerImpl({required this.value});

  @override
  final int value;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DialerEvent.timerPaused(value: $value)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'DialerEvent.timerPaused'))
      ..add(DiagnosticsProperty('value', value));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PausedTimerImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PausedTimerImplCopyWith<_$PausedTimerImpl> get copyWith =>
      __$$PausedTimerImplCopyWithImpl<_$PausedTimerImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? sipUsername, String? sipPassword)
        register,
    required TResult Function() reset,
    required TResult Function() reloadAsterisk,
    required TResult Function(String phoneNumber) callStarted,
    required TResult Function() callStopped,
    required TResult Function() hangedup,
    required TResult Function(sip_ua.CallState event) handledCallStream,
    required TResult Function(DialerStatus status) updatedDialerStatus,
    required TResult Function(sip_ua.Call? call) updatedCall,
    required TResult Function(bool isOnHold, String? onHoldOriginator)
        updatedOnHoldStatus,
    required TResult Function() forceEndCurrentCall,
    required TResult Function(sip_ua.CallStateEnum callStateEnum)
        updatedCallStateEnum,
    required TResult Function(sip_ua.RegistrationStateEnum status)
        updatedRegistrationStatus,
    required TResult Function(String? message) sendErrorMessage,
    required TResult Function() timerStarted,
    required TResult Function(int value) timerPaused,
    required TResult Function() timerStopped,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() holdCall,
    required TResult Function() unholdCall,
    required TResult Function() switchToBluetoothHeadset,
    required TResult Function() switchToWiredHeadet,
    required TResult Function() disableSpeakerPhone,
    required TResult Function() switchSpeakerPhoneOn,
    required TResult Function() muteOn,
    required TResult Function() muteOff,
    required TResult Function() handledAllHeadsetStreams,
    required TResult Function(String value) sendDTMF,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
  }) {
    return timerPaused(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? sipUsername, String? sipPassword)? register,
    TResult? Function()? reset,
    TResult? Function()? reloadAsterisk,
    TResult? Function(String phoneNumber)? callStarted,
    TResult? Function()? callStopped,
    TResult? Function()? hangedup,
    TResult? Function(sip_ua.CallState event)? handledCallStream,
    TResult? Function(DialerStatus status)? updatedDialerStatus,
    TResult? Function(sip_ua.Call? call)? updatedCall,
    TResult? Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult? Function()? forceEndCurrentCall,
    TResult? Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult? Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult? Function(String? message)? sendErrorMessage,
    TResult? Function()? timerStarted,
    TResult? Function(int value)? timerPaused,
    TResult? Function()? timerStopped,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? holdCall,
    TResult? Function()? unholdCall,
    TResult? Function()? switchToBluetoothHeadset,
    TResult? Function()? switchToWiredHeadet,
    TResult? Function()? disableSpeakerPhone,
    TResult? Function()? switchSpeakerPhoneOn,
    TResult? Function()? muteOn,
    TResult? Function()? muteOff,
    TResult? Function()? handledAllHeadsetStreams,
    TResult? Function(String value)? sendDTMF,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
  }) {
    return timerPaused?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? sipUsername, String? sipPassword)? register,
    TResult Function()? reset,
    TResult Function()? reloadAsterisk,
    TResult Function(String phoneNumber)? callStarted,
    TResult Function()? callStopped,
    TResult Function()? hangedup,
    TResult Function(sip_ua.CallState event)? handledCallStream,
    TResult Function(DialerStatus status)? updatedDialerStatus,
    TResult Function(sip_ua.Call? call)? updatedCall,
    TResult Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult Function()? forceEndCurrentCall,
    TResult Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult Function(String? message)? sendErrorMessage,
    TResult Function()? timerStarted,
    TResult Function(int value)? timerPaused,
    TResult Function()? timerStopped,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? holdCall,
    TResult Function()? unholdCall,
    TResult Function()? switchToBluetoothHeadset,
    TResult Function()? switchToWiredHeadet,
    TResult Function()? disableSpeakerPhone,
    TResult Function()? switchSpeakerPhoneOn,
    TResult Function()? muteOn,
    TResult Function()? muteOff,
    TResult Function()? handledAllHeadsetStreams,
    TResult Function(String value)? sendDTMF,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (timerPaused != null) {
      return timerPaused(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) register,
    required TResult Function(_Initial value) reset,
    required TResult Function(_RegisteredAgain value) reloadAsterisk,
    required TResult Function(_CallStarted value) callStarted,
    required TResult Function(_CallStopped value) callStopped,
    required TResult Function(_HangedUp value) hangedup,
    required TResult Function(_HandledCallStream value) handledCallStream,
    required TResult Function(_UpdatedDialerStatus value) updatedDialerStatus,
    required TResult Function(_UpdatedCall value) updatedCall,
    required TResult Function(_UpdatedOnHoldStatus value) updatedOnHoldStatus,
    required TResult Function(_ForceEndCurrentCall value) forceEndCurrentCall,
    required TResult Function(_UpdatedCallStateEnum value) updatedCallStateEnum,
    required TResult Function(_UpdatedRegistrationStatus value)
        updatedRegistrationStatus,
    required TResult Function(_HandledMessageRequest value) sendErrorMessage,
    required TResult Function(_StartedTimer value) timerStarted,
    required TResult Function(_PausedTimer value) timerPaused,
    required TResult Function(_StoppedTimer value) timerStopped,
    required TResult Function(_UpdatedTimer value) timerUpdated,
    required TResult Function(_HoldCall value) holdCall,
    required TResult Function(_UnholdCall value) unholdCall,
    required TResult Function(_SwitchedToBluetooth value)
        switchToBluetoothHeadset,
    required TResult Function(_SwitchedToWiredHeadsetz value)
        switchToWiredHeadet,
    required TResult Function(_SwitchedSpeakerOff value) disableSpeakerPhone,
    required TResult Function(_SwitchSpeakerphoneOn value) switchSpeakerPhoneOn,
    required TResult Function(_MuteOn value) muteOn,
    required TResult Function(_MuteOff value) muteOff,
    required TResult Function(_HandledAllHeadsetStreams value)
        handledAllHeadsetStreams,
    required TResult Function(_SendDTMFValue value) sendDTMF,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
  }) {
    return timerPaused(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? register,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_RegisteredAgain value)? reloadAsterisk,
    TResult? Function(_CallStarted value)? callStarted,
    TResult? Function(_CallStopped value)? callStopped,
    TResult? Function(_HangedUp value)? hangedup,
    TResult? Function(_HandledCallStream value)? handledCallStream,
    TResult? Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult? Function(_UpdatedCall value)? updatedCall,
    TResult? Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult? Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult? Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult? Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult? Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult? Function(_StartedTimer value)? timerStarted,
    TResult? Function(_PausedTimer value)? timerPaused,
    TResult? Function(_StoppedTimer value)? timerStopped,
    TResult? Function(_UpdatedTimer value)? timerUpdated,
    TResult? Function(_HoldCall value)? holdCall,
    TResult? Function(_UnholdCall value)? unholdCall,
    TResult? Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult? Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult? Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult? Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult? Function(_MuteOn value)? muteOn,
    TResult? Function(_MuteOff value)? muteOff,
    TResult? Function(_HandledAllHeadsetStreams value)?
        handledAllHeadsetStreams,
    TResult? Function(_SendDTMFValue value)? sendDTMF,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
  }) {
    return timerPaused?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? register,
    TResult Function(_Initial value)? reset,
    TResult Function(_RegisteredAgain value)? reloadAsterisk,
    TResult Function(_CallStarted value)? callStarted,
    TResult Function(_CallStopped value)? callStopped,
    TResult Function(_HangedUp value)? hangedup,
    TResult Function(_HandledCallStream value)? handledCallStream,
    TResult Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult Function(_UpdatedCall value)? updatedCall,
    TResult Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult Function(_StartedTimer value)? timerStarted,
    TResult Function(_PausedTimer value)? timerPaused,
    TResult Function(_StoppedTimer value)? timerStopped,
    TResult Function(_UpdatedTimer value)? timerUpdated,
    TResult Function(_HoldCall value)? holdCall,
    TResult Function(_UnholdCall value)? unholdCall,
    TResult Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult Function(_MuteOn value)? muteOn,
    TResult Function(_MuteOff value)? muteOff,
    TResult Function(_HandledAllHeadsetStreams value)? handledAllHeadsetStreams,
    TResult Function(_SendDTMFValue value)? sendDTMF,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (timerPaused != null) {
      return timerPaused(this);
    }
    return orElse();
  }
}

abstract class _PausedTimer implements DialerEvent {
  const factory _PausedTimer({required final int value}) = _$PausedTimerImpl;

  int get value;

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PausedTimerImplCopyWith<_$PausedTimerImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$StoppedTimerImplCopyWith<$Res> {
  factory _$$StoppedTimerImplCopyWith(
          _$StoppedTimerImpl value, $Res Function(_$StoppedTimerImpl) then) =
      __$$StoppedTimerImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$StoppedTimerImplCopyWithImpl<$Res>
    extends _$DialerEventCopyWithImpl<$Res, _$StoppedTimerImpl>
    implements _$$StoppedTimerImplCopyWith<$Res> {
  __$$StoppedTimerImplCopyWithImpl(
      _$StoppedTimerImpl _value, $Res Function(_$StoppedTimerImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$StoppedTimerImpl with DiagnosticableTreeMixin implements _StoppedTimer {
  const _$StoppedTimerImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DialerEvent.timerStopped()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'DialerEvent.timerStopped'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$StoppedTimerImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? sipUsername, String? sipPassword)
        register,
    required TResult Function() reset,
    required TResult Function() reloadAsterisk,
    required TResult Function(String phoneNumber) callStarted,
    required TResult Function() callStopped,
    required TResult Function() hangedup,
    required TResult Function(sip_ua.CallState event) handledCallStream,
    required TResult Function(DialerStatus status) updatedDialerStatus,
    required TResult Function(sip_ua.Call? call) updatedCall,
    required TResult Function(bool isOnHold, String? onHoldOriginator)
        updatedOnHoldStatus,
    required TResult Function() forceEndCurrentCall,
    required TResult Function(sip_ua.CallStateEnum callStateEnum)
        updatedCallStateEnum,
    required TResult Function(sip_ua.RegistrationStateEnum status)
        updatedRegistrationStatus,
    required TResult Function(String? message) sendErrorMessage,
    required TResult Function() timerStarted,
    required TResult Function(int value) timerPaused,
    required TResult Function() timerStopped,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() holdCall,
    required TResult Function() unholdCall,
    required TResult Function() switchToBluetoothHeadset,
    required TResult Function() switchToWiredHeadet,
    required TResult Function() disableSpeakerPhone,
    required TResult Function() switchSpeakerPhoneOn,
    required TResult Function() muteOn,
    required TResult Function() muteOff,
    required TResult Function() handledAllHeadsetStreams,
    required TResult Function(String value) sendDTMF,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
  }) {
    return timerStopped();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? sipUsername, String? sipPassword)? register,
    TResult? Function()? reset,
    TResult? Function()? reloadAsterisk,
    TResult? Function(String phoneNumber)? callStarted,
    TResult? Function()? callStopped,
    TResult? Function()? hangedup,
    TResult? Function(sip_ua.CallState event)? handledCallStream,
    TResult? Function(DialerStatus status)? updatedDialerStatus,
    TResult? Function(sip_ua.Call? call)? updatedCall,
    TResult? Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult? Function()? forceEndCurrentCall,
    TResult? Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult? Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult? Function(String? message)? sendErrorMessage,
    TResult? Function()? timerStarted,
    TResult? Function(int value)? timerPaused,
    TResult? Function()? timerStopped,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? holdCall,
    TResult? Function()? unholdCall,
    TResult? Function()? switchToBluetoothHeadset,
    TResult? Function()? switchToWiredHeadet,
    TResult? Function()? disableSpeakerPhone,
    TResult? Function()? switchSpeakerPhoneOn,
    TResult? Function()? muteOn,
    TResult? Function()? muteOff,
    TResult? Function()? handledAllHeadsetStreams,
    TResult? Function(String value)? sendDTMF,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
  }) {
    return timerStopped?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? sipUsername, String? sipPassword)? register,
    TResult Function()? reset,
    TResult Function()? reloadAsterisk,
    TResult Function(String phoneNumber)? callStarted,
    TResult Function()? callStopped,
    TResult Function()? hangedup,
    TResult Function(sip_ua.CallState event)? handledCallStream,
    TResult Function(DialerStatus status)? updatedDialerStatus,
    TResult Function(sip_ua.Call? call)? updatedCall,
    TResult Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult Function()? forceEndCurrentCall,
    TResult Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult Function(String? message)? sendErrorMessage,
    TResult Function()? timerStarted,
    TResult Function(int value)? timerPaused,
    TResult Function()? timerStopped,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? holdCall,
    TResult Function()? unholdCall,
    TResult Function()? switchToBluetoothHeadset,
    TResult Function()? switchToWiredHeadet,
    TResult Function()? disableSpeakerPhone,
    TResult Function()? switchSpeakerPhoneOn,
    TResult Function()? muteOn,
    TResult Function()? muteOff,
    TResult Function()? handledAllHeadsetStreams,
    TResult Function(String value)? sendDTMF,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (timerStopped != null) {
      return timerStopped();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) register,
    required TResult Function(_Initial value) reset,
    required TResult Function(_RegisteredAgain value) reloadAsterisk,
    required TResult Function(_CallStarted value) callStarted,
    required TResult Function(_CallStopped value) callStopped,
    required TResult Function(_HangedUp value) hangedup,
    required TResult Function(_HandledCallStream value) handledCallStream,
    required TResult Function(_UpdatedDialerStatus value) updatedDialerStatus,
    required TResult Function(_UpdatedCall value) updatedCall,
    required TResult Function(_UpdatedOnHoldStatus value) updatedOnHoldStatus,
    required TResult Function(_ForceEndCurrentCall value) forceEndCurrentCall,
    required TResult Function(_UpdatedCallStateEnum value) updatedCallStateEnum,
    required TResult Function(_UpdatedRegistrationStatus value)
        updatedRegistrationStatus,
    required TResult Function(_HandledMessageRequest value) sendErrorMessage,
    required TResult Function(_StartedTimer value) timerStarted,
    required TResult Function(_PausedTimer value) timerPaused,
    required TResult Function(_StoppedTimer value) timerStopped,
    required TResult Function(_UpdatedTimer value) timerUpdated,
    required TResult Function(_HoldCall value) holdCall,
    required TResult Function(_UnholdCall value) unholdCall,
    required TResult Function(_SwitchedToBluetooth value)
        switchToBluetoothHeadset,
    required TResult Function(_SwitchedToWiredHeadsetz value)
        switchToWiredHeadet,
    required TResult Function(_SwitchedSpeakerOff value) disableSpeakerPhone,
    required TResult Function(_SwitchSpeakerphoneOn value) switchSpeakerPhoneOn,
    required TResult Function(_MuteOn value) muteOn,
    required TResult Function(_MuteOff value) muteOff,
    required TResult Function(_HandledAllHeadsetStreams value)
        handledAllHeadsetStreams,
    required TResult Function(_SendDTMFValue value) sendDTMF,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
  }) {
    return timerStopped(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? register,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_RegisteredAgain value)? reloadAsterisk,
    TResult? Function(_CallStarted value)? callStarted,
    TResult? Function(_CallStopped value)? callStopped,
    TResult? Function(_HangedUp value)? hangedup,
    TResult? Function(_HandledCallStream value)? handledCallStream,
    TResult? Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult? Function(_UpdatedCall value)? updatedCall,
    TResult? Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult? Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult? Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult? Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult? Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult? Function(_StartedTimer value)? timerStarted,
    TResult? Function(_PausedTimer value)? timerPaused,
    TResult? Function(_StoppedTimer value)? timerStopped,
    TResult? Function(_UpdatedTimer value)? timerUpdated,
    TResult? Function(_HoldCall value)? holdCall,
    TResult? Function(_UnholdCall value)? unholdCall,
    TResult? Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult? Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult? Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult? Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult? Function(_MuteOn value)? muteOn,
    TResult? Function(_MuteOff value)? muteOff,
    TResult? Function(_HandledAllHeadsetStreams value)?
        handledAllHeadsetStreams,
    TResult? Function(_SendDTMFValue value)? sendDTMF,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
  }) {
    return timerStopped?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? register,
    TResult Function(_Initial value)? reset,
    TResult Function(_RegisteredAgain value)? reloadAsterisk,
    TResult Function(_CallStarted value)? callStarted,
    TResult Function(_CallStopped value)? callStopped,
    TResult Function(_HangedUp value)? hangedup,
    TResult Function(_HandledCallStream value)? handledCallStream,
    TResult Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult Function(_UpdatedCall value)? updatedCall,
    TResult Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult Function(_StartedTimer value)? timerStarted,
    TResult Function(_PausedTimer value)? timerPaused,
    TResult Function(_StoppedTimer value)? timerStopped,
    TResult Function(_UpdatedTimer value)? timerUpdated,
    TResult Function(_HoldCall value)? holdCall,
    TResult Function(_UnholdCall value)? unholdCall,
    TResult Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult Function(_MuteOn value)? muteOn,
    TResult Function(_MuteOff value)? muteOff,
    TResult Function(_HandledAllHeadsetStreams value)? handledAllHeadsetStreams,
    TResult Function(_SendDTMFValue value)? sendDTMF,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (timerStopped != null) {
      return timerStopped(this);
    }
    return orElse();
  }
}

abstract class _StoppedTimer implements DialerEvent {
  const factory _StoppedTimer() = _$StoppedTimerImpl;
}

/// @nodoc
abstract class _$$UpdatedTimerImplCopyWith<$Res> {
  factory _$$UpdatedTimerImplCopyWith(
          _$UpdatedTimerImpl value, $Res Function(_$UpdatedTimerImpl) then) =
      __$$UpdatedTimerImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Duration elapsed});
}

/// @nodoc
class __$$UpdatedTimerImplCopyWithImpl<$Res>
    extends _$DialerEventCopyWithImpl<$Res, _$UpdatedTimerImpl>
    implements _$$UpdatedTimerImplCopyWith<$Res> {
  __$$UpdatedTimerImplCopyWithImpl(
      _$UpdatedTimerImpl _value, $Res Function(_$UpdatedTimerImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? elapsed = null,
  }) {
    return _then(_$UpdatedTimerImpl(
      elapsed: null == elapsed
          ? _value.elapsed
          : elapsed // ignore: cast_nullable_to_non_nullable
              as Duration,
    ));
  }
}

/// @nodoc

class _$UpdatedTimerImpl with DiagnosticableTreeMixin implements _UpdatedTimer {
  const _$UpdatedTimerImpl({this.elapsed = Duration.zero});

  @override
  @JsonKey()
  final Duration elapsed;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DialerEvent.timerUpdated(elapsed: $elapsed)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'DialerEvent.timerUpdated'))
      ..add(DiagnosticsProperty('elapsed', elapsed));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdatedTimerImpl &&
            (identical(other.elapsed, elapsed) || other.elapsed == elapsed));
  }

  @override
  int get hashCode => Object.hash(runtimeType, elapsed);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdatedTimerImplCopyWith<_$UpdatedTimerImpl> get copyWith =>
      __$$UpdatedTimerImplCopyWithImpl<_$UpdatedTimerImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? sipUsername, String? sipPassword)
        register,
    required TResult Function() reset,
    required TResult Function() reloadAsterisk,
    required TResult Function(String phoneNumber) callStarted,
    required TResult Function() callStopped,
    required TResult Function() hangedup,
    required TResult Function(sip_ua.CallState event) handledCallStream,
    required TResult Function(DialerStatus status) updatedDialerStatus,
    required TResult Function(sip_ua.Call? call) updatedCall,
    required TResult Function(bool isOnHold, String? onHoldOriginator)
        updatedOnHoldStatus,
    required TResult Function() forceEndCurrentCall,
    required TResult Function(sip_ua.CallStateEnum callStateEnum)
        updatedCallStateEnum,
    required TResult Function(sip_ua.RegistrationStateEnum status)
        updatedRegistrationStatus,
    required TResult Function(String? message) sendErrorMessage,
    required TResult Function() timerStarted,
    required TResult Function(int value) timerPaused,
    required TResult Function() timerStopped,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() holdCall,
    required TResult Function() unholdCall,
    required TResult Function() switchToBluetoothHeadset,
    required TResult Function() switchToWiredHeadet,
    required TResult Function() disableSpeakerPhone,
    required TResult Function() switchSpeakerPhoneOn,
    required TResult Function() muteOn,
    required TResult Function() muteOff,
    required TResult Function() handledAllHeadsetStreams,
    required TResult Function(String value) sendDTMF,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
  }) {
    return timerUpdated(elapsed);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? sipUsername, String? sipPassword)? register,
    TResult? Function()? reset,
    TResult? Function()? reloadAsterisk,
    TResult? Function(String phoneNumber)? callStarted,
    TResult? Function()? callStopped,
    TResult? Function()? hangedup,
    TResult? Function(sip_ua.CallState event)? handledCallStream,
    TResult? Function(DialerStatus status)? updatedDialerStatus,
    TResult? Function(sip_ua.Call? call)? updatedCall,
    TResult? Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult? Function()? forceEndCurrentCall,
    TResult? Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult? Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult? Function(String? message)? sendErrorMessage,
    TResult? Function()? timerStarted,
    TResult? Function(int value)? timerPaused,
    TResult? Function()? timerStopped,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? holdCall,
    TResult? Function()? unholdCall,
    TResult? Function()? switchToBluetoothHeadset,
    TResult? Function()? switchToWiredHeadet,
    TResult? Function()? disableSpeakerPhone,
    TResult? Function()? switchSpeakerPhoneOn,
    TResult? Function()? muteOn,
    TResult? Function()? muteOff,
    TResult? Function()? handledAllHeadsetStreams,
    TResult? Function(String value)? sendDTMF,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
  }) {
    return timerUpdated?.call(elapsed);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? sipUsername, String? sipPassword)? register,
    TResult Function()? reset,
    TResult Function()? reloadAsterisk,
    TResult Function(String phoneNumber)? callStarted,
    TResult Function()? callStopped,
    TResult Function()? hangedup,
    TResult Function(sip_ua.CallState event)? handledCallStream,
    TResult Function(DialerStatus status)? updatedDialerStatus,
    TResult Function(sip_ua.Call? call)? updatedCall,
    TResult Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult Function()? forceEndCurrentCall,
    TResult Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult Function(String? message)? sendErrorMessage,
    TResult Function()? timerStarted,
    TResult Function(int value)? timerPaused,
    TResult Function()? timerStopped,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? holdCall,
    TResult Function()? unholdCall,
    TResult Function()? switchToBluetoothHeadset,
    TResult Function()? switchToWiredHeadet,
    TResult Function()? disableSpeakerPhone,
    TResult Function()? switchSpeakerPhoneOn,
    TResult Function()? muteOn,
    TResult Function()? muteOff,
    TResult Function()? handledAllHeadsetStreams,
    TResult Function(String value)? sendDTMF,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (timerUpdated != null) {
      return timerUpdated(elapsed);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) register,
    required TResult Function(_Initial value) reset,
    required TResult Function(_RegisteredAgain value) reloadAsterisk,
    required TResult Function(_CallStarted value) callStarted,
    required TResult Function(_CallStopped value) callStopped,
    required TResult Function(_HangedUp value) hangedup,
    required TResult Function(_HandledCallStream value) handledCallStream,
    required TResult Function(_UpdatedDialerStatus value) updatedDialerStatus,
    required TResult Function(_UpdatedCall value) updatedCall,
    required TResult Function(_UpdatedOnHoldStatus value) updatedOnHoldStatus,
    required TResult Function(_ForceEndCurrentCall value) forceEndCurrentCall,
    required TResult Function(_UpdatedCallStateEnum value) updatedCallStateEnum,
    required TResult Function(_UpdatedRegistrationStatus value)
        updatedRegistrationStatus,
    required TResult Function(_HandledMessageRequest value) sendErrorMessage,
    required TResult Function(_StartedTimer value) timerStarted,
    required TResult Function(_PausedTimer value) timerPaused,
    required TResult Function(_StoppedTimer value) timerStopped,
    required TResult Function(_UpdatedTimer value) timerUpdated,
    required TResult Function(_HoldCall value) holdCall,
    required TResult Function(_UnholdCall value) unholdCall,
    required TResult Function(_SwitchedToBluetooth value)
        switchToBluetoothHeadset,
    required TResult Function(_SwitchedToWiredHeadsetz value)
        switchToWiredHeadet,
    required TResult Function(_SwitchedSpeakerOff value) disableSpeakerPhone,
    required TResult Function(_SwitchSpeakerphoneOn value) switchSpeakerPhoneOn,
    required TResult Function(_MuteOn value) muteOn,
    required TResult Function(_MuteOff value) muteOff,
    required TResult Function(_HandledAllHeadsetStreams value)
        handledAllHeadsetStreams,
    required TResult Function(_SendDTMFValue value) sendDTMF,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
  }) {
    return timerUpdated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? register,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_RegisteredAgain value)? reloadAsterisk,
    TResult? Function(_CallStarted value)? callStarted,
    TResult? Function(_CallStopped value)? callStopped,
    TResult? Function(_HangedUp value)? hangedup,
    TResult? Function(_HandledCallStream value)? handledCallStream,
    TResult? Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult? Function(_UpdatedCall value)? updatedCall,
    TResult? Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult? Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult? Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult? Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult? Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult? Function(_StartedTimer value)? timerStarted,
    TResult? Function(_PausedTimer value)? timerPaused,
    TResult? Function(_StoppedTimer value)? timerStopped,
    TResult? Function(_UpdatedTimer value)? timerUpdated,
    TResult? Function(_HoldCall value)? holdCall,
    TResult? Function(_UnholdCall value)? unholdCall,
    TResult? Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult? Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult? Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult? Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult? Function(_MuteOn value)? muteOn,
    TResult? Function(_MuteOff value)? muteOff,
    TResult? Function(_HandledAllHeadsetStreams value)?
        handledAllHeadsetStreams,
    TResult? Function(_SendDTMFValue value)? sendDTMF,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
  }) {
    return timerUpdated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? register,
    TResult Function(_Initial value)? reset,
    TResult Function(_RegisteredAgain value)? reloadAsterisk,
    TResult Function(_CallStarted value)? callStarted,
    TResult Function(_CallStopped value)? callStopped,
    TResult Function(_HangedUp value)? hangedup,
    TResult Function(_HandledCallStream value)? handledCallStream,
    TResult Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult Function(_UpdatedCall value)? updatedCall,
    TResult Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult Function(_StartedTimer value)? timerStarted,
    TResult Function(_PausedTimer value)? timerPaused,
    TResult Function(_StoppedTimer value)? timerStopped,
    TResult Function(_UpdatedTimer value)? timerUpdated,
    TResult Function(_HoldCall value)? holdCall,
    TResult Function(_UnholdCall value)? unholdCall,
    TResult Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult Function(_MuteOn value)? muteOn,
    TResult Function(_MuteOff value)? muteOff,
    TResult Function(_HandledAllHeadsetStreams value)? handledAllHeadsetStreams,
    TResult Function(_SendDTMFValue value)? sendDTMF,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (timerUpdated != null) {
      return timerUpdated(this);
    }
    return orElse();
  }
}

abstract class _UpdatedTimer implements DialerEvent {
  const factory _UpdatedTimer({final Duration elapsed}) = _$UpdatedTimerImpl;

  Duration get elapsed;

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdatedTimerImplCopyWith<_$UpdatedTimerImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$HoldCallImplCopyWith<$Res> {
  factory _$$HoldCallImplCopyWith(
          _$HoldCallImpl value, $Res Function(_$HoldCallImpl) then) =
      __$$HoldCallImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$HoldCallImplCopyWithImpl<$Res>
    extends _$DialerEventCopyWithImpl<$Res, _$HoldCallImpl>
    implements _$$HoldCallImplCopyWith<$Res> {
  __$$HoldCallImplCopyWithImpl(
      _$HoldCallImpl _value, $Res Function(_$HoldCallImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$HoldCallImpl with DiagnosticableTreeMixin implements _HoldCall {
  const _$HoldCallImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DialerEvent.holdCall()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'DialerEvent.holdCall'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$HoldCallImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? sipUsername, String? sipPassword)
        register,
    required TResult Function() reset,
    required TResult Function() reloadAsterisk,
    required TResult Function(String phoneNumber) callStarted,
    required TResult Function() callStopped,
    required TResult Function() hangedup,
    required TResult Function(sip_ua.CallState event) handledCallStream,
    required TResult Function(DialerStatus status) updatedDialerStatus,
    required TResult Function(sip_ua.Call? call) updatedCall,
    required TResult Function(bool isOnHold, String? onHoldOriginator)
        updatedOnHoldStatus,
    required TResult Function() forceEndCurrentCall,
    required TResult Function(sip_ua.CallStateEnum callStateEnum)
        updatedCallStateEnum,
    required TResult Function(sip_ua.RegistrationStateEnum status)
        updatedRegistrationStatus,
    required TResult Function(String? message) sendErrorMessage,
    required TResult Function() timerStarted,
    required TResult Function(int value) timerPaused,
    required TResult Function() timerStopped,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() holdCall,
    required TResult Function() unholdCall,
    required TResult Function() switchToBluetoothHeadset,
    required TResult Function() switchToWiredHeadet,
    required TResult Function() disableSpeakerPhone,
    required TResult Function() switchSpeakerPhoneOn,
    required TResult Function() muteOn,
    required TResult Function() muteOff,
    required TResult Function() handledAllHeadsetStreams,
    required TResult Function(String value) sendDTMF,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
  }) {
    return holdCall();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? sipUsername, String? sipPassword)? register,
    TResult? Function()? reset,
    TResult? Function()? reloadAsterisk,
    TResult? Function(String phoneNumber)? callStarted,
    TResult? Function()? callStopped,
    TResult? Function()? hangedup,
    TResult? Function(sip_ua.CallState event)? handledCallStream,
    TResult? Function(DialerStatus status)? updatedDialerStatus,
    TResult? Function(sip_ua.Call? call)? updatedCall,
    TResult? Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult? Function()? forceEndCurrentCall,
    TResult? Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult? Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult? Function(String? message)? sendErrorMessage,
    TResult? Function()? timerStarted,
    TResult? Function(int value)? timerPaused,
    TResult? Function()? timerStopped,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? holdCall,
    TResult? Function()? unholdCall,
    TResult? Function()? switchToBluetoothHeadset,
    TResult? Function()? switchToWiredHeadet,
    TResult? Function()? disableSpeakerPhone,
    TResult? Function()? switchSpeakerPhoneOn,
    TResult? Function()? muteOn,
    TResult? Function()? muteOff,
    TResult? Function()? handledAllHeadsetStreams,
    TResult? Function(String value)? sendDTMF,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
  }) {
    return holdCall?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? sipUsername, String? sipPassword)? register,
    TResult Function()? reset,
    TResult Function()? reloadAsterisk,
    TResult Function(String phoneNumber)? callStarted,
    TResult Function()? callStopped,
    TResult Function()? hangedup,
    TResult Function(sip_ua.CallState event)? handledCallStream,
    TResult Function(DialerStatus status)? updatedDialerStatus,
    TResult Function(sip_ua.Call? call)? updatedCall,
    TResult Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult Function()? forceEndCurrentCall,
    TResult Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult Function(String? message)? sendErrorMessage,
    TResult Function()? timerStarted,
    TResult Function(int value)? timerPaused,
    TResult Function()? timerStopped,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? holdCall,
    TResult Function()? unholdCall,
    TResult Function()? switchToBluetoothHeadset,
    TResult Function()? switchToWiredHeadet,
    TResult Function()? disableSpeakerPhone,
    TResult Function()? switchSpeakerPhoneOn,
    TResult Function()? muteOn,
    TResult Function()? muteOff,
    TResult Function()? handledAllHeadsetStreams,
    TResult Function(String value)? sendDTMF,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (holdCall != null) {
      return holdCall();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) register,
    required TResult Function(_Initial value) reset,
    required TResult Function(_RegisteredAgain value) reloadAsterisk,
    required TResult Function(_CallStarted value) callStarted,
    required TResult Function(_CallStopped value) callStopped,
    required TResult Function(_HangedUp value) hangedup,
    required TResult Function(_HandledCallStream value) handledCallStream,
    required TResult Function(_UpdatedDialerStatus value) updatedDialerStatus,
    required TResult Function(_UpdatedCall value) updatedCall,
    required TResult Function(_UpdatedOnHoldStatus value) updatedOnHoldStatus,
    required TResult Function(_ForceEndCurrentCall value) forceEndCurrentCall,
    required TResult Function(_UpdatedCallStateEnum value) updatedCallStateEnum,
    required TResult Function(_UpdatedRegistrationStatus value)
        updatedRegistrationStatus,
    required TResult Function(_HandledMessageRequest value) sendErrorMessage,
    required TResult Function(_StartedTimer value) timerStarted,
    required TResult Function(_PausedTimer value) timerPaused,
    required TResult Function(_StoppedTimer value) timerStopped,
    required TResult Function(_UpdatedTimer value) timerUpdated,
    required TResult Function(_HoldCall value) holdCall,
    required TResult Function(_UnholdCall value) unholdCall,
    required TResult Function(_SwitchedToBluetooth value)
        switchToBluetoothHeadset,
    required TResult Function(_SwitchedToWiredHeadsetz value)
        switchToWiredHeadet,
    required TResult Function(_SwitchedSpeakerOff value) disableSpeakerPhone,
    required TResult Function(_SwitchSpeakerphoneOn value) switchSpeakerPhoneOn,
    required TResult Function(_MuteOn value) muteOn,
    required TResult Function(_MuteOff value) muteOff,
    required TResult Function(_HandledAllHeadsetStreams value)
        handledAllHeadsetStreams,
    required TResult Function(_SendDTMFValue value) sendDTMF,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
  }) {
    return holdCall(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? register,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_RegisteredAgain value)? reloadAsterisk,
    TResult? Function(_CallStarted value)? callStarted,
    TResult? Function(_CallStopped value)? callStopped,
    TResult? Function(_HangedUp value)? hangedup,
    TResult? Function(_HandledCallStream value)? handledCallStream,
    TResult? Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult? Function(_UpdatedCall value)? updatedCall,
    TResult? Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult? Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult? Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult? Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult? Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult? Function(_StartedTimer value)? timerStarted,
    TResult? Function(_PausedTimer value)? timerPaused,
    TResult? Function(_StoppedTimer value)? timerStopped,
    TResult? Function(_UpdatedTimer value)? timerUpdated,
    TResult? Function(_HoldCall value)? holdCall,
    TResult? Function(_UnholdCall value)? unholdCall,
    TResult? Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult? Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult? Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult? Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult? Function(_MuteOn value)? muteOn,
    TResult? Function(_MuteOff value)? muteOff,
    TResult? Function(_HandledAllHeadsetStreams value)?
        handledAllHeadsetStreams,
    TResult? Function(_SendDTMFValue value)? sendDTMF,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
  }) {
    return holdCall?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? register,
    TResult Function(_Initial value)? reset,
    TResult Function(_RegisteredAgain value)? reloadAsterisk,
    TResult Function(_CallStarted value)? callStarted,
    TResult Function(_CallStopped value)? callStopped,
    TResult Function(_HangedUp value)? hangedup,
    TResult Function(_HandledCallStream value)? handledCallStream,
    TResult Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult Function(_UpdatedCall value)? updatedCall,
    TResult Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult Function(_StartedTimer value)? timerStarted,
    TResult Function(_PausedTimer value)? timerPaused,
    TResult Function(_StoppedTimer value)? timerStopped,
    TResult Function(_UpdatedTimer value)? timerUpdated,
    TResult Function(_HoldCall value)? holdCall,
    TResult Function(_UnholdCall value)? unholdCall,
    TResult Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult Function(_MuteOn value)? muteOn,
    TResult Function(_MuteOff value)? muteOff,
    TResult Function(_HandledAllHeadsetStreams value)? handledAllHeadsetStreams,
    TResult Function(_SendDTMFValue value)? sendDTMF,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (holdCall != null) {
      return holdCall(this);
    }
    return orElse();
  }
}

abstract class _HoldCall implements DialerEvent {
  const factory _HoldCall() = _$HoldCallImpl;
}

/// @nodoc
abstract class _$$UnholdCallImplCopyWith<$Res> {
  factory _$$UnholdCallImplCopyWith(
          _$UnholdCallImpl value, $Res Function(_$UnholdCallImpl) then) =
      __$$UnholdCallImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UnholdCallImplCopyWithImpl<$Res>
    extends _$DialerEventCopyWithImpl<$Res, _$UnholdCallImpl>
    implements _$$UnholdCallImplCopyWith<$Res> {
  __$$UnholdCallImplCopyWithImpl(
      _$UnholdCallImpl _value, $Res Function(_$UnholdCallImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$UnholdCallImpl with DiagnosticableTreeMixin implements _UnholdCall {
  const _$UnholdCallImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DialerEvent.unholdCall()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'DialerEvent.unholdCall'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$UnholdCallImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? sipUsername, String? sipPassword)
        register,
    required TResult Function() reset,
    required TResult Function() reloadAsterisk,
    required TResult Function(String phoneNumber) callStarted,
    required TResult Function() callStopped,
    required TResult Function() hangedup,
    required TResult Function(sip_ua.CallState event) handledCallStream,
    required TResult Function(DialerStatus status) updatedDialerStatus,
    required TResult Function(sip_ua.Call? call) updatedCall,
    required TResult Function(bool isOnHold, String? onHoldOriginator)
        updatedOnHoldStatus,
    required TResult Function() forceEndCurrentCall,
    required TResult Function(sip_ua.CallStateEnum callStateEnum)
        updatedCallStateEnum,
    required TResult Function(sip_ua.RegistrationStateEnum status)
        updatedRegistrationStatus,
    required TResult Function(String? message) sendErrorMessage,
    required TResult Function() timerStarted,
    required TResult Function(int value) timerPaused,
    required TResult Function() timerStopped,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() holdCall,
    required TResult Function() unholdCall,
    required TResult Function() switchToBluetoothHeadset,
    required TResult Function() switchToWiredHeadet,
    required TResult Function() disableSpeakerPhone,
    required TResult Function() switchSpeakerPhoneOn,
    required TResult Function() muteOn,
    required TResult Function() muteOff,
    required TResult Function() handledAllHeadsetStreams,
    required TResult Function(String value) sendDTMF,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
  }) {
    return unholdCall();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? sipUsername, String? sipPassword)? register,
    TResult? Function()? reset,
    TResult? Function()? reloadAsterisk,
    TResult? Function(String phoneNumber)? callStarted,
    TResult? Function()? callStopped,
    TResult? Function()? hangedup,
    TResult? Function(sip_ua.CallState event)? handledCallStream,
    TResult? Function(DialerStatus status)? updatedDialerStatus,
    TResult? Function(sip_ua.Call? call)? updatedCall,
    TResult? Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult? Function()? forceEndCurrentCall,
    TResult? Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult? Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult? Function(String? message)? sendErrorMessage,
    TResult? Function()? timerStarted,
    TResult? Function(int value)? timerPaused,
    TResult? Function()? timerStopped,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? holdCall,
    TResult? Function()? unholdCall,
    TResult? Function()? switchToBluetoothHeadset,
    TResult? Function()? switchToWiredHeadet,
    TResult? Function()? disableSpeakerPhone,
    TResult? Function()? switchSpeakerPhoneOn,
    TResult? Function()? muteOn,
    TResult? Function()? muteOff,
    TResult? Function()? handledAllHeadsetStreams,
    TResult? Function(String value)? sendDTMF,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
  }) {
    return unholdCall?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? sipUsername, String? sipPassword)? register,
    TResult Function()? reset,
    TResult Function()? reloadAsterisk,
    TResult Function(String phoneNumber)? callStarted,
    TResult Function()? callStopped,
    TResult Function()? hangedup,
    TResult Function(sip_ua.CallState event)? handledCallStream,
    TResult Function(DialerStatus status)? updatedDialerStatus,
    TResult Function(sip_ua.Call? call)? updatedCall,
    TResult Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult Function()? forceEndCurrentCall,
    TResult Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult Function(String? message)? sendErrorMessage,
    TResult Function()? timerStarted,
    TResult Function(int value)? timerPaused,
    TResult Function()? timerStopped,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? holdCall,
    TResult Function()? unholdCall,
    TResult Function()? switchToBluetoothHeadset,
    TResult Function()? switchToWiredHeadet,
    TResult Function()? disableSpeakerPhone,
    TResult Function()? switchSpeakerPhoneOn,
    TResult Function()? muteOn,
    TResult Function()? muteOff,
    TResult Function()? handledAllHeadsetStreams,
    TResult Function(String value)? sendDTMF,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (unholdCall != null) {
      return unholdCall();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) register,
    required TResult Function(_Initial value) reset,
    required TResult Function(_RegisteredAgain value) reloadAsterisk,
    required TResult Function(_CallStarted value) callStarted,
    required TResult Function(_CallStopped value) callStopped,
    required TResult Function(_HangedUp value) hangedup,
    required TResult Function(_HandledCallStream value) handledCallStream,
    required TResult Function(_UpdatedDialerStatus value) updatedDialerStatus,
    required TResult Function(_UpdatedCall value) updatedCall,
    required TResult Function(_UpdatedOnHoldStatus value) updatedOnHoldStatus,
    required TResult Function(_ForceEndCurrentCall value) forceEndCurrentCall,
    required TResult Function(_UpdatedCallStateEnum value) updatedCallStateEnum,
    required TResult Function(_UpdatedRegistrationStatus value)
        updatedRegistrationStatus,
    required TResult Function(_HandledMessageRequest value) sendErrorMessage,
    required TResult Function(_StartedTimer value) timerStarted,
    required TResult Function(_PausedTimer value) timerPaused,
    required TResult Function(_StoppedTimer value) timerStopped,
    required TResult Function(_UpdatedTimer value) timerUpdated,
    required TResult Function(_HoldCall value) holdCall,
    required TResult Function(_UnholdCall value) unholdCall,
    required TResult Function(_SwitchedToBluetooth value)
        switchToBluetoothHeadset,
    required TResult Function(_SwitchedToWiredHeadsetz value)
        switchToWiredHeadet,
    required TResult Function(_SwitchedSpeakerOff value) disableSpeakerPhone,
    required TResult Function(_SwitchSpeakerphoneOn value) switchSpeakerPhoneOn,
    required TResult Function(_MuteOn value) muteOn,
    required TResult Function(_MuteOff value) muteOff,
    required TResult Function(_HandledAllHeadsetStreams value)
        handledAllHeadsetStreams,
    required TResult Function(_SendDTMFValue value) sendDTMF,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
  }) {
    return unholdCall(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? register,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_RegisteredAgain value)? reloadAsterisk,
    TResult? Function(_CallStarted value)? callStarted,
    TResult? Function(_CallStopped value)? callStopped,
    TResult? Function(_HangedUp value)? hangedup,
    TResult? Function(_HandledCallStream value)? handledCallStream,
    TResult? Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult? Function(_UpdatedCall value)? updatedCall,
    TResult? Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult? Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult? Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult? Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult? Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult? Function(_StartedTimer value)? timerStarted,
    TResult? Function(_PausedTimer value)? timerPaused,
    TResult? Function(_StoppedTimer value)? timerStopped,
    TResult? Function(_UpdatedTimer value)? timerUpdated,
    TResult? Function(_HoldCall value)? holdCall,
    TResult? Function(_UnholdCall value)? unholdCall,
    TResult? Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult? Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult? Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult? Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult? Function(_MuteOn value)? muteOn,
    TResult? Function(_MuteOff value)? muteOff,
    TResult? Function(_HandledAllHeadsetStreams value)?
        handledAllHeadsetStreams,
    TResult? Function(_SendDTMFValue value)? sendDTMF,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
  }) {
    return unholdCall?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? register,
    TResult Function(_Initial value)? reset,
    TResult Function(_RegisteredAgain value)? reloadAsterisk,
    TResult Function(_CallStarted value)? callStarted,
    TResult Function(_CallStopped value)? callStopped,
    TResult Function(_HangedUp value)? hangedup,
    TResult Function(_HandledCallStream value)? handledCallStream,
    TResult Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult Function(_UpdatedCall value)? updatedCall,
    TResult Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult Function(_StartedTimer value)? timerStarted,
    TResult Function(_PausedTimer value)? timerPaused,
    TResult Function(_StoppedTimer value)? timerStopped,
    TResult Function(_UpdatedTimer value)? timerUpdated,
    TResult Function(_HoldCall value)? holdCall,
    TResult Function(_UnholdCall value)? unholdCall,
    TResult Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult Function(_MuteOn value)? muteOn,
    TResult Function(_MuteOff value)? muteOff,
    TResult Function(_HandledAllHeadsetStreams value)? handledAllHeadsetStreams,
    TResult Function(_SendDTMFValue value)? sendDTMF,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (unholdCall != null) {
      return unholdCall(this);
    }
    return orElse();
  }
}

abstract class _UnholdCall implements DialerEvent {
  const factory _UnholdCall() = _$UnholdCallImpl;
}

/// @nodoc
abstract class _$$SwitchedToBluetoothImplCopyWith<$Res> {
  factory _$$SwitchedToBluetoothImplCopyWith(_$SwitchedToBluetoothImpl value,
          $Res Function(_$SwitchedToBluetoothImpl) then) =
      __$$SwitchedToBluetoothImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SwitchedToBluetoothImplCopyWithImpl<$Res>
    extends _$DialerEventCopyWithImpl<$Res, _$SwitchedToBluetoothImpl>
    implements _$$SwitchedToBluetoothImplCopyWith<$Res> {
  __$$SwitchedToBluetoothImplCopyWithImpl(_$SwitchedToBluetoothImpl _value,
      $Res Function(_$SwitchedToBluetoothImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SwitchedToBluetoothImpl
    with DiagnosticableTreeMixin
    implements _SwitchedToBluetooth {
  const _$SwitchedToBluetoothImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DialerEvent.switchToBluetoothHeadset()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(
          DiagnosticsProperty('type', 'DialerEvent.switchToBluetoothHeadset'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SwitchedToBluetoothImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? sipUsername, String? sipPassword)
        register,
    required TResult Function() reset,
    required TResult Function() reloadAsterisk,
    required TResult Function(String phoneNumber) callStarted,
    required TResult Function() callStopped,
    required TResult Function() hangedup,
    required TResult Function(sip_ua.CallState event) handledCallStream,
    required TResult Function(DialerStatus status) updatedDialerStatus,
    required TResult Function(sip_ua.Call? call) updatedCall,
    required TResult Function(bool isOnHold, String? onHoldOriginator)
        updatedOnHoldStatus,
    required TResult Function() forceEndCurrentCall,
    required TResult Function(sip_ua.CallStateEnum callStateEnum)
        updatedCallStateEnum,
    required TResult Function(sip_ua.RegistrationStateEnum status)
        updatedRegistrationStatus,
    required TResult Function(String? message) sendErrorMessage,
    required TResult Function() timerStarted,
    required TResult Function(int value) timerPaused,
    required TResult Function() timerStopped,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() holdCall,
    required TResult Function() unholdCall,
    required TResult Function() switchToBluetoothHeadset,
    required TResult Function() switchToWiredHeadet,
    required TResult Function() disableSpeakerPhone,
    required TResult Function() switchSpeakerPhoneOn,
    required TResult Function() muteOn,
    required TResult Function() muteOff,
    required TResult Function() handledAllHeadsetStreams,
    required TResult Function(String value) sendDTMF,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
  }) {
    return switchToBluetoothHeadset();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? sipUsername, String? sipPassword)? register,
    TResult? Function()? reset,
    TResult? Function()? reloadAsterisk,
    TResult? Function(String phoneNumber)? callStarted,
    TResult? Function()? callStopped,
    TResult? Function()? hangedup,
    TResult? Function(sip_ua.CallState event)? handledCallStream,
    TResult? Function(DialerStatus status)? updatedDialerStatus,
    TResult? Function(sip_ua.Call? call)? updatedCall,
    TResult? Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult? Function()? forceEndCurrentCall,
    TResult? Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult? Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult? Function(String? message)? sendErrorMessage,
    TResult? Function()? timerStarted,
    TResult? Function(int value)? timerPaused,
    TResult? Function()? timerStopped,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? holdCall,
    TResult? Function()? unholdCall,
    TResult? Function()? switchToBluetoothHeadset,
    TResult? Function()? switchToWiredHeadet,
    TResult? Function()? disableSpeakerPhone,
    TResult? Function()? switchSpeakerPhoneOn,
    TResult? Function()? muteOn,
    TResult? Function()? muteOff,
    TResult? Function()? handledAllHeadsetStreams,
    TResult? Function(String value)? sendDTMF,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
  }) {
    return switchToBluetoothHeadset?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? sipUsername, String? sipPassword)? register,
    TResult Function()? reset,
    TResult Function()? reloadAsterisk,
    TResult Function(String phoneNumber)? callStarted,
    TResult Function()? callStopped,
    TResult Function()? hangedup,
    TResult Function(sip_ua.CallState event)? handledCallStream,
    TResult Function(DialerStatus status)? updatedDialerStatus,
    TResult Function(sip_ua.Call? call)? updatedCall,
    TResult Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult Function()? forceEndCurrentCall,
    TResult Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult Function(String? message)? sendErrorMessage,
    TResult Function()? timerStarted,
    TResult Function(int value)? timerPaused,
    TResult Function()? timerStopped,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? holdCall,
    TResult Function()? unholdCall,
    TResult Function()? switchToBluetoothHeadset,
    TResult Function()? switchToWiredHeadet,
    TResult Function()? disableSpeakerPhone,
    TResult Function()? switchSpeakerPhoneOn,
    TResult Function()? muteOn,
    TResult Function()? muteOff,
    TResult Function()? handledAllHeadsetStreams,
    TResult Function(String value)? sendDTMF,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (switchToBluetoothHeadset != null) {
      return switchToBluetoothHeadset();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) register,
    required TResult Function(_Initial value) reset,
    required TResult Function(_RegisteredAgain value) reloadAsterisk,
    required TResult Function(_CallStarted value) callStarted,
    required TResult Function(_CallStopped value) callStopped,
    required TResult Function(_HangedUp value) hangedup,
    required TResult Function(_HandledCallStream value) handledCallStream,
    required TResult Function(_UpdatedDialerStatus value) updatedDialerStatus,
    required TResult Function(_UpdatedCall value) updatedCall,
    required TResult Function(_UpdatedOnHoldStatus value) updatedOnHoldStatus,
    required TResult Function(_ForceEndCurrentCall value) forceEndCurrentCall,
    required TResult Function(_UpdatedCallStateEnum value) updatedCallStateEnum,
    required TResult Function(_UpdatedRegistrationStatus value)
        updatedRegistrationStatus,
    required TResult Function(_HandledMessageRequest value) sendErrorMessage,
    required TResult Function(_StartedTimer value) timerStarted,
    required TResult Function(_PausedTimer value) timerPaused,
    required TResult Function(_StoppedTimer value) timerStopped,
    required TResult Function(_UpdatedTimer value) timerUpdated,
    required TResult Function(_HoldCall value) holdCall,
    required TResult Function(_UnholdCall value) unholdCall,
    required TResult Function(_SwitchedToBluetooth value)
        switchToBluetoothHeadset,
    required TResult Function(_SwitchedToWiredHeadsetz value)
        switchToWiredHeadet,
    required TResult Function(_SwitchedSpeakerOff value) disableSpeakerPhone,
    required TResult Function(_SwitchSpeakerphoneOn value) switchSpeakerPhoneOn,
    required TResult Function(_MuteOn value) muteOn,
    required TResult Function(_MuteOff value) muteOff,
    required TResult Function(_HandledAllHeadsetStreams value)
        handledAllHeadsetStreams,
    required TResult Function(_SendDTMFValue value) sendDTMF,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
  }) {
    return switchToBluetoothHeadset(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? register,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_RegisteredAgain value)? reloadAsterisk,
    TResult? Function(_CallStarted value)? callStarted,
    TResult? Function(_CallStopped value)? callStopped,
    TResult? Function(_HangedUp value)? hangedup,
    TResult? Function(_HandledCallStream value)? handledCallStream,
    TResult? Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult? Function(_UpdatedCall value)? updatedCall,
    TResult? Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult? Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult? Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult? Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult? Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult? Function(_StartedTimer value)? timerStarted,
    TResult? Function(_PausedTimer value)? timerPaused,
    TResult? Function(_StoppedTimer value)? timerStopped,
    TResult? Function(_UpdatedTimer value)? timerUpdated,
    TResult? Function(_HoldCall value)? holdCall,
    TResult? Function(_UnholdCall value)? unholdCall,
    TResult? Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult? Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult? Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult? Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult? Function(_MuteOn value)? muteOn,
    TResult? Function(_MuteOff value)? muteOff,
    TResult? Function(_HandledAllHeadsetStreams value)?
        handledAllHeadsetStreams,
    TResult? Function(_SendDTMFValue value)? sendDTMF,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
  }) {
    return switchToBluetoothHeadset?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? register,
    TResult Function(_Initial value)? reset,
    TResult Function(_RegisteredAgain value)? reloadAsterisk,
    TResult Function(_CallStarted value)? callStarted,
    TResult Function(_CallStopped value)? callStopped,
    TResult Function(_HangedUp value)? hangedup,
    TResult Function(_HandledCallStream value)? handledCallStream,
    TResult Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult Function(_UpdatedCall value)? updatedCall,
    TResult Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult Function(_StartedTimer value)? timerStarted,
    TResult Function(_PausedTimer value)? timerPaused,
    TResult Function(_StoppedTimer value)? timerStopped,
    TResult Function(_UpdatedTimer value)? timerUpdated,
    TResult Function(_HoldCall value)? holdCall,
    TResult Function(_UnholdCall value)? unholdCall,
    TResult Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult Function(_MuteOn value)? muteOn,
    TResult Function(_MuteOff value)? muteOff,
    TResult Function(_HandledAllHeadsetStreams value)? handledAllHeadsetStreams,
    TResult Function(_SendDTMFValue value)? sendDTMF,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (switchToBluetoothHeadset != null) {
      return switchToBluetoothHeadset(this);
    }
    return orElse();
  }
}

abstract class _SwitchedToBluetooth implements DialerEvent {
  const factory _SwitchedToBluetooth() = _$SwitchedToBluetoothImpl;
}

/// @nodoc
abstract class _$$SwitchedToWiredHeadsetzImplCopyWith<$Res> {
  factory _$$SwitchedToWiredHeadsetzImplCopyWith(
          _$SwitchedToWiredHeadsetzImpl value,
          $Res Function(_$SwitchedToWiredHeadsetzImpl) then) =
      __$$SwitchedToWiredHeadsetzImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SwitchedToWiredHeadsetzImplCopyWithImpl<$Res>
    extends _$DialerEventCopyWithImpl<$Res, _$SwitchedToWiredHeadsetzImpl>
    implements _$$SwitchedToWiredHeadsetzImplCopyWith<$Res> {
  __$$SwitchedToWiredHeadsetzImplCopyWithImpl(
      _$SwitchedToWiredHeadsetzImpl _value,
      $Res Function(_$SwitchedToWiredHeadsetzImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SwitchedToWiredHeadsetzImpl
    with DiagnosticableTreeMixin
    implements _SwitchedToWiredHeadsetz {
  const _$SwitchedToWiredHeadsetzImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DialerEvent.switchToWiredHeadet()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'DialerEvent.switchToWiredHeadet'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SwitchedToWiredHeadsetzImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? sipUsername, String? sipPassword)
        register,
    required TResult Function() reset,
    required TResult Function() reloadAsterisk,
    required TResult Function(String phoneNumber) callStarted,
    required TResult Function() callStopped,
    required TResult Function() hangedup,
    required TResult Function(sip_ua.CallState event) handledCallStream,
    required TResult Function(DialerStatus status) updatedDialerStatus,
    required TResult Function(sip_ua.Call? call) updatedCall,
    required TResult Function(bool isOnHold, String? onHoldOriginator)
        updatedOnHoldStatus,
    required TResult Function() forceEndCurrentCall,
    required TResult Function(sip_ua.CallStateEnum callStateEnum)
        updatedCallStateEnum,
    required TResult Function(sip_ua.RegistrationStateEnum status)
        updatedRegistrationStatus,
    required TResult Function(String? message) sendErrorMessage,
    required TResult Function() timerStarted,
    required TResult Function(int value) timerPaused,
    required TResult Function() timerStopped,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() holdCall,
    required TResult Function() unholdCall,
    required TResult Function() switchToBluetoothHeadset,
    required TResult Function() switchToWiredHeadet,
    required TResult Function() disableSpeakerPhone,
    required TResult Function() switchSpeakerPhoneOn,
    required TResult Function() muteOn,
    required TResult Function() muteOff,
    required TResult Function() handledAllHeadsetStreams,
    required TResult Function(String value) sendDTMF,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
  }) {
    return switchToWiredHeadet();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? sipUsername, String? sipPassword)? register,
    TResult? Function()? reset,
    TResult? Function()? reloadAsterisk,
    TResult? Function(String phoneNumber)? callStarted,
    TResult? Function()? callStopped,
    TResult? Function()? hangedup,
    TResult? Function(sip_ua.CallState event)? handledCallStream,
    TResult? Function(DialerStatus status)? updatedDialerStatus,
    TResult? Function(sip_ua.Call? call)? updatedCall,
    TResult? Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult? Function()? forceEndCurrentCall,
    TResult? Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult? Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult? Function(String? message)? sendErrorMessage,
    TResult? Function()? timerStarted,
    TResult? Function(int value)? timerPaused,
    TResult? Function()? timerStopped,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? holdCall,
    TResult? Function()? unholdCall,
    TResult? Function()? switchToBluetoothHeadset,
    TResult? Function()? switchToWiredHeadet,
    TResult? Function()? disableSpeakerPhone,
    TResult? Function()? switchSpeakerPhoneOn,
    TResult? Function()? muteOn,
    TResult? Function()? muteOff,
    TResult? Function()? handledAllHeadsetStreams,
    TResult? Function(String value)? sendDTMF,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
  }) {
    return switchToWiredHeadet?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? sipUsername, String? sipPassword)? register,
    TResult Function()? reset,
    TResult Function()? reloadAsterisk,
    TResult Function(String phoneNumber)? callStarted,
    TResult Function()? callStopped,
    TResult Function()? hangedup,
    TResult Function(sip_ua.CallState event)? handledCallStream,
    TResult Function(DialerStatus status)? updatedDialerStatus,
    TResult Function(sip_ua.Call? call)? updatedCall,
    TResult Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult Function()? forceEndCurrentCall,
    TResult Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult Function(String? message)? sendErrorMessage,
    TResult Function()? timerStarted,
    TResult Function(int value)? timerPaused,
    TResult Function()? timerStopped,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? holdCall,
    TResult Function()? unholdCall,
    TResult Function()? switchToBluetoothHeadset,
    TResult Function()? switchToWiredHeadet,
    TResult Function()? disableSpeakerPhone,
    TResult Function()? switchSpeakerPhoneOn,
    TResult Function()? muteOn,
    TResult Function()? muteOff,
    TResult Function()? handledAllHeadsetStreams,
    TResult Function(String value)? sendDTMF,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (switchToWiredHeadet != null) {
      return switchToWiredHeadet();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) register,
    required TResult Function(_Initial value) reset,
    required TResult Function(_RegisteredAgain value) reloadAsterisk,
    required TResult Function(_CallStarted value) callStarted,
    required TResult Function(_CallStopped value) callStopped,
    required TResult Function(_HangedUp value) hangedup,
    required TResult Function(_HandledCallStream value) handledCallStream,
    required TResult Function(_UpdatedDialerStatus value) updatedDialerStatus,
    required TResult Function(_UpdatedCall value) updatedCall,
    required TResult Function(_UpdatedOnHoldStatus value) updatedOnHoldStatus,
    required TResult Function(_ForceEndCurrentCall value) forceEndCurrentCall,
    required TResult Function(_UpdatedCallStateEnum value) updatedCallStateEnum,
    required TResult Function(_UpdatedRegistrationStatus value)
        updatedRegistrationStatus,
    required TResult Function(_HandledMessageRequest value) sendErrorMessage,
    required TResult Function(_StartedTimer value) timerStarted,
    required TResult Function(_PausedTimer value) timerPaused,
    required TResult Function(_StoppedTimer value) timerStopped,
    required TResult Function(_UpdatedTimer value) timerUpdated,
    required TResult Function(_HoldCall value) holdCall,
    required TResult Function(_UnholdCall value) unholdCall,
    required TResult Function(_SwitchedToBluetooth value)
        switchToBluetoothHeadset,
    required TResult Function(_SwitchedToWiredHeadsetz value)
        switchToWiredHeadet,
    required TResult Function(_SwitchedSpeakerOff value) disableSpeakerPhone,
    required TResult Function(_SwitchSpeakerphoneOn value) switchSpeakerPhoneOn,
    required TResult Function(_MuteOn value) muteOn,
    required TResult Function(_MuteOff value) muteOff,
    required TResult Function(_HandledAllHeadsetStreams value)
        handledAllHeadsetStreams,
    required TResult Function(_SendDTMFValue value) sendDTMF,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
  }) {
    return switchToWiredHeadet(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? register,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_RegisteredAgain value)? reloadAsterisk,
    TResult? Function(_CallStarted value)? callStarted,
    TResult? Function(_CallStopped value)? callStopped,
    TResult? Function(_HangedUp value)? hangedup,
    TResult? Function(_HandledCallStream value)? handledCallStream,
    TResult? Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult? Function(_UpdatedCall value)? updatedCall,
    TResult? Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult? Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult? Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult? Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult? Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult? Function(_StartedTimer value)? timerStarted,
    TResult? Function(_PausedTimer value)? timerPaused,
    TResult? Function(_StoppedTimer value)? timerStopped,
    TResult? Function(_UpdatedTimer value)? timerUpdated,
    TResult? Function(_HoldCall value)? holdCall,
    TResult? Function(_UnholdCall value)? unholdCall,
    TResult? Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult? Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult? Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult? Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult? Function(_MuteOn value)? muteOn,
    TResult? Function(_MuteOff value)? muteOff,
    TResult? Function(_HandledAllHeadsetStreams value)?
        handledAllHeadsetStreams,
    TResult? Function(_SendDTMFValue value)? sendDTMF,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
  }) {
    return switchToWiredHeadet?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? register,
    TResult Function(_Initial value)? reset,
    TResult Function(_RegisteredAgain value)? reloadAsterisk,
    TResult Function(_CallStarted value)? callStarted,
    TResult Function(_CallStopped value)? callStopped,
    TResult Function(_HangedUp value)? hangedup,
    TResult Function(_HandledCallStream value)? handledCallStream,
    TResult Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult Function(_UpdatedCall value)? updatedCall,
    TResult Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult Function(_StartedTimer value)? timerStarted,
    TResult Function(_PausedTimer value)? timerPaused,
    TResult Function(_StoppedTimer value)? timerStopped,
    TResult Function(_UpdatedTimer value)? timerUpdated,
    TResult Function(_HoldCall value)? holdCall,
    TResult Function(_UnholdCall value)? unholdCall,
    TResult Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult Function(_MuteOn value)? muteOn,
    TResult Function(_MuteOff value)? muteOff,
    TResult Function(_HandledAllHeadsetStreams value)? handledAllHeadsetStreams,
    TResult Function(_SendDTMFValue value)? sendDTMF,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (switchToWiredHeadet != null) {
      return switchToWiredHeadet(this);
    }
    return orElse();
  }
}

abstract class _SwitchedToWiredHeadsetz implements DialerEvent {
  const factory _SwitchedToWiredHeadsetz() = _$SwitchedToWiredHeadsetzImpl;
}

/// @nodoc
abstract class _$$SwitchedSpeakerOffImplCopyWith<$Res> {
  factory _$$SwitchedSpeakerOffImplCopyWith(_$SwitchedSpeakerOffImpl value,
          $Res Function(_$SwitchedSpeakerOffImpl) then) =
      __$$SwitchedSpeakerOffImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SwitchedSpeakerOffImplCopyWithImpl<$Res>
    extends _$DialerEventCopyWithImpl<$Res, _$SwitchedSpeakerOffImpl>
    implements _$$SwitchedSpeakerOffImplCopyWith<$Res> {
  __$$SwitchedSpeakerOffImplCopyWithImpl(_$SwitchedSpeakerOffImpl _value,
      $Res Function(_$SwitchedSpeakerOffImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SwitchedSpeakerOffImpl
    with DiagnosticableTreeMixin
    implements _SwitchedSpeakerOff {
  const _$SwitchedSpeakerOffImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DialerEvent.disableSpeakerPhone()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'DialerEvent.disableSpeakerPhone'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SwitchedSpeakerOffImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? sipUsername, String? sipPassword)
        register,
    required TResult Function() reset,
    required TResult Function() reloadAsterisk,
    required TResult Function(String phoneNumber) callStarted,
    required TResult Function() callStopped,
    required TResult Function() hangedup,
    required TResult Function(sip_ua.CallState event) handledCallStream,
    required TResult Function(DialerStatus status) updatedDialerStatus,
    required TResult Function(sip_ua.Call? call) updatedCall,
    required TResult Function(bool isOnHold, String? onHoldOriginator)
        updatedOnHoldStatus,
    required TResult Function() forceEndCurrentCall,
    required TResult Function(sip_ua.CallStateEnum callStateEnum)
        updatedCallStateEnum,
    required TResult Function(sip_ua.RegistrationStateEnum status)
        updatedRegistrationStatus,
    required TResult Function(String? message) sendErrorMessage,
    required TResult Function() timerStarted,
    required TResult Function(int value) timerPaused,
    required TResult Function() timerStopped,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() holdCall,
    required TResult Function() unholdCall,
    required TResult Function() switchToBluetoothHeadset,
    required TResult Function() switchToWiredHeadet,
    required TResult Function() disableSpeakerPhone,
    required TResult Function() switchSpeakerPhoneOn,
    required TResult Function() muteOn,
    required TResult Function() muteOff,
    required TResult Function() handledAllHeadsetStreams,
    required TResult Function(String value) sendDTMF,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
  }) {
    return disableSpeakerPhone();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? sipUsername, String? sipPassword)? register,
    TResult? Function()? reset,
    TResult? Function()? reloadAsterisk,
    TResult? Function(String phoneNumber)? callStarted,
    TResult? Function()? callStopped,
    TResult? Function()? hangedup,
    TResult? Function(sip_ua.CallState event)? handledCallStream,
    TResult? Function(DialerStatus status)? updatedDialerStatus,
    TResult? Function(sip_ua.Call? call)? updatedCall,
    TResult? Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult? Function()? forceEndCurrentCall,
    TResult? Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult? Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult? Function(String? message)? sendErrorMessage,
    TResult? Function()? timerStarted,
    TResult? Function(int value)? timerPaused,
    TResult? Function()? timerStopped,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? holdCall,
    TResult? Function()? unholdCall,
    TResult? Function()? switchToBluetoothHeadset,
    TResult? Function()? switchToWiredHeadet,
    TResult? Function()? disableSpeakerPhone,
    TResult? Function()? switchSpeakerPhoneOn,
    TResult? Function()? muteOn,
    TResult? Function()? muteOff,
    TResult? Function()? handledAllHeadsetStreams,
    TResult? Function(String value)? sendDTMF,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
  }) {
    return disableSpeakerPhone?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? sipUsername, String? sipPassword)? register,
    TResult Function()? reset,
    TResult Function()? reloadAsterisk,
    TResult Function(String phoneNumber)? callStarted,
    TResult Function()? callStopped,
    TResult Function()? hangedup,
    TResult Function(sip_ua.CallState event)? handledCallStream,
    TResult Function(DialerStatus status)? updatedDialerStatus,
    TResult Function(sip_ua.Call? call)? updatedCall,
    TResult Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult Function()? forceEndCurrentCall,
    TResult Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult Function(String? message)? sendErrorMessage,
    TResult Function()? timerStarted,
    TResult Function(int value)? timerPaused,
    TResult Function()? timerStopped,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? holdCall,
    TResult Function()? unholdCall,
    TResult Function()? switchToBluetoothHeadset,
    TResult Function()? switchToWiredHeadet,
    TResult Function()? disableSpeakerPhone,
    TResult Function()? switchSpeakerPhoneOn,
    TResult Function()? muteOn,
    TResult Function()? muteOff,
    TResult Function()? handledAllHeadsetStreams,
    TResult Function(String value)? sendDTMF,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (disableSpeakerPhone != null) {
      return disableSpeakerPhone();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) register,
    required TResult Function(_Initial value) reset,
    required TResult Function(_RegisteredAgain value) reloadAsterisk,
    required TResult Function(_CallStarted value) callStarted,
    required TResult Function(_CallStopped value) callStopped,
    required TResult Function(_HangedUp value) hangedup,
    required TResult Function(_HandledCallStream value) handledCallStream,
    required TResult Function(_UpdatedDialerStatus value) updatedDialerStatus,
    required TResult Function(_UpdatedCall value) updatedCall,
    required TResult Function(_UpdatedOnHoldStatus value) updatedOnHoldStatus,
    required TResult Function(_ForceEndCurrentCall value) forceEndCurrentCall,
    required TResult Function(_UpdatedCallStateEnum value) updatedCallStateEnum,
    required TResult Function(_UpdatedRegistrationStatus value)
        updatedRegistrationStatus,
    required TResult Function(_HandledMessageRequest value) sendErrorMessage,
    required TResult Function(_StartedTimer value) timerStarted,
    required TResult Function(_PausedTimer value) timerPaused,
    required TResult Function(_StoppedTimer value) timerStopped,
    required TResult Function(_UpdatedTimer value) timerUpdated,
    required TResult Function(_HoldCall value) holdCall,
    required TResult Function(_UnholdCall value) unholdCall,
    required TResult Function(_SwitchedToBluetooth value)
        switchToBluetoothHeadset,
    required TResult Function(_SwitchedToWiredHeadsetz value)
        switchToWiredHeadet,
    required TResult Function(_SwitchedSpeakerOff value) disableSpeakerPhone,
    required TResult Function(_SwitchSpeakerphoneOn value) switchSpeakerPhoneOn,
    required TResult Function(_MuteOn value) muteOn,
    required TResult Function(_MuteOff value) muteOff,
    required TResult Function(_HandledAllHeadsetStreams value)
        handledAllHeadsetStreams,
    required TResult Function(_SendDTMFValue value) sendDTMF,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
  }) {
    return disableSpeakerPhone(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? register,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_RegisteredAgain value)? reloadAsterisk,
    TResult? Function(_CallStarted value)? callStarted,
    TResult? Function(_CallStopped value)? callStopped,
    TResult? Function(_HangedUp value)? hangedup,
    TResult? Function(_HandledCallStream value)? handledCallStream,
    TResult? Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult? Function(_UpdatedCall value)? updatedCall,
    TResult? Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult? Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult? Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult? Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult? Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult? Function(_StartedTimer value)? timerStarted,
    TResult? Function(_PausedTimer value)? timerPaused,
    TResult? Function(_StoppedTimer value)? timerStopped,
    TResult? Function(_UpdatedTimer value)? timerUpdated,
    TResult? Function(_HoldCall value)? holdCall,
    TResult? Function(_UnholdCall value)? unholdCall,
    TResult? Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult? Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult? Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult? Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult? Function(_MuteOn value)? muteOn,
    TResult? Function(_MuteOff value)? muteOff,
    TResult? Function(_HandledAllHeadsetStreams value)?
        handledAllHeadsetStreams,
    TResult? Function(_SendDTMFValue value)? sendDTMF,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
  }) {
    return disableSpeakerPhone?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? register,
    TResult Function(_Initial value)? reset,
    TResult Function(_RegisteredAgain value)? reloadAsterisk,
    TResult Function(_CallStarted value)? callStarted,
    TResult Function(_CallStopped value)? callStopped,
    TResult Function(_HangedUp value)? hangedup,
    TResult Function(_HandledCallStream value)? handledCallStream,
    TResult Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult Function(_UpdatedCall value)? updatedCall,
    TResult Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult Function(_StartedTimer value)? timerStarted,
    TResult Function(_PausedTimer value)? timerPaused,
    TResult Function(_StoppedTimer value)? timerStopped,
    TResult Function(_UpdatedTimer value)? timerUpdated,
    TResult Function(_HoldCall value)? holdCall,
    TResult Function(_UnholdCall value)? unholdCall,
    TResult Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult Function(_MuteOn value)? muteOn,
    TResult Function(_MuteOff value)? muteOff,
    TResult Function(_HandledAllHeadsetStreams value)? handledAllHeadsetStreams,
    TResult Function(_SendDTMFValue value)? sendDTMF,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (disableSpeakerPhone != null) {
      return disableSpeakerPhone(this);
    }
    return orElse();
  }
}

abstract class _SwitchedSpeakerOff implements DialerEvent {
  const factory _SwitchedSpeakerOff() = _$SwitchedSpeakerOffImpl;
}

/// @nodoc
abstract class _$$SwitchSpeakerphoneOnImplCopyWith<$Res> {
  factory _$$SwitchSpeakerphoneOnImplCopyWith(_$SwitchSpeakerphoneOnImpl value,
          $Res Function(_$SwitchSpeakerphoneOnImpl) then) =
      __$$SwitchSpeakerphoneOnImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SwitchSpeakerphoneOnImplCopyWithImpl<$Res>
    extends _$DialerEventCopyWithImpl<$Res, _$SwitchSpeakerphoneOnImpl>
    implements _$$SwitchSpeakerphoneOnImplCopyWith<$Res> {
  __$$SwitchSpeakerphoneOnImplCopyWithImpl(_$SwitchSpeakerphoneOnImpl _value,
      $Res Function(_$SwitchSpeakerphoneOnImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SwitchSpeakerphoneOnImpl
    with DiagnosticableTreeMixin
    implements _SwitchSpeakerphoneOn {
  const _$SwitchSpeakerphoneOnImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DialerEvent.switchSpeakerPhoneOn()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'DialerEvent.switchSpeakerPhoneOn'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SwitchSpeakerphoneOnImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? sipUsername, String? sipPassword)
        register,
    required TResult Function() reset,
    required TResult Function() reloadAsterisk,
    required TResult Function(String phoneNumber) callStarted,
    required TResult Function() callStopped,
    required TResult Function() hangedup,
    required TResult Function(sip_ua.CallState event) handledCallStream,
    required TResult Function(DialerStatus status) updatedDialerStatus,
    required TResult Function(sip_ua.Call? call) updatedCall,
    required TResult Function(bool isOnHold, String? onHoldOriginator)
        updatedOnHoldStatus,
    required TResult Function() forceEndCurrentCall,
    required TResult Function(sip_ua.CallStateEnum callStateEnum)
        updatedCallStateEnum,
    required TResult Function(sip_ua.RegistrationStateEnum status)
        updatedRegistrationStatus,
    required TResult Function(String? message) sendErrorMessage,
    required TResult Function() timerStarted,
    required TResult Function(int value) timerPaused,
    required TResult Function() timerStopped,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() holdCall,
    required TResult Function() unholdCall,
    required TResult Function() switchToBluetoothHeadset,
    required TResult Function() switchToWiredHeadet,
    required TResult Function() disableSpeakerPhone,
    required TResult Function() switchSpeakerPhoneOn,
    required TResult Function() muteOn,
    required TResult Function() muteOff,
    required TResult Function() handledAllHeadsetStreams,
    required TResult Function(String value) sendDTMF,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
  }) {
    return switchSpeakerPhoneOn();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? sipUsername, String? sipPassword)? register,
    TResult? Function()? reset,
    TResult? Function()? reloadAsterisk,
    TResult? Function(String phoneNumber)? callStarted,
    TResult? Function()? callStopped,
    TResult? Function()? hangedup,
    TResult? Function(sip_ua.CallState event)? handledCallStream,
    TResult? Function(DialerStatus status)? updatedDialerStatus,
    TResult? Function(sip_ua.Call? call)? updatedCall,
    TResult? Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult? Function()? forceEndCurrentCall,
    TResult? Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult? Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult? Function(String? message)? sendErrorMessage,
    TResult? Function()? timerStarted,
    TResult? Function(int value)? timerPaused,
    TResult? Function()? timerStopped,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? holdCall,
    TResult? Function()? unholdCall,
    TResult? Function()? switchToBluetoothHeadset,
    TResult? Function()? switchToWiredHeadet,
    TResult? Function()? disableSpeakerPhone,
    TResult? Function()? switchSpeakerPhoneOn,
    TResult? Function()? muteOn,
    TResult? Function()? muteOff,
    TResult? Function()? handledAllHeadsetStreams,
    TResult? Function(String value)? sendDTMF,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
  }) {
    return switchSpeakerPhoneOn?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? sipUsername, String? sipPassword)? register,
    TResult Function()? reset,
    TResult Function()? reloadAsterisk,
    TResult Function(String phoneNumber)? callStarted,
    TResult Function()? callStopped,
    TResult Function()? hangedup,
    TResult Function(sip_ua.CallState event)? handledCallStream,
    TResult Function(DialerStatus status)? updatedDialerStatus,
    TResult Function(sip_ua.Call? call)? updatedCall,
    TResult Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult Function()? forceEndCurrentCall,
    TResult Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult Function(String? message)? sendErrorMessage,
    TResult Function()? timerStarted,
    TResult Function(int value)? timerPaused,
    TResult Function()? timerStopped,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? holdCall,
    TResult Function()? unholdCall,
    TResult Function()? switchToBluetoothHeadset,
    TResult Function()? switchToWiredHeadet,
    TResult Function()? disableSpeakerPhone,
    TResult Function()? switchSpeakerPhoneOn,
    TResult Function()? muteOn,
    TResult Function()? muteOff,
    TResult Function()? handledAllHeadsetStreams,
    TResult Function(String value)? sendDTMF,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (switchSpeakerPhoneOn != null) {
      return switchSpeakerPhoneOn();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) register,
    required TResult Function(_Initial value) reset,
    required TResult Function(_RegisteredAgain value) reloadAsterisk,
    required TResult Function(_CallStarted value) callStarted,
    required TResult Function(_CallStopped value) callStopped,
    required TResult Function(_HangedUp value) hangedup,
    required TResult Function(_HandledCallStream value) handledCallStream,
    required TResult Function(_UpdatedDialerStatus value) updatedDialerStatus,
    required TResult Function(_UpdatedCall value) updatedCall,
    required TResult Function(_UpdatedOnHoldStatus value) updatedOnHoldStatus,
    required TResult Function(_ForceEndCurrentCall value) forceEndCurrentCall,
    required TResult Function(_UpdatedCallStateEnum value) updatedCallStateEnum,
    required TResult Function(_UpdatedRegistrationStatus value)
        updatedRegistrationStatus,
    required TResult Function(_HandledMessageRequest value) sendErrorMessage,
    required TResult Function(_StartedTimer value) timerStarted,
    required TResult Function(_PausedTimer value) timerPaused,
    required TResult Function(_StoppedTimer value) timerStopped,
    required TResult Function(_UpdatedTimer value) timerUpdated,
    required TResult Function(_HoldCall value) holdCall,
    required TResult Function(_UnholdCall value) unholdCall,
    required TResult Function(_SwitchedToBluetooth value)
        switchToBluetoothHeadset,
    required TResult Function(_SwitchedToWiredHeadsetz value)
        switchToWiredHeadet,
    required TResult Function(_SwitchedSpeakerOff value) disableSpeakerPhone,
    required TResult Function(_SwitchSpeakerphoneOn value) switchSpeakerPhoneOn,
    required TResult Function(_MuteOn value) muteOn,
    required TResult Function(_MuteOff value) muteOff,
    required TResult Function(_HandledAllHeadsetStreams value)
        handledAllHeadsetStreams,
    required TResult Function(_SendDTMFValue value) sendDTMF,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
  }) {
    return switchSpeakerPhoneOn(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? register,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_RegisteredAgain value)? reloadAsterisk,
    TResult? Function(_CallStarted value)? callStarted,
    TResult? Function(_CallStopped value)? callStopped,
    TResult? Function(_HangedUp value)? hangedup,
    TResult? Function(_HandledCallStream value)? handledCallStream,
    TResult? Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult? Function(_UpdatedCall value)? updatedCall,
    TResult? Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult? Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult? Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult? Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult? Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult? Function(_StartedTimer value)? timerStarted,
    TResult? Function(_PausedTimer value)? timerPaused,
    TResult? Function(_StoppedTimer value)? timerStopped,
    TResult? Function(_UpdatedTimer value)? timerUpdated,
    TResult? Function(_HoldCall value)? holdCall,
    TResult? Function(_UnholdCall value)? unholdCall,
    TResult? Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult? Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult? Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult? Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult? Function(_MuteOn value)? muteOn,
    TResult? Function(_MuteOff value)? muteOff,
    TResult? Function(_HandledAllHeadsetStreams value)?
        handledAllHeadsetStreams,
    TResult? Function(_SendDTMFValue value)? sendDTMF,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
  }) {
    return switchSpeakerPhoneOn?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? register,
    TResult Function(_Initial value)? reset,
    TResult Function(_RegisteredAgain value)? reloadAsterisk,
    TResult Function(_CallStarted value)? callStarted,
    TResult Function(_CallStopped value)? callStopped,
    TResult Function(_HangedUp value)? hangedup,
    TResult Function(_HandledCallStream value)? handledCallStream,
    TResult Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult Function(_UpdatedCall value)? updatedCall,
    TResult Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult Function(_StartedTimer value)? timerStarted,
    TResult Function(_PausedTimer value)? timerPaused,
    TResult Function(_StoppedTimer value)? timerStopped,
    TResult Function(_UpdatedTimer value)? timerUpdated,
    TResult Function(_HoldCall value)? holdCall,
    TResult Function(_UnholdCall value)? unholdCall,
    TResult Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult Function(_MuteOn value)? muteOn,
    TResult Function(_MuteOff value)? muteOff,
    TResult Function(_HandledAllHeadsetStreams value)? handledAllHeadsetStreams,
    TResult Function(_SendDTMFValue value)? sendDTMF,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (switchSpeakerPhoneOn != null) {
      return switchSpeakerPhoneOn(this);
    }
    return orElse();
  }
}

abstract class _SwitchSpeakerphoneOn implements DialerEvent {
  const factory _SwitchSpeakerphoneOn() = _$SwitchSpeakerphoneOnImpl;
}

/// @nodoc
abstract class _$$MuteOnImplCopyWith<$Res> {
  factory _$$MuteOnImplCopyWith(
          _$MuteOnImpl value, $Res Function(_$MuteOnImpl) then) =
      __$$MuteOnImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$MuteOnImplCopyWithImpl<$Res>
    extends _$DialerEventCopyWithImpl<$Res, _$MuteOnImpl>
    implements _$$MuteOnImplCopyWith<$Res> {
  __$$MuteOnImplCopyWithImpl(
      _$MuteOnImpl _value, $Res Function(_$MuteOnImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$MuteOnImpl with DiagnosticableTreeMixin implements _MuteOn {
  const _$MuteOnImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DialerEvent.muteOn()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'DialerEvent.muteOn'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$MuteOnImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? sipUsername, String? sipPassword)
        register,
    required TResult Function() reset,
    required TResult Function() reloadAsterisk,
    required TResult Function(String phoneNumber) callStarted,
    required TResult Function() callStopped,
    required TResult Function() hangedup,
    required TResult Function(sip_ua.CallState event) handledCallStream,
    required TResult Function(DialerStatus status) updatedDialerStatus,
    required TResult Function(sip_ua.Call? call) updatedCall,
    required TResult Function(bool isOnHold, String? onHoldOriginator)
        updatedOnHoldStatus,
    required TResult Function() forceEndCurrentCall,
    required TResult Function(sip_ua.CallStateEnum callStateEnum)
        updatedCallStateEnum,
    required TResult Function(sip_ua.RegistrationStateEnum status)
        updatedRegistrationStatus,
    required TResult Function(String? message) sendErrorMessage,
    required TResult Function() timerStarted,
    required TResult Function(int value) timerPaused,
    required TResult Function() timerStopped,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() holdCall,
    required TResult Function() unholdCall,
    required TResult Function() switchToBluetoothHeadset,
    required TResult Function() switchToWiredHeadet,
    required TResult Function() disableSpeakerPhone,
    required TResult Function() switchSpeakerPhoneOn,
    required TResult Function() muteOn,
    required TResult Function() muteOff,
    required TResult Function() handledAllHeadsetStreams,
    required TResult Function(String value) sendDTMF,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
  }) {
    return muteOn();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? sipUsername, String? sipPassword)? register,
    TResult? Function()? reset,
    TResult? Function()? reloadAsterisk,
    TResult? Function(String phoneNumber)? callStarted,
    TResult? Function()? callStopped,
    TResult? Function()? hangedup,
    TResult? Function(sip_ua.CallState event)? handledCallStream,
    TResult? Function(DialerStatus status)? updatedDialerStatus,
    TResult? Function(sip_ua.Call? call)? updatedCall,
    TResult? Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult? Function()? forceEndCurrentCall,
    TResult? Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult? Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult? Function(String? message)? sendErrorMessage,
    TResult? Function()? timerStarted,
    TResult? Function(int value)? timerPaused,
    TResult? Function()? timerStopped,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? holdCall,
    TResult? Function()? unholdCall,
    TResult? Function()? switchToBluetoothHeadset,
    TResult? Function()? switchToWiredHeadet,
    TResult? Function()? disableSpeakerPhone,
    TResult? Function()? switchSpeakerPhoneOn,
    TResult? Function()? muteOn,
    TResult? Function()? muteOff,
    TResult? Function()? handledAllHeadsetStreams,
    TResult? Function(String value)? sendDTMF,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
  }) {
    return muteOn?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? sipUsername, String? sipPassword)? register,
    TResult Function()? reset,
    TResult Function()? reloadAsterisk,
    TResult Function(String phoneNumber)? callStarted,
    TResult Function()? callStopped,
    TResult Function()? hangedup,
    TResult Function(sip_ua.CallState event)? handledCallStream,
    TResult Function(DialerStatus status)? updatedDialerStatus,
    TResult Function(sip_ua.Call? call)? updatedCall,
    TResult Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult Function()? forceEndCurrentCall,
    TResult Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult Function(String? message)? sendErrorMessage,
    TResult Function()? timerStarted,
    TResult Function(int value)? timerPaused,
    TResult Function()? timerStopped,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? holdCall,
    TResult Function()? unholdCall,
    TResult Function()? switchToBluetoothHeadset,
    TResult Function()? switchToWiredHeadet,
    TResult Function()? disableSpeakerPhone,
    TResult Function()? switchSpeakerPhoneOn,
    TResult Function()? muteOn,
    TResult Function()? muteOff,
    TResult Function()? handledAllHeadsetStreams,
    TResult Function(String value)? sendDTMF,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (muteOn != null) {
      return muteOn();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) register,
    required TResult Function(_Initial value) reset,
    required TResult Function(_RegisteredAgain value) reloadAsterisk,
    required TResult Function(_CallStarted value) callStarted,
    required TResult Function(_CallStopped value) callStopped,
    required TResult Function(_HangedUp value) hangedup,
    required TResult Function(_HandledCallStream value) handledCallStream,
    required TResult Function(_UpdatedDialerStatus value) updatedDialerStatus,
    required TResult Function(_UpdatedCall value) updatedCall,
    required TResult Function(_UpdatedOnHoldStatus value) updatedOnHoldStatus,
    required TResult Function(_ForceEndCurrentCall value) forceEndCurrentCall,
    required TResult Function(_UpdatedCallStateEnum value) updatedCallStateEnum,
    required TResult Function(_UpdatedRegistrationStatus value)
        updatedRegistrationStatus,
    required TResult Function(_HandledMessageRequest value) sendErrorMessage,
    required TResult Function(_StartedTimer value) timerStarted,
    required TResult Function(_PausedTimer value) timerPaused,
    required TResult Function(_StoppedTimer value) timerStopped,
    required TResult Function(_UpdatedTimer value) timerUpdated,
    required TResult Function(_HoldCall value) holdCall,
    required TResult Function(_UnholdCall value) unholdCall,
    required TResult Function(_SwitchedToBluetooth value)
        switchToBluetoothHeadset,
    required TResult Function(_SwitchedToWiredHeadsetz value)
        switchToWiredHeadet,
    required TResult Function(_SwitchedSpeakerOff value) disableSpeakerPhone,
    required TResult Function(_SwitchSpeakerphoneOn value) switchSpeakerPhoneOn,
    required TResult Function(_MuteOn value) muteOn,
    required TResult Function(_MuteOff value) muteOff,
    required TResult Function(_HandledAllHeadsetStreams value)
        handledAllHeadsetStreams,
    required TResult Function(_SendDTMFValue value) sendDTMF,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
  }) {
    return muteOn(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? register,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_RegisteredAgain value)? reloadAsterisk,
    TResult? Function(_CallStarted value)? callStarted,
    TResult? Function(_CallStopped value)? callStopped,
    TResult? Function(_HangedUp value)? hangedup,
    TResult? Function(_HandledCallStream value)? handledCallStream,
    TResult? Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult? Function(_UpdatedCall value)? updatedCall,
    TResult? Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult? Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult? Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult? Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult? Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult? Function(_StartedTimer value)? timerStarted,
    TResult? Function(_PausedTimer value)? timerPaused,
    TResult? Function(_StoppedTimer value)? timerStopped,
    TResult? Function(_UpdatedTimer value)? timerUpdated,
    TResult? Function(_HoldCall value)? holdCall,
    TResult? Function(_UnholdCall value)? unholdCall,
    TResult? Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult? Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult? Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult? Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult? Function(_MuteOn value)? muteOn,
    TResult? Function(_MuteOff value)? muteOff,
    TResult? Function(_HandledAllHeadsetStreams value)?
        handledAllHeadsetStreams,
    TResult? Function(_SendDTMFValue value)? sendDTMF,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
  }) {
    return muteOn?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? register,
    TResult Function(_Initial value)? reset,
    TResult Function(_RegisteredAgain value)? reloadAsterisk,
    TResult Function(_CallStarted value)? callStarted,
    TResult Function(_CallStopped value)? callStopped,
    TResult Function(_HangedUp value)? hangedup,
    TResult Function(_HandledCallStream value)? handledCallStream,
    TResult Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult Function(_UpdatedCall value)? updatedCall,
    TResult Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult Function(_StartedTimer value)? timerStarted,
    TResult Function(_PausedTimer value)? timerPaused,
    TResult Function(_StoppedTimer value)? timerStopped,
    TResult Function(_UpdatedTimer value)? timerUpdated,
    TResult Function(_HoldCall value)? holdCall,
    TResult Function(_UnholdCall value)? unholdCall,
    TResult Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult Function(_MuteOn value)? muteOn,
    TResult Function(_MuteOff value)? muteOff,
    TResult Function(_HandledAllHeadsetStreams value)? handledAllHeadsetStreams,
    TResult Function(_SendDTMFValue value)? sendDTMF,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (muteOn != null) {
      return muteOn(this);
    }
    return orElse();
  }
}

abstract class _MuteOn implements DialerEvent {
  const factory _MuteOn() = _$MuteOnImpl;
}

/// @nodoc
abstract class _$$MuteOffImplCopyWith<$Res> {
  factory _$$MuteOffImplCopyWith(
          _$MuteOffImpl value, $Res Function(_$MuteOffImpl) then) =
      __$$MuteOffImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$MuteOffImplCopyWithImpl<$Res>
    extends _$DialerEventCopyWithImpl<$Res, _$MuteOffImpl>
    implements _$$MuteOffImplCopyWith<$Res> {
  __$$MuteOffImplCopyWithImpl(
      _$MuteOffImpl _value, $Res Function(_$MuteOffImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$MuteOffImpl with DiagnosticableTreeMixin implements _MuteOff {
  const _$MuteOffImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DialerEvent.muteOff()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'DialerEvent.muteOff'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$MuteOffImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? sipUsername, String? sipPassword)
        register,
    required TResult Function() reset,
    required TResult Function() reloadAsterisk,
    required TResult Function(String phoneNumber) callStarted,
    required TResult Function() callStopped,
    required TResult Function() hangedup,
    required TResult Function(sip_ua.CallState event) handledCallStream,
    required TResult Function(DialerStatus status) updatedDialerStatus,
    required TResult Function(sip_ua.Call? call) updatedCall,
    required TResult Function(bool isOnHold, String? onHoldOriginator)
        updatedOnHoldStatus,
    required TResult Function() forceEndCurrentCall,
    required TResult Function(sip_ua.CallStateEnum callStateEnum)
        updatedCallStateEnum,
    required TResult Function(sip_ua.RegistrationStateEnum status)
        updatedRegistrationStatus,
    required TResult Function(String? message) sendErrorMessage,
    required TResult Function() timerStarted,
    required TResult Function(int value) timerPaused,
    required TResult Function() timerStopped,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() holdCall,
    required TResult Function() unholdCall,
    required TResult Function() switchToBluetoothHeadset,
    required TResult Function() switchToWiredHeadet,
    required TResult Function() disableSpeakerPhone,
    required TResult Function() switchSpeakerPhoneOn,
    required TResult Function() muteOn,
    required TResult Function() muteOff,
    required TResult Function() handledAllHeadsetStreams,
    required TResult Function(String value) sendDTMF,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
  }) {
    return muteOff();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? sipUsername, String? sipPassword)? register,
    TResult? Function()? reset,
    TResult? Function()? reloadAsterisk,
    TResult? Function(String phoneNumber)? callStarted,
    TResult? Function()? callStopped,
    TResult? Function()? hangedup,
    TResult? Function(sip_ua.CallState event)? handledCallStream,
    TResult? Function(DialerStatus status)? updatedDialerStatus,
    TResult? Function(sip_ua.Call? call)? updatedCall,
    TResult? Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult? Function()? forceEndCurrentCall,
    TResult? Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult? Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult? Function(String? message)? sendErrorMessage,
    TResult? Function()? timerStarted,
    TResult? Function(int value)? timerPaused,
    TResult? Function()? timerStopped,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? holdCall,
    TResult? Function()? unholdCall,
    TResult? Function()? switchToBluetoothHeadset,
    TResult? Function()? switchToWiredHeadet,
    TResult? Function()? disableSpeakerPhone,
    TResult? Function()? switchSpeakerPhoneOn,
    TResult? Function()? muteOn,
    TResult? Function()? muteOff,
    TResult? Function()? handledAllHeadsetStreams,
    TResult? Function(String value)? sendDTMF,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
  }) {
    return muteOff?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? sipUsername, String? sipPassword)? register,
    TResult Function()? reset,
    TResult Function()? reloadAsterisk,
    TResult Function(String phoneNumber)? callStarted,
    TResult Function()? callStopped,
    TResult Function()? hangedup,
    TResult Function(sip_ua.CallState event)? handledCallStream,
    TResult Function(DialerStatus status)? updatedDialerStatus,
    TResult Function(sip_ua.Call? call)? updatedCall,
    TResult Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult Function()? forceEndCurrentCall,
    TResult Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult Function(String? message)? sendErrorMessage,
    TResult Function()? timerStarted,
    TResult Function(int value)? timerPaused,
    TResult Function()? timerStopped,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? holdCall,
    TResult Function()? unholdCall,
    TResult Function()? switchToBluetoothHeadset,
    TResult Function()? switchToWiredHeadet,
    TResult Function()? disableSpeakerPhone,
    TResult Function()? switchSpeakerPhoneOn,
    TResult Function()? muteOn,
    TResult Function()? muteOff,
    TResult Function()? handledAllHeadsetStreams,
    TResult Function(String value)? sendDTMF,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (muteOff != null) {
      return muteOff();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) register,
    required TResult Function(_Initial value) reset,
    required TResult Function(_RegisteredAgain value) reloadAsterisk,
    required TResult Function(_CallStarted value) callStarted,
    required TResult Function(_CallStopped value) callStopped,
    required TResult Function(_HangedUp value) hangedup,
    required TResult Function(_HandledCallStream value) handledCallStream,
    required TResult Function(_UpdatedDialerStatus value) updatedDialerStatus,
    required TResult Function(_UpdatedCall value) updatedCall,
    required TResult Function(_UpdatedOnHoldStatus value) updatedOnHoldStatus,
    required TResult Function(_ForceEndCurrentCall value) forceEndCurrentCall,
    required TResult Function(_UpdatedCallStateEnum value) updatedCallStateEnum,
    required TResult Function(_UpdatedRegistrationStatus value)
        updatedRegistrationStatus,
    required TResult Function(_HandledMessageRequest value) sendErrorMessage,
    required TResult Function(_StartedTimer value) timerStarted,
    required TResult Function(_PausedTimer value) timerPaused,
    required TResult Function(_StoppedTimer value) timerStopped,
    required TResult Function(_UpdatedTimer value) timerUpdated,
    required TResult Function(_HoldCall value) holdCall,
    required TResult Function(_UnholdCall value) unholdCall,
    required TResult Function(_SwitchedToBluetooth value)
        switchToBluetoothHeadset,
    required TResult Function(_SwitchedToWiredHeadsetz value)
        switchToWiredHeadet,
    required TResult Function(_SwitchedSpeakerOff value) disableSpeakerPhone,
    required TResult Function(_SwitchSpeakerphoneOn value) switchSpeakerPhoneOn,
    required TResult Function(_MuteOn value) muteOn,
    required TResult Function(_MuteOff value) muteOff,
    required TResult Function(_HandledAllHeadsetStreams value)
        handledAllHeadsetStreams,
    required TResult Function(_SendDTMFValue value) sendDTMF,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
  }) {
    return muteOff(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? register,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_RegisteredAgain value)? reloadAsterisk,
    TResult? Function(_CallStarted value)? callStarted,
    TResult? Function(_CallStopped value)? callStopped,
    TResult? Function(_HangedUp value)? hangedup,
    TResult? Function(_HandledCallStream value)? handledCallStream,
    TResult? Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult? Function(_UpdatedCall value)? updatedCall,
    TResult? Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult? Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult? Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult? Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult? Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult? Function(_StartedTimer value)? timerStarted,
    TResult? Function(_PausedTimer value)? timerPaused,
    TResult? Function(_StoppedTimer value)? timerStopped,
    TResult? Function(_UpdatedTimer value)? timerUpdated,
    TResult? Function(_HoldCall value)? holdCall,
    TResult? Function(_UnholdCall value)? unholdCall,
    TResult? Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult? Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult? Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult? Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult? Function(_MuteOn value)? muteOn,
    TResult? Function(_MuteOff value)? muteOff,
    TResult? Function(_HandledAllHeadsetStreams value)?
        handledAllHeadsetStreams,
    TResult? Function(_SendDTMFValue value)? sendDTMF,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
  }) {
    return muteOff?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? register,
    TResult Function(_Initial value)? reset,
    TResult Function(_RegisteredAgain value)? reloadAsterisk,
    TResult Function(_CallStarted value)? callStarted,
    TResult Function(_CallStopped value)? callStopped,
    TResult Function(_HangedUp value)? hangedup,
    TResult Function(_HandledCallStream value)? handledCallStream,
    TResult Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult Function(_UpdatedCall value)? updatedCall,
    TResult Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult Function(_StartedTimer value)? timerStarted,
    TResult Function(_PausedTimer value)? timerPaused,
    TResult Function(_StoppedTimer value)? timerStopped,
    TResult Function(_UpdatedTimer value)? timerUpdated,
    TResult Function(_HoldCall value)? holdCall,
    TResult Function(_UnholdCall value)? unholdCall,
    TResult Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult Function(_MuteOn value)? muteOn,
    TResult Function(_MuteOff value)? muteOff,
    TResult Function(_HandledAllHeadsetStreams value)? handledAllHeadsetStreams,
    TResult Function(_SendDTMFValue value)? sendDTMF,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (muteOff != null) {
      return muteOff(this);
    }
    return orElse();
  }
}

abstract class _MuteOff implements DialerEvent {
  const factory _MuteOff() = _$MuteOffImpl;
}

/// @nodoc
abstract class _$$HandledAllHeadsetStreamsImplCopyWith<$Res> {
  factory _$$HandledAllHeadsetStreamsImplCopyWith(
          _$HandledAllHeadsetStreamsImpl value,
          $Res Function(_$HandledAllHeadsetStreamsImpl) then) =
      __$$HandledAllHeadsetStreamsImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$HandledAllHeadsetStreamsImplCopyWithImpl<$Res>
    extends _$DialerEventCopyWithImpl<$Res, _$HandledAllHeadsetStreamsImpl>
    implements _$$HandledAllHeadsetStreamsImplCopyWith<$Res> {
  __$$HandledAllHeadsetStreamsImplCopyWithImpl(
      _$HandledAllHeadsetStreamsImpl _value,
      $Res Function(_$HandledAllHeadsetStreamsImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$HandledAllHeadsetStreamsImpl
    with DiagnosticableTreeMixin
    implements _HandledAllHeadsetStreams {
  const _$HandledAllHeadsetStreamsImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DialerEvent.handledAllHeadsetStreams()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(
          DiagnosticsProperty('type', 'DialerEvent.handledAllHeadsetStreams'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HandledAllHeadsetStreamsImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? sipUsername, String? sipPassword)
        register,
    required TResult Function() reset,
    required TResult Function() reloadAsterisk,
    required TResult Function(String phoneNumber) callStarted,
    required TResult Function() callStopped,
    required TResult Function() hangedup,
    required TResult Function(sip_ua.CallState event) handledCallStream,
    required TResult Function(DialerStatus status) updatedDialerStatus,
    required TResult Function(sip_ua.Call? call) updatedCall,
    required TResult Function(bool isOnHold, String? onHoldOriginator)
        updatedOnHoldStatus,
    required TResult Function() forceEndCurrentCall,
    required TResult Function(sip_ua.CallStateEnum callStateEnum)
        updatedCallStateEnum,
    required TResult Function(sip_ua.RegistrationStateEnum status)
        updatedRegistrationStatus,
    required TResult Function(String? message) sendErrorMessage,
    required TResult Function() timerStarted,
    required TResult Function(int value) timerPaused,
    required TResult Function() timerStopped,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() holdCall,
    required TResult Function() unholdCall,
    required TResult Function() switchToBluetoothHeadset,
    required TResult Function() switchToWiredHeadet,
    required TResult Function() disableSpeakerPhone,
    required TResult Function() switchSpeakerPhoneOn,
    required TResult Function() muteOn,
    required TResult Function() muteOff,
    required TResult Function() handledAllHeadsetStreams,
    required TResult Function(String value) sendDTMF,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
  }) {
    return handledAllHeadsetStreams();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? sipUsername, String? sipPassword)? register,
    TResult? Function()? reset,
    TResult? Function()? reloadAsterisk,
    TResult? Function(String phoneNumber)? callStarted,
    TResult? Function()? callStopped,
    TResult? Function()? hangedup,
    TResult? Function(sip_ua.CallState event)? handledCallStream,
    TResult? Function(DialerStatus status)? updatedDialerStatus,
    TResult? Function(sip_ua.Call? call)? updatedCall,
    TResult? Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult? Function()? forceEndCurrentCall,
    TResult? Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult? Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult? Function(String? message)? sendErrorMessage,
    TResult? Function()? timerStarted,
    TResult? Function(int value)? timerPaused,
    TResult? Function()? timerStopped,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? holdCall,
    TResult? Function()? unholdCall,
    TResult? Function()? switchToBluetoothHeadset,
    TResult? Function()? switchToWiredHeadet,
    TResult? Function()? disableSpeakerPhone,
    TResult? Function()? switchSpeakerPhoneOn,
    TResult? Function()? muteOn,
    TResult? Function()? muteOff,
    TResult? Function()? handledAllHeadsetStreams,
    TResult? Function(String value)? sendDTMF,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
  }) {
    return handledAllHeadsetStreams?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? sipUsername, String? sipPassword)? register,
    TResult Function()? reset,
    TResult Function()? reloadAsterisk,
    TResult Function(String phoneNumber)? callStarted,
    TResult Function()? callStopped,
    TResult Function()? hangedup,
    TResult Function(sip_ua.CallState event)? handledCallStream,
    TResult Function(DialerStatus status)? updatedDialerStatus,
    TResult Function(sip_ua.Call? call)? updatedCall,
    TResult Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult Function()? forceEndCurrentCall,
    TResult Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult Function(String? message)? sendErrorMessage,
    TResult Function()? timerStarted,
    TResult Function(int value)? timerPaused,
    TResult Function()? timerStopped,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? holdCall,
    TResult Function()? unholdCall,
    TResult Function()? switchToBluetoothHeadset,
    TResult Function()? switchToWiredHeadet,
    TResult Function()? disableSpeakerPhone,
    TResult Function()? switchSpeakerPhoneOn,
    TResult Function()? muteOn,
    TResult Function()? muteOff,
    TResult Function()? handledAllHeadsetStreams,
    TResult Function(String value)? sendDTMF,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (handledAllHeadsetStreams != null) {
      return handledAllHeadsetStreams();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) register,
    required TResult Function(_Initial value) reset,
    required TResult Function(_RegisteredAgain value) reloadAsterisk,
    required TResult Function(_CallStarted value) callStarted,
    required TResult Function(_CallStopped value) callStopped,
    required TResult Function(_HangedUp value) hangedup,
    required TResult Function(_HandledCallStream value) handledCallStream,
    required TResult Function(_UpdatedDialerStatus value) updatedDialerStatus,
    required TResult Function(_UpdatedCall value) updatedCall,
    required TResult Function(_UpdatedOnHoldStatus value) updatedOnHoldStatus,
    required TResult Function(_ForceEndCurrentCall value) forceEndCurrentCall,
    required TResult Function(_UpdatedCallStateEnum value) updatedCallStateEnum,
    required TResult Function(_UpdatedRegistrationStatus value)
        updatedRegistrationStatus,
    required TResult Function(_HandledMessageRequest value) sendErrorMessage,
    required TResult Function(_StartedTimer value) timerStarted,
    required TResult Function(_PausedTimer value) timerPaused,
    required TResult Function(_StoppedTimer value) timerStopped,
    required TResult Function(_UpdatedTimer value) timerUpdated,
    required TResult Function(_HoldCall value) holdCall,
    required TResult Function(_UnholdCall value) unholdCall,
    required TResult Function(_SwitchedToBluetooth value)
        switchToBluetoothHeadset,
    required TResult Function(_SwitchedToWiredHeadsetz value)
        switchToWiredHeadet,
    required TResult Function(_SwitchedSpeakerOff value) disableSpeakerPhone,
    required TResult Function(_SwitchSpeakerphoneOn value) switchSpeakerPhoneOn,
    required TResult Function(_MuteOn value) muteOn,
    required TResult Function(_MuteOff value) muteOff,
    required TResult Function(_HandledAllHeadsetStreams value)
        handledAllHeadsetStreams,
    required TResult Function(_SendDTMFValue value) sendDTMF,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
  }) {
    return handledAllHeadsetStreams(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? register,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_RegisteredAgain value)? reloadAsterisk,
    TResult? Function(_CallStarted value)? callStarted,
    TResult? Function(_CallStopped value)? callStopped,
    TResult? Function(_HangedUp value)? hangedup,
    TResult? Function(_HandledCallStream value)? handledCallStream,
    TResult? Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult? Function(_UpdatedCall value)? updatedCall,
    TResult? Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult? Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult? Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult? Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult? Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult? Function(_StartedTimer value)? timerStarted,
    TResult? Function(_PausedTimer value)? timerPaused,
    TResult? Function(_StoppedTimer value)? timerStopped,
    TResult? Function(_UpdatedTimer value)? timerUpdated,
    TResult? Function(_HoldCall value)? holdCall,
    TResult? Function(_UnholdCall value)? unholdCall,
    TResult? Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult? Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult? Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult? Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult? Function(_MuteOn value)? muteOn,
    TResult? Function(_MuteOff value)? muteOff,
    TResult? Function(_HandledAllHeadsetStreams value)?
        handledAllHeadsetStreams,
    TResult? Function(_SendDTMFValue value)? sendDTMF,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
  }) {
    return handledAllHeadsetStreams?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? register,
    TResult Function(_Initial value)? reset,
    TResult Function(_RegisteredAgain value)? reloadAsterisk,
    TResult Function(_CallStarted value)? callStarted,
    TResult Function(_CallStopped value)? callStopped,
    TResult Function(_HangedUp value)? hangedup,
    TResult Function(_HandledCallStream value)? handledCallStream,
    TResult Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult Function(_UpdatedCall value)? updatedCall,
    TResult Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult Function(_StartedTimer value)? timerStarted,
    TResult Function(_PausedTimer value)? timerPaused,
    TResult Function(_StoppedTimer value)? timerStopped,
    TResult Function(_UpdatedTimer value)? timerUpdated,
    TResult Function(_HoldCall value)? holdCall,
    TResult Function(_UnholdCall value)? unholdCall,
    TResult Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult Function(_MuteOn value)? muteOn,
    TResult Function(_MuteOff value)? muteOff,
    TResult Function(_HandledAllHeadsetStreams value)? handledAllHeadsetStreams,
    TResult Function(_SendDTMFValue value)? sendDTMF,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (handledAllHeadsetStreams != null) {
      return handledAllHeadsetStreams(this);
    }
    return orElse();
  }
}

abstract class _HandledAllHeadsetStreams implements DialerEvent {
  const factory _HandledAllHeadsetStreams() = _$HandledAllHeadsetStreamsImpl;
}

/// @nodoc
abstract class _$$SendDTMFValueImplCopyWith<$Res> {
  factory _$$SendDTMFValueImplCopyWith(
          _$SendDTMFValueImpl value, $Res Function(_$SendDTMFValueImpl) then) =
      __$$SendDTMFValueImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String value});
}

/// @nodoc
class __$$SendDTMFValueImplCopyWithImpl<$Res>
    extends _$DialerEventCopyWithImpl<$Res, _$SendDTMFValueImpl>
    implements _$$SendDTMFValueImplCopyWith<$Res> {
  __$$SendDTMFValueImplCopyWithImpl(
      _$SendDTMFValueImpl _value, $Res Function(_$SendDTMFValueImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$SendDTMFValueImpl(
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$SendDTMFValueImpl
    with DiagnosticableTreeMixin
    implements _SendDTMFValue {
  const _$SendDTMFValueImpl({required this.value});

  @override
  final String value;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DialerEvent.sendDTMF(value: $value)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'DialerEvent.sendDTMF'))
      ..add(DiagnosticsProperty('value', value));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SendDTMFValueImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SendDTMFValueImplCopyWith<_$SendDTMFValueImpl> get copyWith =>
      __$$SendDTMFValueImplCopyWithImpl<_$SendDTMFValueImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? sipUsername, String? sipPassword)
        register,
    required TResult Function() reset,
    required TResult Function() reloadAsterisk,
    required TResult Function(String phoneNumber) callStarted,
    required TResult Function() callStopped,
    required TResult Function() hangedup,
    required TResult Function(sip_ua.CallState event) handledCallStream,
    required TResult Function(DialerStatus status) updatedDialerStatus,
    required TResult Function(sip_ua.Call? call) updatedCall,
    required TResult Function(bool isOnHold, String? onHoldOriginator)
        updatedOnHoldStatus,
    required TResult Function() forceEndCurrentCall,
    required TResult Function(sip_ua.CallStateEnum callStateEnum)
        updatedCallStateEnum,
    required TResult Function(sip_ua.RegistrationStateEnum status)
        updatedRegistrationStatus,
    required TResult Function(String? message) sendErrorMessage,
    required TResult Function() timerStarted,
    required TResult Function(int value) timerPaused,
    required TResult Function() timerStopped,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() holdCall,
    required TResult Function() unholdCall,
    required TResult Function() switchToBluetoothHeadset,
    required TResult Function() switchToWiredHeadet,
    required TResult Function() disableSpeakerPhone,
    required TResult Function() switchSpeakerPhoneOn,
    required TResult Function() muteOn,
    required TResult Function() muteOff,
    required TResult Function() handledAllHeadsetStreams,
    required TResult Function(String value) sendDTMF,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
  }) {
    return sendDTMF(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? sipUsername, String? sipPassword)? register,
    TResult? Function()? reset,
    TResult? Function()? reloadAsterisk,
    TResult? Function(String phoneNumber)? callStarted,
    TResult? Function()? callStopped,
    TResult? Function()? hangedup,
    TResult? Function(sip_ua.CallState event)? handledCallStream,
    TResult? Function(DialerStatus status)? updatedDialerStatus,
    TResult? Function(sip_ua.Call? call)? updatedCall,
    TResult? Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult? Function()? forceEndCurrentCall,
    TResult? Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult? Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult? Function(String? message)? sendErrorMessage,
    TResult? Function()? timerStarted,
    TResult? Function(int value)? timerPaused,
    TResult? Function()? timerStopped,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? holdCall,
    TResult? Function()? unholdCall,
    TResult? Function()? switchToBluetoothHeadset,
    TResult? Function()? switchToWiredHeadet,
    TResult? Function()? disableSpeakerPhone,
    TResult? Function()? switchSpeakerPhoneOn,
    TResult? Function()? muteOn,
    TResult? Function()? muteOff,
    TResult? Function()? handledAllHeadsetStreams,
    TResult? Function(String value)? sendDTMF,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
  }) {
    return sendDTMF?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? sipUsername, String? sipPassword)? register,
    TResult Function()? reset,
    TResult Function()? reloadAsterisk,
    TResult Function(String phoneNumber)? callStarted,
    TResult Function()? callStopped,
    TResult Function()? hangedup,
    TResult Function(sip_ua.CallState event)? handledCallStream,
    TResult Function(DialerStatus status)? updatedDialerStatus,
    TResult Function(sip_ua.Call? call)? updatedCall,
    TResult Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult Function()? forceEndCurrentCall,
    TResult Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult Function(String? message)? sendErrorMessage,
    TResult Function()? timerStarted,
    TResult Function(int value)? timerPaused,
    TResult Function()? timerStopped,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? holdCall,
    TResult Function()? unholdCall,
    TResult Function()? switchToBluetoothHeadset,
    TResult Function()? switchToWiredHeadet,
    TResult Function()? disableSpeakerPhone,
    TResult Function()? switchSpeakerPhoneOn,
    TResult Function()? muteOn,
    TResult Function()? muteOff,
    TResult Function()? handledAllHeadsetStreams,
    TResult Function(String value)? sendDTMF,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (sendDTMF != null) {
      return sendDTMF(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) register,
    required TResult Function(_Initial value) reset,
    required TResult Function(_RegisteredAgain value) reloadAsterisk,
    required TResult Function(_CallStarted value) callStarted,
    required TResult Function(_CallStopped value) callStopped,
    required TResult Function(_HangedUp value) hangedup,
    required TResult Function(_HandledCallStream value) handledCallStream,
    required TResult Function(_UpdatedDialerStatus value) updatedDialerStatus,
    required TResult Function(_UpdatedCall value) updatedCall,
    required TResult Function(_UpdatedOnHoldStatus value) updatedOnHoldStatus,
    required TResult Function(_ForceEndCurrentCall value) forceEndCurrentCall,
    required TResult Function(_UpdatedCallStateEnum value) updatedCallStateEnum,
    required TResult Function(_UpdatedRegistrationStatus value)
        updatedRegistrationStatus,
    required TResult Function(_HandledMessageRequest value) sendErrorMessage,
    required TResult Function(_StartedTimer value) timerStarted,
    required TResult Function(_PausedTimer value) timerPaused,
    required TResult Function(_StoppedTimer value) timerStopped,
    required TResult Function(_UpdatedTimer value) timerUpdated,
    required TResult Function(_HoldCall value) holdCall,
    required TResult Function(_UnholdCall value) unholdCall,
    required TResult Function(_SwitchedToBluetooth value)
        switchToBluetoothHeadset,
    required TResult Function(_SwitchedToWiredHeadsetz value)
        switchToWiredHeadet,
    required TResult Function(_SwitchedSpeakerOff value) disableSpeakerPhone,
    required TResult Function(_SwitchSpeakerphoneOn value) switchSpeakerPhoneOn,
    required TResult Function(_MuteOn value) muteOn,
    required TResult Function(_MuteOff value) muteOff,
    required TResult Function(_HandledAllHeadsetStreams value)
        handledAllHeadsetStreams,
    required TResult Function(_SendDTMFValue value) sendDTMF,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
  }) {
    return sendDTMF(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? register,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_RegisteredAgain value)? reloadAsterisk,
    TResult? Function(_CallStarted value)? callStarted,
    TResult? Function(_CallStopped value)? callStopped,
    TResult? Function(_HangedUp value)? hangedup,
    TResult? Function(_HandledCallStream value)? handledCallStream,
    TResult? Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult? Function(_UpdatedCall value)? updatedCall,
    TResult? Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult? Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult? Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult? Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult? Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult? Function(_StartedTimer value)? timerStarted,
    TResult? Function(_PausedTimer value)? timerPaused,
    TResult? Function(_StoppedTimer value)? timerStopped,
    TResult? Function(_UpdatedTimer value)? timerUpdated,
    TResult? Function(_HoldCall value)? holdCall,
    TResult? Function(_UnholdCall value)? unholdCall,
    TResult? Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult? Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult? Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult? Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult? Function(_MuteOn value)? muteOn,
    TResult? Function(_MuteOff value)? muteOff,
    TResult? Function(_HandledAllHeadsetStreams value)?
        handledAllHeadsetStreams,
    TResult? Function(_SendDTMFValue value)? sendDTMF,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
  }) {
    return sendDTMF?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? register,
    TResult Function(_Initial value)? reset,
    TResult Function(_RegisteredAgain value)? reloadAsterisk,
    TResult Function(_CallStarted value)? callStarted,
    TResult Function(_CallStopped value)? callStopped,
    TResult Function(_HangedUp value)? hangedup,
    TResult Function(_HandledCallStream value)? handledCallStream,
    TResult Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult Function(_UpdatedCall value)? updatedCall,
    TResult Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult Function(_StartedTimer value)? timerStarted,
    TResult Function(_PausedTimer value)? timerPaused,
    TResult Function(_StoppedTimer value)? timerStopped,
    TResult Function(_UpdatedTimer value)? timerUpdated,
    TResult Function(_HoldCall value)? holdCall,
    TResult Function(_UnholdCall value)? unholdCall,
    TResult Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult Function(_MuteOn value)? muteOn,
    TResult Function(_MuteOff value)? muteOff,
    TResult Function(_HandledAllHeadsetStreams value)? handledAllHeadsetStreams,
    TResult Function(_SendDTMFValue value)? sendDTMF,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (sendDTMF != null) {
      return sendDTMF(this);
    }
    return orElse();
  }
}

abstract class _SendDTMFValue implements DialerEvent {
  const factory _SendDTMFValue({required final String value}) =
      _$SendDTMFValueImpl;

  String get value;

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SendDTMFValueImplCopyWith<_$SendDTMFValueImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AudioRoutingChangedImplCopyWith<$Res> {
  factory _$$AudioRoutingChangedImplCopyWith(_$AudioRoutingChangedImpl value,
          $Res Function(_$AudioRoutingChangedImpl) then) =
      __$$AudioRoutingChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AudioOutputMode mode});
}

/// @nodoc
class __$$AudioRoutingChangedImplCopyWithImpl<$Res>
    extends _$DialerEventCopyWithImpl<$Res, _$AudioRoutingChangedImpl>
    implements _$$AudioRoutingChangedImplCopyWith<$Res> {
  __$$AudioRoutingChangedImplCopyWithImpl(_$AudioRoutingChangedImpl _value,
      $Res Function(_$AudioRoutingChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mode = null,
  }) {
    return _then(_$AudioRoutingChangedImpl(
      mode: null == mode
          ? _value.mode
          : mode // ignore: cast_nullable_to_non_nullable
              as AudioOutputMode,
    ));
  }
}

/// @nodoc

class _$AudioRoutingChangedImpl
    with DiagnosticableTreeMixin
    implements _AudioRoutingChanged {
  const _$AudioRoutingChangedImpl({required this.mode});

  @override
  final AudioOutputMode mode;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DialerEvent.audioRoutingChanged(mode: $mode)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'DialerEvent.audioRoutingChanged'))
      ..add(DiagnosticsProperty('mode', mode));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AudioRoutingChangedImpl &&
            (identical(other.mode, mode) || other.mode == mode));
  }

  @override
  int get hashCode => Object.hash(runtimeType, mode);

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AudioRoutingChangedImplCopyWith<_$AudioRoutingChangedImpl> get copyWith =>
      __$$AudioRoutingChangedImplCopyWithImpl<_$AudioRoutingChangedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? sipUsername, String? sipPassword)
        register,
    required TResult Function() reset,
    required TResult Function() reloadAsterisk,
    required TResult Function(String phoneNumber) callStarted,
    required TResult Function() callStopped,
    required TResult Function() hangedup,
    required TResult Function(sip_ua.CallState event) handledCallStream,
    required TResult Function(DialerStatus status) updatedDialerStatus,
    required TResult Function(sip_ua.Call? call) updatedCall,
    required TResult Function(bool isOnHold, String? onHoldOriginator)
        updatedOnHoldStatus,
    required TResult Function() forceEndCurrentCall,
    required TResult Function(sip_ua.CallStateEnum callStateEnum)
        updatedCallStateEnum,
    required TResult Function(sip_ua.RegistrationStateEnum status)
        updatedRegistrationStatus,
    required TResult Function(String? message) sendErrorMessage,
    required TResult Function() timerStarted,
    required TResult Function(int value) timerPaused,
    required TResult Function() timerStopped,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() holdCall,
    required TResult Function() unholdCall,
    required TResult Function() switchToBluetoothHeadset,
    required TResult Function() switchToWiredHeadet,
    required TResult Function() disableSpeakerPhone,
    required TResult Function() switchSpeakerPhoneOn,
    required TResult Function() muteOn,
    required TResult Function() muteOff,
    required TResult Function() handledAllHeadsetStreams,
    required TResult Function(String value) sendDTMF,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
  }) {
    return audioRoutingChanged(mode);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? sipUsername, String? sipPassword)? register,
    TResult? Function()? reset,
    TResult? Function()? reloadAsterisk,
    TResult? Function(String phoneNumber)? callStarted,
    TResult? Function()? callStopped,
    TResult? Function()? hangedup,
    TResult? Function(sip_ua.CallState event)? handledCallStream,
    TResult? Function(DialerStatus status)? updatedDialerStatus,
    TResult? Function(sip_ua.Call? call)? updatedCall,
    TResult? Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult? Function()? forceEndCurrentCall,
    TResult? Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult? Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult? Function(String? message)? sendErrorMessage,
    TResult? Function()? timerStarted,
    TResult? Function(int value)? timerPaused,
    TResult? Function()? timerStopped,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? holdCall,
    TResult? Function()? unholdCall,
    TResult? Function()? switchToBluetoothHeadset,
    TResult? Function()? switchToWiredHeadet,
    TResult? Function()? disableSpeakerPhone,
    TResult? Function()? switchSpeakerPhoneOn,
    TResult? Function()? muteOn,
    TResult? Function()? muteOff,
    TResult? Function()? handledAllHeadsetStreams,
    TResult? Function(String value)? sendDTMF,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
  }) {
    return audioRoutingChanged?.call(mode);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? sipUsername, String? sipPassword)? register,
    TResult Function()? reset,
    TResult Function()? reloadAsterisk,
    TResult Function(String phoneNumber)? callStarted,
    TResult Function()? callStopped,
    TResult Function()? hangedup,
    TResult Function(sip_ua.CallState event)? handledCallStream,
    TResult Function(DialerStatus status)? updatedDialerStatus,
    TResult Function(sip_ua.Call? call)? updatedCall,
    TResult Function(bool isOnHold, String? onHoldOriginator)?
        updatedOnHoldStatus,
    TResult Function()? forceEndCurrentCall,
    TResult Function(sip_ua.CallStateEnum callStateEnum)? updatedCallStateEnum,
    TResult Function(sip_ua.RegistrationStateEnum status)?
        updatedRegistrationStatus,
    TResult Function(String? message)? sendErrorMessage,
    TResult Function()? timerStarted,
    TResult Function(int value)? timerPaused,
    TResult Function()? timerStopped,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? holdCall,
    TResult Function()? unholdCall,
    TResult Function()? switchToBluetoothHeadset,
    TResult Function()? switchToWiredHeadet,
    TResult Function()? disableSpeakerPhone,
    TResult Function()? switchSpeakerPhoneOn,
    TResult Function()? muteOn,
    TResult Function()? muteOff,
    TResult Function()? handledAllHeadsetStreams,
    TResult Function(String value)? sendDTMF,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (audioRoutingChanged != null) {
      return audioRoutingChanged(mode);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) register,
    required TResult Function(_Initial value) reset,
    required TResult Function(_RegisteredAgain value) reloadAsterisk,
    required TResult Function(_CallStarted value) callStarted,
    required TResult Function(_CallStopped value) callStopped,
    required TResult Function(_HangedUp value) hangedup,
    required TResult Function(_HandledCallStream value) handledCallStream,
    required TResult Function(_UpdatedDialerStatus value) updatedDialerStatus,
    required TResult Function(_UpdatedCall value) updatedCall,
    required TResult Function(_UpdatedOnHoldStatus value) updatedOnHoldStatus,
    required TResult Function(_ForceEndCurrentCall value) forceEndCurrentCall,
    required TResult Function(_UpdatedCallStateEnum value) updatedCallStateEnum,
    required TResult Function(_UpdatedRegistrationStatus value)
        updatedRegistrationStatus,
    required TResult Function(_HandledMessageRequest value) sendErrorMessage,
    required TResult Function(_StartedTimer value) timerStarted,
    required TResult Function(_PausedTimer value) timerPaused,
    required TResult Function(_StoppedTimer value) timerStopped,
    required TResult Function(_UpdatedTimer value) timerUpdated,
    required TResult Function(_HoldCall value) holdCall,
    required TResult Function(_UnholdCall value) unholdCall,
    required TResult Function(_SwitchedToBluetooth value)
        switchToBluetoothHeadset,
    required TResult Function(_SwitchedToWiredHeadsetz value)
        switchToWiredHeadet,
    required TResult Function(_SwitchedSpeakerOff value) disableSpeakerPhone,
    required TResult Function(_SwitchSpeakerphoneOn value) switchSpeakerPhoneOn,
    required TResult Function(_MuteOn value) muteOn,
    required TResult Function(_MuteOff value) muteOff,
    required TResult Function(_HandledAllHeadsetStreams value)
        handledAllHeadsetStreams,
    required TResult Function(_SendDTMFValue value) sendDTMF,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
  }) {
    return audioRoutingChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? register,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_RegisteredAgain value)? reloadAsterisk,
    TResult? Function(_CallStarted value)? callStarted,
    TResult? Function(_CallStopped value)? callStopped,
    TResult? Function(_HangedUp value)? hangedup,
    TResult? Function(_HandledCallStream value)? handledCallStream,
    TResult? Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult? Function(_UpdatedCall value)? updatedCall,
    TResult? Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult? Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult? Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult? Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult? Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult? Function(_StartedTimer value)? timerStarted,
    TResult? Function(_PausedTimer value)? timerPaused,
    TResult? Function(_StoppedTimer value)? timerStopped,
    TResult? Function(_UpdatedTimer value)? timerUpdated,
    TResult? Function(_HoldCall value)? holdCall,
    TResult? Function(_UnholdCall value)? unholdCall,
    TResult? Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult? Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult? Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult? Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult? Function(_MuteOn value)? muteOn,
    TResult? Function(_MuteOff value)? muteOff,
    TResult? Function(_HandledAllHeadsetStreams value)?
        handledAllHeadsetStreams,
    TResult? Function(_SendDTMFValue value)? sendDTMF,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
  }) {
    return audioRoutingChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? register,
    TResult Function(_Initial value)? reset,
    TResult Function(_RegisteredAgain value)? reloadAsterisk,
    TResult Function(_CallStarted value)? callStarted,
    TResult Function(_CallStopped value)? callStopped,
    TResult Function(_HangedUp value)? hangedup,
    TResult Function(_HandledCallStream value)? handledCallStream,
    TResult Function(_UpdatedDialerStatus value)? updatedDialerStatus,
    TResult Function(_UpdatedCall value)? updatedCall,
    TResult Function(_UpdatedOnHoldStatus value)? updatedOnHoldStatus,
    TResult Function(_ForceEndCurrentCall value)? forceEndCurrentCall,
    TResult Function(_UpdatedCallStateEnum value)? updatedCallStateEnum,
    TResult Function(_UpdatedRegistrationStatus value)?
        updatedRegistrationStatus,
    TResult Function(_HandledMessageRequest value)? sendErrorMessage,
    TResult Function(_StartedTimer value)? timerStarted,
    TResult Function(_PausedTimer value)? timerPaused,
    TResult Function(_StoppedTimer value)? timerStopped,
    TResult Function(_UpdatedTimer value)? timerUpdated,
    TResult Function(_HoldCall value)? holdCall,
    TResult Function(_UnholdCall value)? unholdCall,
    TResult Function(_SwitchedToBluetooth value)? switchToBluetoothHeadset,
    TResult Function(_SwitchedToWiredHeadsetz value)? switchToWiredHeadet,
    TResult Function(_SwitchedSpeakerOff value)? disableSpeakerPhone,
    TResult Function(_SwitchSpeakerphoneOn value)? switchSpeakerPhoneOn,
    TResult Function(_MuteOn value)? muteOn,
    TResult Function(_MuteOff value)? muteOff,
    TResult Function(_HandledAllHeadsetStreams value)? handledAllHeadsetStreams,
    TResult Function(_SendDTMFValue value)? sendDTMF,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    required TResult orElse(),
  }) {
    if (audioRoutingChanged != null) {
      return audioRoutingChanged(this);
    }
    return orElse();
  }
}

abstract class _AudioRoutingChanged implements DialerEvent {
  const factory _AudioRoutingChanged({required final AudioOutputMode mode}) =
      _$AudioRoutingChangedImpl;

  AudioOutputMode get mode;

  /// Create a copy of DialerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AudioRoutingChangedImplCopyWith<_$AudioRoutingChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$DialerState {
  sip_ua.Call? get call => throw _privateConstructorUsedError;
  String? get sipUsername => throw _privateConstructorUsedError;
  String? get sipPassword => throw _privateConstructorUsedError;
  String? get message => throw _privateConstructorUsedError;
  String? get phoneNumber => throw _privateConstructorUsedError;
  String? get countryCode => throw _privateConstructorUsedError;
  String? get holdOriginator => throw _privateConstructorUsedError;
  MediaStream? get localMediaStream => throw _privateConstructorUsedError;
  MediaStream? get remoteMediaStream => throw _privateConstructorUsedError;
  bool get isNumPadShowing => throw _privateConstructorUsedError;
  sip_ua.RegistrationStateEnum get sipRegistrationStatus =>
      throw _privateConstructorUsedError;
  Duration get elapsedCallTimer => throw _privateConstructorUsedError;
  CallAudioTracksMode get audioTrackMode => throw _privateConstructorUsedError;
  sip_ua.CallStateEnum get callStateEnum => throw _privateConstructorUsedError;
  DialerStatus get status => throw _privateConstructorUsedError;
  DialerMuteStatus get muteStatus => throw _privateConstructorUsedError;
  String get audioOutput => throw _privateConstructorUsedError;

  /// Create a copy of DialerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DialerStateCopyWith<DialerState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DialerStateCopyWith<$Res> {
  factory $DialerStateCopyWith(
          DialerState value, $Res Function(DialerState) then) =
      _$DialerStateCopyWithImpl<$Res, DialerState>;
  @useResult
  $Res call(
      {sip_ua.Call? call,
      String? sipUsername,
      String? sipPassword,
      String? message,
      String? phoneNumber,
      String? countryCode,
      String? holdOriginator,
      MediaStream? localMediaStream,
      MediaStream? remoteMediaStream,
      bool isNumPadShowing,
      sip_ua.RegistrationStateEnum sipRegistrationStatus,
      Duration elapsedCallTimer,
      CallAudioTracksMode audioTrackMode,
      sip_ua.CallStateEnum callStateEnum,
      DialerStatus status,
      DialerMuteStatus muteStatus,
      String audioOutput});
}

/// @nodoc
class _$DialerStateCopyWithImpl<$Res, $Val extends DialerState>
    implements $DialerStateCopyWith<$Res> {
  _$DialerStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DialerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? call = freezed,
    Object? sipUsername = freezed,
    Object? sipPassword = freezed,
    Object? message = freezed,
    Object? phoneNumber = freezed,
    Object? countryCode = freezed,
    Object? holdOriginator = freezed,
    Object? localMediaStream = freezed,
    Object? remoteMediaStream = freezed,
    Object? isNumPadShowing = null,
    Object? sipRegistrationStatus = null,
    Object? elapsedCallTimer = null,
    Object? audioTrackMode = null,
    Object? callStateEnum = null,
    Object? status = null,
    Object? muteStatus = null,
    Object? audioOutput = null,
  }) {
    return _then(_value.copyWith(
      call: freezed == call
          ? _value.call
          : call // ignore: cast_nullable_to_non_nullable
              as sip_ua.Call?,
      sipUsername: freezed == sipUsername
          ? _value.sipUsername
          : sipUsername // ignore: cast_nullable_to_non_nullable
              as String?,
      sipPassword: freezed == sipPassword
          ? _value.sipPassword
          : sipPassword // ignore: cast_nullable_to_non_nullable
              as String?,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      countryCode: freezed == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String?,
      holdOriginator: freezed == holdOriginator
          ? _value.holdOriginator
          : holdOriginator // ignore: cast_nullable_to_non_nullable
              as String?,
      localMediaStream: freezed == localMediaStream
          ? _value.localMediaStream
          : localMediaStream // ignore: cast_nullable_to_non_nullable
              as MediaStream?,
      remoteMediaStream: freezed == remoteMediaStream
          ? _value.remoteMediaStream
          : remoteMediaStream // ignore: cast_nullable_to_non_nullable
              as MediaStream?,
      isNumPadShowing: null == isNumPadShowing
          ? _value.isNumPadShowing
          : isNumPadShowing // ignore: cast_nullable_to_non_nullable
              as bool,
      sipRegistrationStatus: null == sipRegistrationStatus
          ? _value.sipRegistrationStatus
          : sipRegistrationStatus // ignore: cast_nullable_to_non_nullable
              as sip_ua.RegistrationStateEnum,
      elapsedCallTimer: null == elapsedCallTimer
          ? _value.elapsedCallTimer
          : elapsedCallTimer // ignore: cast_nullable_to_non_nullable
              as Duration,
      audioTrackMode: null == audioTrackMode
          ? _value.audioTrackMode
          : audioTrackMode // ignore: cast_nullable_to_non_nullable
              as CallAudioTracksMode,
      callStateEnum: null == callStateEnum
          ? _value.callStateEnum
          : callStateEnum // ignore: cast_nullable_to_non_nullable
              as sip_ua.CallStateEnum,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as DialerStatus,
      muteStatus: null == muteStatus
          ? _value.muteStatus
          : muteStatus // ignore: cast_nullable_to_non_nullable
              as DialerMuteStatus,
      audioOutput: null == audioOutput
          ? _value.audioOutput
          : audioOutput // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DialerStateImplCopyWith<$Res>
    implements $DialerStateCopyWith<$Res> {
  factory _$$DialerStateImplCopyWith(
          _$DialerStateImpl value, $Res Function(_$DialerStateImpl) then) =
      __$$DialerStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {sip_ua.Call? call,
      String? sipUsername,
      String? sipPassword,
      String? message,
      String? phoneNumber,
      String? countryCode,
      String? holdOriginator,
      MediaStream? localMediaStream,
      MediaStream? remoteMediaStream,
      bool isNumPadShowing,
      sip_ua.RegistrationStateEnum sipRegistrationStatus,
      Duration elapsedCallTimer,
      CallAudioTracksMode audioTrackMode,
      sip_ua.CallStateEnum callStateEnum,
      DialerStatus status,
      DialerMuteStatus muteStatus,
      String audioOutput});
}

/// @nodoc
class __$$DialerStateImplCopyWithImpl<$Res>
    extends _$DialerStateCopyWithImpl<$Res, _$DialerStateImpl>
    implements _$$DialerStateImplCopyWith<$Res> {
  __$$DialerStateImplCopyWithImpl(
      _$DialerStateImpl _value, $Res Function(_$DialerStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of DialerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? call = freezed,
    Object? sipUsername = freezed,
    Object? sipPassword = freezed,
    Object? message = freezed,
    Object? phoneNumber = freezed,
    Object? countryCode = freezed,
    Object? holdOriginator = freezed,
    Object? localMediaStream = freezed,
    Object? remoteMediaStream = freezed,
    Object? isNumPadShowing = null,
    Object? sipRegistrationStatus = null,
    Object? elapsedCallTimer = null,
    Object? audioTrackMode = null,
    Object? callStateEnum = null,
    Object? status = null,
    Object? muteStatus = null,
    Object? audioOutput = null,
  }) {
    return _then(_$DialerStateImpl(
      call: freezed == call
          ? _value.call
          : call // ignore: cast_nullable_to_non_nullable
              as sip_ua.Call?,
      sipUsername: freezed == sipUsername
          ? _value.sipUsername
          : sipUsername // ignore: cast_nullable_to_non_nullable
              as String?,
      sipPassword: freezed == sipPassword
          ? _value.sipPassword
          : sipPassword // ignore: cast_nullable_to_non_nullable
              as String?,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      countryCode: freezed == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String?,
      holdOriginator: freezed == holdOriginator
          ? _value.holdOriginator
          : holdOriginator // ignore: cast_nullable_to_non_nullable
              as String?,
      localMediaStream: freezed == localMediaStream
          ? _value.localMediaStream
          : localMediaStream // ignore: cast_nullable_to_non_nullable
              as MediaStream?,
      remoteMediaStream: freezed == remoteMediaStream
          ? _value.remoteMediaStream
          : remoteMediaStream // ignore: cast_nullable_to_non_nullable
              as MediaStream?,
      isNumPadShowing: null == isNumPadShowing
          ? _value.isNumPadShowing
          : isNumPadShowing // ignore: cast_nullable_to_non_nullable
              as bool,
      sipRegistrationStatus: null == sipRegistrationStatus
          ? _value.sipRegistrationStatus
          : sipRegistrationStatus // ignore: cast_nullable_to_non_nullable
              as sip_ua.RegistrationStateEnum,
      elapsedCallTimer: null == elapsedCallTimer
          ? _value.elapsedCallTimer
          : elapsedCallTimer // ignore: cast_nullable_to_non_nullable
              as Duration,
      audioTrackMode: null == audioTrackMode
          ? _value.audioTrackMode
          : audioTrackMode // ignore: cast_nullable_to_non_nullable
              as CallAudioTracksMode,
      callStateEnum: null == callStateEnum
          ? _value.callStateEnum
          : callStateEnum // ignore: cast_nullable_to_non_nullable
              as sip_ua.CallStateEnum,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as DialerStatus,
      muteStatus: null == muteStatus
          ? _value.muteStatus
          : muteStatus // ignore: cast_nullable_to_non_nullable
              as DialerMuteStatus,
      audioOutput: null == audioOutput
          ? _value.audioOutput
          : audioOutput // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DialerStateImpl extends _DialerState with DiagnosticableTreeMixin {
  _$DialerStateImpl(
      {this.call,
      this.sipUsername,
      this.sipPassword,
      this.message,
      this.phoneNumber,
      this.countryCode,
      this.holdOriginator,
      this.localMediaStream,
      this.remoteMediaStream,
      this.isNumPadShowing = false,
      this.sipRegistrationStatus = sip_ua.RegistrationStateEnum.NONE,
      this.elapsedCallTimer = Duration.zero,
      this.audioTrackMode = CallAudioTracksMode.phone,
      this.callStateEnum = sip_ua.CallStateEnum.NONE,
      this.status = DialerStatus.initial,
      this.muteStatus = DialerMuteStatus.unmuted,
      this.audioOutput = 'Unknown'})
      : super._();

  @override
  final sip_ua.Call? call;
  @override
  final String? sipUsername;
  @override
  final String? sipPassword;
  @override
  final String? message;
  @override
  final String? phoneNumber;
  @override
  final String? countryCode;
  @override
  final String? holdOriginator;
  @override
  final MediaStream? localMediaStream;
  @override
  final MediaStream? remoteMediaStream;
  @override
  @JsonKey()
  final bool isNumPadShowing;
  @override
  @JsonKey()
  final sip_ua.RegistrationStateEnum sipRegistrationStatus;
  @override
  @JsonKey()
  final Duration elapsedCallTimer;
  @override
  @JsonKey()
  final CallAudioTracksMode audioTrackMode;
  @override
  @JsonKey()
  final sip_ua.CallStateEnum callStateEnum;
  @override
  @JsonKey()
  final DialerStatus status;
  @override
  @JsonKey()
  final DialerMuteStatus muteStatus;
  @override
  @JsonKey()
  final String audioOutput;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'DialerState(call: $call, sipUsername: $sipUsername, sipPassword: $sipPassword, message: $message, phoneNumber: $phoneNumber, countryCode: $countryCode, holdOriginator: $holdOriginator, localMediaStream: $localMediaStream, remoteMediaStream: $remoteMediaStream, isNumPadShowing: $isNumPadShowing, sipRegistrationStatus: $sipRegistrationStatus, elapsedCallTimer: $elapsedCallTimer, audioTrackMode: $audioTrackMode, callStateEnum: $callStateEnum, status: $status, muteStatus: $muteStatus, audioOutput: $audioOutput)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'DialerState'))
      ..add(DiagnosticsProperty('call', call))
      ..add(DiagnosticsProperty('sipUsername', sipUsername))
      ..add(DiagnosticsProperty('sipPassword', sipPassword))
      ..add(DiagnosticsProperty('message', message))
      ..add(DiagnosticsProperty('phoneNumber', phoneNumber))
      ..add(DiagnosticsProperty('countryCode', countryCode))
      ..add(DiagnosticsProperty('holdOriginator', holdOriginator))
      ..add(DiagnosticsProperty('localMediaStream', localMediaStream))
      ..add(DiagnosticsProperty('remoteMediaStream', remoteMediaStream))
      ..add(DiagnosticsProperty('isNumPadShowing', isNumPadShowing))
      ..add(DiagnosticsProperty('sipRegistrationStatus', sipRegistrationStatus))
      ..add(DiagnosticsProperty('elapsedCallTimer', elapsedCallTimer))
      ..add(DiagnosticsProperty('audioTrackMode', audioTrackMode))
      ..add(DiagnosticsProperty('callStateEnum', callStateEnum))
      ..add(DiagnosticsProperty('status', status))
      ..add(DiagnosticsProperty('muteStatus', muteStatus))
      ..add(DiagnosticsProperty('audioOutput', audioOutput));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DialerStateImpl &&
            (identical(other.call, call) || other.call == call) &&
            (identical(other.sipUsername, sipUsername) ||
                other.sipUsername == sipUsername) &&
            (identical(other.sipPassword, sipPassword) ||
                other.sipPassword == sipPassword) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.countryCode, countryCode) ||
                other.countryCode == countryCode) &&
            (identical(other.holdOriginator, holdOriginator) ||
                other.holdOriginator == holdOriginator) &&
            (identical(other.localMediaStream, localMediaStream) ||
                other.localMediaStream == localMediaStream) &&
            (identical(other.remoteMediaStream, remoteMediaStream) ||
                other.remoteMediaStream == remoteMediaStream) &&
            (identical(other.isNumPadShowing, isNumPadShowing) ||
                other.isNumPadShowing == isNumPadShowing) &&
            (identical(other.sipRegistrationStatus, sipRegistrationStatus) ||
                other.sipRegistrationStatus == sipRegistrationStatus) &&
            (identical(other.elapsedCallTimer, elapsedCallTimer) ||
                other.elapsedCallTimer == elapsedCallTimer) &&
            (identical(other.audioTrackMode, audioTrackMode) ||
                other.audioTrackMode == audioTrackMode) &&
            (identical(other.callStateEnum, callStateEnum) ||
                other.callStateEnum == callStateEnum) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.muteStatus, muteStatus) ||
                other.muteStatus == muteStatus) &&
            (identical(other.audioOutput, audioOutput) ||
                other.audioOutput == audioOutput));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      call,
      sipUsername,
      sipPassword,
      message,
      phoneNumber,
      countryCode,
      holdOriginator,
      localMediaStream,
      remoteMediaStream,
      isNumPadShowing,
      sipRegistrationStatus,
      elapsedCallTimer,
      audioTrackMode,
      callStateEnum,
      status,
      muteStatus,
      audioOutput);

  /// Create a copy of DialerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DialerStateImplCopyWith<_$DialerStateImpl> get copyWith =>
      __$$DialerStateImplCopyWithImpl<_$DialerStateImpl>(this, _$identity);
}

abstract class _DialerState extends DialerState {
  factory _DialerState(
      {final sip_ua.Call? call,
      final String? sipUsername,
      final String? sipPassword,
      final String? message,
      final String? phoneNumber,
      final String? countryCode,
      final String? holdOriginator,
      final MediaStream? localMediaStream,
      final MediaStream? remoteMediaStream,
      final bool isNumPadShowing,
      final sip_ua.RegistrationStateEnum sipRegistrationStatus,
      final Duration elapsedCallTimer,
      final CallAudioTracksMode audioTrackMode,
      final sip_ua.CallStateEnum callStateEnum,
      final DialerStatus status,
      final DialerMuteStatus muteStatus,
      final String audioOutput}) = _$DialerStateImpl;
  _DialerState._() : super._();

  @override
  sip_ua.Call? get call;
  @override
  String? get sipUsername;
  @override
  String? get sipPassword;
  @override
  String? get message;
  @override
  String? get phoneNumber;
  @override
  String? get countryCode;
  @override
  String? get holdOriginator;
  @override
  MediaStream? get localMediaStream;
  @override
  MediaStream? get remoteMediaStream;
  @override
  bool get isNumPadShowing;
  @override
  sip_ua.RegistrationStateEnum get sipRegistrationStatus;
  @override
  Duration get elapsedCallTimer;
  @override
  CallAudioTracksMode get audioTrackMode;
  @override
  sip_ua.CallStateEnum get callStateEnum;
  @override
  DialerStatus get status;
  @override
  DialerMuteStatus get muteStatus;
  @override
  String get audioOutput;

  /// Create a copy of DialerState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DialerStateImplCopyWith<_$DialerStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
