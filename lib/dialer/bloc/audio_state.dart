part of 'audio_bloc.dart';

/// Audio state representing all audio-related information during a call
/// 
/// Contains speaker settings, mute status, audio routing information,
/// media streams, and error state for comprehensive audio management.
@freezed
class AudioState with _$AudioState {
  const factory AudioState({
    /// Current audio track mode (speaker, phone, bluetooth, wired headset)
    @Default(CallAudioTracksMode.phone)
        CallAudioTracksMode audioTrackMode,
    
    /// Current mute status of the microphone
    @Default(DialerMuteStatus.unmuted)
        DialerMuteStatus muteStatus,
    
    /// User-friendly name of the current audio output device
    @Default('Earpiece')
        String audioOutput,
    
    /// Local media stream from the user's microphone/camera
    MediaStream? localMediaStream,
    
    /// Remote media stream from the other party
    MediaStream? remoteMediaStream,
    
    /// Last error message if any audio operation failed
    String? lastError,
  }) = _AudioState;

  /// Creates an initial audio state with default values
  factory AudioState.initial() => const AudioState();

  const AudioState._();

  /// Whether speaker phone is currently enabled
  bool get isSpeakerEnabled =>
      audioTrackMode == CallAudioTracksMode.speaker;

  /// Whether speaker phone is currently disabled (earpiece mode)
  bool get isSpeakerDisabled =>
      audioTrackMode == CallAudioTracksMode.phone;

  /// Whether Bluetooth audio is currently enabled
  bool get isBluetoothEnabled =>
      audioTrackMode == CallAudioTracksMode.bluetooth;

  /// Whether wired headset is currently enabled
  bool get isWiredHeadsetEnabled =>
      audioTrackMode == CallAudioTracksMode.wiredHeadset;

  /// Whether microphone is currently muted
  bool get isMuted => muteStatus == DialerMuteStatus.muted;

  /// Whether microphone is currently unmuted
  bool get isUnmuted => muteStatus == DialerMuteStatus.unmuted;

  /// Whether there is an error in the current audio state
  bool get hasError => lastError != null;

  /// User-friendly name of the current audio track mode
  String get audioTrackModeName => audioTrackMode.name;

  /// Whether audio streams are available for the call
  bool get hasStreams =>
      localMediaStream != null || remoteMediaStream != null;

  /// Whether local audio stream is available
  bool get hasLocalStream => localMediaStream != null;

  /// Whether remote audio stream is available  
  bool get hasRemoteStream => remoteMediaStream != null;
}
