import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:froggytalk/dialer/dialer.dart';
import 'package:froggytalk/shared/services/audio_routing_manager.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:sip_ua/sip_ua.dart' as sip_ua;
import 'package:utils/utils.dart';

part 'dialer_bloc.freezed.dart';
part 'dialer_event.dart';
part 'dialer_state.dart';

class DialerBloc extends Bloc<DialerEvent, DialerState>
    implements sip_ua.SipUaHelperListener {
  /// Creates a new DialerBloc with required services and optional
  /// specialized blocs for delegation
  DialerBloc({
    required PhoneNumberService phoneNumberService,
    required sip_ua.SIPUAHelper sipHelper,
    required CallService callService,
    // Optional specialized blocs for delegation
    AudioBloc? audioBloc,
    RegistrationBloc? registrationBloc,
    CallTimerBloc? callTimerBloc,
  })  : _phoneNumberService = phoneNumberService,
        _sipHelper = sipHelper,
        _callService = callService,
        _audioBloc = audioBloc,
        _registrationBloc = registrationBloc,
        _callTimerBloc = callTimerBloc,
        super(DialerState.initial()) {
    on<_Started>(_onStarted);
    on<_RegisteredAgain>(_onReconnectReg);
    on<_CallStarted>(_onCallStarted);
    on<_CallStopped>(_onCallStopped);
    on<_HangedUp>(_onHangedUp);
    on<_UpdatedDialerStatus>(_onUpdatedDialerStatus);
    on<_UpdatedCall>(_onUpdatedCall);
    on<_UpdatedRegistrationStatus>(_onUpdatedRegistrationStatus);
    on<_StartedTimer>(_onStartedTimer);
    on<_UpdatedTimer>(_onUpdatedTimer);
    on<_PausedTimer>(_onPausedTimer);
    on<_StoppedTimer>(_onStoppedTimer);
    on<_SendDTMFValue>(_onSendDTMFValue);
    on<_UnholdCall>(_onUnholdCall);
    on<_HoldCall>(_onHoldCall);
    on<_HandledCallStream>(_onHandledCallStream);
    on<_UpdatedOnHoldStatus>(_onUpdatedOnHoldStatus);
    // on<_UpdatedMuteStatus>(_onUpdatedMuteStatus);
    on<_UpdatedCallStateEnum>(_onUpdatedCallStateEnum);
    on<_MuteOn>(_onMuteOn);
    on<_MuteOff>(_onMuteOff);
    on<_SwitchSpeakerphoneOn>(_onSwitchSpeakerphoneOn);
    on<_SwitchedSpeakerOff>(_onSwitchedSpeakerOff);
    on<_SwitchedToBluetooth>(_onSwitchedToBluetooth);
    on<_HandledAllHeadsetStreams>(_onHandledWiredHeadsetStreams);
    on<_SwitchedToWiredHeadsetz>(onHandledWiredHeadset);
    on<_HandledMessageRequest>(_onHandledMessageRequest);
    on<_Initial>(_onInitial);
    on<_AudioRoutingChanged>(_onAudioRoutingChanged);
    on<_ForceEndCurrentCall>(_onForceEndCurrentCall);

    _sipHelper.addSipUaHelperListener(this);

    // Start monitoring audio routing changes
    monitorAudioRouting();
  }

  final CallService _callService;
  final CountUpTimer _countupTimer = CountUpTimer();
  final PhoneNumberService _phoneNumberService;
  final sip_ua.SIPUAHelper _sipHelper;
  final InAppReview inAppReview = InAppReview.instance;
  final AudioRoutingManager _audioRoutingManager = AudioRoutingManager();

  // Optional specialized blocs for delegation
  final AudioBloc? _audioBloc;
  final RegistrationBloc? _registrationBloc;
  final CallTimerBloc? _callTimerBloc;

  @override
  void callStateChanged(sip_ua.Call call, sip_ua.CallState callState) {
    FroggyLogger.debug(
      'DialerBloc: Call state changed to ${callState.state} '
      'for call ${call.id}',
    );

    // CRITICAL FIX: Only process state changes for the current
    // active call
    // This prevents state pollution from multiple calls
    if (state.call?.id != null && state.call!.id != call.id) {
      FroggyLogger.debug(
        'DialerBloc: Ignoring state change for different call: '
        '${call.id} (current active call: ${state.call!.id})',
      );
      return;
    }

    // If we don't have an active call yet, only respond to
    // CALL_INITIATION to establish the first call
    if (state.call == null &&
        callState.state != sip_ua.CallStateEnum.CALL_INITIATION) {
      FroggyLogger.debug(
        'DialerBloc: No active call set, ignoring state '
        '${callState.state} for call ${call.id}',
      );
      return;
    }

    // Update the call object in state when we start tracking a new call
    if (state.call == null &&
        callState.state == sip_ua.CallStateEnum.CALL_INITIATION) {
      FroggyLogger.debug(
        'DialerBloc: Setting active call to ${call.id}',
      );
      add(DialerEvent.updatedCall(call: call));
    }

    // Update call state enum in the bloc
    add(
      DialerEvent.updatedCallStateEnum(
        callStateEnum: callState.state,
      ),
    );

    // Handle specific call states
    switch (callState.state) {
      case sip_ua.CallStateEnum.CALL_INITIATION:
        add(
          const DialerEvent.updatedDialerStatus(
            status: DialerStatus.initial,
          ),
        );
      case sip_ua.CallStateEnum.CONNECTING:
        add(
          const DialerEvent.updatedDialerStatus(
            status: DialerStatus.connecting,
          ),
        );
      case sip_ua.CallStateEnum.PROGRESS:
        add(
          const DialerEvent.updatedDialerStatus(
            status: DialerStatus.ringing,
          ),
        );
      case sip_ua.CallStateEnum.ACCEPTED:
      case sip_ua.CallStateEnum.CONFIRMED:
        add(
          const DialerEvent.updatedDialerStatus(
            status: DialerStatus.connected,
          ),
        );
        // Start timer when call is confirmed/accepted
        add(const DialerEvent.timerStarted());
      case sip_ua.CallStateEnum.HOLD:
        add(const DialerEvent.updatedOnHoldStatus(isOnHold: true));
      case sip_ua.CallStateEnum.UNHOLD:
        add(const DialerEvent.updatedOnHoldStatus(isOnHold: false));
      case sip_ua.CallStateEnum.MUTED:
        if (callState.audio ?? false) {
          add(const DialerEvent.muteOn());
        }
      case sip_ua.CallStateEnum.UNMUTED:
        if (callState.audio ?? false) {
          add(const DialerEvent.muteOff());
        }
      case sip_ua.CallStateEnum.ENDED:
        add(
          const DialerEvent.updatedDialerStatus(
            status: DialerStatus.ended,
          ),
        );
        add(const DialerEvent.timerStopped());
        // Clear the active call when it ends
        add(const DialerEvent.updatedCall());
      case sip_ua.CallStateEnum.FAILED:
        add(
          const DialerEvent.updatedDialerStatus(
            status: DialerStatus.failedToConnect,
          ),
        );
        add(const DialerEvent.timerStopped());
        // Clear the active call when it fails
        add(const DialerEvent.updatedCall());
      case sip_ua.CallStateEnum.STREAM:
        add(DialerEvent.handledCallStream(event: callState));
      // ignore: no_default_cases
      default:
        FroggyLogger.debug(
          'DialerBloc: Unhandled call state: ${callState.state}',
        );
    }
  }

  @override
  Future<void> close() {
    _sipHelper.removeSipUaHelperListener(this);
    return super.close();
  }

  @override
  void onNewMessage(sip_ua.SIPMessageRequest msg) {}

  @override
  void onNewNotify(sip_ua.Notify ntf) {
    FroggyLogger.debug(
      'Froggy Sip Notify: : ${ntf.request}',
    );
  }

  @override
  void onNewReinvite(sip_ua.ReInvite event) {
    FroggyLogger.debug(
      'Froggy Sip ReInvite: : ${event.hasAudio}',
    );

    // CRITICAL FIX: Accept the re-invite with proper media constraints
    try {
      _callService.safeReinvite(call: state.call!);
    } catch (e) {
      FroggyLogger.error('Error handling re-invite: $e');
    }
  }

  @override
  void registrationStateChanged(sip_ua.RegistrationState state) {
    FroggyLogger.debug(
      'Froggy Sip Registration State: ${state.state} '
      'and reason ${state.cause}',
    );

    // Delegate to RegistrationBloc if available
    if (_registrationBloc != null) {
      _registrationBloc.add(
        RegistrationEvent.registrationStatusUpdated(
          status: state.state ?? sip_ua.RegistrationStateEnum.NONE,
          cause: state.cause?.toString(),
        ),
      );
    }
  }

  @override
  void transportStateChanged(sip_ua.TransportState state) {
    FroggyLogger.debug(
      'Froggy Sip TransportState: ${state.state} '
      'and reason ${state.cause}',
    );
  }

  FutureOr<void> onHandledWiredHeadset(
    // ignore: library_private_types_in_public_api
    _SwitchedToWiredHeadsetz event,
    Emitter<DialerState> emit,
  ) {
    emit(
      state.copyWith(
        audioTrackMode: CallAudioTracksMode.wiredHeadset,
      ),
    );
  }

  FutureOr<void> _onStarted(
    _Started event,
    Emitter<DialerState> emit,
  ) async {
    final rawUsername = (state.sipUsername ?? event.sipUsername) ?? '';
    final username = _normalizeUsername(rawUsername);
    final password = (state.sipPassword ?? event.sipPassword) ?? '';

    if (username.isNotEmpty && password.isNotEmpty) {
      if (state.sipPassword == null || state.sipUsername == null) {
        emit(
          state.copyWith(
            sipUsername: username,
            sipPassword: event.sipPassword,
          ),
        );
      }

      // Delegate to RegistrationBloc if available
      if (_registrationBloc != null) {
        _registrationBloc.add(
          RegistrationEvent.registerRequested(
            username: username,
            password: password,
          ),
        );
      } else {
        // Fallback to original logic
        emit(
          state.copyWith(
            sipRegistrationStatus: sip_ua.RegistrationStateEnum.UNREGISTERED,
          ),
        );
        await _callService.register(
          username: username,
          password: password,
        );
      }
    } else {
      emit(
        state.copyWith(
          sipRegistrationStatus:
              sip_ua.RegistrationStateEnum.REGISTRATION_FAILED,
        ),
      );
    }
  }

  FutureOr<void> _onCallStarted(
    _CallStarted event,
    Emitter<DialerState> emit,
  ) async {
    if (!_sipHelper.registered) {
      add(
        const DialerEvent.sendErrorMessage(
          message: 'Not registered to SIP server',
        ),
      );
      return;
    }

    // Check WebRTC initialization
    try {
      var phoneNumber = event.phoneNumber.removeEmptySpaces();
      phoneNumber = phoneNumber.startsWith('00')
          ? phoneNumber.replaceFirst('00', '+')
          : phoneNumber;

      add(const DialerEvent.handledAllHeadsetStreams());

      if (phoneNumber.isEmpty) {
        add(
          const DialerEvent.updatedDialerStatus(
            status: DialerStatus.error,
          ),
        );

        add(
          const DialerEvent.sendErrorMessage(
            message: 'Please Insert A Phone Number',
          ),
        );
        return;
      }

      try {
        _callService.toggleSpeaker(enabled: false);
      } catch (e) {
        FroggyLogger.error('Error toggling speaker: $e');
      }

      // Init and make the call using CallService
      try {
        await _callService.makeCall(number: phoneNumber);
      } catch (e) {
        FroggyLogger.error('Error making call: $e');
        add(
          const DialerEvent.updatedDialerStatus(
            status: DialerStatus.error,
          ),
        );
        return;
      }

      emit(
        state.copyWith(
          phoneNumber: phoneNumber,
          countryCode: _phoneNumberService.getCountryCode(phoneNumber),
          status: DialerStatus.initial,
        ),
      );

      add(const DialerEvent.disableSpeakerPhone());
    } catch (e) {
      FroggyLogger.error('Error initializing call: $e');
      add(
        const DialerEvent.updatedDialerStatus(
          status: DialerStatus.error,
        ),
      );
    }
  }

  FutureOr<void> _onCallStopped(
    _CallStopped event,
    Emitter<DialerState> emit,
  ) async {
    try {
      // _callService.cleanUp();
      // _callService.deactivate();
      // await WakelockPlus.enable();
      emit(
        state.copyWith(
          status: DialerStatus.initial,
        ),
      );
    } catch (e) {
      FroggyLogger.error('Error stopping call: $e');
    }
  }

  Future<void> _onHangedUp(
    _HangedUp event,
    Emitter<DialerState> emit,
  ) async {
    try {
      FroggyLogger.debug(
        '[DialerBloc] Hangup requested - current call state: '
        '${state.call?.id}, established: '
        '${state.call?.session.isEstablished()}',
      );

      // await WakelockPlus.disable();

      // Perform actual SIP hangup operation with safety checks
      if (state.call != null) {
        try {
          final callSession = state.call!.session;
          FroggyLogger.debug(
            '[DialerBloc] Call session exists, is established: '
            '${callSession.isEstablished()}',
          );

          if (callSession.isEstablished()) {
            FroggyLogger.debug(
              '[DialerBloc] Hanging up call: ${state.call!.id}',
            );
            state.call!.hangup();
            FroggyLogger.debug(
              '[DialerBloc] Hangup call completed for: '
              '${state.call!.id}',
            );
          } else {
            FroggyLogger.debug(
              '[DialerBloc] Call session not established, '
              'terminating session directly',
            );
            // If session is not established but exists, terminate it
            try {
              callSession.terminate();
              FroggyLogger.debug(
                '[DialerBloc] Session terminated successfully',
              );
            } catch (terminateError) {
              FroggyLogger.error(
                '[DialerBloc] Error terminating session: '
                '$terminateError',
              );
            }
          }
        } catch (sipError) {
          FroggyLogger.error('[DialerBloc] Error during SIP hangup: $sipError');
          // Continue with cleanup even if SIP hangup fails
        }
      } else {
        FroggyLogger.warning(
            '[DialerBloc] No call object available for hangup',);
      }

      try {
        _callService.toggleSpeaker(enabled: true);
      } catch (e) {
        FroggyLogger.error('Error toggling speaker: $e');
      }

      // Update dialer status to ended first, then clear call
      FroggyLogger.debug('[DialerBloc] Setting dialer status to ended');
      emit(
        state.copyWith(
          status: DialerStatus.ended,
          call: null, // Clear the call object immediately
        ),
      );

      add(const DialerEvent.timerStopped());

      FroggyLogger.debug('[DialerBloc] Hangup process completed successfully');

      if (await inAppReview.isAvailable()) {
        await inAppReview.requestReview();
      }
    } catch (e) {
      FroggyLogger.error('Error occurred while hanging up call: $e');
      // Ensure state is updated even if hangup fails
      emit(
        state.copyWith(
          status: DialerStatus.ended,
          call: null, // Clear the call object even on error
        ),
      );
      add(const DialerEvent.timerStopped());
    }
  }

  FutureOr<void> _onUpdatedOnHoldStatus(
    _UpdatedOnHoldStatus event,
    Emitter<DialerState> emit,
  ) {
    if (event.isOnHold) {
      add(
        const DialerEvent.updatedDialerStatus(
          status: DialerStatus.hold,
        ),
      );
    } else {
      add(
        const DialerEvent.updatedDialerStatus(
          status: DialerStatus.connected,
        ),
      );
    }
  }

  FutureOr<void> _onUpdatedCallStateEnum(
    _UpdatedCallStateEnum event,
    Emitter<DialerState> emit,
  ) {
    emit(
      state.copyWith(
        callStateEnum: event.callStateEnum,
      ),
    );
  }

  FutureOr<void> _onUpdatedDialerStatus(
    _UpdatedDialerStatus event,
    Emitter<DialerState> emit,
  ) {
    emit(
      state.copyWith(
        status: event.status,
      ),
    );
  }

  FutureOr<void> _onUpdatedCall(
    _UpdatedCall event,
    Emitter<DialerState> emit,
  ) async {
    try {
      FroggyLogger.debug(
        '[DialerBloc] Updating call: ${event.call?.id ?? 'null'}',
      );
      emit(
        state.copyWith(
          call: event.call,
        ),
      );
    } catch (e) {
      FroggyLogger.error('[DialerBloc] Error updating call: $e');
    }
  }

  FutureOr<void> _onUpdatedRegistrationStatus(
    _UpdatedRegistrationStatus event,
    Emitter<DialerState> emit,
  ) async {
    final status = event.status;
    emit(
      state.copyWith(
        sipRegistrationStatus: status,
      ),
    );
  }

  FutureOr<void> _onStartedTimer(
    _StartedTimer event,
    Emitter<DialerState> emit,
  ) async {
    // Delegate to CallTimerBloc if available
    if (_callTimerBloc != null) {
      _callTimerBloc.add(const CallTimerEvent.timerStarted());
    } else {
      // Fallback to original logic
      _countupTimer.restart();
      await _countupTimer.timeStream.forEach((remaining) {
        add(DialerEvent.timerUpdated(elapsed: remaining));
      });
    }
  }

  FutureOr<void> _onPausedTimer(
    _PausedTimer event,
    Emitter<DialerState> emit,
  ) {
    // Delegate to CallTimerBloc if available
    if (_callTimerBloc != null) {
      _callTimerBloc.add(const CallTimerEvent.timerPaused());
    } else {
      // Fallback to original logic
      _countupTimer.pause();
    }
  }

  FutureOr<void> _onStoppedTimer(
    _StoppedTimer event,
    Emitter<DialerState> emit,
  ) {
    // Delegate to CallTimerBloc if available
    if (_callTimerBloc != null) {
      _callTimerBloc.add(const CallTimerEvent.timerStopped());
    } else {
      // Fallback to original logic
      _countupTimer.stop();
    }
  }

  FutureOr<void> _onUpdatedTimer(
    _UpdatedTimer event,
    Emitter<DialerState> emit,
  ) {
    emit(
      state.copyWith(
        elapsedCallTimer: event.elapsed,
      ),
    );
  }

  FutureOr<void> _onSwitchSpeakerphoneOn(
    _SwitchSpeakerphoneOn event,
    Emitter<DialerState> emit,
  ) {
    if (!kIsWeb) {
      // Delegate to AudioBloc if available
      if (_audioBloc != null) {
        _audioBloc.add(const AudioEvent.speakerEnabled());
      } else {
        // Fallback to original logic
        _callService.toggleSpeaker(enabled: true);
      }

      emit(
        state.copyWith(
          audioTrackMode: CallAudioTracksMode.speaker,
        ),
      );
    }
  }

  FutureOr<void> _onSwitchedSpeakerOff(
    _SwitchedSpeakerOff event,
    Emitter<DialerState> emit,
  ) {
    if (!kIsWeb) {
      // Delegate to AudioBloc if available
      if (_audioBloc != null) {
        _audioBloc.add(const AudioEvent.speakerDisabled());
      } else {
        // Fallback to original logic
        _callService.toggleSpeaker(enabled: false);
      }

      emit(
        state.copyWith(
          audioTrackMode: CallAudioTracksMode.phone,
        ),
      );
    }
  }

  FutureOr<void> _onSwitchedToBluetooth(
    _SwitchedToBluetooth event,
    Emitter<DialerState> emit,
  ) {
    Helper.setSpeakerphoneOnButPreferBluetooth();

    emit(
      state.copyWith(
        audioTrackMode: CallAudioTracksMode.bluetooth,
      ),
    );
  }

  FutureOr<void> _onSendDTMFValue(
    _SendDTMFValue event,
    Emitter<DialerState> emit,
  ) {
    try {
      _callService.sendDTMF(event.value);
    } catch (e) {
      FroggyLogger.error('Error sending DTMF Tones: $e');
    }
  }

  FutureOr<void> _onUnholdCall(
    _UnholdCall event,
    Emitter<DialerState> emit,
  ) {
    if (state.isCallConnected && state.call != null) {
      try {
        FroggyLogger.debug('[DialerBloc] Unholding call: ${state.call!.id}');
        // state.call!.unhold();
        _callService.safeUnhold(state.call!);
        add(const DialerEvent.updatedOnHoldStatus(isOnHold: false));
      } catch (e) {
        FroggyLogger.error('Error unholding call: $e');
        // Still update the state even if SIP unhold fails
        add(const DialerEvent.updatedOnHoldStatus(isOnHold: false));
      }
    } else {
      FroggyLogger.warning(
        '[DialerBloc] Cannot unhold: call not connected or call '
        'object is null',
      );
    }
  }

  FutureOr<void> _onHoldCall(
    _HoldCall event,
    Emitter<DialerState> emit,
  ) {
    if (state.isCallConnected && state.call != null) {
      try {
        FroggyLogger.debug('[DialerBloc] Holding call: ${state.call!.id}');
        // state.call!.hold();
        _callService.safeHold(state.call!);
        add(const DialerEvent.updatedOnHoldStatus(isOnHold: true));
      } catch (e) {
        FroggyLogger.error('Error holding call: $e');
        // Still update the state even if SIP hold fails
        add(const DialerEvent.updatedOnHoldStatus(isOnHold: true));
      }
    } else {
      FroggyLogger.warning(
        '[DialerBloc] Cannot hold: call not connected or call object is null',
      );
    }
  }

  FutureOr<void> _onHandledCallStream(
    _HandledCallStream event,
    Emitter<DialerState> emit,
  ) async {
    try {
      final stream = event.event.stream;
      if (stream == null) return;

      if (event.event.originator == 'local') {
        if (!kIsWeb && !WebRTC.platformIsDesktop) {
          await Future.forEach(stream.getAudioTracks(),
              (MediaStreamTrack track) {
            track.enableSpeakerphone(false);
          });
        }

        emit(
          state.copyWith(
            localMediaStream: stream,
            audioTrackMode: CallAudioTracksMode.phone,
          ),
        );
      }

      if (event.event.originator == 'remote') {
        emit(
          state.copyWith(
            remoteMediaStream: stream,
          ),
        );
      }
    } catch (e) {
      FroggyLogger.error('Error handling call stream: $e');
      add(const DialerEvent.updatedDialerStatus(status: DialerStatus.error));
    }
  }

  FutureOr<void> _onHandledWiredHeadsetStreams(
    _HandledAllHeadsetStreams event,
    Emitter<DialerState> emit,
  ) async {
    // FlutterHeadsetListener flutterHeadsetListener =
    //     FlutterHeadsetListener();
    // await flutterHeadsetListener.requestPermission();

    // final btHeadsetIsConnect =
    //     await flutterHeadsetListener.getBTHeadsetIsConnected();

    // if (btHeadsetIsConnect) {
    //   add(const DialerEvent.switchToBluetoothHeadset());
    // }

    // flutterHeadsetListener.headsetStateStream.listen((event) {
    //   debugPrint(event.toString());
    //   if (event == HeadsetState.btConnected) {
    //     add(const DialerEvent.switchToBluetoothHeadset());
    //   }

    //   if (event == HeadsetState.btDisconnected) {
    //     add(const DialerEvent.disableSpeakerPhone());
    //   }

    //   if (event == HeadsetState.plugged) {
    //     add(const DialerEvent.switchToWiredHeadet());
    //   }

    //   if (event == HeadsetState.unPlugged) {
    //     add(const DialerEvent.disableSpeakerPhone());
    //   }
    // });
  }

  FutureOr<void> _onReconnectReg(
    _RegisteredAgain event,
    Emitter<DialerState> emit,
  ) {
    try {
      _sipHelper.register();
      // _sipHelper.renegotiate();
      // add(const DialerEvent.register());
    } catch (e) {
      FroggyLogger.error('Error reconnecting to SIP server: $e');
    }
  }

  FutureOr<void> _onHandledMessageRequest(
    _HandledMessageRequest event,
    Emitter<DialerState> emit,
  ) {
    emit(
      state.copyWith(
        message: event.message ?? '',
      ),
    );
  }

  FutureOr<void> _onMuteOn(_MuteOn event, Emitter<DialerState> emit) {
    if (state.isCallConnected && state.call != null) {
      try {
        FroggyLogger.debug('[DialerBloc] Muting call: ${state.call!.id}');
        state.call!.mute();

        emit(
          state.copyWith(
            muteStatus: DialerMuteStatus.muted,
          ),
        );

        // Delegate to AudioBloc if available
        if (_audioBloc != null) {
          _audioBloc.add(const AudioEvent.muteEnabled());
        }
      } catch (e) {
        FroggyLogger.error('[DialerBloc] Error muting call: $e');
        // Still update the UI state even if SIP mute fails
        emit(
          state.copyWith(
            muteStatus: DialerMuteStatus.muted,
          ),
        );
      }
    } else {
      FroggyLogger.warning(
        '[DialerBloc] Cannot mute: call not connected or call object is null',
      );
    }
  }

  FutureOr<void> _onMuteOff(_MuteOff event, Emitter<DialerState> emit) {
    if (state.isCallConnected && state.call != null) {
      try {
        FroggyLogger.debug('[DialerBloc] Unmuting call: ${state.call!.id}');
        state.call!.unmute();

        emit(
          state.copyWith(
            muteStatus: DialerMuteStatus.unmuted,
          ),
        );

        // Delegate to AudioBloc if available
        if (_audioBloc != null) {
          _audioBloc.add(const AudioEvent.muteDisabled());
        }
      } catch (e) {
        FroggyLogger.error('[DialerBloc] Error unmuting call: $e');
        // Still update the UI state even if SIP unmute fails
        emit(
          state.copyWith(
            muteStatus: DialerMuteStatus.unmuted,
          ),
        );
      }
    } else {
      FroggyLogger.warning(
        '[DialerBloc] Cannot unmute: call not connected or call '
        'object is null',
      );
    }
  }

  FutureOr<void> _onInitial(_Initial event, Emitter<DialerState> emit) {
    emit(
      state.copyWith(
        sipRegistrationStatus: sip_ua.RegistrationStateEnum.NONE,
        sipUsername: null,
        sipPassword: null,
        phoneNumber: null,
        status: DialerStatus.initial,
      ),
    );
  }

  void _onAudioRoutingChanged(
    _AudioRoutingChanged event,
    Emitter<DialerState> emit,
  ) {
    switch (event.mode) {
      case AudioOutputMode.bluetooth:
        emit(state.copyWith(audioOutput: 'Bluetooth'));
      case AudioOutputMode.wiredHeadset:
        emit(state.copyWith(audioOutput: 'Wired Headset'));
      case AudioOutputMode.speaker:
        emit(state.copyWith(audioOutput: 'Speakerphone'));
      case AudioOutputMode.earpiece:
        emit(state.copyWith(audioOutput: 'Earpiece'));
    }
  }

  /// Normalizes username format for SIP registration
  /// Handles international prefixes consistently
  String _normalizeUsername(String username) {
    // Remove any leading/trailing whitespace
    final trimmed = username.trim();

    // Convert '00' prefix to proper format (remove 00, don't add +)
    if (trimmed.startsWith('00')) {
      return trimmed.substring(2); // Remove '00' prefix
    }

    // Remove '+' prefix for SIP registration
    if (trimmed.startsWith('+')) {
      return trimmed.substring(1); // Remove '+' prefix
    }

    return trimmed;
  }

  void monitorAudioRouting() {
    _callService.activateAudioRouting();
    _audioRoutingManager.routingChangeStream.listen((mode) {
      add(DialerEvent.audioRoutingChanged(mode: mode));
    });
  }

  /// Handles the forced call termination event,
  /// ensuring all SIP resources are cleaned up
  /// This is a more aggressive version of hangup that will attempt to
  /// clean up everything related to the call, even if some operations
  /// fail along the way
  Future<void> _onForceEndCurrentCall(
    DialerEvent event,
    Emitter<DialerState> emit,
  ) async {
    FroggyLogger.debug(
      '[DialerBloc] Force end current call requested - performing '
      'complete cleanup',
    );

    // 1. Try standard hangup first (with catch to continue even if
    // fails)
    try {
      if (state.call != null) {
        FroggyLogger.debug('[DialerBloc] Force ending call: ${state.call!.id}');

        try {
          final callSession = state.call!.session;

          // Try hangup if established
          if (callSession.isEstablished()) {
            try {
              state.call!.hangup();
              FroggyLogger.debug(
                '[DialerBloc] Hangup successful for: ${state.call!.id}',
              );
            } catch (e) {
              FroggyLogger.warning(
                '[DialerBloc] Standard hangup failed, trying direct '
                'termination: $e',
              );
              callSession.terminate();
            }
          } else {
            // If not established, terminate directly
            callSession.terminate();
            FroggyLogger.debug(
              '[DialerBloc] Session terminated for non-established call',
            );
          }
        } catch (e) {
          FroggyLogger.error('[DialerBloc] Error during call termination: $e');
          // Continue with cleanup regardless
        }
      }
    } catch (e) {
      FroggyLogger.error('[DialerBloc] Error in force end call operation: $e');
      // Continue with state cleanup regardless
    }

    // 2. Kill any active timers
    try {
      _countupTimer.stop();
      if (_callTimerBloc != null) {
        _callTimerBloc.add(const CallTimerEvent.timerReset());
      }
    } catch (e) {
      FroggyLogger.error('[DialerBloc] Error stopping timers: $e');
    }

    // 3. Reset audio routing and wakelock
    try {
      // await WakelockPlus.disable();
      _callService.toggleSpeaker(enabled: true); // Reset to speaker
    } catch (e) {
      FroggyLogger.error('[DialerBloc] Error resetting audio/wakelock: $e');
    }

    // 4. Reset the DialerBloc state completely
    emit(
      state.copyWith(
        call: null,
        status: DialerStatus.ended,
        elapsedCallTimer: Duration.zero,
        muteStatus: DialerMuteStatus.unmuted,
        audioTrackMode: CallAudioTracksMode.phone,
      ),
    );

    // 5. Log completion of cleanup
    FroggyLogger.debug('[DialerBloc] Force end call cleanup completed');
  }
}
