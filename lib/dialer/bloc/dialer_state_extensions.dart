import 'package:flutter/widgets.dart';
import 'package:froggytalk/dialer/bloc/dialer_bloc.dart';
import 'package:froggytalk/dialer/data/models/call_audio_tracks_mode.dart';
import 'package:froggytalk/dialer/data/models/dialer_status.dart';

/// Extensions for [DialerState] to provide computed properties
/// and optimized state access for UI components.
/// 
/// These computed properties help reduce widget rebuilds by providing
/// specific state selections that components can consume granularly.
extension DialerStateComputedProperties on DialerState {
  /// Computed properties for call status display optimization
  String get displayCallStatus {
    if (status.isConnecting) return 'Connecting...';
    if (status.isRinging) return 'Ringing...';
    if (status.isConnected) return 'Connected';
    if (status.isOnHold) return 'On Hold';
    if (status.isEnded) return 'Call Ended';
    if (status.isFailedToConnect) return 'Failed to Connect';
    return 'Initializing...';
  }

  /// Audio state summary for button components
  AudioStateInfo get audioStateInfo => AudioStateInfo(
        isMuted: muteStatus.isMuted,
        isSpeakerEnabled: audioTrackMode.isSpeakerEnabled,
        isBluetoothEnabled: audioTrackMode.isBluetoothAudioEnabled,
        audioOutput: audioOutput,
      );

  /// Call connection state summary
  CallConnectionInfo get connectionInfo => CallConnectionInfo(
        isConnected: status.isConnected,
        isConnecting: status.isConnecting,
        isRinging: status.isRinging,
        canHangup: status.isConnected ||
            status.isConnecting ||
            status.isRinging,
        duration: elapsedCallTimer,
        formattedDuration: formattedCallTimer,
      );

  /// Registration state summary for dialer button
  RegistrationInfo get registrationInfo => RegistrationInfo(
        isRegistered: isSipRegistered,
        isFailed: isSipRegisterFailed,
        isPending: isSipRegistrationPending,
        isUnregistered: isSipUnregistered,
        canMakeCall: isSipRegistered,
      );

  /// Call display information
  CallDisplayInfo get displayInfo => CallDisplayInfo(
        phoneNumber: phoneNumber,
        countryCode: countryCode,
        displayStatus: displayCallStatus,
        muteStatusText: (context) =>
            muteStatus.getLocalizedMessage(context as BuildContext),
      );

  /// Media stream state
  MediaStreamInfo get mediaInfo => MediaStreamInfo(
        hasLocalStream: localMediaStream != null,
        hasRemoteStream: remoteMediaStream != null,
        isVoiceOnly: call?.voiceOnly ?? true,
        remoteHasVideo: call?.remote_has_video ?? false,
      );

  /// Debug information for development
  @visibleForTesting
  DebugInfo get debugInfo => DebugInfo(
        sipUsername: sipUsername,
        sipRegistrationStatus: sipRegistrationStatus,
        callStateEnum: callStateEnum,
        audioTrackMode: audioTrackMode,
        lastMessage: message,
      );
}

/// Audio state information for button components
@immutable
class AudioStateInfo {
  const AudioStateInfo({
    required this.isMuted,
    required this.isSpeakerEnabled,
    required this.isBluetoothEnabled,
    required this.audioOutput,
  });

  final bool isMuted;
  final bool isSpeakerEnabled;
  final bool isBluetoothEnabled;
  final String audioOutput;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AudioStateInfo &&
          runtimeType == other.runtimeType &&
          isMuted == other.isMuted &&
          isSpeakerEnabled == other.isSpeakerEnabled &&
          isBluetoothEnabled == other.isBluetoothEnabled &&
          audioOutput == other.audioOutput;

  @override
  int get hashCode =>
      isMuted.hashCode ^
      isSpeakerEnabled.hashCode ^
      isBluetoothEnabled.hashCode ^
      audioOutput.hashCode;
}

/// Call connection information
@immutable
class CallConnectionInfo {
  const CallConnectionInfo({
    required this.isConnected,
    required this.isConnecting,
    required this.isRinging,
    required this.canHangup,
    required this.duration,
    required this.formattedDuration,
  });

  final bool isConnected;
  final bool isConnecting;
  final bool isRinging;
  final bool canHangup;
  final Duration duration;
  final String formattedDuration;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CallConnectionInfo &&
          runtimeType == other.runtimeType &&
          isConnected == other.isConnected &&
          isConnecting == other.isConnecting &&
          isRinging == other.isRinging &&
          canHangup == other.canHangup &&
          duration == other.duration &&
          formattedDuration == other.formattedDuration;

  @override
  int get hashCode =>
      isConnected.hashCode ^
      isConnecting.hashCode ^
      isRinging.hashCode ^
      canHangup.hashCode ^
      duration.hashCode ^
      formattedDuration.hashCode;
}

/// Registration information for dialer functionality
@immutable
class RegistrationInfo {
  const RegistrationInfo({
    required this.isRegistered,
    required this.isFailed,
    required this.isPending,
    required this.isUnregistered,
    required this.canMakeCall,
  });

  final bool isRegistered;
  final bool isFailed;
  final bool isPending;
  final bool isUnregistered;
  final bool canMakeCall;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RegistrationInfo &&
          runtimeType == other.runtimeType &&
          isRegistered == other.isRegistered &&
          isFailed == other.isFailed &&
          isPending == other.isPending &&
          isUnregistered == other.isUnregistered &&
          canMakeCall == other.canMakeCall;

  @override
  int get hashCode =>
      isRegistered.hashCode ^
      isFailed.hashCode ^
      isPending.hashCode ^
      isUnregistered.hashCode ^
      canMakeCall.hashCode;
}

/// Call display information
@immutable
class CallDisplayInfo {
  const CallDisplayInfo({
    required this.phoneNumber,
    required this.countryCode,
    required this.displayStatus,
    required this.muteStatusText,
  });

  final String? phoneNumber;
  final String? countryCode;
  final String displayStatus;
  final String Function(dynamic) muteStatusText;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CallDisplayInfo &&
          runtimeType == other.runtimeType &&
          phoneNumber == other.phoneNumber &&
          countryCode == other.countryCode &&
          displayStatus == other.displayStatus;

  @override
  int get hashCode =>
      phoneNumber.hashCode ^
      countryCode.hashCode ^
      displayStatus.hashCode;
}

/// Media stream information
@immutable
class MediaStreamInfo {
  const MediaStreamInfo({
    required this.hasLocalStream,
    required this.hasRemoteStream,
    required this.isVoiceOnly,
    required this.remoteHasVideo,
  });

  final bool hasLocalStream;
  final bool hasRemoteStream;
  final bool isVoiceOnly;
  final bool remoteHasVideo;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MediaStreamInfo &&
          runtimeType == other.runtimeType &&
          hasLocalStream == other.hasLocalStream &&
          hasRemoteStream == other.hasRemoteStream &&
          isVoiceOnly == other.isVoiceOnly &&
          remoteHasVideo == other.remoteHasVideo;

  @override
  int get hashCode =>
      hasLocalStream.hashCode ^
      hasRemoteStream.hashCode ^
      isVoiceOnly.hashCode ^
      remoteHasVideo.hashCode;
}

/// Debug information for development builds
@immutable
class DebugInfo {
  const DebugInfo({
    required this.sipUsername,
    required this.sipRegistrationStatus,
    required this.callStateEnum,
    required this.audioTrackMode,
    required this.lastMessage,
  });

  final String? sipUsername;
  final dynamic sipRegistrationStatus;
  final dynamic callStateEnum;
  final dynamic audioTrackMode;
  final String? lastMessage;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DebugInfo &&
          runtimeType == other.runtimeType &&
          sipUsername == other.sipUsername &&
          sipRegistrationStatus == other.sipRegistrationStatus &&
          callStateEnum == other.callStateEnum &&
          audioTrackMode == other.audioTrackMode &&
          lastMessage == other.lastMessage;

  @override
  int get hashCode =>
      sipUsername.hashCode ^
      sipRegistrationStatus.hashCode ^
      callStateEnum.hashCode ^
      audioTrackMode.hashCode ^
      lastMessage.hashCode;
}
