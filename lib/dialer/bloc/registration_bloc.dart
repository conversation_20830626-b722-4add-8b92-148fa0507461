import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:froggytalk/dialer/dialer.dart';
import 'package:sip_ua/sip_ua.dart' as sip_ua;
import 'package:utils/utils.dart';

part 'registration_bloc.freezed.dart';
part 'registration_event.dart';
part 'registration_state.dart';

/// Specialized BLoC for managing SIP registration and connection
/// status.
///
/// This BLoC handles all SIP registration-related operations
/// including:
/// - User registration with SIP credentials
/// - Registration status monitoring
/// - Auto-reconnection handling
/// - Registration error management
/// - Connection state tracking
///
/// Separates registration concerns from the main DialerBloc for
/// better maintainability and focused responsibility.
class RegistrationBloc extends Bloc<RegistrationEvent, RegistrationState>
    implements sip_ua.SipUaHelperListener {
  /// Creates a RegistrationBloc with required dependencies
  ///
  /// [callService] - Service for SIP registration operations
  /// [sipHelper] - SIP UA helper for SIP protocol communication
  RegistrationBloc({
    required CallService callService,
    required sip_ua.SIPUAHelper sipHelper,
  })  : _callService = callService,
        _sipHelper = sipHelper,
        super(RegistrationState.initial()) {
    // Register event handlers
    on<_RegisterRequested>(
      _onRegisterRequested,
    );
    on<_ReconnectRequested>(
      _onReconnectRequested,
    );
    on<_UnregisterRequested>(
      _onUnregisterRequested,
    );
    on<_RegistrationStatusUpdated>(
      _onRegistrationStatusUpdated,
    );
    on<_RegistrationReset>(
      _onRegistrationReset,
    );

    // Listen to SIP events
    _sipHelper.addSipUaHelperListener(this);
  }

  final CallService _callService;
  final sip_ua.SIPUAHelper _sipHelper;

  @override
  Future<void> close() {
    _sipHelper.removeSipUaHelperListener(this);
    return super.close();
  }

  /// Attempts to register with SIP server using provided credentials
  void register({
    required String username,
    required String password,
  }) {
    add(
      RegistrationEvent.registerRequested(
        username: username,
        password: password,
      ),
    );
  }

  /// Requests reconnection to SIP server
  void reconnect() {
    add(const RegistrationEvent.reconnectRequested());
  }

  /// Unregisters from SIP server
  void unregister() {
    add(const RegistrationEvent.unregisterRequested());
  }

  /// Resets registration state to initial
  void reset() {
    add(const RegistrationEvent.registrationReset());
  }

  // SipUaHelperListener implementation

  @override
  void callStateChanged(sip_ua.Call call, sip_ua.CallState callState) {
    // Not handled in registration bloc
  }

  @override
  void onNewMessage(sip_ua.SIPMessageRequest msg) {
    // Not handled in registration bloc
  }

  @override
  void onNewNotify(sip_ua.Notify ntf) {
    FroggyLogger.debug(
      'RegistrationBloc SIP Notify: ${ntf.request}',
    );
  }

  @override
  void onNewReinvite(sip_ua.ReInvite event) {
    FroggyLogger.debug(
      'RegistrationBloc SIP ReInvite: hasAudio=${event.hasAudio}',
    );
  }

  @override
  void registrationStateChanged(
    sip_ua.RegistrationState sipRegistrationState,
  ) {
    FroggyLogger.debug(
      'RegistrationBloc SIP Registration State: '
      '${sipRegistrationState.state} - '
      '${sipRegistrationState.cause}',
    );

    // Convert SIP registration state to our internal state
    final registrationStatus =
        _convertSipRegistrationState(sipRegistrationState.state);
    add(
      RegistrationEvent.registrationStatusUpdated(
        status: registrationStatus,
        cause: sipRegistrationState.cause?.toString(),
      ),
    );
  }

  @override
  void transportStateChanged(sip_ua.TransportState state) {
    FroggyLogger.debug(
      'RegistrationBloc SIP TransportState: '
      '${state.state} - ${state.cause}',
    );
  }

  /// Handles registration request events
  FutureOr<void> _onRegisterRequested(
    _RegisterRequested event,
    Emitter<RegistrationState> emit,
  ) async {
    try {
      // Normalize username format
      final username = _normalizeUsername(event.username);

      if (username.isEmpty || event.password.isEmpty) {
        emit(
          state.copyWith(
            status: sip_ua.RegistrationStateEnum.REGISTRATION_FAILED,
            lastError: 'Username and password are required',
            sipUsername: null,
            sipPassword: null,
          ),
        );
        return;
      }

      // Update state with credentials and set to registering
      emit(
        state.copyWith(
          sipUsername: username,
          sipPassword: event.password,
          status: sip_ua.RegistrationStateEnum.UNREGISTERED,
          lastError: null,
        ),
      );

      // Attempt registration
      await _callService.register(
        username: username,
        password: event.password,
      );

      FroggyLogger.debug(
        'RegistrationBloc: Registration requested for user: $username',
      );
    } catch (e) {
      FroggyLogger.error('RegistrationBloc: Registration failed: $e');
      emit(
        state.copyWith(
          status: sip_ua.RegistrationStateEnum.REGISTRATION_FAILED,
          lastError: 'Registration failed: $e',
        ),
      );
    }
  }

  /// Handles reconnection request events
  FutureOr<void> _onReconnectRequested(
    _ReconnectRequested event,
    Emitter<RegistrationState> emit,
  ) async {
    try {
      if (!state.hasCredentials) {
        emit(
          state.copyWith(
            lastError: 'No credentials available for reconnection',
          ),
        );
        return;
      }

      emit(
        state.copyWith(
          status: sip_ua.RegistrationStateEnum.UNREGISTERED,
          lastError: null,
        ),
      );

      _sipHelper.register();
      FroggyLogger.debug('RegistrationBloc: Reconnection requested');
    } catch (e) {
      FroggyLogger.error('RegistrationBloc: Reconnection failed: $e');
      emit(
        state.copyWith(
          status: sip_ua.RegistrationStateEnum.REGISTRATION_FAILED,
          lastError: 'Reconnection failed: $e',
        ),
      );
    }
  }

  /// Handles unregistration request events
  FutureOr<void> _onUnregisterRequested(
    _UnregisterRequested event,
    Emitter<RegistrationState> emit,
  ) async {
    try {
      // Attempt unregistration
      _sipHelper.unregister();

      emit(
        state.copyWith(
          status: sip_ua.RegistrationStateEnum.UNREGISTERED,
          lastError: null,
        ),
      );

      FroggyLogger.debug('RegistrationBloc: Unregistration requested');
    } catch (e) {
      FroggyLogger.error('RegistrationBloc: Unregistration failed: $e');
      emit(
        state.copyWith(
          lastError: 'Unregistration failed: $e',
        ),
      );
    }
  }

  /// Handles registration status update events from SIP layer
  FutureOr<void> _onRegistrationStatusUpdated(
    _RegistrationStatusUpdated event,
    Emitter<RegistrationState> emit,
  ) {
    emit(
      state.copyWith(
        status: event.status,
        lastError: (event.cause?.isNotEmpty ?? false) ? event.cause : null,
      ),
    );
  }

  /// Handles registration state reset events
  FutureOr<void> _onRegistrationReset(
    _RegistrationReset event,
    Emitter<RegistrationState> emit,
  ) {
    emit(RegistrationState.initial());
    FroggyLogger.debug('RegistrationBloc: Registration state reset');
  }

  /// Normalizes username format for SIP registration
  /// Handles international prefixes consistently with CallService
  String _normalizeUsername(String username) {
    // Remove any leading/trailing whitespace
    final trimmed = username.trim();

    // Convert '00' prefix to proper format (remove 00, don't add +)
    if (trimmed.startsWith('00')) {
      return trimmed.substring(2); // Remove '00' prefix
    }

    // Remove '+' prefix for SIP registration
    if (trimmed.startsWith('+')) {
      return trimmed.substring(1); // Remove '+' prefix
    }

    return trimmed;
  }

  /// Converts SIP registration state to internal registration
  /// state enum
  sip_ua.RegistrationStateEnum _convertSipRegistrationState(
    sip_ua.RegistrationStateEnum? sipState,
  ) {
    if (sipState == null) return sip_ua.RegistrationStateEnum.NONE;

    // Since both enums have the same values, we can return directly
    return sipState;
  }
}
