// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'keypad_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$KeypadEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<ContactModel> contacts) started,
    required TResult Function(String? value, bool makeDTMFSound)
        phoneNumberChanged,
    required TResult Function(String? value) setPhoneNumber,
    required TResult Function() deleteButtonPressed,
    required TResult Function(int position) updateCursorPosition,
    required TResult Function(String? value) searchedByDialingCode,
    required TResult Function(List<ContactModel> contacts) searchedContact,
    required TResult Function() reset,
    required TResult Function() checkCallRates,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<ContactModel> contacts)? started,
    TResult? Function(String? value, bool makeDTMFSound)? phoneNumberChanged,
    TResult? Function(String? value)? setPhoneNumber,
    TResult? Function()? deleteButtonPressed,
    TResult? Function(int position)? updateCursorPosition,
    TResult? Function(String? value)? searchedByDialingCode,
    TResult? Function(List<ContactModel> contacts)? searchedContact,
    TResult? Function()? reset,
    TResult? Function()? checkCallRates,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<ContactModel> contacts)? started,
    TResult Function(String? value, bool makeDTMFSound)? phoneNumberChanged,
    TResult Function(String? value)? setPhoneNumber,
    TResult Function()? deleteButtonPressed,
    TResult Function(int position)? updateCursorPosition,
    TResult Function(String? value)? searchedByDialingCode,
    TResult Function(List<ContactModel> contacts)? searchedContact,
    TResult Function()? reset,
    TResult Function()? checkCallRates,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_UpdatedPhoneNumber value) phoneNumberChanged,
    required TResult Function(_ReplacedPhoneNumber value) setPhoneNumber,
    required TResult Function(_DeleteButtonPressed value) deleteButtonPressed,
    required TResult Function(_UpdateCursorPosition value) updateCursorPosition,
    required TResult Function(_SearchedByDialingCode value)
        searchedByDialingCode,
    required TResult Function(_SearchedContact value) searchedContact,
    required TResult Function(_Initial value) reset,
    required TResult Function(_CheckedCallRates value) checkCallRates,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_UpdatedPhoneNumber value)? phoneNumberChanged,
    TResult? Function(_ReplacedPhoneNumber value)? setPhoneNumber,
    TResult? Function(_DeleteButtonPressed value)? deleteButtonPressed,
    TResult? Function(_UpdateCursorPosition value)? updateCursorPosition,
    TResult? Function(_SearchedByDialingCode value)? searchedByDialingCode,
    TResult? Function(_SearchedContact value)? searchedContact,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_CheckedCallRates value)? checkCallRates,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_UpdatedPhoneNumber value)? phoneNumberChanged,
    TResult Function(_ReplacedPhoneNumber value)? setPhoneNumber,
    TResult Function(_DeleteButtonPressed value)? deleteButtonPressed,
    TResult Function(_UpdateCursorPosition value)? updateCursorPosition,
    TResult Function(_SearchedByDialingCode value)? searchedByDialingCode,
    TResult Function(_SearchedContact value)? searchedContact,
    TResult Function(_Initial value)? reset,
    TResult Function(_CheckedCallRates value)? checkCallRates,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $KeypadEventCopyWith<$Res> {
  factory $KeypadEventCopyWith(
          KeypadEvent value, $Res Function(KeypadEvent) then) =
      _$KeypadEventCopyWithImpl<$Res, KeypadEvent>;
}

/// @nodoc
class _$KeypadEventCopyWithImpl<$Res, $Val extends KeypadEvent>
    implements $KeypadEventCopyWith<$Res> {
  _$KeypadEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of KeypadEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$StartedImplCopyWith<$Res> {
  factory _$$StartedImplCopyWith(
          _$StartedImpl value, $Res Function(_$StartedImpl) then) =
      __$$StartedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<ContactModel> contacts});
}

/// @nodoc
class __$$StartedImplCopyWithImpl<$Res>
    extends _$KeypadEventCopyWithImpl<$Res, _$StartedImpl>
    implements _$$StartedImplCopyWith<$Res> {
  __$$StartedImplCopyWithImpl(
      _$StartedImpl _value, $Res Function(_$StartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of KeypadEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contacts = null,
  }) {
    return _then(_$StartedImpl(
      contacts: null == contacts
          ? _value._contacts
          : contacts // ignore: cast_nullable_to_non_nullable
              as List<ContactModel>,
    ));
  }
}

/// @nodoc

class _$StartedImpl implements _Started {
  const _$StartedImpl({required final List<ContactModel> contacts})
      : _contacts = contacts;

  final List<ContactModel> _contacts;
  @override
  List<ContactModel> get contacts {
    if (_contacts is EqualUnmodifiableListView) return _contacts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_contacts);
  }

  @override
  String toString() {
    return 'KeypadEvent.started(contacts: $contacts)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StartedImpl &&
            const DeepCollectionEquality().equals(other._contacts, _contacts));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_contacts));

  /// Create a copy of KeypadEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StartedImplCopyWith<_$StartedImpl> get copyWith =>
      __$$StartedImplCopyWithImpl<_$StartedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<ContactModel> contacts) started,
    required TResult Function(String? value, bool makeDTMFSound)
        phoneNumberChanged,
    required TResult Function(String? value) setPhoneNumber,
    required TResult Function() deleteButtonPressed,
    required TResult Function(int position) updateCursorPosition,
    required TResult Function(String? value) searchedByDialingCode,
    required TResult Function(List<ContactModel> contacts) searchedContact,
    required TResult Function() reset,
    required TResult Function() checkCallRates,
  }) {
    return started(contacts);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<ContactModel> contacts)? started,
    TResult? Function(String? value, bool makeDTMFSound)? phoneNumberChanged,
    TResult? Function(String? value)? setPhoneNumber,
    TResult? Function()? deleteButtonPressed,
    TResult? Function(int position)? updateCursorPosition,
    TResult? Function(String? value)? searchedByDialingCode,
    TResult? Function(List<ContactModel> contacts)? searchedContact,
    TResult? Function()? reset,
    TResult? Function()? checkCallRates,
  }) {
    return started?.call(contacts);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<ContactModel> contacts)? started,
    TResult Function(String? value, bool makeDTMFSound)? phoneNumberChanged,
    TResult Function(String? value)? setPhoneNumber,
    TResult Function()? deleteButtonPressed,
    TResult Function(int position)? updateCursorPosition,
    TResult Function(String? value)? searchedByDialingCode,
    TResult Function(List<ContactModel> contacts)? searchedContact,
    TResult Function()? reset,
    TResult Function()? checkCallRates,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started(contacts);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_UpdatedPhoneNumber value) phoneNumberChanged,
    required TResult Function(_ReplacedPhoneNumber value) setPhoneNumber,
    required TResult Function(_DeleteButtonPressed value) deleteButtonPressed,
    required TResult Function(_UpdateCursorPosition value) updateCursorPosition,
    required TResult Function(_SearchedByDialingCode value)
        searchedByDialingCode,
    required TResult Function(_SearchedContact value) searchedContact,
    required TResult Function(_Initial value) reset,
    required TResult Function(_CheckedCallRates value) checkCallRates,
  }) {
    return started(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_UpdatedPhoneNumber value)? phoneNumberChanged,
    TResult? Function(_ReplacedPhoneNumber value)? setPhoneNumber,
    TResult? Function(_DeleteButtonPressed value)? deleteButtonPressed,
    TResult? Function(_UpdateCursorPosition value)? updateCursorPosition,
    TResult? Function(_SearchedByDialingCode value)? searchedByDialingCode,
    TResult? Function(_SearchedContact value)? searchedContact,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_CheckedCallRates value)? checkCallRates,
  }) {
    return started?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_UpdatedPhoneNumber value)? phoneNumberChanged,
    TResult Function(_ReplacedPhoneNumber value)? setPhoneNumber,
    TResult Function(_DeleteButtonPressed value)? deleteButtonPressed,
    TResult Function(_UpdateCursorPosition value)? updateCursorPosition,
    TResult Function(_SearchedByDialingCode value)? searchedByDialingCode,
    TResult Function(_SearchedContact value)? searchedContact,
    TResult Function(_Initial value)? reset,
    TResult Function(_CheckedCallRates value)? checkCallRates,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started(this);
    }
    return orElse();
  }
}

abstract class _Started implements KeypadEvent {
  const factory _Started({required final List<ContactModel> contacts}) =
      _$StartedImpl;

  List<ContactModel> get contacts;

  /// Create a copy of KeypadEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StartedImplCopyWith<_$StartedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdatedPhoneNumberImplCopyWith<$Res> {
  factory _$$UpdatedPhoneNumberImplCopyWith(_$UpdatedPhoneNumberImpl value,
          $Res Function(_$UpdatedPhoneNumberImpl) then) =
      __$$UpdatedPhoneNumberImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? value, bool makeDTMFSound});
}

/// @nodoc
class __$$UpdatedPhoneNumberImplCopyWithImpl<$Res>
    extends _$KeypadEventCopyWithImpl<$Res, _$UpdatedPhoneNumberImpl>
    implements _$$UpdatedPhoneNumberImplCopyWith<$Res> {
  __$$UpdatedPhoneNumberImplCopyWithImpl(_$UpdatedPhoneNumberImpl _value,
      $Res Function(_$UpdatedPhoneNumberImpl) _then)
      : super(_value, _then);

  /// Create a copy of KeypadEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = freezed,
    Object? makeDTMFSound = null,
  }) {
    return _then(_$UpdatedPhoneNumberImpl(
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String?,
      makeDTMFSound: null == makeDTMFSound
          ? _value.makeDTMFSound
          : makeDTMFSound // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$UpdatedPhoneNumberImpl implements _UpdatedPhoneNumber {
  const _$UpdatedPhoneNumberImpl({this.value, this.makeDTMFSound = true});

  @override
  final String? value;
  @override
  @JsonKey()
  final bool makeDTMFSound;

  @override
  String toString() {
    return 'KeypadEvent.phoneNumberChanged(value: $value, makeDTMFSound: $makeDTMFSound)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdatedPhoneNumberImpl &&
            (identical(other.value, value) || other.value == value) &&
            (identical(other.makeDTMFSound, makeDTMFSound) ||
                other.makeDTMFSound == makeDTMFSound));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value, makeDTMFSound);

  /// Create a copy of KeypadEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdatedPhoneNumberImplCopyWith<_$UpdatedPhoneNumberImpl> get copyWith =>
      __$$UpdatedPhoneNumberImplCopyWithImpl<_$UpdatedPhoneNumberImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<ContactModel> contacts) started,
    required TResult Function(String? value, bool makeDTMFSound)
        phoneNumberChanged,
    required TResult Function(String? value) setPhoneNumber,
    required TResult Function() deleteButtonPressed,
    required TResult Function(int position) updateCursorPosition,
    required TResult Function(String? value) searchedByDialingCode,
    required TResult Function(List<ContactModel> contacts) searchedContact,
    required TResult Function() reset,
    required TResult Function() checkCallRates,
  }) {
    return phoneNumberChanged(value, makeDTMFSound);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<ContactModel> contacts)? started,
    TResult? Function(String? value, bool makeDTMFSound)? phoneNumberChanged,
    TResult? Function(String? value)? setPhoneNumber,
    TResult? Function()? deleteButtonPressed,
    TResult? Function(int position)? updateCursorPosition,
    TResult? Function(String? value)? searchedByDialingCode,
    TResult? Function(List<ContactModel> contacts)? searchedContact,
    TResult? Function()? reset,
    TResult? Function()? checkCallRates,
  }) {
    return phoneNumberChanged?.call(value, makeDTMFSound);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<ContactModel> contacts)? started,
    TResult Function(String? value, bool makeDTMFSound)? phoneNumberChanged,
    TResult Function(String? value)? setPhoneNumber,
    TResult Function()? deleteButtonPressed,
    TResult Function(int position)? updateCursorPosition,
    TResult Function(String? value)? searchedByDialingCode,
    TResult Function(List<ContactModel> contacts)? searchedContact,
    TResult Function()? reset,
    TResult Function()? checkCallRates,
    required TResult orElse(),
  }) {
    if (phoneNumberChanged != null) {
      return phoneNumberChanged(value, makeDTMFSound);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_UpdatedPhoneNumber value) phoneNumberChanged,
    required TResult Function(_ReplacedPhoneNumber value) setPhoneNumber,
    required TResult Function(_DeleteButtonPressed value) deleteButtonPressed,
    required TResult Function(_UpdateCursorPosition value) updateCursorPosition,
    required TResult Function(_SearchedByDialingCode value)
        searchedByDialingCode,
    required TResult Function(_SearchedContact value) searchedContact,
    required TResult Function(_Initial value) reset,
    required TResult Function(_CheckedCallRates value) checkCallRates,
  }) {
    return phoneNumberChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_UpdatedPhoneNumber value)? phoneNumberChanged,
    TResult? Function(_ReplacedPhoneNumber value)? setPhoneNumber,
    TResult? Function(_DeleteButtonPressed value)? deleteButtonPressed,
    TResult? Function(_UpdateCursorPosition value)? updateCursorPosition,
    TResult? Function(_SearchedByDialingCode value)? searchedByDialingCode,
    TResult? Function(_SearchedContact value)? searchedContact,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_CheckedCallRates value)? checkCallRates,
  }) {
    return phoneNumberChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_UpdatedPhoneNumber value)? phoneNumberChanged,
    TResult Function(_ReplacedPhoneNumber value)? setPhoneNumber,
    TResult Function(_DeleteButtonPressed value)? deleteButtonPressed,
    TResult Function(_UpdateCursorPosition value)? updateCursorPosition,
    TResult Function(_SearchedByDialingCode value)? searchedByDialingCode,
    TResult Function(_SearchedContact value)? searchedContact,
    TResult Function(_Initial value)? reset,
    TResult Function(_CheckedCallRates value)? checkCallRates,
    required TResult orElse(),
  }) {
    if (phoneNumberChanged != null) {
      return phoneNumberChanged(this);
    }
    return orElse();
  }
}

abstract class _UpdatedPhoneNumber implements KeypadEvent {
  const factory _UpdatedPhoneNumber(
      {final String? value,
      final bool makeDTMFSound}) = _$UpdatedPhoneNumberImpl;

  String? get value;
  bool get makeDTMFSound;

  /// Create a copy of KeypadEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdatedPhoneNumberImplCopyWith<_$UpdatedPhoneNumberImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ReplacedPhoneNumberImplCopyWith<$Res> {
  factory _$$ReplacedPhoneNumberImplCopyWith(_$ReplacedPhoneNumberImpl value,
          $Res Function(_$ReplacedPhoneNumberImpl) then) =
      __$$ReplacedPhoneNumberImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? value});
}

/// @nodoc
class __$$ReplacedPhoneNumberImplCopyWithImpl<$Res>
    extends _$KeypadEventCopyWithImpl<$Res, _$ReplacedPhoneNumberImpl>
    implements _$$ReplacedPhoneNumberImplCopyWith<$Res> {
  __$$ReplacedPhoneNumberImplCopyWithImpl(_$ReplacedPhoneNumberImpl _value,
      $Res Function(_$ReplacedPhoneNumberImpl) _then)
      : super(_value, _then);

  /// Create a copy of KeypadEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = freezed,
  }) {
    return _then(_$ReplacedPhoneNumberImpl(
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$ReplacedPhoneNumberImpl implements _ReplacedPhoneNumber {
  const _$ReplacedPhoneNumberImpl({this.value});

  @override
  final String? value;

  @override
  String toString() {
    return 'KeypadEvent.setPhoneNumber(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReplacedPhoneNumberImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  /// Create a copy of KeypadEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ReplacedPhoneNumberImplCopyWith<_$ReplacedPhoneNumberImpl> get copyWith =>
      __$$ReplacedPhoneNumberImplCopyWithImpl<_$ReplacedPhoneNumberImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<ContactModel> contacts) started,
    required TResult Function(String? value, bool makeDTMFSound)
        phoneNumberChanged,
    required TResult Function(String? value) setPhoneNumber,
    required TResult Function() deleteButtonPressed,
    required TResult Function(int position) updateCursorPosition,
    required TResult Function(String? value) searchedByDialingCode,
    required TResult Function(List<ContactModel> contacts) searchedContact,
    required TResult Function() reset,
    required TResult Function() checkCallRates,
  }) {
    return setPhoneNumber(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<ContactModel> contacts)? started,
    TResult? Function(String? value, bool makeDTMFSound)? phoneNumberChanged,
    TResult? Function(String? value)? setPhoneNumber,
    TResult? Function()? deleteButtonPressed,
    TResult? Function(int position)? updateCursorPosition,
    TResult? Function(String? value)? searchedByDialingCode,
    TResult? Function(List<ContactModel> contacts)? searchedContact,
    TResult? Function()? reset,
    TResult? Function()? checkCallRates,
  }) {
    return setPhoneNumber?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<ContactModel> contacts)? started,
    TResult Function(String? value, bool makeDTMFSound)? phoneNumberChanged,
    TResult Function(String? value)? setPhoneNumber,
    TResult Function()? deleteButtonPressed,
    TResult Function(int position)? updateCursorPosition,
    TResult Function(String? value)? searchedByDialingCode,
    TResult Function(List<ContactModel> contacts)? searchedContact,
    TResult Function()? reset,
    TResult Function()? checkCallRates,
    required TResult orElse(),
  }) {
    if (setPhoneNumber != null) {
      return setPhoneNumber(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_UpdatedPhoneNumber value) phoneNumberChanged,
    required TResult Function(_ReplacedPhoneNumber value) setPhoneNumber,
    required TResult Function(_DeleteButtonPressed value) deleteButtonPressed,
    required TResult Function(_UpdateCursorPosition value) updateCursorPosition,
    required TResult Function(_SearchedByDialingCode value)
        searchedByDialingCode,
    required TResult Function(_SearchedContact value) searchedContact,
    required TResult Function(_Initial value) reset,
    required TResult Function(_CheckedCallRates value) checkCallRates,
  }) {
    return setPhoneNumber(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_UpdatedPhoneNumber value)? phoneNumberChanged,
    TResult? Function(_ReplacedPhoneNumber value)? setPhoneNumber,
    TResult? Function(_DeleteButtonPressed value)? deleteButtonPressed,
    TResult? Function(_UpdateCursorPosition value)? updateCursorPosition,
    TResult? Function(_SearchedByDialingCode value)? searchedByDialingCode,
    TResult? Function(_SearchedContact value)? searchedContact,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_CheckedCallRates value)? checkCallRates,
  }) {
    return setPhoneNumber?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_UpdatedPhoneNumber value)? phoneNumberChanged,
    TResult Function(_ReplacedPhoneNumber value)? setPhoneNumber,
    TResult Function(_DeleteButtonPressed value)? deleteButtonPressed,
    TResult Function(_UpdateCursorPosition value)? updateCursorPosition,
    TResult Function(_SearchedByDialingCode value)? searchedByDialingCode,
    TResult Function(_SearchedContact value)? searchedContact,
    TResult Function(_Initial value)? reset,
    TResult Function(_CheckedCallRates value)? checkCallRates,
    required TResult orElse(),
  }) {
    if (setPhoneNumber != null) {
      return setPhoneNumber(this);
    }
    return orElse();
  }
}

abstract class _ReplacedPhoneNumber implements KeypadEvent {
  const factory _ReplacedPhoneNumber({final String? value}) =
      _$ReplacedPhoneNumberImpl;

  String? get value;

  /// Create a copy of KeypadEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ReplacedPhoneNumberImplCopyWith<_$ReplacedPhoneNumberImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeleteButtonPressedImplCopyWith<$Res> {
  factory _$$DeleteButtonPressedImplCopyWith(_$DeleteButtonPressedImpl value,
          $Res Function(_$DeleteButtonPressedImpl) then) =
      __$$DeleteButtonPressedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DeleteButtonPressedImplCopyWithImpl<$Res>
    extends _$KeypadEventCopyWithImpl<$Res, _$DeleteButtonPressedImpl>
    implements _$$DeleteButtonPressedImplCopyWith<$Res> {
  __$$DeleteButtonPressedImplCopyWithImpl(_$DeleteButtonPressedImpl _value,
      $Res Function(_$DeleteButtonPressedImpl) _then)
      : super(_value, _then);

  /// Create a copy of KeypadEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DeleteButtonPressedImpl implements _DeleteButtonPressed {
  const _$DeleteButtonPressedImpl();

  @override
  String toString() {
    return 'KeypadEvent.deleteButtonPressed()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeleteButtonPressedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<ContactModel> contacts) started,
    required TResult Function(String? value, bool makeDTMFSound)
        phoneNumberChanged,
    required TResult Function(String? value) setPhoneNumber,
    required TResult Function() deleteButtonPressed,
    required TResult Function(int position) updateCursorPosition,
    required TResult Function(String? value) searchedByDialingCode,
    required TResult Function(List<ContactModel> contacts) searchedContact,
    required TResult Function() reset,
    required TResult Function() checkCallRates,
  }) {
    return deleteButtonPressed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<ContactModel> contacts)? started,
    TResult? Function(String? value, bool makeDTMFSound)? phoneNumberChanged,
    TResult? Function(String? value)? setPhoneNumber,
    TResult? Function()? deleteButtonPressed,
    TResult? Function(int position)? updateCursorPosition,
    TResult? Function(String? value)? searchedByDialingCode,
    TResult? Function(List<ContactModel> contacts)? searchedContact,
    TResult? Function()? reset,
    TResult? Function()? checkCallRates,
  }) {
    return deleteButtonPressed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<ContactModel> contacts)? started,
    TResult Function(String? value, bool makeDTMFSound)? phoneNumberChanged,
    TResult Function(String? value)? setPhoneNumber,
    TResult Function()? deleteButtonPressed,
    TResult Function(int position)? updateCursorPosition,
    TResult Function(String? value)? searchedByDialingCode,
    TResult Function(List<ContactModel> contacts)? searchedContact,
    TResult Function()? reset,
    TResult Function()? checkCallRates,
    required TResult orElse(),
  }) {
    if (deleteButtonPressed != null) {
      return deleteButtonPressed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_UpdatedPhoneNumber value) phoneNumberChanged,
    required TResult Function(_ReplacedPhoneNumber value) setPhoneNumber,
    required TResult Function(_DeleteButtonPressed value) deleteButtonPressed,
    required TResult Function(_UpdateCursorPosition value) updateCursorPosition,
    required TResult Function(_SearchedByDialingCode value)
        searchedByDialingCode,
    required TResult Function(_SearchedContact value) searchedContact,
    required TResult Function(_Initial value) reset,
    required TResult Function(_CheckedCallRates value) checkCallRates,
  }) {
    return deleteButtonPressed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_UpdatedPhoneNumber value)? phoneNumberChanged,
    TResult? Function(_ReplacedPhoneNumber value)? setPhoneNumber,
    TResult? Function(_DeleteButtonPressed value)? deleteButtonPressed,
    TResult? Function(_UpdateCursorPosition value)? updateCursorPosition,
    TResult? Function(_SearchedByDialingCode value)? searchedByDialingCode,
    TResult? Function(_SearchedContact value)? searchedContact,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_CheckedCallRates value)? checkCallRates,
  }) {
    return deleteButtonPressed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_UpdatedPhoneNumber value)? phoneNumberChanged,
    TResult Function(_ReplacedPhoneNumber value)? setPhoneNumber,
    TResult Function(_DeleteButtonPressed value)? deleteButtonPressed,
    TResult Function(_UpdateCursorPosition value)? updateCursorPosition,
    TResult Function(_SearchedByDialingCode value)? searchedByDialingCode,
    TResult Function(_SearchedContact value)? searchedContact,
    TResult Function(_Initial value)? reset,
    TResult Function(_CheckedCallRates value)? checkCallRates,
    required TResult orElse(),
  }) {
    if (deleteButtonPressed != null) {
      return deleteButtonPressed(this);
    }
    return orElse();
  }
}

abstract class _DeleteButtonPressed implements KeypadEvent {
  const factory _DeleteButtonPressed() = _$DeleteButtonPressedImpl;
}

/// @nodoc
abstract class _$$UpdateCursorPositionImplCopyWith<$Res> {
  factory _$$UpdateCursorPositionImplCopyWith(_$UpdateCursorPositionImpl value,
          $Res Function(_$UpdateCursorPositionImpl) then) =
      __$$UpdateCursorPositionImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int position});
}

/// @nodoc
class __$$UpdateCursorPositionImplCopyWithImpl<$Res>
    extends _$KeypadEventCopyWithImpl<$Res, _$UpdateCursorPositionImpl>
    implements _$$UpdateCursorPositionImplCopyWith<$Res> {
  __$$UpdateCursorPositionImplCopyWithImpl(_$UpdateCursorPositionImpl _value,
      $Res Function(_$UpdateCursorPositionImpl) _then)
      : super(_value, _then);

  /// Create a copy of KeypadEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? position = null,
  }) {
    return _then(_$UpdateCursorPositionImpl(
      null == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$UpdateCursorPositionImpl implements _UpdateCursorPosition {
  const _$UpdateCursorPositionImpl(this.position);

  @override
  final int position;

  @override
  String toString() {
    return 'KeypadEvent.updateCursorPosition(position: $position)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateCursorPositionImpl &&
            (identical(other.position, position) ||
                other.position == position));
  }

  @override
  int get hashCode => Object.hash(runtimeType, position);

  /// Create a copy of KeypadEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateCursorPositionImplCopyWith<_$UpdateCursorPositionImpl>
      get copyWith =>
          __$$UpdateCursorPositionImplCopyWithImpl<_$UpdateCursorPositionImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<ContactModel> contacts) started,
    required TResult Function(String? value, bool makeDTMFSound)
        phoneNumberChanged,
    required TResult Function(String? value) setPhoneNumber,
    required TResult Function() deleteButtonPressed,
    required TResult Function(int position) updateCursorPosition,
    required TResult Function(String? value) searchedByDialingCode,
    required TResult Function(List<ContactModel> contacts) searchedContact,
    required TResult Function() reset,
    required TResult Function() checkCallRates,
  }) {
    return updateCursorPosition(position);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<ContactModel> contacts)? started,
    TResult? Function(String? value, bool makeDTMFSound)? phoneNumberChanged,
    TResult? Function(String? value)? setPhoneNumber,
    TResult? Function()? deleteButtonPressed,
    TResult? Function(int position)? updateCursorPosition,
    TResult? Function(String? value)? searchedByDialingCode,
    TResult? Function(List<ContactModel> contacts)? searchedContact,
    TResult? Function()? reset,
    TResult? Function()? checkCallRates,
  }) {
    return updateCursorPosition?.call(position);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<ContactModel> contacts)? started,
    TResult Function(String? value, bool makeDTMFSound)? phoneNumberChanged,
    TResult Function(String? value)? setPhoneNumber,
    TResult Function()? deleteButtonPressed,
    TResult Function(int position)? updateCursorPosition,
    TResult Function(String? value)? searchedByDialingCode,
    TResult Function(List<ContactModel> contacts)? searchedContact,
    TResult Function()? reset,
    TResult Function()? checkCallRates,
    required TResult orElse(),
  }) {
    if (updateCursorPosition != null) {
      return updateCursorPosition(position);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_UpdatedPhoneNumber value) phoneNumberChanged,
    required TResult Function(_ReplacedPhoneNumber value) setPhoneNumber,
    required TResult Function(_DeleteButtonPressed value) deleteButtonPressed,
    required TResult Function(_UpdateCursorPosition value) updateCursorPosition,
    required TResult Function(_SearchedByDialingCode value)
        searchedByDialingCode,
    required TResult Function(_SearchedContact value) searchedContact,
    required TResult Function(_Initial value) reset,
    required TResult Function(_CheckedCallRates value) checkCallRates,
  }) {
    return updateCursorPosition(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_UpdatedPhoneNumber value)? phoneNumberChanged,
    TResult? Function(_ReplacedPhoneNumber value)? setPhoneNumber,
    TResult? Function(_DeleteButtonPressed value)? deleteButtonPressed,
    TResult? Function(_UpdateCursorPosition value)? updateCursorPosition,
    TResult? Function(_SearchedByDialingCode value)? searchedByDialingCode,
    TResult? Function(_SearchedContact value)? searchedContact,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_CheckedCallRates value)? checkCallRates,
  }) {
    return updateCursorPosition?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_UpdatedPhoneNumber value)? phoneNumberChanged,
    TResult Function(_ReplacedPhoneNumber value)? setPhoneNumber,
    TResult Function(_DeleteButtonPressed value)? deleteButtonPressed,
    TResult Function(_UpdateCursorPosition value)? updateCursorPosition,
    TResult Function(_SearchedByDialingCode value)? searchedByDialingCode,
    TResult Function(_SearchedContact value)? searchedContact,
    TResult Function(_Initial value)? reset,
    TResult Function(_CheckedCallRates value)? checkCallRates,
    required TResult orElse(),
  }) {
    if (updateCursorPosition != null) {
      return updateCursorPosition(this);
    }
    return orElse();
  }
}

abstract class _UpdateCursorPosition implements KeypadEvent {
  const factory _UpdateCursorPosition(final int position) =
      _$UpdateCursorPositionImpl;

  int get position;

  /// Create a copy of KeypadEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateCursorPositionImplCopyWith<_$UpdateCursorPositionImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SearchedByDialingCodeImplCopyWith<$Res> {
  factory _$$SearchedByDialingCodeImplCopyWith(
          _$SearchedByDialingCodeImpl value,
          $Res Function(_$SearchedByDialingCodeImpl) then) =
      __$$SearchedByDialingCodeImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? value});
}

/// @nodoc
class __$$SearchedByDialingCodeImplCopyWithImpl<$Res>
    extends _$KeypadEventCopyWithImpl<$Res, _$SearchedByDialingCodeImpl>
    implements _$$SearchedByDialingCodeImplCopyWith<$Res> {
  __$$SearchedByDialingCodeImplCopyWithImpl(_$SearchedByDialingCodeImpl _value,
      $Res Function(_$SearchedByDialingCodeImpl) _then)
      : super(_value, _then);

  /// Create a copy of KeypadEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = freezed,
  }) {
    return _then(_$SearchedByDialingCodeImpl(
      value: freezed == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$SearchedByDialingCodeImpl implements _SearchedByDialingCode {
  const _$SearchedByDialingCodeImpl({this.value});

  @override
  final String? value;

  @override
  String toString() {
    return 'KeypadEvent.searchedByDialingCode(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchedByDialingCodeImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  /// Create a copy of KeypadEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchedByDialingCodeImplCopyWith<_$SearchedByDialingCodeImpl>
      get copyWith => __$$SearchedByDialingCodeImplCopyWithImpl<
          _$SearchedByDialingCodeImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<ContactModel> contacts) started,
    required TResult Function(String? value, bool makeDTMFSound)
        phoneNumberChanged,
    required TResult Function(String? value) setPhoneNumber,
    required TResult Function() deleteButtonPressed,
    required TResult Function(int position) updateCursorPosition,
    required TResult Function(String? value) searchedByDialingCode,
    required TResult Function(List<ContactModel> contacts) searchedContact,
    required TResult Function() reset,
    required TResult Function() checkCallRates,
  }) {
    return searchedByDialingCode(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<ContactModel> contacts)? started,
    TResult? Function(String? value, bool makeDTMFSound)? phoneNumberChanged,
    TResult? Function(String? value)? setPhoneNumber,
    TResult? Function()? deleteButtonPressed,
    TResult? Function(int position)? updateCursorPosition,
    TResult? Function(String? value)? searchedByDialingCode,
    TResult? Function(List<ContactModel> contacts)? searchedContact,
    TResult? Function()? reset,
    TResult? Function()? checkCallRates,
  }) {
    return searchedByDialingCode?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<ContactModel> contacts)? started,
    TResult Function(String? value, bool makeDTMFSound)? phoneNumberChanged,
    TResult Function(String? value)? setPhoneNumber,
    TResult Function()? deleteButtonPressed,
    TResult Function(int position)? updateCursorPosition,
    TResult Function(String? value)? searchedByDialingCode,
    TResult Function(List<ContactModel> contacts)? searchedContact,
    TResult Function()? reset,
    TResult Function()? checkCallRates,
    required TResult orElse(),
  }) {
    if (searchedByDialingCode != null) {
      return searchedByDialingCode(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_UpdatedPhoneNumber value) phoneNumberChanged,
    required TResult Function(_ReplacedPhoneNumber value) setPhoneNumber,
    required TResult Function(_DeleteButtonPressed value) deleteButtonPressed,
    required TResult Function(_UpdateCursorPosition value) updateCursorPosition,
    required TResult Function(_SearchedByDialingCode value)
        searchedByDialingCode,
    required TResult Function(_SearchedContact value) searchedContact,
    required TResult Function(_Initial value) reset,
    required TResult Function(_CheckedCallRates value) checkCallRates,
  }) {
    return searchedByDialingCode(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_UpdatedPhoneNumber value)? phoneNumberChanged,
    TResult? Function(_ReplacedPhoneNumber value)? setPhoneNumber,
    TResult? Function(_DeleteButtonPressed value)? deleteButtonPressed,
    TResult? Function(_UpdateCursorPosition value)? updateCursorPosition,
    TResult? Function(_SearchedByDialingCode value)? searchedByDialingCode,
    TResult? Function(_SearchedContact value)? searchedContact,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_CheckedCallRates value)? checkCallRates,
  }) {
    return searchedByDialingCode?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_UpdatedPhoneNumber value)? phoneNumberChanged,
    TResult Function(_ReplacedPhoneNumber value)? setPhoneNumber,
    TResult Function(_DeleteButtonPressed value)? deleteButtonPressed,
    TResult Function(_UpdateCursorPosition value)? updateCursorPosition,
    TResult Function(_SearchedByDialingCode value)? searchedByDialingCode,
    TResult Function(_SearchedContact value)? searchedContact,
    TResult Function(_Initial value)? reset,
    TResult Function(_CheckedCallRates value)? checkCallRates,
    required TResult orElse(),
  }) {
    if (searchedByDialingCode != null) {
      return searchedByDialingCode(this);
    }
    return orElse();
  }
}

abstract class _SearchedByDialingCode implements KeypadEvent {
  const factory _SearchedByDialingCode({final String? value}) =
      _$SearchedByDialingCodeImpl;

  String? get value;

  /// Create a copy of KeypadEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SearchedByDialingCodeImplCopyWith<_$SearchedByDialingCodeImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SearchedContactImplCopyWith<$Res> {
  factory _$$SearchedContactImplCopyWith(_$SearchedContactImpl value,
          $Res Function(_$SearchedContactImpl) then) =
      __$$SearchedContactImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<ContactModel> contacts});
}

/// @nodoc
class __$$SearchedContactImplCopyWithImpl<$Res>
    extends _$KeypadEventCopyWithImpl<$Res, _$SearchedContactImpl>
    implements _$$SearchedContactImplCopyWith<$Res> {
  __$$SearchedContactImplCopyWithImpl(
      _$SearchedContactImpl _value, $Res Function(_$SearchedContactImpl) _then)
      : super(_value, _then);

  /// Create a copy of KeypadEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contacts = null,
  }) {
    return _then(_$SearchedContactImpl(
      contacts: null == contacts
          ? _value._contacts
          : contacts // ignore: cast_nullable_to_non_nullable
              as List<ContactModel>,
    ));
  }
}

/// @nodoc

class _$SearchedContactImpl implements _SearchedContact {
  const _$SearchedContactImpl({final List<ContactModel> contacts = const []})
      : _contacts = contacts;

  final List<ContactModel> _contacts;
  @override
  @JsonKey()
  List<ContactModel> get contacts {
    if (_contacts is EqualUnmodifiableListView) return _contacts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_contacts);
  }

  @override
  String toString() {
    return 'KeypadEvent.searchedContact(contacts: $contacts)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchedContactImpl &&
            const DeepCollectionEquality().equals(other._contacts, _contacts));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_contacts));

  /// Create a copy of KeypadEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchedContactImplCopyWith<_$SearchedContactImpl> get copyWith =>
      __$$SearchedContactImplCopyWithImpl<_$SearchedContactImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<ContactModel> contacts) started,
    required TResult Function(String? value, bool makeDTMFSound)
        phoneNumberChanged,
    required TResult Function(String? value) setPhoneNumber,
    required TResult Function() deleteButtonPressed,
    required TResult Function(int position) updateCursorPosition,
    required TResult Function(String? value) searchedByDialingCode,
    required TResult Function(List<ContactModel> contacts) searchedContact,
    required TResult Function() reset,
    required TResult Function() checkCallRates,
  }) {
    return searchedContact(contacts);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<ContactModel> contacts)? started,
    TResult? Function(String? value, bool makeDTMFSound)? phoneNumberChanged,
    TResult? Function(String? value)? setPhoneNumber,
    TResult? Function()? deleteButtonPressed,
    TResult? Function(int position)? updateCursorPosition,
    TResult? Function(String? value)? searchedByDialingCode,
    TResult? Function(List<ContactModel> contacts)? searchedContact,
    TResult? Function()? reset,
    TResult? Function()? checkCallRates,
  }) {
    return searchedContact?.call(contacts);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<ContactModel> contacts)? started,
    TResult Function(String? value, bool makeDTMFSound)? phoneNumberChanged,
    TResult Function(String? value)? setPhoneNumber,
    TResult Function()? deleteButtonPressed,
    TResult Function(int position)? updateCursorPosition,
    TResult Function(String? value)? searchedByDialingCode,
    TResult Function(List<ContactModel> contacts)? searchedContact,
    TResult Function()? reset,
    TResult Function()? checkCallRates,
    required TResult orElse(),
  }) {
    if (searchedContact != null) {
      return searchedContact(contacts);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_UpdatedPhoneNumber value) phoneNumberChanged,
    required TResult Function(_ReplacedPhoneNumber value) setPhoneNumber,
    required TResult Function(_DeleteButtonPressed value) deleteButtonPressed,
    required TResult Function(_UpdateCursorPosition value) updateCursorPosition,
    required TResult Function(_SearchedByDialingCode value)
        searchedByDialingCode,
    required TResult Function(_SearchedContact value) searchedContact,
    required TResult Function(_Initial value) reset,
    required TResult Function(_CheckedCallRates value) checkCallRates,
  }) {
    return searchedContact(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_UpdatedPhoneNumber value)? phoneNumberChanged,
    TResult? Function(_ReplacedPhoneNumber value)? setPhoneNumber,
    TResult? Function(_DeleteButtonPressed value)? deleteButtonPressed,
    TResult? Function(_UpdateCursorPosition value)? updateCursorPosition,
    TResult? Function(_SearchedByDialingCode value)? searchedByDialingCode,
    TResult? Function(_SearchedContact value)? searchedContact,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_CheckedCallRates value)? checkCallRates,
  }) {
    return searchedContact?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_UpdatedPhoneNumber value)? phoneNumberChanged,
    TResult Function(_ReplacedPhoneNumber value)? setPhoneNumber,
    TResult Function(_DeleteButtonPressed value)? deleteButtonPressed,
    TResult Function(_UpdateCursorPosition value)? updateCursorPosition,
    TResult Function(_SearchedByDialingCode value)? searchedByDialingCode,
    TResult Function(_SearchedContact value)? searchedContact,
    TResult Function(_Initial value)? reset,
    TResult Function(_CheckedCallRates value)? checkCallRates,
    required TResult orElse(),
  }) {
    if (searchedContact != null) {
      return searchedContact(this);
    }
    return orElse();
  }
}

abstract class _SearchedContact implements KeypadEvent {
  const factory _SearchedContact({final List<ContactModel> contacts}) =
      _$SearchedContactImpl;

  List<ContactModel> get contacts;

  /// Create a copy of KeypadEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SearchedContactImplCopyWith<_$SearchedContactImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$KeypadEventCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of KeypadEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'KeypadEvent.reset()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<ContactModel> contacts) started,
    required TResult Function(String? value, bool makeDTMFSound)
        phoneNumberChanged,
    required TResult Function(String? value) setPhoneNumber,
    required TResult Function() deleteButtonPressed,
    required TResult Function(int position) updateCursorPosition,
    required TResult Function(String? value) searchedByDialingCode,
    required TResult Function(List<ContactModel> contacts) searchedContact,
    required TResult Function() reset,
    required TResult Function() checkCallRates,
  }) {
    return reset();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<ContactModel> contacts)? started,
    TResult? Function(String? value, bool makeDTMFSound)? phoneNumberChanged,
    TResult? Function(String? value)? setPhoneNumber,
    TResult? Function()? deleteButtonPressed,
    TResult? Function(int position)? updateCursorPosition,
    TResult? Function(String? value)? searchedByDialingCode,
    TResult? Function(List<ContactModel> contacts)? searchedContact,
    TResult? Function()? reset,
    TResult? Function()? checkCallRates,
  }) {
    return reset?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<ContactModel> contacts)? started,
    TResult Function(String? value, bool makeDTMFSound)? phoneNumberChanged,
    TResult Function(String? value)? setPhoneNumber,
    TResult Function()? deleteButtonPressed,
    TResult Function(int position)? updateCursorPosition,
    TResult Function(String? value)? searchedByDialingCode,
    TResult Function(List<ContactModel> contacts)? searchedContact,
    TResult Function()? reset,
    TResult Function()? checkCallRates,
    required TResult orElse(),
  }) {
    if (reset != null) {
      return reset();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_UpdatedPhoneNumber value) phoneNumberChanged,
    required TResult Function(_ReplacedPhoneNumber value) setPhoneNumber,
    required TResult Function(_DeleteButtonPressed value) deleteButtonPressed,
    required TResult Function(_UpdateCursorPosition value) updateCursorPosition,
    required TResult Function(_SearchedByDialingCode value)
        searchedByDialingCode,
    required TResult Function(_SearchedContact value) searchedContact,
    required TResult Function(_Initial value) reset,
    required TResult Function(_CheckedCallRates value) checkCallRates,
  }) {
    return reset(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_UpdatedPhoneNumber value)? phoneNumberChanged,
    TResult? Function(_ReplacedPhoneNumber value)? setPhoneNumber,
    TResult? Function(_DeleteButtonPressed value)? deleteButtonPressed,
    TResult? Function(_UpdateCursorPosition value)? updateCursorPosition,
    TResult? Function(_SearchedByDialingCode value)? searchedByDialingCode,
    TResult? Function(_SearchedContact value)? searchedContact,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_CheckedCallRates value)? checkCallRates,
  }) {
    return reset?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_UpdatedPhoneNumber value)? phoneNumberChanged,
    TResult Function(_ReplacedPhoneNumber value)? setPhoneNumber,
    TResult Function(_DeleteButtonPressed value)? deleteButtonPressed,
    TResult Function(_UpdateCursorPosition value)? updateCursorPosition,
    TResult Function(_SearchedByDialingCode value)? searchedByDialingCode,
    TResult Function(_SearchedContact value)? searchedContact,
    TResult Function(_Initial value)? reset,
    TResult Function(_CheckedCallRates value)? checkCallRates,
    required TResult orElse(),
  }) {
    if (reset != null) {
      return reset(this);
    }
    return orElse();
  }
}

abstract class _Initial implements KeypadEvent {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$CheckedCallRatesImplCopyWith<$Res> {
  factory _$$CheckedCallRatesImplCopyWith(_$CheckedCallRatesImpl value,
          $Res Function(_$CheckedCallRatesImpl) then) =
      __$$CheckedCallRatesImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CheckedCallRatesImplCopyWithImpl<$Res>
    extends _$KeypadEventCopyWithImpl<$Res, _$CheckedCallRatesImpl>
    implements _$$CheckedCallRatesImplCopyWith<$Res> {
  __$$CheckedCallRatesImplCopyWithImpl(_$CheckedCallRatesImpl _value,
      $Res Function(_$CheckedCallRatesImpl) _then)
      : super(_value, _then);

  /// Create a copy of KeypadEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CheckedCallRatesImpl implements _CheckedCallRates {
  const _$CheckedCallRatesImpl();

  @override
  String toString() {
    return 'KeypadEvent.checkCallRates()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CheckedCallRatesImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<ContactModel> contacts) started,
    required TResult Function(String? value, bool makeDTMFSound)
        phoneNumberChanged,
    required TResult Function(String? value) setPhoneNumber,
    required TResult Function() deleteButtonPressed,
    required TResult Function(int position) updateCursorPosition,
    required TResult Function(String? value) searchedByDialingCode,
    required TResult Function(List<ContactModel> contacts) searchedContact,
    required TResult Function() reset,
    required TResult Function() checkCallRates,
  }) {
    return checkCallRates();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<ContactModel> contacts)? started,
    TResult? Function(String? value, bool makeDTMFSound)? phoneNumberChanged,
    TResult? Function(String? value)? setPhoneNumber,
    TResult? Function()? deleteButtonPressed,
    TResult? Function(int position)? updateCursorPosition,
    TResult? Function(String? value)? searchedByDialingCode,
    TResult? Function(List<ContactModel> contacts)? searchedContact,
    TResult? Function()? reset,
    TResult? Function()? checkCallRates,
  }) {
    return checkCallRates?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<ContactModel> contacts)? started,
    TResult Function(String? value, bool makeDTMFSound)? phoneNumberChanged,
    TResult Function(String? value)? setPhoneNumber,
    TResult Function()? deleteButtonPressed,
    TResult Function(int position)? updateCursorPosition,
    TResult Function(String? value)? searchedByDialingCode,
    TResult Function(List<ContactModel> contacts)? searchedContact,
    TResult Function()? reset,
    TResult Function()? checkCallRates,
    required TResult orElse(),
  }) {
    if (checkCallRates != null) {
      return checkCallRates();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_UpdatedPhoneNumber value) phoneNumberChanged,
    required TResult Function(_ReplacedPhoneNumber value) setPhoneNumber,
    required TResult Function(_DeleteButtonPressed value) deleteButtonPressed,
    required TResult Function(_UpdateCursorPosition value) updateCursorPosition,
    required TResult Function(_SearchedByDialingCode value)
        searchedByDialingCode,
    required TResult Function(_SearchedContact value) searchedContact,
    required TResult Function(_Initial value) reset,
    required TResult Function(_CheckedCallRates value) checkCallRates,
  }) {
    return checkCallRates(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_UpdatedPhoneNumber value)? phoneNumberChanged,
    TResult? Function(_ReplacedPhoneNumber value)? setPhoneNumber,
    TResult? Function(_DeleteButtonPressed value)? deleteButtonPressed,
    TResult? Function(_UpdateCursorPosition value)? updateCursorPosition,
    TResult? Function(_SearchedByDialingCode value)? searchedByDialingCode,
    TResult? Function(_SearchedContact value)? searchedContact,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_CheckedCallRates value)? checkCallRates,
  }) {
    return checkCallRates?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_UpdatedPhoneNumber value)? phoneNumberChanged,
    TResult Function(_ReplacedPhoneNumber value)? setPhoneNumber,
    TResult Function(_DeleteButtonPressed value)? deleteButtonPressed,
    TResult Function(_UpdateCursorPosition value)? updateCursorPosition,
    TResult Function(_SearchedByDialingCode value)? searchedByDialingCode,
    TResult Function(_SearchedContact value)? searchedContact,
    TResult Function(_Initial value)? reset,
    TResult Function(_CheckedCallRates value)? checkCallRates,
    required TResult orElse(),
  }) {
    if (checkCallRates != null) {
      return checkCallRates(this);
    }
    return orElse();
  }
}

abstract class _CheckedCallRates implements KeypadEvent {
  const factory _CheckedCallRates() = _$CheckedCallRatesImpl;
}

/// @nodoc
mixin _$KeypadState {
  DialerPadInput get phoneNumber => throw _privateConstructorUsedError;
  CountryModel? get country => throw _privateConstructorUsedError;
  FormzSubmissionStatus get searchStatus => throw _privateConstructorUsedError;
  String? get message => throw _privateConstructorUsedError;
  bool get isCallRatesLoading => throw _privateConstructorUsedError;
  String? get callRates => throw _privateConstructorUsedError;
  String? get callRatesErrorMessage => throw _privateConstructorUsedError;
  List<ContactModel> get contacts => throw _privateConstructorUsedError;
  int get cursorPosition => throw _privateConstructorUsedError;

  /// Create a copy of KeypadState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $KeypadStateCopyWith<KeypadState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $KeypadStateCopyWith<$Res> {
  factory $KeypadStateCopyWith(
          KeypadState value, $Res Function(KeypadState) then) =
      _$KeypadStateCopyWithImpl<$Res, KeypadState>;
  @useResult
  $Res call(
      {DialerPadInput phoneNumber,
      CountryModel? country,
      FormzSubmissionStatus searchStatus,
      String? message,
      bool isCallRatesLoading,
      String? callRates,
      String? callRatesErrorMessage,
      List<ContactModel> contacts,
      int cursorPosition});

  $CountryModelCopyWith<$Res>? get country;
}

/// @nodoc
class _$KeypadStateCopyWithImpl<$Res, $Val extends KeypadState>
    implements $KeypadStateCopyWith<$Res> {
  _$KeypadStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of KeypadState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phoneNumber = null,
    Object? country = freezed,
    Object? searchStatus = null,
    Object? message = freezed,
    Object? isCallRatesLoading = null,
    Object? callRates = freezed,
    Object? callRatesErrorMessage = freezed,
    Object? contacts = null,
    Object? cursorPosition = null,
  }) {
    return _then(_value.copyWith(
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as DialerPadInput,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as CountryModel?,
      searchStatus: null == searchStatus
          ? _value.searchStatus
          : searchStatus // ignore: cast_nullable_to_non_nullable
              as FormzSubmissionStatus,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      isCallRatesLoading: null == isCallRatesLoading
          ? _value.isCallRatesLoading
          : isCallRatesLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      callRates: freezed == callRates
          ? _value.callRates
          : callRates // ignore: cast_nullable_to_non_nullable
              as String?,
      callRatesErrorMessage: freezed == callRatesErrorMessage
          ? _value.callRatesErrorMessage
          : callRatesErrorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      contacts: null == contacts
          ? _value.contacts
          : contacts // ignore: cast_nullable_to_non_nullable
              as List<ContactModel>,
      cursorPosition: null == cursorPosition
          ? _value.cursorPosition
          : cursorPosition // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }

  /// Create a copy of KeypadState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CountryModelCopyWith<$Res>? get country {
    if (_value.country == null) {
      return null;
    }

    return $CountryModelCopyWith<$Res>(_value.country!, (value) {
      return _then(_value.copyWith(country: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$KeypadStateImplCopyWith<$Res>
    implements $KeypadStateCopyWith<$Res> {
  factory _$$KeypadStateImplCopyWith(
          _$KeypadStateImpl value, $Res Function(_$KeypadStateImpl) then) =
      __$$KeypadStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DialerPadInput phoneNumber,
      CountryModel? country,
      FormzSubmissionStatus searchStatus,
      String? message,
      bool isCallRatesLoading,
      String? callRates,
      String? callRatesErrorMessage,
      List<ContactModel> contacts,
      int cursorPosition});

  @override
  $CountryModelCopyWith<$Res>? get country;
}

/// @nodoc
class __$$KeypadStateImplCopyWithImpl<$Res>
    extends _$KeypadStateCopyWithImpl<$Res, _$KeypadStateImpl>
    implements _$$KeypadStateImplCopyWith<$Res> {
  __$$KeypadStateImplCopyWithImpl(
      _$KeypadStateImpl _value, $Res Function(_$KeypadStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of KeypadState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phoneNumber = null,
    Object? country = freezed,
    Object? searchStatus = null,
    Object? message = freezed,
    Object? isCallRatesLoading = null,
    Object? callRates = freezed,
    Object? callRatesErrorMessage = freezed,
    Object? contacts = null,
    Object? cursorPosition = null,
  }) {
    return _then(_$KeypadStateImpl(
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as DialerPadInput,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as CountryModel?,
      searchStatus: null == searchStatus
          ? _value.searchStatus
          : searchStatus // ignore: cast_nullable_to_non_nullable
              as FormzSubmissionStatus,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      isCallRatesLoading: null == isCallRatesLoading
          ? _value.isCallRatesLoading
          : isCallRatesLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      callRates: freezed == callRates
          ? _value.callRates
          : callRates // ignore: cast_nullable_to_non_nullable
              as String?,
      callRatesErrorMessage: freezed == callRatesErrorMessage
          ? _value.callRatesErrorMessage
          : callRatesErrorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      contacts: null == contacts
          ? _value._contacts
          : contacts // ignore: cast_nullable_to_non_nullable
              as List<ContactModel>,
      cursorPosition: null == cursorPosition
          ? _value.cursorPosition
          : cursorPosition // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$KeypadStateImpl extends _KeypadState {
  _$KeypadStateImpl(
      {this.phoneNumber = const DialerPadInput.pure(),
      this.country,
      this.searchStatus = FormzSubmissionStatus.initial,
      this.message,
      this.isCallRatesLoading = false,
      this.callRates,
      this.callRatesErrorMessage,
      final List<ContactModel> contacts = const [],
      this.cursorPosition = 0})
      : _contacts = contacts,
        super._();

  @override
  @JsonKey()
  final DialerPadInput phoneNumber;
  @override
  final CountryModel? country;
  @override
  @JsonKey()
  final FormzSubmissionStatus searchStatus;
  @override
  final String? message;
  @override
  @JsonKey()
  final bool isCallRatesLoading;
  @override
  final String? callRates;
  @override
  final String? callRatesErrorMessage;
  final List<ContactModel> _contacts;
  @override
  @JsonKey()
  List<ContactModel> get contacts {
    if (_contacts is EqualUnmodifiableListView) return _contacts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_contacts);
  }

  @override
  @JsonKey()
  final int cursorPosition;

  @override
  String toString() {
    return 'KeypadState(phoneNumber: $phoneNumber, country: $country, searchStatus: $searchStatus, message: $message, isCallRatesLoading: $isCallRatesLoading, callRates: $callRates, callRatesErrorMessage: $callRatesErrorMessage, contacts: $contacts, cursorPosition: $cursorPosition)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$KeypadStateImpl &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.searchStatus, searchStatus) ||
                other.searchStatus == searchStatus) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.isCallRatesLoading, isCallRatesLoading) ||
                other.isCallRatesLoading == isCallRatesLoading) &&
            (identical(other.callRates, callRates) ||
                other.callRates == callRates) &&
            (identical(other.callRatesErrorMessage, callRatesErrorMessage) ||
                other.callRatesErrorMessage == callRatesErrorMessage) &&
            const DeepCollectionEquality().equals(other._contacts, _contacts) &&
            (identical(other.cursorPosition, cursorPosition) ||
                other.cursorPosition == cursorPosition));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      phoneNumber,
      country,
      searchStatus,
      message,
      isCallRatesLoading,
      callRates,
      callRatesErrorMessage,
      const DeepCollectionEquality().hash(_contacts),
      cursorPosition);

  /// Create a copy of KeypadState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$KeypadStateImplCopyWith<_$KeypadStateImpl> get copyWith =>
      __$$KeypadStateImplCopyWithImpl<_$KeypadStateImpl>(this, _$identity);
}

abstract class _KeypadState extends KeypadState {
  factory _KeypadState(
      {final DialerPadInput phoneNumber,
      final CountryModel? country,
      final FormzSubmissionStatus searchStatus,
      final String? message,
      final bool isCallRatesLoading,
      final String? callRates,
      final String? callRatesErrorMessage,
      final List<ContactModel> contacts,
      final int cursorPosition}) = _$KeypadStateImpl;
  _KeypadState._() : super._();

  @override
  DialerPadInput get phoneNumber;
  @override
  CountryModel? get country;
  @override
  FormzSubmissionStatus get searchStatus;
  @override
  String? get message;
  @override
  bool get isCallRatesLoading;
  @override
  String? get callRates;
  @override
  String? get callRatesErrorMessage;
  @override
  List<ContactModel> get contacts;
  @override
  int get cursorPosition;

  /// Create a copy of KeypadState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$KeypadStateImplCopyWith<_$KeypadStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
