part of 'keypad_bloc.dart';

@freezed
class KeypadState with _$KeypadState, FormzMixin {
  factory KeypadState({
    @Default(DialerPadInput.pure()) DialerPadInput phoneNumber,
    CountryModel? country,
    @Default(FormzSubmissionStatus.initial) FormzSubmissionStatus searchStatus,
    String? message,
    @Default(false) bool isCallRatesLoading,
    String? callRates,
    String? callRatesErrorMessage,
    @Default([]) List<ContactModel> contacts,
    @Default(0) int cursorPosition,
  }) = _KeypadState;

  factory KeypadState.initial() => KeypadState();

  KeypadState._();

  @override
  List<FormzInput<String, dynamic>> get inputs => [phoneNumber];

  KeypadState searchForContact({String? searchTerm}) {
    if (searchTerm != null && searchTerm.isNotEmpty && searchTerm.length >= 2) {
      final query = searchTerm.toLowerCase();

      final searchResults = contacts.where((e) {
        return e.name.toLowerCase().contains(query) ||
            e.phoneNumber.contains(query);
      }).toList();

      return copyWith(
        contacts: searchResults,
        // searchTerm: searchTerm,
        searchStatus: FormzSubmissionStatus.success,
      );
    }

    return copyWith(
      contacts: contacts,
      // searchTerm: searchTerm,
      searchStatus: FormzSubmissionStatus.success,
    );
  }
}
