import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:bloc_concurrency/bloc_concurrency.dart';
import 'package:countries/countries.dart';
import 'package:flutter_dtmf/dtmf.dart';
import 'package:formz/formz.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:froggytalk/call_rates/call_rates.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:froggytalk/dialer/data/models/dialer_pad_input.dart';
import 'package:rxdart/rxdart.dart';
import 'package:utils/utils.dart';

part 'keypad_bloc.freezed.dart';
part 'keypad_event.dart';
part 'keypad_state.dart';

// ignore: unused_element
EventTransformer<KeypadEvent> _debounce<KeypadEvent>() {
  return (events, mapper) =>
      events.debounceTime(const Duration(milliseconds: 400)).switchMap(mapper);
}

class KeypadBloc extends Bloc<KeypadEvent, KeypadState> {
  KeypadBloc({
    required PhoneNumberService phoneNumberService,
    required CallRatesRepository callRatesRepository,
  })  : _phoneNumberService = phoneNumberService,
        _callRatesRepository = callRatesRepository,
        super(KeypadState.initial()) {
    on<_Started>(_onStarted);
    on<_UpdatedPhoneNumber>(_onUpdatedPhoneNumber, transformer: sequential());
    on<_SearchedByDialingCode>(_onSearchedByDialingCode);
    on<_SearchedContact>(_onSearchedContact, transformer: concurrent());
    on<_Initial>(_onInitial);
    on<_DeleteButtonPressed>(_onDeleteButtonPressed);
    on<_UpdateCursorPosition>(_onUpdateCursorPosition);
    on<_CheckedCallRates>(_onCheckedCallRates);
    on<_ReplacedPhoneNumber>(_onReplacedPhoneNumber);
  }

  final PhoneNumberService _phoneNumberService;
  final CallRatesRepository _callRatesRepository;

  FutureOr<void> _onInitial(
    _Initial event,
    Emitter<KeypadState> emit,
  ) {
    emit(KeypadState.initial());
  }

  FutureOr<void> _onStarted(_Started event, Emitter<KeypadState> emit) {
    emit(
      state.copyWith(
        country: null,
        contacts: event.contacts,
      ),
    );
  }

  Future<void> _onSearchedByDialingCode(
    _SearchedByDialingCode event,
    Emitter<KeypadState> emit,
  ) async {
    final phoneNumber = state.phoneNumber.value;
    final findByDialingCode = _findByDialingCode(phoneNumber);
    final callRatePerMinute =
        await _fetchCallRates(findByDialingCode?.code ?? '');
    emit(
      state.copyWith(
        country: findByDialingCode,
        callRates: callRatePerMinute,
        callRatesErrorMessage: callRatePerMinute == null ? 'Error' : null,
      ),
    );
  }

  CountryModel? _findByDialingCode(String phoneNumber) {
    final replaceFirst = phoneNumber.startsWith('00')
        ? phoneNumber.replaceFirst('00', '+')
        : phoneNumber;
    final retrieveCountryByPhoneNumber =
        _phoneNumberService.findCountryFromNumber(replaceFirst);
    return retrieveCountryByPhoneNumber;
  }

  FutureOr<void> _onSearchedContact(
    _SearchedContact event,
    Emitter<KeypadState> emit,
  ) {
    emit(state.copyWith(searchStatus: FormzSubmissionStatus.inProgress));
    emit(state.searchForContact(searchTerm: state.phoneNumber.value));
  }

  FutureOr<void> _onUpdatedPhoneNumber(
    _UpdatedPhoneNumber event,
    Emitter<KeypadState> emit,
  ) async {
    final eventText = event.value;
    if (eventText == null) {
      emit(
        state.copyWith(
          searchStatus: FormzSubmissionStatus.initial,
          phoneNumber: const DialerPadInput.pure(),
        ),
      );

      return;
    }

    final currentText = state.phoneNumber.value;
    final cursorPos = state.cursorPosition;

    final newText = currentText.substring(0, cursorPos) +
        eventText +
        currentText.substring(cursorPos);

    final isFullNumberSet = eventText.length == newText.length;
    // final country = _findByDialingCode(currentText);
    // final callRatePerMinute = await _fetchCallRates(country?.code ?? '');

    // play dtmf sound
    if (event.makeDTMFSound) {
      try {
        unawaited(
          Dtmf.playTone(
            digits: eventText,
            samplingRate: 80000,
            forceMaxVolume: true,
          ),
        );
      } catch (e) {
        FroggyLogger.error('Error for number');
      }
    }

    // vibrate(const Duration(milliseconds: 100));

    final updatedPhoneNumber = DialerPadInput.dirty(newText);
    emit(
      state.copyWith(
        searchStatus: FormzSubmissionStatus.initial,
        phoneNumber: updatedPhoneNumber,
        cursorPosition: isFullNumberSet ? newText.length : cursorPos + 1,
        // country: country,
        // isCallRatesLoading: false,
        // callRates: callRatePerMinute,
        // callRatesErrorMessage: callRatePerMinute == null ? 'Error' : null,
      ),
    );
  }

  FutureOr<void> _onReplacedPhoneNumber(
    _ReplacedPhoneNumber event,
    Emitter<KeypadState> emit,
  ) async {
    final eventText = event.value;
    if (eventText == null) {
      emit(
        state.copyWith(
          searchStatus: FormzSubmissionStatus.initial,
          phoneNumber: const DialerPadInput.pure(),
        ),
      );

      return;
    }

    emit(
      state.copyWith(
        isCallRatesLoading: true,
        callRates: null,
        callRatesErrorMessage: null,
      ),
    );

    final currentText = eventText;
    final cursorPos = state.cursorPosition;
    final isFullNumberSet = eventText.length == currentText.length;
    final country = _findByDialingCode(currentText);
    final callRatePerMinute = await _fetchCallRates(country?.code ?? '');

    emit(
      state.copyWith(
        searchStatus: FormzSubmissionStatus.initial,
        phoneNumber: DialerPadInput.dirty(currentText),
        cursorPosition: isFullNumberSet ? currentText.length : cursorPos + 1,
        callRates: callRatePerMinute,
        isCallRatesLoading: false,
        country: country,
        callRatesErrorMessage: callRatePerMinute == null ? 'Error' : null,
      ),
    );

    vibrate(const Duration(milliseconds: 200));
  }

  FutureOr<void> _onDeleteButtonPressed(
    _DeleteButtonPressed event,
    Emitter<KeypadState> emit,
  ) {
    final currentText = state.phoneNumber.value;
    final cursorPos = state.cursorPosition;

    if (cursorPos > 0 && cursorPos <= currentText.length) {
      final updatedText = currentText.substring(0, cursorPos - 1) +
          currentText.substring(cursorPos);

      emit(
        state.copyWith(
          phoneNumber: DialerPadInput.dirty(updatedText),
          cursorPosition: cursorPos - 1,
        ),
      );
    } else {
      // Log or handle invalid cursor positions if necessary
    }
  }

  FutureOr<void> _onUpdateCursorPosition(
    _UpdateCursorPosition event,
    Emitter<KeypadState> emit,
  ) {
    emit(state.copyWith(cursorPosition: event.position));
  }

  Future<String?> _fetchCallRates(String countryCode) async {
    final callRates =
        await _callRatesRepository.execute(countryCode: countryCode);

    return callRates.fold(
      (error) {
        return null;
      },
      (response) {
        return response.data?.attributes?.rateMobilePerMinute?.amount;
      },
    );
  }

  FutureOr<void> _onCheckedCallRates(
    _CheckedCallRates event,
    Emitter<KeypadState> emit,
  ) async {
    final countryCode = state.country?.code;

    if (countryCode == null) {
      return;
    }

    emit(
      state.copyWith(
        isCallRatesLoading: true,
        callRates: null,
        callRatesErrorMessage: null,
      ),
    );

    final callRatePerMinute = await _fetchCallRates(countryCode);

    if (callRatePerMinute == null) {
      emit(
        state.copyWith(
          callRates: null,
          callRatesErrorMessage: 'Error',
          isCallRatesLoading: false,
        ),
      );
    } else {
      emit(
        state.copyWith(
          callRates: callRatePerMinute,
          callRatesErrorMessage: null,
          isCallRatesLoading: false,
        ),
      );
    }
  }
}
