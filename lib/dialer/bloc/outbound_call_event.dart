part of 'outbound_call_bloc.dart';

@freezed
class OutboundCallEvent with _$OutboundCallEvent {
  /// Creates a new [OutboundCallEvent]
  /// representing the initiation of an outbound call.
  ///
  /// The [OutboundCallEvent.started] event is triggered when
  /// a call is successfully started.
  /// Example:
  /// ```dart
  /// final event = OutboundCallEvent.started(call);
  /// ```
  const factory OutboundCallEvent.started(Call call) = _Started;

  /// Creates an [OutboundCallEvent] indicating that the call has been stopped.
  ///
  /// Example:
  /// ```dart
  /// final event = OutboundCallEvent.stopped();
  /// ```
  const factory OutboundCallEvent.stopped() = _Stopped;

  /// Creates an [OutboundCallEvent] to initiate a new call.
  ///
  /// Example:
  /// ```dart
  /// final event = OutboundCallEvent.makeCall();
  /// ```
  const factory OutboundCallEvent.makeCall() = _MakeCall;

  /// Creates an [OutboundCallEvent] to end the current call.
  ///
  /// Example:
  /// ```dart
  /// final event = OutboundCallEvent.endCall();
  /// ```
  const factory OutboundCallEvent.endCall() = _EndCall;

  /// Creates an [OutboundCallEvent] to handle changes in the call status.
  ///
  /// Example:
  /// ```dart
  /// final event = OutboundCallEvent.onCallStatusChanged();
  /// ```
  const factory OutboundCallEvent.onCallStatusChanged() = _OnCallStatusChanged;

  /// Creates an [OutboundCallEvent] to handle changes in the mute status.
  ///
  /// Parameters:
  /// - [mute]: Indicates whether the call is muted. Defaults to `false`.
  ///
  /// Example:
  /// ```dart
  /// final event = OutboundCallEvent.onMuteStatusChanged(mute: true);
  /// ```
  const factory OutboundCallEvent.onMuteStatusChanged({
    @Default(false) bool mute,
  }) = _OnMuteStatusChanged;

  /// Creates an [OutboundCallEvent] to handle changes in the speaker status.
  ///
  /// Parameters:
  /// - [speaker]: Indicates whether the speaker is enabled.
  ///   Defaults to `false`.
  ///
  /// Example:
  /// ```dart
  /// final event = OutboundCallEvent.onSpeakerStatusChanged(speaker: true);
  /// ```
  const factory OutboundCallEvent.onSpeakerStatusChanged({
    @Default(false) bool speaker,
  }) = _OnSpeakerStatusChanged;

  /// Creates an [OutboundCallEvent] to handle changes in the hold status.
  ///
  /// Parameters:
  /// - [hold]: Indicates whether the call is on hold. Defaults to `false`.
  ///
  /// Example:
  /// ```dart
  /// final event = OutboundCallEvent.onHoldStatusChanged(hold: true);
  /// ```
  const factory OutboundCallEvent.onHoldStatusChanged({
    @Default(false) bool hold,
  }) = _OnHoldStatusChanged;

  /// Creates an [OutboundCallEvent] when a DTMF key is pressed during the call.
  ///
  /// Parameters:
  /// - [key]: The DTMF key that was pressed.
  ///
  /// Example:
  /// ```dart
  /// final event = OutboundCallEvent.onDTMFKeyPressed(key: '5');
  /// ```
  const factory OutboundCallEvent.onDTMFKeyPressed({
    required String key,
  }) = _OnDTMFKeyPressed;

  /// Creates an [OutboundCallEvent] when the phone number is updated.
  ///
  /// Parameters:
  /// - [number]: The updated phone number.
  ///
  /// Example:
  /// ```dart
  /// final event = OutboundCallEvent.onPhoneNumberUpdated(
  ///   number: '+1234567890',
  /// );
  /// ```
  const factory OutboundCallEvent.onPhoneNumberUpdated({
    required String number,
  }) = _OnPhoneNumberUpdated;
}
