part of 'dialer_bloc.dart';

@freezed
class DialerEvent with _$DialerEvent {
  const factory DialerEvent.register({
    String? sipUsername,
    String? sipPassword,
  }) = _Started;

  const factory DialerEvent.reset() = _Initial;

  const factory DialerEvent.reloadAsterisk() = _RegisteredAgain;

  const factory DialerEvent.callStarted({
    required String phoneNumber,
  }) = _CallStarted;

  const factory DialerEvent.callStopped() = _CallStopped;

  const factory DialerEvent.hangedup() = _HangedUp;

  const factory DialerEvent.handledCallStream({
    required sip_ua.CallState event,
  }) = _HandledCallStream;

  const factory DialerEvent.updatedDialerStatus({
    @Default(DialerStatus.unknown)
        DialerStatus status,
  }) = _UpdatedDialerStatus;

  const factory DialerEvent.updatedCall({
    sip_ua.Call? call,
  }) = _UpdatedCall;

  const factory DialerEvent.updatedOnHoldStatus({
    required bool isOnHold,
    String? onHoldOriginator,
  }) = _UpdatedOnHoldStatus;

  /// Forces the current call to end completely, cleaning up all
  /// resources and resetting state. Useful for cleaning up stuck
  /// calls or when abnormal call termination is detected.
  const factory DialerEvent.forceEndCurrentCall() = _ForceEndCurrentCall;

  // const factory DialerEvent.updatedMuteStatus({
  //   required bool isMuted,
  // }) = _UpdatedMuteStatus;

  const factory DialerEvent.updatedCallStateEnum({
    required sip_ua.CallStateEnum callStateEnum,
  }) = _UpdatedCallStateEnum;

  // const factory DialerEvent.updatedDialpadStatus({
  //   required bool isNumPadShowing,
  // }) = _UpdatedDialpadStatus;

  const factory DialerEvent.updatedRegistrationStatus({
    @Default(sip_ua.RegistrationStateEnum.NONE)
        sip_ua.RegistrationStateEnum status,
  }) = _UpdatedRegistrationStatus;

  const factory DialerEvent.sendErrorMessage({
    String? message,
  }) = _HandledMessageRequest;

  const factory DialerEvent.timerStarted() = _StartedTimer;

  const factory DialerEvent.timerPaused({
    required int value,
  }) = _PausedTimer;

  const factory DialerEvent.timerStopped() = _StoppedTimer;

  const factory DialerEvent.timerUpdated({
    @Default(Duration.zero)
        Duration elapsed,
  }) = _UpdatedTimer;

  const factory DialerEvent.holdCall() = _HoldCall;

  const factory DialerEvent.unholdCall() = _UnholdCall;

  const factory DialerEvent.switchToBluetoothHeadset() =
      _SwitchedToBluetooth;

  const factory DialerEvent.switchToWiredHeadet() =
      _SwitchedToWiredHeadsetz;

  const factory DialerEvent.disableSpeakerPhone() =
      _SwitchedSpeakerOff;

  const factory DialerEvent.switchSpeakerPhoneOn() =
      _SwitchSpeakerphoneOn;

  const factory DialerEvent.muteOn() = _MuteOn;

  const factory DialerEvent.muteOff() = _MuteOff;

  const factory DialerEvent.handledAllHeadsetStreams() =
      _HandledAllHeadsetStreams;

  const factory DialerEvent.sendDTMF({
    required String value,
  }) = _SendDTMFValue;

  const factory DialerEvent.audioRoutingChanged({
    required AudioOutputMode mode,
  }) = _AudioRoutingChanged;
}
