// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'call_timer_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CallTimerEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() timerStarted,
    required TResult Function(DateTime startTime) timerStartedWithTime,
    required TResult Function() timerStopped,
    required TResult Function() timerPaused,
    required TResult Function() timerResumed,
    required TResult Function() timerRestarted,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() timerReset,
    required TResult Function(Duration duration, DateTime timestamp)
        syncFromForegroundService,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? timerStarted,
    TResult? Function(DateTime startTime)? timerStartedWithTime,
    TResult? Function()? timerStopped,
    TResult? Function()? timerPaused,
    TResult? Function()? timerResumed,
    TResult? Function()? timerRestarted,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? timerReset,
    TResult? Function(Duration duration, DateTime timestamp)?
        syncFromForegroundService,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? timerStarted,
    TResult Function(DateTime startTime)? timerStartedWithTime,
    TResult Function()? timerStopped,
    TResult Function()? timerPaused,
    TResult Function()? timerResumed,
    TResult Function()? timerRestarted,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? timerReset,
    TResult Function(Duration duration, DateTime timestamp)?
        syncFromForegroundService,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_TimerStarted value) timerStarted,
    required TResult Function(_TimerStartedWithTime value) timerStartedWithTime,
    required TResult Function(_TimerStopped value) timerStopped,
    required TResult Function(_TimerPaused value) timerPaused,
    required TResult Function(_TimerResumed value) timerResumed,
    required TResult Function(_TimerRestarted value) timerRestarted,
    required TResult Function(_TimerUpdated value) timerUpdated,
    required TResult Function(_TimerReset value) timerReset,
    required TResult Function(_SyncFromForegroundService value)
        syncFromForegroundService,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_TimerStarted value)? timerStarted,
    TResult? Function(_TimerStartedWithTime value)? timerStartedWithTime,
    TResult? Function(_TimerStopped value)? timerStopped,
    TResult? Function(_TimerPaused value)? timerPaused,
    TResult? Function(_TimerResumed value)? timerResumed,
    TResult? Function(_TimerRestarted value)? timerRestarted,
    TResult? Function(_TimerUpdated value)? timerUpdated,
    TResult? Function(_TimerReset value)? timerReset,
    TResult? Function(_SyncFromForegroundService value)?
        syncFromForegroundService,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_TimerStarted value)? timerStarted,
    TResult Function(_TimerStartedWithTime value)? timerStartedWithTime,
    TResult Function(_TimerStopped value)? timerStopped,
    TResult Function(_TimerPaused value)? timerPaused,
    TResult Function(_TimerResumed value)? timerResumed,
    TResult Function(_TimerRestarted value)? timerRestarted,
    TResult Function(_TimerUpdated value)? timerUpdated,
    TResult Function(_TimerReset value)? timerReset,
    TResult Function(_SyncFromForegroundService value)?
        syncFromForegroundService,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CallTimerEventCopyWith<$Res> {
  factory $CallTimerEventCopyWith(
          CallTimerEvent value, $Res Function(CallTimerEvent) then) =
      _$CallTimerEventCopyWithImpl<$Res, CallTimerEvent>;
}

/// @nodoc
class _$CallTimerEventCopyWithImpl<$Res, $Val extends CallTimerEvent>
    implements $CallTimerEventCopyWith<$Res> {
  _$CallTimerEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CallTimerEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$TimerStartedImplCopyWith<$Res> {
  factory _$$TimerStartedImplCopyWith(
          _$TimerStartedImpl value, $Res Function(_$TimerStartedImpl) then) =
      __$$TimerStartedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$TimerStartedImplCopyWithImpl<$Res>
    extends _$CallTimerEventCopyWithImpl<$Res, _$TimerStartedImpl>
    implements _$$TimerStartedImplCopyWith<$Res> {
  __$$TimerStartedImplCopyWithImpl(
      _$TimerStartedImpl _value, $Res Function(_$TimerStartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CallTimerEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$TimerStartedImpl implements _TimerStarted {
  const _$TimerStartedImpl();

  @override
  String toString() {
    return 'CallTimerEvent.timerStarted()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$TimerStartedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() timerStarted,
    required TResult Function(DateTime startTime) timerStartedWithTime,
    required TResult Function() timerStopped,
    required TResult Function() timerPaused,
    required TResult Function() timerResumed,
    required TResult Function() timerRestarted,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() timerReset,
    required TResult Function(Duration duration, DateTime timestamp)
        syncFromForegroundService,
  }) {
    return timerStarted();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? timerStarted,
    TResult? Function(DateTime startTime)? timerStartedWithTime,
    TResult? Function()? timerStopped,
    TResult? Function()? timerPaused,
    TResult? Function()? timerResumed,
    TResult? Function()? timerRestarted,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? timerReset,
    TResult? Function(Duration duration, DateTime timestamp)?
        syncFromForegroundService,
  }) {
    return timerStarted?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? timerStarted,
    TResult Function(DateTime startTime)? timerStartedWithTime,
    TResult Function()? timerStopped,
    TResult Function()? timerPaused,
    TResult Function()? timerResumed,
    TResult Function()? timerRestarted,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? timerReset,
    TResult Function(Duration duration, DateTime timestamp)?
        syncFromForegroundService,
    required TResult orElse(),
  }) {
    if (timerStarted != null) {
      return timerStarted();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_TimerStarted value) timerStarted,
    required TResult Function(_TimerStartedWithTime value) timerStartedWithTime,
    required TResult Function(_TimerStopped value) timerStopped,
    required TResult Function(_TimerPaused value) timerPaused,
    required TResult Function(_TimerResumed value) timerResumed,
    required TResult Function(_TimerRestarted value) timerRestarted,
    required TResult Function(_TimerUpdated value) timerUpdated,
    required TResult Function(_TimerReset value) timerReset,
    required TResult Function(_SyncFromForegroundService value)
        syncFromForegroundService,
  }) {
    return timerStarted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_TimerStarted value)? timerStarted,
    TResult? Function(_TimerStartedWithTime value)? timerStartedWithTime,
    TResult? Function(_TimerStopped value)? timerStopped,
    TResult? Function(_TimerPaused value)? timerPaused,
    TResult? Function(_TimerResumed value)? timerResumed,
    TResult? Function(_TimerRestarted value)? timerRestarted,
    TResult? Function(_TimerUpdated value)? timerUpdated,
    TResult? Function(_TimerReset value)? timerReset,
    TResult? Function(_SyncFromForegroundService value)?
        syncFromForegroundService,
  }) {
    return timerStarted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_TimerStarted value)? timerStarted,
    TResult Function(_TimerStartedWithTime value)? timerStartedWithTime,
    TResult Function(_TimerStopped value)? timerStopped,
    TResult Function(_TimerPaused value)? timerPaused,
    TResult Function(_TimerResumed value)? timerResumed,
    TResult Function(_TimerRestarted value)? timerRestarted,
    TResult Function(_TimerUpdated value)? timerUpdated,
    TResult Function(_TimerReset value)? timerReset,
    TResult Function(_SyncFromForegroundService value)?
        syncFromForegroundService,
    required TResult orElse(),
  }) {
    if (timerStarted != null) {
      return timerStarted(this);
    }
    return orElse();
  }
}

abstract class _TimerStarted implements CallTimerEvent {
  const factory _TimerStarted() = _$TimerStartedImpl;
}

/// @nodoc
abstract class _$$TimerStartedWithTimeImplCopyWith<$Res> {
  factory _$$TimerStartedWithTimeImplCopyWith(_$TimerStartedWithTimeImpl value,
          $Res Function(_$TimerStartedWithTimeImpl) then) =
      __$$TimerStartedWithTimeImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime startTime});
}

/// @nodoc
class __$$TimerStartedWithTimeImplCopyWithImpl<$Res>
    extends _$CallTimerEventCopyWithImpl<$Res, _$TimerStartedWithTimeImpl>
    implements _$$TimerStartedWithTimeImplCopyWith<$Res> {
  __$$TimerStartedWithTimeImplCopyWithImpl(_$TimerStartedWithTimeImpl _value,
      $Res Function(_$TimerStartedWithTimeImpl) _then)
      : super(_value, _then);

  /// Create a copy of CallTimerEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? startTime = null,
  }) {
    return _then(_$TimerStartedWithTimeImpl(
      startTime: null == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$TimerStartedWithTimeImpl implements _TimerStartedWithTime {
  const _$TimerStartedWithTimeImpl({required this.startTime});

  @override
  final DateTime startTime;

  @override
  String toString() {
    return 'CallTimerEvent.timerStartedWithTime(startTime: $startTime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TimerStartedWithTimeImpl &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime));
  }

  @override
  int get hashCode => Object.hash(runtimeType, startTime);

  /// Create a copy of CallTimerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TimerStartedWithTimeImplCopyWith<_$TimerStartedWithTimeImpl>
      get copyWith =>
          __$$TimerStartedWithTimeImplCopyWithImpl<_$TimerStartedWithTimeImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() timerStarted,
    required TResult Function(DateTime startTime) timerStartedWithTime,
    required TResult Function() timerStopped,
    required TResult Function() timerPaused,
    required TResult Function() timerResumed,
    required TResult Function() timerRestarted,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() timerReset,
    required TResult Function(Duration duration, DateTime timestamp)
        syncFromForegroundService,
  }) {
    return timerStartedWithTime(startTime);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? timerStarted,
    TResult? Function(DateTime startTime)? timerStartedWithTime,
    TResult? Function()? timerStopped,
    TResult? Function()? timerPaused,
    TResult? Function()? timerResumed,
    TResult? Function()? timerRestarted,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? timerReset,
    TResult? Function(Duration duration, DateTime timestamp)?
        syncFromForegroundService,
  }) {
    return timerStartedWithTime?.call(startTime);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? timerStarted,
    TResult Function(DateTime startTime)? timerStartedWithTime,
    TResult Function()? timerStopped,
    TResult Function()? timerPaused,
    TResult Function()? timerResumed,
    TResult Function()? timerRestarted,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? timerReset,
    TResult Function(Duration duration, DateTime timestamp)?
        syncFromForegroundService,
    required TResult orElse(),
  }) {
    if (timerStartedWithTime != null) {
      return timerStartedWithTime(startTime);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_TimerStarted value) timerStarted,
    required TResult Function(_TimerStartedWithTime value) timerStartedWithTime,
    required TResult Function(_TimerStopped value) timerStopped,
    required TResult Function(_TimerPaused value) timerPaused,
    required TResult Function(_TimerResumed value) timerResumed,
    required TResult Function(_TimerRestarted value) timerRestarted,
    required TResult Function(_TimerUpdated value) timerUpdated,
    required TResult Function(_TimerReset value) timerReset,
    required TResult Function(_SyncFromForegroundService value)
        syncFromForegroundService,
  }) {
    return timerStartedWithTime(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_TimerStarted value)? timerStarted,
    TResult? Function(_TimerStartedWithTime value)? timerStartedWithTime,
    TResult? Function(_TimerStopped value)? timerStopped,
    TResult? Function(_TimerPaused value)? timerPaused,
    TResult? Function(_TimerResumed value)? timerResumed,
    TResult? Function(_TimerRestarted value)? timerRestarted,
    TResult? Function(_TimerUpdated value)? timerUpdated,
    TResult? Function(_TimerReset value)? timerReset,
    TResult? Function(_SyncFromForegroundService value)?
        syncFromForegroundService,
  }) {
    return timerStartedWithTime?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_TimerStarted value)? timerStarted,
    TResult Function(_TimerStartedWithTime value)? timerStartedWithTime,
    TResult Function(_TimerStopped value)? timerStopped,
    TResult Function(_TimerPaused value)? timerPaused,
    TResult Function(_TimerResumed value)? timerResumed,
    TResult Function(_TimerRestarted value)? timerRestarted,
    TResult Function(_TimerUpdated value)? timerUpdated,
    TResult Function(_TimerReset value)? timerReset,
    TResult Function(_SyncFromForegroundService value)?
        syncFromForegroundService,
    required TResult orElse(),
  }) {
    if (timerStartedWithTime != null) {
      return timerStartedWithTime(this);
    }
    return orElse();
  }
}

abstract class _TimerStartedWithTime implements CallTimerEvent {
  const factory _TimerStartedWithTime({required final DateTime startTime}) =
      _$TimerStartedWithTimeImpl;

  DateTime get startTime;

  /// Create a copy of CallTimerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TimerStartedWithTimeImplCopyWith<_$TimerStartedWithTimeImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$TimerStoppedImplCopyWith<$Res> {
  factory _$$TimerStoppedImplCopyWith(
          _$TimerStoppedImpl value, $Res Function(_$TimerStoppedImpl) then) =
      __$$TimerStoppedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$TimerStoppedImplCopyWithImpl<$Res>
    extends _$CallTimerEventCopyWithImpl<$Res, _$TimerStoppedImpl>
    implements _$$TimerStoppedImplCopyWith<$Res> {
  __$$TimerStoppedImplCopyWithImpl(
      _$TimerStoppedImpl _value, $Res Function(_$TimerStoppedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CallTimerEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$TimerStoppedImpl implements _TimerStopped {
  const _$TimerStoppedImpl();

  @override
  String toString() {
    return 'CallTimerEvent.timerStopped()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$TimerStoppedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() timerStarted,
    required TResult Function(DateTime startTime) timerStartedWithTime,
    required TResult Function() timerStopped,
    required TResult Function() timerPaused,
    required TResult Function() timerResumed,
    required TResult Function() timerRestarted,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() timerReset,
    required TResult Function(Duration duration, DateTime timestamp)
        syncFromForegroundService,
  }) {
    return timerStopped();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? timerStarted,
    TResult? Function(DateTime startTime)? timerStartedWithTime,
    TResult? Function()? timerStopped,
    TResult? Function()? timerPaused,
    TResult? Function()? timerResumed,
    TResult? Function()? timerRestarted,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? timerReset,
    TResult? Function(Duration duration, DateTime timestamp)?
        syncFromForegroundService,
  }) {
    return timerStopped?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? timerStarted,
    TResult Function(DateTime startTime)? timerStartedWithTime,
    TResult Function()? timerStopped,
    TResult Function()? timerPaused,
    TResult Function()? timerResumed,
    TResult Function()? timerRestarted,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? timerReset,
    TResult Function(Duration duration, DateTime timestamp)?
        syncFromForegroundService,
    required TResult orElse(),
  }) {
    if (timerStopped != null) {
      return timerStopped();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_TimerStarted value) timerStarted,
    required TResult Function(_TimerStartedWithTime value) timerStartedWithTime,
    required TResult Function(_TimerStopped value) timerStopped,
    required TResult Function(_TimerPaused value) timerPaused,
    required TResult Function(_TimerResumed value) timerResumed,
    required TResult Function(_TimerRestarted value) timerRestarted,
    required TResult Function(_TimerUpdated value) timerUpdated,
    required TResult Function(_TimerReset value) timerReset,
    required TResult Function(_SyncFromForegroundService value)
        syncFromForegroundService,
  }) {
    return timerStopped(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_TimerStarted value)? timerStarted,
    TResult? Function(_TimerStartedWithTime value)? timerStartedWithTime,
    TResult? Function(_TimerStopped value)? timerStopped,
    TResult? Function(_TimerPaused value)? timerPaused,
    TResult? Function(_TimerResumed value)? timerResumed,
    TResult? Function(_TimerRestarted value)? timerRestarted,
    TResult? Function(_TimerUpdated value)? timerUpdated,
    TResult? Function(_TimerReset value)? timerReset,
    TResult? Function(_SyncFromForegroundService value)?
        syncFromForegroundService,
  }) {
    return timerStopped?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_TimerStarted value)? timerStarted,
    TResult Function(_TimerStartedWithTime value)? timerStartedWithTime,
    TResult Function(_TimerStopped value)? timerStopped,
    TResult Function(_TimerPaused value)? timerPaused,
    TResult Function(_TimerResumed value)? timerResumed,
    TResult Function(_TimerRestarted value)? timerRestarted,
    TResult Function(_TimerUpdated value)? timerUpdated,
    TResult Function(_TimerReset value)? timerReset,
    TResult Function(_SyncFromForegroundService value)?
        syncFromForegroundService,
    required TResult orElse(),
  }) {
    if (timerStopped != null) {
      return timerStopped(this);
    }
    return orElse();
  }
}

abstract class _TimerStopped implements CallTimerEvent {
  const factory _TimerStopped() = _$TimerStoppedImpl;
}

/// @nodoc
abstract class _$$TimerPausedImplCopyWith<$Res> {
  factory _$$TimerPausedImplCopyWith(
          _$TimerPausedImpl value, $Res Function(_$TimerPausedImpl) then) =
      __$$TimerPausedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$TimerPausedImplCopyWithImpl<$Res>
    extends _$CallTimerEventCopyWithImpl<$Res, _$TimerPausedImpl>
    implements _$$TimerPausedImplCopyWith<$Res> {
  __$$TimerPausedImplCopyWithImpl(
      _$TimerPausedImpl _value, $Res Function(_$TimerPausedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CallTimerEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$TimerPausedImpl implements _TimerPaused {
  const _$TimerPausedImpl();

  @override
  String toString() {
    return 'CallTimerEvent.timerPaused()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$TimerPausedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() timerStarted,
    required TResult Function(DateTime startTime) timerStartedWithTime,
    required TResult Function() timerStopped,
    required TResult Function() timerPaused,
    required TResult Function() timerResumed,
    required TResult Function() timerRestarted,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() timerReset,
    required TResult Function(Duration duration, DateTime timestamp)
        syncFromForegroundService,
  }) {
    return timerPaused();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? timerStarted,
    TResult? Function(DateTime startTime)? timerStartedWithTime,
    TResult? Function()? timerStopped,
    TResult? Function()? timerPaused,
    TResult? Function()? timerResumed,
    TResult? Function()? timerRestarted,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? timerReset,
    TResult? Function(Duration duration, DateTime timestamp)?
        syncFromForegroundService,
  }) {
    return timerPaused?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? timerStarted,
    TResult Function(DateTime startTime)? timerStartedWithTime,
    TResult Function()? timerStopped,
    TResult Function()? timerPaused,
    TResult Function()? timerResumed,
    TResult Function()? timerRestarted,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? timerReset,
    TResult Function(Duration duration, DateTime timestamp)?
        syncFromForegroundService,
    required TResult orElse(),
  }) {
    if (timerPaused != null) {
      return timerPaused();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_TimerStarted value) timerStarted,
    required TResult Function(_TimerStartedWithTime value) timerStartedWithTime,
    required TResult Function(_TimerStopped value) timerStopped,
    required TResult Function(_TimerPaused value) timerPaused,
    required TResult Function(_TimerResumed value) timerResumed,
    required TResult Function(_TimerRestarted value) timerRestarted,
    required TResult Function(_TimerUpdated value) timerUpdated,
    required TResult Function(_TimerReset value) timerReset,
    required TResult Function(_SyncFromForegroundService value)
        syncFromForegroundService,
  }) {
    return timerPaused(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_TimerStarted value)? timerStarted,
    TResult? Function(_TimerStartedWithTime value)? timerStartedWithTime,
    TResult? Function(_TimerStopped value)? timerStopped,
    TResult? Function(_TimerPaused value)? timerPaused,
    TResult? Function(_TimerResumed value)? timerResumed,
    TResult? Function(_TimerRestarted value)? timerRestarted,
    TResult? Function(_TimerUpdated value)? timerUpdated,
    TResult? Function(_TimerReset value)? timerReset,
    TResult? Function(_SyncFromForegroundService value)?
        syncFromForegroundService,
  }) {
    return timerPaused?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_TimerStarted value)? timerStarted,
    TResult Function(_TimerStartedWithTime value)? timerStartedWithTime,
    TResult Function(_TimerStopped value)? timerStopped,
    TResult Function(_TimerPaused value)? timerPaused,
    TResult Function(_TimerResumed value)? timerResumed,
    TResult Function(_TimerRestarted value)? timerRestarted,
    TResult Function(_TimerUpdated value)? timerUpdated,
    TResult Function(_TimerReset value)? timerReset,
    TResult Function(_SyncFromForegroundService value)?
        syncFromForegroundService,
    required TResult orElse(),
  }) {
    if (timerPaused != null) {
      return timerPaused(this);
    }
    return orElse();
  }
}

abstract class _TimerPaused implements CallTimerEvent {
  const factory _TimerPaused() = _$TimerPausedImpl;
}

/// @nodoc
abstract class _$$TimerResumedImplCopyWith<$Res> {
  factory _$$TimerResumedImplCopyWith(
          _$TimerResumedImpl value, $Res Function(_$TimerResumedImpl) then) =
      __$$TimerResumedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$TimerResumedImplCopyWithImpl<$Res>
    extends _$CallTimerEventCopyWithImpl<$Res, _$TimerResumedImpl>
    implements _$$TimerResumedImplCopyWith<$Res> {
  __$$TimerResumedImplCopyWithImpl(
      _$TimerResumedImpl _value, $Res Function(_$TimerResumedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CallTimerEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$TimerResumedImpl implements _TimerResumed {
  const _$TimerResumedImpl();

  @override
  String toString() {
    return 'CallTimerEvent.timerResumed()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$TimerResumedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() timerStarted,
    required TResult Function(DateTime startTime) timerStartedWithTime,
    required TResult Function() timerStopped,
    required TResult Function() timerPaused,
    required TResult Function() timerResumed,
    required TResult Function() timerRestarted,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() timerReset,
    required TResult Function(Duration duration, DateTime timestamp)
        syncFromForegroundService,
  }) {
    return timerResumed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? timerStarted,
    TResult? Function(DateTime startTime)? timerStartedWithTime,
    TResult? Function()? timerStopped,
    TResult? Function()? timerPaused,
    TResult? Function()? timerResumed,
    TResult? Function()? timerRestarted,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? timerReset,
    TResult? Function(Duration duration, DateTime timestamp)?
        syncFromForegroundService,
  }) {
    return timerResumed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? timerStarted,
    TResult Function(DateTime startTime)? timerStartedWithTime,
    TResult Function()? timerStopped,
    TResult Function()? timerPaused,
    TResult Function()? timerResumed,
    TResult Function()? timerRestarted,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? timerReset,
    TResult Function(Duration duration, DateTime timestamp)?
        syncFromForegroundService,
    required TResult orElse(),
  }) {
    if (timerResumed != null) {
      return timerResumed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_TimerStarted value) timerStarted,
    required TResult Function(_TimerStartedWithTime value) timerStartedWithTime,
    required TResult Function(_TimerStopped value) timerStopped,
    required TResult Function(_TimerPaused value) timerPaused,
    required TResult Function(_TimerResumed value) timerResumed,
    required TResult Function(_TimerRestarted value) timerRestarted,
    required TResult Function(_TimerUpdated value) timerUpdated,
    required TResult Function(_TimerReset value) timerReset,
    required TResult Function(_SyncFromForegroundService value)
        syncFromForegroundService,
  }) {
    return timerResumed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_TimerStarted value)? timerStarted,
    TResult? Function(_TimerStartedWithTime value)? timerStartedWithTime,
    TResult? Function(_TimerStopped value)? timerStopped,
    TResult? Function(_TimerPaused value)? timerPaused,
    TResult? Function(_TimerResumed value)? timerResumed,
    TResult? Function(_TimerRestarted value)? timerRestarted,
    TResult? Function(_TimerUpdated value)? timerUpdated,
    TResult? Function(_TimerReset value)? timerReset,
    TResult? Function(_SyncFromForegroundService value)?
        syncFromForegroundService,
  }) {
    return timerResumed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_TimerStarted value)? timerStarted,
    TResult Function(_TimerStartedWithTime value)? timerStartedWithTime,
    TResult Function(_TimerStopped value)? timerStopped,
    TResult Function(_TimerPaused value)? timerPaused,
    TResult Function(_TimerResumed value)? timerResumed,
    TResult Function(_TimerRestarted value)? timerRestarted,
    TResult Function(_TimerUpdated value)? timerUpdated,
    TResult Function(_TimerReset value)? timerReset,
    TResult Function(_SyncFromForegroundService value)?
        syncFromForegroundService,
    required TResult orElse(),
  }) {
    if (timerResumed != null) {
      return timerResumed(this);
    }
    return orElse();
  }
}

abstract class _TimerResumed implements CallTimerEvent {
  const factory _TimerResumed() = _$TimerResumedImpl;
}

/// @nodoc
abstract class _$$TimerRestartedImplCopyWith<$Res> {
  factory _$$TimerRestartedImplCopyWith(_$TimerRestartedImpl value,
          $Res Function(_$TimerRestartedImpl) then) =
      __$$TimerRestartedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$TimerRestartedImplCopyWithImpl<$Res>
    extends _$CallTimerEventCopyWithImpl<$Res, _$TimerRestartedImpl>
    implements _$$TimerRestartedImplCopyWith<$Res> {
  __$$TimerRestartedImplCopyWithImpl(
      _$TimerRestartedImpl _value, $Res Function(_$TimerRestartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CallTimerEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$TimerRestartedImpl implements _TimerRestarted {
  const _$TimerRestartedImpl();

  @override
  String toString() {
    return 'CallTimerEvent.timerRestarted()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$TimerRestartedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() timerStarted,
    required TResult Function(DateTime startTime) timerStartedWithTime,
    required TResult Function() timerStopped,
    required TResult Function() timerPaused,
    required TResult Function() timerResumed,
    required TResult Function() timerRestarted,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() timerReset,
    required TResult Function(Duration duration, DateTime timestamp)
        syncFromForegroundService,
  }) {
    return timerRestarted();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? timerStarted,
    TResult? Function(DateTime startTime)? timerStartedWithTime,
    TResult? Function()? timerStopped,
    TResult? Function()? timerPaused,
    TResult? Function()? timerResumed,
    TResult? Function()? timerRestarted,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? timerReset,
    TResult? Function(Duration duration, DateTime timestamp)?
        syncFromForegroundService,
  }) {
    return timerRestarted?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? timerStarted,
    TResult Function(DateTime startTime)? timerStartedWithTime,
    TResult Function()? timerStopped,
    TResult Function()? timerPaused,
    TResult Function()? timerResumed,
    TResult Function()? timerRestarted,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? timerReset,
    TResult Function(Duration duration, DateTime timestamp)?
        syncFromForegroundService,
    required TResult orElse(),
  }) {
    if (timerRestarted != null) {
      return timerRestarted();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_TimerStarted value) timerStarted,
    required TResult Function(_TimerStartedWithTime value) timerStartedWithTime,
    required TResult Function(_TimerStopped value) timerStopped,
    required TResult Function(_TimerPaused value) timerPaused,
    required TResult Function(_TimerResumed value) timerResumed,
    required TResult Function(_TimerRestarted value) timerRestarted,
    required TResult Function(_TimerUpdated value) timerUpdated,
    required TResult Function(_TimerReset value) timerReset,
    required TResult Function(_SyncFromForegroundService value)
        syncFromForegroundService,
  }) {
    return timerRestarted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_TimerStarted value)? timerStarted,
    TResult? Function(_TimerStartedWithTime value)? timerStartedWithTime,
    TResult? Function(_TimerStopped value)? timerStopped,
    TResult? Function(_TimerPaused value)? timerPaused,
    TResult? Function(_TimerResumed value)? timerResumed,
    TResult? Function(_TimerRestarted value)? timerRestarted,
    TResult? Function(_TimerUpdated value)? timerUpdated,
    TResult? Function(_TimerReset value)? timerReset,
    TResult? Function(_SyncFromForegroundService value)?
        syncFromForegroundService,
  }) {
    return timerRestarted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_TimerStarted value)? timerStarted,
    TResult Function(_TimerStartedWithTime value)? timerStartedWithTime,
    TResult Function(_TimerStopped value)? timerStopped,
    TResult Function(_TimerPaused value)? timerPaused,
    TResult Function(_TimerResumed value)? timerResumed,
    TResult Function(_TimerRestarted value)? timerRestarted,
    TResult Function(_TimerUpdated value)? timerUpdated,
    TResult Function(_TimerReset value)? timerReset,
    TResult Function(_SyncFromForegroundService value)?
        syncFromForegroundService,
    required TResult orElse(),
  }) {
    if (timerRestarted != null) {
      return timerRestarted(this);
    }
    return orElse();
  }
}

abstract class _TimerRestarted implements CallTimerEvent {
  const factory _TimerRestarted() = _$TimerRestartedImpl;
}

/// @nodoc
abstract class _$$TimerUpdatedImplCopyWith<$Res> {
  factory _$$TimerUpdatedImplCopyWith(
          _$TimerUpdatedImpl value, $Res Function(_$TimerUpdatedImpl) then) =
      __$$TimerUpdatedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Duration elapsed});
}

/// @nodoc
class __$$TimerUpdatedImplCopyWithImpl<$Res>
    extends _$CallTimerEventCopyWithImpl<$Res, _$TimerUpdatedImpl>
    implements _$$TimerUpdatedImplCopyWith<$Res> {
  __$$TimerUpdatedImplCopyWithImpl(
      _$TimerUpdatedImpl _value, $Res Function(_$TimerUpdatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CallTimerEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? elapsed = null,
  }) {
    return _then(_$TimerUpdatedImpl(
      elapsed: null == elapsed
          ? _value.elapsed
          : elapsed // ignore: cast_nullable_to_non_nullable
              as Duration,
    ));
  }
}

/// @nodoc

class _$TimerUpdatedImpl implements _TimerUpdated {
  const _$TimerUpdatedImpl({this.elapsed = Duration.zero});

  @override
  @JsonKey()
  final Duration elapsed;

  @override
  String toString() {
    return 'CallTimerEvent.timerUpdated(elapsed: $elapsed)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TimerUpdatedImpl &&
            (identical(other.elapsed, elapsed) || other.elapsed == elapsed));
  }

  @override
  int get hashCode => Object.hash(runtimeType, elapsed);

  /// Create a copy of CallTimerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TimerUpdatedImplCopyWith<_$TimerUpdatedImpl> get copyWith =>
      __$$TimerUpdatedImplCopyWithImpl<_$TimerUpdatedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() timerStarted,
    required TResult Function(DateTime startTime) timerStartedWithTime,
    required TResult Function() timerStopped,
    required TResult Function() timerPaused,
    required TResult Function() timerResumed,
    required TResult Function() timerRestarted,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() timerReset,
    required TResult Function(Duration duration, DateTime timestamp)
        syncFromForegroundService,
  }) {
    return timerUpdated(elapsed);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? timerStarted,
    TResult? Function(DateTime startTime)? timerStartedWithTime,
    TResult? Function()? timerStopped,
    TResult? Function()? timerPaused,
    TResult? Function()? timerResumed,
    TResult? Function()? timerRestarted,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? timerReset,
    TResult? Function(Duration duration, DateTime timestamp)?
        syncFromForegroundService,
  }) {
    return timerUpdated?.call(elapsed);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? timerStarted,
    TResult Function(DateTime startTime)? timerStartedWithTime,
    TResult Function()? timerStopped,
    TResult Function()? timerPaused,
    TResult Function()? timerResumed,
    TResult Function()? timerRestarted,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? timerReset,
    TResult Function(Duration duration, DateTime timestamp)?
        syncFromForegroundService,
    required TResult orElse(),
  }) {
    if (timerUpdated != null) {
      return timerUpdated(elapsed);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_TimerStarted value) timerStarted,
    required TResult Function(_TimerStartedWithTime value) timerStartedWithTime,
    required TResult Function(_TimerStopped value) timerStopped,
    required TResult Function(_TimerPaused value) timerPaused,
    required TResult Function(_TimerResumed value) timerResumed,
    required TResult Function(_TimerRestarted value) timerRestarted,
    required TResult Function(_TimerUpdated value) timerUpdated,
    required TResult Function(_TimerReset value) timerReset,
    required TResult Function(_SyncFromForegroundService value)
        syncFromForegroundService,
  }) {
    return timerUpdated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_TimerStarted value)? timerStarted,
    TResult? Function(_TimerStartedWithTime value)? timerStartedWithTime,
    TResult? Function(_TimerStopped value)? timerStopped,
    TResult? Function(_TimerPaused value)? timerPaused,
    TResult? Function(_TimerResumed value)? timerResumed,
    TResult? Function(_TimerRestarted value)? timerRestarted,
    TResult? Function(_TimerUpdated value)? timerUpdated,
    TResult? Function(_TimerReset value)? timerReset,
    TResult? Function(_SyncFromForegroundService value)?
        syncFromForegroundService,
  }) {
    return timerUpdated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_TimerStarted value)? timerStarted,
    TResult Function(_TimerStartedWithTime value)? timerStartedWithTime,
    TResult Function(_TimerStopped value)? timerStopped,
    TResult Function(_TimerPaused value)? timerPaused,
    TResult Function(_TimerResumed value)? timerResumed,
    TResult Function(_TimerRestarted value)? timerRestarted,
    TResult Function(_TimerUpdated value)? timerUpdated,
    TResult Function(_TimerReset value)? timerReset,
    TResult Function(_SyncFromForegroundService value)?
        syncFromForegroundService,
    required TResult orElse(),
  }) {
    if (timerUpdated != null) {
      return timerUpdated(this);
    }
    return orElse();
  }
}

abstract class _TimerUpdated implements CallTimerEvent {
  const factory _TimerUpdated({final Duration elapsed}) = _$TimerUpdatedImpl;

  Duration get elapsed;

  /// Create a copy of CallTimerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TimerUpdatedImplCopyWith<_$TimerUpdatedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$TimerResetImplCopyWith<$Res> {
  factory _$$TimerResetImplCopyWith(
          _$TimerResetImpl value, $Res Function(_$TimerResetImpl) then) =
      __$$TimerResetImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$TimerResetImplCopyWithImpl<$Res>
    extends _$CallTimerEventCopyWithImpl<$Res, _$TimerResetImpl>
    implements _$$TimerResetImplCopyWith<$Res> {
  __$$TimerResetImplCopyWithImpl(
      _$TimerResetImpl _value, $Res Function(_$TimerResetImpl) _then)
      : super(_value, _then);

  /// Create a copy of CallTimerEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$TimerResetImpl implements _TimerReset {
  const _$TimerResetImpl();

  @override
  String toString() {
    return 'CallTimerEvent.timerReset()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$TimerResetImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() timerStarted,
    required TResult Function(DateTime startTime) timerStartedWithTime,
    required TResult Function() timerStopped,
    required TResult Function() timerPaused,
    required TResult Function() timerResumed,
    required TResult Function() timerRestarted,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() timerReset,
    required TResult Function(Duration duration, DateTime timestamp)
        syncFromForegroundService,
  }) {
    return timerReset();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? timerStarted,
    TResult? Function(DateTime startTime)? timerStartedWithTime,
    TResult? Function()? timerStopped,
    TResult? Function()? timerPaused,
    TResult? Function()? timerResumed,
    TResult? Function()? timerRestarted,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? timerReset,
    TResult? Function(Duration duration, DateTime timestamp)?
        syncFromForegroundService,
  }) {
    return timerReset?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? timerStarted,
    TResult Function(DateTime startTime)? timerStartedWithTime,
    TResult Function()? timerStopped,
    TResult Function()? timerPaused,
    TResult Function()? timerResumed,
    TResult Function()? timerRestarted,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? timerReset,
    TResult Function(Duration duration, DateTime timestamp)?
        syncFromForegroundService,
    required TResult orElse(),
  }) {
    if (timerReset != null) {
      return timerReset();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_TimerStarted value) timerStarted,
    required TResult Function(_TimerStartedWithTime value) timerStartedWithTime,
    required TResult Function(_TimerStopped value) timerStopped,
    required TResult Function(_TimerPaused value) timerPaused,
    required TResult Function(_TimerResumed value) timerResumed,
    required TResult Function(_TimerRestarted value) timerRestarted,
    required TResult Function(_TimerUpdated value) timerUpdated,
    required TResult Function(_TimerReset value) timerReset,
    required TResult Function(_SyncFromForegroundService value)
        syncFromForegroundService,
  }) {
    return timerReset(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_TimerStarted value)? timerStarted,
    TResult? Function(_TimerStartedWithTime value)? timerStartedWithTime,
    TResult? Function(_TimerStopped value)? timerStopped,
    TResult? Function(_TimerPaused value)? timerPaused,
    TResult? Function(_TimerResumed value)? timerResumed,
    TResult? Function(_TimerRestarted value)? timerRestarted,
    TResult? Function(_TimerUpdated value)? timerUpdated,
    TResult? Function(_TimerReset value)? timerReset,
    TResult? Function(_SyncFromForegroundService value)?
        syncFromForegroundService,
  }) {
    return timerReset?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_TimerStarted value)? timerStarted,
    TResult Function(_TimerStartedWithTime value)? timerStartedWithTime,
    TResult Function(_TimerStopped value)? timerStopped,
    TResult Function(_TimerPaused value)? timerPaused,
    TResult Function(_TimerResumed value)? timerResumed,
    TResult Function(_TimerRestarted value)? timerRestarted,
    TResult Function(_TimerUpdated value)? timerUpdated,
    TResult Function(_TimerReset value)? timerReset,
    TResult Function(_SyncFromForegroundService value)?
        syncFromForegroundService,
    required TResult orElse(),
  }) {
    if (timerReset != null) {
      return timerReset(this);
    }
    return orElse();
  }
}

abstract class _TimerReset implements CallTimerEvent {
  const factory _TimerReset() = _$TimerResetImpl;
}

/// @nodoc
abstract class _$$SyncFromForegroundServiceImplCopyWith<$Res> {
  factory _$$SyncFromForegroundServiceImplCopyWith(
          _$SyncFromForegroundServiceImpl value,
          $Res Function(_$SyncFromForegroundServiceImpl) then) =
      __$$SyncFromForegroundServiceImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Duration duration, DateTime timestamp});
}

/// @nodoc
class __$$SyncFromForegroundServiceImplCopyWithImpl<$Res>
    extends _$CallTimerEventCopyWithImpl<$Res, _$SyncFromForegroundServiceImpl>
    implements _$$SyncFromForegroundServiceImplCopyWith<$Res> {
  __$$SyncFromForegroundServiceImplCopyWithImpl(
      _$SyncFromForegroundServiceImpl _value,
      $Res Function(_$SyncFromForegroundServiceImpl) _then)
      : super(_value, _then);

  /// Create a copy of CallTimerEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? duration = null,
    Object? timestamp = null,
  }) {
    return _then(_$SyncFromForegroundServiceImpl(
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as Duration,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$SyncFromForegroundServiceImpl implements _SyncFromForegroundService {
  const _$SyncFromForegroundServiceImpl(
      {required this.duration, required this.timestamp});

  @override
  final Duration duration;
  @override
  final DateTime timestamp;

  @override
  String toString() {
    return 'CallTimerEvent.syncFromForegroundService(duration: $duration, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SyncFromForegroundServiceImpl &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @override
  int get hashCode => Object.hash(runtimeType, duration, timestamp);

  /// Create a copy of CallTimerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SyncFromForegroundServiceImplCopyWith<_$SyncFromForegroundServiceImpl>
      get copyWith => __$$SyncFromForegroundServiceImplCopyWithImpl<
          _$SyncFromForegroundServiceImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() timerStarted,
    required TResult Function(DateTime startTime) timerStartedWithTime,
    required TResult Function() timerStopped,
    required TResult Function() timerPaused,
    required TResult Function() timerResumed,
    required TResult Function() timerRestarted,
    required TResult Function(Duration elapsed) timerUpdated,
    required TResult Function() timerReset,
    required TResult Function(Duration duration, DateTime timestamp)
        syncFromForegroundService,
  }) {
    return syncFromForegroundService(duration, timestamp);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? timerStarted,
    TResult? Function(DateTime startTime)? timerStartedWithTime,
    TResult? Function()? timerStopped,
    TResult? Function()? timerPaused,
    TResult? Function()? timerResumed,
    TResult? Function()? timerRestarted,
    TResult? Function(Duration elapsed)? timerUpdated,
    TResult? Function()? timerReset,
    TResult? Function(Duration duration, DateTime timestamp)?
        syncFromForegroundService,
  }) {
    return syncFromForegroundService?.call(duration, timestamp);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? timerStarted,
    TResult Function(DateTime startTime)? timerStartedWithTime,
    TResult Function()? timerStopped,
    TResult Function()? timerPaused,
    TResult Function()? timerResumed,
    TResult Function()? timerRestarted,
    TResult Function(Duration elapsed)? timerUpdated,
    TResult Function()? timerReset,
    TResult Function(Duration duration, DateTime timestamp)?
        syncFromForegroundService,
    required TResult orElse(),
  }) {
    if (syncFromForegroundService != null) {
      return syncFromForegroundService(duration, timestamp);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_TimerStarted value) timerStarted,
    required TResult Function(_TimerStartedWithTime value) timerStartedWithTime,
    required TResult Function(_TimerStopped value) timerStopped,
    required TResult Function(_TimerPaused value) timerPaused,
    required TResult Function(_TimerResumed value) timerResumed,
    required TResult Function(_TimerRestarted value) timerRestarted,
    required TResult Function(_TimerUpdated value) timerUpdated,
    required TResult Function(_TimerReset value) timerReset,
    required TResult Function(_SyncFromForegroundService value)
        syncFromForegroundService,
  }) {
    return syncFromForegroundService(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_TimerStarted value)? timerStarted,
    TResult? Function(_TimerStartedWithTime value)? timerStartedWithTime,
    TResult? Function(_TimerStopped value)? timerStopped,
    TResult? Function(_TimerPaused value)? timerPaused,
    TResult? Function(_TimerResumed value)? timerResumed,
    TResult? Function(_TimerRestarted value)? timerRestarted,
    TResult? Function(_TimerUpdated value)? timerUpdated,
    TResult? Function(_TimerReset value)? timerReset,
    TResult? Function(_SyncFromForegroundService value)?
        syncFromForegroundService,
  }) {
    return syncFromForegroundService?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_TimerStarted value)? timerStarted,
    TResult Function(_TimerStartedWithTime value)? timerStartedWithTime,
    TResult Function(_TimerStopped value)? timerStopped,
    TResult Function(_TimerPaused value)? timerPaused,
    TResult Function(_TimerResumed value)? timerResumed,
    TResult Function(_TimerRestarted value)? timerRestarted,
    TResult Function(_TimerUpdated value)? timerUpdated,
    TResult Function(_TimerReset value)? timerReset,
    TResult Function(_SyncFromForegroundService value)?
        syncFromForegroundService,
    required TResult orElse(),
  }) {
    if (syncFromForegroundService != null) {
      return syncFromForegroundService(this);
    }
    return orElse();
  }
}

abstract class _SyncFromForegroundService implements CallTimerEvent {
  const factory _SyncFromForegroundService(
      {required final Duration duration,
      required final DateTime timestamp}) = _$SyncFromForegroundServiceImpl;

  Duration get duration;
  DateTime get timestamp;

  /// Create a copy of CallTimerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SyncFromForegroundServiceImplCopyWith<_$SyncFromForegroundServiceImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$CallTimerState {
  /// Current elapsed time since timer started
  Duration get elapsedTime => throw _privateConstructorUsedError;

  /// Whether the timer is currently running
  bool get isRunning => throw _privateConstructorUsedError;

  /// Whether the timer is currently paused
  bool get isPaused => throw _privateConstructorUsedError;

  /// Last error message if any timer operation failed
  String? get lastError => throw _privateConstructorUsedError;

  /// Create a copy of CallTimerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CallTimerStateCopyWith<CallTimerState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CallTimerStateCopyWith<$Res> {
  factory $CallTimerStateCopyWith(
          CallTimerState value, $Res Function(CallTimerState) then) =
      _$CallTimerStateCopyWithImpl<$Res, CallTimerState>;
  @useResult
  $Res call(
      {Duration elapsedTime, bool isRunning, bool isPaused, String? lastError});
}

/// @nodoc
class _$CallTimerStateCopyWithImpl<$Res, $Val extends CallTimerState>
    implements $CallTimerStateCopyWith<$Res> {
  _$CallTimerStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CallTimerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? elapsedTime = null,
    Object? isRunning = null,
    Object? isPaused = null,
    Object? lastError = freezed,
  }) {
    return _then(_value.copyWith(
      elapsedTime: null == elapsedTime
          ? _value.elapsedTime
          : elapsedTime // ignore: cast_nullable_to_non_nullable
              as Duration,
      isRunning: null == isRunning
          ? _value.isRunning
          : isRunning // ignore: cast_nullable_to_non_nullable
              as bool,
      isPaused: null == isPaused
          ? _value.isPaused
          : isPaused // ignore: cast_nullable_to_non_nullable
              as bool,
      lastError: freezed == lastError
          ? _value.lastError
          : lastError // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CallTimerStateImplCopyWith<$Res>
    implements $CallTimerStateCopyWith<$Res> {
  factory _$$CallTimerStateImplCopyWith(_$CallTimerStateImpl value,
          $Res Function(_$CallTimerStateImpl) then) =
      __$$CallTimerStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Duration elapsedTime, bool isRunning, bool isPaused, String? lastError});
}

/// @nodoc
class __$$CallTimerStateImplCopyWithImpl<$Res>
    extends _$CallTimerStateCopyWithImpl<$Res, _$CallTimerStateImpl>
    implements _$$CallTimerStateImplCopyWith<$Res> {
  __$$CallTimerStateImplCopyWithImpl(
      _$CallTimerStateImpl _value, $Res Function(_$CallTimerStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CallTimerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? elapsedTime = null,
    Object? isRunning = null,
    Object? isPaused = null,
    Object? lastError = freezed,
  }) {
    return _then(_$CallTimerStateImpl(
      elapsedTime: null == elapsedTime
          ? _value.elapsedTime
          : elapsedTime // ignore: cast_nullable_to_non_nullable
              as Duration,
      isRunning: null == isRunning
          ? _value.isRunning
          : isRunning // ignore: cast_nullable_to_non_nullable
              as bool,
      isPaused: null == isPaused
          ? _value.isPaused
          : isPaused // ignore: cast_nullable_to_non_nullable
              as bool,
      lastError: freezed == lastError
          ? _value.lastError
          : lastError // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$CallTimerStateImpl extends _CallTimerState {
  const _$CallTimerStateImpl(
      {this.elapsedTime = Duration.zero,
      this.isRunning = false,
      this.isPaused = false,
      this.lastError})
      : super._();

  /// Current elapsed time since timer started
  @override
  @JsonKey()
  final Duration elapsedTime;

  /// Whether the timer is currently running
  @override
  @JsonKey()
  final bool isRunning;

  /// Whether the timer is currently paused
  @override
  @JsonKey()
  final bool isPaused;

  /// Last error message if any timer operation failed
  @override
  final String? lastError;

  @override
  String toString() {
    return 'CallTimerState(elapsedTime: $elapsedTime, isRunning: $isRunning, isPaused: $isPaused, lastError: $lastError)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CallTimerStateImpl &&
            (identical(other.elapsedTime, elapsedTime) ||
                other.elapsedTime == elapsedTime) &&
            (identical(other.isRunning, isRunning) ||
                other.isRunning == isRunning) &&
            (identical(other.isPaused, isPaused) ||
                other.isPaused == isPaused) &&
            (identical(other.lastError, lastError) ||
                other.lastError == lastError));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, elapsedTime, isRunning, isPaused, lastError);

  /// Create a copy of CallTimerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CallTimerStateImplCopyWith<_$CallTimerStateImpl> get copyWith =>
      __$$CallTimerStateImplCopyWithImpl<_$CallTimerStateImpl>(
          this, _$identity);
}

abstract class _CallTimerState extends CallTimerState {
  const factory _CallTimerState(
      {final Duration elapsedTime,
      final bool isRunning,
      final bool isPaused,
      final String? lastError}) = _$CallTimerStateImpl;
  const _CallTimerState._() : super._();

  /// Current elapsed time since timer started
  @override
  Duration get elapsedTime;

  /// Whether the timer is currently running
  @override
  bool get isRunning;

  /// Whether the timer is currently paused
  @override
  bool get isPaused;

  /// Last error message if any timer operation failed
  @override
  String? get lastError;

  /// Create a copy of CallTimerState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CallTimerStateImplCopyWith<_$CallTimerStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
