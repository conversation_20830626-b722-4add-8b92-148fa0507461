part of 'dialer_bloc.dart';

@freezed
class DialerState with _$DialerState {
  factory DialerState({
    sip_ua.Call? call,
    String? sipUsername,
    String? sipPassword,
    String? message,
    String? phoneNumber,
    String? countryCode,
    String? holdOriginator,
    MediaStream? localMediaStream,
    MediaStream? remoteMediaStream,
    @Default(false) bool isNumPadShowing,
    @Default(sip_ua.RegistrationStateEnum.NONE)
    sip_ua.RegistrationStateEnum sipRegistrationStatus,
    @Default(Duration.zero) Duration elapsedCallTimer,
    @Default(CallAudioTracksMode.phone) CallAudioTracksMode audioTrackMode,
    @Default(sip_ua.CallStateEnum.NONE) sip_ua.CallStateEnum callStateEnum,
    @Default(DialerStatus.initial) DialerStatus status,
    @Default(DialerMuteStatus.unmuted) DialerMuteStatus muteStatus,
    @Default('Unknown') String audioOutput,
  }) = _DialerState;

  factory DialerState.initial() => DialerState();

  const DialerState._();

  String get direction => call!.direction;

  String? get remoteIdentity => call!.remote_identity;

  bool get voiceOnly => call!.voiceOnly && !call!.remote_has_video;

  String get formattedCallTimer => [
        elapsedCallTimer.inMinutes,
        elapsedCallTimer.inSeconds,
      ].map((seg) => seg.remainder(60).toString().padLeft(2, '0')).join(':');

  bool get isCallConnected => status.isConnected;

  bool get isCallConnecting => status.isConnecting;

  bool get isRinging => status.isRinging;

  bool get isCallInitial => status.isInitial;
  bool get isCallInitiating => status.isInitiating;

  String get audioTrackModeName => audioTrackMode.name;

  bool get isSpeakerEnabled => audioTrackMode.isSpeakerEnabled;

  bool get isSpeakerDisabled => audioTrackMode.isSpeakerDisabled;

  bool get isBluetoothAudioEnabled => audioTrackMode.isBluetoothAudioEnabled;

  bool get isSipRegistered =>
      sipRegistrationStatus == sip_ua.RegistrationStateEnum.REGISTERED;

  bool get isSipRegisterFailed =>
      sipRegistrationStatus == sip_ua.RegistrationStateEnum.REGISTRATION_FAILED;

  bool get isSipRegistrationPending =>
      sipRegistrationStatus == sip_ua.RegistrationStateEnum.NONE;

  bool get isSipUnregistered =>
      sipRegistrationStatus == sip_ua.RegistrationStateEnum.UNREGISTERED;

  bool get isOnHold => status.isOnHold;

  bool get isMuted => muteStatus.isMuted;
}
