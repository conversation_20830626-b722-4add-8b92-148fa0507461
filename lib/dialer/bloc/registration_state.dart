part of 'registration_bloc.dart';

/// Registration state representing SIP registration status and credentials
///
/// Contains registration status, credentials, error information, and helper
/// properties for managing SIP registration state.
@freezed
class RegistrationState with _$RegistrationState {
  const factory RegistrationState({
    /// Current SIP username (normalized format)
    String? sipUsername,

    /// Current SIP password
    String? sipPassword,

    /// Current registration status
    @Default(sip_ua.RegistrationStateEnum.NONE)
    sip_ua.RegistrationStateEnum status,

    /// Last error message if registration failed
    String? lastError,
  }) = _RegistrationState;

  /// Creates an initial registration state with default values
  factory RegistrationState.initial() => const RegistrationState();

  const RegistrationState._();

  /// Whether SIP is currently registered
  bool get isRegistered => status == sip_ua.RegistrationStateEnum.REGISTERED;

  /// Whether SIP registration failed
  bool get isRegistrationFailed =>
      status == sip_ua.RegistrationStateEnum.REGISTRATION_FAILED;

  /// Whether SIP is currently unregistered
  bool get isUnregistered =>
      status == sip_ua.RegistrationStateEnum.UNREGISTERED;

  /// Whether SIP registration is in progress
  bool get isRegistering => status == sip_ua.RegistrationStateEnum.UNREGISTERED;

  /// Whether SIP registration is in initial/unknown state
  bool get isInitial => status == sip_ua.RegistrationStateEnum.NONE;

  /// Whether credentials are available for registration
  bool get hasCredentials =>
      (sipUsername?.isNotEmpty ?? false) && (sipPassword?.isNotEmpty ?? false);

  /// Whether there is an error in the current registration state
  bool get hasError => lastError != null;

  /// User-friendly registration status name
  String get statusName => status.name;

  /// Whether the user can attempt registration
  bool get canRegister => hasCredentials && !isRegistered;

  /// Whether the user can attempt reconnection
  bool get canReconnect =>
      hasCredentials && (isUnregistered || isRegistrationFailed);
}
