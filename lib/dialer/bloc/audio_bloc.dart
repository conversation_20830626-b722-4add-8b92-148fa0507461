import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:froggytalk/dialer/dialer.dart';
import 'package:froggytalk/shared/services/audio_routing_manager.dart';
import 'package:utils/utils.dart';

part 'audio_bloc.freezed.dart';
part 'audio_event.dart';
part 'audio_state.dart';

/// Specialized BLoC for managing audio routing, speaker controls, and mute
/// functionality.
///
/// This BLoC handles all audio-related operations including:
/// - Speaker phone toggle (on/off)
/// - Bluetooth headset switching
/// - Wired headset detection and switching
/// - Mute/unmute functionality
/// - Audio routing change monitoring
/// - Audio stream management for calls
///
/// Follows the single responsibility principle by separating audio concerns
/// from the main DialerBloc, improving maintainability and testability.
class AudioBloc extends Bloc<AudioEvent, AudioState> {
  /// Creates an AudioBloc with required dependencies
  ///
  /// [callService] - Service for managing call-related audio operations
  /// [audioRoutingManager] - Manager for handling audio routing changes
  AudioBloc({
    required CallService callService,
    AudioRoutingManager? audioRoutingManager,
  })  : _callService = callService,
        _audioRoutingManager =
            audioRoutingManager ?? AudioRoutingManager(),
        super(AudioState.initial()) {
    // Register event handlers
    on<_SpeakerToggled>(_onSpeakerToggled);
    on<_SpeakerEnabled>(_onSpeakerEnabled);
    on<_SpeakerDisabled>(_onSpeakerDisabled);
    on<_BluetoothEnabled>(_onBluetoothEnabled);
    on<_WiredHeadsetEnabled>(_onWiredHeadsetEnabled);
    on<_MuteToggled>(_onMuteToggled);
    on<_MuteEnabled>(_onMuteEnabled);
    on<_MuteDisabled>(_onMuteDisabled);
    on<_AudioRoutingChanged>(_onAudioRoutingChanged);
    on<_LocalStreamUpdated>(_onLocalStreamUpdated);
    on<_RemoteStreamUpdated>(_onRemoteStreamUpdated);
    on<_AudioReset>(_onAudioReset);

    // Start monitoring audio routing changes
    _startAudioRoutingMonitoring();
  }

  final CallService _callService;
  final AudioRoutingManager _audioRoutingManager;
  StreamSubscription<AudioOutputMode>? _audioRoutingSubscription;

  @override
  Future<void> close() {
    _audioRoutingSubscription?.cancel();
    return super.close();
  }

  /// Toggles speaker phone on/off based on current state
  void toggleSpeaker() {
    if (state.audioTrackMode == CallAudioTracksMode.speaker) {
      add(const AudioEvent.speakerDisabled());
    } else {
      add(const AudioEvent.speakerEnabled());
    }
  }

  /// Toggles mute on/off based on current state
  void toggleMute() {
    if (state.muteStatus == DialerMuteStatus.muted) {
      add(const AudioEvent.muteDisabled());
    } else {
      add(const AudioEvent.muteEnabled());
    }
  }

  /// Starts monitoring audio routing changes from the system
  void _startAudioRoutingMonitoring() {
    try {
      _callService.activateAudioRouting();
      _audioRoutingSubscription =
          _audioRoutingManager.routingChangeStream.listen((mode) {
        add(AudioEvent.audioRoutingChanged(mode: mode));
      });
    } catch (e) {
      FroggyLogger.error(
        'AudioBloc: Error starting audio routing monitoring: $e',
      );
    }
  }

  /// Handles speaker toggle events
  FutureOr<void> _onSpeakerToggled(
    _SpeakerToggled event,
    Emitter<AudioState> emit,
  ) {
    toggleSpeaker();
  }

  /// Handles enabling speaker phone
  FutureOr<void> _onSpeakerEnabled(
    _SpeakerEnabled event,
    Emitter<AudioState> emit,
  ) {
    if (!kIsWeb) {
      try {
        _callService.toggleSpeaker(enabled: true);
        emit(
          state.copyWith(
            audioTrackMode: CallAudioTracksMode.speaker,
            lastError: null,
          ),
        );
        FroggyLogger.debug(
          'AudioBloc: Speaker phone enabled successfully',
        );
      } catch (e) {
        FroggyLogger.error(
          'AudioBloc: Error enabling speaker phone: $e',
        );
        emit(
          state.copyWith(
            lastError: 'Failed to enable speaker phone: $e',
          ),
        );
      }
    }
  }

  /// Handles disabling speaker phone
  FutureOr<void> _onSpeakerDisabled(
    _SpeakerDisabled event,
    Emitter<AudioState> emit,
  ) {
    if (!kIsWeb) {
      try {
        _callService.toggleSpeaker(enabled: false);
        emit(
          state.copyWith(
            audioTrackMode: CallAudioTracksMode.phone,
            lastError: null,
          ),
        );
        FroggyLogger.debug(
          'AudioBloc: Speaker phone disabled successfully',
        );
      } catch (e) {
        FroggyLogger.error(
          'AudioBloc: Error disabling speaker phone: $e',
        );
        emit(
          state.copyWith(
            lastError: 'Failed to disable speaker phone: $e',
          ),
        );
      }
    }
  }

  /// Handles switching to Bluetooth headset
  FutureOr<void> _onBluetoothEnabled(
    _BluetoothEnabled event,
    Emitter<AudioState> emit,
  ) {
    try {
      Helper.setSpeakerphoneOnButPreferBluetooth();
      emit(
        state.copyWith(
          audioTrackMode: CallAudioTracksMode.bluetooth,
          lastError: null,
        ),
      );
      FroggyLogger.debug(
        'AudioBloc: Bluetooth headset enabled successfully',
      );
    } catch (e) {
      FroggyLogger.error(
        'AudioBloc: Error enabling Bluetooth headset: $e',
      );
      emit(
        state.copyWith(
          lastError: 'Failed to enable Bluetooth headset: $e',
        ),
      );
    }
  }

  /// Handles switching to wired headset
  FutureOr<void> _onWiredHeadsetEnabled(
    _WiredHeadsetEnabled event,
    Emitter<AudioState> emit,
  ) {
    try {
      emit(
        state.copyWith(
          audioTrackMode: CallAudioTracksMode.wiredHeadset,
          lastError: null,
        ),
      );
      FroggyLogger.debug(
        'AudioBloc: Wired headset enabled successfully',
      );
    } catch (e) {
      FroggyLogger.error(
        'AudioBloc: Error enabling wired headset: $e',
      );
      emit(
        state.copyWith(
          lastError: 'Failed to enable wired headset: $e',
        ),
      );
    }
  }

  /// Handles mute toggle events
  FutureOr<void> _onMuteToggled(
    _MuteToggled event,
    Emitter<AudioState> emit,
  ) {
    toggleMute();
  }

  /// Handles enabling mute
  FutureOr<void> _onMuteEnabled(
    _MuteEnabled event,
    Emitter<AudioState> emit,
  ) {
    try {
      // TODO: Integrate with actual call mute functionality when available
      emit(
        state.copyWith(
          muteStatus: DialerMuteStatus.muted,
          lastError: null,
        ),
      );
      FroggyLogger.debug(
        'AudioBloc: Mute enabled successfully',
      );
    } catch (e) {
      FroggyLogger.error(
        'AudioBloc: Error enabling mute: $e',
      );
      emit(
        state.copyWith(
          lastError: 'Failed to enable mute: $e',
        ),
      );
    }
  }

  /// Handles disabling mute
  FutureOr<void> _onMuteDisabled(
    _MuteDisabled event,
    Emitter<AudioState> emit,
  ) {
    try {
      // TODO: Integrate with actual call unmute functionality when available
      emit(
        state.copyWith(
          muteStatus: DialerMuteStatus.unmuted,
          lastError: null,
        ),
      );
      FroggyLogger.debug(
        'AudioBloc: Mute disabled successfully',
      );
    } catch (e) {
      FroggyLogger.error(
        'AudioBloc: Error disabling mute: $e',
      );
      emit(
        state.copyWith(
          lastError: 'Failed to disable mute: $e',
        ),
      );
    }
  }

  /// Handles audio routing changes from the system
  FutureOr<void> _onAudioRoutingChanged(
    _AudioRoutingChanged event,
    Emitter<AudioState> emit,
  ) {
    try {
      final audioOutput = _getAudioOutputName(event.mode);
      final audioTrackMode = _getAudioTrackMode(event.mode);

      emit(
        state.copyWith(
          audioOutput: audioOutput,
          audioTrackMode: audioTrackMode,
          lastError: null,
        ),
      );
      FroggyLogger.debug(
        'AudioBloc: Audio routing changed to ${event.mode}',
      );
    } catch (e) {
      FroggyLogger.error(
        'AudioBloc: Error handling audio routing change: $e',
      );
      emit(
        state.copyWith(
          lastError: 'Failed to handle audio routing change: $e',
        ),
      );
    }
  }

  /// Handles local media stream updates
  FutureOr<void> _onLocalStreamUpdated(
    _LocalStreamUpdated event,
    Emitter<AudioState> emit,
  ) {
    try {
      emit(
        state.copyWith(
          localMediaStream: event.stream,
          lastError: null,
        ),
      );
      FroggyLogger.debug(
        'AudioBloc: Local media stream updated',
      );
    } catch (e) {
      FroggyLogger.error(
        'AudioBloc: Error updating local stream: $e',
      );
      emit(
        state.copyWith(
          lastError: 'Failed to update local stream: $e',
        ),
      );
    }
  }

  /// Handles remote media stream updates
  FutureOr<void> _onRemoteStreamUpdated(
    _RemoteStreamUpdated event,
    Emitter<AudioState> emit,
  ) {
    try {
      emit(
        state.copyWith(
          remoteMediaStream: event.stream,
          lastError: null,
        ),
      );
      FroggyLogger.debug(
        'AudioBloc: Remote media stream updated',
      );
    } catch (e) {
      FroggyLogger.error(
        'AudioBloc: Error updating remote stream: $e',
      );
      emit(
        state.copyWith(
          lastError: 'Failed to update remote stream: $e',
        ),
      );
    }
  }

  /// Handles audio state reset
  FutureOr<void> _onAudioReset(
    _AudioReset event,
    Emitter<AudioState> emit,
  ) {
    try {
      emit(AudioState.initial());
      FroggyLogger.debug(
        'AudioBloc: Audio state reset successfully',
      );
    } catch (e) {
      FroggyLogger.error(
        'AudioBloc: Error resetting audio state: $e',
      );
    }
  }

  /// Converts AudioOutputMode to user-friendly display name
  String _getAudioOutputName(AudioOutputMode mode) {
    switch (mode) {
      case AudioOutputMode.bluetooth:
        return 'Bluetooth';
      case AudioOutputMode.wiredHeadset:
        return 'Wired Headset';
      case AudioOutputMode.speaker:
        return 'Speakerphone';
      case AudioOutputMode.earpiece:
        return 'Earpiece';
    }
  }

  /// Converts AudioOutputMode to CallAudioTracksMode
  CallAudioTracksMode _getAudioTrackMode(AudioOutputMode mode) {
    switch (mode) {
      case AudioOutputMode.bluetooth:
        return CallAudioTracksMode.bluetooth;
      case AudioOutputMode.wiredHeadset:
        return CallAudioTracksMode.wiredHeadset;
      case AudioOutputMode.speaker:
        return CallAudioTracksMode.speaker;
      case AudioOutputMode.earpiece:
        return CallAudioTracksMode.phone;
    }
  }
}
