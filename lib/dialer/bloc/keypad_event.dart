part of 'keypad_bloc.dart';

@freezed
class KeypadEvent with _$KeypadEvent {
  const factory KeypadEvent.started({required List<ContactModel> contacts}) =
      _Started;

  const factory KeypadEvent.phoneNumberChanged({
    String? value,
    @Default(true) bool makeDTMFSound,
  }) = _UpdatedPhoneNumber;

  const factory KeypadEvent.setPhoneNumber({
    String? value,
  }) = _ReplacedPhoneNumber;

  const factory KeypadEvent.deleteButtonPressed() = _DeleteButtonPressed;

  const factory KeypadEvent.updateCursorPosition(int position) =
      _UpdateCursorPosition;

  const factory KeypadEvent.searchedByDialingCode({String? value}) =
      _SearchedByDialingCode;

  const factory KeypadEvent.searchedContact({
    @Default([]) List<ContactModel> contacts,
  }) = _SearchedContact;

  const factory KeypadEvent.reset() = _Initial;
  const factory KeypadEvent.checkCallRates() = _CheckedCallRates;
}
