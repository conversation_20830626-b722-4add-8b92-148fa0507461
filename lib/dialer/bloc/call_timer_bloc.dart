import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:froggytalk/dialer/dialer.dart';
import 'package:utils/utils.dart';

part 'call_timer_bloc.freezed.dart';
part 'call_timer_event.dart';
part 'call_timer_state.dart';

/// Specialized BLoC for managing call timer functionality.
///
/// This BLoC handles all call timer-related operations including:
/// - Timer start/stop/pause/resume operations
/// - Elapsed time tracking and updates
/// - Timer formatting for UI display
/// - Timer state management during calls
///
/// Separates timer concerns from the main DialerBloc for better
/// maintainability and single responsibility principle.
class CallTimerBloc extends Bloc<CallTimerEvent, CallTimerState> {
  /// Creates a CallTimerBloc with timer service dependency.
  ///
  /// [countUpTimer] - Optional timer service, defaults to singleton
  /// instance.
  CallTimerBloc({
    CountUpTimer? countUpTimer,
  })  : _countUpTimer = countUpTimer ?? CountUpTimer(),
        super(CallTimerState.initial()) {
    // Register event handlers
    on<_TimerStarted>(_onTimerStarted);
    on<_TimerStartedWithTime>(_onTimerStartedWithTime);
    on<_TimerStopped>(_onTimerStopped);
    on<_TimerPaused>(_onTimerPaused);
    on<_TimerResumed>(_onTimerResumed);
    on<_TimerRestarted>(_onTimerRestarted);
    on<_TimerUpdated>(_onTimerUpdated);
    on<_TimerReset>(_onTimerReset);
    on<_SyncFromForegroundService>(_onSyncFromForegroundService);
  }

  final CountUpTimer _countUpTimer;
  StreamSubscription<Duration>? _timerSubscription;

  @override
  Future<void> close() {
    _timerSubscription?.cancel();
    return super.close();
  }

  /// Starts the call timer
  void startTimer() {
    add(const CallTimerEvent.timerStarted());
  }

  /// Stops the call timer
  void stopTimer() {
    add(const CallTimerEvent.timerStopped());
  }

  /// Pauses the call timer
  void pauseTimer() {
    add(const CallTimerEvent.timerPaused());
  }

  /// Resumes the call timer
  void resumeTimer() {
    add(const CallTimerEvent.timerResumed());
  }

  /// Restarts the call timer from zero
  void restartTimer() {
    add(const CallTimerEvent.timerRestarted());
  }

  /// Resets timer state to initial
  void resetTimer() {
    add(const CallTimerEvent.timerReset());
  }

  /// Handles timer start events
  FutureOr<void> _onTimerStarted(
    _TimerStarted event,
    Emitter<CallTimerState> emit,
  ) async {
    try {
      // Cancel any existing timer subscription
      await _timerSubscription?.cancel();

      // Start the timer
      _countUpTimer.start();

      // Listen to timer updates
      _timerSubscription =
          _countUpTimer.timeStream.listen((elapsed) {
        add(CallTimerEvent.timerUpdated(elapsed: elapsed));
      });

      emit(
        state.copyWith(
          isRunning: true,
          isPaused: false,
          lastError: null,
        ),
      );

      FroggyLogger.debug('CallTimerBloc: Timer started successfully');
    } catch (e) {
      FroggyLogger.error('CallTimerBloc: Error starting timer: $e');
      emit(
        state.copyWith(
          lastError: 'Failed to start timer: $e',
        ),
      );
    }
  }

  /// Handles timer start events with custom start time for synchronization
  FutureOr<void> _onTimerStartedWithTime(
    _TimerStartedWithTime event,
    Emitter<CallTimerState> emit,
  ) async {
    try {
      // Cancel any existing timer subscription
      await _timerSubscription?.cancel();

      // Start the timer with the provided start time
      _countUpTimer.startWithTime(event.startTime);

      // Listen to timer updates
      _timerSubscription =
          _countUpTimer.timeStream.listen((elapsed) {
        add(CallTimerEvent.timerUpdated(elapsed: elapsed));
      });

      emit(
        state.copyWith(
          isRunning: true,
          isPaused: false,
          lastError: null,
        ),
      );

      FroggyLogger.debug(
        'CallTimerBloc: Timer started with custom start time: '
        '${event.startTime}',
      );
    } catch (e) {
      FroggyLogger.error('CallTimerBloc: Error starting timer with time: $e');
      emit(
        state.copyWith(
          lastError: 'Failed to start timer with time: $e',
        ),
      );
    }
  }

  /// Handles timer stop events
  FutureOr<void> _onTimerStopped(
    _TimerStopped event,
    Emitter<CallTimerState> emit,
  ) {
    try {
      // Cancel timer subscription and stop timer
      _timerSubscription?.cancel();
      _countUpTimer.stop();

      emit(
        state.copyWith(
          isRunning: false,
          isPaused: false,
          lastError: null,
        ),
      );

      FroggyLogger.debug('CallTimerBloc: Timer stopped successfully');
    } catch (e) {
      FroggyLogger.error('CallTimerBloc: Error stopping timer: $e');
      emit(
        state.copyWith(
          lastError: 'Failed to stop timer: $e',
        ),
      );
    }
  }

  /// Handles timer pause events
  FutureOr<void> _onTimerPaused(
    _TimerPaused event,
    Emitter<CallTimerState> emit,
  ) {
    try {
      _countUpTimer.pause();

      emit(
        state.copyWith(
          isPaused: true,
          lastError: null,
        ),
      );

      FroggyLogger.debug('CallTimerBloc: Timer paused successfully');
    } catch (e) {
      FroggyLogger.error('CallTimerBloc: Error pausing timer: $e');
      emit(
        state.copyWith(
          lastError: 'Failed to pause timer: $e',
        ),
      );
    }
  }

  /// Handles timer resume events
  FutureOr<void> _onTimerResumed(
    _TimerResumed event,
    Emitter<CallTimerState> emit,
  ) {
    try {
      _countUpTimer.resume();

      emit(
        state.copyWith(
          isPaused: false,
          lastError: null,
        ),
      );

      FroggyLogger.debug('CallTimerBloc: Timer resumed successfully');
    } catch (e) {
      FroggyLogger.error('CallTimerBloc: Error resuming timer: $e');
      emit(
        state.copyWith(
          lastError: 'Failed to resume timer: $e',
        ),
      );
    }
  }

  /// Handles timer restart events
  FutureOr<void> _onTimerRestarted(
    _TimerRestarted event,
    Emitter<CallTimerState> emit,
  ) async {
    try {
      // Cancel existing subscription
      await _timerSubscription?.cancel();

      // Restart timer from zero
      _countUpTimer.restart();

      // Start new subscription
      _timerSubscription =
          _countUpTimer.timeStream.listen((elapsed) {
        add(CallTimerEvent.timerUpdated(elapsed: elapsed));
      });

      emit(
        state.copyWith(
          elapsedTime: Duration.zero,
          isRunning: true,
          isPaused: false,
          lastError: null,
        ),
      );

      FroggyLogger.debug('CallTimerBloc: Timer restarted successfully');
    } catch (e) {
      FroggyLogger.error('CallTimerBloc: Error restarting timer: $e');
      emit(
        state.copyWith(
          lastError: 'Failed to restart timer: $e',
        ),
      );
    }
  }

  /// Handles timer update events
  FutureOr<void> _onTimerUpdated(
    _TimerUpdated event,
    Emitter<CallTimerState> emit,
  ) {
    emit(
      state.copyWith(
        elapsedTime: event.elapsed,
        lastError: null,
      ),
    );
  }

  /// Handles timer reset events
  FutureOr<void> _onTimerReset(
    _TimerReset event,
    Emitter<CallTimerState> emit,
  ) async {
    try {
      // Stop and clean up timer
      await _timerSubscription?.cancel();
      _countUpTimer.stop();

      // Reset to initial state
      emit(CallTimerState.initial());

      FroggyLogger.debug('CallTimerBloc: Timer reset successfully');
    } catch (e) {
      FroggyLogger.error('CallTimerBloc: Error resetting timer: $e');
      emit(
        state.copyWith(
          lastError: 'Failed to reset timer: $e',
        ),
      );
    }
  }

  /// Handles synchronization from foreground service
  FutureOr<void> _onSyncFromForegroundService(
    _SyncFromForegroundService event,
    Emitter<CallTimerState> emit,
  ) async {
    try {
      // Validate incoming data
      if (event.duration.isNegative) {
        FroggyLogger.warning(
          'CallTimerBloc: Invalid negative duration from foreground service',
        );
        return;
      }

      // Update state with synchronized time from foreground service
      emit(
        state.copyWith(
          elapsedTime: event.duration,
          lastError: null,
        ),
      );

      FroggyLogger.debug(
        'CallTimerBloc: Synchronized with foreground service - Duration: '
        '${event.duration}, Timestamp: ${event.timestamp}',
      );
    } catch (e) {
      FroggyLogger.error(
        'CallTimerBloc: Error syncing from foreground service: $e',
      );
      emit(
        state.copyWith(
          lastError: 'Failed to sync from foreground service: $e',
        ),
      );
    }
  }
}
