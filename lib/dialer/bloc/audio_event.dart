part of 'audio_bloc.dart';

/// Audio-related events for the AudioBloc.
///
/// These events represent all possible audio-related actions that can
/// occur during a call, including speaker controls, mute functionality,
/// audio routing, and media stream management.
@freezed
class AudioEvent with _$AudioEvent {
  /// Toggles speaker phone on/off based on current state
  const factory AudioEvent.speakerToggled() = _SpeakerToggled;

  /// Enables speaker phone
  const factory AudioEvent.speakerEnabled() = _SpeakerEnabled;

  /// Disables speaker phone (returns to earpiece)
  const factory AudioEvent.speakerDisabled() = _SpeakerDisabled;

  /// Switches audio routing to Bluetooth headset
  const factory AudioEvent.bluetoothEnabled() = _BluetoothEnabled;

  /// Switches audio routing to wired headset
  const factory AudioEvent.wiredHeadsetEnabled() =
      _WiredHeadsetEnabled;

  /// Toggles mute on/off based on current state
  const factory AudioEvent.muteToggled() = _MuteToggled;

  /// Enables microphone mute
  const factory AudioEvent.muteEnabled() = _MuteEnabled;

  /// Disables microphone mute
  const factory AudioEvent.muteDisabled() = _MuteDisabled;

  /// System audio routing changed (from AudioRoutingManager)
  const factory AudioEvent.audioRoutingChanged({
    required AudioOutputMode mode,
  }) = _AudioRoutingChanged;

  /// Local media stream updated
  const factory AudioEvent.localStreamUpdated({
    MediaStream? stream,
  }) = _LocalStreamUpdated;

  /// Remote media stream updated
  const factory AudioEvent.remoteStreamUpdated({
    MediaStream? stream,
  }) = _RemoteStreamUpdated;

  /// Reset all audio settings to initial state
  const factory AudioEvent.audioReset() = _AudioReset;
}
