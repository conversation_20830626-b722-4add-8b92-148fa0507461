import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sip_ua/sip_ua.dart';

part 'outbound_call_event.dart';
part 'outbound_call_state.dart';
part 'outbound_call_bloc.freezed.dart';

class OutboundCallBloc extends Bloc<OutboundCallEvent, OutboundCallState> {
  OutboundCallBloc() : super(const _Initial()) {
    on<OutboundCallEvent>((event, emit) {
      // TODO(peterson): implement event handler
    });
  }
}
