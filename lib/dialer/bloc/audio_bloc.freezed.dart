// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'audio_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AudioEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() speakerToggled,
    required TResult Function() speakerEnabled,
    required TResult Function() speakerDisabled,
    required TResult Function() bluetoothEnabled,
    required TResult Function() wiredHeadsetEnabled,
    required TResult Function() muteToggled,
    required TResult Function() muteEnabled,
    required TResult Function() muteDisabled,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
    required TResult Function(MediaStream? stream) localStreamUpdated,
    required TResult Function(MediaStream? stream) remoteStreamUpdated,
    required TResult Function() audioReset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? speakerToggled,
    TResult? Function()? speakerEnabled,
    TResult? Function()? speakerDisabled,
    TResult? Function()? bluetoothEnabled,
    TResult? Function()? wiredHeadsetEnabled,
    TResult? Function()? muteToggled,
    TResult? Function()? muteEnabled,
    TResult? Function()? muteDisabled,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
    TResult? Function(MediaStream? stream)? localStreamUpdated,
    TResult? Function(MediaStream? stream)? remoteStreamUpdated,
    TResult? Function()? audioReset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? speakerToggled,
    TResult Function()? speakerEnabled,
    TResult Function()? speakerDisabled,
    TResult Function()? bluetoothEnabled,
    TResult Function()? wiredHeadsetEnabled,
    TResult Function()? muteToggled,
    TResult Function()? muteEnabled,
    TResult Function()? muteDisabled,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    TResult Function(MediaStream? stream)? localStreamUpdated,
    TResult Function(MediaStream? stream)? remoteStreamUpdated,
    TResult Function()? audioReset,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SpeakerToggled value) speakerToggled,
    required TResult Function(_SpeakerEnabled value) speakerEnabled,
    required TResult Function(_SpeakerDisabled value) speakerDisabled,
    required TResult Function(_BluetoothEnabled value) bluetoothEnabled,
    required TResult Function(_WiredHeadsetEnabled value) wiredHeadsetEnabled,
    required TResult Function(_MuteToggled value) muteToggled,
    required TResult Function(_MuteEnabled value) muteEnabled,
    required TResult Function(_MuteDisabled value) muteDisabled,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
    required TResult Function(_LocalStreamUpdated value) localStreamUpdated,
    required TResult Function(_RemoteStreamUpdated value) remoteStreamUpdated,
    required TResult Function(_AudioReset value) audioReset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SpeakerToggled value)? speakerToggled,
    TResult? Function(_SpeakerEnabled value)? speakerEnabled,
    TResult? Function(_SpeakerDisabled value)? speakerDisabled,
    TResult? Function(_BluetoothEnabled value)? bluetoothEnabled,
    TResult? Function(_WiredHeadsetEnabled value)? wiredHeadsetEnabled,
    TResult? Function(_MuteToggled value)? muteToggled,
    TResult? Function(_MuteEnabled value)? muteEnabled,
    TResult? Function(_MuteDisabled value)? muteDisabled,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
    TResult? Function(_LocalStreamUpdated value)? localStreamUpdated,
    TResult? Function(_RemoteStreamUpdated value)? remoteStreamUpdated,
    TResult? Function(_AudioReset value)? audioReset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SpeakerToggled value)? speakerToggled,
    TResult Function(_SpeakerEnabled value)? speakerEnabled,
    TResult Function(_SpeakerDisabled value)? speakerDisabled,
    TResult Function(_BluetoothEnabled value)? bluetoothEnabled,
    TResult Function(_WiredHeadsetEnabled value)? wiredHeadsetEnabled,
    TResult Function(_MuteToggled value)? muteToggled,
    TResult Function(_MuteEnabled value)? muteEnabled,
    TResult Function(_MuteDisabled value)? muteDisabled,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    TResult Function(_LocalStreamUpdated value)? localStreamUpdated,
    TResult Function(_RemoteStreamUpdated value)? remoteStreamUpdated,
    TResult Function(_AudioReset value)? audioReset,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AudioEventCopyWith<$Res> {
  factory $AudioEventCopyWith(
          AudioEvent value, $Res Function(AudioEvent) then) =
      _$AudioEventCopyWithImpl<$Res, AudioEvent>;
}

/// @nodoc
class _$AudioEventCopyWithImpl<$Res, $Val extends AudioEvent>
    implements $AudioEventCopyWith<$Res> {
  _$AudioEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AudioEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$SpeakerToggledImplCopyWith<$Res> {
  factory _$$SpeakerToggledImplCopyWith(_$SpeakerToggledImpl value,
          $Res Function(_$SpeakerToggledImpl) then) =
      __$$SpeakerToggledImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SpeakerToggledImplCopyWithImpl<$Res>
    extends _$AudioEventCopyWithImpl<$Res, _$SpeakerToggledImpl>
    implements _$$SpeakerToggledImplCopyWith<$Res> {
  __$$SpeakerToggledImplCopyWithImpl(
      _$SpeakerToggledImpl _value, $Res Function(_$SpeakerToggledImpl) _then)
      : super(_value, _then);

  /// Create a copy of AudioEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SpeakerToggledImpl
    with DiagnosticableTreeMixin
    implements _SpeakerToggled {
  const _$SpeakerToggledImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AudioEvent.speakerToggled()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'AudioEvent.speakerToggled'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SpeakerToggledImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() speakerToggled,
    required TResult Function() speakerEnabled,
    required TResult Function() speakerDisabled,
    required TResult Function() bluetoothEnabled,
    required TResult Function() wiredHeadsetEnabled,
    required TResult Function() muteToggled,
    required TResult Function() muteEnabled,
    required TResult Function() muteDisabled,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
    required TResult Function(MediaStream? stream) localStreamUpdated,
    required TResult Function(MediaStream? stream) remoteStreamUpdated,
    required TResult Function() audioReset,
  }) {
    return speakerToggled();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? speakerToggled,
    TResult? Function()? speakerEnabled,
    TResult? Function()? speakerDisabled,
    TResult? Function()? bluetoothEnabled,
    TResult? Function()? wiredHeadsetEnabled,
    TResult? Function()? muteToggled,
    TResult? Function()? muteEnabled,
    TResult? Function()? muteDisabled,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
    TResult? Function(MediaStream? stream)? localStreamUpdated,
    TResult? Function(MediaStream? stream)? remoteStreamUpdated,
    TResult? Function()? audioReset,
  }) {
    return speakerToggled?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? speakerToggled,
    TResult Function()? speakerEnabled,
    TResult Function()? speakerDisabled,
    TResult Function()? bluetoothEnabled,
    TResult Function()? wiredHeadsetEnabled,
    TResult Function()? muteToggled,
    TResult Function()? muteEnabled,
    TResult Function()? muteDisabled,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    TResult Function(MediaStream? stream)? localStreamUpdated,
    TResult Function(MediaStream? stream)? remoteStreamUpdated,
    TResult Function()? audioReset,
    required TResult orElse(),
  }) {
    if (speakerToggled != null) {
      return speakerToggled();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SpeakerToggled value) speakerToggled,
    required TResult Function(_SpeakerEnabled value) speakerEnabled,
    required TResult Function(_SpeakerDisabled value) speakerDisabled,
    required TResult Function(_BluetoothEnabled value) bluetoothEnabled,
    required TResult Function(_WiredHeadsetEnabled value) wiredHeadsetEnabled,
    required TResult Function(_MuteToggled value) muteToggled,
    required TResult Function(_MuteEnabled value) muteEnabled,
    required TResult Function(_MuteDisabled value) muteDisabled,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
    required TResult Function(_LocalStreamUpdated value) localStreamUpdated,
    required TResult Function(_RemoteStreamUpdated value) remoteStreamUpdated,
    required TResult Function(_AudioReset value) audioReset,
  }) {
    return speakerToggled(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SpeakerToggled value)? speakerToggled,
    TResult? Function(_SpeakerEnabled value)? speakerEnabled,
    TResult? Function(_SpeakerDisabled value)? speakerDisabled,
    TResult? Function(_BluetoothEnabled value)? bluetoothEnabled,
    TResult? Function(_WiredHeadsetEnabled value)? wiredHeadsetEnabled,
    TResult? Function(_MuteToggled value)? muteToggled,
    TResult? Function(_MuteEnabled value)? muteEnabled,
    TResult? Function(_MuteDisabled value)? muteDisabled,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
    TResult? Function(_LocalStreamUpdated value)? localStreamUpdated,
    TResult? Function(_RemoteStreamUpdated value)? remoteStreamUpdated,
    TResult? Function(_AudioReset value)? audioReset,
  }) {
    return speakerToggled?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SpeakerToggled value)? speakerToggled,
    TResult Function(_SpeakerEnabled value)? speakerEnabled,
    TResult Function(_SpeakerDisabled value)? speakerDisabled,
    TResult Function(_BluetoothEnabled value)? bluetoothEnabled,
    TResult Function(_WiredHeadsetEnabled value)? wiredHeadsetEnabled,
    TResult Function(_MuteToggled value)? muteToggled,
    TResult Function(_MuteEnabled value)? muteEnabled,
    TResult Function(_MuteDisabled value)? muteDisabled,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    TResult Function(_LocalStreamUpdated value)? localStreamUpdated,
    TResult Function(_RemoteStreamUpdated value)? remoteStreamUpdated,
    TResult Function(_AudioReset value)? audioReset,
    required TResult orElse(),
  }) {
    if (speakerToggled != null) {
      return speakerToggled(this);
    }
    return orElse();
  }
}

abstract class _SpeakerToggled implements AudioEvent {
  const factory _SpeakerToggled() = _$SpeakerToggledImpl;
}

/// @nodoc
abstract class _$$SpeakerEnabledImplCopyWith<$Res> {
  factory _$$SpeakerEnabledImplCopyWith(_$SpeakerEnabledImpl value,
          $Res Function(_$SpeakerEnabledImpl) then) =
      __$$SpeakerEnabledImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SpeakerEnabledImplCopyWithImpl<$Res>
    extends _$AudioEventCopyWithImpl<$Res, _$SpeakerEnabledImpl>
    implements _$$SpeakerEnabledImplCopyWith<$Res> {
  __$$SpeakerEnabledImplCopyWithImpl(
      _$SpeakerEnabledImpl _value, $Res Function(_$SpeakerEnabledImpl) _then)
      : super(_value, _then);

  /// Create a copy of AudioEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SpeakerEnabledImpl
    with DiagnosticableTreeMixin
    implements _SpeakerEnabled {
  const _$SpeakerEnabledImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AudioEvent.speakerEnabled()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'AudioEvent.speakerEnabled'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SpeakerEnabledImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() speakerToggled,
    required TResult Function() speakerEnabled,
    required TResult Function() speakerDisabled,
    required TResult Function() bluetoothEnabled,
    required TResult Function() wiredHeadsetEnabled,
    required TResult Function() muteToggled,
    required TResult Function() muteEnabled,
    required TResult Function() muteDisabled,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
    required TResult Function(MediaStream? stream) localStreamUpdated,
    required TResult Function(MediaStream? stream) remoteStreamUpdated,
    required TResult Function() audioReset,
  }) {
    return speakerEnabled();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? speakerToggled,
    TResult? Function()? speakerEnabled,
    TResult? Function()? speakerDisabled,
    TResult? Function()? bluetoothEnabled,
    TResult? Function()? wiredHeadsetEnabled,
    TResult? Function()? muteToggled,
    TResult? Function()? muteEnabled,
    TResult? Function()? muteDisabled,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
    TResult? Function(MediaStream? stream)? localStreamUpdated,
    TResult? Function(MediaStream? stream)? remoteStreamUpdated,
    TResult? Function()? audioReset,
  }) {
    return speakerEnabled?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? speakerToggled,
    TResult Function()? speakerEnabled,
    TResult Function()? speakerDisabled,
    TResult Function()? bluetoothEnabled,
    TResult Function()? wiredHeadsetEnabled,
    TResult Function()? muteToggled,
    TResult Function()? muteEnabled,
    TResult Function()? muteDisabled,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    TResult Function(MediaStream? stream)? localStreamUpdated,
    TResult Function(MediaStream? stream)? remoteStreamUpdated,
    TResult Function()? audioReset,
    required TResult orElse(),
  }) {
    if (speakerEnabled != null) {
      return speakerEnabled();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SpeakerToggled value) speakerToggled,
    required TResult Function(_SpeakerEnabled value) speakerEnabled,
    required TResult Function(_SpeakerDisabled value) speakerDisabled,
    required TResult Function(_BluetoothEnabled value) bluetoothEnabled,
    required TResult Function(_WiredHeadsetEnabled value) wiredHeadsetEnabled,
    required TResult Function(_MuteToggled value) muteToggled,
    required TResult Function(_MuteEnabled value) muteEnabled,
    required TResult Function(_MuteDisabled value) muteDisabled,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
    required TResult Function(_LocalStreamUpdated value) localStreamUpdated,
    required TResult Function(_RemoteStreamUpdated value) remoteStreamUpdated,
    required TResult Function(_AudioReset value) audioReset,
  }) {
    return speakerEnabled(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SpeakerToggled value)? speakerToggled,
    TResult? Function(_SpeakerEnabled value)? speakerEnabled,
    TResult? Function(_SpeakerDisabled value)? speakerDisabled,
    TResult? Function(_BluetoothEnabled value)? bluetoothEnabled,
    TResult? Function(_WiredHeadsetEnabled value)? wiredHeadsetEnabled,
    TResult? Function(_MuteToggled value)? muteToggled,
    TResult? Function(_MuteEnabled value)? muteEnabled,
    TResult? Function(_MuteDisabled value)? muteDisabled,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
    TResult? Function(_LocalStreamUpdated value)? localStreamUpdated,
    TResult? Function(_RemoteStreamUpdated value)? remoteStreamUpdated,
    TResult? Function(_AudioReset value)? audioReset,
  }) {
    return speakerEnabled?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SpeakerToggled value)? speakerToggled,
    TResult Function(_SpeakerEnabled value)? speakerEnabled,
    TResult Function(_SpeakerDisabled value)? speakerDisabled,
    TResult Function(_BluetoothEnabled value)? bluetoothEnabled,
    TResult Function(_WiredHeadsetEnabled value)? wiredHeadsetEnabled,
    TResult Function(_MuteToggled value)? muteToggled,
    TResult Function(_MuteEnabled value)? muteEnabled,
    TResult Function(_MuteDisabled value)? muteDisabled,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    TResult Function(_LocalStreamUpdated value)? localStreamUpdated,
    TResult Function(_RemoteStreamUpdated value)? remoteStreamUpdated,
    TResult Function(_AudioReset value)? audioReset,
    required TResult orElse(),
  }) {
    if (speakerEnabled != null) {
      return speakerEnabled(this);
    }
    return orElse();
  }
}

abstract class _SpeakerEnabled implements AudioEvent {
  const factory _SpeakerEnabled() = _$SpeakerEnabledImpl;
}

/// @nodoc
abstract class _$$SpeakerDisabledImplCopyWith<$Res> {
  factory _$$SpeakerDisabledImplCopyWith(_$SpeakerDisabledImpl value,
          $Res Function(_$SpeakerDisabledImpl) then) =
      __$$SpeakerDisabledImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SpeakerDisabledImplCopyWithImpl<$Res>
    extends _$AudioEventCopyWithImpl<$Res, _$SpeakerDisabledImpl>
    implements _$$SpeakerDisabledImplCopyWith<$Res> {
  __$$SpeakerDisabledImplCopyWithImpl(
      _$SpeakerDisabledImpl _value, $Res Function(_$SpeakerDisabledImpl) _then)
      : super(_value, _then);

  /// Create a copy of AudioEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SpeakerDisabledImpl
    with DiagnosticableTreeMixin
    implements _SpeakerDisabled {
  const _$SpeakerDisabledImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AudioEvent.speakerDisabled()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'AudioEvent.speakerDisabled'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SpeakerDisabledImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() speakerToggled,
    required TResult Function() speakerEnabled,
    required TResult Function() speakerDisabled,
    required TResult Function() bluetoothEnabled,
    required TResult Function() wiredHeadsetEnabled,
    required TResult Function() muteToggled,
    required TResult Function() muteEnabled,
    required TResult Function() muteDisabled,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
    required TResult Function(MediaStream? stream) localStreamUpdated,
    required TResult Function(MediaStream? stream) remoteStreamUpdated,
    required TResult Function() audioReset,
  }) {
    return speakerDisabled();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? speakerToggled,
    TResult? Function()? speakerEnabled,
    TResult? Function()? speakerDisabled,
    TResult? Function()? bluetoothEnabled,
    TResult? Function()? wiredHeadsetEnabled,
    TResult? Function()? muteToggled,
    TResult? Function()? muteEnabled,
    TResult? Function()? muteDisabled,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
    TResult? Function(MediaStream? stream)? localStreamUpdated,
    TResult? Function(MediaStream? stream)? remoteStreamUpdated,
    TResult? Function()? audioReset,
  }) {
    return speakerDisabled?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? speakerToggled,
    TResult Function()? speakerEnabled,
    TResult Function()? speakerDisabled,
    TResult Function()? bluetoothEnabled,
    TResult Function()? wiredHeadsetEnabled,
    TResult Function()? muteToggled,
    TResult Function()? muteEnabled,
    TResult Function()? muteDisabled,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    TResult Function(MediaStream? stream)? localStreamUpdated,
    TResult Function(MediaStream? stream)? remoteStreamUpdated,
    TResult Function()? audioReset,
    required TResult orElse(),
  }) {
    if (speakerDisabled != null) {
      return speakerDisabled();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SpeakerToggled value) speakerToggled,
    required TResult Function(_SpeakerEnabled value) speakerEnabled,
    required TResult Function(_SpeakerDisabled value) speakerDisabled,
    required TResult Function(_BluetoothEnabled value) bluetoothEnabled,
    required TResult Function(_WiredHeadsetEnabled value) wiredHeadsetEnabled,
    required TResult Function(_MuteToggled value) muteToggled,
    required TResult Function(_MuteEnabled value) muteEnabled,
    required TResult Function(_MuteDisabled value) muteDisabled,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
    required TResult Function(_LocalStreamUpdated value) localStreamUpdated,
    required TResult Function(_RemoteStreamUpdated value) remoteStreamUpdated,
    required TResult Function(_AudioReset value) audioReset,
  }) {
    return speakerDisabled(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SpeakerToggled value)? speakerToggled,
    TResult? Function(_SpeakerEnabled value)? speakerEnabled,
    TResult? Function(_SpeakerDisabled value)? speakerDisabled,
    TResult? Function(_BluetoothEnabled value)? bluetoothEnabled,
    TResult? Function(_WiredHeadsetEnabled value)? wiredHeadsetEnabled,
    TResult? Function(_MuteToggled value)? muteToggled,
    TResult? Function(_MuteEnabled value)? muteEnabled,
    TResult? Function(_MuteDisabled value)? muteDisabled,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
    TResult? Function(_LocalStreamUpdated value)? localStreamUpdated,
    TResult? Function(_RemoteStreamUpdated value)? remoteStreamUpdated,
    TResult? Function(_AudioReset value)? audioReset,
  }) {
    return speakerDisabled?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SpeakerToggled value)? speakerToggled,
    TResult Function(_SpeakerEnabled value)? speakerEnabled,
    TResult Function(_SpeakerDisabled value)? speakerDisabled,
    TResult Function(_BluetoothEnabled value)? bluetoothEnabled,
    TResult Function(_WiredHeadsetEnabled value)? wiredHeadsetEnabled,
    TResult Function(_MuteToggled value)? muteToggled,
    TResult Function(_MuteEnabled value)? muteEnabled,
    TResult Function(_MuteDisabled value)? muteDisabled,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    TResult Function(_LocalStreamUpdated value)? localStreamUpdated,
    TResult Function(_RemoteStreamUpdated value)? remoteStreamUpdated,
    TResult Function(_AudioReset value)? audioReset,
    required TResult orElse(),
  }) {
    if (speakerDisabled != null) {
      return speakerDisabled(this);
    }
    return orElse();
  }
}

abstract class _SpeakerDisabled implements AudioEvent {
  const factory _SpeakerDisabled() = _$SpeakerDisabledImpl;
}

/// @nodoc
abstract class _$$BluetoothEnabledImplCopyWith<$Res> {
  factory _$$BluetoothEnabledImplCopyWith(_$BluetoothEnabledImpl value,
          $Res Function(_$BluetoothEnabledImpl) then) =
      __$$BluetoothEnabledImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$BluetoothEnabledImplCopyWithImpl<$Res>
    extends _$AudioEventCopyWithImpl<$Res, _$BluetoothEnabledImpl>
    implements _$$BluetoothEnabledImplCopyWith<$Res> {
  __$$BluetoothEnabledImplCopyWithImpl(_$BluetoothEnabledImpl _value,
      $Res Function(_$BluetoothEnabledImpl) _then)
      : super(_value, _then);

  /// Create a copy of AudioEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$BluetoothEnabledImpl
    with DiagnosticableTreeMixin
    implements _BluetoothEnabled {
  const _$BluetoothEnabledImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AudioEvent.bluetoothEnabled()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'AudioEvent.bluetoothEnabled'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$BluetoothEnabledImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() speakerToggled,
    required TResult Function() speakerEnabled,
    required TResult Function() speakerDisabled,
    required TResult Function() bluetoothEnabled,
    required TResult Function() wiredHeadsetEnabled,
    required TResult Function() muteToggled,
    required TResult Function() muteEnabled,
    required TResult Function() muteDisabled,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
    required TResult Function(MediaStream? stream) localStreamUpdated,
    required TResult Function(MediaStream? stream) remoteStreamUpdated,
    required TResult Function() audioReset,
  }) {
    return bluetoothEnabled();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? speakerToggled,
    TResult? Function()? speakerEnabled,
    TResult? Function()? speakerDisabled,
    TResult? Function()? bluetoothEnabled,
    TResult? Function()? wiredHeadsetEnabled,
    TResult? Function()? muteToggled,
    TResult? Function()? muteEnabled,
    TResult? Function()? muteDisabled,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
    TResult? Function(MediaStream? stream)? localStreamUpdated,
    TResult? Function(MediaStream? stream)? remoteStreamUpdated,
    TResult? Function()? audioReset,
  }) {
    return bluetoothEnabled?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? speakerToggled,
    TResult Function()? speakerEnabled,
    TResult Function()? speakerDisabled,
    TResult Function()? bluetoothEnabled,
    TResult Function()? wiredHeadsetEnabled,
    TResult Function()? muteToggled,
    TResult Function()? muteEnabled,
    TResult Function()? muteDisabled,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    TResult Function(MediaStream? stream)? localStreamUpdated,
    TResult Function(MediaStream? stream)? remoteStreamUpdated,
    TResult Function()? audioReset,
    required TResult orElse(),
  }) {
    if (bluetoothEnabled != null) {
      return bluetoothEnabled();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SpeakerToggled value) speakerToggled,
    required TResult Function(_SpeakerEnabled value) speakerEnabled,
    required TResult Function(_SpeakerDisabled value) speakerDisabled,
    required TResult Function(_BluetoothEnabled value) bluetoothEnabled,
    required TResult Function(_WiredHeadsetEnabled value) wiredHeadsetEnabled,
    required TResult Function(_MuteToggled value) muteToggled,
    required TResult Function(_MuteEnabled value) muteEnabled,
    required TResult Function(_MuteDisabled value) muteDisabled,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
    required TResult Function(_LocalStreamUpdated value) localStreamUpdated,
    required TResult Function(_RemoteStreamUpdated value) remoteStreamUpdated,
    required TResult Function(_AudioReset value) audioReset,
  }) {
    return bluetoothEnabled(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SpeakerToggled value)? speakerToggled,
    TResult? Function(_SpeakerEnabled value)? speakerEnabled,
    TResult? Function(_SpeakerDisabled value)? speakerDisabled,
    TResult? Function(_BluetoothEnabled value)? bluetoothEnabled,
    TResult? Function(_WiredHeadsetEnabled value)? wiredHeadsetEnabled,
    TResult? Function(_MuteToggled value)? muteToggled,
    TResult? Function(_MuteEnabled value)? muteEnabled,
    TResult? Function(_MuteDisabled value)? muteDisabled,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
    TResult? Function(_LocalStreamUpdated value)? localStreamUpdated,
    TResult? Function(_RemoteStreamUpdated value)? remoteStreamUpdated,
    TResult? Function(_AudioReset value)? audioReset,
  }) {
    return bluetoothEnabled?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SpeakerToggled value)? speakerToggled,
    TResult Function(_SpeakerEnabled value)? speakerEnabled,
    TResult Function(_SpeakerDisabled value)? speakerDisabled,
    TResult Function(_BluetoothEnabled value)? bluetoothEnabled,
    TResult Function(_WiredHeadsetEnabled value)? wiredHeadsetEnabled,
    TResult Function(_MuteToggled value)? muteToggled,
    TResult Function(_MuteEnabled value)? muteEnabled,
    TResult Function(_MuteDisabled value)? muteDisabled,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    TResult Function(_LocalStreamUpdated value)? localStreamUpdated,
    TResult Function(_RemoteStreamUpdated value)? remoteStreamUpdated,
    TResult Function(_AudioReset value)? audioReset,
    required TResult orElse(),
  }) {
    if (bluetoothEnabled != null) {
      return bluetoothEnabled(this);
    }
    return orElse();
  }
}

abstract class _BluetoothEnabled implements AudioEvent {
  const factory _BluetoothEnabled() = _$BluetoothEnabledImpl;
}

/// @nodoc
abstract class _$$WiredHeadsetEnabledImplCopyWith<$Res> {
  factory _$$WiredHeadsetEnabledImplCopyWith(_$WiredHeadsetEnabledImpl value,
          $Res Function(_$WiredHeadsetEnabledImpl) then) =
      __$$WiredHeadsetEnabledImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$WiredHeadsetEnabledImplCopyWithImpl<$Res>
    extends _$AudioEventCopyWithImpl<$Res, _$WiredHeadsetEnabledImpl>
    implements _$$WiredHeadsetEnabledImplCopyWith<$Res> {
  __$$WiredHeadsetEnabledImplCopyWithImpl(_$WiredHeadsetEnabledImpl _value,
      $Res Function(_$WiredHeadsetEnabledImpl) _then)
      : super(_value, _then);

  /// Create a copy of AudioEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$WiredHeadsetEnabledImpl
    with DiagnosticableTreeMixin
    implements _WiredHeadsetEnabled {
  const _$WiredHeadsetEnabledImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AudioEvent.wiredHeadsetEnabled()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AudioEvent.wiredHeadsetEnabled'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WiredHeadsetEnabledImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() speakerToggled,
    required TResult Function() speakerEnabled,
    required TResult Function() speakerDisabled,
    required TResult Function() bluetoothEnabled,
    required TResult Function() wiredHeadsetEnabled,
    required TResult Function() muteToggled,
    required TResult Function() muteEnabled,
    required TResult Function() muteDisabled,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
    required TResult Function(MediaStream? stream) localStreamUpdated,
    required TResult Function(MediaStream? stream) remoteStreamUpdated,
    required TResult Function() audioReset,
  }) {
    return wiredHeadsetEnabled();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? speakerToggled,
    TResult? Function()? speakerEnabled,
    TResult? Function()? speakerDisabled,
    TResult? Function()? bluetoothEnabled,
    TResult? Function()? wiredHeadsetEnabled,
    TResult? Function()? muteToggled,
    TResult? Function()? muteEnabled,
    TResult? Function()? muteDisabled,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
    TResult? Function(MediaStream? stream)? localStreamUpdated,
    TResult? Function(MediaStream? stream)? remoteStreamUpdated,
    TResult? Function()? audioReset,
  }) {
    return wiredHeadsetEnabled?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? speakerToggled,
    TResult Function()? speakerEnabled,
    TResult Function()? speakerDisabled,
    TResult Function()? bluetoothEnabled,
    TResult Function()? wiredHeadsetEnabled,
    TResult Function()? muteToggled,
    TResult Function()? muteEnabled,
    TResult Function()? muteDisabled,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    TResult Function(MediaStream? stream)? localStreamUpdated,
    TResult Function(MediaStream? stream)? remoteStreamUpdated,
    TResult Function()? audioReset,
    required TResult orElse(),
  }) {
    if (wiredHeadsetEnabled != null) {
      return wiredHeadsetEnabled();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SpeakerToggled value) speakerToggled,
    required TResult Function(_SpeakerEnabled value) speakerEnabled,
    required TResult Function(_SpeakerDisabled value) speakerDisabled,
    required TResult Function(_BluetoothEnabled value) bluetoothEnabled,
    required TResult Function(_WiredHeadsetEnabled value) wiredHeadsetEnabled,
    required TResult Function(_MuteToggled value) muteToggled,
    required TResult Function(_MuteEnabled value) muteEnabled,
    required TResult Function(_MuteDisabled value) muteDisabled,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
    required TResult Function(_LocalStreamUpdated value) localStreamUpdated,
    required TResult Function(_RemoteStreamUpdated value) remoteStreamUpdated,
    required TResult Function(_AudioReset value) audioReset,
  }) {
    return wiredHeadsetEnabled(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SpeakerToggled value)? speakerToggled,
    TResult? Function(_SpeakerEnabled value)? speakerEnabled,
    TResult? Function(_SpeakerDisabled value)? speakerDisabled,
    TResult? Function(_BluetoothEnabled value)? bluetoothEnabled,
    TResult? Function(_WiredHeadsetEnabled value)? wiredHeadsetEnabled,
    TResult? Function(_MuteToggled value)? muteToggled,
    TResult? Function(_MuteEnabled value)? muteEnabled,
    TResult? Function(_MuteDisabled value)? muteDisabled,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
    TResult? Function(_LocalStreamUpdated value)? localStreamUpdated,
    TResult? Function(_RemoteStreamUpdated value)? remoteStreamUpdated,
    TResult? Function(_AudioReset value)? audioReset,
  }) {
    return wiredHeadsetEnabled?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SpeakerToggled value)? speakerToggled,
    TResult Function(_SpeakerEnabled value)? speakerEnabled,
    TResult Function(_SpeakerDisabled value)? speakerDisabled,
    TResult Function(_BluetoothEnabled value)? bluetoothEnabled,
    TResult Function(_WiredHeadsetEnabled value)? wiredHeadsetEnabled,
    TResult Function(_MuteToggled value)? muteToggled,
    TResult Function(_MuteEnabled value)? muteEnabled,
    TResult Function(_MuteDisabled value)? muteDisabled,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    TResult Function(_LocalStreamUpdated value)? localStreamUpdated,
    TResult Function(_RemoteStreamUpdated value)? remoteStreamUpdated,
    TResult Function(_AudioReset value)? audioReset,
    required TResult orElse(),
  }) {
    if (wiredHeadsetEnabled != null) {
      return wiredHeadsetEnabled(this);
    }
    return orElse();
  }
}

abstract class _WiredHeadsetEnabled implements AudioEvent {
  const factory _WiredHeadsetEnabled() = _$WiredHeadsetEnabledImpl;
}

/// @nodoc
abstract class _$$MuteToggledImplCopyWith<$Res> {
  factory _$$MuteToggledImplCopyWith(
          _$MuteToggledImpl value, $Res Function(_$MuteToggledImpl) then) =
      __$$MuteToggledImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$MuteToggledImplCopyWithImpl<$Res>
    extends _$AudioEventCopyWithImpl<$Res, _$MuteToggledImpl>
    implements _$$MuteToggledImplCopyWith<$Res> {
  __$$MuteToggledImplCopyWithImpl(
      _$MuteToggledImpl _value, $Res Function(_$MuteToggledImpl) _then)
      : super(_value, _then);

  /// Create a copy of AudioEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$MuteToggledImpl with DiagnosticableTreeMixin implements _MuteToggled {
  const _$MuteToggledImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AudioEvent.muteToggled()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'AudioEvent.muteToggled'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$MuteToggledImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() speakerToggled,
    required TResult Function() speakerEnabled,
    required TResult Function() speakerDisabled,
    required TResult Function() bluetoothEnabled,
    required TResult Function() wiredHeadsetEnabled,
    required TResult Function() muteToggled,
    required TResult Function() muteEnabled,
    required TResult Function() muteDisabled,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
    required TResult Function(MediaStream? stream) localStreamUpdated,
    required TResult Function(MediaStream? stream) remoteStreamUpdated,
    required TResult Function() audioReset,
  }) {
    return muteToggled();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? speakerToggled,
    TResult? Function()? speakerEnabled,
    TResult? Function()? speakerDisabled,
    TResult? Function()? bluetoothEnabled,
    TResult? Function()? wiredHeadsetEnabled,
    TResult? Function()? muteToggled,
    TResult? Function()? muteEnabled,
    TResult? Function()? muteDisabled,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
    TResult? Function(MediaStream? stream)? localStreamUpdated,
    TResult? Function(MediaStream? stream)? remoteStreamUpdated,
    TResult? Function()? audioReset,
  }) {
    return muteToggled?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? speakerToggled,
    TResult Function()? speakerEnabled,
    TResult Function()? speakerDisabled,
    TResult Function()? bluetoothEnabled,
    TResult Function()? wiredHeadsetEnabled,
    TResult Function()? muteToggled,
    TResult Function()? muteEnabled,
    TResult Function()? muteDisabled,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    TResult Function(MediaStream? stream)? localStreamUpdated,
    TResult Function(MediaStream? stream)? remoteStreamUpdated,
    TResult Function()? audioReset,
    required TResult orElse(),
  }) {
    if (muteToggled != null) {
      return muteToggled();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SpeakerToggled value) speakerToggled,
    required TResult Function(_SpeakerEnabled value) speakerEnabled,
    required TResult Function(_SpeakerDisabled value) speakerDisabled,
    required TResult Function(_BluetoothEnabled value) bluetoothEnabled,
    required TResult Function(_WiredHeadsetEnabled value) wiredHeadsetEnabled,
    required TResult Function(_MuteToggled value) muteToggled,
    required TResult Function(_MuteEnabled value) muteEnabled,
    required TResult Function(_MuteDisabled value) muteDisabled,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
    required TResult Function(_LocalStreamUpdated value) localStreamUpdated,
    required TResult Function(_RemoteStreamUpdated value) remoteStreamUpdated,
    required TResult Function(_AudioReset value) audioReset,
  }) {
    return muteToggled(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SpeakerToggled value)? speakerToggled,
    TResult? Function(_SpeakerEnabled value)? speakerEnabled,
    TResult? Function(_SpeakerDisabled value)? speakerDisabled,
    TResult? Function(_BluetoothEnabled value)? bluetoothEnabled,
    TResult? Function(_WiredHeadsetEnabled value)? wiredHeadsetEnabled,
    TResult? Function(_MuteToggled value)? muteToggled,
    TResult? Function(_MuteEnabled value)? muteEnabled,
    TResult? Function(_MuteDisabled value)? muteDisabled,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
    TResult? Function(_LocalStreamUpdated value)? localStreamUpdated,
    TResult? Function(_RemoteStreamUpdated value)? remoteStreamUpdated,
    TResult? Function(_AudioReset value)? audioReset,
  }) {
    return muteToggled?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SpeakerToggled value)? speakerToggled,
    TResult Function(_SpeakerEnabled value)? speakerEnabled,
    TResult Function(_SpeakerDisabled value)? speakerDisabled,
    TResult Function(_BluetoothEnabled value)? bluetoothEnabled,
    TResult Function(_WiredHeadsetEnabled value)? wiredHeadsetEnabled,
    TResult Function(_MuteToggled value)? muteToggled,
    TResult Function(_MuteEnabled value)? muteEnabled,
    TResult Function(_MuteDisabled value)? muteDisabled,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    TResult Function(_LocalStreamUpdated value)? localStreamUpdated,
    TResult Function(_RemoteStreamUpdated value)? remoteStreamUpdated,
    TResult Function(_AudioReset value)? audioReset,
    required TResult orElse(),
  }) {
    if (muteToggled != null) {
      return muteToggled(this);
    }
    return orElse();
  }
}

abstract class _MuteToggled implements AudioEvent {
  const factory _MuteToggled() = _$MuteToggledImpl;
}

/// @nodoc
abstract class _$$MuteEnabledImplCopyWith<$Res> {
  factory _$$MuteEnabledImplCopyWith(
          _$MuteEnabledImpl value, $Res Function(_$MuteEnabledImpl) then) =
      __$$MuteEnabledImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$MuteEnabledImplCopyWithImpl<$Res>
    extends _$AudioEventCopyWithImpl<$Res, _$MuteEnabledImpl>
    implements _$$MuteEnabledImplCopyWith<$Res> {
  __$$MuteEnabledImplCopyWithImpl(
      _$MuteEnabledImpl _value, $Res Function(_$MuteEnabledImpl) _then)
      : super(_value, _then);

  /// Create a copy of AudioEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$MuteEnabledImpl with DiagnosticableTreeMixin implements _MuteEnabled {
  const _$MuteEnabledImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AudioEvent.muteEnabled()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'AudioEvent.muteEnabled'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$MuteEnabledImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() speakerToggled,
    required TResult Function() speakerEnabled,
    required TResult Function() speakerDisabled,
    required TResult Function() bluetoothEnabled,
    required TResult Function() wiredHeadsetEnabled,
    required TResult Function() muteToggled,
    required TResult Function() muteEnabled,
    required TResult Function() muteDisabled,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
    required TResult Function(MediaStream? stream) localStreamUpdated,
    required TResult Function(MediaStream? stream) remoteStreamUpdated,
    required TResult Function() audioReset,
  }) {
    return muteEnabled();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? speakerToggled,
    TResult? Function()? speakerEnabled,
    TResult? Function()? speakerDisabled,
    TResult? Function()? bluetoothEnabled,
    TResult? Function()? wiredHeadsetEnabled,
    TResult? Function()? muteToggled,
    TResult? Function()? muteEnabled,
    TResult? Function()? muteDisabled,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
    TResult? Function(MediaStream? stream)? localStreamUpdated,
    TResult? Function(MediaStream? stream)? remoteStreamUpdated,
    TResult? Function()? audioReset,
  }) {
    return muteEnabled?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? speakerToggled,
    TResult Function()? speakerEnabled,
    TResult Function()? speakerDisabled,
    TResult Function()? bluetoothEnabled,
    TResult Function()? wiredHeadsetEnabled,
    TResult Function()? muteToggled,
    TResult Function()? muteEnabled,
    TResult Function()? muteDisabled,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    TResult Function(MediaStream? stream)? localStreamUpdated,
    TResult Function(MediaStream? stream)? remoteStreamUpdated,
    TResult Function()? audioReset,
    required TResult orElse(),
  }) {
    if (muteEnabled != null) {
      return muteEnabled();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SpeakerToggled value) speakerToggled,
    required TResult Function(_SpeakerEnabled value) speakerEnabled,
    required TResult Function(_SpeakerDisabled value) speakerDisabled,
    required TResult Function(_BluetoothEnabled value) bluetoothEnabled,
    required TResult Function(_WiredHeadsetEnabled value) wiredHeadsetEnabled,
    required TResult Function(_MuteToggled value) muteToggled,
    required TResult Function(_MuteEnabled value) muteEnabled,
    required TResult Function(_MuteDisabled value) muteDisabled,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
    required TResult Function(_LocalStreamUpdated value) localStreamUpdated,
    required TResult Function(_RemoteStreamUpdated value) remoteStreamUpdated,
    required TResult Function(_AudioReset value) audioReset,
  }) {
    return muteEnabled(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SpeakerToggled value)? speakerToggled,
    TResult? Function(_SpeakerEnabled value)? speakerEnabled,
    TResult? Function(_SpeakerDisabled value)? speakerDisabled,
    TResult? Function(_BluetoothEnabled value)? bluetoothEnabled,
    TResult? Function(_WiredHeadsetEnabled value)? wiredHeadsetEnabled,
    TResult? Function(_MuteToggled value)? muteToggled,
    TResult? Function(_MuteEnabled value)? muteEnabled,
    TResult? Function(_MuteDisabled value)? muteDisabled,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
    TResult? Function(_LocalStreamUpdated value)? localStreamUpdated,
    TResult? Function(_RemoteStreamUpdated value)? remoteStreamUpdated,
    TResult? Function(_AudioReset value)? audioReset,
  }) {
    return muteEnabled?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SpeakerToggled value)? speakerToggled,
    TResult Function(_SpeakerEnabled value)? speakerEnabled,
    TResult Function(_SpeakerDisabled value)? speakerDisabled,
    TResult Function(_BluetoothEnabled value)? bluetoothEnabled,
    TResult Function(_WiredHeadsetEnabled value)? wiredHeadsetEnabled,
    TResult Function(_MuteToggled value)? muteToggled,
    TResult Function(_MuteEnabled value)? muteEnabled,
    TResult Function(_MuteDisabled value)? muteDisabled,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    TResult Function(_LocalStreamUpdated value)? localStreamUpdated,
    TResult Function(_RemoteStreamUpdated value)? remoteStreamUpdated,
    TResult Function(_AudioReset value)? audioReset,
    required TResult orElse(),
  }) {
    if (muteEnabled != null) {
      return muteEnabled(this);
    }
    return orElse();
  }
}

abstract class _MuteEnabled implements AudioEvent {
  const factory _MuteEnabled() = _$MuteEnabledImpl;
}

/// @nodoc
abstract class _$$MuteDisabledImplCopyWith<$Res> {
  factory _$$MuteDisabledImplCopyWith(
          _$MuteDisabledImpl value, $Res Function(_$MuteDisabledImpl) then) =
      __$$MuteDisabledImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$MuteDisabledImplCopyWithImpl<$Res>
    extends _$AudioEventCopyWithImpl<$Res, _$MuteDisabledImpl>
    implements _$$MuteDisabledImplCopyWith<$Res> {
  __$$MuteDisabledImplCopyWithImpl(
      _$MuteDisabledImpl _value, $Res Function(_$MuteDisabledImpl) _then)
      : super(_value, _then);

  /// Create a copy of AudioEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$MuteDisabledImpl with DiagnosticableTreeMixin implements _MuteDisabled {
  const _$MuteDisabledImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AudioEvent.muteDisabled()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'AudioEvent.muteDisabled'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$MuteDisabledImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() speakerToggled,
    required TResult Function() speakerEnabled,
    required TResult Function() speakerDisabled,
    required TResult Function() bluetoothEnabled,
    required TResult Function() wiredHeadsetEnabled,
    required TResult Function() muteToggled,
    required TResult Function() muteEnabled,
    required TResult Function() muteDisabled,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
    required TResult Function(MediaStream? stream) localStreamUpdated,
    required TResult Function(MediaStream? stream) remoteStreamUpdated,
    required TResult Function() audioReset,
  }) {
    return muteDisabled();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? speakerToggled,
    TResult? Function()? speakerEnabled,
    TResult? Function()? speakerDisabled,
    TResult? Function()? bluetoothEnabled,
    TResult? Function()? wiredHeadsetEnabled,
    TResult? Function()? muteToggled,
    TResult? Function()? muteEnabled,
    TResult? Function()? muteDisabled,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
    TResult? Function(MediaStream? stream)? localStreamUpdated,
    TResult? Function(MediaStream? stream)? remoteStreamUpdated,
    TResult? Function()? audioReset,
  }) {
    return muteDisabled?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? speakerToggled,
    TResult Function()? speakerEnabled,
    TResult Function()? speakerDisabled,
    TResult Function()? bluetoothEnabled,
    TResult Function()? wiredHeadsetEnabled,
    TResult Function()? muteToggled,
    TResult Function()? muteEnabled,
    TResult Function()? muteDisabled,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    TResult Function(MediaStream? stream)? localStreamUpdated,
    TResult Function(MediaStream? stream)? remoteStreamUpdated,
    TResult Function()? audioReset,
    required TResult orElse(),
  }) {
    if (muteDisabled != null) {
      return muteDisabled();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SpeakerToggled value) speakerToggled,
    required TResult Function(_SpeakerEnabled value) speakerEnabled,
    required TResult Function(_SpeakerDisabled value) speakerDisabled,
    required TResult Function(_BluetoothEnabled value) bluetoothEnabled,
    required TResult Function(_WiredHeadsetEnabled value) wiredHeadsetEnabled,
    required TResult Function(_MuteToggled value) muteToggled,
    required TResult Function(_MuteEnabled value) muteEnabled,
    required TResult Function(_MuteDisabled value) muteDisabled,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
    required TResult Function(_LocalStreamUpdated value) localStreamUpdated,
    required TResult Function(_RemoteStreamUpdated value) remoteStreamUpdated,
    required TResult Function(_AudioReset value) audioReset,
  }) {
    return muteDisabled(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SpeakerToggled value)? speakerToggled,
    TResult? Function(_SpeakerEnabled value)? speakerEnabled,
    TResult? Function(_SpeakerDisabled value)? speakerDisabled,
    TResult? Function(_BluetoothEnabled value)? bluetoothEnabled,
    TResult? Function(_WiredHeadsetEnabled value)? wiredHeadsetEnabled,
    TResult? Function(_MuteToggled value)? muteToggled,
    TResult? Function(_MuteEnabled value)? muteEnabled,
    TResult? Function(_MuteDisabled value)? muteDisabled,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
    TResult? Function(_LocalStreamUpdated value)? localStreamUpdated,
    TResult? Function(_RemoteStreamUpdated value)? remoteStreamUpdated,
    TResult? Function(_AudioReset value)? audioReset,
  }) {
    return muteDisabled?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SpeakerToggled value)? speakerToggled,
    TResult Function(_SpeakerEnabled value)? speakerEnabled,
    TResult Function(_SpeakerDisabled value)? speakerDisabled,
    TResult Function(_BluetoothEnabled value)? bluetoothEnabled,
    TResult Function(_WiredHeadsetEnabled value)? wiredHeadsetEnabled,
    TResult Function(_MuteToggled value)? muteToggled,
    TResult Function(_MuteEnabled value)? muteEnabled,
    TResult Function(_MuteDisabled value)? muteDisabled,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    TResult Function(_LocalStreamUpdated value)? localStreamUpdated,
    TResult Function(_RemoteStreamUpdated value)? remoteStreamUpdated,
    TResult Function(_AudioReset value)? audioReset,
    required TResult orElse(),
  }) {
    if (muteDisabled != null) {
      return muteDisabled(this);
    }
    return orElse();
  }
}

abstract class _MuteDisabled implements AudioEvent {
  const factory _MuteDisabled() = _$MuteDisabledImpl;
}

/// @nodoc
abstract class _$$AudioRoutingChangedImplCopyWith<$Res> {
  factory _$$AudioRoutingChangedImplCopyWith(_$AudioRoutingChangedImpl value,
          $Res Function(_$AudioRoutingChangedImpl) then) =
      __$$AudioRoutingChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AudioOutputMode mode});
}

/// @nodoc
class __$$AudioRoutingChangedImplCopyWithImpl<$Res>
    extends _$AudioEventCopyWithImpl<$Res, _$AudioRoutingChangedImpl>
    implements _$$AudioRoutingChangedImplCopyWith<$Res> {
  __$$AudioRoutingChangedImplCopyWithImpl(_$AudioRoutingChangedImpl _value,
      $Res Function(_$AudioRoutingChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of AudioEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mode = null,
  }) {
    return _then(_$AudioRoutingChangedImpl(
      mode: null == mode
          ? _value.mode
          : mode // ignore: cast_nullable_to_non_nullable
              as AudioOutputMode,
    ));
  }
}

/// @nodoc

class _$AudioRoutingChangedImpl
    with DiagnosticableTreeMixin
    implements _AudioRoutingChanged {
  const _$AudioRoutingChangedImpl({required this.mode});

  @override
  final AudioOutputMode mode;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AudioEvent.audioRoutingChanged(mode: $mode)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AudioEvent.audioRoutingChanged'))
      ..add(DiagnosticsProperty('mode', mode));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AudioRoutingChangedImpl &&
            (identical(other.mode, mode) || other.mode == mode));
  }

  @override
  int get hashCode => Object.hash(runtimeType, mode);

  /// Create a copy of AudioEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AudioRoutingChangedImplCopyWith<_$AudioRoutingChangedImpl> get copyWith =>
      __$$AudioRoutingChangedImplCopyWithImpl<_$AudioRoutingChangedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() speakerToggled,
    required TResult Function() speakerEnabled,
    required TResult Function() speakerDisabled,
    required TResult Function() bluetoothEnabled,
    required TResult Function() wiredHeadsetEnabled,
    required TResult Function() muteToggled,
    required TResult Function() muteEnabled,
    required TResult Function() muteDisabled,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
    required TResult Function(MediaStream? stream) localStreamUpdated,
    required TResult Function(MediaStream? stream) remoteStreamUpdated,
    required TResult Function() audioReset,
  }) {
    return audioRoutingChanged(mode);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? speakerToggled,
    TResult? Function()? speakerEnabled,
    TResult? Function()? speakerDisabled,
    TResult? Function()? bluetoothEnabled,
    TResult? Function()? wiredHeadsetEnabled,
    TResult? Function()? muteToggled,
    TResult? Function()? muteEnabled,
    TResult? Function()? muteDisabled,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
    TResult? Function(MediaStream? stream)? localStreamUpdated,
    TResult? Function(MediaStream? stream)? remoteStreamUpdated,
    TResult? Function()? audioReset,
  }) {
    return audioRoutingChanged?.call(mode);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? speakerToggled,
    TResult Function()? speakerEnabled,
    TResult Function()? speakerDisabled,
    TResult Function()? bluetoothEnabled,
    TResult Function()? wiredHeadsetEnabled,
    TResult Function()? muteToggled,
    TResult Function()? muteEnabled,
    TResult Function()? muteDisabled,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    TResult Function(MediaStream? stream)? localStreamUpdated,
    TResult Function(MediaStream? stream)? remoteStreamUpdated,
    TResult Function()? audioReset,
    required TResult orElse(),
  }) {
    if (audioRoutingChanged != null) {
      return audioRoutingChanged(mode);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SpeakerToggled value) speakerToggled,
    required TResult Function(_SpeakerEnabled value) speakerEnabled,
    required TResult Function(_SpeakerDisabled value) speakerDisabled,
    required TResult Function(_BluetoothEnabled value) bluetoothEnabled,
    required TResult Function(_WiredHeadsetEnabled value) wiredHeadsetEnabled,
    required TResult Function(_MuteToggled value) muteToggled,
    required TResult Function(_MuteEnabled value) muteEnabled,
    required TResult Function(_MuteDisabled value) muteDisabled,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
    required TResult Function(_LocalStreamUpdated value) localStreamUpdated,
    required TResult Function(_RemoteStreamUpdated value) remoteStreamUpdated,
    required TResult Function(_AudioReset value) audioReset,
  }) {
    return audioRoutingChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SpeakerToggled value)? speakerToggled,
    TResult? Function(_SpeakerEnabled value)? speakerEnabled,
    TResult? Function(_SpeakerDisabled value)? speakerDisabled,
    TResult? Function(_BluetoothEnabled value)? bluetoothEnabled,
    TResult? Function(_WiredHeadsetEnabled value)? wiredHeadsetEnabled,
    TResult? Function(_MuteToggled value)? muteToggled,
    TResult? Function(_MuteEnabled value)? muteEnabled,
    TResult? Function(_MuteDisabled value)? muteDisabled,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
    TResult? Function(_LocalStreamUpdated value)? localStreamUpdated,
    TResult? Function(_RemoteStreamUpdated value)? remoteStreamUpdated,
    TResult? Function(_AudioReset value)? audioReset,
  }) {
    return audioRoutingChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SpeakerToggled value)? speakerToggled,
    TResult Function(_SpeakerEnabled value)? speakerEnabled,
    TResult Function(_SpeakerDisabled value)? speakerDisabled,
    TResult Function(_BluetoothEnabled value)? bluetoothEnabled,
    TResult Function(_WiredHeadsetEnabled value)? wiredHeadsetEnabled,
    TResult Function(_MuteToggled value)? muteToggled,
    TResult Function(_MuteEnabled value)? muteEnabled,
    TResult Function(_MuteDisabled value)? muteDisabled,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    TResult Function(_LocalStreamUpdated value)? localStreamUpdated,
    TResult Function(_RemoteStreamUpdated value)? remoteStreamUpdated,
    TResult Function(_AudioReset value)? audioReset,
    required TResult orElse(),
  }) {
    if (audioRoutingChanged != null) {
      return audioRoutingChanged(this);
    }
    return orElse();
  }
}

abstract class _AudioRoutingChanged implements AudioEvent {
  const factory _AudioRoutingChanged({required final AudioOutputMode mode}) =
      _$AudioRoutingChangedImpl;

  AudioOutputMode get mode;

  /// Create a copy of AudioEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AudioRoutingChangedImplCopyWith<_$AudioRoutingChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LocalStreamUpdatedImplCopyWith<$Res> {
  factory _$$LocalStreamUpdatedImplCopyWith(_$LocalStreamUpdatedImpl value,
          $Res Function(_$LocalStreamUpdatedImpl) then) =
      __$$LocalStreamUpdatedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({MediaStream? stream});
}

/// @nodoc
class __$$LocalStreamUpdatedImplCopyWithImpl<$Res>
    extends _$AudioEventCopyWithImpl<$Res, _$LocalStreamUpdatedImpl>
    implements _$$LocalStreamUpdatedImplCopyWith<$Res> {
  __$$LocalStreamUpdatedImplCopyWithImpl(_$LocalStreamUpdatedImpl _value,
      $Res Function(_$LocalStreamUpdatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of AudioEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? stream = freezed,
  }) {
    return _then(_$LocalStreamUpdatedImpl(
      stream: freezed == stream
          ? _value.stream
          : stream // ignore: cast_nullable_to_non_nullable
              as MediaStream?,
    ));
  }
}

/// @nodoc

class _$LocalStreamUpdatedImpl
    with DiagnosticableTreeMixin
    implements _LocalStreamUpdated {
  const _$LocalStreamUpdatedImpl({this.stream});

  @override
  final MediaStream? stream;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AudioEvent.localStreamUpdated(stream: $stream)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AudioEvent.localStreamUpdated'))
      ..add(DiagnosticsProperty('stream', stream));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocalStreamUpdatedImpl &&
            (identical(other.stream, stream) || other.stream == stream));
  }

  @override
  int get hashCode => Object.hash(runtimeType, stream);

  /// Create a copy of AudioEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LocalStreamUpdatedImplCopyWith<_$LocalStreamUpdatedImpl> get copyWith =>
      __$$LocalStreamUpdatedImplCopyWithImpl<_$LocalStreamUpdatedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() speakerToggled,
    required TResult Function() speakerEnabled,
    required TResult Function() speakerDisabled,
    required TResult Function() bluetoothEnabled,
    required TResult Function() wiredHeadsetEnabled,
    required TResult Function() muteToggled,
    required TResult Function() muteEnabled,
    required TResult Function() muteDisabled,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
    required TResult Function(MediaStream? stream) localStreamUpdated,
    required TResult Function(MediaStream? stream) remoteStreamUpdated,
    required TResult Function() audioReset,
  }) {
    return localStreamUpdated(stream);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? speakerToggled,
    TResult? Function()? speakerEnabled,
    TResult? Function()? speakerDisabled,
    TResult? Function()? bluetoothEnabled,
    TResult? Function()? wiredHeadsetEnabled,
    TResult? Function()? muteToggled,
    TResult? Function()? muteEnabled,
    TResult? Function()? muteDisabled,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
    TResult? Function(MediaStream? stream)? localStreamUpdated,
    TResult? Function(MediaStream? stream)? remoteStreamUpdated,
    TResult? Function()? audioReset,
  }) {
    return localStreamUpdated?.call(stream);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? speakerToggled,
    TResult Function()? speakerEnabled,
    TResult Function()? speakerDisabled,
    TResult Function()? bluetoothEnabled,
    TResult Function()? wiredHeadsetEnabled,
    TResult Function()? muteToggled,
    TResult Function()? muteEnabled,
    TResult Function()? muteDisabled,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    TResult Function(MediaStream? stream)? localStreamUpdated,
    TResult Function(MediaStream? stream)? remoteStreamUpdated,
    TResult Function()? audioReset,
    required TResult orElse(),
  }) {
    if (localStreamUpdated != null) {
      return localStreamUpdated(stream);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SpeakerToggled value) speakerToggled,
    required TResult Function(_SpeakerEnabled value) speakerEnabled,
    required TResult Function(_SpeakerDisabled value) speakerDisabled,
    required TResult Function(_BluetoothEnabled value) bluetoothEnabled,
    required TResult Function(_WiredHeadsetEnabled value) wiredHeadsetEnabled,
    required TResult Function(_MuteToggled value) muteToggled,
    required TResult Function(_MuteEnabled value) muteEnabled,
    required TResult Function(_MuteDisabled value) muteDisabled,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
    required TResult Function(_LocalStreamUpdated value) localStreamUpdated,
    required TResult Function(_RemoteStreamUpdated value) remoteStreamUpdated,
    required TResult Function(_AudioReset value) audioReset,
  }) {
    return localStreamUpdated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SpeakerToggled value)? speakerToggled,
    TResult? Function(_SpeakerEnabled value)? speakerEnabled,
    TResult? Function(_SpeakerDisabled value)? speakerDisabled,
    TResult? Function(_BluetoothEnabled value)? bluetoothEnabled,
    TResult? Function(_WiredHeadsetEnabled value)? wiredHeadsetEnabled,
    TResult? Function(_MuteToggled value)? muteToggled,
    TResult? Function(_MuteEnabled value)? muteEnabled,
    TResult? Function(_MuteDisabled value)? muteDisabled,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
    TResult? Function(_LocalStreamUpdated value)? localStreamUpdated,
    TResult? Function(_RemoteStreamUpdated value)? remoteStreamUpdated,
    TResult? Function(_AudioReset value)? audioReset,
  }) {
    return localStreamUpdated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SpeakerToggled value)? speakerToggled,
    TResult Function(_SpeakerEnabled value)? speakerEnabled,
    TResult Function(_SpeakerDisabled value)? speakerDisabled,
    TResult Function(_BluetoothEnabled value)? bluetoothEnabled,
    TResult Function(_WiredHeadsetEnabled value)? wiredHeadsetEnabled,
    TResult Function(_MuteToggled value)? muteToggled,
    TResult Function(_MuteEnabled value)? muteEnabled,
    TResult Function(_MuteDisabled value)? muteDisabled,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    TResult Function(_LocalStreamUpdated value)? localStreamUpdated,
    TResult Function(_RemoteStreamUpdated value)? remoteStreamUpdated,
    TResult Function(_AudioReset value)? audioReset,
    required TResult orElse(),
  }) {
    if (localStreamUpdated != null) {
      return localStreamUpdated(this);
    }
    return orElse();
  }
}

abstract class _LocalStreamUpdated implements AudioEvent {
  const factory _LocalStreamUpdated({final MediaStream? stream}) =
      _$LocalStreamUpdatedImpl;

  MediaStream? get stream;

  /// Create a copy of AudioEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LocalStreamUpdatedImplCopyWith<_$LocalStreamUpdatedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RemoteStreamUpdatedImplCopyWith<$Res> {
  factory _$$RemoteStreamUpdatedImplCopyWith(_$RemoteStreamUpdatedImpl value,
          $Res Function(_$RemoteStreamUpdatedImpl) then) =
      __$$RemoteStreamUpdatedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({MediaStream? stream});
}

/// @nodoc
class __$$RemoteStreamUpdatedImplCopyWithImpl<$Res>
    extends _$AudioEventCopyWithImpl<$Res, _$RemoteStreamUpdatedImpl>
    implements _$$RemoteStreamUpdatedImplCopyWith<$Res> {
  __$$RemoteStreamUpdatedImplCopyWithImpl(_$RemoteStreamUpdatedImpl _value,
      $Res Function(_$RemoteStreamUpdatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of AudioEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? stream = freezed,
  }) {
    return _then(_$RemoteStreamUpdatedImpl(
      stream: freezed == stream
          ? _value.stream
          : stream // ignore: cast_nullable_to_non_nullable
              as MediaStream?,
    ));
  }
}

/// @nodoc

class _$RemoteStreamUpdatedImpl
    with DiagnosticableTreeMixin
    implements _RemoteStreamUpdated {
  const _$RemoteStreamUpdatedImpl({this.stream});

  @override
  final MediaStream? stream;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AudioEvent.remoteStreamUpdated(stream: $stream)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AudioEvent.remoteStreamUpdated'))
      ..add(DiagnosticsProperty('stream', stream));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RemoteStreamUpdatedImpl &&
            (identical(other.stream, stream) || other.stream == stream));
  }

  @override
  int get hashCode => Object.hash(runtimeType, stream);

  /// Create a copy of AudioEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RemoteStreamUpdatedImplCopyWith<_$RemoteStreamUpdatedImpl> get copyWith =>
      __$$RemoteStreamUpdatedImplCopyWithImpl<_$RemoteStreamUpdatedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() speakerToggled,
    required TResult Function() speakerEnabled,
    required TResult Function() speakerDisabled,
    required TResult Function() bluetoothEnabled,
    required TResult Function() wiredHeadsetEnabled,
    required TResult Function() muteToggled,
    required TResult Function() muteEnabled,
    required TResult Function() muteDisabled,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
    required TResult Function(MediaStream? stream) localStreamUpdated,
    required TResult Function(MediaStream? stream) remoteStreamUpdated,
    required TResult Function() audioReset,
  }) {
    return remoteStreamUpdated(stream);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? speakerToggled,
    TResult? Function()? speakerEnabled,
    TResult? Function()? speakerDisabled,
    TResult? Function()? bluetoothEnabled,
    TResult? Function()? wiredHeadsetEnabled,
    TResult? Function()? muteToggled,
    TResult? Function()? muteEnabled,
    TResult? Function()? muteDisabled,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
    TResult? Function(MediaStream? stream)? localStreamUpdated,
    TResult? Function(MediaStream? stream)? remoteStreamUpdated,
    TResult? Function()? audioReset,
  }) {
    return remoteStreamUpdated?.call(stream);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? speakerToggled,
    TResult Function()? speakerEnabled,
    TResult Function()? speakerDisabled,
    TResult Function()? bluetoothEnabled,
    TResult Function()? wiredHeadsetEnabled,
    TResult Function()? muteToggled,
    TResult Function()? muteEnabled,
    TResult Function()? muteDisabled,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    TResult Function(MediaStream? stream)? localStreamUpdated,
    TResult Function(MediaStream? stream)? remoteStreamUpdated,
    TResult Function()? audioReset,
    required TResult orElse(),
  }) {
    if (remoteStreamUpdated != null) {
      return remoteStreamUpdated(stream);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SpeakerToggled value) speakerToggled,
    required TResult Function(_SpeakerEnabled value) speakerEnabled,
    required TResult Function(_SpeakerDisabled value) speakerDisabled,
    required TResult Function(_BluetoothEnabled value) bluetoothEnabled,
    required TResult Function(_WiredHeadsetEnabled value) wiredHeadsetEnabled,
    required TResult Function(_MuteToggled value) muteToggled,
    required TResult Function(_MuteEnabled value) muteEnabled,
    required TResult Function(_MuteDisabled value) muteDisabled,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
    required TResult Function(_LocalStreamUpdated value) localStreamUpdated,
    required TResult Function(_RemoteStreamUpdated value) remoteStreamUpdated,
    required TResult Function(_AudioReset value) audioReset,
  }) {
    return remoteStreamUpdated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SpeakerToggled value)? speakerToggled,
    TResult? Function(_SpeakerEnabled value)? speakerEnabled,
    TResult? Function(_SpeakerDisabled value)? speakerDisabled,
    TResult? Function(_BluetoothEnabled value)? bluetoothEnabled,
    TResult? Function(_WiredHeadsetEnabled value)? wiredHeadsetEnabled,
    TResult? Function(_MuteToggled value)? muteToggled,
    TResult? Function(_MuteEnabled value)? muteEnabled,
    TResult? Function(_MuteDisabled value)? muteDisabled,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
    TResult? Function(_LocalStreamUpdated value)? localStreamUpdated,
    TResult? Function(_RemoteStreamUpdated value)? remoteStreamUpdated,
    TResult? Function(_AudioReset value)? audioReset,
  }) {
    return remoteStreamUpdated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SpeakerToggled value)? speakerToggled,
    TResult Function(_SpeakerEnabled value)? speakerEnabled,
    TResult Function(_SpeakerDisabled value)? speakerDisabled,
    TResult Function(_BluetoothEnabled value)? bluetoothEnabled,
    TResult Function(_WiredHeadsetEnabled value)? wiredHeadsetEnabled,
    TResult Function(_MuteToggled value)? muteToggled,
    TResult Function(_MuteEnabled value)? muteEnabled,
    TResult Function(_MuteDisabled value)? muteDisabled,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    TResult Function(_LocalStreamUpdated value)? localStreamUpdated,
    TResult Function(_RemoteStreamUpdated value)? remoteStreamUpdated,
    TResult Function(_AudioReset value)? audioReset,
    required TResult orElse(),
  }) {
    if (remoteStreamUpdated != null) {
      return remoteStreamUpdated(this);
    }
    return orElse();
  }
}

abstract class _RemoteStreamUpdated implements AudioEvent {
  const factory _RemoteStreamUpdated({final MediaStream? stream}) =
      _$RemoteStreamUpdatedImpl;

  MediaStream? get stream;

  /// Create a copy of AudioEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RemoteStreamUpdatedImplCopyWith<_$RemoteStreamUpdatedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AudioResetImplCopyWith<$Res> {
  factory _$$AudioResetImplCopyWith(
          _$AudioResetImpl value, $Res Function(_$AudioResetImpl) then) =
      __$$AudioResetImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$AudioResetImplCopyWithImpl<$Res>
    extends _$AudioEventCopyWithImpl<$Res, _$AudioResetImpl>
    implements _$$AudioResetImplCopyWith<$Res> {
  __$$AudioResetImplCopyWithImpl(
      _$AudioResetImpl _value, $Res Function(_$AudioResetImpl) _then)
      : super(_value, _then);

  /// Create a copy of AudioEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$AudioResetImpl with DiagnosticableTreeMixin implements _AudioReset {
  const _$AudioResetImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AudioEvent.audioReset()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'AudioEvent.audioReset'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$AudioResetImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() speakerToggled,
    required TResult Function() speakerEnabled,
    required TResult Function() speakerDisabled,
    required TResult Function() bluetoothEnabled,
    required TResult Function() wiredHeadsetEnabled,
    required TResult Function() muteToggled,
    required TResult Function() muteEnabled,
    required TResult Function() muteDisabled,
    required TResult Function(AudioOutputMode mode) audioRoutingChanged,
    required TResult Function(MediaStream? stream) localStreamUpdated,
    required TResult Function(MediaStream? stream) remoteStreamUpdated,
    required TResult Function() audioReset,
  }) {
    return audioReset();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? speakerToggled,
    TResult? Function()? speakerEnabled,
    TResult? Function()? speakerDisabled,
    TResult? Function()? bluetoothEnabled,
    TResult? Function()? wiredHeadsetEnabled,
    TResult? Function()? muteToggled,
    TResult? Function()? muteEnabled,
    TResult? Function()? muteDisabled,
    TResult? Function(AudioOutputMode mode)? audioRoutingChanged,
    TResult? Function(MediaStream? stream)? localStreamUpdated,
    TResult? Function(MediaStream? stream)? remoteStreamUpdated,
    TResult? Function()? audioReset,
  }) {
    return audioReset?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? speakerToggled,
    TResult Function()? speakerEnabled,
    TResult Function()? speakerDisabled,
    TResult Function()? bluetoothEnabled,
    TResult Function()? wiredHeadsetEnabled,
    TResult Function()? muteToggled,
    TResult Function()? muteEnabled,
    TResult Function()? muteDisabled,
    TResult Function(AudioOutputMode mode)? audioRoutingChanged,
    TResult Function(MediaStream? stream)? localStreamUpdated,
    TResult Function(MediaStream? stream)? remoteStreamUpdated,
    TResult Function()? audioReset,
    required TResult orElse(),
  }) {
    if (audioReset != null) {
      return audioReset();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SpeakerToggled value) speakerToggled,
    required TResult Function(_SpeakerEnabled value) speakerEnabled,
    required TResult Function(_SpeakerDisabled value) speakerDisabled,
    required TResult Function(_BluetoothEnabled value) bluetoothEnabled,
    required TResult Function(_WiredHeadsetEnabled value) wiredHeadsetEnabled,
    required TResult Function(_MuteToggled value) muteToggled,
    required TResult Function(_MuteEnabled value) muteEnabled,
    required TResult Function(_MuteDisabled value) muteDisabled,
    required TResult Function(_AudioRoutingChanged value) audioRoutingChanged,
    required TResult Function(_LocalStreamUpdated value) localStreamUpdated,
    required TResult Function(_RemoteStreamUpdated value) remoteStreamUpdated,
    required TResult Function(_AudioReset value) audioReset,
  }) {
    return audioReset(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SpeakerToggled value)? speakerToggled,
    TResult? Function(_SpeakerEnabled value)? speakerEnabled,
    TResult? Function(_SpeakerDisabled value)? speakerDisabled,
    TResult? Function(_BluetoothEnabled value)? bluetoothEnabled,
    TResult? Function(_WiredHeadsetEnabled value)? wiredHeadsetEnabled,
    TResult? Function(_MuteToggled value)? muteToggled,
    TResult? Function(_MuteEnabled value)? muteEnabled,
    TResult? Function(_MuteDisabled value)? muteDisabled,
    TResult? Function(_AudioRoutingChanged value)? audioRoutingChanged,
    TResult? Function(_LocalStreamUpdated value)? localStreamUpdated,
    TResult? Function(_RemoteStreamUpdated value)? remoteStreamUpdated,
    TResult? Function(_AudioReset value)? audioReset,
  }) {
    return audioReset?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SpeakerToggled value)? speakerToggled,
    TResult Function(_SpeakerEnabled value)? speakerEnabled,
    TResult Function(_SpeakerDisabled value)? speakerDisabled,
    TResult Function(_BluetoothEnabled value)? bluetoothEnabled,
    TResult Function(_WiredHeadsetEnabled value)? wiredHeadsetEnabled,
    TResult Function(_MuteToggled value)? muteToggled,
    TResult Function(_MuteEnabled value)? muteEnabled,
    TResult Function(_MuteDisabled value)? muteDisabled,
    TResult Function(_AudioRoutingChanged value)? audioRoutingChanged,
    TResult Function(_LocalStreamUpdated value)? localStreamUpdated,
    TResult Function(_RemoteStreamUpdated value)? remoteStreamUpdated,
    TResult Function(_AudioReset value)? audioReset,
    required TResult orElse(),
  }) {
    if (audioReset != null) {
      return audioReset(this);
    }
    return orElse();
  }
}

abstract class _AudioReset implements AudioEvent {
  const factory _AudioReset() = _$AudioResetImpl;
}

/// @nodoc
mixin _$AudioState {
  /// Current audio track mode (speaker, phone, bluetooth, wired headset)
  CallAudioTracksMode get audioTrackMode => throw _privateConstructorUsedError;

  /// Current mute status of the microphone
  DialerMuteStatus get muteStatus => throw _privateConstructorUsedError;

  /// User-friendly name of the current audio output device
  String get audioOutput => throw _privateConstructorUsedError;

  /// Local media stream from the user's microphone/camera
  MediaStream? get localMediaStream => throw _privateConstructorUsedError;

  /// Remote media stream from the other party
  MediaStream? get remoteMediaStream => throw _privateConstructorUsedError;

  /// Last error message if any audio operation failed
  String? get lastError => throw _privateConstructorUsedError;

  /// Create a copy of AudioState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AudioStateCopyWith<AudioState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AudioStateCopyWith<$Res> {
  factory $AudioStateCopyWith(
          AudioState value, $Res Function(AudioState) then) =
      _$AudioStateCopyWithImpl<$Res, AudioState>;
  @useResult
  $Res call(
      {CallAudioTracksMode audioTrackMode,
      DialerMuteStatus muteStatus,
      String audioOutput,
      MediaStream? localMediaStream,
      MediaStream? remoteMediaStream,
      String? lastError});
}

/// @nodoc
class _$AudioStateCopyWithImpl<$Res, $Val extends AudioState>
    implements $AudioStateCopyWith<$Res> {
  _$AudioStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AudioState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? audioTrackMode = null,
    Object? muteStatus = null,
    Object? audioOutput = null,
    Object? localMediaStream = freezed,
    Object? remoteMediaStream = freezed,
    Object? lastError = freezed,
  }) {
    return _then(_value.copyWith(
      audioTrackMode: null == audioTrackMode
          ? _value.audioTrackMode
          : audioTrackMode // ignore: cast_nullable_to_non_nullable
              as CallAudioTracksMode,
      muteStatus: null == muteStatus
          ? _value.muteStatus
          : muteStatus // ignore: cast_nullable_to_non_nullable
              as DialerMuteStatus,
      audioOutput: null == audioOutput
          ? _value.audioOutput
          : audioOutput // ignore: cast_nullable_to_non_nullable
              as String,
      localMediaStream: freezed == localMediaStream
          ? _value.localMediaStream
          : localMediaStream // ignore: cast_nullable_to_non_nullable
              as MediaStream?,
      remoteMediaStream: freezed == remoteMediaStream
          ? _value.remoteMediaStream
          : remoteMediaStream // ignore: cast_nullable_to_non_nullable
              as MediaStream?,
      lastError: freezed == lastError
          ? _value.lastError
          : lastError // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AudioStateImplCopyWith<$Res>
    implements $AudioStateCopyWith<$Res> {
  factory _$$AudioStateImplCopyWith(
          _$AudioStateImpl value, $Res Function(_$AudioStateImpl) then) =
      __$$AudioStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {CallAudioTracksMode audioTrackMode,
      DialerMuteStatus muteStatus,
      String audioOutput,
      MediaStream? localMediaStream,
      MediaStream? remoteMediaStream,
      String? lastError});
}

/// @nodoc
class __$$AudioStateImplCopyWithImpl<$Res>
    extends _$AudioStateCopyWithImpl<$Res, _$AudioStateImpl>
    implements _$$AudioStateImplCopyWith<$Res> {
  __$$AudioStateImplCopyWithImpl(
      _$AudioStateImpl _value, $Res Function(_$AudioStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of AudioState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? audioTrackMode = null,
    Object? muteStatus = null,
    Object? audioOutput = null,
    Object? localMediaStream = freezed,
    Object? remoteMediaStream = freezed,
    Object? lastError = freezed,
  }) {
    return _then(_$AudioStateImpl(
      audioTrackMode: null == audioTrackMode
          ? _value.audioTrackMode
          : audioTrackMode // ignore: cast_nullable_to_non_nullable
              as CallAudioTracksMode,
      muteStatus: null == muteStatus
          ? _value.muteStatus
          : muteStatus // ignore: cast_nullable_to_non_nullable
              as DialerMuteStatus,
      audioOutput: null == audioOutput
          ? _value.audioOutput
          : audioOutput // ignore: cast_nullable_to_non_nullable
              as String,
      localMediaStream: freezed == localMediaStream
          ? _value.localMediaStream
          : localMediaStream // ignore: cast_nullable_to_non_nullable
              as MediaStream?,
      remoteMediaStream: freezed == remoteMediaStream
          ? _value.remoteMediaStream
          : remoteMediaStream // ignore: cast_nullable_to_non_nullable
              as MediaStream?,
      lastError: freezed == lastError
          ? _value.lastError
          : lastError // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$AudioStateImpl extends _AudioState with DiagnosticableTreeMixin {
  const _$AudioStateImpl(
      {this.audioTrackMode = CallAudioTracksMode.phone,
      this.muteStatus = DialerMuteStatus.unmuted,
      this.audioOutput = 'Earpiece',
      this.localMediaStream,
      this.remoteMediaStream,
      this.lastError})
      : super._();

  /// Current audio track mode (speaker, phone, bluetooth, wired headset)
  @override
  @JsonKey()
  final CallAudioTracksMode audioTrackMode;

  /// Current mute status of the microphone
  @override
  @JsonKey()
  final DialerMuteStatus muteStatus;

  /// User-friendly name of the current audio output device
  @override
  @JsonKey()
  final String audioOutput;

  /// Local media stream from the user's microphone/camera
  @override
  final MediaStream? localMediaStream;

  /// Remote media stream from the other party
  @override
  final MediaStream? remoteMediaStream;

  /// Last error message if any audio operation failed
  @override
  final String? lastError;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AudioState(audioTrackMode: $audioTrackMode, muteStatus: $muteStatus, audioOutput: $audioOutput, localMediaStream: $localMediaStream, remoteMediaStream: $remoteMediaStream, lastError: $lastError)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AudioState'))
      ..add(DiagnosticsProperty('audioTrackMode', audioTrackMode))
      ..add(DiagnosticsProperty('muteStatus', muteStatus))
      ..add(DiagnosticsProperty('audioOutput', audioOutput))
      ..add(DiagnosticsProperty('localMediaStream', localMediaStream))
      ..add(DiagnosticsProperty('remoteMediaStream', remoteMediaStream))
      ..add(DiagnosticsProperty('lastError', lastError));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AudioStateImpl &&
            (identical(other.audioTrackMode, audioTrackMode) ||
                other.audioTrackMode == audioTrackMode) &&
            (identical(other.muteStatus, muteStatus) ||
                other.muteStatus == muteStatus) &&
            (identical(other.audioOutput, audioOutput) ||
                other.audioOutput == audioOutput) &&
            (identical(other.localMediaStream, localMediaStream) ||
                other.localMediaStream == localMediaStream) &&
            (identical(other.remoteMediaStream, remoteMediaStream) ||
                other.remoteMediaStream == remoteMediaStream) &&
            (identical(other.lastError, lastError) ||
                other.lastError == lastError));
  }

  @override
  int get hashCode => Object.hash(runtimeType, audioTrackMode, muteStatus,
      audioOutput, localMediaStream, remoteMediaStream, lastError);

  /// Create a copy of AudioState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AudioStateImplCopyWith<_$AudioStateImpl> get copyWith =>
      __$$AudioStateImplCopyWithImpl<_$AudioStateImpl>(this, _$identity);
}

abstract class _AudioState extends AudioState {
  const factory _AudioState(
      {final CallAudioTracksMode audioTrackMode,
      final DialerMuteStatus muteStatus,
      final String audioOutput,
      final MediaStream? localMediaStream,
      final MediaStream? remoteMediaStream,
      final String? lastError}) = _$AudioStateImpl;
  const _AudioState._() : super._();

  /// Current audio track mode (speaker, phone, bluetooth, wired headset)
  @override
  CallAudioTracksMode get audioTrackMode;

  /// Current mute status of the microphone
  @override
  DialerMuteStatus get muteStatus;

  /// User-friendly name of the current audio output device
  @override
  String get audioOutput;

  /// Local media stream from the user's microphone/camera
  @override
  MediaStream? get localMediaStream;

  /// Remote media stream from the other party
  @override
  MediaStream? get remoteMediaStream;

  /// Last error message if any audio operation failed
  @override
  String? get lastError;

  /// Create a copy of AudioState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AudioStateImplCopyWith<_$AudioStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
