part of 'registration_bloc.dart';

/// Registration-related events for the RegistrationBloc
/// 
/// These events represent all possible registration-related actions
/// that can occur during SIP registration, including credential
/// handling, connection management, and status updates.
@freezed
class RegistrationEvent with _$RegistrationEvent {
  /// Request registration with SIP server using credentials
  const factory RegistrationEvent.registerRequested({
    required String username,
    required String password,
  }) = _RegisterRequested;

  /// Request reconnection to SIP server using stored credentials
  const factory RegistrationEvent.reconnectRequested() =
      _ReconnectRequested;

  /// Request unregistration from SIP server
  const factory RegistrationEvent.unregisterRequested() =
      _UnregisterRequested;

  /// Registration status updated from SIP layer
  const factory RegistrationEvent.registrationStatusUpdated({
    required sip_ua.RegistrationStateEnum status,
    String? cause,
  }) = _RegistrationStatusUpdated;

  /// Reset registration state to initial
  const factory RegistrationEvent.registrationReset() =
      _RegistrationReset;
}
