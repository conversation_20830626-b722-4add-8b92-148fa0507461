// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'outbound_call_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$OutboundCallEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Call call) started,
    required TResult Function() stopped,
    required TResult Function() makeCall,
    required TResult Function() endCall,
    required TResult Function() onCallStatusChanged,
    required TResult Function(bool mute) onMuteStatusChanged,
    required TResult Function(bool speaker) onSpeakerStatusChanged,
    required TResult Function(bool hold) onHoldStatusChanged,
    required TResult Function(String key) onDTMFKeyPressed,
    required TResult Function(String number) onPhoneNumberUpdated,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Call call)? started,
    TResult? Function()? stopped,
    TResult? Function()? makeCall,
    TResult? Function()? endCall,
    TResult? Function()? onCallStatusChanged,
    TResult? Function(bool mute)? onMuteStatusChanged,
    TResult? Function(bool speaker)? onSpeakerStatusChanged,
    TResult? Function(bool hold)? onHoldStatusChanged,
    TResult? Function(String key)? onDTMFKeyPressed,
    TResult? Function(String number)? onPhoneNumberUpdated,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Call call)? started,
    TResult Function()? stopped,
    TResult Function()? makeCall,
    TResult Function()? endCall,
    TResult Function()? onCallStatusChanged,
    TResult Function(bool mute)? onMuteStatusChanged,
    TResult Function(bool speaker)? onSpeakerStatusChanged,
    TResult Function(bool hold)? onHoldStatusChanged,
    TResult Function(String key)? onDTMFKeyPressed,
    TResult Function(String number)? onPhoneNumberUpdated,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Stopped value) stopped,
    required TResult Function(_MakeCall value) makeCall,
    required TResult Function(_EndCall value) endCall,
    required TResult Function(_OnCallStatusChanged value) onCallStatusChanged,
    required TResult Function(_OnMuteStatusChanged value) onMuteStatusChanged,
    required TResult Function(_OnSpeakerStatusChanged value)
        onSpeakerStatusChanged,
    required TResult Function(_OnHoldStatusChanged value) onHoldStatusChanged,
    required TResult Function(_OnDTMFKeyPressed value) onDTMFKeyPressed,
    required TResult Function(_OnPhoneNumberUpdated value) onPhoneNumberUpdated,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Stopped value)? stopped,
    TResult? Function(_MakeCall value)? makeCall,
    TResult? Function(_EndCall value)? endCall,
    TResult? Function(_OnCallStatusChanged value)? onCallStatusChanged,
    TResult? Function(_OnMuteStatusChanged value)? onMuteStatusChanged,
    TResult? Function(_OnSpeakerStatusChanged value)? onSpeakerStatusChanged,
    TResult? Function(_OnHoldStatusChanged value)? onHoldStatusChanged,
    TResult? Function(_OnDTMFKeyPressed value)? onDTMFKeyPressed,
    TResult? Function(_OnPhoneNumberUpdated value)? onPhoneNumberUpdated,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Stopped value)? stopped,
    TResult Function(_MakeCall value)? makeCall,
    TResult Function(_EndCall value)? endCall,
    TResult Function(_OnCallStatusChanged value)? onCallStatusChanged,
    TResult Function(_OnMuteStatusChanged value)? onMuteStatusChanged,
    TResult Function(_OnSpeakerStatusChanged value)? onSpeakerStatusChanged,
    TResult Function(_OnHoldStatusChanged value)? onHoldStatusChanged,
    TResult Function(_OnDTMFKeyPressed value)? onDTMFKeyPressed,
    TResult Function(_OnPhoneNumberUpdated value)? onPhoneNumberUpdated,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OutboundCallEventCopyWith<$Res> {
  factory $OutboundCallEventCopyWith(
          OutboundCallEvent value, $Res Function(OutboundCallEvent) then) =
      _$OutboundCallEventCopyWithImpl<$Res, OutboundCallEvent>;
}

/// @nodoc
class _$OutboundCallEventCopyWithImpl<$Res, $Val extends OutboundCallEvent>
    implements $OutboundCallEventCopyWith<$Res> {
  _$OutboundCallEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OutboundCallEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$StartedImplCopyWith<$Res> {
  factory _$$StartedImplCopyWith(
          _$StartedImpl value, $Res Function(_$StartedImpl) then) =
      __$$StartedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Call call});
}

/// @nodoc
class __$$StartedImplCopyWithImpl<$Res>
    extends _$OutboundCallEventCopyWithImpl<$Res, _$StartedImpl>
    implements _$$StartedImplCopyWith<$Res> {
  __$$StartedImplCopyWithImpl(
      _$StartedImpl _value, $Res Function(_$StartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of OutboundCallEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? call = null,
  }) {
    return _then(_$StartedImpl(
      null == call
          ? _value.call
          : call // ignore: cast_nullable_to_non_nullable
              as Call,
    ));
  }
}

/// @nodoc

class _$StartedImpl implements _Started {
  const _$StartedImpl(this.call);

  @override
  final Call call;

  @override
  String toString() {
    return 'OutboundCallEvent.started(call: $call)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StartedImpl &&
            (identical(other.call, call) || other.call == call));
  }

  @override
  int get hashCode => Object.hash(runtimeType, call);

  /// Create a copy of OutboundCallEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StartedImplCopyWith<_$StartedImpl> get copyWith =>
      __$$StartedImplCopyWithImpl<_$StartedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Call call) started,
    required TResult Function() stopped,
    required TResult Function() makeCall,
    required TResult Function() endCall,
    required TResult Function() onCallStatusChanged,
    required TResult Function(bool mute) onMuteStatusChanged,
    required TResult Function(bool speaker) onSpeakerStatusChanged,
    required TResult Function(bool hold) onHoldStatusChanged,
    required TResult Function(String key) onDTMFKeyPressed,
    required TResult Function(String number) onPhoneNumberUpdated,
  }) {
    return started(call);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Call call)? started,
    TResult? Function()? stopped,
    TResult? Function()? makeCall,
    TResult? Function()? endCall,
    TResult? Function()? onCallStatusChanged,
    TResult? Function(bool mute)? onMuteStatusChanged,
    TResult? Function(bool speaker)? onSpeakerStatusChanged,
    TResult? Function(bool hold)? onHoldStatusChanged,
    TResult? Function(String key)? onDTMFKeyPressed,
    TResult? Function(String number)? onPhoneNumberUpdated,
  }) {
    return started?.call(call);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Call call)? started,
    TResult Function()? stopped,
    TResult Function()? makeCall,
    TResult Function()? endCall,
    TResult Function()? onCallStatusChanged,
    TResult Function(bool mute)? onMuteStatusChanged,
    TResult Function(bool speaker)? onSpeakerStatusChanged,
    TResult Function(bool hold)? onHoldStatusChanged,
    TResult Function(String key)? onDTMFKeyPressed,
    TResult Function(String number)? onPhoneNumberUpdated,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started(call);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Stopped value) stopped,
    required TResult Function(_MakeCall value) makeCall,
    required TResult Function(_EndCall value) endCall,
    required TResult Function(_OnCallStatusChanged value) onCallStatusChanged,
    required TResult Function(_OnMuteStatusChanged value) onMuteStatusChanged,
    required TResult Function(_OnSpeakerStatusChanged value)
        onSpeakerStatusChanged,
    required TResult Function(_OnHoldStatusChanged value) onHoldStatusChanged,
    required TResult Function(_OnDTMFKeyPressed value) onDTMFKeyPressed,
    required TResult Function(_OnPhoneNumberUpdated value) onPhoneNumberUpdated,
  }) {
    return started(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Stopped value)? stopped,
    TResult? Function(_MakeCall value)? makeCall,
    TResult? Function(_EndCall value)? endCall,
    TResult? Function(_OnCallStatusChanged value)? onCallStatusChanged,
    TResult? Function(_OnMuteStatusChanged value)? onMuteStatusChanged,
    TResult? Function(_OnSpeakerStatusChanged value)? onSpeakerStatusChanged,
    TResult? Function(_OnHoldStatusChanged value)? onHoldStatusChanged,
    TResult? Function(_OnDTMFKeyPressed value)? onDTMFKeyPressed,
    TResult? Function(_OnPhoneNumberUpdated value)? onPhoneNumberUpdated,
  }) {
    return started?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Stopped value)? stopped,
    TResult Function(_MakeCall value)? makeCall,
    TResult Function(_EndCall value)? endCall,
    TResult Function(_OnCallStatusChanged value)? onCallStatusChanged,
    TResult Function(_OnMuteStatusChanged value)? onMuteStatusChanged,
    TResult Function(_OnSpeakerStatusChanged value)? onSpeakerStatusChanged,
    TResult Function(_OnHoldStatusChanged value)? onHoldStatusChanged,
    TResult Function(_OnDTMFKeyPressed value)? onDTMFKeyPressed,
    TResult Function(_OnPhoneNumberUpdated value)? onPhoneNumberUpdated,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started(this);
    }
    return orElse();
  }
}

abstract class _Started implements OutboundCallEvent {
  const factory _Started(final Call call) = _$StartedImpl;

  Call get call;

  /// Create a copy of OutboundCallEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StartedImplCopyWith<_$StartedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$StoppedImplCopyWith<$Res> {
  factory _$$StoppedImplCopyWith(
          _$StoppedImpl value, $Res Function(_$StoppedImpl) then) =
      __$$StoppedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$StoppedImplCopyWithImpl<$Res>
    extends _$OutboundCallEventCopyWithImpl<$Res, _$StoppedImpl>
    implements _$$StoppedImplCopyWith<$Res> {
  __$$StoppedImplCopyWithImpl(
      _$StoppedImpl _value, $Res Function(_$StoppedImpl) _then)
      : super(_value, _then);

  /// Create a copy of OutboundCallEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$StoppedImpl implements _Stopped {
  const _$StoppedImpl();

  @override
  String toString() {
    return 'OutboundCallEvent.stopped()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$StoppedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Call call) started,
    required TResult Function() stopped,
    required TResult Function() makeCall,
    required TResult Function() endCall,
    required TResult Function() onCallStatusChanged,
    required TResult Function(bool mute) onMuteStatusChanged,
    required TResult Function(bool speaker) onSpeakerStatusChanged,
    required TResult Function(bool hold) onHoldStatusChanged,
    required TResult Function(String key) onDTMFKeyPressed,
    required TResult Function(String number) onPhoneNumberUpdated,
  }) {
    return stopped();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Call call)? started,
    TResult? Function()? stopped,
    TResult? Function()? makeCall,
    TResult? Function()? endCall,
    TResult? Function()? onCallStatusChanged,
    TResult? Function(bool mute)? onMuteStatusChanged,
    TResult? Function(bool speaker)? onSpeakerStatusChanged,
    TResult? Function(bool hold)? onHoldStatusChanged,
    TResult? Function(String key)? onDTMFKeyPressed,
    TResult? Function(String number)? onPhoneNumberUpdated,
  }) {
    return stopped?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Call call)? started,
    TResult Function()? stopped,
    TResult Function()? makeCall,
    TResult Function()? endCall,
    TResult Function()? onCallStatusChanged,
    TResult Function(bool mute)? onMuteStatusChanged,
    TResult Function(bool speaker)? onSpeakerStatusChanged,
    TResult Function(bool hold)? onHoldStatusChanged,
    TResult Function(String key)? onDTMFKeyPressed,
    TResult Function(String number)? onPhoneNumberUpdated,
    required TResult orElse(),
  }) {
    if (stopped != null) {
      return stopped();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Stopped value) stopped,
    required TResult Function(_MakeCall value) makeCall,
    required TResult Function(_EndCall value) endCall,
    required TResult Function(_OnCallStatusChanged value) onCallStatusChanged,
    required TResult Function(_OnMuteStatusChanged value) onMuteStatusChanged,
    required TResult Function(_OnSpeakerStatusChanged value)
        onSpeakerStatusChanged,
    required TResult Function(_OnHoldStatusChanged value) onHoldStatusChanged,
    required TResult Function(_OnDTMFKeyPressed value) onDTMFKeyPressed,
    required TResult Function(_OnPhoneNumberUpdated value) onPhoneNumberUpdated,
  }) {
    return stopped(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Stopped value)? stopped,
    TResult? Function(_MakeCall value)? makeCall,
    TResult? Function(_EndCall value)? endCall,
    TResult? Function(_OnCallStatusChanged value)? onCallStatusChanged,
    TResult? Function(_OnMuteStatusChanged value)? onMuteStatusChanged,
    TResult? Function(_OnSpeakerStatusChanged value)? onSpeakerStatusChanged,
    TResult? Function(_OnHoldStatusChanged value)? onHoldStatusChanged,
    TResult? Function(_OnDTMFKeyPressed value)? onDTMFKeyPressed,
    TResult? Function(_OnPhoneNumberUpdated value)? onPhoneNumberUpdated,
  }) {
    return stopped?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Stopped value)? stopped,
    TResult Function(_MakeCall value)? makeCall,
    TResult Function(_EndCall value)? endCall,
    TResult Function(_OnCallStatusChanged value)? onCallStatusChanged,
    TResult Function(_OnMuteStatusChanged value)? onMuteStatusChanged,
    TResult Function(_OnSpeakerStatusChanged value)? onSpeakerStatusChanged,
    TResult Function(_OnHoldStatusChanged value)? onHoldStatusChanged,
    TResult Function(_OnDTMFKeyPressed value)? onDTMFKeyPressed,
    TResult Function(_OnPhoneNumberUpdated value)? onPhoneNumberUpdated,
    required TResult orElse(),
  }) {
    if (stopped != null) {
      return stopped(this);
    }
    return orElse();
  }
}

abstract class _Stopped implements OutboundCallEvent {
  const factory _Stopped() = _$StoppedImpl;
}

/// @nodoc
abstract class _$$MakeCallImplCopyWith<$Res> {
  factory _$$MakeCallImplCopyWith(
          _$MakeCallImpl value, $Res Function(_$MakeCallImpl) then) =
      __$$MakeCallImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$MakeCallImplCopyWithImpl<$Res>
    extends _$OutboundCallEventCopyWithImpl<$Res, _$MakeCallImpl>
    implements _$$MakeCallImplCopyWith<$Res> {
  __$$MakeCallImplCopyWithImpl(
      _$MakeCallImpl _value, $Res Function(_$MakeCallImpl) _then)
      : super(_value, _then);

  /// Create a copy of OutboundCallEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$MakeCallImpl implements _MakeCall {
  const _$MakeCallImpl();

  @override
  String toString() {
    return 'OutboundCallEvent.makeCall()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$MakeCallImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Call call) started,
    required TResult Function() stopped,
    required TResult Function() makeCall,
    required TResult Function() endCall,
    required TResult Function() onCallStatusChanged,
    required TResult Function(bool mute) onMuteStatusChanged,
    required TResult Function(bool speaker) onSpeakerStatusChanged,
    required TResult Function(bool hold) onHoldStatusChanged,
    required TResult Function(String key) onDTMFKeyPressed,
    required TResult Function(String number) onPhoneNumberUpdated,
  }) {
    return makeCall();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Call call)? started,
    TResult? Function()? stopped,
    TResult? Function()? makeCall,
    TResult? Function()? endCall,
    TResult? Function()? onCallStatusChanged,
    TResult? Function(bool mute)? onMuteStatusChanged,
    TResult? Function(bool speaker)? onSpeakerStatusChanged,
    TResult? Function(bool hold)? onHoldStatusChanged,
    TResult? Function(String key)? onDTMFKeyPressed,
    TResult? Function(String number)? onPhoneNumberUpdated,
  }) {
    return makeCall?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Call call)? started,
    TResult Function()? stopped,
    TResult Function()? makeCall,
    TResult Function()? endCall,
    TResult Function()? onCallStatusChanged,
    TResult Function(bool mute)? onMuteStatusChanged,
    TResult Function(bool speaker)? onSpeakerStatusChanged,
    TResult Function(bool hold)? onHoldStatusChanged,
    TResult Function(String key)? onDTMFKeyPressed,
    TResult Function(String number)? onPhoneNumberUpdated,
    required TResult orElse(),
  }) {
    if (makeCall != null) {
      return makeCall();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Stopped value) stopped,
    required TResult Function(_MakeCall value) makeCall,
    required TResult Function(_EndCall value) endCall,
    required TResult Function(_OnCallStatusChanged value) onCallStatusChanged,
    required TResult Function(_OnMuteStatusChanged value) onMuteStatusChanged,
    required TResult Function(_OnSpeakerStatusChanged value)
        onSpeakerStatusChanged,
    required TResult Function(_OnHoldStatusChanged value) onHoldStatusChanged,
    required TResult Function(_OnDTMFKeyPressed value) onDTMFKeyPressed,
    required TResult Function(_OnPhoneNumberUpdated value) onPhoneNumberUpdated,
  }) {
    return makeCall(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Stopped value)? stopped,
    TResult? Function(_MakeCall value)? makeCall,
    TResult? Function(_EndCall value)? endCall,
    TResult? Function(_OnCallStatusChanged value)? onCallStatusChanged,
    TResult? Function(_OnMuteStatusChanged value)? onMuteStatusChanged,
    TResult? Function(_OnSpeakerStatusChanged value)? onSpeakerStatusChanged,
    TResult? Function(_OnHoldStatusChanged value)? onHoldStatusChanged,
    TResult? Function(_OnDTMFKeyPressed value)? onDTMFKeyPressed,
    TResult? Function(_OnPhoneNumberUpdated value)? onPhoneNumberUpdated,
  }) {
    return makeCall?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Stopped value)? stopped,
    TResult Function(_MakeCall value)? makeCall,
    TResult Function(_EndCall value)? endCall,
    TResult Function(_OnCallStatusChanged value)? onCallStatusChanged,
    TResult Function(_OnMuteStatusChanged value)? onMuteStatusChanged,
    TResult Function(_OnSpeakerStatusChanged value)? onSpeakerStatusChanged,
    TResult Function(_OnHoldStatusChanged value)? onHoldStatusChanged,
    TResult Function(_OnDTMFKeyPressed value)? onDTMFKeyPressed,
    TResult Function(_OnPhoneNumberUpdated value)? onPhoneNumberUpdated,
    required TResult orElse(),
  }) {
    if (makeCall != null) {
      return makeCall(this);
    }
    return orElse();
  }
}

abstract class _MakeCall implements OutboundCallEvent {
  const factory _MakeCall() = _$MakeCallImpl;
}

/// @nodoc
abstract class _$$EndCallImplCopyWith<$Res> {
  factory _$$EndCallImplCopyWith(
          _$EndCallImpl value, $Res Function(_$EndCallImpl) then) =
      __$$EndCallImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$EndCallImplCopyWithImpl<$Res>
    extends _$OutboundCallEventCopyWithImpl<$Res, _$EndCallImpl>
    implements _$$EndCallImplCopyWith<$Res> {
  __$$EndCallImplCopyWithImpl(
      _$EndCallImpl _value, $Res Function(_$EndCallImpl) _then)
      : super(_value, _then);

  /// Create a copy of OutboundCallEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$EndCallImpl implements _EndCall {
  const _$EndCallImpl();

  @override
  String toString() {
    return 'OutboundCallEvent.endCall()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$EndCallImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Call call) started,
    required TResult Function() stopped,
    required TResult Function() makeCall,
    required TResult Function() endCall,
    required TResult Function() onCallStatusChanged,
    required TResult Function(bool mute) onMuteStatusChanged,
    required TResult Function(bool speaker) onSpeakerStatusChanged,
    required TResult Function(bool hold) onHoldStatusChanged,
    required TResult Function(String key) onDTMFKeyPressed,
    required TResult Function(String number) onPhoneNumberUpdated,
  }) {
    return endCall();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Call call)? started,
    TResult? Function()? stopped,
    TResult? Function()? makeCall,
    TResult? Function()? endCall,
    TResult? Function()? onCallStatusChanged,
    TResult? Function(bool mute)? onMuteStatusChanged,
    TResult? Function(bool speaker)? onSpeakerStatusChanged,
    TResult? Function(bool hold)? onHoldStatusChanged,
    TResult? Function(String key)? onDTMFKeyPressed,
    TResult? Function(String number)? onPhoneNumberUpdated,
  }) {
    return endCall?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Call call)? started,
    TResult Function()? stopped,
    TResult Function()? makeCall,
    TResult Function()? endCall,
    TResult Function()? onCallStatusChanged,
    TResult Function(bool mute)? onMuteStatusChanged,
    TResult Function(bool speaker)? onSpeakerStatusChanged,
    TResult Function(bool hold)? onHoldStatusChanged,
    TResult Function(String key)? onDTMFKeyPressed,
    TResult Function(String number)? onPhoneNumberUpdated,
    required TResult orElse(),
  }) {
    if (endCall != null) {
      return endCall();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Stopped value) stopped,
    required TResult Function(_MakeCall value) makeCall,
    required TResult Function(_EndCall value) endCall,
    required TResult Function(_OnCallStatusChanged value) onCallStatusChanged,
    required TResult Function(_OnMuteStatusChanged value) onMuteStatusChanged,
    required TResult Function(_OnSpeakerStatusChanged value)
        onSpeakerStatusChanged,
    required TResult Function(_OnHoldStatusChanged value) onHoldStatusChanged,
    required TResult Function(_OnDTMFKeyPressed value) onDTMFKeyPressed,
    required TResult Function(_OnPhoneNumberUpdated value) onPhoneNumberUpdated,
  }) {
    return endCall(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Stopped value)? stopped,
    TResult? Function(_MakeCall value)? makeCall,
    TResult? Function(_EndCall value)? endCall,
    TResult? Function(_OnCallStatusChanged value)? onCallStatusChanged,
    TResult? Function(_OnMuteStatusChanged value)? onMuteStatusChanged,
    TResult? Function(_OnSpeakerStatusChanged value)? onSpeakerStatusChanged,
    TResult? Function(_OnHoldStatusChanged value)? onHoldStatusChanged,
    TResult? Function(_OnDTMFKeyPressed value)? onDTMFKeyPressed,
    TResult? Function(_OnPhoneNumberUpdated value)? onPhoneNumberUpdated,
  }) {
    return endCall?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Stopped value)? stopped,
    TResult Function(_MakeCall value)? makeCall,
    TResult Function(_EndCall value)? endCall,
    TResult Function(_OnCallStatusChanged value)? onCallStatusChanged,
    TResult Function(_OnMuteStatusChanged value)? onMuteStatusChanged,
    TResult Function(_OnSpeakerStatusChanged value)? onSpeakerStatusChanged,
    TResult Function(_OnHoldStatusChanged value)? onHoldStatusChanged,
    TResult Function(_OnDTMFKeyPressed value)? onDTMFKeyPressed,
    TResult Function(_OnPhoneNumberUpdated value)? onPhoneNumberUpdated,
    required TResult orElse(),
  }) {
    if (endCall != null) {
      return endCall(this);
    }
    return orElse();
  }
}

abstract class _EndCall implements OutboundCallEvent {
  const factory _EndCall() = _$EndCallImpl;
}

/// @nodoc
abstract class _$$OnCallStatusChangedImplCopyWith<$Res> {
  factory _$$OnCallStatusChangedImplCopyWith(_$OnCallStatusChangedImpl value,
          $Res Function(_$OnCallStatusChangedImpl) then) =
      __$$OnCallStatusChangedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$OnCallStatusChangedImplCopyWithImpl<$Res>
    extends _$OutboundCallEventCopyWithImpl<$Res, _$OnCallStatusChangedImpl>
    implements _$$OnCallStatusChangedImplCopyWith<$Res> {
  __$$OnCallStatusChangedImplCopyWithImpl(_$OnCallStatusChangedImpl _value,
      $Res Function(_$OnCallStatusChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of OutboundCallEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$OnCallStatusChangedImpl implements _OnCallStatusChanged {
  const _$OnCallStatusChangedImpl();

  @override
  String toString() {
    return 'OutboundCallEvent.onCallStatusChanged()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OnCallStatusChangedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Call call) started,
    required TResult Function() stopped,
    required TResult Function() makeCall,
    required TResult Function() endCall,
    required TResult Function() onCallStatusChanged,
    required TResult Function(bool mute) onMuteStatusChanged,
    required TResult Function(bool speaker) onSpeakerStatusChanged,
    required TResult Function(bool hold) onHoldStatusChanged,
    required TResult Function(String key) onDTMFKeyPressed,
    required TResult Function(String number) onPhoneNumberUpdated,
  }) {
    return onCallStatusChanged();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Call call)? started,
    TResult? Function()? stopped,
    TResult? Function()? makeCall,
    TResult? Function()? endCall,
    TResult? Function()? onCallStatusChanged,
    TResult? Function(bool mute)? onMuteStatusChanged,
    TResult? Function(bool speaker)? onSpeakerStatusChanged,
    TResult? Function(bool hold)? onHoldStatusChanged,
    TResult? Function(String key)? onDTMFKeyPressed,
    TResult? Function(String number)? onPhoneNumberUpdated,
  }) {
    return onCallStatusChanged?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Call call)? started,
    TResult Function()? stopped,
    TResult Function()? makeCall,
    TResult Function()? endCall,
    TResult Function()? onCallStatusChanged,
    TResult Function(bool mute)? onMuteStatusChanged,
    TResult Function(bool speaker)? onSpeakerStatusChanged,
    TResult Function(bool hold)? onHoldStatusChanged,
    TResult Function(String key)? onDTMFKeyPressed,
    TResult Function(String number)? onPhoneNumberUpdated,
    required TResult orElse(),
  }) {
    if (onCallStatusChanged != null) {
      return onCallStatusChanged();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Stopped value) stopped,
    required TResult Function(_MakeCall value) makeCall,
    required TResult Function(_EndCall value) endCall,
    required TResult Function(_OnCallStatusChanged value) onCallStatusChanged,
    required TResult Function(_OnMuteStatusChanged value) onMuteStatusChanged,
    required TResult Function(_OnSpeakerStatusChanged value)
        onSpeakerStatusChanged,
    required TResult Function(_OnHoldStatusChanged value) onHoldStatusChanged,
    required TResult Function(_OnDTMFKeyPressed value) onDTMFKeyPressed,
    required TResult Function(_OnPhoneNumberUpdated value) onPhoneNumberUpdated,
  }) {
    return onCallStatusChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Stopped value)? stopped,
    TResult? Function(_MakeCall value)? makeCall,
    TResult? Function(_EndCall value)? endCall,
    TResult? Function(_OnCallStatusChanged value)? onCallStatusChanged,
    TResult? Function(_OnMuteStatusChanged value)? onMuteStatusChanged,
    TResult? Function(_OnSpeakerStatusChanged value)? onSpeakerStatusChanged,
    TResult? Function(_OnHoldStatusChanged value)? onHoldStatusChanged,
    TResult? Function(_OnDTMFKeyPressed value)? onDTMFKeyPressed,
    TResult? Function(_OnPhoneNumberUpdated value)? onPhoneNumberUpdated,
  }) {
    return onCallStatusChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Stopped value)? stopped,
    TResult Function(_MakeCall value)? makeCall,
    TResult Function(_EndCall value)? endCall,
    TResult Function(_OnCallStatusChanged value)? onCallStatusChanged,
    TResult Function(_OnMuteStatusChanged value)? onMuteStatusChanged,
    TResult Function(_OnSpeakerStatusChanged value)? onSpeakerStatusChanged,
    TResult Function(_OnHoldStatusChanged value)? onHoldStatusChanged,
    TResult Function(_OnDTMFKeyPressed value)? onDTMFKeyPressed,
    TResult Function(_OnPhoneNumberUpdated value)? onPhoneNumberUpdated,
    required TResult orElse(),
  }) {
    if (onCallStatusChanged != null) {
      return onCallStatusChanged(this);
    }
    return orElse();
  }
}

abstract class _OnCallStatusChanged implements OutboundCallEvent {
  const factory _OnCallStatusChanged() = _$OnCallStatusChangedImpl;
}

/// @nodoc
abstract class _$$OnMuteStatusChangedImplCopyWith<$Res> {
  factory _$$OnMuteStatusChangedImplCopyWith(_$OnMuteStatusChangedImpl value,
          $Res Function(_$OnMuteStatusChangedImpl) then) =
      __$$OnMuteStatusChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool mute});
}

/// @nodoc
class __$$OnMuteStatusChangedImplCopyWithImpl<$Res>
    extends _$OutboundCallEventCopyWithImpl<$Res, _$OnMuteStatusChangedImpl>
    implements _$$OnMuteStatusChangedImplCopyWith<$Res> {
  __$$OnMuteStatusChangedImplCopyWithImpl(_$OnMuteStatusChangedImpl _value,
      $Res Function(_$OnMuteStatusChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of OutboundCallEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mute = null,
  }) {
    return _then(_$OnMuteStatusChangedImpl(
      mute: null == mute
          ? _value.mute
          : mute // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$OnMuteStatusChangedImpl implements _OnMuteStatusChanged {
  const _$OnMuteStatusChangedImpl({this.mute = false});

  @override
  @JsonKey()
  final bool mute;

  @override
  String toString() {
    return 'OutboundCallEvent.onMuteStatusChanged(mute: $mute)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OnMuteStatusChangedImpl &&
            (identical(other.mute, mute) || other.mute == mute));
  }

  @override
  int get hashCode => Object.hash(runtimeType, mute);

  /// Create a copy of OutboundCallEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OnMuteStatusChangedImplCopyWith<_$OnMuteStatusChangedImpl> get copyWith =>
      __$$OnMuteStatusChangedImplCopyWithImpl<_$OnMuteStatusChangedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Call call) started,
    required TResult Function() stopped,
    required TResult Function() makeCall,
    required TResult Function() endCall,
    required TResult Function() onCallStatusChanged,
    required TResult Function(bool mute) onMuteStatusChanged,
    required TResult Function(bool speaker) onSpeakerStatusChanged,
    required TResult Function(bool hold) onHoldStatusChanged,
    required TResult Function(String key) onDTMFKeyPressed,
    required TResult Function(String number) onPhoneNumberUpdated,
  }) {
    return onMuteStatusChanged(mute);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Call call)? started,
    TResult? Function()? stopped,
    TResult? Function()? makeCall,
    TResult? Function()? endCall,
    TResult? Function()? onCallStatusChanged,
    TResult? Function(bool mute)? onMuteStatusChanged,
    TResult? Function(bool speaker)? onSpeakerStatusChanged,
    TResult? Function(bool hold)? onHoldStatusChanged,
    TResult? Function(String key)? onDTMFKeyPressed,
    TResult? Function(String number)? onPhoneNumberUpdated,
  }) {
    return onMuteStatusChanged?.call(mute);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Call call)? started,
    TResult Function()? stopped,
    TResult Function()? makeCall,
    TResult Function()? endCall,
    TResult Function()? onCallStatusChanged,
    TResult Function(bool mute)? onMuteStatusChanged,
    TResult Function(bool speaker)? onSpeakerStatusChanged,
    TResult Function(bool hold)? onHoldStatusChanged,
    TResult Function(String key)? onDTMFKeyPressed,
    TResult Function(String number)? onPhoneNumberUpdated,
    required TResult orElse(),
  }) {
    if (onMuteStatusChanged != null) {
      return onMuteStatusChanged(mute);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Stopped value) stopped,
    required TResult Function(_MakeCall value) makeCall,
    required TResult Function(_EndCall value) endCall,
    required TResult Function(_OnCallStatusChanged value) onCallStatusChanged,
    required TResult Function(_OnMuteStatusChanged value) onMuteStatusChanged,
    required TResult Function(_OnSpeakerStatusChanged value)
        onSpeakerStatusChanged,
    required TResult Function(_OnHoldStatusChanged value) onHoldStatusChanged,
    required TResult Function(_OnDTMFKeyPressed value) onDTMFKeyPressed,
    required TResult Function(_OnPhoneNumberUpdated value) onPhoneNumberUpdated,
  }) {
    return onMuteStatusChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Stopped value)? stopped,
    TResult? Function(_MakeCall value)? makeCall,
    TResult? Function(_EndCall value)? endCall,
    TResult? Function(_OnCallStatusChanged value)? onCallStatusChanged,
    TResult? Function(_OnMuteStatusChanged value)? onMuteStatusChanged,
    TResult? Function(_OnSpeakerStatusChanged value)? onSpeakerStatusChanged,
    TResult? Function(_OnHoldStatusChanged value)? onHoldStatusChanged,
    TResult? Function(_OnDTMFKeyPressed value)? onDTMFKeyPressed,
    TResult? Function(_OnPhoneNumberUpdated value)? onPhoneNumberUpdated,
  }) {
    return onMuteStatusChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Stopped value)? stopped,
    TResult Function(_MakeCall value)? makeCall,
    TResult Function(_EndCall value)? endCall,
    TResult Function(_OnCallStatusChanged value)? onCallStatusChanged,
    TResult Function(_OnMuteStatusChanged value)? onMuteStatusChanged,
    TResult Function(_OnSpeakerStatusChanged value)? onSpeakerStatusChanged,
    TResult Function(_OnHoldStatusChanged value)? onHoldStatusChanged,
    TResult Function(_OnDTMFKeyPressed value)? onDTMFKeyPressed,
    TResult Function(_OnPhoneNumberUpdated value)? onPhoneNumberUpdated,
    required TResult orElse(),
  }) {
    if (onMuteStatusChanged != null) {
      return onMuteStatusChanged(this);
    }
    return orElse();
  }
}

abstract class _OnMuteStatusChanged implements OutboundCallEvent {
  const factory _OnMuteStatusChanged({final bool mute}) =
      _$OnMuteStatusChangedImpl;

  bool get mute;

  /// Create a copy of OutboundCallEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OnMuteStatusChangedImplCopyWith<_$OnMuteStatusChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$OnSpeakerStatusChangedImplCopyWith<$Res> {
  factory _$$OnSpeakerStatusChangedImplCopyWith(
          _$OnSpeakerStatusChangedImpl value,
          $Res Function(_$OnSpeakerStatusChangedImpl) then) =
      __$$OnSpeakerStatusChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool speaker});
}

/// @nodoc
class __$$OnSpeakerStatusChangedImplCopyWithImpl<$Res>
    extends _$OutboundCallEventCopyWithImpl<$Res, _$OnSpeakerStatusChangedImpl>
    implements _$$OnSpeakerStatusChangedImplCopyWith<$Res> {
  __$$OnSpeakerStatusChangedImplCopyWithImpl(
      _$OnSpeakerStatusChangedImpl _value,
      $Res Function(_$OnSpeakerStatusChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of OutboundCallEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? speaker = null,
  }) {
    return _then(_$OnSpeakerStatusChangedImpl(
      speaker: null == speaker
          ? _value.speaker
          : speaker // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$OnSpeakerStatusChangedImpl implements _OnSpeakerStatusChanged {
  const _$OnSpeakerStatusChangedImpl({this.speaker = false});

  @override
  @JsonKey()
  final bool speaker;

  @override
  String toString() {
    return 'OutboundCallEvent.onSpeakerStatusChanged(speaker: $speaker)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OnSpeakerStatusChangedImpl &&
            (identical(other.speaker, speaker) || other.speaker == speaker));
  }

  @override
  int get hashCode => Object.hash(runtimeType, speaker);

  /// Create a copy of OutboundCallEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OnSpeakerStatusChangedImplCopyWith<_$OnSpeakerStatusChangedImpl>
      get copyWith => __$$OnSpeakerStatusChangedImplCopyWithImpl<
          _$OnSpeakerStatusChangedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Call call) started,
    required TResult Function() stopped,
    required TResult Function() makeCall,
    required TResult Function() endCall,
    required TResult Function() onCallStatusChanged,
    required TResult Function(bool mute) onMuteStatusChanged,
    required TResult Function(bool speaker) onSpeakerStatusChanged,
    required TResult Function(bool hold) onHoldStatusChanged,
    required TResult Function(String key) onDTMFKeyPressed,
    required TResult Function(String number) onPhoneNumberUpdated,
  }) {
    return onSpeakerStatusChanged(speaker);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Call call)? started,
    TResult? Function()? stopped,
    TResult? Function()? makeCall,
    TResult? Function()? endCall,
    TResult? Function()? onCallStatusChanged,
    TResult? Function(bool mute)? onMuteStatusChanged,
    TResult? Function(bool speaker)? onSpeakerStatusChanged,
    TResult? Function(bool hold)? onHoldStatusChanged,
    TResult? Function(String key)? onDTMFKeyPressed,
    TResult? Function(String number)? onPhoneNumberUpdated,
  }) {
    return onSpeakerStatusChanged?.call(speaker);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Call call)? started,
    TResult Function()? stopped,
    TResult Function()? makeCall,
    TResult Function()? endCall,
    TResult Function()? onCallStatusChanged,
    TResult Function(bool mute)? onMuteStatusChanged,
    TResult Function(bool speaker)? onSpeakerStatusChanged,
    TResult Function(bool hold)? onHoldStatusChanged,
    TResult Function(String key)? onDTMFKeyPressed,
    TResult Function(String number)? onPhoneNumberUpdated,
    required TResult orElse(),
  }) {
    if (onSpeakerStatusChanged != null) {
      return onSpeakerStatusChanged(speaker);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Stopped value) stopped,
    required TResult Function(_MakeCall value) makeCall,
    required TResult Function(_EndCall value) endCall,
    required TResult Function(_OnCallStatusChanged value) onCallStatusChanged,
    required TResult Function(_OnMuteStatusChanged value) onMuteStatusChanged,
    required TResult Function(_OnSpeakerStatusChanged value)
        onSpeakerStatusChanged,
    required TResult Function(_OnHoldStatusChanged value) onHoldStatusChanged,
    required TResult Function(_OnDTMFKeyPressed value) onDTMFKeyPressed,
    required TResult Function(_OnPhoneNumberUpdated value) onPhoneNumberUpdated,
  }) {
    return onSpeakerStatusChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Stopped value)? stopped,
    TResult? Function(_MakeCall value)? makeCall,
    TResult? Function(_EndCall value)? endCall,
    TResult? Function(_OnCallStatusChanged value)? onCallStatusChanged,
    TResult? Function(_OnMuteStatusChanged value)? onMuteStatusChanged,
    TResult? Function(_OnSpeakerStatusChanged value)? onSpeakerStatusChanged,
    TResult? Function(_OnHoldStatusChanged value)? onHoldStatusChanged,
    TResult? Function(_OnDTMFKeyPressed value)? onDTMFKeyPressed,
    TResult? Function(_OnPhoneNumberUpdated value)? onPhoneNumberUpdated,
  }) {
    return onSpeakerStatusChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Stopped value)? stopped,
    TResult Function(_MakeCall value)? makeCall,
    TResult Function(_EndCall value)? endCall,
    TResult Function(_OnCallStatusChanged value)? onCallStatusChanged,
    TResult Function(_OnMuteStatusChanged value)? onMuteStatusChanged,
    TResult Function(_OnSpeakerStatusChanged value)? onSpeakerStatusChanged,
    TResult Function(_OnHoldStatusChanged value)? onHoldStatusChanged,
    TResult Function(_OnDTMFKeyPressed value)? onDTMFKeyPressed,
    TResult Function(_OnPhoneNumberUpdated value)? onPhoneNumberUpdated,
    required TResult orElse(),
  }) {
    if (onSpeakerStatusChanged != null) {
      return onSpeakerStatusChanged(this);
    }
    return orElse();
  }
}

abstract class _OnSpeakerStatusChanged implements OutboundCallEvent {
  const factory _OnSpeakerStatusChanged({final bool speaker}) =
      _$OnSpeakerStatusChangedImpl;

  bool get speaker;

  /// Create a copy of OutboundCallEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OnSpeakerStatusChangedImplCopyWith<_$OnSpeakerStatusChangedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$OnHoldStatusChangedImplCopyWith<$Res> {
  factory _$$OnHoldStatusChangedImplCopyWith(_$OnHoldStatusChangedImpl value,
          $Res Function(_$OnHoldStatusChangedImpl) then) =
      __$$OnHoldStatusChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool hold});
}

/// @nodoc
class __$$OnHoldStatusChangedImplCopyWithImpl<$Res>
    extends _$OutboundCallEventCopyWithImpl<$Res, _$OnHoldStatusChangedImpl>
    implements _$$OnHoldStatusChangedImplCopyWith<$Res> {
  __$$OnHoldStatusChangedImplCopyWithImpl(_$OnHoldStatusChangedImpl _value,
      $Res Function(_$OnHoldStatusChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of OutboundCallEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? hold = null,
  }) {
    return _then(_$OnHoldStatusChangedImpl(
      hold: null == hold
          ? _value.hold
          : hold // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$OnHoldStatusChangedImpl implements _OnHoldStatusChanged {
  const _$OnHoldStatusChangedImpl({this.hold = false});

  @override
  @JsonKey()
  final bool hold;

  @override
  String toString() {
    return 'OutboundCallEvent.onHoldStatusChanged(hold: $hold)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OnHoldStatusChangedImpl &&
            (identical(other.hold, hold) || other.hold == hold));
  }

  @override
  int get hashCode => Object.hash(runtimeType, hold);

  /// Create a copy of OutboundCallEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OnHoldStatusChangedImplCopyWith<_$OnHoldStatusChangedImpl> get copyWith =>
      __$$OnHoldStatusChangedImplCopyWithImpl<_$OnHoldStatusChangedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Call call) started,
    required TResult Function() stopped,
    required TResult Function() makeCall,
    required TResult Function() endCall,
    required TResult Function() onCallStatusChanged,
    required TResult Function(bool mute) onMuteStatusChanged,
    required TResult Function(bool speaker) onSpeakerStatusChanged,
    required TResult Function(bool hold) onHoldStatusChanged,
    required TResult Function(String key) onDTMFKeyPressed,
    required TResult Function(String number) onPhoneNumberUpdated,
  }) {
    return onHoldStatusChanged(hold);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Call call)? started,
    TResult? Function()? stopped,
    TResult? Function()? makeCall,
    TResult? Function()? endCall,
    TResult? Function()? onCallStatusChanged,
    TResult? Function(bool mute)? onMuteStatusChanged,
    TResult? Function(bool speaker)? onSpeakerStatusChanged,
    TResult? Function(bool hold)? onHoldStatusChanged,
    TResult? Function(String key)? onDTMFKeyPressed,
    TResult? Function(String number)? onPhoneNumberUpdated,
  }) {
    return onHoldStatusChanged?.call(hold);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Call call)? started,
    TResult Function()? stopped,
    TResult Function()? makeCall,
    TResult Function()? endCall,
    TResult Function()? onCallStatusChanged,
    TResult Function(bool mute)? onMuteStatusChanged,
    TResult Function(bool speaker)? onSpeakerStatusChanged,
    TResult Function(bool hold)? onHoldStatusChanged,
    TResult Function(String key)? onDTMFKeyPressed,
    TResult Function(String number)? onPhoneNumberUpdated,
    required TResult orElse(),
  }) {
    if (onHoldStatusChanged != null) {
      return onHoldStatusChanged(hold);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Stopped value) stopped,
    required TResult Function(_MakeCall value) makeCall,
    required TResult Function(_EndCall value) endCall,
    required TResult Function(_OnCallStatusChanged value) onCallStatusChanged,
    required TResult Function(_OnMuteStatusChanged value) onMuteStatusChanged,
    required TResult Function(_OnSpeakerStatusChanged value)
        onSpeakerStatusChanged,
    required TResult Function(_OnHoldStatusChanged value) onHoldStatusChanged,
    required TResult Function(_OnDTMFKeyPressed value) onDTMFKeyPressed,
    required TResult Function(_OnPhoneNumberUpdated value) onPhoneNumberUpdated,
  }) {
    return onHoldStatusChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Stopped value)? stopped,
    TResult? Function(_MakeCall value)? makeCall,
    TResult? Function(_EndCall value)? endCall,
    TResult? Function(_OnCallStatusChanged value)? onCallStatusChanged,
    TResult? Function(_OnMuteStatusChanged value)? onMuteStatusChanged,
    TResult? Function(_OnSpeakerStatusChanged value)? onSpeakerStatusChanged,
    TResult? Function(_OnHoldStatusChanged value)? onHoldStatusChanged,
    TResult? Function(_OnDTMFKeyPressed value)? onDTMFKeyPressed,
    TResult? Function(_OnPhoneNumberUpdated value)? onPhoneNumberUpdated,
  }) {
    return onHoldStatusChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Stopped value)? stopped,
    TResult Function(_MakeCall value)? makeCall,
    TResult Function(_EndCall value)? endCall,
    TResult Function(_OnCallStatusChanged value)? onCallStatusChanged,
    TResult Function(_OnMuteStatusChanged value)? onMuteStatusChanged,
    TResult Function(_OnSpeakerStatusChanged value)? onSpeakerStatusChanged,
    TResult Function(_OnHoldStatusChanged value)? onHoldStatusChanged,
    TResult Function(_OnDTMFKeyPressed value)? onDTMFKeyPressed,
    TResult Function(_OnPhoneNumberUpdated value)? onPhoneNumberUpdated,
    required TResult orElse(),
  }) {
    if (onHoldStatusChanged != null) {
      return onHoldStatusChanged(this);
    }
    return orElse();
  }
}

abstract class _OnHoldStatusChanged implements OutboundCallEvent {
  const factory _OnHoldStatusChanged({final bool hold}) =
      _$OnHoldStatusChangedImpl;

  bool get hold;

  /// Create a copy of OutboundCallEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OnHoldStatusChangedImplCopyWith<_$OnHoldStatusChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$OnDTMFKeyPressedImplCopyWith<$Res> {
  factory _$$OnDTMFKeyPressedImplCopyWith(_$OnDTMFKeyPressedImpl value,
          $Res Function(_$OnDTMFKeyPressedImpl) then) =
      __$$OnDTMFKeyPressedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String key});
}

/// @nodoc
class __$$OnDTMFKeyPressedImplCopyWithImpl<$Res>
    extends _$OutboundCallEventCopyWithImpl<$Res, _$OnDTMFKeyPressedImpl>
    implements _$$OnDTMFKeyPressedImplCopyWith<$Res> {
  __$$OnDTMFKeyPressedImplCopyWithImpl(_$OnDTMFKeyPressedImpl _value,
      $Res Function(_$OnDTMFKeyPressedImpl) _then)
      : super(_value, _then);

  /// Create a copy of OutboundCallEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? key = null,
  }) {
    return _then(_$OnDTMFKeyPressedImpl(
      key: null == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$OnDTMFKeyPressedImpl implements _OnDTMFKeyPressed {
  const _$OnDTMFKeyPressedImpl({required this.key});

  @override
  final String key;

  @override
  String toString() {
    return 'OutboundCallEvent.onDTMFKeyPressed(key: $key)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OnDTMFKeyPressedImpl &&
            (identical(other.key, key) || other.key == key));
  }

  @override
  int get hashCode => Object.hash(runtimeType, key);

  /// Create a copy of OutboundCallEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OnDTMFKeyPressedImplCopyWith<_$OnDTMFKeyPressedImpl> get copyWith =>
      __$$OnDTMFKeyPressedImplCopyWithImpl<_$OnDTMFKeyPressedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Call call) started,
    required TResult Function() stopped,
    required TResult Function() makeCall,
    required TResult Function() endCall,
    required TResult Function() onCallStatusChanged,
    required TResult Function(bool mute) onMuteStatusChanged,
    required TResult Function(bool speaker) onSpeakerStatusChanged,
    required TResult Function(bool hold) onHoldStatusChanged,
    required TResult Function(String key) onDTMFKeyPressed,
    required TResult Function(String number) onPhoneNumberUpdated,
  }) {
    return onDTMFKeyPressed(key);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Call call)? started,
    TResult? Function()? stopped,
    TResult? Function()? makeCall,
    TResult? Function()? endCall,
    TResult? Function()? onCallStatusChanged,
    TResult? Function(bool mute)? onMuteStatusChanged,
    TResult? Function(bool speaker)? onSpeakerStatusChanged,
    TResult? Function(bool hold)? onHoldStatusChanged,
    TResult? Function(String key)? onDTMFKeyPressed,
    TResult? Function(String number)? onPhoneNumberUpdated,
  }) {
    return onDTMFKeyPressed?.call(key);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Call call)? started,
    TResult Function()? stopped,
    TResult Function()? makeCall,
    TResult Function()? endCall,
    TResult Function()? onCallStatusChanged,
    TResult Function(bool mute)? onMuteStatusChanged,
    TResult Function(bool speaker)? onSpeakerStatusChanged,
    TResult Function(bool hold)? onHoldStatusChanged,
    TResult Function(String key)? onDTMFKeyPressed,
    TResult Function(String number)? onPhoneNumberUpdated,
    required TResult orElse(),
  }) {
    if (onDTMFKeyPressed != null) {
      return onDTMFKeyPressed(key);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Stopped value) stopped,
    required TResult Function(_MakeCall value) makeCall,
    required TResult Function(_EndCall value) endCall,
    required TResult Function(_OnCallStatusChanged value) onCallStatusChanged,
    required TResult Function(_OnMuteStatusChanged value) onMuteStatusChanged,
    required TResult Function(_OnSpeakerStatusChanged value)
        onSpeakerStatusChanged,
    required TResult Function(_OnHoldStatusChanged value) onHoldStatusChanged,
    required TResult Function(_OnDTMFKeyPressed value) onDTMFKeyPressed,
    required TResult Function(_OnPhoneNumberUpdated value) onPhoneNumberUpdated,
  }) {
    return onDTMFKeyPressed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Stopped value)? stopped,
    TResult? Function(_MakeCall value)? makeCall,
    TResult? Function(_EndCall value)? endCall,
    TResult? Function(_OnCallStatusChanged value)? onCallStatusChanged,
    TResult? Function(_OnMuteStatusChanged value)? onMuteStatusChanged,
    TResult? Function(_OnSpeakerStatusChanged value)? onSpeakerStatusChanged,
    TResult? Function(_OnHoldStatusChanged value)? onHoldStatusChanged,
    TResult? Function(_OnDTMFKeyPressed value)? onDTMFKeyPressed,
    TResult? Function(_OnPhoneNumberUpdated value)? onPhoneNumberUpdated,
  }) {
    return onDTMFKeyPressed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Stopped value)? stopped,
    TResult Function(_MakeCall value)? makeCall,
    TResult Function(_EndCall value)? endCall,
    TResult Function(_OnCallStatusChanged value)? onCallStatusChanged,
    TResult Function(_OnMuteStatusChanged value)? onMuteStatusChanged,
    TResult Function(_OnSpeakerStatusChanged value)? onSpeakerStatusChanged,
    TResult Function(_OnHoldStatusChanged value)? onHoldStatusChanged,
    TResult Function(_OnDTMFKeyPressed value)? onDTMFKeyPressed,
    TResult Function(_OnPhoneNumberUpdated value)? onPhoneNumberUpdated,
    required TResult orElse(),
  }) {
    if (onDTMFKeyPressed != null) {
      return onDTMFKeyPressed(this);
    }
    return orElse();
  }
}

abstract class _OnDTMFKeyPressed implements OutboundCallEvent {
  const factory _OnDTMFKeyPressed({required final String key}) =
      _$OnDTMFKeyPressedImpl;

  String get key;

  /// Create a copy of OutboundCallEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OnDTMFKeyPressedImplCopyWith<_$OnDTMFKeyPressedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$OnPhoneNumberUpdatedImplCopyWith<$Res> {
  factory _$$OnPhoneNumberUpdatedImplCopyWith(_$OnPhoneNumberUpdatedImpl value,
          $Res Function(_$OnPhoneNumberUpdatedImpl) then) =
      __$$OnPhoneNumberUpdatedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String number});
}

/// @nodoc
class __$$OnPhoneNumberUpdatedImplCopyWithImpl<$Res>
    extends _$OutboundCallEventCopyWithImpl<$Res, _$OnPhoneNumberUpdatedImpl>
    implements _$$OnPhoneNumberUpdatedImplCopyWith<$Res> {
  __$$OnPhoneNumberUpdatedImplCopyWithImpl(_$OnPhoneNumberUpdatedImpl _value,
      $Res Function(_$OnPhoneNumberUpdatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of OutboundCallEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? number = null,
  }) {
    return _then(_$OnPhoneNumberUpdatedImpl(
      number: null == number
          ? _value.number
          : number // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$OnPhoneNumberUpdatedImpl implements _OnPhoneNumberUpdated {
  const _$OnPhoneNumberUpdatedImpl({required this.number});

  @override
  final String number;

  @override
  String toString() {
    return 'OutboundCallEvent.onPhoneNumberUpdated(number: $number)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OnPhoneNumberUpdatedImpl &&
            (identical(other.number, number) || other.number == number));
  }

  @override
  int get hashCode => Object.hash(runtimeType, number);

  /// Create a copy of OutboundCallEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OnPhoneNumberUpdatedImplCopyWith<_$OnPhoneNumberUpdatedImpl>
      get copyWith =>
          __$$OnPhoneNumberUpdatedImplCopyWithImpl<_$OnPhoneNumberUpdatedImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Call call) started,
    required TResult Function() stopped,
    required TResult Function() makeCall,
    required TResult Function() endCall,
    required TResult Function() onCallStatusChanged,
    required TResult Function(bool mute) onMuteStatusChanged,
    required TResult Function(bool speaker) onSpeakerStatusChanged,
    required TResult Function(bool hold) onHoldStatusChanged,
    required TResult Function(String key) onDTMFKeyPressed,
    required TResult Function(String number) onPhoneNumberUpdated,
  }) {
    return onPhoneNumberUpdated(number);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Call call)? started,
    TResult? Function()? stopped,
    TResult? Function()? makeCall,
    TResult? Function()? endCall,
    TResult? Function()? onCallStatusChanged,
    TResult? Function(bool mute)? onMuteStatusChanged,
    TResult? Function(bool speaker)? onSpeakerStatusChanged,
    TResult? Function(bool hold)? onHoldStatusChanged,
    TResult? Function(String key)? onDTMFKeyPressed,
    TResult? Function(String number)? onPhoneNumberUpdated,
  }) {
    return onPhoneNumberUpdated?.call(number);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Call call)? started,
    TResult Function()? stopped,
    TResult Function()? makeCall,
    TResult Function()? endCall,
    TResult Function()? onCallStatusChanged,
    TResult Function(bool mute)? onMuteStatusChanged,
    TResult Function(bool speaker)? onSpeakerStatusChanged,
    TResult Function(bool hold)? onHoldStatusChanged,
    TResult Function(String key)? onDTMFKeyPressed,
    TResult Function(String number)? onPhoneNumberUpdated,
    required TResult orElse(),
  }) {
    if (onPhoneNumberUpdated != null) {
      return onPhoneNumberUpdated(number);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Stopped value) stopped,
    required TResult Function(_MakeCall value) makeCall,
    required TResult Function(_EndCall value) endCall,
    required TResult Function(_OnCallStatusChanged value) onCallStatusChanged,
    required TResult Function(_OnMuteStatusChanged value) onMuteStatusChanged,
    required TResult Function(_OnSpeakerStatusChanged value)
        onSpeakerStatusChanged,
    required TResult Function(_OnHoldStatusChanged value) onHoldStatusChanged,
    required TResult Function(_OnDTMFKeyPressed value) onDTMFKeyPressed,
    required TResult Function(_OnPhoneNumberUpdated value) onPhoneNumberUpdated,
  }) {
    return onPhoneNumberUpdated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Stopped value)? stopped,
    TResult? Function(_MakeCall value)? makeCall,
    TResult? Function(_EndCall value)? endCall,
    TResult? Function(_OnCallStatusChanged value)? onCallStatusChanged,
    TResult? Function(_OnMuteStatusChanged value)? onMuteStatusChanged,
    TResult? Function(_OnSpeakerStatusChanged value)? onSpeakerStatusChanged,
    TResult? Function(_OnHoldStatusChanged value)? onHoldStatusChanged,
    TResult? Function(_OnDTMFKeyPressed value)? onDTMFKeyPressed,
    TResult? Function(_OnPhoneNumberUpdated value)? onPhoneNumberUpdated,
  }) {
    return onPhoneNumberUpdated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Stopped value)? stopped,
    TResult Function(_MakeCall value)? makeCall,
    TResult Function(_EndCall value)? endCall,
    TResult Function(_OnCallStatusChanged value)? onCallStatusChanged,
    TResult Function(_OnMuteStatusChanged value)? onMuteStatusChanged,
    TResult Function(_OnSpeakerStatusChanged value)? onSpeakerStatusChanged,
    TResult Function(_OnHoldStatusChanged value)? onHoldStatusChanged,
    TResult Function(_OnDTMFKeyPressed value)? onDTMFKeyPressed,
    TResult Function(_OnPhoneNumberUpdated value)? onPhoneNumberUpdated,
    required TResult orElse(),
  }) {
    if (onPhoneNumberUpdated != null) {
      return onPhoneNumberUpdated(this);
    }
    return orElse();
  }
}

abstract class _OnPhoneNumberUpdated implements OutboundCallEvent {
  const factory _OnPhoneNumberUpdated({required final String number}) =
      _$OnPhoneNumberUpdatedImpl;

  String get number;

  /// Create a copy of OutboundCallEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OnPhoneNumberUpdatedImplCopyWith<_$OnPhoneNumberUpdatedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$OutboundCallState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OutboundCallStateCopyWith<$Res> {
  factory $OutboundCallStateCopyWith(
          OutboundCallState value, $Res Function(OutboundCallState) then) =
      _$OutboundCallStateCopyWithImpl<$Res, OutboundCallState>;
}

/// @nodoc
class _$OutboundCallStateCopyWithImpl<$Res, $Val extends OutboundCallState>
    implements $OutboundCallStateCopyWith<$Res> {
  _$OutboundCallStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OutboundCallState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$OutboundCallStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of OutboundCallState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'OutboundCallState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements OutboundCallState {
  const factory _Initial() = _$InitialImpl;
}
