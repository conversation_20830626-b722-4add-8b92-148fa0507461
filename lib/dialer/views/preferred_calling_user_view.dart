// ignore_for_file: no_default_cases, lines_longer_than_80_chars

import 'dart:async';
import 'dart:io';

import 'package:constants/constants.dart';
import 'package:countries/countries.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_foreground_task/flutter_foreground_task.dart' as fgt;
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:froggytalk/app/data/services/event_tracker.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:froggytalk/dialer/dialer.dart';
import 'package:sip_ua/sip_ua.dart' as sip_ua;
import 'package:wakelock_plus/wakelock_plus.dart';

// Callback for the foreground service.
@pragma('vm:entry-point')
void _foregroundTaskCallback() {
  fgt.FlutterForegroundTask.setTaskHandler(ForegroundTaskHandler());
}

/// Handles all the logic for the foreground service running in a separate
/// isolate. This includes updating the notification and handling button
/// presses.
///
/// **Production Features:**
/// - Automatic timer synchronization with centralized CallTimerBloc
/// - Enhanced error handling and logging
/// - Memory leak prevention
/// - Proper communication with main isolate
/// - Platform-specific notification handling (Android/iOS)
/// - Robust service lifecycle management
class ForegroundTaskHandler extends fgt.TaskHandler {
  // PERMANENT: Disable all foreground notifications as per user request.
  static const bool kDisableForegroundNotification =
      true; // Disable notification logic

  DateTime? _callStartTime;
  String _callStatus = 'Initializing...'; // Will be updated with localized text
  String? _callerName;
  bool _isCallConnected = false;
  // Service state tracking
  bool _isServiceDestroyed = false;

  bool _isServiceInitialized = false;
  DateTime? _lastErrorTime;
  String? _lastNotificationText;
  String? _lastNotificationTitle;
  final int _maxNotificationErrors = 5;
  // Enhanced error tracking
  int _notificationUpdateErrorCount = 0;

  String? _phoneNumber;
  Timer? _timer; // Keep for other uses, but not for notification updates

  /// Called when the task is destroyed.
  @override
  Future<void> onDestroy(DateTime timestamp) async {
    if (_isServiceDestroyed) return; // Prevent multiple destruction calls
    _isServiceDestroyed = true;

    try {
      await WakelockPlus.disable();
      if (kDebugMode) {
        print('[ForegroundTaskHandler] Wakelock disabled.');
      }
    } catch (e) {
      if (kDebugMode) {
        print('[ForegroundTaskHandler] Error disabling Wakelock: $e');
      }
    }

    try {
      if (kDebugMode) {
        print(
          '[PreferredCallingUserView] ForegroundTaskHandler destroyed at: '
          '$timestamp',
        );
      }

      // Clean up timer to prevent memory leaks
      _timer?.cancel();
      _timer = null;
      _isCallConnected = false;
      _callStartTime = null;
      _callStatus = 'Call Ended'; // Fallback in service context
      _isServiceInitialized = false;

      // Show final "Call Ended" notification only on Android
      if (_notificationUpdateErrorCount < _maxNotificationErrors &&
          _shouldUpdateNotification('Call Ended', 'Call has ended')) {
        try {
          await fgt.FlutterForegroundTask.updateService(
            notificationTitle: 'Call Ended',
            notificationText: 'Call has ended',
          );
        } catch (e) {
          _handleNotificationError(e, 'final_notification_update');
        }
      }

      // Notify main isolate that service is stopping
      fgt.FlutterForegroundTask.sendDataToMain({
        'event': 'service_destroyed',
        'timestamp': timestamp.millisecondsSinceEpoch,
        'notification_errors': _notificationUpdateErrorCount,
      });
    } catch (e) {
      if (kDebugMode) {
        print(
          '[PreferredCallingUserView] Error in ForegroundTaskHandler.onDestroy: $e',
        );
      }
      // Still send destruction event even if there were errors
      try {
        fgt.FlutterForegroundTask.sendDataToMain({
          'event': 'service_destroyed_with_error',
          'timestamp': timestamp.millisecondsSinceEpoch,
          'error': e.toString(),
        });
      } catch (_) {
        // Last resort - just log
        if (kDebugMode) {
          print(
            '[PreferredCallingUserView] Failed to send destruction event to main isolate',
          );
        }
      }
    }
  }

  /// Called when a notification button is pressed.
  @override
  void onNotificationButtonPressed(String id) {
    try {
      // Update notification timer only when user interacts
      _updateNotificationWithTimer();

      switch (id) {
        case 'end_call':
          // Send message to main isolate to hang up the call
          fgt.FlutterForegroundTask.sendDataToMain({
            'event': 'hangup_requested',
            'timestamp': DateTime.now().millisecondsSinceEpoch,
          });

          // Mark call as inactive and stop service
          _isCallConnected = false;
          fgt.FlutterForegroundTask.stopService();
        case 'return_to_call':
          // Send message to bring app to foreground
          fgt.FlutterForegroundTask.sendDataToMain({
            'event': 'return_to_call_requested',
            'timestamp': DateTime.now().millisecondsSinceEpoch,
          });
      }
    } catch (e) {
      if (kDebugMode) {
        print(
          '[PreferredCallingUserView] Error in ForegroundTaskHandler.onNotificationButtonPressed: $e',
        );
      }
    }
  }

  /// Called when the notification itself is dismissed.
  @override
  void onNotificationDismissed() {
    try {
      fgt.FlutterForegroundTask.sendDataToMain({
        'event': 'notification_dismissed',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      if (kDebugMode) {
        print(
          '[PreferredCallingUserView] Error in ForegroundTaskHandler.onNotificationDismissed: $e',
        );
      }
    }
  }

  /// Called when the notification itself is pressed.
  @override
  void onNotificationPressed() {
    try {
      // Update notification timer only when user interacts
      _updateNotificationWithTimer();

      fgt.FlutterForegroundTask.sendDataToMain({
        'event': 'notification_pressed',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      if (kDebugMode) {
        print(
          '[PreferredCallingUserView] Error in ForegroundTaskHandler.onNotificationPressed: $e',
        );
      }
    }
  }

  /// Called when data is sent from the main isolate
  /// using FlutterForegroundTask.sendDataToTask.
  @override
  void onReceiveData(Object data) {
    try {
      if (data is Map<String, dynamic>) {
        final action = data['action'] as String?;

        switch (action) {
          case 'update_call_status':
            _callStatus = data['status'] as String? ?? 'Connecting...';
            _updateNotificationWithStatus();
          case 'start_call_timer':
            _callStartTime = DateTime.fromMillisecondsSinceEpoch(
              data['start_time'] as int? ??
                  DateTime.now().millisecondsSinceEpoch,
            );
            _isCallConnected = true;
            _callStatus = data['status'] as String? ?? 'Connected';
            // Don't start separate timer on iOS - use only onRepeatEvent
            if (!kIsWeb && Platform.isAndroid) {
              _startTimer();
            }
            _updateNotificationWithTimer();
          case 'end_call':
            _isCallConnected = false;
            _callStatus = data['status'] as String? ?? 'Call Ended';
            _timer?.cancel();
            _updateNotificationWithStatus();
            // Delay service stop to show "Call Ended" briefly
            Timer(const Duration(seconds: 2), () async {
              await fgt.FlutterForegroundTask.stopService();
            });
          case 'update_call_info':
            _callerName = data['caller_name'] as String?;
            _phoneNumber = data['phone_number'] as String?;
            if (_isCallConnected) {
              _updateNotificationWithTimer();
            } else {
              _updateNotificationWithStatus();
            }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print(
          '[PreferredCallingUserView] Error in ForegroundTaskHandler.onReceiveData: $e',
        );
      }
    }
  }

  /// Called based on the eventAction set in ForegroundTaskOptions.
  /// This replaces the deprecated onEvent method.
  @override
  void onRepeatEvent(DateTime timestamp) {
    try {
      // Skip all notification updates on iOS to prevent headaches
      if (Platform.isIOS) {
        // Still send timer updates for BLoC
        // synchronization if call is connected
        if (_isCallConnected && _callStartTime != null) {
          final duration = timestamp.difference(_callStartTime!);
          fgt.FlutterForegroundTask.sendDataToMain({
            'event': 'timer_update',
            'duration_seconds': duration.inSeconds,
            'timestamp': timestamp.millisecondsSinceEpoch,
          });
        }
        return;
      }

      // Android notification updates (original behavior)
      if (_isCallConnected && _callStartTime != null) {
        _updateNotificationWithTimer();
      } else {
        _updateNotificationWithStatus();
      }
    } catch (e) {
      if (kDebugMode) {
        print(
          '[PreferredCallingUserView] Error in ForegroundTaskHandler.onRepeatEvent: $e',
        );
      }
    }
  }

  /// Called when the task is started.
  /// [starter] indicates how the task was
  /// started (e.g., by user, by system boot, etc.)
  @override
  Future<void> onStart(DateTime timestamp, fgt.TaskStarter starter) async {
    if (_isServiceDestroyed) return; // Don't start if already destroyed

    // CRITICAL FIX FOR ANDROID: Acquire a wakelock to keep the CPU running
    try {
      await WakelockPlus.enable();
      if (kDebugMode) {
        print('[ForegroundTaskHandler] Wakelock enabled.');
      }
    } catch (e) {
      if (kDebugMode) {
        print('[ForegroundTaskHandler] Error enabling Wakelock: $e');
      }
    }

    try {
      // Log task startup for debugging
      if (kDebugMode) {
        print(
          'ForegroundTaskHandler started at: $timestamp by ${starter.name}',
        );
      }

      // Reset error counters on fresh start
      _notificationUpdateErrorCount = 0;
      _lastErrorTime = null;
      _isServiceInitialized = true;

      // Don't start timer immediately - wait for call connection
      // Service is active but call timer hasn't started yet

      // Send initial data to main isolate to confirm service is running
      fgt.FlutterForegroundTask.sendDataToMain({
        'event': 'service_started',
        'timestamp': timestamp.millisecondsSinceEpoch,
        'starter': starter.name,
        'initialized': _isServiceInitialized,
      });

      // Update initial notification with connecting status
      _updateNotificationWithStatus();
    } catch (e) {
      _isServiceInitialized = false;

      if (kDebugMode) {
        print(
          '[PreferredCallingUserView] Error in ForegroundTaskHandler.onStart: $e',
        );
      }

      // Send error event to main isolate
      try {
        fgt.FlutterForegroundTask.sendDataToMain({
          'event': 'service_start_error',
          'timestamp': timestamp.millisecondsSinceEpoch,
          'error': e.toString(),
          'initialized': _isServiceInitialized,
        });
      } catch (_) {
        // Last resort logging
        if (kDebugMode) {
          print(
            '[PreferredCallingUserView] Failed to send start error to main isolate',
          );
        }
      }
    }
  }

  /// Starts the internal timer for more frequent updates than the repeat event.
  /// Only used on Android to avoid iOS double-notification issue.
  void _startTimer() {
    // DISABLED: Do not update notification timer in the background
    _timer?.cancel();
    // _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
    //   _updateNotificationWithTimer();
    // });
  }

  /// Updates notification with call status (no timer shown).
  void _updateNotificationWithStatus() {
    // TEMPORARY: Disable notification updates
    if (kDisableForegroundNotification) return;

    // Skip notification updates on iOS completely
    if (Platform.isIOS || _isServiceDestroyed) return;

    // Don't update notifications if we've had too many errors
    if (_notificationUpdateErrorCount >= _maxNotificationErrors) {
      return;
    }

    try {
      var title = _callStatus;
      var text = 'Tap to return to the call';

      // Add caller info if available
      if (_callerName != null && _callerName!.isNotEmpty) {
        title = '$_callStatus with $_callerName';
      } else if (_phoneNumber != null && _phoneNumber!.isNotEmpty) {
        title = '$_callStatus with $_phoneNumber';
        text = _phoneNumber!;
      }

      // Only update if content has actually changed (Android only)
      if (_shouldUpdateNotification(title, text)) {
        try {
          fgt.FlutterForegroundTask.updateService(
            notificationTitle: title,
            notificationText: text,
          );

          _lastNotificationTitle = title;
          _lastNotificationText = text;
        } catch (e) {
          _handleNotificationError(e, 'status_notification_update');
        }
      }
    } catch (e) {
      _handleNotificationError(e, 'status_preparation');
    }
  }

  /// Updates notification with call timer (for connected calls).
  void _updateNotificationWithTimer() {
    // TEMPORARY: Disable notification updates
    if (kDisableForegroundNotification) return;

    if (_callStartTime == null || !_isCallConnected || _isServiceDestroyed) {
      return;
    }

    // Skip notification updates on iOS completely
    if (Platform.isIOS) {
      // Still send timer updates to main isolate for BLoC synchronization
      final duration = DateTime.now().difference(_callStartTime!);
      final nowTime = DateTime.now();
      fgt.FlutterForegroundTask.sendDataToMain({
        'event': 'timer_update',
        'duration_seconds': duration.inSeconds,
        'timestamp': nowTime.millisecondsSinceEpoch,
      });
      return;
    }

    // Don't update notifications if we've had too many errors
    if (_notificationUpdateErrorCount >= _maxNotificationErrors) {
      return;
    }

    try {
      final duration = DateTime.now().difference(_callStartTime!);
      final durationString = _formatDuration(duration);

      var title = 'Ongoing Call';
      if (_callerName != null && _callerName!.isNotEmpty) {
        title = 'Call with $_callerName';
      } else if (_phoneNumber != null && _phoneNumber!.isNotEmpty) {
        title = 'Call with $_phoneNumber';
      }

      // Send timer update to main isolate for BLoC synchronization
      final nowTime = DateTime.now();
      fgt.FlutterForegroundTask.sendDataToMain({
        'event': 'timer_update',
        'duration_seconds': duration.inSeconds,
        'timestamp': nowTime.millisecondsSinceEpoch,
      });

      // Only update notification if content actually changed
      if (_shouldUpdateNotification(title, durationString)) {
        try {
          fgt.FlutterForegroundTask.updateService(
            notificationTitle: title,
            notificationText: durationString,
          );

          _lastNotificationTitle = title;
          _lastNotificationText = durationString;
        } catch (e) {
          _handleNotificationError(e, 'timer_notification_update');
        }
      }
    } catch (e) {
      _handleNotificationError(e, 'timer_calculation');
    }
  }

  /// Checks if notification content has changed and warrants an update.
  bool _shouldUpdateNotification(String title, String text) {
    return _lastNotificationTitle != title || _lastNotificationText != text;
  }

  /// Formats duration into MM:SS format.
  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes.toString().padLeft(2, '0');
    final seconds = (duration.inSeconds % 60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }

  /// Handles notification update errors with throttling and logging.
  void _handleNotificationError(dynamic error, String operation) {
    _notificationUpdateErrorCount++;
    _lastErrorTime = DateTime.now();

    if (kDebugMode) {
      print(
        '[PreferredCallingUserView] Notification error during $operation: $error (Count: $_notificationUpdateErrorCount)',
      );
    }

    // Throttle notifications if too many errors recently
    if (_lastErrorTime != null &&
        DateTime.now().difference(_lastErrorTime!).inMinutes < 1 &&
        _notificationUpdateErrorCount > _maxNotificationErrors) {
      if (kDebugMode) {
        print(
          '[PreferredCallingUserView] Throttling notification updates due to repeated errors',
        );
      }
      return;
    }

    // Send error info to main isolate for analytics/logging
    try {
      fgt.FlutterForegroundTask.sendDataToMain({
        'event': 'notification_error',
        'operation': operation,
        'error': error.toString(),
        'error_count': _notificationUpdateErrorCount,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      if (kDebugMode) {
        print(
          '[PreferredCallingUserView] Failed to send notification error '
          'to main isolate: $e',
        );
      }
    }
  }
}

class PreferredCallingUserView extends StatefulHookWidget {
  const PreferredCallingUserView({
    required this.call,
    super.key,
    this.phoneNumber,
  });

  static String routeName = '/calling-user-page';

  final sip_ua.Call call;
  final String? phoneNumber;

  @override
  State<PreferredCallingUserView> createState() => _CallingUserPageState();

  static Route<Object?> route({required sip_ua.Call call}) {
    return MaterialPageRoute<Object?>(
      builder: (_) => PreferredCallingUserView(call: call),
    );
  }
}

class _CallingUserPageState extends State<PreferredCallingUserView>
    with EnhancedCallStateHandling {
  AuthenticationBloc? authenticationBloc;
  CallTimerBloc? callTimerBloc; // Add CallTimerBloc integration
  DialerBloc? dialerBloc;

  bool _audioMuted = false;

  /// iOS-specific background task management
  int? _backgroundTaskId;

  Duration _callDuration = Duration.zero;
  bool _callHasProgressed =
      false; // Track if call has progressed beyond initial state

  late final DateTime
      _callScreenCreatedAt; // Track when call screen was created

  Timer? _connectionQualityTimer;
  // Enhanced debugging and crash prevention
  late final CallTerminationDebugger _debugger;

  // SIP helper removed - all SIP operations now go through DialerBloc
  // sip_ua.SIPUAHelper? _helper;
  bool _hold = false;

  bool _isCallConnected = false;
  bool _isDisposed = false; // Add disposal flag to prevent crashes
  bool _isServiceInitialized = false;
  Timer? _keepAliveTimer;
  double _lastRTT = 0;
  RTCVideoRenderer? _localRenderer = RTCVideoRenderer();
  // MediaStream? _localStream; // Removed - now handled by DialerBloc
  // Production-grade call quality metrics
  int _packetLossCount = 0;

  RTCVideoRenderer? _remoteRenderer = RTCVideoRenderer();
  // MediaStream? _remoteStream; // Removed - now handled by DialerBloc
  bool _showNumPad = false;

  bool _speakerOn = false;

  // SIP event handlers are now managed by DialerBloc
  // Removed the `implements sip_ua.SipUaHelperListener` from class declaration

  @override
  void deactivate() {
    _performSystemCleanup(); // Ensure cleanup happens when navigating away
    super.deactivate();
    _safeCleanup();
  }

  @override
  void dispose() {
    _safeCleanup();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();

    // Initialize timestamp to track call screen creation
    _callScreenCreatedAt = DateTime.now();

    // Initialize debugging utilities
    _debugger = CallTerminationDebugger();
    _debugger.startCallSession(widget.call.id ?? 'unknown_call');

    try {
      dialerBloc = BlocProvider.of<DialerBloc>(context);
      debugPrint(
        '[PreferredCallingUserView] DialerBloc initialized: ${dialerBloc != null}',
      );
    } catch (e) {
      debugPrint(
        '[PreferredCallingUserView] ERROR: Failed to get DialerBloc: $e',
      );
    }

    try {
      authenticationBloc = BlocProvider.of<AuthenticationBloc>(context);
      debugPrint(
        '[PreferredCallingUserView] AuthenticationBloc initialized: ${authenticationBloc != null}',
      );
    } catch (e) {
      debugPrint(
        '[PreferredCallingUserView] ERROR: Failed to get AuthenticationBloc: $e',
      );
    }

    try {
      callTimerBloc = BlocProvider.of<CallTimerBloc>(context);
      debugPrint(
        '[PreferredCallingUserView] CallTimerBloc initialized: ${callTimerBloc != null}',
      );
    } catch (e) {
      debugPrint(
        '[PreferredCallingUserView] ERROR: Failed to get CallTimerBloc: $e',
      );
    }

    // Let DialerBloc handle all SIP events instead of duplicating listeners
    // _helper = context.read<sip_ua.SIPUAHelper>();
    // _helper?.addSipUaHelperListener(this);

    _initializeCall();
  }

  sip_ua.Call get call => widget.call;

  String? get remoteIdentity => call.remote_identity;

  String? get phoneNumber => widget.phoneNumber ?? call.remote_identity;

  /// Initializes all call-related services and monitoring.
  Future<void> _initializeCall() async {
    try {
      // Pass the current call to DialerBloc so it can perform SIP operations
      debugPrint(
        '[PreferredCallingUserView] Passing call to DialerBloc: ${call.id}',
      );
      if (dialerBloc != null) {
        dialerBloc!.add(DialerEvent.updatedCall(call: call));
        debugPrint(
          '[PreferredCallingUserView] Successfully sent updatedCall event to DialerBloc',
        );
      } else {
        debugPrint(
          '[PreferredCallingUserView] WARNING: DialerBloc is null, cannot send updatedCall event',
        );
      }

      await _initRenderers();
      await _initWakePlus();
      await _startForegroundService();
      _setupForegroundTaskListener();
      _startKeepAlive();
      _startConnectionQualityMonitoring();

      setState(() {
        _isServiceInitialized = true;
      });

      debugPrint(
        '[PreferredCallingUserView] Call initialization completed successfully',
      );
    } catch (e) {
      debugPrint('[PreferredCallingUserView] Error initializing call: $e');

      // Still mark as initialized to prevent infinite loading
      setState(() {
        _isServiceInitialized = true;
      });

      _handleError('Failed to initialize call service');
    }
  }

  /// Safe cleanup that prevents multiple cleanup calls and race conditions.
  void _safeCleanup() {
    if (_isDisposed) return; // Prevent multiple cleanup calls
    _isDisposed = true;

    // Enhanced cleanup logging
    _debugger.trackException(
      'Widget disposal initiated',
      'Normal widget cleanup',
      StackTrace.current,
    );

    // Remove SIP listener immediately to prevent callbacks during cleanup
    // NOTE: SIP listener is now handled by DialerBloc, not by this view
    // try {
    //   _helper?.removeSipUaHelperListener(this);
    // } catch (e) {
    //   debugPrint('[PreferredCallingUserView] Error removing SIP listener: $e');
    //   _debugger.trackException(
    //     'SIP listener removal failed',
    //     e,
    //     StackTrace.current,
    //   );
    // }

    _cleanupResources();

    // End debugging session
    _debugger.endCallSession();
  }

  /// Comprehensive cleanup to prevent memory leaks
  /// and ensure proper call termination.
  void _cleanupResources() {
    try {
      // SIP listener already removed in _safeCleanup()

      // Cancel timers first to prevent any ongoing operations
      _keepAliveTimer?.cancel();
      _keepAliveTimer = null;
      _connectionQualityTimer?.cancel();
      _connectionQualityTimer = null;

      // Remove foreground task callback with safety check
      try {
        fgt.FlutterForegroundTask.removeTaskDataCallback(_onReceiveTaskData);
      } catch (e) {
        debugPrint(
          '[PreferredCallingUserView] Error removing task callback: $e',
        );
      }

      // Dispose renderers safely
      _disposeRenderers();

      // Clean up streams - delegated to CallService
      // CallService handles media stream cleanup
      // _cleanUp(); // Removed - now handled by DialerBloc/CallService

      // Disable wakelock
      _destroyWakePlus();

      // Stop foreground service last
      _stopForegroundService();
    } catch (e) {
      debugPrint('[PreferredCallingUserView] Error during cleanup: $e');
    }
  }

  /// Sets up listener for foreground task events to handle user interactions.
  void _setupForegroundTaskListener() {
    fgt.FlutterForegroundTask.addTaskDataCallback(_onReceiveTaskData);
  }

  /// Handles data received from the foreground task service.
  void _onReceiveTaskData(Object data) {
    if (data is Map<String, dynamic>) {
      final event = data['event'] as String?;

      switch (event) {
        case 'hangup_requested':
          debugPrint(
            '[PreferredCallingUserView] Hangup requested from notification button',
          );

          // Track the notification button hangup for analytics
          _trackCallTermination('notification_button_hangup');

          // Call the same hangup method as the in-app button
          _handleHangup();
        case 'return_to_call_requested':
          // Track the interaction for analytics
          _trackUserInteraction('notification_return_tapped');

          // Ensure the call screen is visible and app is in foreground
          _handleReturnToCall();
        case 'notification_pressed':
          // Analytics or custom behavior when notification is tapped
          _trackUserInteraction('notification_tapped');
        case 'timer_update':
          // Synchronize timer updates from foreground service with CallTimerBloc
          final durationSeconds = data['duration_seconds'] as int?;
          final timestamp = data['timestamp'] as int?;

          if (durationSeconds != null && timestamp != null) {
            final duration = Duration(seconds: durationSeconds);

            // Update CallTimerBloc with synchronized timer data
            callTimerBloc?.add(
              CallTimerEvent.syncFromForegroundService(
                duration: duration,
                timestamp: DateTime.fromMillisecondsSinceEpoch(timestamp),
              ),
            );

            // Update local duration for UI
            if (mounted && !_isDisposed) {
              setState(() {
                _callDuration = duration;
              });
            }
          }
        case 'service_started':
          _handleServiceStarted(data);
        case 'service_destroyed':
          _handleServiceDestroyed(data);
        case 'notification_error':
          _handleNotificationError(data);
        case 'service_start_error':
        case 'service_destroyed_with_error':
          _handleServiceError(data);
      }
    }
  }

  /// Starts monitoring connection quality for production reliability.
  void _startConnectionQualityMonitoring() {
    _connectionQualityTimer =
        Timer.periodic(const Duration(seconds: 5), (timer) {
      _checkConnectionQuality();
    });
  }

  /// Monitors call quality metrics for debugging and user experience.
  Future<void> _checkConnectionQuality() async {
    try {
      if (_isCallConnected && call.session.isEstablished()) {
        // Get WebRTC stats for connection quality
        final stats = await _getConnectionStats();

        if (stats != null) {
          final packetLoss = stats['packetsLost'] as int? ?? 0;
          final rtt = stats['roundTripTime'] as double? ?? 0.0;

          if (packetLoss > _packetLossCount + 10) {
            debugPrint(
              '[PreferredCallingUserView] Warning: High packet loss detected: $packetLoss',
            );
            _showConnectionWarning('Poor connection quality detected');
          }

          _packetLossCount = packetLoss;
          _lastRTT = rtt;
        }
      }
    } catch (e) {
      debugPrint(
        '[PreferredCallingUserView] Error checking connection quality: $e',
      );
    }
  }

  /// Gets WebRTC connection statistics for quality monitoring.
  Future<Map<String, dynamic>?> _getConnectionStats() async {
    try {
      if (_localRenderer?.srcObject != null) {
        // Get WebRTC statistics for real connection quality monitoring
        final sender = _localRenderer!.srcObject!.getAudioTracks().firstOrNull;

        if (sender != null) {
          final peerConnection = call.session.connection;
          if (peerConnection != null) {
            final stats = await peerConnection.getStats();

            // Parse relevant statistics from WebRTC stats
            for (final report in stats) {
              if (report.type == 'inbound-rtp' &&
                  report.values['mediaType'] == 'audio') {
                final packetsLost = report.values['packetsLost'] as int? ?? 0;
                final jitter = report.values['jitter'] as double? ?? 0.0;
                final bytesReceived =
                    report.values['bytesReceived'] as int? ?? 0;

                return {
                  'packetsLost': packetsLost,
                  'jitter': jitter,
                  'bytesReceived': bytesReceived,
                  'timestamp': report.timestamp,
                };
              }

              if (report.type == 'candidate-pair' &&
                  report.values['state'] == 'succeeded') {
                final rtt =
                    report.values['currentRoundTripTime'] as double? ?? 0.0;
                final availableOutgoingBitrate =
                    report.values['availableOutgoingBitrate'] as int? ?? 0;

                return {
                  'packetsLost': _packetLossCount,
                  'roundTripTime': rtt * 1000, // Convert to milliseconds
                  'bitrate': availableOutgoingBitrate,
                  'timestamp': report.timestamp,
                };
              }
            }
          }
        }

        // Fallback to cached values if no stats available
        return {
          'packetsLost': _packetLossCount,
          'roundTripTime': _lastRTT,
          'bitrate': 0,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        };
      }
    } catch (e) {
      debugPrint(
        '[PreferredCallingUserView] Error getting connection stats: $e',
      );
    }
    return null;
  }

  /// Shows connection quality warning to user.
  void _showConnectionWarning(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.orange,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  /// Tracks user interactions for marketing analytics.
  void _trackUserInteraction(String action) {
    try {
      // Only track genuine user interactions for marketing analytics
      final isUserAction = _isMarketingRelevantAction(action);

      if (isUserAction) {
        debugPrint('[PreferredCallingUserView] Marketing event: $action');
        context.read<EventTrackerService>().logEvent(
          description: 'User interaction during call: $action',
          schema: 'user_behavior', // Changed to user_behavior schema
          data: {
            'action': action,
            'call_id': call.id,
            'remote_identity': remoteIdentity,
            'phone_number': phoneNumber,
            'timestamp': DateTime.now().toIso8601String(),
          },
        );
      } else {
        // Log developer events separately for debugging
        _trackDeveloperEvent(action);
      }
    } catch (e) {
      debugPrint(
        '[PreferredCallingUserView] Error tracking user interaction: $e',
      );
    }
  }

  /// Tracks developer/system events for debugging (not marketing).
  void _trackDeveloperEvent(String action) {
    try {
      debugPrint('[PreferredCallingUserView] Developer event: $action');
      // Use a different service or schema for developer events
      context.read<EventTrackerService>().logEvent(
        description: 'System event during call: $action',
        schema: 'developer_debug', // Separate schema for dev events
        data: {
          'event': action,
          'call_id': call.id,
          'timestamp': DateTime.now().toIso8601String(),
          'session_duration':
              DateTime.now().difference(_callScreenCreatedAt).inMilliseconds,
        },
      );
    } catch (e) {
      debugPrint(
        '[PreferredCallingUserView] Error tracking developer event: $e',
      );
    }
  }

  /// Determines if an action is relevant for marketing analytics.
  bool _isMarketingRelevantAction(String action) {
    // Marketing-relevant user actions
    const marketingActions = {
      'call_connected',
      'call_ended',
      'hangup_initiated',
      'mute',
      'unmute',
      'speaker_on',
      'speaker_off',
      'keypad_opened',
      'keypad_closed',
      'dtmf_sent',
      'notification_return_tapped', // User chose to return
      'notification_tapped', // User interacted with notification
    };

    return marketingActions.contains(action);
  }

  /// Handles production-grade error scenarios with user feedback.
  void _handleError(String message) {
    if (mounted && kDebugMode) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          action: SnackBarAction(
            label: 'Retry',
            onPressed: _initializeCall,
          ),
        ),
      );
    }
  }

  void _refreshProfile() {
    authenticationBloc?.add(const AuthenticationEvent.refreshProfile());
  }

  /// Handles foreground service startup events
  void _handleServiceStarted(Map<String, dynamic> data) {
    try {
      final timestamp = data['timestamp'] as int?;
      final starter = data['starter'] as String?;

      debugPrint(
        '[PreferredCallingUserView] Foreground service started at ${timestamp ?? 'unknown'} by ${starter ?? 'unknown'}',
      );

      // Track service lifecycle for debugging (not marketing)
      _trackDeveloperEvent('foreground_service_started');
    } catch (e) {
      debugPrint(
        '[PreferredCallingUserView] Error handling service started event: $e',
      );
    }
  }

  /// Handles foreground service destruction events
  void _handleServiceDestroyed(Map<String, dynamic> data) {
    try {
      final timestamp = data['timestamp'] as int?;
      final notificationErrors = data['notification_errors'] as int?;

      debugPrint(
        '[PreferredCallingUserView] Foreground service destroyed at ${timestamp ?? 'unknown'}, notification errors: ${notificationErrors ?? 0}',
      );

      // Track service lifecycle for debugging (not marketing)
      _trackDeveloperEvent('foreground_service_destroyed');

      // Log notification errors if any
      if (notificationErrors != null && notificationErrors > 0) {
        debugPrint(
          '[PreferredCallingUserView] Foreground service had $notificationErrors notification errors',
        );
      }
    } catch (e) {
      debugPrint(
        '[PreferredCallingUserView] Error handling service destroyed event: $e',
      );
    }
  }

  /// Handles notification errors from foreground service
  void _handleNotificationError(Map<String, dynamic> data) {
    try {
      final operation = data['operation'] as String?;
      final error = data['error'] as String?;
      final errorCount = data['error_count'] as int?;
      final timestamp = data['timestamp'] as int?;

      debugPrint(
        '[PreferredCallingUserView] Notification error during $operation: $error (Count: $errorCount)',
      );

      // Log error for analytics/debugging
      context.read<EventTrackerService>().logEvent(
        description: 'Foreground notification error: $operation',
        schema: 'notification_error',
        data: {
          'operation': operation ?? 'unknown',
          'error': error ?? 'unknown',
          'error_count': errorCount ?? 0,
          'call_id': call.id,
          'timestamp': timestamp != null
              ? DateTime.fromMillisecondsSinceEpoch(timestamp).toIso8601String()
              : DateTime.now().toIso8601String(),
        },
      );

      // Show user warning if too many errors
      if (errorCount != null && errorCount >= 3) {
        _showConnectionWarning('Notification issues detected');
      }
    } catch (e) {
      debugPrint(
        '[PreferredCallingUserView] Error handling notification error event: $e',
      );
    }
  }

  /// Handles service errors from foreground service
  void _handleServiceError(Map<String, dynamic> data) {
    try {
      final error = data['error'] as String?;
      final timestamp = data['timestamp'] as int?;

      debugPrint(
        '[PreferredCallingUserView] Foreground service error: $error at ${timestamp ?? 'unknown'}',
      );

      // Log service error for analytics
      context.read<EventTrackerService>().logEvent(
        description: 'Foreground service error',
        schema: 'service_error',
        data: {
          'error': error ?? 'unknown',
          'call_id': call.id,
          'timestamp': timestamp != null
              ? DateTime.fromMillisecondsSinceEpoch(timestamp).toIso8601String()
              : DateTime.now().toIso8601String(),
        },
      );

      // Consider restarting service if critical error
      _showConnectionWarning('Call service encountered an issue');
    } catch (e) {
      debugPrint(
        '[PreferredCallingUserView] Error handling service error event: $e',
      );
    }
  }

  /// Production-grade keep-alive mechanism with retry logic.
  void _startKeepAlive() {
    const keepAliveInterval = Duration(seconds: 30);
    _keepAliveTimer = Timer.periodic(keepAliveInterval, (timer) {
      try {
        final currentSession = call.session;
        if (currentSession.isEstablished()) {
          // currentSession.sendRequest(sip_ua.SipMethod.UPDATE);
        } else {
          // Delegate registration to DialerBloc instead of direct SIP call
          dialerBloc?.add(const DialerEvent.reloadAsterisk());
        }
      } catch (e) {
        debugPrint('[PreferredCallingUserView] Keep-alive error: $e');
        // Implement retry logic or connection recovery
        _handleConnectionLoss();
      }
    });
  }

  /// Handles connection loss with automatic recovery attempts.
  void _handleConnectionLoss() {
    if (_isCallConnected) {
      _showConnectionWarning('Connection lost. Attempting to reconnect...');

      // Attempt to re-register and maintain call
      Future.delayed(const Duration(seconds: 2), () {
        if (_isCallConnected) {
          // Delegate registration to DialerBloc instead of direct SIP call
          dialerBloc?.add(const DialerEvent.reloadAsterisk());
        }
      });
    }
  }

  Future<void> _initRenderers() async {
    try {
      if (_localRenderer != null) {
        await SafeWebRTCOperations.safeInitializeRenderer(_localRenderer!);
      }
      if (_remoteRenderer != null) {
        await SafeWebRTCOperations.safeInitializeRenderer(_remoteRenderer!);
      }
    } catch (e) {
      debugPrint('[PreferredCallingUserView] Error initializing renderers: $e');
      await MediaErrorHandlerService.reportMediaError(
        Exception('Renderer initialization failed: $e'),
        operation: 'initialize_renderers',
        mediaType: 'video_renderer',
        stackTrace: StackTrace.current,
        additionalData: {
          'call_id': call.id,
          'remote_identity': remoteIdentity,
          'local_renderer_available': _localRenderer != null,
          'remote_renderer_available': _remoteRenderer != null,
        },
      );
      throw Exception(
        '[PreferredCallingUserView] Failed to initialize video renderers',
      );
    }
  }

  void _disposeRenderers() {
    try {
      if (_localRenderer != null) {
        SafeWebRTCOperations.safeDisposeRenderer(_localRenderer!);
        _localRenderer = null;
      }
      if (_remoteRenderer != null) {
        SafeWebRTCOperations.safeDisposeRenderer(_remoteRenderer!);
        _remoteRenderer = null;
      }
    } catch (e) {
      debugPrint('[PreferredCallingUserView] Error disposing renderers: $e');
      MediaErrorHandlerService.reportMediaError(
        Exception('Renderer disposal failed: $e'),
        operation: 'dispose_renderers',
        mediaType: 'video_renderer',
        stackTrace: StackTrace.current,
        additionalData: {
          'call_id': call.id ?? 'unknown',
        },
      );
    }
  }

  /// Handles when call is successfully connected.
  void _handleCallConnected() {
    if (_isDisposed) return; // Safety check

    // Only start timer if SIP session is established
    if (!call.session.isEstablished()) {
      debugPrint(
        '[PreferredCallingUserView] Call not established, skipping timer start',
      );
      return;
    }

    if (mounted) {
      setState(() {
        _isCallConnected = true;
      });
    }

    // Update dialer status to connected
    dialerBloc?.add(
      const DialerEvent.updatedDialerStatus(
        status: DialerStatus.connected,
      ),
    );

    final connectedMessage = mounted
        ? DialerStatus.connected.getLocalizedMessage(context)
        : 'Connected';
    _updateCallStatus(connectedMessage);

    // TIMER SYNCHRONIZATION FIX: Capture the exact start time once
    final callStartTime = DateTime.now();
    final callStartTimestamp = callStartTime.millisecondsSinceEpoch;

    // Start centralized timer using CallTimerBloc with synchronized time
    callTimerBloc
        ?.add(CallTimerEvent.timerStartedWithTime(startTime: callStartTime));

    // Also start legacy DialerBloc timer for backward compatibility
    dialerBloc?.add(const DialerEvent.timerStarted());

    // Send call start info to foreground service with SAME timestamp
    try {
      fgt.FlutterForegroundTask.sendDataToTask({
        'action': 'start_call_timer',
        'start_time': callStartTimestamp, // Use the SAME timestamp
        'status': connectedMessage, // Send localized status
      });

      // Update notification with caller info
      if (phoneNumber != null) {
        fgt.FlutterForegroundTask.sendDataToTask({
          'action': 'update_call_info',
          'phone_number': phoneNumber,
          'caller_name': _getDisplayName(),
        });
      }
    } catch (e) {
      debugPrint(
        '[PreferredCallingUserView] Error sending call start info: $e',
      );
    }

    _trackUserInteraction('call_connected');
  }

  /// Gets display name for the caller (could be from contacts).
  String? _getDisplayName() {
    // In a production app, you would look up the contact name
    // from the phone number using the contacts repository
    return null; // Return actual contact name if available
  }

  /// Updates call status in the UI.
  void _updateCallStatus(String status) {
    debugPrint('[PreferredCallingUserView] Call status: $status');
    // You could emit this to a BLoC or update UI state
    // For now, we'll just log it
  }

  /// Sends call status updates to the foreground service.
  void _sendStatusToForegroundService(String status) {
    try {
      fgt.FlutterForegroundTask.sendDataToTask({
        'action': 'update_call_status',
        'status': status,
      });
    } catch (e) {
      debugPrint(
        '[PreferredCallingUserView] Error sending status to foreground service: $e',
      );
    }
  }

  /// Handles call end logic, including UI updates and state management.
  void _handleCallEnd() {
    if (_isDisposed) return; // Prevent operations after disposal

    try {
      // Enhanced debugging for call termination
      _debugger.trackException(
        'Call termination initiated\n'
            'connected: _isCallConnected\n'
            'duration: _callDuration',
        'Normal call termination',
        StackTrace.current,
      );

      // Stop centralized timer using CallTimerBloc
      callTimerBloc?.add(const CallTimerEvent.timerStopped());

      // Only call setState if widget is still mounted and not disposed
      if (mounted && !_isDisposed) {
        setState(() {
          _isCallConnected = false;
          _callDuration = Duration.zero; // Reset timer duration
        });
      }

      _refreshProfile();
      _trackUserInteraction('call_ended');

      // Send end call signal to foreground service with error handling
      try {
        final endedMessage = mounted
            ? DialerStatus.ended.getLocalizedMessage(context)
            : 'Call Ended';

        fgt.FlutterForegroundTask.sendDataToTask({
          'action': 'end_call',
          'status': endedMessage, // Send localized end message
        });
      } catch (e) {
        debugPrint(
          '[PreferredCallingUserView] Error sending end call signal: $e',
        );
      }

      // Small delay to ensure state is properly updated before navigation
      Future.delayed(const Duration(milliseconds: 100), () {
        if (!_isDisposed) {
          _routeBack();
        }
      });
    } catch (e) {
      debugPrint('[PreferredCallingUserView] Error handling call end: $e');
      _routeBack(); // Ensure we always navigate back
    }
  }

  void _routeBack() {
    if (_isDisposed) return; // Prevent navigation after disposal

    // Ensure system cleanup before navigating away
    _performSystemCleanup();

    if (mounted && !_isDisposed) {
      try {
        // Use context check before navigation
        if (context.mounted) {
          Navigator.of(context).maybePop();
        }
      } catch (e) {
        debugPrint('[PreferredCallingUserView] Error during navigation: $e');
        // Try alternative navigation method
        try {
          if (context.mounted) {
            Navigator.of(context).pop();
          }
        } catch (e2) {
          debugPrint(
            '[PreferredCallingUserView] Error with alternative navigation: $e2',
          );
        }
      }
    }
  }

  /// Production-grade hangup with error handling and cleanup.
  void _handleHangup() {
    if (_isDisposed) return; // Prevent operations after disposal

    try {
      _trackUserInteraction('hangup_initiated');
      _trackCallTermination('user_hangup');

      // Use DialerBloc for hangup instead of direct SIP operation
      if (dialerBloc != null) {
        debugPrint(
          '[PreferredCallingUserView] Sending hangup event to DialerBloc',
        );
        dialerBloc!.add(const DialerEvent.hangedup());

        // DON'T update UI immediately - wait for bloc to confirm call has ended
        // The MultiBlocListener will handle the UI transition when DialerStatus.ended is received
        debugPrint(
          '[PreferredCallingUserView] Hangup event sent, waiting for bloc confirmation',
        );

        // Add a timeout safety mechanism - if bloc doesn't respond in 5 seconds, force navigation
        Timer(const Duration(seconds: 5), () {
          if (mounted && !_isDisposed && _isCallConnected) {
            debugPrint(
              '[PreferredCallingUserView] Timeout waiting for bloc hangup confirmation, performing verification and forcing navigation',
            );
            _verifyCallEndedAndCleanup();
            _handleCallEnd();
          }
        });
      } else {
        debugPrint(
          '[PreferredCallingUserView] ERROR: DialerBloc is null, cannot send hangup event',
        );
        // Fallback to direct SIP operation if DialerBloc is unavailable
        if (call.session.isEstablished()) {
          call.hangup();
        }
        // Handle UI transition immediately for fallback case
        if (mounted && !_isDisposed) {
          setState(() {
            _isCallConnected = false;
          });
        }
        _handleCallEnd();
      }

      // Use localized message for ending call status
      final endingCallMessage = mounted
          ? DialerStatus.ended.getLocalizedMessage(context)
          : 'Ending call...';
      _updateCallStatus(endingCallMessage);
      _sendStatusToForegroundService(endingCallMessage);
    } catch (e) {
      debugPrint('[PreferredCallingUserView] Error during hangup: $e');
      // Force navigation back even if hangup fails
      _handleCallEnd();
    }
  }

  /// Enhanced audio muting with user feedback and safe operations.
  void _muteAudio() {
    try {
      if (dialerBloc == null) {
        debugPrint(
          '[PreferredCallingUserView] ERROR: DialerBloc is null, cannot toggle mute',
        );
        // Fallback to direct SIP operation if DialerBloc is unavailable
        if (_audioMuted) {
          call.unmute();
        } else {
          call.mute();
        }
        setState(() => _audioMuted = !_audioMuted);
        return;
      }

      if (_audioMuted) {
        // Use DialerBloc for unmute instead of direct SIP operation
        debugPrint(
          '[PreferredCallingUserView] Sending unmute event to DialerBloc',
        );
        dialerBloc!.add(const DialerEvent.muteOff());
        _updateCallStatus('Microphone unmuted');
      } else {
        // Use DialerBloc for mute instead of direct SIP operation
        debugPrint(
          '[PreferredCallingUserView] Sending mute event to DialerBloc',
        );
        dialerBloc!.add(const DialerEvent.muteOn());
        _updateCallStatus('Microphone muted');
      }
      _trackUserInteraction(_audioMuted ? 'unmute' : 'mute');
    } catch (e) {
      debugPrint('[PreferredCallingUserView] Error toggling mute: $e');

      // Report audio muting error with detailed context
      MediaErrorHandlerService.reportMediaError(
        Exception('Audio mute toggle failed: $e'),
        operation: 'toggle_audio_mute',
        mediaType: 'audio_control',
        stackTrace: StackTrace.current,
        additionalData: {
          'call_id': call.id,
          'was_muted': _audioMuted,
        },
      );

      _handleError('Failed to toggle microphone');
    }
  }

  /// Enhanced hold functionality with status tracking.
  /// Available for future use when hold/unhold buttons are added to UI.
  // ignore: unused_element
  void _handleHold() {
    try {
      if (_hold) {
        // Use DialerBloc for unhold instead of direct SIP operation
        dialerBloc?.add(const DialerEvent.unholdCall());
        _updateCallStatus('Call resumed');
      } else {
        // Use DialerBloc for hold instead of direct SIP operation
        dialerBloc?.add(const DialerEvent.holdCall());
        _updateCallStatus('Call on hold');
      }
      _trackUserInteraction(_hold ? 'unhold' : 'hold');
    } catch (e) {
      debugPrint('[PreferredCallingUserView] Error toggling hold: $e');
      _handleError('Failed to toggle call hold');
    }
  }

  /// Enhanced DTMF handling with validation.
  void _handleDtmf(String tone) {
    try {
      // Validate DTMF tone
      if (!RegExp(r'^[0-9*#]$').hasMatch(tone)) {
        debugPrint('[PreferredCallingUserView] Invalid DTMF tone: $tone');
        return;
      }

      call.sendDTMF(tone);
      _updateCallStatus('DTMF sent: $tone');
      _trackUserInteraction('dtmf_sent');
    } catch (e) {
      debugPrint('[PreferredCallingUserView] Error sending DTMF: $e');
      _handleError('Failed to send DTMF tone');
    }
  }

  void _handleKeyPad() {
    setState(() => _showNumPad = !_showNumPad);
    _trackUserInteraction(_showNumPad ? 'keypad_opened' : 'keypad_closed');
  }

  /// Enhanced speaker toggle with error handling and WebRTC integration.
  void _toggleSpeaker(bool value) {
    try {
      // Use DialerBloc for speaker control instead of direct stream manipulation
      if (value) {
        dialerBloc?.add(const DialerEvent.switchSpeakerPhoneOn());
      } else {
        dialerBloc?.add(const DialerEvent.disableSpeakerPhone());
      }

      setState(() => _speakerOn = value);
      _updateCallStatus(value ? 'Speaker enabled' : 'Speaker disabled');
      _trackUserInteraction(value ? 'speaker_on' : 'speaker_off');
    } catch (e) {
      debugPrint('[PreferredCallingUserView] Error toggling speaker: $e');

      // Report speaker toggle error with detailed context
      MediaErrorHandlerService.reportMediaError(
        Exception('Speaker toggle failed: $e'),
        operation: 'toggle_speaker',
        mediaType: 'audio_control',
        stackTrace: StackTrace.current,
        additionalData: {
          'call_id': call.id,
          'speaker_value': value,
        },
      );

      _handleError('Failed to toggle speaker');
    }
  }

  /// Builds the main call content area.
  /// Builds the call timer display.
  Widget _buildCallTimer() {
    return Center(
      child: Text(
        _formatCallDuration(_callDuration),
        style: const TextStyle(
          color: Colors.white,
          fontSize: 40,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  /// Builds connection quality indicator.
  Widget _buildConnectionQualityIndicator() {
    IconData icon;
    Color color;

    if (_lastRTT < 100 && _packetLossCount < 5) {
      icon = Icons.signal_cellular_4_bar;
      color = Colors.green;
    } else if (_lastRTT < 200 && _packetLossCount < 10) {
      icon = Icons.signal_cellular_alt;
      color = Colors.yellow;
    } else {
      icon = Icons.signal_cellular_null;
      color = Colors.red;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.black54,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Icon(
        icon,
        color: color,
        size: 24,
      ),
    );
  }

  Positioned _buildText(
    BuildContext context, {
    required double top,
    required double left,
    required double fontSize,
    required FontWeight fontWeight,
    required String label,
    TextDirection? textDirection,
  }) {
    return Positioned(
      top: top,
      left: MediaQuery.of(context).size.width / 2 -
          left, // Adjust 15 based on flag width
      child: SizedBox.fromSize(
        size: const Size(200, 50),
        child: Text(
          label,
          textDirection: textDirection,
          style: TextStyle(
            color: FroggyColors.white,
            fontSize: fontSize,
            fontWeight: fontWeight,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Positioned _buildFlag(
    BuildContext context, {
    double flagSize = 20,
    String countryCode = 'ng',
  }) {
    return Positioned(
      top: 80,
      left: MediaQuery.of(context).size.width / 2 -
          flagSize, // Adjust 15 based on flag width
      child: FroggyCountries.showCountryFlagByCountryCode(
        countryCode: countryCode,
        width: flagSize,
        height: flagSize,
        margin: 0,
      ),
    );
  }

  /// Formats call duration for display.
  String _formatCallDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:'
          '${minutes.toString().padLeft(2, '0')}:'
          '${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:'
          '${seconds.toString().padLeft(2, '0')}';
    }
  }

  Future<void> _initWakePlus() async => WakelockPlus.enable();

  Future<void> _destroyWakePlus() async => WakelockPlus.disable();

  /// iOS background task handler for maintaining call state
  Future<void> _startIOSBackgroundTask() async {
    if (!Platform.isIOS) return;

    try {
      // Request background processing capability on iOS
      debugPrint(
        '[PreferredCallingUserView] Starting iOS background task for call',
      );

      // Instead of foreground service, use iOS background task
      // This allows the call to continue when app goes to background

      // Track that background task is active
      _backgroundTaskId = DateTime.now().millisecondsSinceEpoch;

      debugPrint(
        '[PreferredCallingUserView] iOS background task started: $_backgroundTaskId',
      );
    } catch (e) {
      debugPrint(
        '[PreferredCallingUserView] Error starting iOS background task: $e',
      );
    }
  }

  /// Stop iOS background task
  Future<void> _stopIOSBackgroundTask() async {
    if (!Platform.isIOS || _backgroundTaskId == null) return;

    try {
      debugPrint(
        '[PreferredCallingUserView] Stopping iOS background task: $_backgroundTaskId',
      );
      _backgroundTaskId = null;
    } catch (e) {
      debugPrint(
        '[PreferredCallingUserView] Error stopping iOS background task: $e',
      );
    }
  }

  Future<void> _startForegroundService() async {
    try {
      // Handle iOS differently - use background tasks instead of foreground service
      if (Platform.isIOS) {
        await _startIOSBackgroundTask();
        debugPrint(
          '[PreferredCallingUserView] iOS background task management active',
        );
        return;
      }

      // Android foreground service implementation
      // Check if service is already running and restart if needed
      if (await fgt.FlutterForegroundTask.isRunningService) {
        await fgt.FlutterForegroundTask.restartService();
      } else {
        // Ensure proper service configuration for Android
        final serviceConfig = await _buildServiceConfiguration();

        await fgt.FlutterForegroundTask.startService(
          serviceId: serviceConfig['serviceId'] as int,
          notificationTitle: serviceConfig['notificationTitle'] as String,
          notificationText: serviceConfig['notificationText'] as String,
          notificationButtons: serviceConfig['notificationButtons']
              as List<fgt.NotificationButton>,
          callback: _foregroundTaskCallback,
        );
      }

      // Verify service started successfully (Android only)
      final isRunning = await fgt.FlutterForegroundTask.isRunningService;
      if (!isRunning) {
        throw Exception('Android foreground service failed to start');
      }

      debugPrint(
        '[PreferredCallingUserView] Android foreground service started successfully',
      );
    } catch (e) {
      debugPrint(
        '[PreferredCallingUserView] Error starting foreground service: $e',
      );

      // Track service startup failure
      if (mounted) {
        await context.read<EventTrackerService>().logEvent(
          description: 'Foreground service startup failed',
          schema: 'service_error',
          data: {
            'error': e.toString(),
            'platform': Platform.operatingSystem,
            'call_id': call.id,
            'phone_number': phoneNumber,
            'timestamp': DateTime.now().toIso8601String(),
          },
        );
      }

      // On iOS, don't show error for foreground service issues
      // since we're using background tasks instead
      if (Platform.isIOS) {
        debugPrint(
          '[PreferredCallingUserView] iOS using background task management instead of foreground service',
        );
        return;
      }

      // Only show error on Android where foreground service is critical
      _handleError('Call notification service unavailable');
    }
  }

  /// Builds platform-specific service configuration
  Future<Map<String, dynamic>> _buildServiceConfiguration() async {
    if (!mounted) return {};

    final blocStatus = context.select(
      (DialerBloc bloc) => bloc.state.status,
    );

    try {
      // Request notification permissions on supported platforms
      await _requestNotificationPermissions();

      if (!mounted) return {};

      return {
        'serviceId': 256, // Consistent service ID
        'notificationTitle': blocStatus.getLocalizedMessage(context),
        'notificationText': phoneNumber != null
            ? 'Call to $phoneNumber'
            : 'Tap to return to the call',
        'notificationButtons': _buildNotificationButtons(),
      };
    } catch (e) {
      debugPrint(
        '[PreferredCallingUserView] Error building service configuration: $e',
      );
      // Return basic configuration as fallback
      return {
        'serviceId': 256,
        'notificationTitle': blocStatus.getLocalizedMessage(context),
        'notificationText': 'Tap to return to the call',
        'notificationButtons': <fgt.NotificationButton>[],
      };
    }
  }

  /// Requests notification permissions where applicable
  Future<void> _requestNotificationPermissions() async {
    try {
      if (Platform.isAndroid) {
        // Android 13+ requires notification permissions
        final permission =
            await fgt.FlutterForegroundTask.requestNotificationPermission();
        if (permission != fgt.NotificationPermission.granted) {
          debugPrint(
            '[PreferredCallingUserView] Notification permission denied on Android: $permission',
          );
        }
      }

      // iOS notifications are handled differently
      // VoIP calls use CallKit which doesn't require foreground service notifications
      if (Platform.isIOS) {
        debugPrint(
          '[PreferredCallingUserView] iOS using CallKit and background tasks, not foreground service notifications',
        );

        // For iOS, we rely on:
        // 1. CallKit for call interface
        // 2. Background audio mode for call continuation
        // 3. VoIP push notifications
        // 4. Background processing for call state management
      }
    } catch (e) {
      debugPrint(
        '[PreferredCallingUserView] Error requesting notification permissions: $e',
      );
    }
  }

  /// Builds notification buttons based on platform capabilities
  List<fgt.NotificationButton> _buildNotificationButtons() {
    // Only add buttons for Android - iOS doesn't support notification buttons well
    if (Platform.isIOS || Platform.isAndroid) {
      return [];
    }

    return [
      const fgt.NotificationButton(
        id: 'end_call',
        text: 'End Call',
        textColor: Colors.red,
      ),
      const fgt.NotificationButton(
        id: 'return_to_call',
        text: 'Return',
        textColor: Colors.blue,
      ),
    ];
  }

  Future<void> _stopForegroundService() async {
    try {
      // Handle iOS background task cleanup
      if (Platform.isIOS) {
        await _stopIOSBackgroundTask();
        debugPrint(
          '[PreferredCallingUserView] iOS background task cleanup completed',
        );
        return;
      }

      // Android foreground service cleanup
      if (await fgt.FlutterForegroundTask.isRunningService) {
        await fgt.FlutterForegroundTask.stopService();

        // Verify service stopped successfully
        await Future<void>.delayed(const Duration(milliseconds: 500));
        final isStillRunning = await fgt.FlutterForegroundTask.isRunningService;

        if (isStillRunning) {
          debugPrint(
            '[PreferredCallingUserView] Warning: Android foreground service may not have stopped completely',
          );
        } else {
          debugPrint(
            '[PreferredCallingUserView] Foreground service stopped successfully',
          );
        }
      }
    } catch (e) {
      debugPrint(
        '[PreferredCallingUserView] Error stopping foreground service: $e',
      );

      // Track service stop failure
      try {
        if (mounted) {
          await context.read<EventTrackerService>().logEvent(
            description: 'Foreground service stop failed',
            schema: 'service_error',
            data: {
              'error': e.toString(),
              'call_id': call.id ?? 'unknown',
              'timestamp': DateTime.now().toIso8601String(),
            },
          );
        }
      } catch (analyticsError) {
        debugPrint(
          '[PreferredCallingUserView] Error logging service stop failure: $analyticsError',
        );
      }
    }
  }

  /// Ensures complete system cleanup when a call ends or fails, preventing ghost call indicators
  void _performSystemCleanup() {
    debugPrint(
      '[PreferredCallingUserView] Performing system-wide call cleanup',
    );

    // Cancel and reset all timers
    _connectionQualityTimer?.cancel();
    _keepAliveTimer?.cancel();

    // Force reset call state in both blocs
    if (dialerBloc != null) {
      dialerBloc!.add(const DialerEvent.forceEndCurrentCall());
    }

    // Reset timer and hide timer widget when stopped
    if (callTimerBloc != null) {
      callTimerBloc!.add(const CallTimerEvent.timerReset());
    }

    // Make sure foreground service is stopped
    _stopForegroundService();

    // Clear call status from OS status bar
    try {
      fgt.FlutterForegroundTask.sendDataToTask({
        'action': 'force_stop_service',
      });
    } catch (e) {
      debugPrint(
        '[PreferredCallingUserView] Error sending force stop signal: $e',
      );
    }

    // Additional logging for debugging
    _debugger.trackException(
      'System-wide cleanup performed',
      'Call page exited',
      StackTrace.current,
    );
  }

  /// Verifies that the call has actually ended and performs cleanup if needed.
  /// This method provides additional safety to ensure calls are properly terminated.
  void _verifyCallEndedAndCleanup() {
    if (_isDisposed) return;

    try {
      // Check if call session is still active after hangup
      if (call.session.isEstablished()) {
        debugPrint(
          '[PreferredCallingUserView] Call session still active after hangup, '
          'forcing termination',
        );

        // Force session termination as a last resort
        try {
          call.session.terminate();
        } catch (e) {
          debugPrint(
            '[PreferredCallingUserView] Error terminating session: $e',
          );
        }
      }

      // Ensure DialerBloc state is properly updated
      if (dialerBloc != null) {
        final currentStatus = dialerBloc!.state.status;
        if (currentStatus != DialerStatus.ended &&
            currentStatus != DialerStatus.failedToConnect) {
          debugPrint(
            '[PreferredCallingUserView] DialerBloc status not ended '
            '($currentStatus), forcing end status',
          );
          dialerBloc!.add(
            const DialerEvent.updatedDialerStatus(
              status: DialerStatus.ended,
            ),
          );
        }
      }

      // Ensure timers are stopped
      if (callTimerBloc != null) {
        final timerState = callTimerBloc!.state;
        if (timerState.isRunning || timerState.isPaused) {
          debugPrint(
            '[PreferredCallingUserView] Timer still running, forcing stop',
          );
          callTimerBloc!.add(const CallTimerEvent.timerStopped());
        }
      }

      // Log verification completion
      debugPrint(
        '[PreferredCallingUserView] Call end verification completed',
      );
    } catch (e) {
      debugPrint(
        '[PreferredCallingUserView] Error during call end verification: $e',
      );
    }
  }

  /// Tracks call termination events for debugging and analytics.
  /// This helps verify that the end call button is working correctly.
  void _trackCallTermination(String reason) {
    try {
      final terminationData = {
        'call_id': call.id ?? 'unknown',
        'termination_reason': reason,
        'call_duration': _callDuration.inSeconds,
        'was_connected': _isCallConnected,
        'remote_identity': remoteIdentity ?? 'unknown',
        'timestamp': DateTime.now().toIso8601String(),
      };

      debugPrint(
        '[PreferredCallingUserView] Call termination tracked: $terminationData',
      );

      // Track in analytics/crash reporting
      _debugger.trackException(
        'Call terminated: $reason',
        'Call termination tracking',
        StackTrace.current,
      );

      // Track user interaction for analytics
      _trackUserInteraction('call_terminated_$reason');
    } catch (e) {
      debugPrint(
        '[PreferredCallingUserView] Error tracking call termination: $e',
      );
    }
  }

  /// Handles returning to the call screen from notification.
  /// Ensures the app is brought to foreground and call screen is visible.
  ///
  /// IMPORTANT: This method ONLY returns to the existing call screen
  /// and does NOT initiate a new call. It safely refreshes the UI
  /// and ensures the user can see their ongoing call.
  void _handleReturnToCall() {
    try {
      debugPrint(
        '[PreferredCallingUserView] Return to call requested from notification',
      );

      // SAFETY CHECK: Ensure we're not disposed and have an active call
      if (!mounted || _isDisposed) {
        debugPrint(
          '[PreferredCallingUserView] Call screen not available for return',
        );
        return;
      }

      // SAFETY: Verify this is for an existing call only
      if (call.state == sip_ua.CallStateEnum.NONE ||
          call.state == sip_ua.CallStateEnum.ENDED) {
        debugPrint(
          '[PreferredCallingUserView] Call has ended, return action ignored',
        );
        return;
      }

      // Ensure the current route is the call screen
      _ensureCallScreenIsVisible();

      // Handle potential app backgrounding scenarios
      _handleAppToForeground();

      // If we're already on the call screen, just refresh the UI state
      if (mounted && !_isDisposed) {
        // Refresh call status and UI (NO NEW CALL INITIATION)
        setState(() {
          // Force UI refresh to ensure latest state is displayed
        });

        // Update the foreground service with current status
        if (_isCallConnected) {
          final connectedMessage = mounted
              ? DialerStatus.connected.getLocalizedMessage(context)
              : 'Connected';
          _sendStatusToForegroundService(connectedMessage);
        } else {
          final connectingMessage = mounted
              ? DialerStatus.connecting.getLocalizedMessage(context)
              : 'Connecting...';
          _sendStatusToForegroundService(connectingMessage);
        }

        debugPrint(
          '[PreferredCallingUserView] Successfully returned to call screen',
        );
      }

      // Track successful return for debugging (not marketing)
      _trackDeveloperEvent('return_to_call_successful');
    } catch (e) {
      debugPrint(
        '[PreferredCallingUserView] Error handling return to call: $e',
      );

      // Track failed return for debugging
      _trackDeveloperEvent('return_to_call_failed');

      // Attempt to refresh the call state as fallback
      if (mounted && !_isDisposed) {
        setState(() {
          // Force refresh even if there was an error
        });
      }
    }
  }

  /// Ensures the call screen is visible by checking current route
  /// and navigating back if needed.
  ///
  /// SAFETY: This method only navigates within existing routes
  /// and NEVER initiates new calls or creates new call screens.
  void _ensureCallScreenIsVisible() {
    try {
      if (!mounted || _isDisposed) return;

      // SAFETY: Double-check we have an active call before navigation
      if (call.state == sip_ua.CallStateEnum.NONE ||
          call.state == sip_ua.CallStateEnum.ENDED) {
        debugPrint(
          '[PreferredCallingUserView] Call not active, navigation skipped',
        );
        return;
      }

      // Check if the current route is this call screen
      final currentRoute = ModalRoute.of(context);
      if (currentRoute != null && currentRoute.isCurrent) {
        // We're already on the correct screen
        debugPrint(
          '[PreferredCallingUserView] Already on call screen',
        );
        return;
      }

      // If user navigated away, try to pop back to this screen
      if (context.mounted && Navigator.of(context).canPop()) {
        debugPrint(
          '[PreferredCallingUserView] Attempting to navigate back to call screen',
        );

        // SAFETY: Only pop back to existing call screen, don't push new routes
        Navigator.of(context).popUntil((route) {
          return route.settings.name == '/calling' ||
              route.settings.name == '/preferred_calling' ||
              !Navigator.of(context).canPop();
        });
      }
    } catch (e) {
      debugPrint(
        '[PreferredCallingUserView] Error ensuring call screen visibility: $e',
      );
    }
  }

  /// Additional enhancement: Handle the case where the app might be
  /// completely backgrounded and needs to be brought to foreground.
  /// This is primarily handled by the Flutter Foreground Task package,
  /// but we can add additional safety measures.
  void _handleAppToForeground() {
    try {
      // The Flutter Foreground Task package should handle bringing
      // the app to foreground automatically when notification buttons
      // are pressed. This method provides additional fallback logic.

      if (!mounted || _isDisposed) return;

      // Log that foreground request was received
      debugPrint(
        '[PreferredCallingUserView] App foreground request processed',
      );

      // If the app was completely backgrounded, ensure the widget
      // tree is properly rebuilt with current state
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && !_isDisposed) {
          setState(() {
            // Force a complete UI rebuild after coming from background
          });
        }
      });

      // Track foreground activity for debugging (not marketing)
      _trackDeveloperEvent('app_brought_to_foreground');
    } catch (e) {
      debugPrint(
        '[PreferredCallingUserView] Error handling app foreground: $e',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final canPopScope = context.select<DialerBloc, bool>(
      (bloc) =>
          bloc.state.isCallConnected ||
          bloc.state.isRinging ||
          bloc.state.isCallInitiating ||
          bloc.state.isCallInitial,
    );

    if (!_isServiceInitialized) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    // Wrap in comprehensive media error boundary for crash prevention
    return CallUIErrorBoundary(
      onMediaError: (FlutterErrorDetails errorDetails) {
        _debugger.trackException(
          'UI Error in CallScreen build',
          errorDetails.exception,
          errorDetails.stack,
        );

        // Additional custom handling for specific call scenarios
        MediaErrorHandlerService.reportMediaError(
          Exception('CallUI Widget Error: ${errorDetails.exception}'),
          operation: 'call_ui_widget_build',
          mediaType: 'ui_rendering',
          stackTrace: errorDetails.stack,
          additionalData: {
            'call_id': call.id,
            'remote_identity': remoteIdentity,
            'is_service_initialized': _isServiceInitialized,
            'is_call_connected': _isCallConnected,
          },
        );
      },
      child: MultiBlocListener(
        listeners: [
          BlocListener<DialerBloc, DialerState>(
            listener: (context, state) {
              debugPrint(
                '[PreferredCallingUserView] DialerBloc state changed: ${state.status}',
              );

              // IMPROVED CALL FILTERING: Only respond to state changes for OUR specific call
              // This prevents the UI from exiting when other calls fail
              // Handle case where state.call might be null during initialization
              if (state.call?.id != null &&
                  call.id != null &&
                  state.call!.id != call.id) {
                debugPrint(
                  '[PreferredCallingUserView] Ignoring state change for different call: ${state.call!.id} (our call: ${call.id})',
                );
                return;
              }

              // ADDITIONAL CHECK: If we have no state.call but we're getting ended/failed status,
              // and our widget.call ID doesn't match any recent activity, ignore it
              if (state.call == null &&
                  (state.status == DialerStatus.ended ||
                      state.status == DialerStatus.failedToConnect)) {
                debugPrint(
                  '[PreferredCallingUserView] Ignoring ended/failed status when no active call in bloc state (our call: ${call.id})',
                );
                return;
              }

              // Handle call state transitions
              switch (state.status) {
                case DialerStatus.connecting:
                  _callHasProgressed =
                      true; // Mark that call has started progressing
                  _updateCallStatus(state.status.getLocalizedMessage(context));
                  _sendStatusToForegroundService(
                    state.status.getLocalizedMessage(context),
                  );
                case DialerStatus.ringing:
                  _callHasProgressed =
                      true; // Mark that call has started progressing
                  _updateCallStatus(state.status.getLocalizedMessage(context));
                  _sendStatusToForegroundService(
                    state.status.getLocalizedMessage(context),
                  );
                case DialerStatus.connected:
                  _callHasProgressed =
                      true; // Mark that call has started progressing
                  if (!_isCallConnected) {
                    _handleCallConnected();
                  }
                case DialerStatus.hold:
                  if (mounted) {
                    setState(() => _hold = true);
                  }
                  _updateCallStatus(state.status.getLocalizedMessage(context));
                  _sendStatusToForegroundService(
                    state.status.getLocalizedMessage(context),
                  );
                case DialerStatus.ended:
                case DialerStatus.failedToConnect:
                  debugPrint(
                    '[PreferredCallingUserView] Call ended/failed detected - our call: ${call.id}, bloc call: ${state.call?.id}, connected state: $_isCallConnected',
                  );

                  // ENHANCED SAFETY CHECK: Only exit if this is actually about our call
                  // and we've progressed beyond initial state, or if we've been connected
                  // Also prevent immediate exits within the first 2 seconds
                  final timeSinceCreation =
                      DateTime.now().difference(_callScreenCreatedAt);
                  final hasWaitedMinimumTime = timeSinceCreation.inSeconds >= 2;

                  final shouldHandleTermination = _callHasProgressed &&
                      hasWaitedMinimumTime &&
                      ((state.call?.id == call.id) ||
                          (state.call == null && _isCallConnected) ||
                          (state.call != null && state.call!.id == call.id));

                  if (!shouldHandleTermination) {
                    debugPrint(
                      '[PreferredCallingUserView] Ignoring termination - progressed: $_callHasProgressed, waited: $hasWaitedMinimumTime (${timeSinceCreation.inSeconds}s), call match check failed',
                    );
                    return;
                  }

                  debugPrint(
                    '[PreferredCallingUserView] Processing termination for our call: ${call.id}, performing verification and handling in 500ms',
                  );

                  // Verify call is properly ended before proceeding
                  _verifyCallEndedAndCleanup();

                  // Add a small delay to ensure all state transitions are complete
                  Future.delayed(const Duration(milliseconds: 500), () {
                    if (mounted && !_isDisposed) {
                      debugPrint(
                        '[PreferredCallingUserView] Executing _handleCallEnd after delay',
                      );
                      _handleCallEnd();
                    } else {
                      debugPrint(
                        '[PreferredCallingUserView] Skipping _handleCallEnd - widget disposed or not mounted',
                      );
                    }
                  });
                case DialerStatus.error:
                  _updateCallStatus(state.status.getLocalizedMessage(context));
                  _sendStatusToForegroundService(
                    state.status.getLocalizedMessage(context),
                  );
                default:
                  // Handle other states as needed
                  break;
              }

              // Handle mute state changes
              if (state.isMuted != _audioMuted) {
                if (mounted) {
                  setState(() => _audioMuted = state.isMuted);
                }
                _updateCallStatus(
                  state.isMuted
                      ? state.muteStatus.getLocalizedMessage(context)
                      : state.status.getLocalizedMessage(context),
                );
              }

              // Handle hold state changes (if not already handled above)
              if (state.isOnHold != _hold &&
                  state.status != DialerStatus.hold) {
                if (mounted) {
                  setState(() => _hold = state.isOnHold);
                }
                if (state.isOnHold) {
                  _updateCallStatus(state.status.getLocalizedMessage(context));
                  _sendStatusToForegroundService(
                    state.status.getLocalizedMessage(context),
                  );
                } else {
                  _updateCallStatus(state.status.getLocalizedMessage(context));
                  _sendStatusToForegroundService(
                    state.status.getLocalizedMessage(context),
                  );
                }
              }

              // Update call duration display from DialerBloc
              // (backward compatibility)
              if (state.elapsedCallTimer != _callDuration) {
                setState(() {
                  _callDuration = state.elapsedCallTimer;
                });
              }
            },
          ),
          BlocListener<CallTimerBloc, CallTimerState>(
            listener: (context, state) {
              // Update call duration display from centralized CallTimerBloc
              if (state.elapsedTime != _callDuration) {
                setState(() {
                  _callDuration = state.elapsedTime;
                });
              }

              // Reset timer and hide timer widget when stopped
              if (!state.isRunning &&
                  !state.isPaused &&
                  _callDuration != Duration.zero) {
                setState(() {
                  _callDuration = Duration.zero;
                  _isCallConnected = false;
                });
              }

              // Handle timer errors
              if (state.lastError != null) {
                debugPrint(
                  '[PreferredCallingUserView] CallTimerBloc error: '
                  '{state.lastError}',
                );
                _showConnectionWarning('Timer synchronization issue');
              }
            },
          ),
        ],
        child: fgt.WithForegroundTask(
          child: PopScope(
            canPop: !canPopScope, // Prevent accidental back navigation

            // onPopInvokedWithResult: (didPop, result) {
            //   if (didPop) return;
            //   // Directly hang up without confirmation modal
            //   _handleHangup();
            // },
            child: BlocBuilder<DialerBloc, DialerState>(
              builder: (context, state) {
                if (_showNumPad) {
                  return DtmfNumPad(
                    onChanged: _handleDtmf,
                    onBackPressed: _handleKeyPad,
                  );
                }
                return Scaffold(
                  appBar: AppBar(
                    backgroundColor: FroggyColors.froggyBlack,
                    automaticallyImplyLeading: false,
                    systemOverlayStyle: SystemUiOverlayStyle.light,
                    forceMaterialTransparency: true,
                    // title: _buildCallStatus(),
                  ),
                  extendBodyBehindAppBar: true,
                  extendBody: true,
                  floatingActionButtonLocation:
                      FloatingActionButtonLocation.centerFloat,
                  floatingActionButton: SizedBox(
                    width: 250,
                    child: BuildCallingActionButtons(
                      onEndCall: _handleHangup,
                      onToggleMute: _muteAudio,
                      onKeyPad: _handleKeyPad,
                      onSpeaker: _toggleSpeaker,
                      isMuted: _audioMuted,
                      isSpeakerOn: _speakerOn,
                    ),
                  ),
                  body: Stack(
                    children: [
                      const AnimatedCallingBackground(), // Maestro: animated gradient background
                      Stack(
                        children: [
                          // // Main call content
                          // _buildCallContent(),

                          // // Connection quality indicator
                          // if (_isCallConnected)
                          //   Positioned(
                          //     top: 100,
                          //     right: 20,
                          //     child: _buildConnectionQualityIndicator(),
                          //   ),

                          if (state.countryCode != null)
                            _buildFlag(
                              context,
                              flagSize: 15,
                              countryCode: state.countryCode!,
                            ),

                          _buildText(
                            context,
                            top: 100,
                            left: 100,
                            fontSize: 20,
                            textDirection: TextDirection.ltr,
                            fontWeight: FontWeight.w600,
                            label: state.phoneNumber ?? '',
                          ),
                          _buildText(
                            context,
                            top: 130,
                            left: 100,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            label: state.isMuted
                                ? state.muteStatus.getLocalizedMessage(context)
                                : state.status.getLocalizedMessage(context),
                          ),

                          // Call timer
                          if (state.status.isConnected)
                            Positioned(
                              top: 160,
                              left: 0,
                              right: 0,
                              child: Center(
                                child: _buildCallTimer(),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                );
              },
            ), // Close BlocBuilder
          ), // Close PopScope
        ), // Close WithForegroundTask
      ), // Close BlocListener
    );
  }
}
