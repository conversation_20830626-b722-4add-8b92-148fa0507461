import 'dart:async';
import 'dart:io';

import 'package:constants/constants.dart';
import 'package:countries/countries.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_foreground_task/flutter_foreground_task.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:froggytalk/dialer/dialer.dart';
import 'package:sip_ua/sip_ua.dart'  as sip_ua;
import 'package:utils/utils.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

class OldCallingUserView extends StatefulHookWidget {
  const OldCallingUserView({
    required this.call,
    super.key,
    this.phoneNumber,
  });

  final sip_ua.Call call;

  final String? phoneNumber;

  static Route<Object?> route({required sip_ua.Call call}) {
    return MaterialPageRoute<Object?>(
      builder: (_) => OldCallingUserView(call: call),
    );
  }

  static String routeName = '/calling-user-page';

  @override
  State<OldCallingUserView> createState() => _CallingUserPageState();
}

class _CallingUserPageState extends State<OldCallingUserView>
    implements sip_ua.SipUaHelperListener {
  RTCVideoRenderer? _localRenderer = RTCVideoRenderer();
  RTCVideoRenderer? _remoteRenderer = RTCVideoRenderer();
  MediaStream? _localStream;
  // ignore: unused_field
  MediaStream? _remoteStream;

  bool _showNumPad = false;
  bool _audioMuted = false;
  bool _speakerOn = false;
  bool _hold = false;

  sip_ua.SIPUAHelper? get helper => _helper;

  bool get voiceOnly => call.voiceOnly && !call.remote_has_video;

  String? get remoteIdentity => call.remote_identity;

  String get direction => call.direction;

  sip_ua.Call get call => widget.call;

  DialerBloc? dialerBloc;
  AuthenticationBloc? authenticationBloc;
  sip_ua.SIPUAHelper? _helper;

  late Timer _keepAliveTimer;
  final Timer _creditCheckTimer = Timer(Duration.zero, () {});

  @override
  void initState() {
    super.initState();
    dialerBloc = BlocProvider.of<DialerBloc>(context);
    authenticationBloc = BlocProvider.of<AuthenticationBloc>(context);

    _helper = context.read<sip_ua.SIPUAHelper>();
    _helper?.addSipUaHelperListener(this);
    _initRenderers();
    _initWakePlus();
    _startKeepAlive();
    _checkCreditEveryMin();
    _initForegroundService();
    // _startForegroundService();
  }

  @override
  void deactivate() {
    super.deactivate();
    _helper?.removeSipUaHelperListener(this);
    _disposeRenderers();
    _destroyWakePlus();
    _keepAliveTimer.cancel();
    _creditCheckTimer.cancel();
    _stopForegroundService();
  }

  void _checkCreditEveryMin() {
    // const creditCheckInterval = Duration(minutes: 1, seconds: 25);
    // _creditCheckTimer = Timer.periodic(creditCheckInterval, (timer) {
    //   _refreshProfile();
    // });
  }

  void _refreshProfile() {
    authenticationBloc?.add(const AuthenticationEvent.refreshProfile());
  }

  void _startKeepAlive() {
    // Configure the interval for the keep-alive (e.g., 2 minutes)
    const keepAliveInterval = Duration(seconds: 30);

    // Send an `OPTIONS` message or session-refresh `UPDATE`
    _keepAliveTimer = Timer.periodic(keepAliveInterval, (timer) {
      final currentSession = call.session;
      if (currentSession.isEstablished()) {
        // Send UPDATE to refresh the session
        currentSession.sendRequest(sip_ua.SipMethod.UPDATE);
        // print('Sent UPDATE request for session refresh.');
      } else {
        currentSession.sendRequest(sip_ua.SipMethod.OPTIONS);
        FroggyLogger.debug(
          'No active session to refresh: ${currentSession.id}',
        );
        // print('No active session to refresh.');
      }
    });
  }

  Future<void> _initRenderers() async {
    if (_localRenderer != null) {
      await _localRenderer!.initialize();
    }

    if (_remoteRenderer != null) {
      await _remoteRenderer!.initialize();
    }
  }

  void _disposeRenderers() {
    if (_localRenderer != null) {
      _localRenderer!.dispose();
      _localRenderer = null;
    }
    if (_remoteRenderer != null) {
      _remoteRenderer!.dispose();
      _remoteRenderer = null;
    }
  }

  @override
  void callStateChanged(sip_ua.Call call, sip_ua.CallState callState) {
    if (callState.state == sip_ua.CallStateEnum.HOLD ||
        callState.state == sip_ua.CallStateEnum.UNHOLD) {
      _hold = callState.state == sip_ua.CallStateEnum.HOLD;
      setState(() {});
      return;
    }

    if (callState.state == sip_ua.CallStateEnum.MUTED) {
      if (callState.audio!) _audioMuted = true;
      if (callState.video!) setState(() {});
      return;
    }

    if (callState.state == sip_ua.CallStateEnum.UNMUTED) {
      if (callState.audio!) _audioMuted = false;
      if (callState.video!) setState(() {});
      return;
    }

    if (callState.state != sip_ua.CallStateEnum.STREAM) {}

    switch (callState.state) {
      case sip_ua.CallStateEnum.STREAM:
        _handleStreams(callState);
      case sip_ua.CallStateEnum.ENDED:
        dialerBloc?.add(
          const DialerEvent.updatedDialerStatus(
            status: DialerStatus.ended,
          ),
        );
      case sip_ua.CallStateEnum.FAILED:
        dialerBloc?.add(
          const DialerEvent.updatedDialerStatus(
            status: DialerStatus.failedToConnect,
          ),
        );
      case sip_ua.CallStateEnum.UNMUTED:
        dialerBloc
          ?..add(
            const DialerEvent.muteOff(),
          )
          ..add(
            const DialerEvent.updatedDialerStatus(
              status: DialerStatus.connected,
            ),
          );
      case sip_ua.CallStateEnum.MUTED:
        dialerBloc?.add(
          const DialerEvent.muteOn(),
        );
      case sip_ua.CallStateEnum.CONNECTING:
        dialerBloc?.add(
          const DialerEvent.updatedDialerStatus(
            status: DialerStatus.connecting,
          ),
        );
      case sip_ua.CallStateEnum.PROGRESS:
        dialerBloc?.add(
          const DialerEvent.updatedDialerStatus(
            status: DialerStatus.ringing,
          ),
        );
      case sip_ua.CallStateEnum.ACCEPTED:
      case sip_ua.CallStateEnum.CONFIRMED:
        // _countupTimer.restart();
        dialerBloc
          ?..add(
            const DialerEvent.updatedDialerStatus(
              status: DialerStatus.connected,
            ),
          )
          ..add(const DialerEvent.timerStarted());
      case sip_ua.CallStateEnum.HOLD:
      case sip_ua.CallStateEnum.UNHOLD:
      case sip_ua.CallStateEnum.NONE:
        break;
      case sip_ua.CallStateEnum.CALL_INITIATION:
        dialerBloc?.add(
          const DialerEvent.updatedDialerStatus(
            status: DialerStatus.initiating,
          ),
        );
        _handleStreams(callState);
      case sip_ua.CallStateEnum.REFER:
        break;
    }
  }

  @override
  void transportStateChanged(sip_ua.TransportState state) {}

  @override
  void registrationStateChanged(sip_ua.RegistrationState state) {}

  void _cleanUp() {
    if (_localStream == null) return;
    _localStream?.getTracks().forEach((track) {
      track.stop();
    });
    _localStream!.dispose();
    _localStream = null;
  }

  void _routeBack() {
    try {
      if (!mounted) return;
      WidgetsBinding.instance
          .addPostFrameCallback((_) => Navigator.of(context).maybePop());

      // _countupTimer.stop();
      // Navigator.of(context).maybePop();
      // setTimeout(
      //   computation: () {
      //   },
      //   seconds: 2,
      // );
    } on Exception catch (_) {}
    _cleanUp();
  }

  Future<void> _handleStreams(sip_ua.CallState event) async {
    final stream = event.stream;
    if (event.originator == 'local') {
      if (_localRenderer != null) {
        await _localRenderer!.initialize();

        _localRenderer!.srcObject = stream;
      }

      if (!kIsWeb && !WebRTC.platformIsDesktop) {
        event.stream?.getAudioTracks().first.enableSpeakerphone(false);

        // event.stream!.getAudioTracks().forEach((track) {
        //   track.enableSpeakerphone(false);
        // });

        // _toggleSpeaker(false);
      }

      setState(() {
        _localStream = event.stream;
        _speakerOn = false;
      });
    }

    if (event.originator == 'remote') {
      if (_remoteRenderer != null) {
        await _remoteRenderer!.initialize();
        _remoteRenderer!.srcObject = stream;
      }
      _remoteStream = stream;
    }
  }

  void _handleHangup() {
    try {
      call.hangup({'status_code': 603});
      _refreshProfile();
      // _countupTimer.stop();
    } catch (e) {
      FroggyLogger.error('Error hanging up: $e');
    }
  }

  void _muteAudio() {
    if (_audioMuted) {
      call.unmute(true, false);
    } else {
      call.mute(true, false);
    }
  }

  // ignore: unused_element
  void _handleHold() {
    if (_hold) {
      call.unhold();
    } else {
      call.hold();
    }
  }

  void _handleDtmf(String tone) {
    call.sendDTMF(tone);
  }

  void _handleKeyPad() {
    setState(() {
      _showNumPad = !_showNumPad;
    });
  }

  void _toggleSpeaker(bool value) {
    // setState(() {
    //   _speakerOn = !_speakerOn;
    // });

    if (_localStream != null) {
      setState(() {
        _speakerOn = !_speakerOn;
      });

      if (!kIsWeb) {
        _localStream!.getAudioTracks().forEach((track) {
          track.enableSpeakerphone(_speakerOn);
        });

        // _localStream!.getAudioTracks()[0].enableSpeakerphone(_speakerOn);
      }
    }
  }

  @override
  void onNewReinvite(sip_ua.ReInvite event) {
    if (event.accept == null) return;
    if (event.reject == null) return;
  }

  @override
  void onNewMessage(sip_ua.SIPMessageRequest msg) {
    // NO OP
  }

  @override
  void onNewNotify(sip_ua.Notify ntf) {
    // NO OP
  }

  @override
  Widget build(BuildContext context) {
    final canPopScope = context.select<DialerBloc, bool>(
      (bloc) =>
          bloc.state.isCallConnected ||
          bloc.state.isRinging ||
          bloc.state.isCallInitiating ||
          bloc.state.isCallInitial,
    );

    return BlocListener<DialerBloc, DialerState>(
      listener: (context, state) {
        if (state.status.isFailedToConnect || state.status.isEnded) {
          _routeBack();
        }
      },
      child: PopScope(
        canPop: !canPopScope,
        child: BlocConsumer<DialerBloc, DialerState>(
          listener: (context, state) {
            // if (state.status == DialerStatus.ended) {
            //   onCallEnded();
            // }
          },
          builder: (context, state) {
            if (_showNumPad) {
              return DtmfNumPad(
                onChanged: _handleDtmf,
                onBackPressed: _handleKeyPad,
              );
            }

            return Scaffold(
              appBar: AppBar(
                backgroundColor: FroggyColors.froggyBlack,
                automaticallyImplyLeading: false,
                systemOverlayStyle: SystemUiOverlayStyle.light,
                forceMaterialTransparency: true,
              ),
              extendBodyBehindAppBar: true,
              extendBody: true,
              floatingActionButtonLocation:
                  FloatingActionButtonLocation.centerFloat,
              floatingActionButton: SizedBox(
                width: 250,
                child: BuildCallingActionButtons(
                  onEndCall: _handleHangup,
                  onToggleMute: _muteAudio,
                  onKeyPad: _handleKeyPad,
                  onSpeaker: _toggleSpeaker,
                  isMuted: _audioMuted,
                  isSpeakerOn: _speakerOn,
                ),
              ),
              body: Container(
                height: MediaQuery.of(context).size.height,
                width: MediaQuery.of(context).size.width,
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage('assets/images/calling_bg_gradient.png'),
                    fit: BoxFit.contain,
                  ),
                  color: FroggyColors.froggyBlack,
                ),
                child: Stack(
                  children: [
                    Positioned(
                      bottom: 150,
                      height: 350,
                      child: Image.asset(
                        'assets/images/calling_bg_item4.png',
                      ),
                    ),
                    const SizedBox(),
                    if (state.countryCode != null)
                      _buildFlag(
                        context,
                        flagSize: 15,
                        countryCode: state.countryCode!,
                      ),
                    _buildText(
                      context,
                      top: 100,
                      left: 100,
                      fontSize: 20,
                      textDirection: TextDirection.ltr,
                      fontWeight: FontWeight.w600,
                      label: state.phoneNumber ?? '',
                    ),
                    _buildText(
                      context,
                      top: 130,
                      left: 100,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      label: state.isMuted
                          ? state.muteStatus.getLocalizedMessage(context)
                          : state.status.getLocalizedMessage(context),
                    ),
                    if (state.status.isConnected)
                      _buildText(
                        context,
                        top: 215,
                        left: 100,
                        fontSize: 40,
                        fontWeight: FontWeight.w600,
                        label: state.formattedCallTimer,
                      ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Positioned _buildText(
    BuildContext context, {
    required double top,
    required double left,
    required double fontSize,
    required FontWeight fontWeight,
    required String label,
    TextDirection? textDirection,
  }) {
    return Positioned(
      top: top,
      left: MediaQuery.of(context).size.width / 2 -
          left, // Adjust 15 based on flag width
      child: SizedBox.fromSize(
        size: const Size(200, 50),
        child: Text(
          label,
          textDirection: textDirection,
          style: TextStyle(
            color: FroggyColors.white,
            fontSize: fontSize,
            fontWeight: fontWeight,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Positioned _buildFlag(
    BuildContext context, {
    double flagSize = 20,
    String countryCode = 'ng',
  }) {
    return Positioned(
      top: 80,
      left: MediaQuery.of(context).size.width / 2 -
          flagSize, // Adjust 15 based on flag width
      child: FroggyCountries.showCountryFlagByCountryCode(
        countryCode: countryCode,
        width: flagSize,
        height: flagSize,
        margin: 0,
      ),
    );
  }

  Future<void> _initWakePlus() async {
    await WakelockPlus.enable();
  }

  Future<void> _destroyWakePlus() async {
    await WakelockPlus.disable();
  }

  void _initForegroundService() {
    // Add a callback to receive data sent from the TaskHandler.
    // FlutterForegroundTask.addTaskDataCallback(_onReceiveTaskData);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Request permissions and initialize the service.
      _requestPermissions();
      _initService();
      // _startForegroundService();
    });
  }

  void _initService() {
    try {
      FlutterForegroundTask.init(
        androidNotificationOptions: AndroidNotificationOptions(
          channelId: 'foreground_service',
          channelName: 'FroggyTalk Phone Call In Progress',
          channelDescription:
              'This notification appears when the user is in a phone call.',
          onlyAlertOnce: true,
        ),
        iosNotificationOptions: const IOSNotificationOptions(
          showNotification: false,
        ),
        foregroundTaskOptions: ForegroundTaskOptions(
          eventAction: ForegroundTaskEventAction.repeat(5000),
          autoRunOnBoot: true,
          autoRunOnMyPackageReplaced: true,
          allowWifiLock: true,
        ),
      );
    } catch (e) {
      FroggyLogger.error('Failed to initialize foreground service: $e');
    }
  }

  Future<void> _requestPermissions() async {
    // Android 13+, you need to allow notification permission
    // to display foreground service notification.
    //
    // iOS: If you need notification, ask for permission.
    final notificationPermission =
        await FlutterForegroundTask.checkNotificationPermission();
    if (notificationPermission != NotificationPermission.granted) {
      await FlutterForegroundTask.requestNotificationPermission();
    }

    if (Platform.isAndroid) {
      // // Android 12+, there are restrictions on starting a foreground service.
      // //
      // // To restart the service on device reboot or unexpected problem, you need to allow below permission.
      // if (!await FlutterForegroundTask.isIgnoringBatteryOptimizations) {
      //   // This function requires `android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` permission.
      //   await FlutterForegroundTask.requestIgnoreBatteryOptimization();
      // }

      // // Use this utility only if you provide services that require long-term survival,
      // // such as exact alarm service, healthcare service, or Bluetooth communication.
      // //
      // // This utility requires the "android.permission.SCHEDULE_EXACT_ALARM" permission.
      // // Using this permission may make app distribution difficult due to Google policy.
      // if (!await FlutterForegroundTask.canScheduleExactAlarms) {
      //   // When you call this function, will be gone to the settings page.
      //   // So you need to explain to the user why set it.
      //   await FlutterForegroundTask.openAlarmsAndRemindersSettings();
      // }

      // await FlutterForegroundTask
    }
  }

  // Future<void> _startForegroundService() async {
  //   try {
  //     if (await FlutterForegroundTask.isRunningService) {
  //       await FlutterForegroundTask.restartService();
  //     } else {
  //       await FlutterForegroundTask.startService(
  //         serviceId: 256,
  //         // notificationTitle: 'Foreground Service is running',
  //         notificationTitle: 'FroggyTalk Phone Call In Progress',
  //         notificationText: 'Tap to return to the app',
  //         // notificationIcon: null,
  //         // notificationButtons: [
  //         //   const NotificationButton(id: 'btn_hello', text: 'hello'),
  //         // ],
  //         // notificationInitialRoute: '/',
  //         // callback: startCallback,
  //       );
  //     }
  //   } on Exception catch (e) {
  //     FroggyLogger.error('Failed to start foreground service: $e');
  //   }
  // }

  Future<void> _stopForegroundService() async {
    try {
      await FlutterForegroundTask.stopService();
    } on Exception catch (e) {
      FroggyLogger.error('Failed to stop foreground service: $e');
    }
  }
}
