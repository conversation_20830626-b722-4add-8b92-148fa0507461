import 'package:common/common.dart';
import 'package:constants/constants.dart';
import 'package:countries/countries.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/app/data/services/event_tracker.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:froggytalk/dialer/dialer.dart';
import 'package:sip_ua/sip_ua.dart' as sip_ua;
import 'package:utils/utils.dart';

class DialerPage extends StatefulHookWidget {
  const DialerPage({super.key});

  static Route<Object?> route() {
    return MaterialPageRoute<Object?>(
      builder: (_) => const DialerPage(),
    );
  }

  static String routeName = '/dialer';

  @override
  State<DialerPage> createState() => _DialerPageState();
}

class _DialerPageState extends State<DialerPage>
    implements sip_ua.SipUaHelperListener {
  DialerBloc? dialerBloc;
  KeypadBloc? keypadBloc;
  RecentCallsBloc? recentCallsBloc;
  sip_ua.SIPUAHelper? _helper;

  @override
  void initState() {
    super.initState();
    dialerBloc = context.read<DialerBloc>();
    keypadBloc = context.read<KeypadBloc>();
    recentCallsBloc = context.read<RecentCallsBloc>();
    _helper = context.read<sip_ua.SIPUAHelper>();
    _helper?.addSipUaHelperListener(this);

    // dialerBloc?.add(
    //   const DialerEvent.reloadAsterisk(),
    // );

    // _helper!.unregister();
    if(!_helper!.registered) {
    _helper!.register();
    }
  }

  @override
  void deactivate() {
    super.deactivate();
    _helper?.removeSipUaHelperListener(this);
  }

  @override
  void callStateChanged(sip_ua.Call call, sip_ua.CallState state) {}

  @override
  void onNewMessage(sip_ua.SIPMessageRequest msg) {}

  @override
  void onNewNotify(sip_ua.Notify ntf) {}

  @override
  void onNewReinvite(sip_ua.ReInvite event) {}

  @override
  void registrationStateChanged(sip_ua.RegistrationState state) {
    final registrationState = state.state;
    if (registrationState != null) {
      // if (registrationState == RegistrationStateEnum.REGISTRATION_FAILED ||
      //     registrationState == RegistrationStateEnum.UNREGISTERED) {
      //   // if (authUser != null) {
      //   dialerBloc?.add(
      //     const DialerEvent.reloadAsterisk(),
      //   );
      //   // }
      //   // dialerBloc?.add(
      //   //   DialerEvent.updatedRegistrationStatus(status: registrationState),
      //   // );
      // }

      dialerBloc?.add(
        DialerEvent.updatedRegistrationStatus(status: registrationState),
      );
    }
  }

  @override
  void transportStateChanged(sip_ua.TransportState state) {}

  @override
  Widget build(BuildContext context) {
    // final l10n = useLocale();
    final keypadBloc = context.read<KeypadBloc>();
    final mediaHeight = MediaQuery.sizeOf(context).height;

    return Center(
      child: Column(
        children: [
          _BuildDialerInputWidget(),
          const _BuildDialerErrorWidget(),
          SizedBox.fromSize(
            size: const Size.fromHeight(30),
            child: BlocSelector<KeypadBloc, KeypadState, CountryModel?>(
              selector: (state) => state.country,
              builder: (context, country) {
                if (country == null) {
                  return const SizedBox.shrink();
                }

                return FroggyPhoneNumberWithFlag(
                  flagPath: 'flags/${country.code ?? 'nl'}.svg',
                  phoneNumber: country.code != null
                      ? '(${country.dialingCode}) ${country.name}'
                      : '(+31) Netherlands',
                );
              },
            ),
          ),
          // Visibility(
          //   visible: showDetails.value,
          //   replacement: const SizedBox.square(
          //     dimension: 60,
          //     // height: 75,
          //   ),
          //   child: Container(
          //     margin: const EdgeInsets.symmetric(horizontal: 25),
          //     child: QuickContactOptions(
          //       viewMoreLength: 5,
          //       displayedContact: const ContactModel(
          //         name: 'John Doe',
          //         prefix: '+1',
          //         phone: '1234567890',
          //       ),
          //       onViewMorePressed: onViewMorePressed,
          //     ),
          //   ),
          // ),
          BlocSelector<KeypadBloc, KeypadState, DialerPadInput?>(
            selector: (keyState) => keyState.phoneNumber,
            builder: (context, phoneNumber) => Flexible(
              flex: 8,
              child: FractionallySizedBox(
                widthFactor: mediaHeight >= 700 ? 0.8 : 0.6,
                child: NumPad(
                  onNumpadPressed: (number) async {
                    keypadBloc
                          ..add(
                            KeypadEvent.phoneNumberChanged(
                              value: number,
                              // makeDTMFSound: true,
                            ),
                          )
                          ..add(const KeypadEvent.searchedByDialingCode())
                        // ..add(const KeypadEvent.checkCallRates())
                        // ..add(const KeypadEvent.searchedContact())
                        ;
                  },
                  hideActionButtons: false,
                  // showDeleteButton: showDetails.value,
                ),
              ),
            ),
          ),
          FroggySpacer.y8(),
        ],
      ),
    );
  }
}

class _BuildDialerInputWidget extends HookWidget {
  @override
  Widget build(BuildContext context) {
    final keypadBloc = context.read<KeypadBloc>();

    // Selects the current phone number
    // and cursor position from the KeypadBloc state.
    final phoneNumber =
        context.select((KeypadBloc bloc) => bloc.state.phoneNumber.value);
    final cursorPosition =
        context.select((KeypadBloc bloc) => bloc.state.cursorPosition);

    // Initialize controllers and focus node using flutter_hooks.
    final scrollController = useScrollController();
    final textController = useTextEditingController(text: phoneNumber);
    final focusNode = useFocusNode();

    // ignore: unused_local_variable
    final onPaste = useCallback(
      () async {
        final data = await Clipboard.getData('text/plain');
        if (data != null && data.text != null) {
          // textController.text += data.text!;
          keypadBloc.add(
            KeypadEvent.setPhoneNumber(value: data.text),
          );
        }
      },
      [textController.text],
    );

    // Effect to update the TextField
    // and handle scrolling when phoneNumber changes.
    useEffect(
      () {
        // Update the TextField's text and move the cursor to the end.
        textController
          ..text = phoneNumber
          ..selection = TextSelection.fromPosition(
            TextPosition(
              offset: cursorPosition.clamp(0, textController.text.length),
            ),
            // textController.selection.base,
          );

        // Animate scrolling to the end if phone number exceeds 10 characters.
        if (phoneNumber.length > 10) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (scrollController.hasClients) {
              scrollController.animateTo(
                scrollController.position.maxScrollExtent,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeOut,
              );
            }
          });
        }

        void listener() {
          final position = textController.selection.baseOffset;

          keypadBloc.add(
            KeypadEvent.updateCursorPosition(position),
          );
        }

        textController.addListener(listener);

        context.read<EventTrackerService>().logEvent(
              schema: 'dialer_page',
              description: 'User is on dialing page',
            );

        return () => textController.removeListener(listener);
      },
      [phoneNumber, scrollController, textController],
    );

    return SizedBox(
      width: 350,
      child: InkWell(
        onLongPress: onPaste,
        onDoubleTap: onPaste,
        onTap: () => hideKeyboard(context),
        child: IgnorePointer(
          child: TextField(
            controller: textController,
            focusNode: focusNode,
            scrollController: scrollController,
            textDirection: TextDirection.ltr,
            style: const TextStyle(
              fontSize: 35,
            ),
            decoration: const InputDecoration(
              contentPadding: EdgeInsets.symmetric(
                horizontal: 2,
                vertical: 10,
              ),
            ),
            enabled: true,
            readOnly: true,
            keyboardType: TextInputType.none,
            enableInteractiveSelection: true,
            textAlign: TextAlign.center,
            scrollPhysics: const BouncingScrollPhysics(),
            textAlignVertical: TextAlignVertical.center,
            scrollPadding: EdgeInsets.zero,
          ),
        ),
      ),
    );
  }
}

class _BuildDialerErrorWidget extends StatelessWidget {
  const _BuildDialerErrorWidget();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<KeypadBloc, KeypadState>(
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: SizedBox.fromSize(
            size: const Size.fromHeight(15),
            child: BlocSelector<KeypadBloc, KeypadState, String?>(
              selector: (state) {
                return state.phoneNumber.error?.getLocalizedMessage(context);
              },
              builder: (context, errorMessage) {
                return Text(
                  errorMessage ?? '',
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    color: FroggyColors.error,
                    fontSize: 12,
                  ),
                  softWrap: true,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                );
              },
            ),
          ),
        );
      },
    );
  }
}
