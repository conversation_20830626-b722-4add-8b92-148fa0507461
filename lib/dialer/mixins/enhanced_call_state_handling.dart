// Enhanced call state handling with crash prevention
// This mixin provides robust call state management that prevents
// crashes when the receiver drops the call

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:froggytalk/dialer/utils/call_termination_debugger.dart';
import 'package:sip_ua/sip_ua.dart';
import 'package:utils/utils.dart';

/// Mixin that provides enhanced call state handling with crash
/// prevention
/// Use this mixin in your calling screen to prevent crashes
/// during call termination.
mixin EnhancedCallStateHandling<T extends StatefulWidget> on State<T> {
  /// Debug tracker for monitoring call termination issues
  final CallTerminationDebugger _debugger = CallTerminationDebugger();

  /// Flag to prevent duplicate cleanup calls
  bool _isCleanedUp = false;

  /// Flag to track if we're in a critical termination phase
  bool _inTerminationPhase = false;

  /// Timer for delayed cleanup to prevent race conditions
  Timer? _cleanupTimer;

  /// Enhanced call state change handler with crash prevention
  void handleCallStateChangeSafely(Call call, CallState callState) {
    try {
      // Track all state changes for debugging
      _debugger.trackCallStateChange(
        callState.state.toString(),
        callState.originator,
        callState.cause?.cause,
      );

      // Handle specific states with enhanced error protection
      switch (callState.state) {
        case CallStateEnum.CONNECTING:
          _handleConnecting(callState);
        case CallStateEnum.PROGRESS:
          _handleProgress(callState);
        case CallStateEnum.ACCEPTED:
        case CallStateEnum.CONFIRMED:
          _handleCallConnected(callState);
        case CallStateEnum.ENDED:
          _handleCallEndedSafely(callState);
        case CallStateEnum.FAILED:
          _handleCallFailedSafely(callState);
        case CallStateEnum.HOLD:
        case CallStateEnum.UNHOLD:
          _handleHoldStateChange(callState);
        case CallStateEnum.MUTED:
        case CallStateEnum.UNMUTED:
          _handleMuteStateChange(callState);
        case CallStateEnum.STREAM:
          _handleStreamSafely(callState);
        // ignore: no_default_cases
        default:
          FroggyLogger.debug(
            '[CallStateHandler] Unhandled state: ${callState.state}',
          );
      }
    } catch (e, stackTrace) {
      _debugger.trackException(
        '[EnhancedCallStateHandling] handleCallStateChange',
        e,
        stackTrace,
      );
      _handleStateChangeError(e, stackTrace, callState);
    }
  }

  /// Handle call connection safely
  void _handleConnecting(CallState callState) {
    if (!mounted || _isCleanedUp) return;

    try {
      onCallConnecting(callState);
    } catch (e, stackTrace) {
      _debugger.trackException(
        '[EnhancedCallStateHandling] handleConnecting',
        e,
        stackTrace,
      );
    }
  }

  /// Handle call progress safely
  void _handleProgress(CallState callState) {
    if (!mounted || _isCleanedUp) return;

    try {
      onCallProgress(callState);
    } catch (e, stackTrace) {
      _debugger.trackException(
        '[EnhancedCallStateHandling] handleProgress',
        e,
        stackTrace,
      );
    }
  }

  /// Handle call connected state safely
  void _handleCallConnected(CallState callState) {
    if (!mounted || _isCleanedUp) return;

    try {
      onCallConnected(callState);
    } catch (e, stackTrace) {
      _debugger.trackException(
        '[EnhancedCallStateHandling] handleCallConnected',
        e,
        stackTrace,
      );
    }
  }

  /// Handle call ended with enhanced crash prevention
  void _handleCallEndedSafely(CallState callState) {
    if (_inTerminationPhase || _isCleanedUp) {
      FroggyLogger.debug(
        '[CallStateHandler] Already in termination phase, '
        'ignoring duplicate ENDED',
      );
      return;
    }

    _inTerminationPhase = true;

    try {
      FroggyLogger.info(
        '[CallStateHandler] Call ended - originator:'
        ' ${callState.originator}',
      );

      // Check if this is a remote termination (receiver dropped call)
      if (callState.originator == 'remote') {
        FroggyLogger.warning(
          '[CallStateHandler] Remote termination detected -'
          ' potential crash scenario',
        );
        _handleRemoteTermination(callState);
      } else {
        _handleLocalTermination(callState);
      }
    } catch (e, stackTrace) {
      _debugger.trackException(
        '[EnhancedCallStateHandling] handleCallEnded',
        e,
        stackTrace,
      );
      _forceCleanupAndExit();
    }
  }

  /// Handle call failed with enhanced crash prevention
  void _handleCallFailedSafely(CallState callState) {
    if (_inTerminationPhase || _isCleanedUp) {
      FroggyLogger.debug(
        '[CallStateHandler] Already in termination phase, '
        'ignoring duplicate FAILED',
      );
      return;
    }

    _inTerminationPhase = true;

    try {
      FroggyLogger.warning(
        '[CallStateHandler] Call failed - originator: '
        '${callState.originator}, cause: ${callState.cause?.cause}',
      );

      // Failed calls need special handling
      _handleCallFailure(callState);
    } catch (e, stackTrace) {
      _debugger.trackException(
        '[EnhancedCallStateHandling] handleCallFailed',
        e,
        stackTrace,
      );
      _forceCleanupAndExit();
    }
  }

  /// Handle remote termination (when receiver drops call) with extra
  /// caution
  void _handleRemoteTermination(CallState callState) {
    FroggyLogger.info(
        '[CallStateHandler] Handling remote termination carefully',);

    // Schedule cleanup with delay to prevent race conditions
    _cleanupTimer = Timer(const Duration(milliseconds: 250), () {
      if (mounted && !_isCleanedUp) {
        _performSafeCleanup();
        onCallEndedByReceiver(callState);
      }
    });
  }

  /// Handle local termination (when user ends call)
  void _handleLocalTermination(CallState callState) {
    FroggyLogger.info('[CallStateHandler] Handling local termination');

    // Local termination can be handled immediately
    if (mounted && !_isCleanedUp) {
      _performSafeCleanup();
      onCallEndedByUser(callState);
    }
  }

  /// Handle call failure scenarios
  void _handleCallFailure(CallState callState) {
    FroggyLogger.warning('[CallStateHandler] Handling call failure');

    // Failed calls need immediate cleanup
    if (mounted && !_isCleanedUp) {
      _performSafeCleanup();
      onCallFailed(callState);
    }
  }

  /// Handle hold state changes safely
  void _handleHoldStateChange(CallState callState) {
    if (!mounted || _isCleanedUp || _inTerminationPhase) return;

    try {
      onHoldStateChanged(callState);
    } catch (e, stackTrace) {
      _debugger.trackException(
        '[EnhancedCallStateHandling] handleHoldStateChange',
        e,
        stackTrace,
      );
    }
  }

  /// Handle mute state changes safely
  void _handleMuteStateChange(CallState callState) {
    if (!mounted || _isCleanedUp || _inTerminationPhase) return;

    try {
      onMuteStateChanged(callState);
    } catch (e, stackTrace) {
      _debugger.trackException(
        '[EnhancedCallStateHandling] handleMuteStateChange',
        e,
        stackTrace,
      );
    }
  }

  /// Handle stream events safely
  void _handleStreamSafely(CallState callState) {
    if (!mounted || _isCleanedUp || _inTerminationPhase) return;

    try {
      onStreamReceived(callState);
    } catch (e, stackTrace) {
      _debugger.trackException('handleStream', e, stackTrace);
    }
  }

  /// Handle errors in state change processing
  void _handleStateChangeError(
      dynamic error, StackTrace stackTrace, CallState callState,) {
    FroggyLogger.error(
        '[CallStateHandler] Error processing state change: $error',);

    // If we're already in termination and get an error, force cleanup
    if (_inTerminationPhase) {
      _forceCleanupAndExit();
    } else {
      // For other errors, try to continue gracefully
      FroggyLogger.warning(
        '[CallStateHandler] Continuing despite error in state:'
        ' ${callState.state}',
      );
    }
  }

  /// Perform safe cleanup to prevent crashes
  void _performSafeCleanup() {
    if (_isCleanedUp) return;

    _isCleanedUp = true;

    try {
      // Cancel any pending timers
      _cleanupTimer?.cancel();
      _cleanupTimer = null;

      // Perform custom cleanup
      performCallCleanup();
    } catch (e, stackTrace) {
      _debugger.trackException(
        '[EnhancedCallStateHandling] performSafeCleanup',
        e,
        stackTrace,
      );
      FroggyLogger.error('[CallStateHandler] Error during cleanup: $e');
    }
  }

  /// Force cleanup and exit in case of critical errors
  void _forceCleanupAndExit() {
    FroggyLogger.warning('[CallStateHandler] Forcing cleanup and exit');

    try {
      _performSafeCleanup();

      // Navigate away immediately
      if (mounted && context.mounted) {
        Navigator.of(context).maybePop().catchError((dynamic e) {
          FroggyLogger.error(
              '[CallStateHandler] Error during forced navigation: $e',);
          return false;
        });
      }
    } catch (e) {
      FroggyLogger.error(
        '[CallStateHandler] Error during force cleanup: $e',
      );
    }
  }

  /// Initialize enhanced call state handling
  void initializeEnhancedCallHandling(String phoneNumber) {
    _debugger.startCallSession(phoneNumber);
    _isCleanedUp = false;
    _inTerminationPhase = false;
  }

  /// Cleanup when widget is disposed
  @override
  void dispose() {
    _performSafeCleanup();
    super.dispose();
  }

  // Abstract methods that implementing classes must provide
  void onCallConnecting(CallState callState) {}
  void onCallProgress(CallState callState) {}
  void onCallConnected(CallState callState) {}
  void onCallEndedByReceiver(CallState callState) {}
  void onCallEndedByUser(CallState callState) {}
  void onCallFailed(CallState callState) {}
  void onHoldStateChanged(CallState callState) {}
  void onMuteStateChanged(CallState callState) {}
  void onStreamReceived(CallState callState) {}
  void performCallCleanup() {}
}
