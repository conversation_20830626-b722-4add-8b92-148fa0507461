// ignore_for_file: use_late_for_private_fields_and_variables, unused_field

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/dialer/dialer.dart';
import 'package:froggytalk/shared/services/audio_routing_manager.dart';
import 'package:logger/logger.dart';
import 'package:navigation/navigation.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sip_ua/sip_ua.dart' as sip_ua;
import 'package:wakelock_plus/wakelock_plus.dart';

// Initialize logger
final Logger _logger = Logger();

/// Service responsible for managing SIP calls and audio routing
/// Implements SipUaHelperListener to handle SIP events and call states
class CallService implements sip_ua.SipUaHelperListener {
  CallService({required this.helper}) {
    helper.addSipUaHelperListener(this);
  }

  // Initialize the helper field in the constructor
  final sip_ua.SIPUAHelper helper;

  bool isAudioMuted = false;
  bool isCallOnHold = false;
  bool isSpeakerOn = false;

  // Audio routing manager instance
  final AudioRoutingManager _audioRoutingManager =
      AudioRoutingManager.instance;

  late sip_ua.Call? _call;
  final CountUpTimer _countupTimer = CountUpTimer();
  String? _holdOriginator;
  RTCVideoRenderer? _localRenderer = RTCVideoRenderer();
  MediaStream? _localStream;
  double? _localVideoHeight;
  EdgeInsetsGeometry? _localVideoMargin;
  double? _localVideoWidth;
  final bool _mirror = true;
  RTCVideoRenderer? _remoteRenderer = RTCVideoRenderer();
  MediaStream? _remoteStream;
  final sip_ua.CallStateEnum _state = sip_ua.CallStateEnum.NONE;
  late String _transferTarget;
  final bool _videoMuted = false;

  @override
  void callStateChanged(sip_ua.Call call, sip_ua.CallState callState) {
    _logger.i('CallService: Call state changed to ${callState.state} for call ${call.id}');

    switch (callState.state) {
      case sip_ua.CallStateEnum.CALL_INITIATION:
        // Centrally handle navigation to calling screen
        _handleCallInitiation(call);
      case sip_ua.CallStateEnum.HOLD:
        isCallOnHold = true;
      case sip_ua.CallStateEnum.UNHOLD:
        isCallOnHold = false;
      case sip_ua.CallStateEnum.MUTED:
        isAudioMuted = true;
      case sip_ua.CallStateEnum.UNMUTED:
        isAudioMuted = false;
      case sip_ua.CallStateEnum.CONNECTING:
      case sip_ua.CallStateEnum.ACCEPTED:
      case sip_ua.CallStateEnum.CONFIRMED:
        _call = call;
      case sip_ua.CallStateEnum.ENDED:
      case sip_ua.CallStateEnum.FAILED:
        _handleCallTermination(call);
      case sip_ua.CallStateEnum.STREAM:
        handleStreams(callState);
      // ignore: no_default_cases
      default:
        _logger.d('CallService: Unhandled call state: ${callState.state}');
    }
  }

  /// Handles call initiation and navigation to calling screen
  void _handleCallInitiation(sip_ua.Call call) {
    _logger.i('CallService: Handling call initiation for call ${call.id}');
    _call = call;

    // Use centralized navigation service to prevent duplicates
    CallNavigationService.handleCallInitiation(call).then((navigated) {
      if (navigated) {
        _logger.i('CallService: Successfully navigated to calling screen');
        startTimer(); // Start the call timer when screen is shown
      } else {
        _logger.w('CallService: Navigation was blocked (likely duplicate)');
      }
    }).catchError((Object error) {
      _logger.e('CallService: Error during navigation: $error');
    });
  }

  /// Handles call termination and cleanup
  void _handleCallTermination(sip_ua.Call call) {
    _logger.i('CallService: Handling call termination for call ${call.id}');

    stopTimer();
    CallNavigationService.handleCallTermination(call.id);

    // Additional cleanup
    _call = null;
    isAudioMuted = false;
    isCallOnHold = false;
    isSpeakerOn = false;
  }

  @override
  void onNewMessage(sip_ua.SIPMessageRequest msg) {
    // Implementation here
  }

  @override
  void onNewNotify(sip_ua.Notify ntf) {
    // Implementation here
  }

  @override
  void onNewReinvite(sip_ua.ReInvite event) {
    // Implementation here
  }

  @override
  void registrationStateChanged(sip_ua.RegistrationState state) {
    // Implementation here
  }

  @override
  void transportStateChanged(sip_ua.TransportState state) {
    // Implementation here
  }

  Stream<Duration> get timeStream => _countupTimer.timeStream;

  bool get voiceOnly => call!.voiceOnly && !call!.remote_has_video;

  String? get remoteIdentity => call!.remote_identity;

  String get direction => call!.direction;

  sip_ua.Call? get call => _call;

  /// Registers the SIP client with the provided credentials
  ///
  /// [username] - SIP username for authentication
  /// [password] - SIP password for authentication
  Future<void> register({
    required String username,
    required String password,
  }) async {
    final webSocketUrl =
        'ws://${AppInstance.getInstance().sipWebsocketUrl}/ws';
    final origin =
        'https://${AppInstance.getInstance().sipWebsocketUrl}/ws';
    final host = AppInstance.getInstance().sipWebsocketUrl;
    final userAgent = AppInstance.getInstance().sipUserAgent;
    final sanitizedUsername = _normalizeUsername(username);

    final sipUri =
        '$sanitizedUsername@${AppInstance.getInstance().sipUri}';

    // SIP Configuration with Session Timer Fix
    // Session timers are disabled to prevent automatic call termination
    // after 2 minutes due to RFC 4028 timeout behavior
    // See: https://github.com/your-org/froggytalk-mobile/issues/session-timer-bug
    final settings = sip_ua.UaSettings()
      ..webSocketUrl = webSocketUrl
      ..sessionTimers = false  // FIXED: Disable session timers to prevent 2-minute call timeout
      ..sessionTimersRefreshMethod = sip_ua.SipMethod.OPTIONS
      ..uri = sipUri
      ..authorizationUser = sanitizedUsername
      ..password = password
      ..displayName = sanitizedUsername
      ..userAgent = userAgent
      ..dtmfMode = sip_ua.DtmfMode.RFC2833
      ..register_expires = 1500
      ..transportType = sip_ua.TransportType.WS
      ..contact_uri = 'sip:$sipUri'
      ..connectionRecoveryMinInterval = 30
      ..connectionRecoveryMaxInterval = 120;

    settings.webSocketSettings.extraHeaders = {
      'Origin': origin,
      'Host': host,
    };

    settings.webSocketSettings.allowBadCertificate = true;
    settings.webSocketSettings.userAgent = userAgent;

    helper.loggingLevel =
        AppInstance.getInstance().environmentType.isProduction
            ? Level.off
            : Level.debug;

    await helper.start(settings);
  }

  /// Unregisters the SIP client and stops all connections
  void unregister() {
    helper.stop();
  }

  /// Activates the call service
  void activate() {
    // _initRenderers();
    // helper.addSipUaHelperListener(this);
  }

  /// Starts the call duration timer
  void startTimer() {
    _countupTimer.restart();
  }

  /// Stops the call duration timer
  void stopTimer() {
    _countupTimer.stop();
  }

  /// Deactivates the call service and cleans up resources
  void deactivate() {
    // _disposeRenderers();
    // helper.removeSipUaHelperListener(this);
  }

  /// Makes a call to the specified number with comprehensive error handling
  ///
  /// [number] - Phone number to call
  /// [voiceOnly] - Whether to make a voice-only call (default: true)
  Future<void> makeCall({String? number, bool voiceOnly = true}) async {
    if (number == null) return;

    try {
      // 1. Check permissions properly
      if (defaultTargetPlatform == TargetPlatform.android ||
          defaultTargetPlatform == TargetPlatform.iOS) {
        final permissionStatus = await Permission.microphone.request();
        if (permissionStatus != PermissionStatus.granted) {
          _logger.e('Microphone permission denied');
          throw Exception('Microphone permission denied');
        }
      }

      // 2. Tell the audio routing manager we're starting a call
      // This will handle the transition from any other audio source
      // Explicitly configure for earpiece mode (not speakerphone)
      await _audioRoutingManager.configureForCall();

      // 3. Configure media constraints with better defaults
      final mediaConstraints = <String, dynamic>{
        'audio': true,
        'video': voiceOnly ? false : {
          'mandatory': <String, dynamic>{
            'minWidth': '640',
            'minHeight': '480',
            'minFrameRate': '30',
          },
          'facingMode': 'user',
        },
      };

      // 4. Get media stream with proper error handling
      MediaStream mediaStream;

      try {
        if (kIsWeb && !voiceOnly) {
          mediaStream = await navigator.mediaDevices
              .getDisplayMedia(mediaConstraints);
          mediaConstraints['video'] = false;
          final userStream = await navigator.mediaDevices
              .getUserMedia(mediaConstraints);
          final audioTracks = userStream.getAudioTracks();
          if (audioTracks.isNotEmpty) {
            await mediaStream.addTrack(audioTracks.first);
          }
        } else {
          mediaStream = await navigator.mediaDevices
              .getUserMedia(mediaConstraints);
        }
      } catch (e) {
        _logger.e('Failed to get media stream: $e');
        // Clean up audio routing on error
        await _audioRoutingManager.resetToDefault();
        throw Exception('Failed to access microphone: $e');
      }

      // 5. Register tracks with audio routing manager
      if (!kIsWeb && !WebRTC.platformIsDesktop) {
        final audioTracks = mediaStream.getAudioTracks();
        if (audioTracks.isEmpty) {
          _logger.w('No audio tracks found in media stream');
        }
        _audioRoutingManager.registerWebRTCTracks(audioTracks);
      }

      // 6. Enable wakelock to keep screen on during call
      await WakelockPlus.enable();

      // 7. Make the call
      await helper.call(
        number,
        voiceOnly: voiceOnly,
        mediaStream: mediaStream,
      );

    } catch (e) {
      _logger.e('Error in makeCall: $e');

      // Clean up resources on error
      await _audioRoutingManager.resetToDefault();
      await WakelockPlus.disable();

      rethrow; // Allow caller to handle the error
    }
  }

  /// Cleans up local media stream and disposes of tracks
  void cleanUp() {
    if (_localStream == null) return;
    _localStream?.getTracks().forEach((track) {
      track.stop();
    });
    _localStream!.dispose();
    _localStream = null;
  }

  /// Handles call hangup and cleanup
  Future<void> handleHangup() async {
    await WakelockPlus.disable();
    call!.hangup({'status_code': 603});
    _countupTimer.stop();

    // Clean up audio routing when call ends
    _audioRoutingManager.clearWebRTCTracks();
    await _audioRoutingManager.resetToDefault();
  }

  /// Toggles audio mute state
  ///
  /// [mute] - Optional boolean to force mute state
  void toggleMuteAudio({bool? mute}) {
    final isAudioMuted2 = mute ?? isAudioMuted;
    if (isAudioMuted2) {
      call!.unmute(true, false);
      isAudioMuted = false;
    } else {
      call!.mute(true, false);
      isAudioMuted = true;
    }
  }

  /// Toggles call hold state
  ///
  /// [hold] - Optional boolean to force hold state
  void toggleHoldCall({bool? hold}) {
    final isCallOnHold2 = hold ?? isCallOnHold;
    if (isCallOnHold2) {
      call!.unhold();
      isCallOnHold = false;
    } else {
      call!.hold();
      isCallOnHold = true;
    }
  }

  /// Sends DTMF tone during call
  ///
  /// [tone] - DTMF tone to send (0-9, *, #)
  void sendDTMF(String tone) {
    call!.sendDTMF(tone);
  }

  /// Toggles speaker mode with proper audio routing
  ///
  /// [enabled] - Optional boolean to force speaker state
  void toggleSpeaker({bool? enabled}) {
    if (_localStream != null) {
      // Update internal state
      isSpeakerOn = enabled ?? !isSpeakerOn;

      // Use audio routing manager to properly handle speaker mode
      if (isSpeakerOn) {
        _audioRoutingManager
            .setAudioOutputMode(AudioOutputMode.speaker);
      } else {
        _audioRoutingManager
            .setAudioOutputMode(AudioOutputMode.earpiece);
      }

      // Register tracks with audio routing manager
      if (!kIsWeb) {
        final audioTracks = _localStream!.getAudioTracks();
        _audioRoutingManager.registerWebRTCTracks(audioTracks);
      }
    }
  }

  /// Initializes video renderers for local and remote streams
  Future<void> initRenderers() async {
    if (_localRenderer != null) {
      await _localRenderer!.initialize();
    }

    if (_remoteRenderer != null) {
      await _remoteRenderer!.initialize();
    }
  }

  /// Disposes video renderers and cleans up resources
  void disposeRenderers() {
    if (_localRenderer != null) {
      _localRenderer!.dispose();
      _localRenderer = null;
    }

    if (_remoteRenderer != null) {
      _remoteRenderer!.dispose();
      _remoteRenderer = null;
    }
  }

  // Added methods to integrate with AudioRoutingManager

  /// Activates audio routing management and listens for changes
  void activateAudioRouting() {
    _audioRoutingManager.routingChangeStream.listen((mode) {
      switch (mode) {
        case AudioOutputMode.bluetooth:
          _logger.d('Audio output switched to Bluetooth');
        case AudioOutputMode.wiredHeadset:
          _logger.d('Audio output switched to Wired Headset');
        case AudioOutputMode.speaker:
          _logger.d('Audio output switched to Speakerphone');
        case AudioOutputMode.earpiece:
          _logger.d('Audio output switched to Earpiece');
      }
    });
  }

  bool get isBluetoothActive => _audioRoutingManager.isBluetoothActive;
  bool get isWiredHeadsetActive =>
      _audioRoutingManager.isWiredHeadsetActive;
  bool get isSpeakerphoneOn => _audioRoutingManager.isSpeakerphoneOn;
  bool get isEarpieceActive => _audioRoutingManager.isEarpieceActive;

  // ignore: unused_element
  void _backToDialPad() {
    _countupTimer.stop();
    FroggyRouter.maybePop();
    cleanUp();
  }

  /// Handles incoming media streams and configures audio routing
  ///
  /// [event] - CallState event containing stream information
  Future<void> handleStreams(sip_ua.CallState event) async {
    final stream = event.stream;
    if (event.originator == 'local') {
      if (_localRenderer != null) {
        await _localRenderer!.initialize();

        _localRenderer!.srcObject = stream;
      }

      if (!kIsWeb &&
          !WebRTC.platformIsDesktop &&
          event.stream != null) {
        final audioTracks = event.stream!.getAudioTracks();

        // CRITICAL FIX: Explicitly disable speakerphone on all audio tracks
        // This prevents automatic speakerphone activation during outbound calls
        for (final track in audioTracks) {
          track.enableSpeakerphone(false);
        }

        // Update internal state to reflect earpiece mode
        isSpeakerOn = false;

        // Register tracks with audio routing manager
        _audioRoutingManager.registerWebRTCTracks(audioTracks);
        // Ensure earpiece mode is maintained (don't override if already set)
        if (_audioRoutingManager.currentOutputMode !=
            AudioOutputMode.earpiece) {
          await _audioRoutingManager
              .setAudioOutputMode(AudioOutputMode.earpiece);
        }
      }

      _localStream = stream;
    }

    if (event.originator == 'remote') {
      if (_remoteRenderer != null) {
        await _remoteRenderer!.initialize();
        _remoteRenderer!.srcObject = stream;
      }
      _remoteStream = stream;
    }

    _resizeLocalVideo();
  }

  /// Resizes local video based on remote stream availability
  void _resizeLocalVideo() {
    _localVideoMargin = _remoteStream != null
        ? const EdgeInsets.only(top: 15, right: 15)
        : EdgeInsets.zero;
    _localVideoWidth = _remoteStream != null
        ? MediaQuery.of(FroggyRouter.context!).size.width / 4
        : MediaQuery.of(FroggyRouter.context!).size.width;
    _localVideoHeight = _remoteStream != null
        ? MediaQuery.of(FroggyRouter.context!).size.height / 4
        : MediaQuery.of(FroggyRouter.context!).size.height;
  }

  /// Normalizes username format for SIP registration
  /// Handles international prefixes consistently
  String _normalizeUsername(String username) {
    // Remove any leading/trailing whitespace
    final trimmed = username.trim();

    // Convert '00' prefix to proper format (remove 00, don't add +)
    if (trimmed.startsWith('00')) {
      return trimmed.substring(2); // Remove '00' prefix
    }

    // Remove '+' prefix for SIP registration
    if (trimmed.startsWith('+')) {
      return trimmed.substring(1); // Remove '+' prefix
    }

    return trimmed;
  }

  /// Safe wrapper for re-invite operations to prevent getUserMedia errors
  /// Ensures proper media constraints are always provided
  void safeReinvite({
    required sip_ua.Call call,
    bool voiceOnly = true,
    Map<String, dynamic>? customOptions,
  }) {
    try {
      // Always provide valid media constraints to prevent "no media types" error
      final defaultMediaConstraints = <String, dynamic>{
        'audio': true,
        'video': voiceOnly ? false : {
          'mandatory': <String, dynamic>{
            'minWidth': '640',
            'minHeight': '480',
            'minFrameRate': '30',
          },
          'facingMode': 'user',
        },
      };

      final options = <String, dynamic>{
        'mediaConstraints': defaultMediaConstraints,
        ...?customOptions, // Spread custom options if provided
      };

      // Use the call's renegotiate method which internally calls _sendReinvite
      call.renegotiate(options: options);

      _logger.i('Safe re-invite initiated with audio: true, video: ${!voiceOnly}');
    } catch (e) {
      _logger.e('Safe re-invite failed: $e');
      // Handle the error gracefully without crashing the app
    }
  }

  /// Safe wrapper for hold operations
  void safeHold(sip_ua.Call call) {
    try {
      // According to SIP/WebRTC specs, hold() should be called without mediaConstraints
      // The dart-sip-ua library handles the SDP manipulation internally
      call.hold();
      _logger.i('Safe hold operation initiated');
    } catch (e) {
      _logger.e('Safe hold failed: $e');
    }
  }

  /// Safe wrapper for unhold operations
  void safeUnhold(sip_ua.Call call) {
    try {
      // According to SIP/WebRTC specs, unhold() should be called without mediaConstraints
      // The dart-sip-ua library handles the SDP manipulation internally
      call.unhold();
      _logger.i('Safe unhold operation initiated');
    } catch (e) {
      _logger.e('Safe unhold failed: $e');
    }
  }

  /// Safely handle call operations that might trigger re-invite
  /// This prevents the "constraints requests no media types" error
  Future<void> handleCallOperation(String operation, sip_ua.Call call, {
    bool voiceOnly = true,
    Map<String, dynamic>? customOptions,
  }) async {
    try {
      switch (operation.toLowerCase()) {
        case 'hold':
          safeHold(call);
        case 'unhold':
          safeUnhold(call);
        case 'renegotiate':
          safeReinvite(call: call, voiceOnly: voiceOnly, customOptions: customOptions);
        default:
          _logger.w('Unknown call operation: $operation');
      }
    } catch (e) {
      _logger.e('Call operation $operation failed: $e');

      // Report the error to analytics
      await MediaErrorHandlerService.reportMediaError(
        Exception('Call operation $operation failed: $e'),
        operation: operation,
        mediaType: 'call_control',
        additionalData: {
          'call_id': call.id,
          'voice_only': voiceOnly,
          'operation_type': operation,
        },
      );
    }
  }
}
