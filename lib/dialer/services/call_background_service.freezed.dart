// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'call_background_service.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CallBackgroundState _$CallBackgroundStateFromJson(Map<String, dynamic> json) {
  return _CallBackgroundState.fromJson(json);
}

/// @nodoc
mixin _$CallBackgroundState {
  String get callId => throw _privateConstructorUsedError;
  String get phoneNumber => throw _privateConstructorUsedError;
  String get duration => throw _privateConstructorUsedError;
  String get state => throw _privateConstructorUsedError;
  String? get displayName => throw _privateConstructorUsedError;

  /// Serializes this CallBackgroundState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CallBackgroundState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CallBackgroundStateCopyWith<CallBackgroundState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CallBackgroundStateCopyWith<$Res> {
  factory $CallBackgroundStateCopyWith(
          CallBackgroundState value, $Res Function(CallBackgroundState) then) =
      _$CallBackgroundStateCopyWithImpl<$Res, CallBackgroundState>;
  @useResult
  $Res call(
      {String callId,
      String phoneNumber,
      String duration,
      String state,
      String? displayName});
}

/// @nodoc
class _$CallBackgroundStateCopyWithImpl<$Res, $Val extends CallBackgroundState>
    implements $CallBackgroundStateCopyWith<$Res> {
  _$CallBackgroundStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CallBackgroundState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? callId = null,
    Object? phoneNumber = null,
    Object? duration = null,
    Object? state = null,
    Object? displayName = freezed,
  }) {
    return _then(_value.copyWith(
      callId: null == callId
          ? _value.callId
          : callId // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as String,
      state: null == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as String,
      displayName: freezed == displayName
          ? _value.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CallBackgroundStateImplCopyWith<$Res>
    implements $CallBackgroundStateCopyWith<$Res> {
  factory _$$CallBackgroundStateImplCopyWith(_$CallBackgroundStateImpl value,
          $Res Function(_$CallBackgroundStateImpl) then) =
      __$$CallBackgroundStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String callId,
      String phoneNumber,
      String duration,
      String state,
      String? displayName});
}

/// @nodoc
class __$$CallBackgroundStateImplCopyWithImpl<$Res>
    extends _$CallBackgroundStateCopyWithImpl<$Res, _$CallBackgroundStateImpl>
    implements _$$CallBackgroundStateImplCopyWith<$Res> {
  __$$CallBackgroundStateImplCopyWithImpl(_$CallBackgroundStateImpl _value,
      $Res Function(_$CallBackgroundStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CallBackgroundState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? callId = null,
    Object? phoneNumber = null,
    Object? duration = null,
    Object? state = null,
    Object? displayName = freezed,
  }) {
    return _then(_$CallBackgroundStateImpl(
      callId: null == callId
          ? _value.callId
          : callId // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as String,
      state: null == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as String,
      displayName: freezed == displayName
          ? _value.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CallBackgroundStateImpl implements _CallBackgroundState {
  const _$CallBackgroundStateImpl(
      {required this.callId,
      required this.phoneNumber,
      required this.duration,
      required this.state,
      this.displayName});

  factory _$CallBackgroundStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$CallBackgroundStateImplFromJson(json);

  @override
  final String callId;
  @override
  final String phoneNumber;
  @override
  final String duration;
  @override
  final String state;
  @override
  final String? displayName;

  @override
  String toString() {
    return 'CallBackgroundState(callId: $callId, phoneNumber: $phoneNumber, duration: $duration, state: $state, displayName: $displayName)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CallBackgroundStateImpl &&
            (identical(other.callId, callId) || other.callId == callId) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, callId, phoneNumber, duration, state, displayName);

  /// Create a copy of CallBackgroundState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CallBackgroundStateImplCopyWith<_$CallBackgroundStateImpl> get copyWith =>
      __$$CallBackgroundStateImplCopyWithImpl<_$CallBackgroundStateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CallBackgroundStateImplToJson(
      this,
    );
  }
}

abstract class _CallBackgroundState implements CallBackgroundState {
  const factory _CallBackgroundState(
      {required final String callId,
      required final String phoneNumber,
      required final String duration,
      required final String state,
      final String? displayName}) = _$CallBackgroundStateImpl;

  factory _CallBackgroundState.fromJson(Map<String, dynamic> json) =
      _$CallBackgroundStateImpl.fromJson;

  @override
  String get callId;
  @override
  String get phoneNumber;
  @override
  String get duration;
  @override
  String get state;
  @override
  String? get displayName;

  /// Create a copy of CallBackgroundState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CallBackgroundStateImplCopyWith<_$CallBackgroundStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
