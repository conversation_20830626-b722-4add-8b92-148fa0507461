import 'package:flutter/material.dart';
import 'package:froggytalk/dialer/dialer.dart';
import 'package:navigation/navigation.dart';
import 'package:sip_ua/sip_ua.dart';
import 'package:utils/utils.dart';

/// Service responsible for managing navigation to call screens
/// Prevents duplicate navigation and handles call screen routing centrally
class CallNavigationService {
  CallNavigationService._();

  static String? _currentCallId;
  static final CallNavigationService _instance = CallNavigationService._();
  // Track if we're currently navigating to prevent duplicates
  static bool _isNavigatingToCall = false;

  static CallNavigationService get instance => _instance;

  /// Handles navigation to the calling screen when a call is initiated
  ///
  /// [call] - The SIP call object
  /// Returns true if navigation was handled, false if duplicate/ignored
  static Future<bool> handleCallInitiation(Call call) async {
    final callId = call.id ?? call.hashCode.toString();

    // Prevent duplicate navigation for the same call
    if (_isNavigatingToCall || _currentCallId == callId) {
      FroggyLogger.debug(
        'CallNavigationService: Ignoring duplicate navigation for call $callId',
      );
      return false;
    }

    try {
      _isNavigatingToCall = true;
      _currentCallId = callId;

      FroggyLogger.debug(
        'CallNavigationService: Navigating to CallingUserView for call $callId',
      );

      final context = FroggyRouter.navigatorKey.currentContext;
      if (context == null) {
        FroggyLogger.error(
          'CallNavigationService: No context available for navigation',
        );
        return false;
      }

      // Navigate to the calling screen
      await Navigator.of(context).push(
        // CallingUserView.route(call: call),
        PreferredCallingUserView.route(call: call),
      );

      FroggyLogger.debug(
        'CallNavigationService: Successfully navigated to CallingUserView',
      );

      return true;
    } catch (e) {
      FroggyLogger.error(
        'CallNavigationService: Error during navigation: $e',
      );
      return false;
    } finally {
      // Reset flags when navigation completes (user returns)
      _isNavigatingToCall = false;
      _currentCallId = null;

      FroggyLogger.debug(
        'CallNavigationService: Navigation completed, flags reset',
      );
    }
  }

  /// Checks if we're currently navigating to a call screen
  static bool get isNavigatingToCall => _isNavigatingToCall;

  /// Gets the current call ID being navigated to
  static String? get currentCallId => _currentCallId;

  /// Manually reset navigation state (use with caution)
  static void resetNavigationState() {
    _isNavigatingToCall = false;
    _currentCallId = null;
    FroggyLogger.debug(
        'CallNavigationService: Navigation state manually reset',);
  }

  /// Handle call termination - cleanup navigation state if needed
  static void handleCallTermination(String? callId) {
    if (_currentCallId == callId) {
      FroggyLogger.debug(
        'CallNavigationService: Call $callId terminated, resetting state',
      );
      resetNavigationState();
    }
  }
}
