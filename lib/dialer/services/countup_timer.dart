import 'dart:async';

import 'package:rxdart/rxdart.dart';

class CountUpTimer {
  // Factory constructor to return the same instance
  factory CountUpTimer() {
    return _instance;
  }

  // Private constructor
  CountUpTimer._internal();

  // Singleton instance
  static final CountUpTimer _instance = CountUpTimer._internal();

  // Timer-related properties
  Duration _elapsedTime = Duration.zero;
  Timer? _timer;
  bool _isRunning = false;

  // Stream controller to notify listeners about time updates
  final BehaviorSubject<Duration> _timeStreamController =
      BehaviorSubject<Duration>();

  // Public stream to allow external listening to time updates
  Stream<Duration> get timeStream => _timeStreamController.stream;

  // Start or resume the timer
  void start() {
    if (!_isRunning) {
      _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        _elapsedTime += const Duration(seconds: 1);
        _timeStreamController.add(_elapsedTime); // Notify listeners
      });
      _isRunning = true;
    }
  }

  // Pause the timer
  void pause() {
    if (_isRunning) {
      _timer?.cancel();
      _isRunning = false;
    }
  }

  void stop() {
    _timer?.cancel();
    _isRunning = false;
  }

  // Resume the timer
  void resume() {
    if (!_isRunning) {
      start();
    }
  }

  // Restart the timer
  void restart() {
    _timer?.cancel();
    _elapsedTime = Duration.zero;
    _timeStreamController.add(_elapsedTime); // Notify listeners
    _isRunning = false;
    start(); // Restart the timer from zero
  }

  // Start the timer with a specific start time (for synchronization)
  void startWithTime(DateTime startTime) {
    if (!_isRunning) {
      // Calculate the elapsed time from the provided start time
      final now = DateTime.now();
      _elapsedTime = now.difference(startTime);

      // Ensure elapsed time is not negative
      if (_elapsedTime.isNegative) {
        _elapsedTime = Duration.zero;
      }

      _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        _elapsedTime += const Duration(seconds: 1);
        _timeStreamController.add(_elapsedTime); // Notify listeners
      });
      _isRunning = true;

      // Immediately notify with the initial elapsed time
      _timeStreamController.add(_elapsedTime);
    }
  }

  // Get the current elapsed time
  Duration getElapsedTime() {
    return _elapsedTime;
  }

  // Method to format time in "MM:SS" format
  String formatTime(Duration duration) {
    final minutes = duration.inMinutes.toString().padLeft(2, '0');
    final seconds = (duration.inSeconds % 60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }

  // Dispose the stream controller when done to avoid memory leaks
  void dispose() {
    _timeStreamController.close();
  }
}
