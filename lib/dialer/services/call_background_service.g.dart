// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'call_background_service.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CallBackgroundStateImpl _$$CallBackgroundStateImplFromJson(
        Map<String, dynamic> json) =>
    _$CallBackgroundStateImpl(
      callId: json['callId'] as String,
      phoneNumber: json['phoneNumber'] as String,
      duration: json['duration'] as String,
      state: json['state'] as String,
      displayName: json['displayName'] as String?,
    );

const _$$CallBackgroundStateImplFieldMap = <String, String>{
  'callId': 'callId',
  'phoneNumber': 'phoneNumber',
  'duration': 'duration',
  'state': 'state',
  'displayName': 'displayName',
};

Map<String, dynamic> _$$CallBackgroundStateImplToJson(
        _$CallBackgroundStateImpl instance) =>
    <String, dynamic>{
      'callId': instance.callId,
      'phoneNumber': instance.phoneNumber,
      'duration': instance.duration,
      'state': instance.state,
      if (instance.displayName case final value?) 'displayName': value,
    };
