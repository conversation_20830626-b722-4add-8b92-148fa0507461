import 'dart:async';
import 'dart:convert';

import 'package:bloc/bloc.dart';
import 'package:countries/countries.dart';
import 'package:data/data.dart';
import 'package:flutter/foundation.dart';
import 'package:formz/formz.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:froggytalk/app/data/services/event_tracker.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:froggytalk/auth/data/models/phone_number_data.dart';
import 'package:http/http.dart' as http;
import 'package:utils/utils.dart';

part 'send_otp_form_bloc.freezed.dart';
part 'send_otp_form_event.dart';
part 'send_otp_form_state.dart';

class SendOtpFormBloc extends Bloc<SendOtpFormEvent, SendOtpFormState> {
  SendOtpFormBloc() : super(SendOtpFormState.initial()) {
    on<_Started>(_onStarted);
    on<_PhoneNumberChanged>(
      _onPhoneNumberChanged,
      transformer: debounce(),
    );
    on<_CountryCodeChanged>(
      _onCountryCodeChanged,
      transformer: debounce(),
    );
    on<_ReferralCodeChanged>(
      _onReferralCodeChanged,
      transformer: debounce(),
    );
    on<_Submitted>(_onSubmitted);
    on<_ResentOtp>(_onResentOtp);
    on<_ResetFormErrors>(_onFormResetErrors);
    on<_InitializeCountry>(_onInitializeCountry);
  }

  final _sendOtpRepository = SendOtpRepository();
  final _resendOtpRepository = ResendOtpRepository();
  final _checkUserExistsRepository = CheckExistingUserRepository();
  final _eventTracker = EventTrackerService.getInstance();

  FutureOr<void> _onStarted(_Started event, Emitter<SendOtpFormState> emit) {
    // Initialize with empty state and trigger country lookup
    emit(
      state.copyWith(
        status: FormzSubmissionStatus.initial,
        phoneNumber: PhoneNumberInput.dirty(event.phoneNumber ?? ''),
        countryCode: event.countryCode != null
            ? VarCharInput.dirty(event.countryCode!)
            : state.countryCode,
        referralCode: event.referralCode == null
            ? null
            : VarCharInput.dirty(event.referralCode ?? ''),
      ),
    );

    // Automatically initialize country via IP lookup if not already set
    if (state.selectedCountry == null) {
      add(const SendOtpFormEvent.initializeCountry());
    }
  }

  FutureOr<void> _onPhoneNumberChanged(
    _PhoneNumberChanged event,
    Emitter<SendOtpFormState> emit,
  ) async {
    final phoneNumber = event.phoneNumber;
    // Use selectedCountry if available, otherwise fallback to existing countryCode or US
    final countryCode = state.selectedCountry?.code ??
        (state.countryCode.value.isEmpty ? 'us' : state.countryCode.value);
    final formInput = PhoneNumberInput.dirty(phoneNumber);

    // Create standardized phone number data using centralized utilities
    final phoneNumberData = PhoneNumberData.fromInput(
      rawInput: phoneNumber,
      countryCode: countryCode,
    );

    emit(
      state.copyWith(
        status: FormzSubmissionStatus.initial,
        phoneNumber: formInput,
        countryCode: VarCharInput.dirty(countryCode),
        phoneNumberErrorMessage:
            phoneNumberData.errorMessage ?? formInput.errorMessage,
        phoneNumberData: phoneNumberData,
      ),
    );

    if (formInput.isValid && phoneNumberData.isValid) {
      await _eventTracker.logEvent(
        schema: 'Verification_Started',
        description: 'User enters WhatsApp Number',
      );

      final checkUserExistsResponse = await _checkUserExistsRepository.execute(
        phoneNumber: phoneNumberData.nationalNumber,
      );

      if (checkUserExistsResponse.isRight) {
        emit(
          state.copyWith(
            status: FormzSubmissionStatus.initial,
            showReferralField: checkUserExistsResponse
                    .right.data?.attributes?.isExistingUser ==
                '0',
          ),
        );
      } else {
        emit(
          state.copyWith(
            status: FormzSubmissionStatus.initial,
            showReferralField: false,
          ),
        );
      }
    }
  }

  FutureOr<void> _onReferralCodeChanged(
    _ReferralCodeChanged event,
    Emitter<SendOtpFormState> emit,
  ) {
    final formInput = event.referralCode == null
        ? null
        : VarCharInput.dirty(event.referralCode!);
    emit(
      state.copyWith(
        status: FormzSubmissionStatus.initial,
        referralCode: formInput,
        referralErrorMessage: formInput?.errorMessage,
      ),
    );
  }

  FutureOr<void> _onCountryCodeChanged(
    _CountryCodeChanged event,
    Emitter<SendOtpFormState> emit,
  ) {
    final formInput = VarCharInput.dirty(event.countryCode.code ?? '');
    // Update phone number data when country changes
    PhoneNumberData? updatedPhoneData;
    if (state.phoneNumber.value.isNotEmpty) {
      updatedPhoneData = PhoneNumberData.fromInput(
        rawInput: state.phoneNumber.value,
        countryCode: formInput.value,
      );
    }
    emit(
      state.copyWith(
        status: FormzSubmissionStatus.initial,
        countryCode: formInput,
        countryCodeErrorMessage: formInput.errorMessage,
        selectedCountry: event.countryCode,
        phoneNumberData: updatedPhoneData,
      ),
    );
  }

  /// Handles the OTP form submission event.
  /// Adds debug logs and assertions for easier troubleshooting.
  FutureOr<void> _onSubmitted(
    _Submitted event,
    Emitter<SendOtpFormState> emit,
  ) async {
    // Debug: Log current state before submission
    if (kDebugMode) {
      debugPrint('[SendOtpFormBloc] _onSubmitted called with state:'
          '\n  phoneNumberData: [32m${state.phoneNumberData}[0m,'
          '\n  referralCode: ${state.referralCode},'
          '\n  countryCode: ${state.countryCode}');
      debugPrint('[SendOtpFormBloc] Submission details: '
          'phoneNumber: [34m${state.phoneNumber.value}[0m, '
          'country: [34m${state.countryCode.value}[0m');
    }

    // Check if we have valid phone number data
    if (state.phoneNumberData == null || !state.phoneNumberData!.isValid) {
      if (kDebugMode) {
        debugPrint('[SendOtpFormBloc] Invalid phone number data: '
            '${state.phoneNumberData}');
      }
      emit(
        state.copyWith(
          status: FormzSubmissionStatus.failure,
          message:
              state.phoneNumberData?.errorMessage ?? 'Invalid phone number',
        ),
      );
      // Debug: Print stack for invalid state
      if (kDebugMode) {
        debugPrintStack(
          label: '[SendOtpFormBloc] Submission failed: Invalid phone number',
        );
      }
      return;
    }

    await _eventTracker.logEvent(
      schema: 'Verification_Requested',
      description: 'User requests verification',
    );
    if (kDebugMode) {
      debugPrint('[SendOtpFormBloc] Verification_Requested event logged');
    }

    emit(
      state.copyWith(
        status: FormzSubmissionStatus.inProgress,
      ),
    );

    final phoneData = state.phoneNumberData!;
    if (kDebugMode) {
      debugPrint('[SendOtpFormBloc] Sending OTP with phoneData: '
          '${phoneData.nationalNumber}, country: ${phoneData.countryCode}, referral: ${state.referralCode?.value}');
    }
    final sendOtpResponse = await _sendOtpRepository.execute(
      phoneNumber: phoneData.nationalNumber,
      countryCode: phoneData.countryCode,
      referralCode: state.referralCode?.value,
    );

    // Debug: Log the response from repository
    if (kDebugMode) {
      debugPrint('[SendOtpFormBloc] sendOtpResponse: $sendOtpResponse');
    }

    if (sendOtpResponse.isRight) {
      await _eventTracker.logEvent(
        schema: 'OTP_sent',
        description: 'OTP gets sent to the user',
      );
      if (kDebugMode) {
        debugPrint('[SendOtpFormBloc] OTP_sent event logged');
      }

      emit(
        state.copyWith(
          status: FormzSubmissionStatus.success,
          message: sendOtpResponse.right.message,
          noOfTriesLeft: sendOtpResponse.right.data?.noOfTriesLeft ?? '3',
        ),
      );
      if (kDebugMode) {
        debugPrint('[SendOtpFormBloc] OTP sent successfully. Message: '
            '${sendOtpResponse.right.message}');
      }
    } else {
      final message = sendOtpResponse.left.message;
      final code = sendOtpResponse.left.code;
      if (kDebugMode) {
        debugPrint(
          '[SendOtpFormBloc] OTP send failed. Code: $code, Message: $message',
        );
        debugPrint('[SendOtpFormBloc] Form validation errors: '
            'phone: [31m${sendOtpResponse.left.formValidationErrors?['telephone']?.detail}[0m, '
            'country: [31m${sendOtpResponse.left.formValidationErrors?['country_code']?.detail}[0m, '
            'referral: [31m${sendOtpResponse.left.formValidationErrors?['referral_code']?.detail}[0m, '
            'device: [31m${sendOtpResponse.left.formValidationErrors?['device_id']?.detail}[0m');
      }
      if (kDebugMode) {
        debugPrintStack(label: '[SendOtpFormBloc] OTP send failed');
      }
      // Robust error handling: log unexpected codes, do not assert
      final allowedCodes = [
        'internet-out',
        'validation-error',
        'server-error',
        'form-validation-failed',
      ];
      if (!allowedCodes.contains(code)) {
        debugPrint('[SendOtpFormBloc] Unexpected error code: $code');
      }
      emit(
        state.copyWith(
          status: FormzSubmissionStatus.failure,
          message: message ?? 'An unknown error occurred',
        ),
      );
    }
  }

  Future<void> _onResentOtp(
    _ResentOtp event,
    Emitter<SendOtpFormState> emit,
  ) async {
    // Check if we have valid phone number data
    if (state.phoneNumberData == null || !state.phoneNumberData!.isValid) {
      emit(
        state.copyWith(
          status: FormzSubmissionStatus.failure,
          message: 'Cannot resend OTP: Invalid phone number',
        ),
      );
      return;
    }

    final phoneData = state.phoneNumberData!;
    final resendOtpResponse = await _resendOtpRepository.execute(
      phoneNumber: phoneData.nationalNumber,
      countryCode: phoneData.countryCode,
    );

    if (resendOtpResponse.isRight) {
      emit(
        state.copyWith(
          status: FormzSubmissionStatus.success,
          message: resendOtpResponse.right.message,
          noOfTriesLeft: resendOtpResponse.right.data?.noOfTriesLeft ?? '3',
        ),
      );
    } else {
      emit(
        state.copyWith(
          status: FormzSubmissionStatus.failure,
          message: resendOtpResponse.left.message ??
              'Error occurred while '
                  'resending OTP',
          phoneNumberErrorMessage:
              resendOtpResponse.left.formValidationErrors?['telephone']?.title,
          countryCodeErrorMessage: resendOtpResponse
              .left.formValidationErrors?['country_code']?.title,
        ),
      );
    }
  }

  FutureOr<void> _onFormResetErrors(
    _ResetFormErrors event,
    Emitter<SendOtpFormState> emit,
  ) {
    emit(
      state.copyWith(
        status: FormzSubmissionStatus.initial,
        message: null,
        phoneNumberErrorMessage: null,
        countryCodeErrorMessage: null,
        deviceIdErrorMessage: null,
        referralErrorMessage: null,
      ),
    );
  }

  /// Handles IP-based country initialization
  FutureOr<void> _onInitializeCountry(
    _InitializeCountry event,
    Emitter<SendOtpFormState> emit,
  ) async {
    if (state.selectedCountry != null) {
      // Country already set, no need to fetch
      return;
    }

    try {
      String? ipCountryCode;
      try {
        FroggyLogger.debug('[GeoLocation] Starting IP country lookup...');
        final response = await http.get(Uri.parse('https://ipinfo.io/json'));
        if (response.statusCode == 200) {
          final data = jsonDecode(response.body) as Map<String, dynamic>;
          ipCountryCode = (data['country'] as String?)?.toLowerCase();
          FroggyLogger.info(
            '[GeoLocation] IP lookup successful: $ipCountryCode',
          );
        } else {
          FroggyLogger.warning(
            '[GeoLocation] IP lookup failed with status: ${response.statusCode}',
          );
        }
      } catch (e) {
        FroggyLogger.error('[GeoLocation] IP lookup error: $e');
      }

      final froggyCountriesInstance = FroggyCountries.getInstance();
      await froggyCountriesInstance.reload();

      final countryModel = ipCountryCode != null
          ? (froggyCountriesInstance.findCountryByCountryCode(ipCountryCode) ??
              froggyCountriesInstance
                  .findCountryByCountryCode(ipCountryCode.toUpperCase()))
          : null;

      FroggyLogger.debug(
        '[GeoLocation] Country lookup result for $ipCountryCode (also tried ${ipCountryCode?.toUpperCase()}): $countryModel',
      );

      if (countryModel != null) {
        FroggyLogger.info(
          '[GeoLocation] Using IP-detected country: ${countryModel.code} (${countryModel.name})',
        );
        emit(
          state.copyWith(
            selectedCountry: countryModel,
            countryCode: VarCharInput.dirty(countryModel.code ?? ''),
          ),
        );
      } else {
        // If IP lookup fails, use a more sensible fallback (US instead of Netherlands)
        final fallbackCountry =
            froggyCountriesInstance.findCountryByCountryCode('us') ??
                froggyCountriesInstance.getDefaultCountry();
        FroggyLogger.warning(
          '[GeoLocation] Country not found for code $ipCountryCode, using fallback: ${fallbackCountry.code} (${fallbackCountry.name})',
        );
        emit(
          state.copyWith(
            selectedCountry: fallbackCountry,
            countryCode: VarCharInput.dirty(fallbackCountry.code ?? ''),
          ),
        );
      }
    } catch (e) {
      // Fallback to US on any error instead of Netherlands
      FroggyLogger.error(
        '[GeoLocation] Critical error in country initialization: $e',
      );
      final froggyCountriesInstance = FroggyCountries.getInstance();
      final fallbackCountry =
          froggyCountriesInstance.findCountryByCountryCode('us') ??
              froggyCountriesInstance.getDefaultCountry();
      emit(
        state.copyWith(
          selectedCountry: fallbackCountry,
          countryCode: VarCharInput.dirty(fallbackCountry.code ?? ''),
        ),
      );
    }
  }
}
