import 'dart:async';

import 'package:data/data.dart';
import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:froggytalk/profile/profile.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:utils/utils.dart';

part 'authentication_bloc.freezed.dart';
part 'authentication_bloc.g.dart';
part 'authentication_event.dart';
part 'authentication_state.dart';

class AuthenticationBloc
    extends HydratedBloc<AuthenticationEvent, AuthenticationState> {
  AuthenticationBloc({
    required PushNotificationService pushNotificationService,
  })  : _pushNotificationService = pushNotificationService,
        super(AuthenticationState.initial()) {
    on<_Started>(_onStarted);
    on<_UpdatedUser>(_onUpdatedUser);
    on<_SignedIn>(_onSignedIn);
    on<_SignedOut>(_onSignedOut);
    on<_DestroyedToken>(_onDestroyedToken);
    on<_Initial>(_onInitial);
    on<_EnabledOfflineMode>(_onEnabledOfflineMode);
  }

  final _logoutRepository = LogoutRepository();
  final _profileRepository = AuthProfileRepository();
  final PushNotificationService _pushNotificationService;
  final _tokenManager = FroggyAccessTokenManager();
  final _connectivityManager = ConnectivityManager();

  @override
  AuthenticationState? fromJson(Map<String, dynamic> json) {
    try {
      // return AuthenticationState.fromJson(json);
      return AuthenticationState(
        user:
            UserAttributesModel.fromJson(json['user'] as Map<String, dynamic>),
        status: state.status.toString().toAuthenticationStatus ??
            AuthenticationStatus.unknown,
      );
    } catch (_) {
      return AuthenticationState.initial();
    }
  }

  @override
  Map<String, dynamic>? toJson(AuthenticationState state) {
    try {
      // return state.toJson();
      return {
        'user': state.user?.toJson(),
        'status': state.status.toAuthenticationStatusString,
      };
    } catch (_) {
      return null;
    }
  }

  FutureOr<void> _onStarted(
    _Started event,
    Emitter<AuthenticationState> emit,
  ) async {
    emit(AuthenticationState.loading());

    final hasToken = _tokenManager.get();
    if (hasToken == null) {
      // add(const AuthenticationEvent.destroyedToken());
      emit(
        state.copyWith(
          user: null,
          status: AuthenticationStatus.unauthenticated,
        ),
      );

      await clear();
      return Future.value();
      // return;
    }

    final hasInternetAccess = await _connectivityManager.canPingGoogle();

    // if (!state.hasInternetAccess) {
    if (!hasInternetAccess) {
      await _handleNoInternetAccess(emit);
      return Future.value();
    }

    await _fetchAndUpdateProfile(emit);
  }

  Future<void> _handleNoInternetAccess(
    Emitter<AuthenticationState> emit,
  ) async {
    emit(
      state.copyWith(
        // message: 'No internet access, please wait ...',
        user: state.user,
        status: AuthenticationStatus.authenticated,
      ),
    );
  }

  Future<void> _fetchAndUpdateProfile(Emitter<AuthenticationState> emit) async {
    final initialState = state;

    final authResponse = await _profileRepository.execute();

    // Check if state changed during async operation
    if (state != initialState || isClosed) return;

    emit(state.copyWith(message: null));

    if (authResponse.isRight) {
      final user = authResponse.right.data?.attributes;
      add(AuthenticationEvent.updatedUser(user: user));
      return Future.value();
    }

    await _handleProfileError(authResponse.left, emit);
  }

  Future<void> _handleProfileError(
    GenericErrorResponse error,
    Emitter<AuthenticationState> emit,
  ) async {
    switch (error.code) {
      case 'unauthenticated':
        add(const AuthenticationEvent.destroyedToken());
      case 'internet-out':
        await _handleNoInternetAccess(emit);
      default:
        emit(
          state.copyWith(
            message: error.message,
            status: AuthenticationStatus.unknown,
          ),
        );
    }
  }

  @protected
  Future<bool> safeEmit(
    Emitter<AuthenticationState> emit,
    AuthenticationState newState,
  ) async {
    if (!isClosed) {
      emit(newState);
      return true;
    }

    return false;
  }

  FutureOr<void> _onUpdatedUser(
    _UpdatedUser event,
    Emitter<AuthenticationState> emit,
  ) async {
    // login with onesignal/customerio only if user is not null
    if (event.user != null) {
      await _pushNotificationService.identify(
        externalId: event.user!.oneSignalPassKey.toString(),
        currentUser: event.user,
      );
    }

    emit(
      state.copyWith(
        user: event.user,
        status: event.user == null
            ? AuthenticationStatus.unauthenticated
            : AuthenticationStatus.authenticated,
      ),
    );
  }

  FutureOr<void> _onSignedIn(
    _SignedIn event,
    Emitter<AuthenticationState> emit,
  ) async {
    if (event.user == null) return; // Add null check

    // set token
    _tokenManager.set(event.token);

    add(AuthenticationEvent.updatedUser(user: event.user));
    // emit(
    //   state.copyWith(
    //     // user: event.user,
    //     status: AuthenticationStatus.unauthenticated,
    //   ),
    // );
  }

  /// Clears the cached country code from FroggyLocalStorage. Called on logout for privacy and correctness.
  void _clearCountryCodeCache() {
    final storage = FroggyLocalStorage.getInstance();
    storage.remove('cached_country_code');
    storage.remove('cached_country_code_ts');
    FroggyLogger.debug('[AuthenticationBloc] Country code cache cleared');
  }

  Future<void> _onDestroyedToken(
    _DestroyedToken event,
    Emitter<AuthenticationState> emit,
  ) async {
    try {
      emit(AuthenticationState.loading());

      unawaited(_logoutRepository.execute());

      // remove token
      _tokenManager.nuke();

      // login with onesignal
      unawaited(_pushNotificationService.logout());

      unawaited(clear());
      // Clear country code cache on logout
      _clearCountryCodeCache();

      // add(const AuthenticationEvent.updatedUser());
      emit(
        state.copyWith(
          user: null,
          status: AuthenticationStatus.unauthenticated,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          user: null,
          status: AuthenticationStatus.unauthenticated,
        ),
      );
    }
  }

  Future<void> _onSignedOut(
    _SignedOut event,
    Emitter<AuthenticationState> emit,
  ) async {
    emit(AuthenticationState.loading());
    // Clear country code cache on explicit sign out
    _clearCountryCodeCache();
    // add(const AuthenticationEvent.updatedUser());
    emit(
      state.copyWith(
        user: null,
        status: AuthenticationStatus.unauthenticated,
      ),
    );
  }

  FutureOr<void> _onInitial(_Initial event, Emitter<AuthenticationState> emit) {
    emit(AuthenticationState.initial());
  }

  FutureOr<void> _onEnabledOfflineMode(
    _EnabledOfflineMode event,
    Emitter<AuthenticationState> emit,
  ) {
    emit(
      state.copyWith(
        // message: 'No internet access, please wait ...',
        user: state.user,
        status: AuthenticationStatus.authenticated,
      ),
    );
  }
}
