part of 'send_otp_form_bloc.dart';

@freezed
class SendOtpFormEvent with _$SendOtpFormEvent {
  const factory SendOtpFormEvent.started({
    String? phoneNumber,
    String? countryCode,
    String? referralCode,
  }) = _Started;

  const factory SendOtpFormEvent.phoneNumberChanged({
    required String phoneNumber,
  }) = _PhoneNumberChanged;

  const factory SendOtpFormEvent.referralCodeChanged({
    String? referralCode,
  }) = _ReferralCodeChanged;

  const factory SendOtpFormEvent.countryCodeChanged({
    required CountryModel countryCode,
  }) = _CountryCodeChanged;

  const factory SendOtpFormEvent.submited() = _Submitted;

  const factory SendOtpFormEvent.resentOtp({
    required String phoneNumber,
    required String countryCode,
  }) = _ResentOtp;

  const factory SendOtpFormEvent.formErrorsResetted() = _ResetFormErrors;

  const factory SendOtpFormEvent.initializeCountry() = _InitializeCountry;
}
