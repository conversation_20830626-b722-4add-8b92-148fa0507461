// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'verify_otp_form_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$VerifyOtpFormEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? phoneNumber) started,
    required TResult Function(PhoneNumberData phoneNumberData)
        startedWithPhoneData,
    required TResult Function(String otp) otpChanged,
    required TResult Function(CountryModel? country) submited,
    required TResult Function(CountryModel? country) updateCountryModel,
    required TResult Function() formErrorsResetted,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? phoneNumber)? started,
    TResult? Function(PhoneNumberData phoneNumberData)? startedWithPhoneData,
    TResult? Function(String otp)? otpChanged,
    TResult? Function(CountryModel? country)? submited,
    TResult? Function(CountryModel? country)? updateCountryModel,
    TResult? Function()? formErrorsResetted,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? phoneNumber)? started,
    TResult Function(PhoneNumberData phoneNumberData)? startedWithPhoneData,
    TResult Function(String otp)? otpChanged,
    TResult Function(CountryModel? country)? submited,
    TResult Function(CountryModel? country)? updateCountryModel,
    TResult Function()? formErrorsResetted,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_StartedWithPhoneData value) startedWithPhoneData,
    required TResult Function(_OtpChanged value) otpChanged,
    required TResult Function(_Submitted value) submited,
    required TResult Function(_UpdateCountryModel value) updateCountryModel,
    required TResult Function(_ResetFormErrors value) formErrorsResetted,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_StartedWithPhoneData value)? startedWithPhoneData,
    TResult? Function(_OtpChanged value)? otpChanged,
    TResult? Function(_Submitted value)? submited,
    TResult? Function(_UpdateCountryModel value)? updateCountryModel,
    TResult? Function(_ResetFormErrors value)? formErrorsResetted,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_StartedWithPhoneData value)? startedWithPhoneData,
    TResult Function(_OtpChanged value)? otpChanged,
    TResult Function(_Submitted value)? submited,
    TResult Function(_UpdateCountryModel value)? updateCountryModel,
    TResult Function(_ResetFormErrors value)? formErrorsResetted,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VerifyOtpFormEventCopyWith<$Res> {
  factory $VerifyOtpFormEventCopyWith(
          VerifyOtpFormEvent value, $Res Function(VerifyOtpFormEvent) then) =
      _$VerifyOtpFormEventCopyWithImpl<$Res, VerifyOtpFormEvent>;
}

/// @nodoc
class _$VerifyOtpFormEventCopyWithImpl<$Res, $Val extends VerifyOtpFormEvent>
    implements $VerifyOtpFormEventCopyWith<$Res> {
  _$VerifyOtpFormEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VerifyOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$StartedImplCopyWith<$Res> {
  factory _$$StartedImplCopyWith(
          _$StartedImpl value, $Res Function(_$StartedImpl) then) =
      __$$StartedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? phoneNumber});
}

/// @nodoc
class __$$StartedImplCopyWithImpl<$Res>
    extends _$VerifyOtpFormEventCopyWithImpl<$Res, _$StartedImpl>
    implements _$$StartedImplCopyWith<$Res> {
  __$$StartedImplCopyWithImpl(
      _$StartedImpl _value, $Res Function(_$StartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of VerifyOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phoneNumber = freezed,
  }) {
    return _then(_$StartedImpl(
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$StartedImpl implements _Started {
  const _$StartedImpl({this.phoneNumber});

  @override
  final String? phoneNumber;

  @override
  String toString() {
    return 'VerifyOtpFormEvent.started(phoneNumber: $phoneNumber)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StartedImpl &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber));
  }

  @override
  int get hashCode => Object.hash(runtimeType, phoneNumber);

  /// Create a copy of VerifyOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StartedImplCopyWith<_$StartedImpl> get copyWith =>
      __$$StartedImplCopyWithImpl<_$StartedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? phoneNumber) started,
    required TResult Function(PhoneNumberData phoneNumberData)
        startedWithPhoneData,
    required TResult Function(String otp) otpChanged,
    required TResult Function(CountryModel? country) submited,
    required TResult Function(CountryModel? country) updateCountryModel,
    required TResult Function() formErrorsResetted,
  }) {
    return started(phoneNumber);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? phoneNumber)? started,
    TResult? Function(PhoneNumberData phoneNumberData)? startedWithPhoneData,
    TResult? Function(String otp)? otpChanged,
    TResult? Function(CountryModel? country)? submited,
    TResult? Function(CountryModel? country)? updateCountryModel,
    TResult? Function()? formErrorsResetted,
  }) {
    return started?.call(phoneNumber);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? phoneNumber)? started,
    TResult Function(PhoneNumberData phoneNumberData)? startedWithPhoneData,
    TResult Function(String otp)? otpChanged,
    TResult Function(CountryModel? country)? submited,
    TResult Function(CountryModel? country)? updateCountryModel,
    TResult Function()? formErrorsResetted,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started(phoneNumber);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_StartedWithPhoneData value) startedWithPhoneData,
    required TResult Function(_OtpChanged value) otpChanged,
    required TResult Function(_Submitted value) submited,
    required TResult Function(_UpdateCountryModel value) updateCountryModel,
    required TResult Function(_ResetFormErrors value) formErrorsResetted,
  }) {
    return started(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_StartedWithPhoneData value)? startedWithPhoneData,
    TResult? Function(_OtpChanged value)? otpChanged,
    TResult? Function(_Submitted value)? submited,
    TResult? Function(_UpdateCountryModel value)? updateCountryModel,
    TResult? Function(_ResetFormErrors value)? formErrorsResetted,
  }) {
    return started?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_StartedWithPhoneData value)? startedWithPhoneData,
    TResult Function(_OtpChanged value)? otpChanged,
    TResult Function(_Submitted value)? submited,
    TResult Function(_UpdateCountryModel value)? updateCountryModel,
    TResult Function(_ResetFormErrors value)? formErrorsResetted,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started(this);
    }
    return orElse();
  }
}

abstract class _Started implements VerifyOtpFormEvent {
  const factory _Started({final String? phoneNumber}) = _$StartedImpl;

  String? get phoneNumber;

  /// Create a copy of VerifyOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StartedImplCopyWith<_$StartedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$StartedWithPhoneDataImplCopyWith<$Res> {
  factory _$$StartedWithPhoneDataImplCopyWith(_$StartedWithPhoneDataImpl value,
          $Res Function(_$StartedWithPhoneDataImpl) then) =
      __$$StartedWithPhoneDataImplCopyWithImpl<$Res>;
  @useResult
  $Res call({PhoneNumberData phoneNumberData});

  $PhoneNumberDataCopyWith<$Res> get phoneNumberData;
}

/// @nodoc
class __$$StartedWithPhoneDataImplCopyWithImpl<$Res>
    extends _$VerifyOtpFormEventCopyWithImpl<$Res, _$StartedWithPhoneDataImpl>
    implements _$$StartedWithPhoneDataImplCopyWith<$Res> {
  __$$StartedWithPhoneDataImplCopyWithImpl(_$StartedWithPhoneDataImpl _value,
      $Res Function(_$StartedWithPhoneDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of VerifyOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phoneNumberData = null,
  }) {
    return _then(_$StartedWithPhoneDataImpl(
      phoneNumberData: null == phoneNumberData
          ? _value.phoneNumberData
          : phoneNumberData // ignore: cast_nullable_to_non_nullable
              as PhoneNumberData,
    ));
  }

  /// Create a copy of VerifyOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PhoneNumberDataCopyWith<$Res> get phoneNumberData {
    return $PhoneNumberDataCopyWith<$Res>(_value.phoneNumberData, (value) {
      return _then(_value.copyWith(phoneNumberData: value));
    });
  }
}

/// @nodoc

class _$StartedWithPhoneDataImpl implements _StartedWithPhoneData {
  const _$StartedWithPhoneDataImpl({required this.phoneNumberData});

  @override
  final PhoneNumberData phoneNumberData;

  @override
  String toString() {
    return 'VerifyOtpFormEvent.startedWithPhoneData(phoneNumberData: $phoneNumberData)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StartedWithPhoneDataImpl &&
            (identical(other.phoneNumberData, phoneNumberData) ||
                other.phoneNumberData == phoneNumberData));
  }

  @override
  int get hashCode => Object.hash(runtimeType, phoneNumberData);

  /// Create a copy of VerifyOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StartedWithPhoneDataImplCopyWith<_$StartedWithPhoneDataImpl>
      get copyWith =>
          __$$StartedWithPhoneDataImplCopyWithImpl<_$StartedWithPhoneDataImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? phoneNumber) started,
    required TResult Function(PhoneNumberData phoneNumberData)
        startedWithPhoneData,
    required TResult Function(String otp) otpChanged,
    required TResult Function(CountryModel? country) submited,
    required TResult Function(CountryModel? country) updateCountryModel,
    required TResult Function() formErrorsResetted,
  }) {
    return startedWithPhoneData(phoneNumberData);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? phoneNumber)? started,
    TResult? Function(PhoneNumberData phoneNumberData)? startedWithPhoneData,
    TResult? Function(String otp)? otpChanged,
    TResult? Function(CountryModel? country)? submited,
    TResult? Function(CountryModel? country)? updateCountryModel,
    TResult? Function()? formErrorsResetted,
  }) {
    return startedWithPhoneData?.call(phoneNumberData);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? phoneNumber)? started,
    TResult Function(PhoneNumberData phoneNumberData)? startedWithPhoneData,
    TResult Function(String otp)? otpChanged,
    TResult Function(CountryModel? country)? submited,
    TResult Function(CountryModel? country)? updateCountryModel,
    TResult Function()? formErrorsResetted,
    required TResult orElse(),
  }) {
    if (startedWithPhoneData != null) {
      return startedWithPhoneData(phoneNumberData);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_StartedWithPhoneData value) startedWithPhoneData,
    required TResult Function(_OtpChanged value) otpChanged,
    required TResult Function(_Submitted value) submited,
    required TResult Function(_UpdateCountryModel value) updateCountryModel,
    required TResult Function(_ResetFormErrors value) formErrorsResetted,
  }) {
    return startedWithPhoneData(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_StartedWithPhoneData value)? startedWithPhoneData,
    TResult? Function(_OtpChanged value)? otpChanged,
    TResult? Function(_Submitted value)? submited,
    TResult? Function(_UpdateCountryModel value)? updateCountryModel,
    TResult? Function(_ResetFormErrors value)? formErrorsResetted,
  }) {
    return startedWithPhoneData?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_StartedWithPhoneData value)? startedWithPhoneData,
    TResult Function(_OtpChanged value)? otpChanged,
    TResult Function(_Submitted value)? submited,
    TResult Function(_UpdateCountryModel value)? updateCountryModel,
    TResult Function(_ResetFormErrors value)? formErrorsResetted,
    required TResult orElse(),
  }) {
    if (startedWithPhoneData != null) {
      return startedWithPhoneData(this);
    }
    return orElse();
  }
}

abstract class _StartedWithPhoneData implements VerifyOtpFormEvent {
  const factory _StartedWithPhoneData(
          {required final PhoneNumberData phoneNumberData}) =
      _$StartedWithPhoneDataImpl;

  PhoneNumberData get phoneNumberData;

  /// Create a copy of VerifyOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StartedWithPhoneDataImplCopyWith<_$StartedWithPhoneDataImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$OtpChangedImplCopyWith<$Res> {
  factory _$$OtpChangedImplCopyWith(
          _$OtpChangedImpl value, $Res Function(_$OtpChangedImpl) then) =
      __$$OtpChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String otp});
}

/// @nodoc
class __$$OtpChangedImplCopyWithImpl<$Res>
    extends _$VerifyOtpFormEventCopyWithImpl<$Res, _$OtpChangedImpl>
    implements _$$OtpChangedImplCopyWith<$Res> {
  __$$OtpChangedImplCopyWithImpl(
      _$OtpChangedImpl _value, $Res Function(_$OtpChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of VerifyOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? otp = null,
  }) {
    return _then(_$OtpChangedImpl(
      otp: null == otp
          ? _value.otp
          : otp // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$OtpChangedImpl implements _OtpChanged {
  const _$OtpChangedImpl({required this.otp});

  @override
  final String otp;

  @override
  String toString() {
    return 'VerifyOtpFormEvent.otpChanged(otp: $otp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OtpChangedImpl &&
            (identical(other.otp, otp) || other.otp == otp));
  }

  @override
  int get hashCode => Object.hash(runtimeType, otp);

  /// Create a copy of VerifyOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OtpChangedImplCopyWith<_$OtpChangedImpl> get copyWith =>
      __$$OtpChangedImplCopyWithImpl<_$OtpChangedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? phoneNumber) started,
    required TResult Function(PhoneNumberData phoneNumberData)
        startedWithPhoneData,
    required TResult Function(String otp) otpChanged,
    required TResult Function(CountryModel? country) submited,
    required TResult Function(CountryModel? country) updateCountryModel,
    required TResult Function() formErrorsResetted,
  }) {
    return otpChanged(otp);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? phoneNumber)? started,
    TResult? Function(PhoneNumberData phoneNumberData)? startedWithPhoneData,
    TResult? Function(String otp)? otpChanged,
    TResult? Function(CountryModel? country)? submited,
    TResult? Function(CountryModel? country)? updateCountryModel,
    TResult? Function()? formErrorsResetted,
  }) {
    return otpChanged?.call(otp);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? phoneNumber)? started,
    TResult Function(PhoneNumberData phoneNumberData)? startedWithPhoneData,
    TResult Function(String otp)? otpChanged,
    TResult Function(CountryModel? country)? submited,
    TResult Function(CountryModel? country)? updateCountryModel,
    TResult Function()? formErrorsResetted,
    required TResult orElse(),
  }) {
    if (otpChanged != null) {
      return otpChanged(otp);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_StartedWithPhoneData value) startedWithPhoneData,
    required TResult Function(_OtpChanged value) otpChanged,
    required TResult Function(_Submitted value) submited,
    required TResult Function(_UpdateCountryModel value) updateCountryModel,
    required TResult Function(_ResetFormErrors value) formErrorsResetted,
  }) {
    return otpChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_StartedWithPhoneData value)? startedWithPhoneData,
    TResult? Function(_OtpChanged value)? otpChanged,
    TResult? Function(_Submitted value)? submited,
    TResult? Function(_UpdateCountryModel value)? updateCountryModel,
    TResult? Function(_ResetFormErrors value)? formErrorsResetted,
  }) {
    return otpChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_StartedWithPhoneData value)? startedWithPhoneData,
    TResult Function(_OtpChanged value)? otpChanged,
    TResult Function(_Submitted value)? submited,
    TResult Function(_UpdateCountryModel value)? updateCountryModel,
    TResult Function(_ResetFormErrors value)? formErrorsResetted,
    required TResult orElse(),
  }) {
    if (otpChanged != null) {
      return otpChanged(this);
    }
    return orElse();
  }
}

abstract class _OtpChanged implements VerifyOtpFormEvent {
  const factory _OtpChanged({required final String otp}) = _$OtpChangedImpl;

  String get otp;

  /// Create a copy of VerifyOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OtpChangedImplCopyWith<_$OtpChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SubmittedImplCopyWith<$Res> {
  factory _$$SubmittedImplCopyWith(
          _$SubmittedImpl value, $Res Function(_$SubmittedImpl) then) =
      __$$SubmittedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({CountryModel? country});

  $CountryModelCopyWith<$Res>? get country;
}

/// @nodoc
class __$$SubmittedImplCopyWithImpl<$Res>
    extends _$VerifyOtpFormEventCopyWithImpl<$Res, _$SubmittedImpl>
    implements _$$SubmittedImplCopyWith<$Res> {
  __$$SubmittedImplCopyWithImpl(
      _$SubmittedImpl _value, $Res Function(_$SubmittedImpl) _then)
      : super(_value, _then);

  /// Create a copy of VerifyOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? country = freezed,
  }) {
    return _then(_$SubmittedImpl(
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as CountryModel?,
    ));
  }

  /// Create a copy of VerifyOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CountryModelCopyWith<$Res>? get country {
    if (_value.country == null) {
      return null;
    }

    return $CountryModelCopyWith<$Res>(_value.country!, (value) {
      return _then(_value.copyWith(country: value));
    });
  }
}

/// @nodoc

class _$SubmittedImpl implements _Submitted {
  const _$SubmittedImpl({this.country});

  @override
  final CountryModel? country;

  @override
  String toString() {
    return 'VerifyOtpFormEvent.submited(country: $country)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SubmittedImpl &&
            (identical(other.country, country) || other.country == country));
  }

  @override
  int get hashCode => Object.hash(runtimeType, country);

  /// Create a copy of VerifyOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SubmittedImplCopyWith<_$SubmittedImpl> get copyWith =>
      __$$SubmittedImplCopyWithImpl<_$SubmittedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? phoneNumber) started,
    required TResult Function(PhoneNumberData phoneNumberData)
        startedWithPhoneData,
    required TResult Function(String otp) otpChanged,
    required TResult Function(CountryModel? country) submited,
    required TResult Function(CountryModel? country) updateCountryModel,
    required TResult Function() formErrorsResetted,
  }) {
    return submited(country);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? phoneNumber)? started,
    TResult? Function(PhoneNumberData phoneNumberData)? startedWithPhoneData,
    TResult? Function(String otp)? otpChanged,
    TResult? Function(CountryModel? country)? submited,
    TResult? Function(CountryModel? country)? updateCountryModel,
    TResult? Function()? formErrorsResetted,
  }) {
    return submited?.call(country);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? phoneNumber)? started,
    TResult Function(PhoneNumberData phoneNumberData)? startedWithPhoneData,
    TResult Function(String otp)? otpChanged,
    TResult Function(CountryModel? country)? submited,
    TResult Function(CountryModel? country)? updateCountryModel,
    TResult Function()? formErrorsResetted,
    required TResult orElse(),
  }) {
    if (submited != null) {
      return submited(country);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_StartedWithPhoneData value) startedWithPhoneData,
    required TResult Function(_OtpChanged value) otpChanged,
    required TResult Function(_Submitted value) submited,
    required TResult Function(_UpdateCountryModel value) updateCountryModel,
    required TResult Function(_ResetFormErrors value) formErrorsResetted,
  }) {
    return submited(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_StartedWithPhoneData value)? startedWithPhoneData,
    TResult? Function(_OtpChanged value)? otpChanged,
    TResult? Function(_Submitted value)? submited,
    TResult? Function(_UpdateCountryModel value)? updateCountryModel,
    TResult? Function(_ResetFormErrors value)? formErrorsResetted,
  }) {
    return submited?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_StartedWithPhoneData value)? startedWithPhoneData,
    TResult Function(_OtpChanged value)? otpChanged,
    TResult Function(_Submitted value)? submited,
    TResult Function(_UpdateCountryModel value)? updateCountryModel,
    TResult Function(_ResetFormErrors value)? formErrorsResetted,
    required TResult orElse(),
  }) {
    if (submited != null) {
      return submited(this);
    }
    return orElse();
  }
}

abstract class _Submitted implements VerifyOtpFormEvent {
  const factory _Submitted({final CountryModel? country}) = _$SubmittedImpl;

  CountryModel? get country;

  /// Create a copy of VerifyOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SubmittedImplCopyWith<_$SubmittedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateCountryModelImplCopyWith<$Res> {
  factory _$$UpdateCountryModelImplCopyWith(_$UpdateCountryModelImpl value,
          $Res Function(_$UpdateCountryModelImpl) then) =
      __$$UpdateCountryModelImplCopyWithImpl<$Res>;
  @useResult
  $Res call({CountryModel? country});

  $CountryModelCopyWith<$Res>? get country;
}

/// @nodoc
class __$$UpdateCountryModelImplCopyWithImpl<$Res>
    extends _$VerifyOtpFormEventCopyWithImpl<$Res, _$UpdateCountryModelImpl>
    implements _$$UpdateCountryModelImplCopyWith<$Res> {
  __$$UpdateCountryModelImplCopyWithImpl(_$UpdateCountryModelImpl _value,
      $Res Function(_$UpdateCountryModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of VerifyOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? country = freezed,
  }) {
    return _then(_$UpdateCountryModelImpl(
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as CountryModel?,
    ));
  }

  /// Create a copy of VerifyOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CountryModelCopyWith<$Res>? get country {
    if (_value.country == null) {
      return null;
    }

    return $CountryModelCopyWith<$Res>(_value.country!, (value) {
      return _then(_value.copyWith(country: value));
    });
  }
}

/// @nodoc

class _$UpdateCountryModelImpl implements _UpdateCountryModel {
  const _$UpdateCountryModelImpl({this.country});

  @override
  final CountryModel? country;

  @override
  String toString() {
    return 'VerifyOtpFormEvent.updateCountryModel(country: $country)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateCountryModelImpl &&
            (identical(other.country, country) || other.country == country));
  }

  @override
  int get hashCode => Object.hash(runtimeType, country);

  /// Create a copy of VerifyOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateCountryModelImplCopyWith<_$UpdateCountryModelImpl> get copyWith =>
      __$$UpdateCountryModelImplCopyWithImpl<_$UpdateCountryModelImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? phoneNumber) started,
    required TResult Function(PhoneNumberData phoneNumberData)
        startedWithPhoneData,
    required TResult Function(String otp) otpChanged,
    required TResult Function(CountryModel? country) submited,
    required TResult Function(CountryModel? country) updateCountryModel,
    required TResult Function() formErrorsResetted,
  }) {
    return updateCountryModel(country);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? phoneNumber)? started,
    TResult? Function(PhoneNumberData phoneNumberData)? startedWithPhoneData,
    TResult? Function(String otp)? otpChanged,
    TResult? Function(CountryModel? country)? submited,
    TResult? Function(CountryModel? country)? updateCountryModel,
    TResult? Function()? formErrorsResetted,
  }) {
    return updateCountryModel?.call(country);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? phoneNumber)? started,
    TResult Function(PhoneNumberData phoneNumberData)? startedWithPhoneData,
    TResult Function(String otp)? otpChanged,
    TResult Function(CountryModel? country)? submited,
    TResult Function(CountryModel? country)? updateCountryModel,
    TResult Function()? formErrorsResetted,
    required TResult orElse(),
  }) {
    if (updateCountryModel != null) {
      return updateCountryModel(country);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_StartedWithPhoneData value) startedWithPhoneData,
    required TResult Function(_OtpChanged value) otpChanged,
    required TResult Function(_Submitted value) submited,
    required TResult Function(_UpdateCountryModel value) updateCountryModel,
    required TResult Function(_ResetFormErrors value) formErrorsResetted,
  }) {
    return updateCountryModel(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_StartedWithPhoneData value)? startedWithPhoneData,
    TResult? Function(_OtpChanged value)? otpChanged,
    TResult? Function(_Submitted value)? submited,
    TResult? Function(_UpdateCountryModel value)? updateCountryModel,
    TResult? Function(_ResetFormErrors value)? formErrorsResetted,
  }) {
    return updateCountryModel?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_StartedWithPhoneData value)? startedWithPhoneData,
    TResult Function(_OtpChanged value)? otpChanged,
    TResult Function(_Submitted value)? submited,
    TResult Function(_UpdateCountryModel value)? updateCountryModel,
    TResult Function(_ResetFormErrors value)? formErrorsResetted,
    required TResult orElse(),
  }) {
    if (updateCountryModel != null) {
      return updateCountryModel(this);
    }
    return orElse();
  }
}

abstract class _UpdateCountryModel implements VerifyOtpFormEvent {
  const factory _UpdateCountryModel({final CountryModel? country}) =
      _$UpdateCountryModelImpl;

  CountryModel? get country;

  /// Create a copy of VerifyOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateCountryModelImplCopyWith<_$UpdateCountryModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ResetFormErrorsImplCopyWith<$Res> {
  factory _$$ResetFormErrorsImplCopyWith(_$ResetFormErrorsImpl value,
          $Res Function(_$ResetFormErrorsImpl) then) =
      __$$ResetFormErrorsImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ResetFormErrorsImplCopyWithImpl<$Res>
    extends _$VerifyOtpFormEventCopyWithImpl<$Res, _$ResetFormErrorsImpl>
    implements _$$ResetFormErrorsImplCopyWith<$Res> {
  __$$ResetFormErrorsImplCopyWithImpl(
      _$ResetFormErrorsImpl _value, $Res Function(_$ResetFormErrorsImpl) _then)
      : super(_value, _then);

  /// Create a copy of VerifyOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ResetFormErrorsImpl implements _ResetFormErrors {
  const _$ResetFormErrorsImpl();

  @override
  String toString() {
    return 'VerifyOtpFormEvent.formErrorsResetted()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ResetFormErrorsImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String? phoneNumber) started,
    required TResult Function(PhoneNumberData phoneNumberData)
        startedWithPhoneData,
    required TResult Function(String otp) otpChanged,
    required TResult Function(CountryModel? country) submited,
    required TResult Function(CountryModel? country) updateCountryModel,
    required TResult Function() formErrorsResetted,
  }) {
    return formErrorsResetted();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String? phoneNumber)? started,
    TResult? Function(PhoneNumberData phoneNumberData)? startedWithPhoneData,
    TResult? Function(String otp)? otpChanged,
    TResult? Function(CountryModel? country)? submited,
    TResult? Function(CountryModel? country)? updateCountryModel,
    TResult? Function()? formErrorsResetted,
  }) {
    return formErrorsResetted?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String? phoneNumber)? started,
    TResult Function(PhoneNumberData phoneNumberData)? startedWithPhoneData,
    TResult Function(String otp)? otpChanged,
    TResult Function(CountryModel? country)? submited,
    TResult Function(CountryModel? country)? updateCountryModel,
    TResult Function()? formErrorsResetted,
    required TResult orElse(),
  }) {
    if (formErrorsResetted != null) {
      return formErrorsResetted();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_StartedWithPhoneData value) startedWithPhoneData,
    required TResult Function(_OtpChanged value) otpChanged,
    required TResult Function(_Submitted value) submited,
    required TResult Function(_UpdateCountryModel value) updateCountryModel,
    required TResult Function(_ResetFormErrors value) formErrorsResetted,
  }) {
    return formErrorsResetted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_StartedWithPhoneData value)? startedWithPhoneData,
    TResult? Function(_OtpChanged value)? otpChanged,
    TResult? Function(_Submitted value)? submited,
    TResult? Function(_UpdateCountryModel value)? updateCountryModel,
    TResult? Function(_ResetFormErrors value)? formErrorsResetted,
  }) {
    return formErrorsResetted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_StartedWithPhoneData value)? startedWithPhoneData,
    TResult Function(_OtpChanged value)? otpChanged,
    TResult Function(_Submitted value)? submited,
    TResult Function(_UpdateCountryModel value)? updateCountryModel,
    TResult Function(_ResetFormErrors value)? formErrorsResetted,
    required TResult orElse(),
  }) {
    if (formErrorsResetted != null) {
      return formErrorsResetted(this);
    }
    return orElse();
  }
}

abstract class _ResetFormErrors implements VerifyOtpFormEvent {
  const factory _ResetFormErrors() = _$ResetFormErrorsImpl;
}

/// @nodoc
mixin _$VerifyOtpFormState {
  MinimumInput get otp => throw _privateConstructorUsedError;
  String? get phoneNumber => throw _privateConstructorUsedError;
  CountryModel? get country => throw _privateConstructorUsedError;
  String? get otpErrorMessage => throw _privateConstructorUsedError;
  String? get phoneNumberErrorMessage => throw _privateConstructorUsedError;
  String? get countryCodeErrorMessage => throw _privateConstructorUsedError;
  String? get message => throw _privateConstructorUsedError;
  String? get token => throw _privateConstructorUsedError;
  UserAttributesModel? get user => throw _privateConstructorUsedError;
  FormzSubmissionStatus get status => throw _privateConstructorUsedError;

  /// Standardized phone number data for consistent handling
  PhoneNumberData? get phoneNumberData => throw _privateConstructorUsedError;

  /// Create a copy of VerifyOtpFormState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VerifyOtpFormStateCopyWith<VerifyOtpFormState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VerifyOtpFormStateCopyWith<$Res> {
  factory $VerifyOtpFormStateCopyWith(
          VerifyOtpFormState value, $Res Function(VerifyOtpFormState) then) =
      _$VerifyOtpFormStateCopyWithImpl<$Res, VerifyOtpFormState>;
  @useResult
  $Res call(
      {MinimumInput otp,
      String? phoneNumber,
      CountryModel? country,
      String? otpErrorMessage,
      String? phoneNumberErrorMessage,
      String? countryCodeErrorMessage,
      String? message,
      String? token,
      UserAttributesModel? user,
      FormzSubmissionStatus status,
      PhoneNumberData? phoneNumberData});

  $CountryModelCopyWith<$Res>? get country;
  $UserAttributesModelCopyWith<$Res>? get user;
  $PhoneNumberDataCopyWith<$Res>? get phoneNumberData;
}

/// @nodoc
class _$VerifyOtpFormStateCopyWithImpl<$Res, $Val extends VerifyOtpFormState>
    implements $VerifyOtpFormStateCopyWith<$Res> {
  _$VerifyOtpFormStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VerifyOtpFormState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? otp = null,
    Object? phoneNumber = freezed,
    Object? country = freezed,
    Object? otpErrorMessage = freezed,
    Object? phoneNumberErrorMessage = freezed,
    Object? countryCodeErrorMessage = freezed,
    Object? message = freezed,
    Object? token = freezed,
    Object? user = freezed,
    Object? status = null,
    Object? phoneNumberData = freezed,
  }) {
    return _then(_value.copyWith(
      otp: null == otp
          ? _value.otp
          : otp // ignore: cast_nullable_to_non_nullable
              as MinimumInput,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as CountryModel?,
      otpErrorMessage: freezed == otpErrorMessage
          ? _value.otpErrorMessage
          : otpErrorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumberErrorMessage: freezed == phoneNumberErrorMessage
          ? _value.phoneNumberErrorMessage
          : phoneNumberErrorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      countryCodeErrorMessage: freezed == countryCodeErrorMessage
          ? _value.countryCodeErrorMessage
          : countryCodeErrorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      token: freezed == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String?,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as UserAttributesModel?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as FormzSubmissionStatus,
      phoneNumberData: freezed == phoneNumberData
          ? _value.phoneNumberData
          : phoneNumberData // ignore: cast_nullable_to_non_nullable
              as PhoneNumberData?,
    ) as $Val);
  }

  /// Create a copy of VerifyOtpFormState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CountryModelCopyWith<$Res>? get country {
    if (_value.country == null) {
      return null;
    }

    return $CountryModelCopyWith<$Res>(_value.country!, (value) {
      return _then(_value.copyWith(country: value) as $Val);
    });
  }

  /// Create a copy of VerifyOtpFormState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserAttributesModelCopyWith<$Res>? get user {
    if (_value.user == null) {
      return null;
    }

    return $UserAttributesModelCopyWith<$Res>(_value.user!, (value) {
      return _then(_value.copyWith(user: value) as $Val);
    });
  }

  /// Create a copy of VerifyOtpFormState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PhoneNumberDataCopyWith<$Res>? get phoneNumberData {
    if (_value.phoneNumberData == null) {
      return null;
    }

    return $PhoneNumberDataCopyWith<$Res>(_value.phoneNumberData!, (value) {
      return _then(_value.copyWith(phoneNumberData: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$VerifyOtpFormStateImplCopyWith<$Res>
    implements $VerifyOtpFormStateCopyWith<$Res> {
  factory _$$VerifyOtpFormStateImplCopyWith(_$VerifyOtpFormStateImpl value,
          $Res Function(_$VerifyOtpFormStateImpl) then) =
      __$$VerifyOtpFormStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {MinimumInput otp,
      String? phoneNumber,
      CountryModel? country,
      String? otpErrorMessage,
      String? phoneNumberErrorMessage,
      String? countryCodeErrorMessage,
      String? message,
      String? token,
      UserAttributesModel? user,
      FormzSubmissionStatus status,
      PhoneNumberData? phoneNumberData});

  @override
  $CountryModelCopyWith<$Res>? get country;
  @override
  $UserAttributesModelCopyWith<$Res>? get user;
  @override
  $PhoneNumberDataCopyWith<$Res>? get phoneNumberData;
}

/// @nodoc
class __$$VerifyOtpFormStateImplCopyWithImpl<$Res>
    extends _$VerifyOtpFormStateCopyWithImpl<$Res, _$VerifyOtpFormStateImpl>
    implements _$$VerifyOtpFormStateImplCopyWith<$Res> {
  __$$VerifyOtpFormStateImplCopyWithImpl(_$VerifyOtpFormStateImpl _value,
      $Res Function(_$VerifyOtpFormStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of VerifyOtpFormState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? otp = null,
    Object? phoneNumber = freezed,
    Object? country = freezed,
    Object? otpErrorMessage = freezed,
    Object? phoneNumberErrorMessage = freezed,
    Object? countryCodeErrorMessage = freezed,
    Object? message = freezed,
    Object? token = freezed,
    Object? user = freezed,
    Object? status = null,
    Object? phoneNumberData = freezed,
  }) {
    return _then(_$VerifyOtpFormStateImpl(
      otp: null == otp
          ? _value.otp
          : otp // ignore: cast_nullable_to_non_nullable
              as MinimumInput,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as CountryModel?,
      otpErrorMessage: freezed == otpErrorMessage
          ? _value.otpErrorMessage
          : otpErrorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumberErrorMessage: freezed == phoneNumberErrorMessage
          ? _value.phoneNumberErrorMessage
          : phoneNumberErrorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      countryCodeErrorMessage: freezed == countryCodeErrorMessage
          ? _value.countryCodeErrorMessage
          : countryCodeErrorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      token: freezed == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String?,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as UserAttributesModel?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as FormzSubmissionStatus,
      phoneNumberData: freezed == phoneNumberData
          ? _value.phoneNumberData
          : phoneNumberData // ignore: cast_nullable_to_non_nullable
              as PhoneNumberData?,
    ));
  }
}

/// @nodoc

class _$VerifyOtpFormStateImpl extends _VerifyOtpFormState {
  _$VerifyOtpFormStateImpl(
      {this.otp = const MinimumInput.pure(),
      this.phoneNumber,
      this.country,
      this.otpErrorMessage,
      this.phoneNumberErrorMessage,
      this.countryCodeErrorMessage,
      this.message,
      this.token,
      this.user,
      this.status = FormzSubmissionStatus.initial,
      this.phoneNumberData})
      : super._();

  @override
  @JsonKey()
  final MinimumInput otp;
  @override
  final String? phoneNumber;
  @override
  final CountryModel? country;
  @override
  final String? otpErrorMessage;
  @override
  final String? phoneNumberErrorMessage;
  @override
  final String? countryCodeErrorMessage;
  @override
  final String? message;
  @override
  final String? token;
  @override
  final UserAttributesModel? user;
  @override
  @JsonKey()
  final FormzSubmissionStatus status;

  /// Standardized phone number data for consistent handling
  @override
  final PhoneNumberData? phoneNumberData;

  @override
  String toString() {
    return 'VerifyOtpFormState(otp: $otp, phoneNumber: $phoneNumber, country: $country, otpErrorMessage: $otpErrorMessage, phoneNumberErrorMessage: $phoneNumberErrorMessage, countryCodeErrorMessage: $countryCodeErrorMessage, message: $message, token: $token, user: $user, status: $status, phoneNumberData: $phoneNumberData)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerifyOtpFormStateImpl &&
            (identical(other.otp, otp) || other.otp == otp) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.otpErrorMessage, otpErrorMessage) ||
                other.otpErrorMessage == otpErrorMessage) &&
            (identical(
                    other.phoneNumberErrorMessage, phoneNumberErrorMessage) ||
                other.phoneNumberErrorMessage == phoneNumberErrorMessage) &&
            (identical(
                    other.countryCodeErrorMessage, countryCodeErrorMessage) ||
                other.countryCodeErrorMessage == countryCodeErrorMessage) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.token, token) || other.token == token) &&
            (identical(other.user, user) || other.user == user) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.phoneNumberData, phoneNumberData) ||
                other.phoneNumberData == phoneNumberData));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      otp,
      phoneNumber,
      country,
      otpErrorMessage,
      phoneNumberErrorMessage,
      countryCodeErrorMessage,
      message,
      token,
      user,
      status,
      phoneNumberData);

  /// Create a copy of VerifyOtpFormState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VerifyOtpFormStateImplCopyWith<_$VerifyOtpFormStateImpl> get copyWith =>
      __$$VerifyOtpFormStateImplCopyWithImpl<_$VerifyOtpFormStateImpl>(
          this, _$identity);
}

abstract class _VerifyOtpFormState extends VerifyOtpFormState {
  factory _VerifyOtpFormState(
      {final MinimumInput otp,
      final String? phoneNumber,
      final CountryModel? country,
      final String? otpErrorMessage,
      final String? phoneNumberErrorMessage,
      final String? countryCodeErrorMessage,
      final String? message,
      final String? token,
      final UserAttributesModel? user,
      final FormzSubmissionStatus status,
      final PhoneNumberData? phoneNumberData}) = _$VerifyOtpFormStateImpl;
  _VerifyOtpFormState._() : super._();

  @override
  MinimumInput get otp;
  @override
  String? get phoneNumber;
  @override
  CountryModel? get country;
  @override
  String? get otpErrorMessage;
  @override
  String? get phoneNumberErrorMessage;
  @override
  String? get countryCodeErrorMessage;
  @override
  String? get message;
  @override
  String? get token;
  @override
  UserAttributesModel? get user;
  @override
  FormzSubmissionStatus get status;

  /// Standardized phone number data for consistent handling
  @override
  PhoneNumberData? get phoneNumberData;

  /// Create a copy of VerifyOtpFormState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VerifyOtpFormStateImplCopyWith<_$VerifyOtpFormStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
