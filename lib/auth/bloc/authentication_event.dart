part of 'authentication_bloc.dart';

@freezed
class AuthenticationEvent with _$AuthenticationEvent {
  const factory AuthenticationEvent.refreshProfile({
    @Default(false) bool updateProfileMetadata,
  }) = _Started;

  const factory AuthenticationEvent.reset() = _Initial;

  const factory AuthenticationEvent.updatedUser({
    UserAttributesModel? user,
  }) = _UpdatedUser;

  const factory AuthenticationEvent.signedOut() = _SignedOut;

  const factory AuthenticationEvent.destroyedToken() = _DestroyedToken;

  const factory AuthenticationEvent.welcomeBack({
    @Default(WelcomeBackMode.proceedToHome) WelcomeBackMode mode,
    @Default(AuthenticationStatus.unknown) AuthenticationStatus status,
    UserAttributesModel? user,
    String? message,
  }) = _WelcomeBack;

  const factory AuthenticationEvent.signedIn({
    required String token,
    UserAttributesModel? user,
  }) = _SignedIn;

  const factory AuthenticationEvent.enabledOfflineMode() = _EnabledOfflineMode;
}
