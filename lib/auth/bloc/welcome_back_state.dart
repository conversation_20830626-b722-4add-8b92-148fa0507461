part of 'welcome_back_bloc.dart';

@freezed
class WelcomeBackState with _$WelcomeBackState {
  const factory WelcomeBackState.initial() = WelcomeBackInitial;
  const factory WelcomeBackState.possible() = WelcomeBackCanAuthenticateLocally;
  const factory WelcomeBackState.impossible() =
      WelcomeBackCanNotAuthenticateLocally;
  const factory WelcomeBackState.localAuthSuccess() =
      WelcomeBackAuthenticationSuccessful;
  const factory WelcomeBackState.localAuthFailed() =
      WelcomeBackAuthenticationFailed;
}
