import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:countries/countries.dart';
import 'package:data/data.dart';
import 'package:formz/formz.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:froggytalk/app/data/services/event_tracker.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:froggytalk/auth/data/models/phone_number_data.dart';
import 'package:froggytalk/contacts/data/services/phone_number_utils.dart';
import 'package:froggytalk/profile/profile.dart';
import 'package:utils/utils.dart';

part 'verify_otp_form_bloc.freezed.dart';
part 'verify_otp_form_event.dart';
part 'verify_otp_form_state.dart';

class VerifyOtpFormBloc extends Bloc<VerifyOtpFormEvent, VerifyOtpFormState> {
  VerifyOtpFormBloc() : super(VerifyOtpFormState.initial()) {
    on<_Started>(_onStarted);
    on<_StartedWithPhoneData>(_onStartedWithPhoneData);
    on<_UpdateCountryModel>(_onUpdatedCountryModel);
    on<_OtpChanged>(
      _onOtpChanged,
      transformer: debounce(),
    );
    on<_Submitted>(_onSubmitted);
  }

  final _verifyOtpRepository = VerifyOtpRepository();
  final _eventTracker = EventTrackerService.getInstance();

  FutureOr<void> _onStarted(
    _Started event, 
    Emitter<VerifyOtpFormState> emit,
  ) {
    // Create PhoneNumberData if only a phone number string is provided 
    // (legacy support)
    PhoneNumberData? phoneNumberData;
    if (event.phoneNumber != null && 
        event.phoneNumber!.isNotEmpty) {
      // For legacy compatibility, attempt to parse the phone number 
      // with NL as default (more sensible than US)
      const countryCode = 'nl'; // More sensible default country code
      
      phoneNumberData = PhoneNumberData.fromInput(
        rawInput: event.phoneNumber!,
        countryCode: countryCode,
      );
    }
    
    emit(
      state.copyWith(
        otp: const MinimumInput.dirty(''),
        phoneNumber: event.phoneNumber ?? '',
        phoneNumberData: phoneNumberData,
        otpErrorMessage: null,
        phoneNumberErrorMessage: null,
        message: null,
        status: FormzSubmissionStatus.initial,
      ),
    );
  }

  /// Enhanced handler for standardized phone number data
  FutureOr<void> _onStartedWithPhoneData(
    _StartedWithPhoneData event, 
    Emitter<VerifyOtpFormState> emit,
  ) {
    final phoneData = event.phoneNumberData;
    
    emit(
      state.copyWith(
        otp: const MinimumInput.dirty(''),
        phoneNumber: phoneData.formattedNumber ?? phoneData.rawInput,
        phoneNumberData: phoneData,
        country: phoneData.country,
        otpErrorMessage: null,
        phoneNumberErrorMessage: phoneData.errorMessage,
        message: null,
        status: FormzSubmissionStatus.initial,
      ),
    );
  }

  FutureOr<void> _onOtpChanged(
    _OtpChanged event,
    Emitter<VerifyOtpFormState> emit,
  ) {
    final otp = MinimumInput.dirty(event.otp, title: 'OTP');
    emit(
      state.copyWith(
        status: FormzSubmissionStatus.initial,
        otp: otp,
        // otpErrorMessage: otp.errorMessage,
      ),
    );

    if (otp.displayError == null && state.phoneNumber != null) {
      add(const _Submitted());
    }
  }

  FutureOr<void> _onSubmitted(
    _Submitted event,
    Emitter<VerifyOtpFormState> emit,
  ) async {
    if (!state.isValid) {
      emit(
        state.copyWith(
          status: FormzSubmissionStatus.failure,
          message: 'Invalid OTP',
        ),
      );

      return;
    }

    emit(
      state.copyWith(
        status: FormzSubmissionStatus.inProgress,
      ),
    );

    // Use standardized phone number data if available, 
    // otherwise fallback to legacy logic
    String nationalNumber;
    String countryCode;
    
    if (state.phoneNumberData != null && 
        state.phoneNumberData!.isValid) {
      nationalNumber = state.phoneNumberData!.nationalNumber;
      countryCode = state.phoneNumberData!.countryCode;
    } else {
      // Legacy fallback logic - use NL as more sensible fallback than US
      final countryModel = state.country ?? event.country;
      countryCode = countryModel?.code ?? 'nl';
      nationalNumber = _getNationalNumber(state.phoneNumber ?? '', countryCode);
    }

    final otpResponse = await _verifyOtpRepository.execute(
      otp: state.otp.value,
      phoneNumber: nationalNumber,
      countryCode: countryCode,
    );

    if (otpResponse.isRight) {
      await _eventTracker.logEvent(
        schema: 'Verification Completed',
        description: 'User confirms OTP',
      );

      emit(
        state.copyWith(
          status: FormzSubmissionStatus.success,
          message: otpResponse.right.message,
          token: otpResponse.right.data?.token,
          user: otpResponse.right.data?.user?.attributes,
        ),
      );
    } else {
      final code = otpResponse.left.code as String;
      final message = otpResponse.left.message;

      emit(
        state.copyWith(
          status: code == 'internet-out'
              ? FormzSubmissionStatus.canceled
              : FormzSubmissionStatus.failure,
          // code: otpResponse.left.code,
          message: message,
          otpErrorMessage:
              otpResponse.left.formValidationErrors?['otp']?.detail,
          phoneNumberErrorMessage:
              otpResponse.left.formValidationErrors?['telephone']?.detail,
          countryCodeErrorMessage:
              otpResponse.left.formValidationErrors?['country_code']?.detail,
        ),
      );
    }
  }

  FutureOr<void> _onUpdatedCountryModel(
    _UpdateCountryModel event,
    Emitter<VerifyOtpFormState> emit,
  ) {
    emit(
      state.copyWith(
        country: event.country,
      ),
    );
  }

  String _getNationalNumber(
    String phoneNumber,
    String countryCode,
  ) {
    final phoneUtils = PhoneNumberUtils();
    return phoneUtils.getNationalNumber(phoneNumber, countryCode);
  }
}
