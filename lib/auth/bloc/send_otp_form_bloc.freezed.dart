// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'send_otp_form_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SendOtpFormEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String? phoneNumber, String? countryCode, String? referralCode)
        started,
    required TResult Function(String phoneNumber) phoneNumberChanged,
    required TResult Function(String? referralCode) referralCodeChanged,
    required TResult Function(CountryModel countryCode) countryCodeChanged,
    required TResult Function() submited,
    required TResult Function(String phoneNumber, String countryCode) resentOtp,
    required TResult Function() formErrorsResetted,
    required TResult Function() initializeCountry,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String? phoneNumber, String? countryCode, String? referralCode)?
        started,
    TResult? Function(String phoneNumber)? phoneNumberChanged,
    TResult? Function(String? referralCode)? referralCodeChanged,
    TResult? Function(CountryModel countryCode)? countryCodeChanged,
    TResult? Function()? submited,
    TResult? Function(String phoneNumber, String countryCode)? resentOtp,
    TResult? Function()? formErrorsResetted,
    TResult? Function()? initializeCountry,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String? phoneNumber, String? countryCode, String? referralCode)?
        started,
    TResult Function(String phoneNumber)? phoneNumberChanged,
    TResult Function(String? referralCode)? referralCodeChanged,
    TResult Function(CountryModel countryCode)? countryCodeChanged,
    TResult Function()? submited,
    TResult Function(String phoneNumber, String countryCode)? resentOtp,
    TResult Function()? formErrorsResetted,
    TResult Function()? initializeCountry,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_PhoneNumberChanged value) phoneNumberChanged,
    required TResult Function(_ReferralCodeChanged value) referralCodeChanged,
    required TResult Function(_CountryCodeChanged value) countryCodeChanged,
    required TResult Function(_Submitted value) submited,
    required TResult Function(_ResentOtp value) resentOtp,
    required TResult Function(_ResetFormErrors value) formErrorsResetted,
    required TResult Function(_InitializeCountry value) initializeCountry,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_PhoneNumberChanged value)? phoneNumberChanged,
    TResult? Function(_ReferralCodeChanged value)? referralCodeChanged,
    TResult? Function(_CountryCodeChanged value)? countryCodeChanged,
    TResult? Function(_Submitted value)? submited,
    TResult? Function(_ResentOtp value)? resentOtp,
    TResult? Function(_ResetFormErrors value)? formErrorsResetted,
    TResult? Function(_InitializeCountry value)? initializeCountry,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_PhoneNumberChanged value)? phoneNumberChanged,
    TResult Function(_ReferralCodeChanged value)? referralCodeChanged,
    TResult Function(_CountryCodeChanged value)? countryCodeChanged,
    TResult Function(_Submitted value)? submited,
    TResult Function(_ResentOtp value)? resentOtp,
    TResult Function(_ResetFormErrors value)? formErrorsResetted,
    TResult Function(_InitializeCountry value)? initializeCountry,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SendOtpFormEventCopyWith<$Res> {
  factory $SendOtpFormEventCopyWith(
          SendOtpFormEvent value, $Res Function(SendOtpFormEvent) then) =
      _$SendOtpFormEventCopyWithImpl<$Res, SendOtpFormEvent>;
}

/// @nodoc
class _$SendOtpFormEventCopyWithImpl<$Res, $Val extends SendOtpFormEvent>
    implements $SendOtpFormEventCopyWith<$Res> {
  _$SendOtpFormEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SendOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$StartedImplCopyWith<$Res> {
  factory _$$StartedImplCopyWith(
          _$StartedImpl value, $Res Function(_$StartedImpl) then) =
      __$$StartedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? phoneNumber, String? countryCode, String? referralCode});
}

/// @nodoc
class __$$StartedImplCopyWithImpl<$Res>
    extends _$SendOtpFormEventCopyWithImpl<$Res, _$StartedImpl>
    implements _$$StartedImplCopyWith<$Res> {
  __$$StartedImplCopyWithImpl(
      _$StartedImpl _value, $Res Function(_$StartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SendOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phoneNumber = freezed,
    Object? countryCode = freezed,
    Object? referralCode = freezed,
  }) {
    return _then(_$StartedImpl(
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      countryCode: freezed == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String?,
      referralCode: freezed == referralCode
          ? _value.referralCode
          : referralCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$StartedImpl with DiagnosticableTreeMixin implements _Started {
  const _$StartedImpl({this.phoneNumber, this.countryCode, this.referralCode});

  @override
  final String? phoneNumber;
  @override
  final String? countryCode;
  @override
  final String? referralCode;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'SendOtpFormEvent.started(phoneNumber: $phoneNumber, countryCode: $countryCode, referralCode: $referralCode)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'SendOtpFormEvent.started'))
      ..add(DiagnosticsProperty('phoneNumber', phoneNumber))
      ..add(DiagnosticsProperty('countryCode', countryCode))
      ..add(DiagnosticsProperty('referralCode', referralCode));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StartedImpl &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.countryCode, countryCode) ||
                other.countryCode == countryCode) &&
            (identical(other.referralCode, referralCode) ||
                other.referralCode == referralCode));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, phoneNumber, countryCode, referralCode);

  /// Create a copy of SendOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StartedImplCopyWith<_$StartedImpl> get copyWith =>
      __$$StartedImplCopyWithImpl<_$StartedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String? phoneNumber, String? countryCode, String? referralCode)
        started,
    required TResult Function(String phoneNumber) phoneNumberChanged,
    required TResult Function(String? referralCode) referralCodeChanged,
    required TResult Function(CountryModel countryCode) countryCodeChanged,
    required TResult Function() submited,
    required TResult Function(String phoneNumber, String countryCode) resentOtp,
    required TResult Function() formErrorsResetted,
    required TResult Function() initializeCountry,
  }) {
    return started(phoneNumber, countryCode, referralCode);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String? phoneNumber, String? countryCode, String? referralCode)?
        started,
    TResult? Function(String phoneNumber)? phoneNumberChanged,
    TResult? Function(String? referralCode)? referralCodeChanged,
    TResult? Function(CountryModel countryCode)? countryCodeChanged,
    TResult? Function()? submited,
    TResult? Function(String phoneNumber, String countryCode)? resentOtp,
    TResult? Function()? formErrorsResetted,
    TResult? Function()? initializeCountry,
  }) {
    return started?.call(phoneNumber, countryCode, referralCode);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String? phoneNumber, String? countryCode, String? referralCode)?
        started,
    TResult Function(String phoneNumber)? phoneNumberChanged,
    TResult Function(String? referralCode)? referralCodeChanged,
    TResult Function(CountryModel countryCode)? countryCodeChanged,
    TResult Function()? submited,
    TResult Function(String phoneNumber, String countryCode)? resentOtp,
    TResult Function()? formErrorsResetted,
    TResult Function()? initializeCountry,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started(phoneNumber, countryCode, referralCode);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_PhoneNumberChanged value) phoneNumberChanged,
    required TResult Function(_ReferralCodeChanged value) referralCodeChanged,
    required TResult Function(_CountryCodeChanged value) countryCodeChanged,
    required TResult Function(_Submitted value) submited,
    required TResult Function(_ResentOtp value) resentOtp,
    required TResult Function(_ResetFormErrors value) formErrorsResetted,
    required TResult Function(_InitializeCountry value) initializeCountry,
  }) {
    return started(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_PhoneNumberChanged value)? phoneNumberChanged,
    TResult? Function(_ReferralCodeChanged value)? referralCodeChanged,
    TResult? Function(_CountryCodeChanged value)? countryCodeChanged,
    TResult? Function(_Submitted value)? submited,
    TResult? Function(_ResentOtp value)? resentOtp,
    TResult? Function(_ResetFormErrors value)? formErrorsResetted,
    TResult? Function(_InitializeCountry value)? initializeCountry,
  }) {
    return started?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_PhoneNumberChanged value)? phoneNumberChanged,
    TResult Function(_ReferralCodeChanged value)? referralCodeChanged,
    TResult Function(_CountryCodeChanged value)? countryCodeChanged,
    TResult Function(_Submitted value)? submited,
    TResult Function(_ResentOtp value)? resentOtp,
    TResult Function(_ResetFormErrors value)? formErrorsResetted,
    TResult Function(_InitializeCountry value)? initializeCountry,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started(this);
    }
    return orElse();
  }
}

abstract class _Started implements SendOtpFormEvent {
  const factory _Started(
      {final String? phoneNumber,
      final String? countryCode,
      final String? referralCode}) = _$StartedImpl;

  String? get phoneNumber;
  String? get countryCode;
  String? get referralCode;

  /// Create a copy of SendOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StartedImplCopyWith<_$StartedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PhoneNumberChangedImplCopyWith<$Res> {
  factory _$$PhoneNumberChangedImplCopyWith(_$PhoneNumberChangedImpl value,
          $Res Function(_$PhoneNumberChangedImpl) then) =
      __$$PhoneNumberChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String phoneNumber});
}

/// @nodoc
class __$$PhoneNumberChangedImplCopyWithImpl<$Res>
    extends _$SendOtpFormEventCopyWithImpl<$Res, _$PhoneNumberChangedImpl>
    implements _$$PhoneNumberChangedImplCopyWith<$Res> {
  __$$PhoneNumberChangedImplCopyWithImpl(_$PhoneNumberChangedImpl _value,
      $Res Function(_$PhoneNumberChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SendOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phoneNumber = null,
  }) {
    return _then(_$PhoneNumberChangedImpl(
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$PhoneNumberChangedImpl
    with DiagnosticableTreeMixin
    implements _PhoneNumberChanged {
  const _$PhoneNumberChangedImpl({required this.phoneNumber});

  @override
  final String phoneNumber;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'SendOtpFormEvent.phoneNumberChanged(phoneNumber: $phoneNumber)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'SendOtpFormEvent.phoneNumberChanged'))
      ..add(DiagnosticsProperty('phoneNumber', phoneNumber));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PhoneNumberChangedImpl &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber));
  }

  @override
  int get hashCode => Object.hash(runtimeType, phoneNumber);

  /// Create a copy of SendOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PhoneNumberChangedImplCopyWith<_$PhoneNumberChangedImpl> get copyWith =>
      __$$PhoneNumberChangedImplCopyWithImpl<_$PhoneNumberChangedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String? phoneNumber, String? countryCode, String? referralCode)
        started,
    required TResult Function(String phoneNumber) phoneNumberChanged,
    required TResult Function(String? referralCode) referralCodeChanged,
    required TResult Function(CountryModel countryCode) countryCodeChanged,
    required TResult Function() submited,
    required TResult Function(String phoneNumber, String countryCode) resentOtp,
    required TResult Function() formErrorsResetted,
    required TResult Function() initializeCountry,
  }) {
    return phoneNumberChanged(phoneNumber);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String? phoneNumber, String? countryCode, String? referralCode)?
        started,
    TResult? Function(String phoneNumber)? phoneNumberChanged,
    TResult? Function(String? referralCode)? referralCodeChanged,
    TResult? Function(CountryModel countryCode)? countryCodeChanged,
    TResult? Function()? submited,
    TResult? Function(String phoneNumber, String countryCode)? resentOtp,
    TResult? Function()? formErrorsResetted,
    TResult? Function()? initializeCountry,
  }) {
    return phoneNumberChanged?.call(phoneNumber);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String? phoneNumber, String? countryCode, String? referralCode)?
        started,
    TResult Function(String phoneNumber)? phoneNumberChanged,
    TResult Function(String? referralCode)? referralCodeChanged,
    TResult Function(CountryModel countryCode)? countryCodeChanged,
    TResult Function()? submited,
    TResult Function(String phoneNumber, String countryCode)? resentOtp,
    TResult Function()? formErrorsResetted,
    TResult Function()? initializeCountry,
    required TResult orElse(),
  }) {
    if (phoneNumberChanged != null) {
      return phoneNumberChanged(phoneNumber);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_PhoneNumberChanged value) phoneNumberChanged,
    required TResult Function(_ReferralCodeChanged value) referralCodeChanged,
    required TResult Function(_CountryCodeChanged value) countryCodeChanged,
    required TResult Function(_Submitted value) submited,
    required TResult Function(_ResentOtp value) resentOtp,
    required TResult Function(_ResetFormErrors value) formErrorsResetted,
    required TResult Function(_InitializeCountry value) initializeCountry,
  }) {
    return phoneNumberChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_PhoneNumberChanged value)? phoneNumberChanged,
    TResult? Function(_ReferralCodeChanged value)? referralCodeChanged,
    TResult? Function(_CountryCodeChanged value)? countryCodeChanged,
    TResult? Function(_Submitted value)? submited,
    TResult? Function(_ResentOtp value)? resentOtp,
    TResult? Function(_ResetFormErrors value)? formErrorsResetted,
    TResult? Function(_InitializeCountry value)? initializeCountry,
  }) {
    return phoneNumberChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_PhoneNumberChanged value)? phoneNumberChanged,
    TResult Function(_ReferralCodeChanged value)? referralCodeChanged,
    TResult Function(_CountryCodeChanged value)? countryCodeChanged,
    TResult Function(_Submitted value)? submited,
    TResult Function(_ResentOtp value)? resentOtp,
    TResult Function(_ResetFormErrors value)? formErrorsResetted,
    TResult Function(_InitializeCountry value)? initializeCountry,
    required TResult orElse(),
  }) {
    if (phoneNumberChanged != null) {
      return phoneNumberChanged(this);
    }
    return orElse();
  }
}

abstract class _PhoneNumberChanged implements SendOtpFormEvent {
  const factory _PhoneNumberChanged({required final String phoneNumber}) =
      _$PhoneNumberChangedImpl;

  String get phoneNumber;

  /// Create a copy of SendOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PhoneNumberChangedImplCopyWith<_$PhoneNumberChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ReferralCodeChangedImplCopyWith<$Res> {
  factory _$$ReferralCodeChangedImplCopyWith(_$ReferralCodeChangedImpl value,
          $Res Function(_$ReferralCodeChangedImpl) then) =
      __$$ReferralCodeChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? referralCode});
}

/// @nodoc
class __$$ReferralCodeChangedImplCopyWithImpl<$Res>
    extends _$SendOtpFormEventCopyWithImpl<$Res, _$ReferralCodeChangedImpl>
    implements _$$ReferralCodeChangedImplCopyWith<$Res> {
  __$$ReferralCodeChangedImplCopyWithImpl(_$ReferralCodeChangedImpl _value,
      $Res Function(_$ReferralCodeChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SendOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? referralCode = freezed,
  }) {
    return _then(_$ReferralCodeChangedImpl(
      referralCode: freezed == referralCode
          ? _value.referralCode
          : referralCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$ReferralCodeChangedImpl
    with DiagnosticableTreeMixin
    implements _ReferralCodeChanged {
  const _$ReferralCodeChangedImpl({this.referralCode});

  @override
  final String? referralCode;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'SendOtpFormEvent.referralCodeChanged(referralCode: $referralCode)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'SendOtpFormEvent.referralCodeChanged'))
      ..add(DiagnosticsProperty('referralCode', referralCode));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReferralCodeChangedImpl &&
            (identical(other.referralCode, referralCode) ||
                other.referralCode == referralCode));
  }

  @override
  int get hashCode => Object.hash(runtimeType, referralCode);

  /// Create a copy of SendOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ReferralCodeChangedImplCopyWith<_$ReferralCodeChangedImpl> get copyWith =>
      __$$ReferralCodeChangedImplCopyWithImpl<_$ReferralCodeChangedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String? phoneNumber, String? countryCode, String? referralCode)
        started,
    required TResult Function(String phoneNumber) phoneNumberChanged,
    required TResult Function(String? referralCode) referralCodeChanged,
    required TResult Function(CountryModel countryCode) countryCodeChanged,
    required TResult Function() submited,
    required TResult Function(String phoneNumber, String countryCode) resentOtp,
    required TResult Function() formErrorsResetted,
    required TResult Function() initializeCountry,
  }) {
    return referralCodeChanged(referralCode);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String? phoneNumber, String? countryCode, String? referralCode)?
        started,
    TResult? Function(String phoneNumber)? phoneNumberChanged,
    TResult? Function(String? referralCode)? referralCodeChanged,
    TResult? Function(CountryModel countryCode)? countryCodeChanged,
    TResult? Function()? submited,
    TResult? Function(String phoneNumber, String countryCode)? resentOtp,
    TResult? Function()? formErrorsResetted,
    TResult? Function()? initializeCountry,
  }) {
    return referralCodeChanged?.call(referralCode);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String? phoneNumber, String? countryCode, String? referralCode)?
        started,
    TResult Function(String phoneNumber)? phoneNumberChanged,
    TResult Function(String? referralCode)? referralCodeChanged,
    TResult Function(CountryModel countryCode)? countryCodeChanged,
    TResult Function()? submited,
    TResult Function(String phoneNumber, String countryCode)? resentOtp,
    TResult Function()? formErrorsResetted,
    TResult Function()? initializeCountry,
    required TResult orElse(),
  }) {
    if (referralCodeChanged != null) {
      return referralCodeChanged(referralCode);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_PhoneNumberChanged value) phoneNumberChanged,
    required TResult Function(_ReferralCodeChanged value) referralCodeChanged,
    required TResult Function(_CountryCodeChanged value) countryCodeChanged,
    required TResult Function(_Submitted value) submited,
    required TResult Function(_ResentOtp value) resentOtp,
    required TResult Function(_ResetFormErrors value) formErrorsResetted,
    required TResult Function(_InitializeCountry value) initializeCountry,
  }) {
    return referralCodeChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_PhoneNumberChanged value)? phoneNumberChanged,
    TResult? Function(_ReferralCodeChanged value)? referralCodeChanged,
    TResult? Function(_CountryCodeChanged value)? countryCodeChanged,
    TResult? Function(_Submitted value)? submited,
    TResult? Function(_ResentOtp value)? resentOtp,
    TResult? Function(_ResetFormErrors value)? formErrorsResetted,
    TResult? Function(_InitializeCountry value)? initializeCountry,
  }) {
    return referralCodeChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_PhoneNumberChanged value)? phoneNumberChanged,
    TResult Function(_ReferralCodeChanged value)? referralCodeChanged,
    TResult Function(_CountryCodeChanged value)? countryCodeChanged,
    TResult Function(_Submitted value)? submited,
    TResult Function(_ResentOtp value)? resentOtp,
    TResult Function(_ResetFormErrors value)? formErrorsResetted,
    TResult Function(_InitializeCountry value)? initializeCountry,
    required TResult orElse(),
  }) {
    if (referralCodeChanged != null) {
      return referralCodeChanged(this);
    }
    return orElse();
  }
}

abstract class _ReferralCodeChanged implements SendOtpFormEvent {
  const factory _ReferralCodeChanged({final String? referralCode}) =
      _$ReferralCodeChangedImpl;

  String? get referralCode;

  /// Create a copy of SendOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ReferralCodeChangedImplCopyWith<_$ReferralCodeChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CountryCodeChangedImplCopyWith<$Res> {
  factory _$$CountryCodeChangedImplCopyWith(_$CountryCodeChangedImpl value,
          $Res Function(_$CountryCodeChangedImpl) then) =
      __$$CountryCodeChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({CountryModel countryCode});

  $CountryModelCopyWith<$Res> get countryCode;
}

/// @nodoc
class __$$CountryCodeChangedImplCopyWithImpl<$Res>
    extends _$SendOtpFormEventCopyWithImpl<$Res, _$CountryCodeChangedImpl>
    implements _$$CountryCodeChangedImplCopyWith<$Res> {
  __$$CountryCodeChangedImplCopyWithImpl(_$CountryCodeChangedImpl _value,
      $Res Function(_$CountryCodeChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SendOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? countryCode = null,
  }) {
    return _then(_$CountryCodeChangedImpl(
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as CountryModel,
    ));
  }

  /// Create a copy of SendOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CountryModelCopyWith<$Res> get countryCode {
    return $CountryModelCopyWith<$Res>(_value.countryCode, (value) {
      return _then(_value.copyWith(countryCode: value));
    });
  }
}

/// @nodoc

class _$CountryCodeChangedImpl
    with DiagnosticableTreeMixin
    implements _CountryCodeChanged {
  const _$CountryCodeChangedImpl({required this.countryCode});

  @override
  final CountryModel countryCode;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'SendOtpFormEvent.countryCodeChanged(countryCode: $countryCode)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'SendOtpFormEvent.countryCodeChanged'))
      ..add(DiagnosticsProperty('countryCode', countryCode));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CountryCodeChangedImpl &&
            (identical(other.countryCode, countryCode) ||
                other.countryCode == countryCode));
  }

  @override
  int get hashCode => Object.hash(runtimeType, countryCode);

  /// Create a copy of SendOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CountryCodeChangedImplCopyWith<_$CountryCodeChangedImpl> get copyWith =>
      __$$CountryCodeChangedImplCopyWithImpl<_$CountryCodeChangedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String? phoneNumber, String? countryCode, String? referralCode)
        started,
    required TResult Function(String phoneNumber) phoneNumberChanged,
    required TResult Function(String? referralCode) referralCodeChanged,
    required TResult Function(CountryModel countryCode) countryCodeChanged,
    required TResult Function() submited,
    required TResult Function(String phoneNumber, String countryCode) resentOtp,
    required TResult Function() formErrorsResetted,
    required TResult Function() initializeCountry,
  }) {
    return countryCodeChanged(countryCode);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String? phoneNumber, String? countryCode, String? referralCode)?
        started,
    TResult? Function(String phoneNumber)? phoneNumberChanged,
    TResult? Function(String? referralCode)? referralCodeChanged,
    TResult? Function(CountryModel countryCode)? countryCodeChanged,
    TResult? Function()? submited,
    TResult? Function(String phoneNumber, String countryCode)? resentOtp,
    TResult? Function()? formErrorsResetted,
    TResult? Function()? initializeCountry,
  }) {
    return countryCodeChanged?.call(countryCode);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String? phoneNumber, String? countryCode, String? referralCode)?
        started,
    TResult Function(String phoneNumber)? phoneNumberChanged,
    TResult Function(String? referralCode)? referralCodeChanged,
    TResult Function(CountryModel countryCode)? countryCodeChanged,
    TResult Function()? submited,
    TResult Function(String phoneNumber, String countryCode)? resentOtp,
    TResult Function()? formErrorsResetted,
    TResult Function()? initializeCountry,
    required TResult orElse(),
  }) {
    if (countryCodeChanged != null) {
      return countryCodeChanged(countryCode);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_PhoneNumberChanged value) phoneNumberChanged,
    required TResult Function(_ReferralCodeChanged value) referralCodeChanged,
    required TResult Function(_CountryCodeChanged value) countryCodeChanged,
    required TResult Function(_Submitted value) submited,
    required TResult Function(_ResentOtp value) resentOtp,
    required TResult Function(_ResetFormErrors value) formErrorsResetted,
    required TResult Function(_InitializeCountry value) initializeCountry,
  }) {
    return countryCodeChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_PhoneNumberChanged value)? phoneNumberChanged,
    TResult? Function(_ReferralCodeChanged value)? referralCodeChanged,
    TResult? Function(_CountryCodeChanged value)? countryCodeChanged,
    TResult? Function(_Submitted value)? submited,
    TResult? Function(_ResentOtp value)? resentOtp,
    TResult? Function(_ResetFormErrors value)? formErrorsResetted,
    TResult? Function(_InitializeCountry value)? initializeCountry,
  }) {
    return countryCodeChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_PhoneNumberChanged value)? phoneNumberChanged,
    TResult Function(_ReferralCodeChanged value)? referralCodeChanged,
    TResult Function(_CountryCodeChanged value)? countryCodeChanged,
    TResult Function(_Submitted value)? submited,
    TResult Function(_ResentOtp value)? resentOtp,
    TResult Function(_ResetFormErrors value)? formErrorsResetted,
    TResult Function(_InitializeCountry value)? initializeCountry,
    required TResult orElse(),
  }) {
    if (countryCodeChanged != null) {
      return countryCodeChanged(this);
    }
    return orElse();
  }
}

abstract class _CountryCodeChanged implements SendOtpFormEvent {
  const factory _CountryCodeChanged({required final CountryModel countryCode}) =
      _$CountryCodeChangedImpl;

  CountryModel get countryCode;

  /// Create a copy of SendOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CountryCodeChangedImplCopyWith<_$CountryCodeChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SubmittedImplCopyWith<$Res> {
  factory _$$SubmittedImplCopyWith(
          _$SubmittedImpl value, $Res Function(_$SubmittedImpl) then) =
      __$$SubmittedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SubmittedImplCopyWithImpl<$Res>
    extends _$SendOtpFormEventCopyWithImpl<$Res, _$SubmittedImpl>
    implements _$$SubmittedImplCopyWith<$Res> {
  __$$SubmittedImplCopyWithImpl(
      _$SubmittedImpl _value, $Res Function(_$SubmittedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SendOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SubmittedImpl with DiagnosticableTreeMixin implements _Submitted {
  const _$SubmittedImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'SendOtpFormEvent.submited()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'SendOtpFormEvent.submited'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SubmittedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String? phoneNumber, String? countryCode, String? referralCode)
        started,
    required TResult Function(String phoneNumber) phoneNumberChanged,
    required TResult Function(String? referralCode) referralCodeChanged,
    required TResult Function(CountryModel countryCode) countryCodeChanged,
    required TResult Function() submited,
    required TResult Function(String phoneNumber, String countryCode) resentOtp,
    required TResult Function() formErrorsResetted,
    required TResult Function() initializeCountry,
  }) {
    return submited();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String? phoneNumber, String? countryCode, String? referralCode)?
        started,
    TResult? Function(String phoneNumber)? phoneNumberChanged,
    TResult? Function(String? referralCode)? referralCodeChanged,
    TResult? Function(CountryModel countryCode)? countryCodeChanged,
    TResult? Function()? submited,
    TResult? Function(String phoneNumber, String countryCode)? resentOtp,
    TResult? Function()? formErrorsResetted,
    TResult? Function()? initializeCountry,
  }) {
    return submited?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String? phoneNumber, String? countryCode, String? referralCode)?
        started,
    TResult Function(String phoneNumber)? phoneNumberChanged,
    TResult Function(String? referralCode)? referralCodeChanged,
    TResult Function(CountryModel countryCode)? countryCodeChanged,
    TResult Function()? submited,
    TResult Function(String phoneNumber, String countryCode)? resentOtp,
    TResult Function()? formErrorsResetted,
    TResult Function()? initializeCountry,
    required TResult orElse(),
  }) {
    if (submited != null) {
      return submited();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_PhoneNumberChanged value) phoneNumberChanged,
    required TResult Function(_ReferralCodeChanged value) referralCodeChanged,
    required TResult Function(_CountryCodeChanged value) countryCodeChanged,
    required TResult Function(_Submitted value) submited,
    required TResult Function(_ResentOtp value) resentOtp,
    required TResult Function(_ResetFormErrors value) formErrorsResetted,
    required TResult Function(_InitializeCountry value) initializeCountry,
  }) {
    return submited(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_PhoneNumberChanged value)? phoneNumberChanged,
    TResult? Function(_ReferralCodeChanged value)? referralCodeChanged,
    TResult? Function(_CountryCodeChanged value)? countryCodeChanged,
    TResult? Function(_Submitted value)? submited,
    TResult? Function(_ResentOtp value)? resentOtp,
    TResult? Function(_ResetFormErrors value)? formErrorsResetted,
    TResult? Function(_InitializeCountry value)? initializeCountry,
  }) {
    return submited?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_PhoneNumberChanged value)? phoneNumberChanged,
    TResult Function(_ReferralCodeChanged value)? referralCodeChanged,
    TResult Function(_CountryCodeChanged value)? countryCodeChanged,
    TResult Function(_Submitted value)? submited,
    TResult Function(_ResentOtp value)? resentOtp,
    TResult Function(_ResetFormErrors value)? formErrorsResetted,
    TResult Function(_InitializeCountry value)? initializeCountry,
    required TResult orElse(),
  }) {
    if (submited != null) {
      return submited(this);
    }
    return orElse();
  }
}

abstract class _Submitted implements SendOtpFormEvent {
  const factory _Submitted() = _$SubmittedImpl;
}

/// @nodoc
abstract class _$$ResentOtpImplCopyWith<$Res> {
  factory _$$ResentOtpImplCopyWith(
          _$ResentOtpImpl value, $Res Function(_$ResentOtpImpl) then) =
      __$$ResentOtpImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String phoneNumber, String countryCode});
}

/// @nodoc
class __$$ResentOtpImplCopyWithImpl<$Res>
    extends _$SendOtpFormEventCopyWithImpl<$Res, _$ResentOtpImpl>
    implements _$$ResentOtpImplCopyWith<$Res> {
  __$$ResentOtpImplCopyWithImpl(
      _$ResentOtpImpl _value, $Res Function(_$ResentOtpImpl) _then)
      : super(_value, _then);

  /// Create a copy of SendOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phoneNumber = null,
    Object? countryCode = null,
  }) {
    return _then(_$ResentOtpImpl(
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ResentOtpImpl with DiagnosticableTreeMixin implements _ResentOtp {
  const _$ResentOtpImpl({required this.phoneNumber, required this.countryCode});

  @override
  final String phoneNumber;
  @override
  final String countryCode;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'SendOtpFormEvent.resentOtp(phoneNumber: $phoneNumber, countryCode: $countryCode)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'SendOtpFormEvent.resentOtp'))
      ..add(DiagnosticsProperty('phoneNumber', phoneNumber))
      ..add(DiagnosticsProperty('countryCode', countryCode));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ResentOtpImpl &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.countryCode, countryCode) ||
                other.countryCode == countryCode));
  }

  @override
  int get hashCode => Object.hash(runtimeType, phoneNumber, countryCode);

  /// Create a copy of SendOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ResentOtpImplCopyWith<_$ResentOtpImpl> get copyWith =>
      __$$ResentOtpImplCopyWithImpl<_$ResentOtpImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String? phoneNumber, String? countryCode, String? referralCode)
        started,
    required TResult Function(String phoneNumber) phoneNumberChanged,
    required TResult Function(String? referralCode) referralCodeChanged,
    required TResult Function(CountryModel countryCode) countryCodeChanged,
    required TResult Function() submited,
    required TResult Function(String phoneNumber, String countryCode) resentOtp,
    required TResult Function() formErrorsResetted,
    required TResult Function() initializeCountry,
  }) {
    return resentOtp(phoneNumber, countryCode);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String? phoneNumber, String? countryCode, String? referralCode)?
        started,
    TResult? Function(String phoneNumber)? phoneNumberChanged,
    TResult? Function(String? referralCode)? referralCodeChanged,
    TResult? Function(CountryModel countryCode)? countryCodeChanged,
    TResult? Function()? submited,
    TResult? Function(String phoneNumber, String countryCode)? resentOtp,
    TResult? Function()? formErrorsResetted,
    TResult? Function()? initializeCountry,
  }) {
    return resentOtp?.call(phoneNumber, countryCode);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String? phoneNumber, String? countryCode, String? referralCode)?
        started,
    TResult Function(String phoneNumber)? phoneNumberChanged,
    TResult Function(String? referralCode)? referralCodeChanged,
    TResult Function(CountryModel countryCode)? countryCodeChanged,
    TResult Function()? submited,
    TResult Function(String phoneNumber, String countryCode)? resentOtp,
    TResult Function()? formErrorsResetted,
    TResult Function()? initializeCountry,
    required TResult orElse(),
  }) {
    if (resentOtp != null) {
      return resentOtp(phoneNumber, countryCode);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_PhoneNumberChanged value) phoneNumberChanged,
    required TResult Function(_ReferralCodeChanged value) referralCodeChanged,
    required TResult Function(_CountryCodeChanged value) countryCodeChanged,
    required TResult Function(_Submitted value) submited,
    required TResult Function(_ResentOtp value) resentOtp,
    required TResult Function(_ResetFormErrors value) formErrorsResetted,
    required TResult Function(_InitializeCountry value) initializeCountry,
  }) {
    return resentOtp(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_PhoneNumberChanged value)? phoneNumberChanged,
    TResult? Function(_ReferralCodeChanged value)? referralCodeChanged,
    TResult? Function(_CountryCodeChanged value)? countryCodeChanged,
    TResult? Function(_Submitted value)? submited,
    TResult? Function(_ResentOtp value)? resentOtp,
    TResult? Function(_ResetFormErrors value)? formErrorsResetted,
    TResult? Function(_InitializeCountry value)? initializeCountry,
  }) {
    return resentOtp?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_PhoneNumberChanged value)? phoneNumberChanged,
    TResult Function(_ReferralCodeChanged value)? referralCodeChanged,
    TResult Function(_CountryCodeChanged value)? countryCodeChanged,
    TResult Function(_Submitted value)? submited,
    TResult Function(_ResentOtp value)? resentOtp,
    TResult Function(_ResetFormErrors value)? formErrorsResetted,
    TResult Function(_InitializeCountry value)? initializeCountry,
    required TResult orElse(),
  }) {
    if (resentOtp != null) {
      return resentOtp(this);
    }
    return orElse();
  }
}

abstract class _ResentOtp implements SendOtpFormEvent {
  const factory _ResentOtp(
      {required final String phoneNumber,
      required final String countryCode}) = _$ResentOtpImpl;

  String get phoneNumber;
  String get countryCode;

  /// Create a copy of SendOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ResentOtpImplCopyWith<_$ResentOtpImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ResetFormErrorsImplCopyWith<$Res> {
  factory _$$ResetFormErrorsImplCopyWith(_$ResetFormErrorsImpl value,
          $Res Function(_$ResetFormErrorsImpl) then) =
      __$$ResetFormErrorsImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ResetFormErrorsImplCopyWithImpl<$Res>
    extends _$SendOtpFormEventCopyWithImpl<$Res, _$ResetFormErrorsImpl>
    implements _$$ResetFormErrorsImplCopyWith<$Res> {
  __$$ResetFormErrorsImplCopyWithImpl(
      _$ResetFormErrorsImpl _value, $Res Function(_$ResetFormErrorsImpl) _then)
      : super(_value, _then);

  /// Create a copy of SendOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ResetFormErrorsImpl
    with DiagnosticableTreeMixin
    implements _ResetFormErrors {
  const _$ResetFormErrorsImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'SendOtpFormEvent.formErrorsResetted()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'SendOtpFormEvent.formErrorsResetted'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ResetFormErrorsImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String? phoneNumber, String? countryCode, String? referralCode)
        started,
    required TResult Function(String phoneNumber) phoneNumberChanged,
    required TResult Function(String? referralCode) referralCodeChanged,
    required TResult Function(CountryModel countryCode) countryCodeChanged,
    required TResult Function() submited,
    required TResult Function(String phoneNumber, String countryCode) resentOtp,
    required TResult Function() formErrorsResetted,
    required TResult Function() initializeCountry,
  }) {
    return formErrorsResetted();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String? phoneNumber, String? countryCode, String? referralCode)?
        started,
    TResult? Function(String phoneNumber)? phoneNumberChanged,
    TResult? Function(String? referralCode)? referralCodeChanged,
    TResult? Function(CountryModel countryCode)? countryCodeChanged,
    TResult? Function()? submited,
    TResult? Function(String phoneNumber, String countryCode)? resentOtp,
    TResult? Function()? formErrorsResetted,
    TResult? Function()? initializeCountry,
  }) {
    return formErrorsResetted?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String? phoneNumber, String? countryCode, String? referralCode)?
        started,
    TResult Function(String phoneNumber)? phoneNumberChanged,
    TResult Function(String? referralCode)? referralCodeChanged,
    TResult Function(CountryModel countryCode)? countryCodeChanged,
    TResult Function()? submited,
    TResult Function(String phoneNumber, String countryCode)? resentOtp,
    TResult Function()? formErrorsResetted,
    TResult Function()? initializeCountry,
    required TResult orElse(),
  }) {
    if (formErrorsResetted != null) {
      return formErrorsResetted();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_PhoneNumberChanged value) phoneNumberChanged,
    required TResult Function(_ReferralCodeChanged value) referralCodeChanged,
    required TResult Function(_CountryCodeChanged value) countryCodeChanged,
    required TResult Function(_Submitted value) submited,
    required TResult Function(_ResentOtp value) resentOtp,
    required TResult Function(_ResetFormErrors value) formErrorsResetted,
    required TResult Function(_InitializeCountry value) initializeCountry,
  }) {
    return formErrorsResetted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_PhoneNumberChanged value)? phoneNumberChanged,
    TResult? Function(_ReferralCodeChanged value)? referralCodeChanged,
    TResult? Function(_CountryCodeChanged value)? countryCodeChanged,
    TResult? Function(_Submitted value)? submited,
    TResult? Function(_ResentOtp value)? resentOtp,
    TResult? Function(_ResetFormErrors value)? formErrorsResetted,
    TResult? Function(_InitializeCountry value)? initializeCountry,
  }) {
    return formErrorsResetted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_PhoneNumberChanged value)? phoneNumberChanged,
    TResult Function(_ReferralCodeChanged value)? referralCodeChanged,
    TResult Function(_CountryCodeChanged value)? countryCodeChanged,
    TResult Function(_Submitted value)? submited,
    TResult Function(_ResentOtp value)? resentOtp,
    TResult Function(_ResetFormErrors value)? formErrorsResetted,
    TResult Function(_InitializeCountry value)? initializeCountry,
    required TResult orElse(),
  }) {
    if (formErrorsResetted != null) {
      return formErrorsResetted(this);
    }
    return orElse();
  }
}

abstract class _ResetFormErrors implements SendOtpFormEvent {
  const factory _ResetFormErrors() = _$ResetFormErrorsImpl;
}

/// @nodoc
abstract class _$$InitializeCountryImplCopyWith<$Res> {
  factory _$$InitializeCountryImplCopyWith(_$InitializeCountryImpl value,
          $Res Function(_$InitializeCountryImpl) then) =
      __$$InitializeCountryImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitializeCountryImplCopyWithImpl<$Res>
    extends _$SendOtpFormEventCopyWithImpl<$Res, _$InitializeCountryImpl>
    implements _$$InitializeCountryImplCopyWith<$Res> {
  __$$InitializeCountryImplCopyWithImpl(_$InitializeCountryImpl _value,
      $Res Function(_$InitializeCountryImpl) _then)
      : super(_value, _then);

  /// Create a copy of SendOtpFormEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitializeCountryImpl
    with DiagnosticableTreeMixin
    implements _InitializeCountry {
  const _$InitializeCountryImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'SendOtpFormEvent.initializeCountry()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'SendOtpFormEvent.initializeCountry'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitializeCountryImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String? phoneNumber, String? countryCode, String? referralCode)
        started,
    required TResult Function(String phoneNumber) phoneNumberChanged,
    required TResult Function(String? referralCode) referralCodeChanged,
    required TResult Function(CountryModel countryCode) countryCodeChanged,
    required TResult Function() submited,
    required TResult Function(String phoneNumber, String countryCode) resentOtp,
    required TResult Function() formErrorsResetted,
    required TResult Function() initializeCountry,
  }) {
    return initializeCountry();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            String? phoneNumber, String? countryCode, String? referralCode)?
        started,
    TResult? Function(String phoneNumber)? phoneNumberChanged,
    TResult? Function(String? referralCode)? referralCodeChanged,
    TResult? Function(CountryModel countryCode)? countryCodeChanged,
    TResult? Function()? submited,
    TResult? Function(String phoneNumber, String countryCode)? resentOtp,
    TResult? Function()? formErrorsResetted,
    TResult? Function()? initializeCountry,
  }) {
    return initializeCountry?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
            String? phoneNumber, String? countryCode, String? referralCode)?
        started,
    TResult Function(String phoneNumber)? phoneNumberChanged,
    TResult Function(String? referralCode)? referralCodeChanged,
    TResult Function(CountryModel countryCode)? countryCodeChanged,
    TResult Function()? submited,
    TResult Function(String phoneNumber, String countryCode)? resentOtp,
    TResult Function()? formErrorsResetted,
    TResult Function()? initializeCountry,
    required TResult orElse(),
  }) {
    if (initializeCountry != null) {
      return initializeCountry();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_PhoneNumberChanged value) phoneNumberChanged,
    required TResult Function(_ReferralCodeChanged value) referralCodeChanged,
    required TResult Function(_CountryCodeChanged value) countryCodeChanged,
    required TResult Function(_Submitted value) submited,
    required TResult Function(_ResentOtp value) resentOtp,
    required TResult Function(_ResetFormErrors value) formErrorsResetted,
    required TResult Function(_InitializeCountry value) initializeCountry,
  }) {
    return initializeCountry(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_PhoneNumberChanged value)? phoneNumberChanged,
    TResult? Function(_ReferralCodeChanged value)? referralCodeChanged,
    TResult? Function(_CountryCodeChanged value)? countryCodeChanged,
    TResult? Function(_Submitted value)? submited,
    TResult? Function(_ResentOtp value)? resentOtp,
    TResult? Function(_ResetFormErrors value)? formErrorsResetted,
    TResult? Function(_InitializeCountry value)? initializeCountry,
  }) {
    return initializeCountry?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_PhoneNumberChanged value)? phoneNumberChanged,
    TResult Function(_ReferralCodeChanged value)? referralCodeChanged,
    TResult Function(_CountryCodeChanged value)? countryCodeChanged,
    TResult Function(_Submitted value)? submited,
    TResult Function(_ResentOtp value)? resentOtp,
    TResult Function(_ResetFormErrors value)? formErrorsResetted,
    TResult Function(_InitializeCountry value)? initializeCountry,
    required TResult orElse(),
  }) {
    if (initializeCountry != null) {
      return initializeCountry(this);
    }
    return orElse();
  }
}

abstract class _InitializeCountry implements SendOtpFormEvent {
  const factory _InitializeCountry() = _$InitializeCountryImpl;
}

/// @nodoc
mixin _$SendOtpFormState {
  VarCharInput get countryCode => throw _privateConstructorUsedError;
  PhoneNumberInput get phoneNumber => throw _privateConstructorUsedError;
  VarCharInput? get referralCode => throw _privateConstructorUsedError;
  String? get phoneNumberErrorMessage => throw _privateConstructorUsedError;
  String? get referralErrorMessage => throw _privateConstructorUsedError;
  String? get countryCodeErrorMessage => throw _privateConstructorUsedError;
  String? get deviceIdErrorMessage => throw _privateConstructorUsedError;
  String get noOfTriesLeft => throw _privateConstructorUsedError;
  String? get message => throw _privateConstructorUsedError;
  CountryModel? get selectedCountry => throw _privateConstructorUsedError;
  bool get showReferralField => throw _privateConstructorUsedError;
  FormzSubmissionStatus get status => throw _privateConstructorUsedError;

  /// Standardized phone number data for consistent handling and transfer
  PhoneNumberData? get phoneNumberData => throw _privateConstructorUsedError;

  /// Create a copy of SendOtpFormState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SendOtpFormStateCopyWith<SendOtpFormState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SendOtpFormStateCopyWith<$Res> {
  factory $SendOtpFormStateCopyWith(
          SendOtpFormState value, $Res Function(SendOtpFormState) then) =
      _$SendOtpFormStateCopyWithImpl<$Res, SendOtpFormState>;
  @useResult
  $Res call(
      {VarCharInput countryCode,
      PhoneNumberInput phoneNumber,
      VarCharInput? referralCode,
      String? phoneNumberErrorMessage,
      String? referralErrorMessage,
      String? countryCodeErrorMessage,
      String? deviceIdErrorMessage,
      String noOfTriesLeft,
      String? message,
      CountryModel? selectedCountry,
      bool showReferralField,
      FormzSubmissionStatus status,
      PhoneNumberData? phoneNumberData});

  $CountryModelCopyWith<$Res>? get selectedCountry;
  $PhoneNumberDataCopyWith<$Res>? get phoneNumberData;
}

/// @nodoc
class _$SendOtpFormStateCopyWithImpl<$Res, $Val extends SendOtpFormState>
    implements $SendOtpFormStateCopyWith<$Res> {
  _$SendOtpFormStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SendOtpFormState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? countryCode = null,
    Object? phoneNumber = null,
    Object? referralCode = freezed,
    Object? phoneNumberErrorMessage = freezed,
    Object? referralErrorMessage = freezed,
    Object? countryCodeErrorMessage = freezed,
    Object? deviceIdErrorMessage = freezed,
    Object? noOfTriesLeft = null,
    Object? message = freezed,
    Object? selectedCountry = freezed,
    Object? showReferralField = null,
    Object? status = null,
    Object? phoneNumberData = freezed,
  }) {
    return _then(_value.copyWith(
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as VarCharInput,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as PhoneNumberInput,
      referralCode: freezed == referralCode
          ? _value.referralCode
          : referralCode // ignore: cast_nullable_to_non_nullable
              as VarCharInput?,
      phoneNumberErrorMessage: freezed == phoneNumberErrorMessage
          ? _value.phoneNumberErrorMessage
          : phoneNumberErrorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      referralErrorMessage: freezed == referralErrorMessage
          ? _value.referralErrorMessage
          : referralErrorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      countryCodeErrorMessage: freezed == countryCodeErrorMessage
          ? _value.countryCodeErrorMessage
          : countryCodeErrorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      deviceIdErrorMessage: freezed == deviceIdErrorMessage
          ? _value.deviceIdErrorMessage
          : deviceIdErrorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      noOfTriesLeft: null == noOfTriesLeft
          ? _value.noOfTriesLeft
          : noOfTriesLeft // ignore: cast_nullable_to_non_nullable
              as String,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedCountry: freezed == selectedCountry
          ? _value.selectedCountry
          : selectedCountry // ignore: cast_nullable_to_non_nullable
              as CountryModel?,
      showReferralField: null == showReferralField
          ? _value.showReferralField
          : showReferralField // ignore: cast_nullable_to_non_nullable
              as bool,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as FormzSubmissionStatus,
      phoneNumberData: freezed == phoneNumberData
          ? _value.phoneNumberData
          : phoneNumberData // ignore: cast_nullable_to_non_nullable
              as PhoneNumberData?,
    ) as $Val);
  }

  /// Create a copy of SendOtpFormState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CountryModelCopyWith<$Res>? get selectedCountry {
    if (_value.selectedCountry == null) {
      return null;
    }

    return $CountryModelCopyWith<$Res>(_value.selectedCountry!, (value) {
      return _then(_value.copyWith(selectedCountry: value) as $Val);
    });
  }

  /// Create a copy of SendOtpFormState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PhoneNumberDataCopyWith<$Res>? get phoneNumberData {
    if (_value.phoneNumberData == null) {
      return null;
    }

    return $PhoneNumberDataCopyWith<$Res>(_value.phoneNumberData!, (value) {
      return _then(_value.copyWith(phoneNumberData: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SendOtpFormStateImplCopyWith<$Res>
    implements $SendOtpFormStateCopyWith<$Res> {
  factory _$$SendOtpFormStateImplCopyWith(_$SendOtpFormStateImpl value,
          $Res Function(_$SendOtpFormStateImpl) then) =
      __$$SendOtpFormStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {VarCharInput countryCode,
      PhoneNumberInput phoneNumber,
      VarCharInput? referralCode,
      String? phoneNumberErrorMessage,
      String? referralErrorMessage,
      String? countryCodeErrorMessage,
      String? deviceIdErrorMessage,
      String noOfTriesLeft,
      String? message,
      CountryModel? selectedCountry,
      bool showReferralField,
      FormzSubmissionStatus status,
      PhoneNumberData? phoneNumberData});

  @override
  $CountryModelCopyWith<$Res>? get selectedCountry;
  @override
  $PhoneNumberDataCopyWith<$Res>? get phoneNumberData;
}

/// @nodoc
class __$$SendOtpFormStateImplCopyWithImpl<$Res>
    extends _$SendOtpFormStateCopyWithImpl<$Res, _$SendOtpFormStateImpl>
    implements _$$SendOtpFormStateImplCopyWith<$Res> {
  __$$SendOtpFormStateImplCopyWithImpl(_$SendOtpFormStateImpl _value,
      $Res Function(_$SendOtpFormStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of SendOtpFormState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? countryCode = null,
    Object? phoneNumber = null,
    Object? referralCode = freezed,
    Object? phoneNumberErrorMessage = freezed,
    Object? referralErrorMessage = freezed,
    Object? countryCodeErrorMessage = freezed,
    Object? deviceIdErrorMessage = freezed,
    Object? noOfTriesLeft = null,
    Object? message = freezed,
    Object? selectedCountry = freezed,
    Object? showReferralField = null,
    Object? status = null,
    Object? phoneNumberData = freezed,
  }) {
    return _then(_$SendOtpFormStateImpl(
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as VarCharInput,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as PhoneNumberInput,
      referralCode: freezed == referralCode
          ? _value.referralCode
          : referralCode // ignore: cast_nullable_to_non_nullable
              as VarCharInput?,
      phoneNumberErrorMessage: freezed == phoneNumberErrorMessage
          ? _value.phoneNumberErrorMessage
          : phoneNumberErrorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      referralErrorMessage: freezed == referralErrorMessage
          ? _value.referralErrorMessage
          : referralErrorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      countryCodeErrorMessage: freezed == countryCodeErrorMessage
          ? _value.countryCodeErrorMessage
          : countryCodeErrorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      deviceIdErrorMessage: freezed == deviceIdErrorMessage
          ? _value.deviceIdErrorMessage
          : deviceIdErrorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      noOfTriesLeft: null == noOfTriesLeft
          ? _value.noOfTriesLeft
          : noOfTriesLeft // ignore: cast_nullable_to_non_nullable
              as String,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      selectedCountry: freezed == selectedCountry
          ? _value.selectedCountry
          : selectedCountry // ignore: cast_nullable_to_non_nullable
              as CountryModel?,
      showReferralField: null == showReferralField
          ? _value.showReferralField
          : showReferralField // ignore: cast_nullable_to_non_nullable
              as bool,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as FormzSubmissionStatus,
      phoneNumberData: freezed == phoneNumberData
          ? _value.phoneNumberData
          : phoneNumberData // ignore: cast_nullable_to_non_nullable
              as PhoneNumberData?,
    ));
  }
}

/// @nodoc

class _$SendOtpFormStateImpl extends _SendOtpFormState
    with DiagnosticableTreeMixin {
  _$SendOtpFormStateImpl(
      {this.countryCode = const VarCharInput.pure(),
      this.phoneNumber = const PhoneNumberInput.pure(),
      this.referralCode,
      this.phoneNumberErrorMessage,
      this.referralErrorMessage,
      this.countryCodeErrorMessage,
      this.deviceIdErrorMessage,
      this.noOfTriesLeft = '3',
      this.message,
      this.selectedCountry,
      this.showReferralField = false,
      this.status = FormzSubmissionStatus.initial,
      this.phoneNumberData})
      : super._();

  @override
  @JsonKey()
  final VarCharInput countryCode;
  @override
  @JsonKey()
  final PhoneNumberInput phoneNumber;
  @override
  final VarCharInput? referralCode;
  @override
  final String? phoneNumberErrorMessage;
  @override
  final String? referralErrorMessage;
  @override
  final String? countryCodeErrorMessage;
  @override
  final String? deviceIdErrorMessage;
  @override
  @JsonKey()
  final String noOfTriesLeft;
  @override
  final String? message;
  @override
  final CountryModel? selectedCountry;
  @override
  @JsonKey()
  final bool showReferralField;
  @override
  @JsonKey()
  final FormzSubmissionStatus status;

  /// Standardized phone number data for consistent handling and transfer
  @override
  final PhoneNumberData? phoneNumberData;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'SendOtpFormState(countryCode: $countryCode, phoneNumber: $phoneNumber, referralCode: $referralCode, phoneNumberErrorMessage: $phoneNumberErrorMessage, referralErrorMessage: $referralErrorMessage, countryCodeErrorMessage: $countryCodeErrorMessage, deviceIdErrorMessage: $deviceIdErrorMessage, noOfTriesLeft: $noOfTriesLeft, message: $message, selectedCountry: $selectedCountry, showReferralField: $showReferralField, status: $status, phoneNumberData: $phoneNumberData)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'SendOtpFormState'))
      ..add(DiagnosticsProperty('countryCode', countryCode))
      ..add(DiagnosticsProperty('phoneNumber', phoneNumber))
      ..add(DiagnosticsProperty('referralCode', referralCode))
      ..add(DiagnosticsProperty(
          'phoneNumberErrorMessage', phoneNumberErrorMessage))
      ..add(DiagnosticsProperty('referralErrorMessage', referralErrorMessage))
      ..add(DiagnosticsProperty(
          'countryCodeErrorMessage', countryCodeErrorMessage))
      ..add(DiagnosticsProperty('deviceIdErrorMessage', deviceIdErrorMessage))
      ..add(DiagnosticsProperty('noOfTriesLeft', noOfTriesLeft))
      ..add(DiagnosticsProperty('message', message))
      ..add(DiagnosticsProperty('selectedCountry', selectedCountry))
      ..add(DiagnosticsProperty('showReferralField', showReferralField))
      ..add(DiagnosticsProperty('status', status))
      ..add(DiagnosticsProperty('phoneNumberData', phoneNumberData));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SendOtpFormStateImpl &&
            (identical(other.countryCode, countryCode) ||
                other.countryCode == countryCode) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.referralCode, referralCode) ||
                other.referralCode == referralCode) &&
            (identical(
                    other.phoneNumberErrorMessage, phoneNumberErrorMessage) ||
                other.phoneNumberErrorMessage == phoneNumberErrorMessage) &&
            (identical(other.referralErrorMessage, referralErrorMessage) ||
                other.referralErrorMessage == referralErrorMessage) &&
            (identical(
                    other.countryCodeErrorMessage, countryCodeErrorMessage) ||
                other.countryCodeErrorMessage == countryCodeErrorMessage) &&
            (identical(other.deviceIdErrorMessage, deviceIdErrorMessage) ||
                other.deviceIdErrorMessage == deviceIdErrorMessage) &&
            (identical(other.noOfTriesLeft, noOfTriesLeft) ||
                other.noOfTriesLeft == noOfTriesLeft) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.selectedCountry, selectedCountry) ||
                other.selectedCountry == selectedCountry) &&
            (identical(other.showReferralField, showReferralField) ||
                other.showReferralField == showReferralField) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.phoneNumberData, phoneNumberData) ||
                other.phoneNumberData == phoneNumberData));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      countryCode,
      phoneNumber,
      referralCode,
      phoneNumberErrorMessage,
      referralErrorMessage,
      countryCodeErrorMessage,
      deviceIdErrorMessage,
      noOfTriesLeft,
      message,
      selectedCountry,
      showReferralField,
      status,
      phoneNumberData);

  /// Create a copy of SendOtpFormState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SendOtpFormStateImplCopyWith<_$SendOtpFormStateImpl> get copyWith =>
      __$$SendOtpFormStateImplCopyWithImpl<_$SendOtpFormStateImpl>(
          this, _$identity);
}

abstract class _SendOtpFormState extends SendOtpFormState {
  factory _SendOtpFormState(
      {final VarCharInput countryCode,
      final PhoneNumberInput phoneNumber,
      final VarCharInput? referralCode,
      final String? phoneNumberErrorMessage,
      final String? referralErrorMessage,
      final String? countryCodeErrorMessage,
      final String? deviceIdErrorMessage,
      final String noOfTriesLeft,
      final String? message,
      final CountryModel? selectedCountry,
      final bool showReferralField,
      final FormzSubmissionStatus status,
      final PhoneNumberData? phoneNumberData}) = _$SendOtpFormStateImpl;
  _SendOtpFormState._() : super._();

  @override
  VarCharInput get countryCode;
  @override
  PhoneNumberInput get phoneNumber;
  @override
  VarCharInput? get referralCode;
  @override
  String? get phoneNumberErrorMessage;
  @override
  String? get referralErrorMessage;
  @override
  String? get countryCodeErrorMessage;
  @override
  String? get deviceIdErrorMessage;
  @override
  String get noOfTriesLeft;
  @override
  String? get message;
  @override
  CountryModel? get selectedCountry;
  @override
  bool get showReferralField;
  @override
  FormzSubmissionStatus get status;

  /// Standardized phone number data for consistent handling and transfer
  @override
  PhoneNumberData? get phoneNumberData;

  /// Create a copy of SendOtpFormState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SendOtpFormStateImplCopyWith<_$SendOtpFormStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
