// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'authentication_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AuthenticationEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool updateProfileMetadata) refreshProfile,
    required TResult Function() reset,
    required TResult Function(UserAttributesModel? user) updatedUser,
    required TResult Function() signedOut,
    required TResult Function() destroyedToken,
    required TResult Function(WelcomeBackMode mode, AuthenticationStatus status,
            UserAttributesModel? user, String? message)
        welcomeBack,
    required TResult Function(String token, UserAttributesModel? user) signedIn,
    required TResult Function() enabledOfflineMode,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool updateProfileMetadata)? refreshProfile,
    TResult? Function()? reset,
    TResult? Function(UserAttributesModel? user)? updatedUser,
    TResult? Function()? signedOut,
    TResult? Function()? destroyedToken,
    TResult? Function(WelcomeBackMode mode, AuthenticationStatus status,
            UserAttributesModel? user, String? message)?
        welcomeBack,
    TResult? Function(String token, UserAttributesModel? user)? signedIn,
    TResult? Function()? enabledOfflineMode,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool updateProfileMetadata)? refreshProfile,
    TResult Function()? reset,
    TResult Function(UserAttributesModel? user)? updatedUser,
    TResult Function()? signedOut,
    TResult Function()? destroyedToken,
    TResult Function(WelcomeBackMode mode, AuthenticationStatus status,
            UserAttributesModel? user, String? message)?
        welcomeBack,
    TResult Function(String token, UserAttributesModel? user)? signedIn,
    TResult Function()? enabledOfflineMode,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) refreshProfile,
    required TResult Function(_Initial value) reset,
    required TResult Function(_UpdatedUser value) updatedUser,
    required TResult Function(_SignedOut value) signedOut,
    required TResult Function(_DestroyedToken value) destroyedToken,
    required TResult Function(_WelcomeBack value) welcomeBack,
    required TResult Function(_SignedIn value) signedIn,
    required TResult Function(_EnabledOfflineMode value) enabledOfflineMode,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? refreshProfile,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_UpdatedUser value)? updatedUser,
    TResult? Function(_SignedOut value)? signedOut,
    TResult? Function(_DestroyedToken value)? destroyedToken,
    TResult? Function(_WelcomeBack value)? welcomeBack,
    TResult? Function(_SignedIn value)? signedIn,
    TResult? Function(_EnabledOfflineMode value)? enabledOfflineMode,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? refreshProfile,
    TResult Function(_Initial value)? reset,
    TResult Function(_UpdatedUser value)? updatedUser,
    TResult Function(_SignedOut value)? signedOut,
    TResult Function(_DestroyedToken value)? destroyedToken,
    TResult Function(_WelcomeBack value)? welcomeBack,
    TResult Function(_SignedIn value)? signedIn,
    TResult Function(_EnabledOfflineMode value)? enabledOfflineMode,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuthenticationEventCopyWith<$Res> {
  factory $AuthenticationEventCopyWith(
          AuthenticationEvent value, $Res Function(AuthenticationEvent) then) =
      _$AuthenticationEventCopyWithImpl<$Res, AuthenticationEvent>;
}

/// @nodoc
class _$AuthenticationEventCopyWithImpl<$Res, $Val extends AuthenticationEvent>
    implements $AuthenticationEventCopyWith<$Res> {
  _$AuthenticationEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AuthenticationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$StartedImplCopyWith<$Res> {
  factory _$$StartedImplCopyWith(
          _$StartedImpl value, $Res Function(_$StartedImpl) then) =
      __$$StartedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool updateProfileMetadata});
}

/// @nodoc
class __$$StartedImplCopyWithImpl<$Res>
    extends _$AuthenticationEventCopyWithImpl<$Res, _$StartedImpl>
    implements _$$StartedImplCopyWith<$Res> {
  __$$StartedImplCopyWithImpl(
      _$StartedImpl _value, $Res Function(_$StartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthenticationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? updateProfileMetadata = null,
  }) {
    return _then(_$StartedImpl(
      updateProfileMetadata: null == updateProfileMetadata
          ? _value.updateProfileMetadata
          : updateProfileMetadata // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$StartedImpl with DiagnosticableTreeMixin implements _Started {
  const _$StartedImpl({this.updateProfileMetadata = false});

  @override
  @JsonKey()
  final bool updateProfileMetadata;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthenticationEvent.refreshProfile(updateProfileMetadata: $updateProfileMetadata)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AuthenticationEvent.refreshProfile'))
      ..add(
          DiagnosticsProperty('updateProfileMetadata', updateProfileMetadata));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StartedImpl &&
            (identical(other.updateProfileMetadata, updateProfileMetadata) ||
                other.updateProfileMetadata == updateProfileMetadata));
  }

  @override
  int get hashCode => Object.hash(runtimeType, updateProfileMetadata);

  /// Create a copy of AuthenticationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StartedImplCopyWith<_$StartedImpl> get copyWith =>
      __$$StartedImplCopyWithImpl<_$StartedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool updateProfileMetadata) refreshProfile,
    required TResult Function() reset,
    required TResult Function(UserAttributesModel? user) updatedUser,
    required TResult Function() signedOut,
    required TResult Function() destroyedToken,
    required TResult Function(WelcomeBackMode mode, AuthenticationStatus status,
            UserAttributesModel? user, String? message)
        welcomeBack,
    required TResult Function(String token, UserAttributesModel? user) signedIn,
    required TResult Function() enabledOfflineMode,
  }) {
    return refreshProfile(updateProfileMetadata);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool updateProfileMetadata)? refreshProfile,
    TResult? Function()? reset,
    TResult? Function(UserAttributesModel? user)? updatedUser,
    TResult? Function()? signedOut,
    TResult? Function()? destroyedToken,
    TResult? Function(WelcomeBackMode mode, AuthenticationStatus status,
            UserAttributesModel? user, String? message)?
        welcomeBack,
    TResult? Function(String token, UserAttributesModel? user)? signedIn,
    TResult? Function()? enabledOfflineMode,
  }) {
    return refreshProfile?.call(updateProfileMetadata);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool updateProfileMetadata)? refreshProfile,
    TResult Function()? reset,
    TResult Function(UserAttributesModel? user)? updatedUser,
    TResult Function()? signedOut,
    TResult Function()? destroyedToken,
    TResult Function(WelcomeBackMode mode, AuthenticationStatus status,
            UserAttributesModel? user, String? message)?
        welcomeBack,
    TResult Function(String token, UserAttributesModel? user)? signedIn,
    TResult Function()? enabledOfflineMode,
    required TResult orElse(),
  }) {
    if (refreshProfile != null) {
      return refreshProfile(updateProfileMetadata);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) refreshProfile,
    required TResult Function(_Initial value) reset,
    required TResult Function(_UpdatedUser value) updatedUser,
    required TResult Function(_SignedOut value) signedOut,
    required TResult Function(_DestroyedToken value) destroyedToken,
    required TResult Function(_WelcomeBack value) welcomeBack,
    required TResult Function(_SignedIn value) signedIn,
    required TResult Function(_EnabledOfflineMode value) enabledOfflineMode,
  }) {
    return refreshProfile(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? refreshProfile,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_UpdatedUser value)? updatedUser,
    TResult? Function(_SignedOut value)? signedOut,
    TResult? Function(_DestroyedToken value)? destroyedToken,
    TResult? Function(_WelcomeBack value)? welcomeBack,
    TResult? Function(_SignedIn value)? signedIn,
    TResult? Function(_EnabledOfflineMode value)? enabledOfflineMode,
  }) {
    return refreshProfile?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? refreshProfile,
    TResult Function(_Initial value)? reset,
    TResult Function(_UpdatedUser value)? updatedUser,
    TResult Function(_SignedOut value)? signedOut,
    TResult Function(_DestroyedToken value)? destroyedToken,
    TResult Function(_WelcomeBack value)? welcomeBack,
    TResult Function(_SignedIn value)? signedIn,
    TResult Function(_EnabledOfflineMode value)? enabledOfflineMode,
    required TResult orElse(),
  }) {
    if (refreshProfile != null) {
      return refreshProfile(this);
    }
    return orElse();
  }
}

abstract class _Started implements AuthenticationEvent {
  const factory _Started({final bool updateProfileMetadata}) = _$StartedImpl;

  bool get updateProfileMetadata;

  /// Create a copy of AuthenticationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StartedImplCopyWith<_$StartedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$AuthenticationEventCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthenticationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl with DiagnosticableTreeMixin implements _Initial {
  const _$InitialImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthenticationEvent.reset()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'AuthenticationEvent.reset'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool updateProfileMetadata) refreshProfile,
    required TResult Function() reset,
    required TResult Function(UserAttributesModel? user) updatedUser,
    required TResult Function() signedOut,
    required TResult Function() destroyedToken,
    required TResult Function(WelcomeBackMode mode, AuthenticationStatus status,
            UserAttributesModel? user, String? message)
        welcomeBack,
    required TResult Function(String token, UserAttributesModel? user) signedIn,
    required TResult Function() enabledOfflineMode,
  }) {
    return reset();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool updateProfileMetadata)? refreshProfile,
    TResult? Function()? reset,
    TResult? Function(UserAttributesModel? user)? updatedUser,
    TResult? Function()? signedOut,
    TResult? Function()? destroyedToken,
    TResult? Function(WelcomeBackMode mode, AuthenticationStatus status,
            UserAttributesModel? user, String? message)?
        welcomeBack,
    TResult? Function(String token, UserAttributesModel? user)? signedIn,
    TResult? Function()? enabledOfflineMode,
  }) {
    return reset?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool updateProfileMetadata)? refreshProfile,
    TResult Function()? reset,
    TResult Function(UserAttributesModel? user)? updatedUser,
    TResult Function()? signedOut,
    TResult Function()? destroyedToken,
    TResult Function(WelcomeBackMode mode, AuthenticationStatus status,
            UserAttributesModel? user, String? message)?
        welcomeBack,
    TResult Function(String token, UserAttributesModel? user)? signedIn,
    TResult Function()? enabledOfflineMode,
    required TResult orElse(),
  }) {
    if (reset != null) {
      return reset();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) refreshProfile,
    required TResult Function(_Initial value) reset,
    required TResult Function(_UpdatedUser value) updatedUser,
    required TResult Function(_SignedOut value) signedOut,
    required TResult Function(_DestroyedToken value) destroyedToken,
    required TResult Function(_WelcomeBack value) welcomeBack,
    required TResult Function(_SignedIn value) signedIn,
    required TResult Function(_EnabledOfflineMode value) enabledOfflineMode,
  }) {
    return reset(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? refreshProfile,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_UpdatedUser value)? updatedUser,
    TResult? Function(_SignedOut value)? signedOut,
    TResult? Function(_DestroyedToken value)? destroyedToken,
    TResult? Function(_WelcomeBack value)? welcomeBack,
    TResult? Function(_SignedIn value)? signedIn,
    TResult? Function(_EnabledOfflineMode value)? enabledOfflineMode,
  }) {
    return reset?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? refreshProfile,
    TResult Function(_Initial value)? reset,
    TResult Function(_UpdatedUser value)? updatedUser,
    TResult Function(_SignedOut value)? signedOut,
    TResult Function(_DestroyedToken value)? destroyedToken,
    TResult Function(_WelcomeBack value)? welcomeBack,
    TResult Function(_SignedIn value)? signedIn,
    TResult Function(_EnabledOfflineMode value)? enabledOfflineMode,
    required TResult orElse(),
  }) {
    if (reset != null) {
      return reset(this);
    }
    return orElse();
  }
}

abstract class _Initial implements AuthenticationEvent {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$UpdatedUserImplCopyWith<$Res> {
  factory _$$UpdatedUserImplCopyWith(
          _$UpdatedUserImpl value, $Res Function(_$UpdatedUserImpl) then) =
      __$$UpdatedUserImplCopyWithImpl<$Res>;
  @useResult
  $Res call({UserAttributesModel? user});

  $UserAttributesModelCopyWith<$Res>? get user;
}

/// @nodoc
class __$$UpdatedUserImplCopyWithImpl<$Res>
    extends _$AuthenticationEventCopyWithImpl<$Res, _$UpdatedUserImpl>
    implements _$$UpdatedUserImplCopyWith<$Res> {
  __$$UpdatedUserImplCopyWithImpl(
      _$UpdatedUserImpl _value, $Res Function(_$UpdatedUserImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthenticationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? user = freezed,
  }) {
    return _then(_$UpdatedUserImpl(
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as UserAttributesModel?,
    ));
  }

  /// Create a copy of AuthenticationEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserAttributesModelCopyWith<$Res>? get user {
    if (_value.user == null) {
      return null;
    }

    return $UserAttributesModelCopyWith<$Res>(_value.user!, (value) {
      return _then(_value.copyWith(user: value));
    });
  }
}

/// @nodoc

class _$UpdatedUserImpl with DiagnosticableTreeMixin implements _UpdatedUser {
  const _$UpdatedUserImpl({this.user});

  @override
  final UserAttributesModel? user;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthenticationEvent.updatedUser(user: $user)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AuthenticationEvent.updatedUser'))
      ..add(DiagnosticsProperty('user', user));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdatedUserImpl &&
            (identical(other.user, user) || other.user == user));
  }

  @override
  int get hashCode => Object.hash(runtimeType, user);

  /// Create a copy of AuthenticationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdatedUserImplCopyWith<_$UpdatedUserImpl> get copyWith =>
      __$$UpdatedUserImplCopyWithImpl<_$UpdatedUserImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool updateProfileMetadata) refreshProfile,
    required TResult Function() reset,
    required TResult Function(UserAttributesModel? user) updatedUser,
    required TResult Function() signedOut,
    required TResult Function() destroyedToken,
    required TResult Function(WelcomeBackMode mode, AuthenticationStatus status,
            UserAttributesModel? user, String? message)
        welcomeBack,
    required TResult Function(String token, UserAttributesModel? user) signedIn,
    required TResult Function() enabledOfflineMode,
  }) {
    return updatedUser(user);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool updateProfileMetadata)? refreshProfile,
    TResult? Function()? reset,
    TResult? Function(UserAttributesModel? user)? updatedUser,
    TResult? Function()? signedOut,
    TResult? Function()? destroyedToken,
    TResult? Function(WelcomeBackMode mode, AuthenticationStatus status,
            UserAttributesModel? user, String? message)?
        welcomeBack,
    TResult? Function(String token, UserAttributesModel? user)? signedIn,
    TResult? Function()? enabledOfflineMode,
  }) {
    return updatedUser?.call(user);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool updateProfileMetadata)? refreshProfile,
    TResult Function()? reset,
    TResult Function(UserAttributesModel? user)? updatedUser,
    TResult Function()? signedOut,
    TResult Function()? destroyedToken,
    TResult Function(WelcomeBackMode mode, AuthenticationStatus status,
            UserAttributesModel? user, String? message)?
        welcomeBack,
    TResult Function(String token, UserAttributesModel? user)? signedIn,
    TResult Function()? enabledOfflineMode,
    required TResult orElse(),
  }) {
    if (updatedUser != null) {
      return updatedUser(user);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) refreshProfile,
    required TResult Function(_Initial value) reset,
    required TResult Function(_UpdatedUser value) updatedUser,
    required TResult Function(_SignedOut value) signedOut,
    required TResult Function(_DestroyedToken value) destroyedToken,
    required TResult Function(_WelcomeBack value) welcomeBack,
    required TResult Function(_SignedIn value) signedIn,
    required TResult Function(_EnabledOfflineMode value) enabledOfflineMode,
  }) {
    return updatedUser(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? refreshProfile,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_UpdatedUser value)? updatedUser,
    TResult? Function(_SignedOut value)? signedOut,
    TResult? Function(_DestroyedToken value)? destroyedToken,
    TResult? Function(_WelcomeBack value)? welcomeBack,
    TResult? Function(_SignedIn value)? signedIn,
    TResult? Function(_EnabledOfflineMode value)? enabledOfflineMode,
  }) {
    return updatedUser?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? refreshProfile,
    TResult Function(_Initial value)? reset,
    TResult Function(_UpdatedUser value)? updatedUser,
    TResult Function(_SignedOut value)? signedOut,
    TResult Function(_DestroyedToken value)? destroyedToken,
    TResult Function(_WelcomeBack value)? welcomeBack,
    TResult Function(_SignedIn value)? signedIn,
    TResult Function(_EnabledOfflineMode value)? enabledOfflineMode,
    required TResult orElse(),
  }) {
    if (updatedUser != null) {
      return updatedUser(this);
    }
    return orElse();
  }
}

abstract class _UpdatedUser implements AuthenticationEvent {
  const factory _UpdatedUser({final UserAttributesModel? user}) =
      _$UpdatedUserImpl;

  UserAttributesModel? get user;

  /// Create a copy of AuthenticationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdatedUserImplCopyWith<_$UpdatedUserImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SignedOutImplCopyWith<$Res> {
  factory _$$SignedOutImplCopyWith(
          _$SignedOutImpl value, $Res Function(_$SignedOutImpl) then) =
      __$$SignedOutImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SignedOutImplCopyWithImpl<$Res>
    extends _$AuthenticationEventCopyWithImpl<$Res, _$SignedOutImpl>
    implements _$$SignedOutImplCopyWith<$Res> {
  __$$SignedOutImplCopyWithImpl(
      _$SignedOutImpl _value, $Res Function(_$SignedOutImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthenticationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SignedOutImpl with DiagnosticableTreeMixin implements _SignedOut {
  const _$SignedOutImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthenticationEvent.signedOut()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AuthenticationEvent.signedOut'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SignedOutImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool updateProfileMetadata) refreshProfile,
    required TResult Function() reset,
    required TResult Function(UserAttributesModel? user) updatedUser,
    required TResult Function() signedOut,
    required TResult Function() destroyedToken,
    required TResult Function(WelcomeBackMode mode, AuthenticationStatus status,
            UserAttributesModel? user, String? message)
        welcomeBack,
    required TResult Function(String token, UserAttributesModel? user) signedIn,
    required TResult Function() enabledOfflineMode,
  }) {
    return signedOut();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool updateProfileMetadata)? refreshProfile,
    TResult? Function()? reset,
    TResult? Function(UserAttributesModel? user)? updatedUser,
    TResult? Function()? signedOut,
    TResult? Function()? destroyedToken,
    TResult? Function(WelcomeBackMode mode, AuthenticationStatus status,
            UserAttributesModel? user, String? message)?
        welcomeBack,
    TResult? Function(String token, UserAttributesModel? user)? signedIn,
    TResult? Function()? enabledOfflineMode,
  }) {
    return signedOut?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool updateProfileMetadata)? refreshProfile,
    TResult Function()? reset,
    TResult Function(UserAttributesModel? user)? updatedUser,
    TResult Function()? signedOut,
    TResult Function()? destroyedToken,
    TResult Function(WelcomeBackMode mode, AuthenticationStatus status,
            UserAttributesModel? user, String? message)?
        welcomeBack,
    TResult Function(String token, UserAttributesModel? user)? signedIn,
    TResult Function()? enabledOfflineMode,
    required TResult orElse(),
  }) {
    if (signedOut != null) {
      return signedOut();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) refreshProfile,
    required TResult Function(_Initial value) reset,
    required TResult Function(_UpdatedUser value) updatedUser,
    required TResult Function(_SignedOut value) signedOut,
    required TResult Function(_DestroyedToken value) destroyedToken,
    required TResult Function(_WelcomeBack value) welcomeBack,
    required TResult Function(_SignedIn value) signedIn,
    required TResult Function(_EnabledOfflineMode value) enabledOfflineMode,
  }) {
    return signedOut(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? refreshProfile,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_UpdatedUser value)? updatedUser,
    TResult? Function(_SignedOut value)? signedOut,
    TResult? Function(_DestroyedToken value)? destroyedToken,
    TResult? Function(_WelcomeBack value)? welcomeBack,
    TResult? Function(_SignedIn value)? signedIn,
    TResult? Function(_EnabledOfflineMode value)? enabledOfflineMode,
  }) {
    return signedOut?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? refreshProfile,
    TResult Function(_Initial value)? reset,
    TResult Function(_UpdatedUser value)? updatedUser,
    TResult Function(_SignedOut value)? signedOut,
    TResult Function(_DestroyedToken value)? destroyedToken,
    TResult Function(_WelcomeBack value)? welcomeBack,
    TResult Function(_SignedIn value)? signedIn,
    TResult Function(_EnabledOfflineMode value)? enabledOfflineMode,
    required TResult orElse(),
  }) {
    if (signedOut != null) {
      return signedOut(this);
    }
    return orElse();
  }
}

abstract class _SignedOut implements AuthenticationEvent {
  const factory _SignedOut() = _$SignedOutImpl;
}

/// @nodoc
abstract class _$$DestroyedTokenImplCopyWith<$Res> {
  factory _$$DestroyedTokenImplCopyWith(_$DestroyedTokenImpl value,
          $Res Function(_$DestroyedTokenImpl) then) =
      __$$DestroyedTokenImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DestroyedTokenImplCopyWithImpl<$Res>
    extends _$AuthenticationEventCopyWithImpl<$Res, _$DestroyedTokenImpl>
    implements _$$DestroyedTokenImplCopyWith<$Res> {
  __$$DestroyedTokenImplCopyWithImpl(
      _$DestroyedTokenImpl _value, $Res Function(_$DestroyedTokenImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthenticationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DestroyedTokenImpl
    with DiagnosticableTreeMixin
    implements _DestroyedToken {
  const _$DestroyedTokenImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthenticationEvent.destroyedToken()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AuthenticationEvent.destroyedToken'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$DestroyedTokenImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool updateProfileMetadata) refreshProfile,
    required TResult Function() reset,
    required TResult Function(UserAttributesModel? user) updatedUser,
    required TResult Function() signedOut,
    required TResult Function() destroyedToken,
    required TResult Function(WelcomeBackMode mode, AuthenticationStatus status,
            UserAttributesModel? user, String? message)
        welcomeBack,
    required TResult Function(String token, UserAttributesModel? user) signedIn,
    required TResult Function() enabledOfflineMode,
  }) {
    return destroyedToken();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool updateProfileMetadata)? refreshProfile,
    TResult? Function()? reset,
    TResult? Function(UserAttributesModel? user)? updatedUser,
    TResult? Function()? signedOut,
    TResult? Function()? destroyedToken,
    TResult? Function(WelcomeBackMode mode, AuthenticationStatus status,
            UserAttributesModel? user, String? message)?
        welcomeBack,
    TResult? Function(String token, UserAttributesModel? user)? signedIn,
    TResult? Function()? enabledOfflineMode,
  }) {
    return destroyedToken?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool updateProfileMetadata)? refreshProfile,
    TResult Function()? reset,
    TResult Function(UserAttributesModel? user)? updatedUser,
    TResult Function()? signedOut,
    TResult Function()? destroyedToken,
    TResult Function(WelcomeBackMode mode, AuthenticationStatus status,
            UserAttributesModel? user, String? message)?
        welcomeBack,
    TResult Function(String token, UserAttributesModel? user)? signedIn,
    TResult Function()? enabledOfflineMode,
    required TResult orElse(),
  }) {
    if (destroyedToken != null) {
      return destroyedToken();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) refreshProfile,
    required TResult Function(_Initial value) reset,
    required TResult Function(_UpdatedUser value) updatedUser,
    required TResult Function(_SignedOut value) signedOut,
    required TResult Function(_DestroyedToken value) destroyedToken,
    required TResult Function(_WelcomeBack value) welcomeBack,
    required TResult Function(_SignedIn value) signedIn,
    required TResult Function(_EnabledOfflineMode value) enabledOfflineMode,
  }) {
    return destroyedToken(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? refreshProfile,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_UpdatedUser value)? updatedUser,
    TResult? Function(_SignedOut value)? signedOut,
    TResult? Function(_DestroyedToken value)? destroyedToken,
    TResult? Function(_WelcomeBack value)? welcomeBack,
    TResult? Function(_SignedIn value)? signedIn,
    TResult? Function(_EnabledOfflineMode value)? enabledOfflineMode,
  }) {
    return destroyedToken?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? refreshProfile,
    TResult Function(_Initial value)? reset,
    TResult Function(_UpdatedUser value)? updatedUser,
    TResult Function(_SignedOut value)? signedOut,
    TResult Function(_DestroyedToken value)? destroyedToken,
    TResult Function(_WelcomeBack value)? welcomeBack,
    TResult Function(_SignedIn value)? signedIn,
    TResult Function(_EnabledOfflineMode value)? enabledOfflineMode,
    required TResult orElse(),
  }) {
    if (destroyedToken != null) {
      return destroyedToken(this);
    }
    return orElse();
  }
}

abstract class _DestroyedToken implements AuthenticationEvent {
  const factory _DestroyedToken() = _$DestroyedTokenImpl;
}

/// @nodoc
abstract class _$$WelcomeBackImplCopyWith<$Res> {
  factory _$$WelcomeBackImplCopyWith(
          _$WelcomeBackImpl value, $Res Function(_$WelcomeBackImpl) then) =
      __$$WelcomeBackImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {WelcomeBackMode mode,
      AuthenticationStatus status,
      UserAttributesModel? user,
      String? message});

  $UserAttributesModelCopyWith<$Res>? get user;
}

/// @nodoc
class __$$WelcomeBackImplCopyWithImpl<$Res>
    extends _$AuthenticationEventCopyWithImpl<$Res, _$WelcomeBackImpl>
    implements _$$WelcomeBackImplCopyWith<$Res> {
  __$$WelcomeBackImplCopyWithImpl(
      _$WelcomeBackImpl _value, $Res Function(_$WelcomeBackImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthenticationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mode = null,
    Object? status = null,
    Object? user = freezed,
    Object? message = freezed,
  }) {
    return _then(_$WelcomeBackImpl(
      mode: null == mode
          ? _value.mode
          : mode // ignore: cast_nullable_to_non_nullable
              as WelcomeBackMode,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as AuthenticationStatus,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as UserAttributesModel?,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of AuthenticationEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserAttributesModelCopyWith<$Res>? get user {
    if (_value.user == null) {
      return null;
    }

    return $UserAttributesModelCopyWith<$Res>(_value.user!, (value) {
      return _then(_value.copyWith(user: value));
    });
  }
}

/// @nodoc

class _$WelcomeBackImpl with DiagnosticableTreeMixin implements _WelcomeBack {
  const _$WelcomeBackImpl(
      {this.mode = WelcomeBackMode.proceedToHome,
      this.status = AuthenticationStatus.unknown,
      this.user,
      this.message});

  @override
  @JsonKey()
  final WelcomeBackMode mode;
  @override
  @JsonKey()
  final AuthenticationStatus status;
  @override
  final UserAttributesModel? user;
  @override
  final String? message;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthenticationEvent.welcomeBack(mode: $mode, status: $status, user: $user, message: $message)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AuthenticationEvent.welcomeBack'))
      ..add(DiagnosticsProperty('mode', mode))
      ..add(DiagnosticsProperty('status', status))
      ..add(DiagnosticsProperty('user', user))
      ..add(DiagnosticsProperty('message', message));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WelcomeBackImpl &&
            (identical(other.mode, mode) || other.mode == mode) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.user, user) || other.user == user) &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, mode, status, user, message);

  /// Create a copy of AuthenticationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WelcomeBackImplCopyWith<_$WelcomeBackImpl> get copyWith =>
      __$$WelcomeBackImplCopyWithImpl<_$WelcomeBackImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool updateProfileMetadata) refreshProfile,
    required TResult Function() reset,
    required TResult Function(UserAttributesModel? user) updatedUser,
    required TResult Function() signedOut,
    required TResult Function() destroyedToken,
    required TResult Function(WelcomeBackMode mode, AuthenticationStatus status,
            UserAttributesModel? user, String? message)
        welcomeBack,
    required TResult Function(String token, UserAttributesModel? user) signedIn,
    required TResult Function() enabledOfflineMode,
  }) {
    return welcomeBack(mode, status, user, message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool updateProfileMetadata)? refreshProfile,
    TResult? Function()? reset,
    TResult? Function(UserAttributesModel? user)? updatedUser,
    TResult? Function()? signedOut,
    TResult? Function()? destroyedToken,
    TResult? Function(WelcomeBackMode mode, AuthenticationStatus status,
            UserAttributesModel? user, String? message)?
        welcomeBack,
    TResult? Function(String token, UserAttributesModel? user)? signedIn,
    TResult? Function()? enabledOfflineMode,
  }) {
    return welcomeBack?.call(mode, status, user, message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool updateProfileMetadata)? refreshProfile,
    TResult Function()? reset,
    TResult Function(UserAttributesModel? user)? updatedUser,
    TResult Function()? signedOut,
    TResult Function()? destroyedToken,
    TResult Function(WelcomeBackMode mode, AuthenticationStatus status,
            UserAttributesModel? user, String? message)?
        welcomeBack,
    TResult Function(String token, UserAttributesModel? user)? signedIn,
    TResult Function()? enabledOfflineMode,
    required TResult orElse(),
  }) {
    if (welcomeBack != null) {
      return welcomeBack(mode, status, user, message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) refreshProfile,
    required TResult Function(_Initial value) reset,
    required TResult Function(_UpdatedUser value) updatedUser,
    required TResult Function(_SignedOut value) signedOut,
    required TResult Function(_DestroyedToken value) destroyedToken,
    required TResult Function(_WelcomeBack value) welcomeBack,
    required TResult Function(_SignedIn value) signedIn,
    required TResult Function(_EnabledOfflineMode value) enabledOfflineMode,
  }) {
    return welcomeBack(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? refreshProfile,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_UpdatedUser value)? updatedUser,
    TResult? Function(_SignedOut value)? signedOut,
    TResult? Function(_DestroyedToken value)? destroyedToken,
    TResult? Function(_WelcomeBack value)? welcomeBack,
    TResult? Function(_SignedIn value)? signedIn,
    TResult? Function(_EnabledOfflineMode value)? enabledOfflineMode,
  }) {
    return welcomeBack?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? refreshProfile,
    TResult Function(_Initial value)? reset,
    TResult Function(_UpdatedUser value)? updatedUser,
    TResult Function(_SignedOut value)? signedOut,
    TResult Function(_DestroyedToken value)? destroyedToken,
    TResult Function(_WelcomeBack value)? welcomeBack,
    TResult Function(_SignedIn value)? signedIn,
    TResult Function(_EnabledOfflineMode value)? enabledOfflineMode,
    required TResult orElse(),
  }) {
    if (welcomeBack != null) {
      return welcomeBack(this);
    }
    return orElse();
  }
}

abstract class _WelcomeBack implements AuthenticationEvent {
  const factory _WelcomeBack(
      {final WelcomeBackMode mode,
      final AuthenticationStatus status,
      final UserAttributesModel? user,
      final String? message}) = _$WelcomeBackImpl;

  WelcomeBackMode get mode;
  AuthenticationStatus get status;
  UserAttributesModel? get user;
  String? get message;

  /// Create a copy of AuthenticationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WelcomeBackImplCopyWith<_$WelcomeBackImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SignedInImplCopyWith<$Res> {
  factory _$$SignedInImplCopyWith(
          _$SignedInImpl value, $Res Function(_$SignedInImpl) then) =
      __$$SignedInImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String token, UserAttributesModel? user});

  $UserAttributesModelCopyWith<$Res>? get user;
}

/// @nodoc
class __$$SignedInImplCopyWithImpl<$Res>
    extends _$AuthenticationEventCopyWithImpl<$Res, _$SignedInImpl>
    implements _$$SignedInImplCopyWith<$Res> {
  __$$SignedInImplCopyWithImpl(
      _$SignedInImpl _value, $Res Function(_$SignedInImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthenticationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? token = null,
    Object? user = freezed,
  }) {
    return _then(_$SignedInImpl(
      token: null == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as UserAttributesModel?,
    ));
  }

  /// Create a copy of AuthenticationEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserAttributesModelCopyWith<$Res>? get user {
    if (_value.user == null) {
      return null;
    }

    return $UserAttributesModelCopyWith<$Res>(_value.user!, (value) {
      return _then(_value.copyWith(user: value));
    });
  }
}

/// @nodoc

class _$SignedInImpl with DiagnosticableTreeMixin implements _SignedIn {
  const _$SignedInImpl({required this.token, this.user});

  @override
  final String token;
  @override
  final UserAttributesModel? user;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthenticationEvent.signedIn(token: $token, user: $user)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'AuthenticationEvent.signedIn'))
      ..add(DiagnosticsProperty('token', token))
      ..add(DiagnosticsProperty('user', user));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SignedInImpl &&
            (identical(other.token, token) || other.token == token) &&
            (identical(other.user, user) || other.user == user));
  }

  @override
  int get hashCode => Object.hash(runtimeType, token, user);

  /// Create a copy of AuthenticationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SignedInImplCopyWith<_$SignedInImpl> get copyWith =>
      __$$SignedInImplCopyWithImpl<_$SignedInImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool updateProfileMetadata) refreshProfile,
    required TResult Function() reset,
    required TResult Function(UserAttributesModel? user) updatedUser,
    required TResult Function() signedOut,
    required TResult Function() destroyedToken,
    required TResult Function(WelcomeBackMode mode, AuthenticationStatus status,
            UserAttributesModel? user, String? message)
        welcomeBack,
    required TResult Function(String token, UserAttributesModel? user) signedIn,
    required TResult Function() enabledOfflineMode,
  }) {
    return signedIn(token, user);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool updateProfileMetadata)? refreshProfile,
    TResult? Function()? reset,
    TResult? Function(UserAttributesModel? user)? updatedUser,
    TResult? Function()? signedOut,
    TResult? Function()? destroyedToken,
    TResult? Function(WelcomeBackMode mode, AuthenticationStatus status,
            UserAttributesModel? user, String? message)?
        welcomeBack,
    TResult? Function(String token, UserAttributesModel? user)? signedIn,
    TResult? Function()? enabledOfflineMode,
  }) {
    return signedIn?.call(token, user);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool updateProfileMetadata)? refreshProfile,
    TResult Function()? reset,
    TResult Function(UserAttributesModel? user)? updatedUser,
    TResult Function()? signedOut,
    TResult Function()? destroyedToken,
    TResult Function(WelcomeBackMode mode, AuthenticationStatus status,
            UserAttributesModel? user, String? message)?
        welcomeBack,
    TResult Function(String token, UserAttributesModel? user)? signedIn,
    TResult Function()? enabledOfflineMode,
    required TResult orElse(),
  }) {
    if (signedIn != null) {
      return signedIn(token, user);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) refreshProfile,
    required TResult Function(_Initial value) reset,
    required TResult Function(_UpdatedUser value) updatedUser,
    required TResult Function(_SignedOut value) signedOut,
    required TResult Function(_DestroyedToken value) destroyedToken,
    required TResult Function(_WelcomeBack value) welcomeBack,
    required TResult Function(_SignedIn value) signedIn,
    required TResult Function(_EnabledOfflineMode value) enabledOfflineMode,
  }) {
    return signedIn(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? refreshProfile,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_UpdatedUser value)? updatedUser,
    TResult? Function(_SignedOut value)? signedOut,
    TResult? Function(_DestroyedToken value)? destroyedToken,
    TResult? Function(_WelcomeBack value)? welcomeBack,
    TResult? Function(_SignedIn value)? signedIn,
    TResult? Function(_EnabledOfflineMode value)? enabledOfflineMode,
  }) {
    return signedIn?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? refreshProfile,
    TResult Function(_Initial value)? reset,
    TResult Function(_UpdatedUser value)? updatedUser,
    TResult Function(_SignedOut value)? signedOut,
    TResult Function(_DestroyedToken value)? destroyedToken,
    TResult Function(_WelcomeBack value)? welcomeBack,
    TResult Function(_SignedIn value)? signedIn,
    TResult Function(_EnabledOfflineMode value)? enabledOfflineMode,
    required TResult orElse(),
  }) {
    if (signedIn != null) {
      return signedIn(this);
    }
    return orElse();
  }
}

abstract class _SignedIn implements AuthenticationEvent {
  const factory _SignedIn(
      {required final String token,
      final UserAttributesModel? user}) = _$SignedInImpl;

  String get token;
  UserAttributesModel? get user;

  /// Create a copy of AuthenticationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SignedInImplCopyWith<_$SignedInImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$EnabledOfflineModeImplCopyWith<$Res> {
  factory _$$EnabledOfflineModeImplCopyWith(_$EnabledOfflineModeImpl value,
          $Res Function(_$EnabledOfflineModeImpl) then) =
      __$$EnabledOfflineModeImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$EnabledOfflineModeImplCopyWithImpl<$Res>
    extends _$AuthenticationEventCopyWithImpl<$Res, _$EnabledOfflineModeImpl>
    implements _$$EnabledOfflineModeImplCopyWith<$Res> {
  __$$EnabledOfflineModeImplCopyWithImpl(_$EnabledOfflineModeImpl _value,
      $Res Function(_$EnabledOfflineModeImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthenticationEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$EnabledOfflineModeImpl
    with DiagnosticableTreeMixin
    implements _EnabledOfflineMode {
  const _$EnabledOfflineModeImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AuthenticationEvent.enabledOfflineMode()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty(
          'type', 'AuthenticationEvent.enabledOfflineMode'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$EnabledOfflineModeImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool updateProfileMetadata) refreshProfile,
    required TResult Function() reset,
    required TResult Function(UserAttributesModel? user) updatedUser,
    required TResult Function() signedOut,
    required TResult Function() destroyedToken,
    required TResult Function(WelcomeBackMode mode, AuthenticationStatus status,
            UserAttributesModel? user, String? message)
        welcomeBack,
    required TResult Function(String token, UserAttributesModel? user) signedIn,
    required TResult Function() enabledOfflineMode,
  }) {
    return enabledOfflineMode();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool updateProfileMetadata)? refreshProfile,
    TResult? Function()? reset,
    TResult? Function(UserAttributesModel? user)? updatedUser,
    TResult? Function()? signedOut,
    TResult? Function()? destroyedToken,
    TResult? Function(WelcomeBackMode mode, AuthenticationStatus status,
            UserAttributesModel? user, String? message)?
        welcomeBack,
    TResult? Function(String token, UserAttributesModel? user)? signedIn,
    TResult? Function()? enabledOfflineMode,
  }) {
    return enabledOfflineMode?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool updateProfileMetadata)? refreshProfile,
    TResult Function()? reset,
    TResult Function(UserAttributesModel? user)? updatedUser,
    TResult Function()? signedOut,
    TResult Function()? destroyedToken,
    TResult Function(WelcomeBackMode mode, AuthenticationStatus status,
            UserAttributesModel? user, String? message)?
        welcomeBack,
    TResult Function(String token, UserAttributesModel? user)? signedIn,
    TResult Function()? enabledOfflineMode,
    required TResult orElse(),
  }) {
    if (enabledOfflineMode != null) {
      return enabledOfflineMode();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) refreshProfile,
    required TResult Function(_Initial value) reset,
    required TResult Function(_UpdatedUser value) updatedUser,
    required TResult Function(_SignedOut value) signedOut,
    required TResult Function(_DestroyedToken value) destroyedToken,
    required TResult Function(_WelcomeBack value) welcomeBack,
    required TResult Function(_SignedIn value) signedIn,
    required TResult Function(_EnabledOfflineMode value) enabledOfflineMode,
  }) {
    return enabledOfflineMode(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? refreshProfile,
    TResult? Function(_Initial value)? reset,
    TResult? Function(_UpdatedUser value)? updatedUser,
    TResult? Function(_SignedOut value)? signedOut,
    TResult? Function(_DestroyedToken value)? destroyedToken,
    TResult? Function(_WelcomeBack value)? welcomeBack,
    TResult? Function(_SignedIn value)? signedIn,
    TResult? Function(_EnabledOfflineMode value)? enabledOfflineMode,
  }) {
    return enabledOfflineMode?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? refreshProfile,
    TResult Function(_Initial value)? reset,
    TResult Function(_UpdatedUser value)? updatedUser,
    TResult Function(_SignedOut value)? signedOut,
    TResult Function(_DestroyedToken value)? destroyedToken,
    TResult Function(_WelcomeBack value)? welcomeBack,
    TResult Function(_SignedIn value)? signedIn,
    TResult Function(_EnabledOfflineMode value)? enabledOfflineMode,
    required TResult orElse(),
  }) {
    if (enabledOfflineMode != null) {
      return enabledOfflineMode(this);
    }
    return orElse();
  }
}

abstract class _EnabledOfflineMode implements AuthenticationEvent {
  const factory _EnabledOfflineMode() = _$EnabledOfflineModeImpl;
}

AuthenticationState _$AuthenticationStateFromJson(Map<String, dynamic> json) {
  return _AuthenticationState.fromJson(json);
}

/// @nodoc
mixin _$AuthenticationState {
  UserAttributesModel? get user => throw _privateConstructorUsedError;
  String? get message => throw _privateConstructorUsedError;
  String? get code => throw _privateConstructorUsedError;
  @AuthenticationStatusConverter()
  AuthenticationStatus get status => throw _privateConstructorUsedError;
  WelcomeBackMode get welcomeBackMode => throw _privateConstructorUsedError;

  /// Serializes this AuthenticationState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AuthenticationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AuthenticationStateCopyWith<AuthenticationState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuthenticationStateCopyWith<$Res> {
  factory $AuthenticationStateCopyWith(
          AuthenticationState value, $Res Function(AuthenticationState) then) =
      _$AuthenticationStateCopyWithImpl<$Res, AuthenticationState>;
  @useResult
  $Res call(
      {UserAttributesModel? user,
      String? message,
      String? code,
      @AuthenticationStatusConverter() AuthenticationStatus status,
      WelcomeBackMode welcomeBackMode});

  $UserAttributesModelCopyWith<$Res>? get user;
}

/// @nodoc
class _$AuthenticationStateCopyWithImpl<$Res, $Val extends AuthenticationState>
    implements $AuthenticationStateCopyWith<$Res> {
  _$AuthenticationStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AuthenticationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? user = freezed,
    Object? message = freezed,
    Object? code = freezed,
    Object? status = null,
    Object? welcomeBackMode = null,
  }) {
    return _then(_value.copyWith(
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as UserAttributesModel?,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as AuthenticationStatus,
      welcomeBackMode: null == welcomeBackMode
          ? _value.welcomeBackMode
          : welcomeBackMode // ignore: cast_nullable_to_non_nullable
              as WelcomeBackMode,
    ) as $Val);
  }

  /// Create a copy of AuthenticationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserAttributesModelCopyWith<$Res>? get user {
    if (_value.user == null) {
      return null;
    }

    return $UserAttributesModelCopyWith<$Res>(_value.user!, (value) {
      return _then(_value.copyWith(user: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AuthenticationStateImplCopyWith<$Res>
    implements $AuthenticationStateCopyWith<$Res> {
  factory _$$AuthenticationStateImplCopyWith(_$AuthenticationStateImpl value,
          $Res Function(_$AuthenticationStateImpl) then) =
      __$$AuthenticationStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {UserAttributesModel? user,
      String? message,
      String? code,
      @AuthenticationStatusConverter() AuthenticationStatus status,
      WelcomeBackMode welcomeBackMode});

  @override
  $UserAttributesModelCopyWith<$Res>? get user;
}

/// @nodoc
class __$$AuthenticationStateImplCopyWithImpl<$Res>
    extends _$AuthenticationStateCopyWithImpl<$Res, _$AuthenticationStateImpl>
    implements _$$AuthenticationStateImplCopyWith<$Res> {
  __$$AuthenticationStateImplCopyWithImpl(_$AuthenticationStateImpl _value,
      $Res Function(_$AuthenticationStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthenticationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? user = freezed,
    Object? message = freezed,
    Object? code = freezed,
    Object? status = null,
    Object? welcomeBackMode = null,
  }) {
    return _then(_$AuthenticationStateImpl(
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as UserAttributesModel?,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as AuthenticationStatus,
      welcomeBackMode: null == welcomeBackMode
          ? _value.welcomeBackMode
          : welcomeBackMode // ignore: cast_nullable_to_non_nullable
              as WelcomeBackMode,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AuthenticationStateImpl extends _AuthenticationState {
  _$AuthenticationStateImpl(
      {this.user,
      this.message,
      this.code,
      @AuthenticationStatusConverter()
      this.status = AuthenticationStatus.unknown,
      this.welcomeBackMode = WelcomeBackMode.unknown})
      : super._();

  factory _$AuthenticationStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$AuthenticationStateImplFromJson(json);

  @override
  final UserAttributesModel? user;
  @override
  final String? message;
  @override
  final String? code;
  @override
  @JsonKey()
  @AuthenticationStatusConverter()
  final AuthenticationStatus status;
  @override
  @JsonKey()
  final WelcomeBackMode welcomeBackMode;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthenticationStateImpl &&
            (identical(other.user, user) || other.user == user) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.welcomeBackMode, welcomeBackMode) ||
                other.welcomeBackMode == welcomeBackMode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, user, message, code, status, welcomeBackMode);

  /// Create a copy of AuthenticationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AuthenticationStateImplCopyWith<_$AuthenticationStateImpl> get copyWith =>
      __$$AuthenticationStateImplCopyWithImpl<_$AuthenticationStateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AuthenticationStateImplToJson(
      this,
    );
  }
}

abstract class _AuthenticationState extends AuthenticationState {
  factory _AuthenticationState(
      {final UserAttributesModel? user,
      final String? message,
      final String? code,
      @AuthenticationStatusConverter() final AuthenticationStatus status,
      final WelcomeBackMode welcomeBackMode}) = _$AuthenticationStateImpl;
  _AuthenticationState._() : super._();

  factory _AuthenticationState.fromJson(Map<String, dynamic> json) =
      _$AuthenticationStateImpl.fromJson;

  @override
  UserAttributesModel? get user;
  @override
  String? get message;
  @override
  String? get code;
  @override
  @AuthenticationStatusConverter()
  AuthenticationStatus get status;
  @override
  WelcomeBackMode get welcomeBackMode;

  /// Create a copy of AuthenticationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AuthenticationStateImplCopyWith<_$AuthenticationStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
