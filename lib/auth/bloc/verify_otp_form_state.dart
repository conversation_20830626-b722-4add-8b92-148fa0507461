part of 'verify_otp_form_bloc.dart';

@freezed
class VerifyOtpFormState with _$VerifyOtpFormState, FormzMixin {
  factory VerifyOtpFormState({
    @Default(MinimumInput.pure()) MinimumInput otp,
    String? phoneNumber,
    CountryModel? country,
    String? otpErrorMessage,
    String? phoneNumberErrorMessage,
    String? countryCodeErrorMessage,
    String? message,
    String? token,
    UserAttributesModel? user,
    @Default(FormzSubmissionStatus.initial) FormzSubmissionStatus status,
    /// Standardized phone number data for consistent handling
    PhoneNumberData? phoneNumberData,
  }) = _VerifyOtpFormState;

  factory VerifyOtpFormState.initial() => VerifyOtpFormState();

  const VerifyOtpFormState._();

  bool get isSubmissionSuccess {
    return status == FormzSubmissionStatus.success;
  }

  bool get isSubmissionFailure {
    return status == FormzSubmissionStatus.failure;
  }

  bool get isSubmissionInProgress {
    return status == FormzSubmissionStatus.inProgress;
  }

  bool get isSubmissionInitial {
    return status == FormzSubmissionStatus.initial;
  }

  @override
  List<FormzInput<String, dynamic>> get inputs {
    return [
      otp,
    ];
  }
}
