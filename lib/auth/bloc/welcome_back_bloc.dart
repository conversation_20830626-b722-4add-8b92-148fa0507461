import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

// import 'package:local_auth/local_auth.dart';

part 'welcome_back_bloc.freezed.dart';
part 'welcome_back_event.dart';
part 'welcome_back_state.dart';

class WelcomeBackBloc extends Bloc<WelcomeBackEvent, WelcomeBackState> {
  WelcomeBackBloc() : super(const WelcomeBackState.initial()) {
    on<_Started>(_onStarted);
  }

  // final LocalAuthentication auth = LocalAuthentication();

  FutureOr<void> _onStarted(
    _Started event,
    Emitter<WelcomeBackState> emit,
  ) async {
    // final canAuthenticateWithBiometrics = await auth.canCheckBiometrics;
    // final canAuthenticate =
    //     canAuthenticateWithBiometrics || await auth.isDeviceSupported();

    //     try {
    //   final bool didAuthenticate = await auth.authenticate(
    //       localizedReason: 'Please authenticate to show account balance',
    //       options: const AuthenticationOptions(useErrorDialogs: false));
    //   // ···
    // } on PlatformException catch (e) {
    //   if (e.code == auth_error.notAvailable) {
    //     // Add handling of no hardware here.
    //   } else if (e.code == auth_error.notEnrolled) {
    //     // ...
    //   } else {
    //     // ...
    //   }
    // }
  }
}
