// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'welcome_back_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$WelcomeBackEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WelcomeBackEventCopyWith<$Res> {
  factory $WelcomeBackEventCopyWith(
          WelcomeBackEvent value, $Res Function(WelcomeBackEvent) then) =
      _$WelcomeBackEventCopyWithImpl<$Res, WelcomeBackEvent>;
}

/// @nodoc
class _$WelcomeBackEventCopyWithImpl<$Res, $Val extends WelcomeBackEvent>
    implements $WelcomeBackEventCopyWith<$Res> {
  _$WelcomeBackEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WelcomeBackEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$StartedImplCopyWith<$Res> {
  factory _$$StartedImplCopyWith(
          _$StartedImpl value, $Res Function(_$StartedImpl) then) =
      __$$StartedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$StartedImplCopyWithImpl<$Res>
    extends _$WelcomeBackEventCopyWithImpl<$Res, _$StartedImpl>
    implements _$$StartedImplCopyWith<$Res> {
  __$$StartedImplCopyWithImpl(
      _$StartedImpl _value, $Res Function(_$StartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of WelcomeBackEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$StartedImpl implements _Started {
  const _$StartedImpl();

  @override
  String toString() {
    return 'WelcomeBackEvent.started()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$StartedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
  }) {
    return started();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
  }) {
    return started?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
  }) {
    return started(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
  }) {
    return started?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started(this);
    }
    return orElse();
  }
}

abstract class _Started implements WelcomeBackEvent {
  const factory _Started() = _$StartedImpl;
}

/// @nodoc
mixin _$WelcomeBackState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() possible,
    required TResult Function() impossible,
    required TResult Function() localAuthSuccess,
    required TResult Function() localAuthFailed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? possible,
    TResult? Function()? impossible,
    TResult? Function()? localAuthSuccess,
    TResult? Function()? localAuthFailed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? possible,
    TResult Function()? impossible,
    TResult Function()? localAuthSuccess,
    TResult Function()? localAuthFailed,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(WelcomeBackInitial value) initial,
    required TResult Function(WelcomeBackCanAuthenticateLocally value) possible,
    required TResult Function(WelcomeBackCanNotAuthenticateLocally value)
        impossible,
    required TResult Function(WelcomeBackAuthenticationSuccessful value)
        localAuthSuccess,
    required TResult Function(WelcomeBackAuthenticationFailed value)
        localAuthFailed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(WelcomeBackInitial value)? initial,
    TResult? Function(WelcomeBackCanAuthenticateLocally value)? possible,
    TResult? Function(WelcomeBackCanNotAuthenticateLocally value)? impossible,
    TResult? Function(WelcomeBackAuthenticationSuccessful value)?
        localAuthSuccess,
    TResult? Function(WelcomeBackAuthenticationFailed value)? localAuthFailed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(WelcomeBackInitial value)? initial,
    TResult Function(WelcomeBackCanAuthenticateLocally value)? possible,
    TResult Function(WelcomeBackCanNotAuthenticateLocally value)? impossible,
    TResult Function(WelcomeBackAuthenticationSuccessful value)?
        localAuthSuccess,
    TResult Function(WelcomeBackAuthenticationFailed value)? localAuthFailed,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WelcomeBackStateCopyWith<$Res> {
  factory $WelcomeBackStateCopyWith(
          WelcomeBackState value, $Res Function(WelcomeBackState) then) =
      _$WelcomeBackStateCopyWithImpl<$Res, WelcomeBackState>;
}

/// @nodoc
class _$WelcomeBackStateCopyWithImpl<$Res, $Val extends WelcomeBackState>
    implements $WelcomeBackStateCopyWith<$Res> {
  _$WelcomeBackStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WelcomeBackState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$WelcomeBackInitialImplCopyWith<$Res> {
  factory _$$WelcomeBackInitialImplCopyWith(_$WelcomeBackInitialImpl value,
          $Res Function(_$WelcomeBackInitialImpl) then) =
      __$$WelcomeBackInitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$WelcomeBackInitialImplCopyWithImpl<$Res>
    extends _$WelcomeBackStateCopyWithImpl<$Res, _$WelcomeBackInitialImpl>
    implements _$$WelcomeBackInitialImplCopyWith<$Res> {
  __$$WelcomeBackInitialImplCopyWithImpl(_$WelcomeBackInitialImpl _value,
      $Res Function(_$WelcomeBackInitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of WelcomeBackState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$WelcomeBackInitialImpl implements WelcomeBackInitial {
  const _$WelcomeBackInitialImpl();

  @override
  String toString() {
    return 'WelcomeBackState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$WelcomeBackInitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() possible,
    required TResult Function() impossible,
    required TResult Function() localAuthSuccess,
    required TResult Function() localAuthFailed,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? possible,
    TResult? Function()? impossible,
    TResult? Function()? localAuthSuccess,
    TResult? Function()? localAuthFailed,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? possible,
    TResult Function()? impossible,
    TResult Function()? localAuthSuccess,
    TResult Function()? localAuthFailed,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(WelcomeBackInitial value) initial,
    required TResult Function(WelcomeBackCanAuthenticateLocally value) possible,
    required TResult Function(WelcomeBackCanNotAuthenticateLocally value)
        impossible,
    required TResult Function(WelcomeBackAuthenticationSuccessful value)
        localAuthSuccess,
    required TResult Function(WelcomeBackAuthenticationFailed value)
        localAuthFailed,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(WelcomeBackInitial value)? initial,
    TResult? Function(WelcomeBackCanAuthenticateLocally value)? possible,
    TResult? Function(WelcomeBackCanNotAuthenticateLocally value)? impossible,
    TResult? Function(WelcomeBackAuthenticationSuccessful value)?
        localAuthSuccess,
    TResult? Function(WelcomeBackAuthenticationFailed value)? localAuthFailed,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(WelcomeBackInitial value)? initial,
    TResult Function(WelcomeBackCanAuthenticateLocally value)? possible,
    TResult Function(WelcomeBackCanNotAuthenticateLocally value)? impossible,
    TResult Function(WelcomeBackAuthenticationSuccessful value)?
        localAuthSuccess,
    TResult Function(WelcomeBackAuthenticationFailed value)? localAuthFailed,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class WelcomeBackInitial implements WelcomeBackState {
  const factory WelcomeBackInitial() = _$WelcomeBackInitialImpl;
}

/// @nodoc
abstract class _$$WelcomeBackCanAuthenticateLocallyImplCopyWith<$Res> {
  factory _$$WelcomeBackCanAuthenticateLocallyImplCopyWith(
          _$WelcomeBackCanAuthenticateLocallyImpl value,
          $Res Function(_$WelcomeBackCanAuthenticateLocallyImpl) then) =
      __$$WelcomeBackCanAuthenticateLocallyImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$WelcomeBackCanAuthenticateLocallyImplCopyWithImpl<$Res>
    extends _$WelcomeBackStateCopyWithImpl<$Res,
        _$WelcomeBackCanAuthenticateLocallyImpl>
    implements _$$WelcomeBackCanAuthenticateLocallyImplCopyWith<$Res> {
  __$$WelcomeBackCanAuthenticateLocallyImplCopyWithImpl(
      _$WelcomeBackCanAuthenticateLocallyImpl _value,
      $Res Function(_$WelcomeBackCanAuthenticateLocallyImpl) _then)
      : super(_value, _then);

  /// Create a copy of WelcomeBackState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$WelcomeBackCanAuthenticateLocallyImpl
    implements WelcomeBackCanAuthenticateLocally {
  const _$WelcomeBackCanAuthenticateLocallyImpl();

  @override
  String toString() {
    return 'WelcomeBackState.possible()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WelcomeBackCanAuthenticateLocallyImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() possible,
    required TResult Function() impossible,
    required TResult Function() localAuthSuccess,
    required TResult Function() localAuthFailed,
  }) {
    return possible();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? possible,
    TResult? Function()? impossible,
    TResult? Function()? localAuthSuccess,
    TResult? Function()? localAuthFailed,
  }) {
    return possible?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? possible,
    TResult Function()? impossible,
    TResult Function()? localAuthSuccess,
    TResult Function()? localAuthFailed,
    required TResult orElse(),
  }) {
    if (possible != null) {
      return possible();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(WelcomeBackInitial value) initial,
    required TResult Function(WelcomeBackCanAuthenticateLocally value) possible,
    required TResult Function(WelcomeBackCanNotAuthenticateLocally value)
        impossible,
    required TResult Function(WelcomeBackAuthenticationSuccessful value)
        localAuthSuccess,
    required TResult Function(WelcomeBackAuthenticationFailed value)
        localAuthFailed,
  }) {
    return possible(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(WelcomeBackInitial value)? initial,
    TResult? Function(WelcomeBackCanAuthenticateLocally value)? possible,
    TResult? Function(WelcomeBackCanNotAuthenticateLocally value)? impossible,
    TResult? Function(WelcomeBackAuthenticationSuccessful value)?
        localAuthSuccess,
    TResult? Function(WelcomeBackAuthenticationFailed value)? localAuthFailed,
  }) {
    return possible?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(WelcomeBackInitial value)? initial,
    TResult Function(WelcomeBackCanAuthenticateLocally value)? possible,
    TResult Function(WelcomeBackCanNotAuthenticateLocally value)? impossible,
    TResult Function(WelcomeBackAuthenticationSuccessful value)?
        localAuthSuccess,
    TResult Function(WelcomeBackAuthenticationFailed value)? localAuthFailed,
    required TResult orElse(),
  }) {
    if (possible != null) {
      return possible(this);
    }
    return orElse();
  }
}

abstract class WelcomeBackCanAuthenticateLocally implements WelcomeBackState {
  const factory WelcomeBackCanAuthenticateLocally() =
      _$WelcomeBackCanAuthenticateLocallyImpl;
}

/// @nodoc
abstract class _$$WelcomeBackCanNotAuthenticateLocallyImplCopyWith<$Res> {
  factory _$$WelcomeBackCanNotAuthenticateLocallyImplCopyWith(
          _$WelcomeBackCanNotAuthenticateLocallyImpl value,
          $Res Function(_$WelcomeBackCanNotAuthenticateLocallyImpl) then) =
      __$$WelcomeBackCanNotAuthenticateLocallyImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$WelcomeBackCanNotAuthenticateLocallyImplCopyWithImpl<$Res>
    extends _$WelcomeBackStateCopyWithImpl<$Res,
        _$WelcomeBackCanNotAuthenticateLocallyImpl>
    implements _$$WelcomeBackCanNotAuthenticateLocallyImplCopyWith<$Res> {
  __$$WelcomeBackCanNotAuthenticateLocallyImplCopyWithImpl(
      _$WelcomeBackCanNotAuthenticateLocallyImpl _value,
      $Res Function(_$WelcomeBackCanNotAuthenticateLocallyImpl) _then)
      : super(_value, _then);

  /// Create a copy of WelcomeBackState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$WelcomeBackCanNotAuthenticateLocallyImpl
    implements WelcomeBackCanNotAuthenticateLocally {
  const _$WelcomeBackCanNotAuthenticateLocallyImpl();

  @override
  String toString() {
    return 'WelcomeBackState.impossible()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WelcomeBackCanNotAuthenticateLocallyImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() possible,
    required TResult Function() impossible,
    required TResult Function() localAuthSuccess,
    required TResult Function() localAuthFailed,
  }) {
    return impossible();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? possible,
    TResult? Function()? impossible,
    TResult? Function()? localAuthSuccess,
    TResult? Function()? localAuthFailed,
  }) {
    return impossible?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? possible,
    TResult Function()? impossible,
    TResult Function()? localAuthSuccess,
    TResult Function()? localAuthFailed,
    required TResult orElse(),
  }) {
    if (impossible != null) {
      return impossible();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(WelcomeBackInitial value) initial,
    required TResult Function(WelcomeBackCanAuthenticateLocally value) possible,
    required TResult Function(WelcomeBackCanNotAuthenticateLocally value)
        impossible,
    required TResult Function(WelcomeBackAuthenticationSuccessful value)
        localAuthSuccess,
    required TResult Function(WelcomeBackAuthenticationFailed value)
        localAuthFailed,
  }) {
    return impossible(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(WelcomeBackInitial value)? initial,
    TResult? Function(WelcomeBackCanAuthenticateLocally value)? possible,
    TResult? Function(WelcomeBackCanNotAuthenticateLocally value)? impossible,
    TResult? Function(WelcomeBackAuthenticationSuccessful value)?
        localAuthSuccess,
    TResult? Function(WelcomeBackAuthenticationFailed value)? localAuthFailed,
  }) {
    return impossible?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(WelcomeBackInitial value)? initial,
    TResult Function(WelcomeBackCanAuthenticateLocally value)? possible,
    TResult Function(WelcomeBackCanNotAuthenticateLocally value)? impossible,
    TResult Function(WelcomeBackAuthenticationSuccessful value)?
        localAuthSuccess,
    TResult Function(WelcomeBackAuthenticationFailed value)? localAuthFailed,
    required TResult orElse(),
  }) {
    if (impossible != null) {
      return impossible(this);
    }
    return orElse();
  }
}

abstract class WelcomeBackCanNotAuthenticateLocally
    implements WelcomeBackState {
  const factory WelcomeBackCanNotAuthenticateLocally() =
      _$WelcomeBackCanNotAuthenticateLocallyImpl;
}

/// @nodoc
abstract class _$$WelcomeBackAuthenticationSuccessfulImplCopyWith<$Res> {
  factory _$$WelcomeBackAuthenticationSuccessfulImplCopyWith(
          _$WelcomeBackAuthenticationSuccessfulImpl value,
          $Res Function(_$WelcomeBackAuthenticationSuccessfulImpl) then) =
      __$$WelcomeBackAuthenticationSuccessfulImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$WelcomeBackAuthenticationSuccessfulImplCopyWithImpl<$Res>
    extends _$WelcomeBackStateCopyWithImpl<$Res,
        _$WelcomeBackAuthenticationSuccessfulImpl>
    implements _$$WelcomeBackAuthenticationSuccessfulImplCopyWith<$Res> {
  __$$WelcomeBackAuthenticationSuccessfulImplCopyWithImpl(
      _$WelcomeBackAuthenticationSuccessfulImpl _value,
      $Res Function(_$WelcomeBackAuthenticationSuccessfulImpl) _then)
      : super(_value, _then);

  /// Create a copy of WelcomeBackState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$WelcomeBackAuthenticationSuccessfulImpl
    implements WelcomeBackAuthenticationSuccessful {
  const _$WelcomeBackAuthenticationSuccessfulImpl();

  @override
  String toString() {
    return 'WelcomeBackState.localAuthSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WelcomeBackAuthenticationSuccessfulImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() possible,
    required TResult Function() impossible,
    required TResult Function() localAuthSuccess,
    required TResult Function() localAuthFailed,
  }) {
    return localAuthSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? possible,
    TResult? Function()? impossible,
    TResult? Function()? localAuthSuccess,
    TResult? Function()? localAuthFailed,
  }) {
    return localAuthSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? possible,
    TResult Function()? impossible,
    TResult Function()? localAuthSuccess,
    TResult Function()? localAuthFailed,
    required TResult orElse(),
  }) {
    if (localAuthSuccess != null) {
      return localAuthSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(WelcomeBackInitial value) initial,
    required TResult Function(WelcomeBackCanAuthenticateLocally value) possible,
    required TResult Function(WelcomeBackCanNotAuthenticateLocally value)
        impossible,
    required TResult Function(WelcomeBackAuthenticationSuccessful value)
        localAuthSuccess,
    required TResult Function(WelcomeBackAuthenticationFailed value)
        localAuthFailed,
  }) {
    return localAuthSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(WelcomeBackInitial value)? initial,
    TResult? Function(WelcomeBackCanAuthenticateLocally value)? possible,
    TResult? Function(WelcomeBackCanNotAuthenticateLocally value)? impossible,
    TResult? Function(WelcomeBackAuthenticationSuccessful value)?
        localAuthSuccess,
    TResult? Function(WelcomeBackAuthenticationFailed value)? localAuthFailed,
  }) {
    return localAuthSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(WelcomeBackInitial value)? initial,
    TResult Function(WelcomeBackCanAuthenticateLocally value)? possible,
    TResult Function(WelcomeBackCanNotAuthenticateLocally value)? impossible,
    TResult Function(WelcomeBackAuthenticationSuccessful value)?
        localAuthSuccess,
    TResult Function(WelcomeBackAuthenticationFailed value)? localAuthFailed,
    required TResult orElse(),
  }) {
    if (localAuthSuccess != null) {
      return localAuthSuccess(this);
    }
    return orElse();
  }
}

abstract class WelcomeBackAuthenticationSuccessful implements WelcomeBackState {
  const factory WelcomeBackAuthenticationSuccessful() =
      _$WelcomeBackAuthenticationSuccessfulImpl;
}

/// @nodoc
abstract class _$$WelcomeBackAuthenticationFailedImplCopyWith<$Res> {
  factory _$$WelcomeBackAuthenticationFailedImplCopyWith(
          _$WelcomeBackAuthenticationFailedImpl value,
          $Res Function(_$WelcomeBackAuthenticationFailedImpl) then) =
      __$$WelcomeBackAuthenticationFailedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$WelcomeBackAuthenticationFailedImplCopyWithImpl<$Res>
    extends _$WelcomeBackStateCopyWithImpl<$Res,
        _$WelcomeBackAuthenticationFailedImpl>
    implements _$$WelcomeBackAuthenticationFailedImplCopyWith<$Res> {
  __$$WelcomeBackAuthenticationFailedImplCopyWithImpl(
      _$WelcomeBackAuthenticationFailedImpl _value,
      $Res Function(_$WelcomeBackAuthenticationFailedImpl) _then)
      : super(_value, _then);

  /// Create a copy of WelcomeBackState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$WelcomeBackAuthenticationFailedImpl
    implements WelcomeBackAuthenticationFailed {
  const _$WelcomeBackAuthenticationFailedImpl();

  @override
  String toString() {
    return 'WelcomeBackState.localAuthFailed()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WelcomeBackAuthenticationFailedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() possible,
    required TResult Function() impossible,
    required TResult Function() localAuthSuccess,
    required TResult Function() localAuthFailed,
  }) {
    return localAuthFailed();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? possible,
    TResult? Function()? impossible,
    TResult? Function()? localAuthSuccess,
    TResult? Function()? localAuthFailed,
  }) {
    return localAuthFailed?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? possible,
    TResult Function()? impossible,
    TResult Function()? localAuthSuccess,
    TResult Function()? localAuthFailed,
    required TResult orElse(),
  }) {
    if (localAuthFailed != null) {
      return localAuthFailed();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(WelcomeBackInitial value) initial,
    required TResult Function(WelcomeBackCanAuthenticateLocally value) possible,
    required TResult Function(WelcomeBackCanNotAuthenticateLocally value)
        impossible,
    required TResult Function(WelcomeBackAuthenticationSuccessful value)
        localAuthSuccess,
    required TResult Function(WelcomeBackAuthenticationFailed value)
        localAuthFailed,
  }) {
    return localAuthFailed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(WelcomeBackInitial value)? initial,
    TResult? Function(WelcomeBackCanAuthenticateLocally value)? possible,
    TResult? Function(WelcomeBackCanNotAuthenticateLocally value)? impossible,
    TResult? Function(WelcomeBackAuthenticationSuccessful value)?
        localAuthSuccess,
    TResult? Function(WelcomeBackAuthenticationFailed value)? localAuthFailed,
  }) {
    return localAuthFailed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(WelcomeBackInitial value)? initial,
    TResult Function(WelcomeBackCanAuthenticateLocally value)? possible,
    TResult Function(WelcomeBackCanNotAuthenticateLocally value)? impossible,
    TResult Function(WelcomeBackAuthenticationSuccessful value)?
        localAuthSuccess,
    TResult Function(WelcomeBackAuthenticationFailed value)? localAuthFailed,
    required TResult orElse(),
  }) {
    if (localAuthFailed != null) {
      return localAuthFailed(this);
    }
    return orElse();
  }
}

abstract class WelcomeBackAuthenticationFailed implements WelcomeBackState {
  const factory WelcomeBackAuthenticationFailed() =
      _$WelcomeBackAuthenticationFailedImpl;
}
