// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'authentication_bloc.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AuthenticationStateImpl _$$AuthenticationStateImplFromJson(
        Map<String, dynamic> json) =>
    _$AuthenticationStateImpl(
      user: json['user'] == null
          ? null
          : UserAttributesModel.fromJson(json['user'] as Map<String, dynamic>),
      message: json['message'] as String?,
      code: json['code'] as String?,
      status: json['status'] == null
          ? AuthenticationStatus.unknown
          : const AuthenticationStatusConverter()
              .fromJson(json['status'] as String),
      welcomeBackMode: $enumDecodeNullable(
              _$WelcomeBackModeEnumMap, json['welcomeBackMode']) ??
          WelcomeBackMode.unknown,
    );

const _$$AuthenticationStateImplFieldMap = <String, String>{
  'user': 'user',
  'message': 'message',
  'code': 'code',
  'status': 'status',
  'welcomeBackMode': 'welcomeBackMode',
};

Map<String, dynamic> _$$AuthenticationStateImplToJson(
        _$AuthenticationStateImpl instance) =>
    <String, dynamic>{
      if (instance.user?.toJson() case final value?) 'user': value,
      if (instance.message case final value?) 'message': value,
      if (instance.code case final value?) 'code': value,
      'status': const AuthenticationStatusConverter().toJson(instance.status),
      'welcomeBackMode': _$WelcomeBackModeEnumMap[instance.welcomeBackMode]!,
    };

const _$WelcomeBackModeEnumMap = {
  WelcomeBackMode.unknown: 'unknown',
  WelcomeBackMode.proceedToHome: 'proceedToHome',
  WelcomeBackMode.needsBiometrics: 'needsBiometrics',
  WelcomeBackMode.needsPassword: 'needsPassword',
  WelcomeBackMode.needsPincode: 'needsPincode',
};
