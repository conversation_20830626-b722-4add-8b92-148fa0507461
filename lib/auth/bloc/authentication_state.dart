part of 'authentication_bloc.dart';

@freezed
class AuthenticationState with _$AuthenticationState {
  factory AuthenticationState({
    UserAttributesModel? user,
    String? message,
    String? code,
    @AuthenticationStatusConverter()
    @Default(AuthenticationStatus.unknown)
    AuthenticationStatus status,
    @Default(WelcomeBackMode.unknown) WelcomeBackMode welcomeBackMode,
  }) = _AuthenticationState;

  factory AuthenticationState.fromJson(Map<String, dynamic> json) =>
      _$AuthenticationStateFromJson(json);

  factory AuthenticationState.initial() => AuthenticationState();

  factory AuthenticationState.authenticated({
    required UserAttributesModel user,
  }) =>
      AuthenticationState(
        user: user,
        status: AuthenticationStatus.authenticated,
      );

  factory AuthenticationState.unauthenticated() =>
      AuthenticationState(
        status: AuthenticationStatus.unauthenticated,
      );

  factory AuthenticationState.loading() =>
      AuthenticationState(
        status: AuthenticationStatus.checking,
      );

  factory AuthenticationState.error({String? message}) =>
      AuthenticationState(
        message: message,
      );

  const AuthenticationState._();

  bool get isAuthenticated =>
      user != null &&
      status == AuthenticationStatus.authenticated;

  bool get isUnauthenticated =>
      (status == AuthenticationStatus.unauthenticated ||
          status == AuthenticationStatus.unknown) &&
      user == null;

  // bool get isUnauthenticated =>
  //     status == AuthenticationStatus.unauthenticated && user == null;

  bool get isLoading => status == AuthenticationStatus.checking;

  // The setLoading method has been replaced by the
  // loading() factory constructor.
  @override
  String toString() {
    return 'AuthenticationState(status: $status, user: $user, '
        'message: $message)';
  }
}
