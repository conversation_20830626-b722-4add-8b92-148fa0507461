part of 'verify_otp_form_bloc.dart';

@freezed
class VerifyOtpFormEvent with _$VerifyOtpFormEvent {
  const factory VerifyOtpFormEvent.started({
    String? phoneNumber,
  }) = _Started;

  /// Initialize with standardized phone number data for improved consistency
  const factory VerifyOtpFormEvent.startedWithPhoneData({
    required PhoneNumberData phoneNumberData,
  }) = _StartedWithPhoneData;

  const factory VerifyOtpFormEvent.otpChanged({required String otp}) =
      _OtpChanged;

  const factory VerifyOtpFormEvent.submited({CountryModel? country}) =
      _Submitted;

  const factory VerifyOtpFormEvent.updateCountryModel({CountryModel? country}) =
      _UpdateCountryModel;

  const factory VerifyOtpFormEvent.formErrorsResetted() = _ResetFormErrors;
}
