part of 'send_otp_form_bloc.dart';

@freezed
class SendOtpFormState with _$SendOtpFormState, FormzMixin {
  factory SendOtpFormState({
    @Default(VarCharInput.pure()) VarCharInput countryCode,
    @Default(PhoneNumberInput.pure()) PhoneNumberInput phoneNumber,
    VarCharInput? referralCode,
    String? phoneNumberErrorMessage,
    String? referralErrorMessage,
    String? countryCodeErrorMessage,
    String? deviceIdErrorMessage,
    @Default('3') String noOfTriesLeft,
    String? message,
    CountryModel? selectedCountry,
    @Default(false) bool showReferralField,
    @Default(FormzSubmissionStatus.initial) FormzSubmissionStatus status,
    /// Standardized phone number data for consistent handling and transfer
    PhoneNumberData? phoneNumberData,
  }) = _SendOtpFormState;

  factory SendOtpFormState.initial() => SendOtpFormState();

  const SendOtpFormState._();

  bool get isSubmissionSuccess {
    return status == FormzSubmissionStatus.success;
  }

  bool get isSubmissionFailure {
    return status == FormzSubmissionStatus.failure;
  }

  bool get isSubmissionInProgress {
    return status == FormzSubmissionStatus.inProgress;
  }

  bool get isSubmissionInitial {
    return status == FormzSubmissionStatus.initial;
  }

  @override
  List<FormzInput<String, dynamic>> get inputs {
    if (referralCode != null) {
      return [
        phoneNumber,
        countryCode,
        referralCode!,
      ];
    } else {
      return [
        phoneNumber,
        countryCode,
      ];
    }
  }
}
