import 'package:common/common.dart';
import 'package:constants/constants.dart';
import 'package:countries/countries.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_pin_code_fields/flutter_pin_code_fields.dart';
import 'package:formz/formz.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:froggytalk/dialer/dialer.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:froggytalk/notifications/notifications.dart';
import 'package:navigation/navigation.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:utils/utils.dart';

class VerifyOtpPage extends StatefulHookWidget {
  const VerifyOtpPage({super.key});

  static String routeName = '/authentication/step2';

  static Route<Object?> route({required String phoneNumber}) {
    return MaterialPageRoute(
      builder: (context) => BlocProvider(
        create: (context) => VerifyOtpFormBloc()
          ..add(
            VerifyOtpFormEvent.started(
              phoneNumber: phoneNumber,
              // country: context.read<SendOtpFormBloc>().state.selectedCountry,
            ),
          ),
        child: const VerifyOtpPage(),
      ),
    );
  }

  @override
  State<VerifyOtpPage> createState() => _VerifyOtpPageState();
}

class _VerifyOtpPageState extends State<VerifyOtpPage> {
  SendOtpFormBloc? sendOtpBloc;
  VerifyOtpFormBloc? verifyOtpBloc;

  CountryModel? _countryModel;

  @override
  void initState() {
    super.initState();
    sendOtpBloc = context.read<SendOtpFormBloc>();
    verifyOtpBloc = context.read<VerifyOtpFormBloc>();

    setState(() {
      _countryModel = sendOtpBloc?.state.selectedCountry;
    });

    setCountryOnLoad();
  }

  void setCountryOnLoad() {
    verifyOtpBloc?.add(
      VerifyOtpFormEvent.updateCountryModel(
        country: _countryModel,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = useLocale();
    final pinController = useValueNotifier<String>('');
    final bloc = context.read<VerifyOtpFormBloc>();
    final authbloc = context.read<AuthenticationBloc>();
    final unreadbloc = context.read<UnreadNotificationsBloc>();
    final sendOtpBloc = context.read<SendOtpFormBloc>();
    final dialerBloc = context.read<DialerBloc>();
    final recentCallsBloc = context.read<RecentCallsBloc>();
    final favBloc = context.read<FavouriteContactsBloc>();
    final contactsBloc = context.read<ContactListBloc>();
    final libphonenumber = context.read<PhoneNumberService>();
    final errorMessage = useState<String?>(null);

    final otpPhoneNumber = context.select(
      (VerifyOtpFormBloc bloc) => bloc.state.phoneNumber,
    );

    final selectedCountry = context.select(
      (SendOtpFormBloc bloc) =>
          bloc.state.selectedCountry ??
          (FroggyCountries.getInstance().findCountryByCountryCode('us') ??
           FroggyCountries.getInstance().getDefaultCountry()),
    );

    final numberOfRetriesLeft = context.select(
      (SendOtpFormBloc bloc) => bloc.state.noOfTriesLeft,
    );

    final onSubmit = useCallback(
      () {
        bloc.add(
          const VerifyOtpFormEvent.submited(),
        );
      },
      [bloc],
    );

    // ignore: unused_local_variable
    final redirectToLoadingPage = useCallback(
      () {
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute<dynamic>(
            builder: (_) => const AppLoadingPage(),
          ),
          (Route<dynamic> route) => false,
        );
      },
      [],
    );

    // ignore: unused_local_variable
    final redirectToDashboardPage = useCallback(
      () {
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute<dynamic>(
            builder: (_) => const AppDashboardPage(),
          ),
          (Route<dynamic> route) => false,
        );
      },
      [],
    );

    // ignore: unused_local_variable
    final showDialog = useCallback(
      ({String? message}) {
        AuthenticationBottomSheet.showErrorAlertDialog(
          context: context,
          message: message ?? l10n.loginPageErrorMessage,
        );
      },
      [],
    );

    final editNumber = useCallback(
      FroggyRouter.maybePop,
      [],
    );

    final resendOtp = useCallback(
      (String? phoneNumber, String? countryCode) {
        sendOtpBloc.add(
          SendOtpFormEvent.resentOtp(
            phoneNumber: phoneNumber ?? '',
            countryCode: selectedCountry.code ?? '',
          ),
        );
      },
      [],
    );

    final formattedPhoneNumber = useCallback(
      ({String? phoneNumber}) {
        return libphonenumber.format(
          phoneNumber ?? (otpPhoneNumber ?? ''),
        );
      },
      [otpPhoneNumber],
    );

    final loadAllContactsModule = useCallback(
      () async {
        final status = await Permission.contacts.isGranted;

        if (status) {
          favBloc.add(const FavouriteContactsEvent.started());
          contactsBloc.add(const ContactListEvent.started());
          recentCallsBloc.add(const RecentCallsEvent.started());
        } else {
          recentCallsBloc.add(const RecentCallsEvent.started());
        }
      },
      [],
    );

    useEffect(
      () {
        void onPinNumberChanged() {
          bloc.add(VerifyOtpFormEvent.otpChanged(otp: pinController.value));
        }

        pinController.addListener(onPinNumberChanged);

        return () => pinController.removeListener(onPinNumberChanged);
      },
      [pinController.value],
    );

    return BlocListener<ConnectivityBloc, ConnectivityState>(
      listener: (context, state) {
        if (state.status.hasNoInternetConnection) {
          FroggyToast.showErrorToast(
            context,
            l10n.internetConnectionAlertTextError,
          );
        }
      },
      child: BlocConsumer<VerifyOtpFormBloc, VerifyOtpFormState>(
        listener: (context, state) {
          final otpErrorObj = state.otp;
          if (otpErrorObj.isTooShort) {
            errorMessage.value = l10n.validationMinLengthError('OTP', 4);
          } else if (otpErrorObj.isInvalid) {
            errorMessage.value = l10n.validationFieldIsRequired('OTP');
          } else {
            errorMessage.value = null;
          }

          if (state.status.isSuccess) {
            authbloc.add(
              AuthenticationEvent.signedIn(
                token: state.token!,
                user: state.user,
              ),
            );

            dialerBloc.add(
              DialerEvent.register(
                sipUsername:
                    state.user!.formattedPhoneNumber?.replaceFirst('+', ''),
                sipPassword: state.user!.asteriskPassword,
              ),
            );

            unreadbloc.add(
              const UnreadNotificationsEvent.started(),
            );

            loadAllContactsModule();

            // go to loading page
            // redirectToLoadingPage();
            redirectToDashboardPage();
          }

          if (state.status.isFailure) {
            showDialog(message: state.message);
          }

          if (state.status.isCanceled) {
            showDialog(message: l10n.internetConnectionAlertTextError);
          }
        },
        builder: (context, state) {
          return Scaffold(
            appBar: AuthenticationAppBar(
              title: l10n.loginPageAppBarTitle,
              subtitle: l10n.enterOtpPageAppBarSubtitle,
            ),
            body: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Expanded(
                  child: ListView(
                    children: [
                      BlocSelector<SendOtpFormBloc, SendOtpFormState, String?>(
                        selector: (sendOtpStateSelector) {
                          final dialingCode =
                              sendOtpStateSelector.selectedCountry?.dialingCode;
                          final phoneNumber =
                              sendOtpStateSelector.phoneNumber.value;
                          return formattedPhoneNumber(
                            phoneNumber: '$dialingCode$phoneNumber',
                          );
                        },
                        builder: (context, phoneNumber) {
                          return AuthenticationDisplayHeader(
                            title: l10n.enterOtpPagePhoneLabel,
                            subtitleWidget: Text.rich(
                              TextSpan(
                                children: [
                                  _buildTranslatedTitle(l10n),
                                  TextSpan(
                                    text: phoneNumber,
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w700,
                                    ),
                                  ),
                                  const TextSpan(
                                    text: '. Check your WhatsApp ',
                                  ),
                                  const TextSpan(
                                    text: 'or sms for the code.',
                                  ),
                                ],
                              ),
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          );
                        },
                      ),
                      FroggySpacer.y24(),
                      FroggySpacer.y8(),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        decoration: BoxDecoration(
                          // color: FroggyColors.froggyGrey5,
                          borderRadius: BorderRadius.circular(30),
                        ),
                        child: PinCodeFields(
                          onChange: (value) => pinController.value = value,
                          onComplete: (value) {},
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(RegExp('[0-9]')),
                          ],
                          fieldHeight: 70,
                          fieldWidth: 60,
                          enabled: !state.isSubmissionInProgress,
                          fieldBackgroundColor: FroggyColors.froggyGrey5,
                          fieldBorderStyle: FieldBorderStyle.square,
                          padding: const EdgeInsets.all(20),
                          keyboardType: TextInputType.number,
                          borderRadius: BorderRadius.circular(20),
                          borderColor: Colors.transparent,
                          activeBorderColor: FroggyColors.primary,
                          activeBackgroundColor: FroggyColors.froggyCream,
                        ),
                      ),
                      Builder(
                        builder: (context) {
                          if (errorMessage.value != null) {
                            return Padding(
                              padding: const EdgeInsets.only(
                                top: 10,
                                left: 20,
                                right: 20,
                              ),
                              child: Text(
                                errorMessage.value ?? '',
                                style: const TextStyle(
                                  color: FroggyColors.error,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            );
                          }

                          return Padding(
                            padding: const EdgeInsets.only(
                              top: 10,
                              left: 20,
                              right: 20,
                            ),
                            child: Text(
                              state.otpErrorMessage ?? '',
                              style: const TextStyle(
                                color: FroggyColors.error,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          );
                        },
                      ),
                      FroggySpacer.y8(),
                      AuthenticationRetrialTimerWidget(
                        // initialDuration: const Duration(minutes: 2),
                        retriesLeft: numberOfRetriesLeft,
                        onTimerRestart: () {
                          final tries = numberOfRetriesLeft.toInt;
                          if (tries > 0) {
                            resendOtp(otpPhoneNumber, selectedCountry.code);
                          } else {
                            showDialog(
                              message: l10n.enterOtpPageResendOtpRetriesError,
                            );
                          }
                        },
                      ),
                      // AuthenticationCountdownTimer(
                      //   retriesLeft: numberOfRetriesLeft,
                      //   initialTimeInMinutes: 5,
                      //   onTimerRestarted: () =>
                      //       resendOtp(otpPhoneNumber, selectedCountry.code),
                      //   onRetrialsExhausted: () {},
                      //   onTimerValueChanged: (value) {},
                      // ),
                      AuthenticationTextButton(
                        linkText: l10n.enterOtpPageEditButton,
                        onPressed: editNumber,
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 20,
                    // horizontal: horizontal,
                  ),
                  child: AuthenticationButton(
                    text: l10n.enterOtpPageSubmitButton,
                    inProgress: state.isSubmissionInProgress,
                    onPressed: state.isValid ? onSubmit : null,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  TextSpan _buildTranslatedTitle(AppLocalizations l10n) {
    return TextSpan(
      text: '${l10n.enterOtpPagePhoneLabelDescription('')} ',
    );
  }
}
