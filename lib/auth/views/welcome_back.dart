import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:navigation/navigation.dart';

/// A beautiful welcome back page with subtle animations that welcomes
/// returning users.
///
/// This page displays a welcome image, title, subtitle, and a call-to-action
/// button that animates in sequence for a polished user experience.
class AuthenticationWelcomeBackPage extends HookWidget {
  const AuthenticationWelcomeBackPage({super.key});

  static Route<Object?> route() {
    return MaterialPageRoute<Object?>(
      builder: (_) => const AuthenticationWelcomeBackPage(),
    );
  }

  static String routeName = '/authentication_welcome_back';

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context);

    // Animation controllers
    final animationController = useAnimationController(
      duration: const Duration(milliseconds: 1500),
    );
    final user = useAuthUser();

    // Start animation when the widget is built
    useEffect(
      () {
        animationController.forward();
        return null;
      },
      [],
    );

    // Create staggered animations for each element
    final imageAnimation = CurvedAnimation(
      parent: animationController,
      curve: const Interval(0, 0.6, curve: Curves.easeOut),
    );

    final titleAnimation = CurvedAnimation(
      parent: animationController,
      curve: const Interval(0.2, 0.7, curve: Curves.easeOut),
    );

    final subtitleAnimation = CurvedAnimation(
      parent: animationController,
      curve: const Interval(0.3, 0.8, curve: Curves.easeOut),
    );

    final buttonAnimation = CurvedAnimation(
      parent: animationController,
      curve: const Interval(0.5, 1, curve: Curves.easeOut),
    );

    return Scaffold(
      appBar: AppBar(),
      persistentFooterButtons: [
// Animated CTA button
        FadeTransition(
          opacity: buttonAnimation,
          child: SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, 0.2),
              end: Offset.zero,
            ).animate(buttonAnimation),
            child: SizedBox(
              width: double.infinity,
              child: FroggyButton(
                onPressed: () {
                  // Navigate to the next screen or perform action
                  // Navigator.of(context).pushNamed('/home');
                  FroggyRouter.push(
                    const ChooseLanguageModalPage(),
                  );
                },
                label: l10n.updateYourLanguage,
              ),
            ),
          ),
        ),
      ],
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Column(
            children: [
              const Spacer(),

              // Animated image
              FadeTransition(
                opacity: imageAnimation,
                child: SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(0, 0.2),
                    end: Offset.zero,
                  ).animate(imageAnimation),
                  child: const Center(
                    child: FroggyImage(
                      height: 200,
                      width: 200,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 40),
              // Welcome back name display with subtle animation
              FadeTransition(
                opacity: titleAnimation,
                child: SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(0, 0.2),
                    end: Offset.zero,
                  ).animate(titleAnimation),
                  child: Padding(
                    padding: const EdgeInsets.only(top: 24),
                    child: Text(
                      l10n.welcomeBackText(user?.name ?? ''),
                      textAlign: TextAlign.left,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w700,
                        fontSize: 20,
                        // color: theme.colorScheme.secondary,
                      ),
                    ),
                  ),
                ),
              ),

              // // Animated title
              // FadeTransition(
              //   opacity: titleAnimation,
              //   child: SlideTransition(
              //     position: Tween<Offset>(
              //       begin: const Offset(0, 0.2),
              //       end: Offset.zero,
              //     ).animate(titleAnimation),
              //     child: Text(
              //       l10n.froggyTalkLovesYou,
              //       textAlign: TextAlign.center,
              //       style: theme.textTheme.headlineMedium?.copyWith(
              //         fontWeight: FontWeight.bold,
              //         color: theme.colorScheme.primary,
              //       ),
              //     ),
              //   ),
              // ),

              // const SizedBox(height: 16),

              // Animated subtitle
              FadeTransition(
                opacity: subtitleAnimation,
                child: SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(0, 0.2),
                    end: Offset.zero,
                  ).animate(subtitleAnimation),
                  child: Text(
                    l10n.updateLanguagePrompt,
                    textAlign: TextAlign.center,
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: theme.colorScheme.onSurface.withAlpha(179),
                    ),
                  ),
                ),
              ),

              const Spacer(flex: 2),

              // // Animated CTA button
              // FadeTransition(
              //   opacity: buttonAnimation,
              //   child: SlideTransition(
              //     position: Tween<Offset>(
              //       begin: const Offset(0, 0.2),
              //       end: Offset.zero,
              //     ).animate(buttonAnimation),
              //     child: SizedBox(
              //       width: double.infinity,
              //       child: FroggyButton(
              //         onPressed: () {
              //           // Navigate to the next screen or perform action
              //           // Navigator.of(context).pushNamed('/home');
              //           FroggyRouter.push(
              //             const ChooseLanguageModalPage(),
              //           );
              //         },
              //         label: l10n.updateYourLanguage,
              //       ),
              //     ),
              //   ),
              // ),

              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }
}

/// A placeholder widget for the frog image.
/// Replace this with an actual image from your assets.
class FroggyImage extends StatelessWidget {
  const FroggyImage({super.key, this.height, this.width});

  final double? height;
  final double? width;

  @override
  Widget build(BuildContext context) {
    // Replace with actual asset image
    return Image.asset(
      'assets/images/onboarding-img1.png',
      height: height,
      width: width,
      fit: BoxFit.contain,
    );
  }
}

/// A styled button used throughout the application.
class FroggyButton extends StatelessWidget {
  const FroggyButton({
    required this.onPressed,
    required this.label,
    super.key,
    this.isLoading = false,
  });

  final VoidCallback onPressed;
  final String label;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      child: isLoading
          ? const SizedBox(
              height: 24,
              width: 24,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
    );
  }
}
