import 'package:common/common.dart';
import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/auth/auth.dart' show AuthenticationAppBar;
import 'package:froggytalk/l10n/l10n.dart';
import 'package:froggytalk/language/language.dart';

/// A page that handles language selection functionality.
///
/// This page provides an interface for users to select their preferred
/// application language. It displays the current language and allows
/// the user to open a modal to change it.
class ChooseLanguageModalPage extends HookWidget {
  /// Creates a new instance of [ChooseLanguageModalPage].
  const ChooseLanguageModalPage({super.key});

  /// The route name for this page.
  static String routeName = '/settings/language';

  /// Returns a route definition for this page.
  static Route<String?> route() {
    return MaterialPageRoute<String?>(
      builder: (context) => BlocProvider(
        create: (context) => LanguageBloc()..add(const LanguageEvent.started()),
        child: const ChooseLanguageModalPage(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = useLocale();

    // Function to show language selection modal
    final showLanguageModal = useCallback(
      () async {
        final selectedLanguage = await ChooseLanguageModal.show(context);
        if (selectedLanguage != null) {
          // Handle language change if needed
          // For example, notify parent or update app state
        }
      },
      [],
    );

    return Scaffold(
      // appBar: AppBar(
      //   title: Text(l10n.appLanguageAppbarTitle),
      //   leading: IconButton(
      //     icon: const Icon(Icons.arrow_back),
      //     onPressed: () => Navigator.of(context).pop(),
      //   ),
      // ),
      appBar: AuthenticationAppBar(
        title: l10n.loginPageAppBarTitle,
        subtitle: l10n.languageSetupPageAppBarSubtitle,
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    BlocBuilder<LanguageBloc, LanguageState>(
                      builder: (context, state) {
                        return _buildCurrentLanguage(
                          context,
                          l10n,
                          state.selectedLanguage,
                          showLanguageModal,
                        );
                      },
                    ),
                    FroggySpacer.y16(),
                    _buildLanguageNote(l10n),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the card displaying the current language.
  Widget _buildCurrentLanguage(
    BuildContext context,
    AppLocalizations l10n,
    Language? selectedLanguage,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 0,
      color: FroggyColors.froggyGrey5,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // If we have a flag asset, display it here
              // if (selectedLanguage?.flagAsset != null)
              //   CircleAvatar(
              //     radius: 12,
              //     backgroundImage: AssetImage(selectedLanguage!.flagAsset),
              //   ),
              FroggySpacer.x16(),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      l10n.morePageLanguageMenuText,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    FroggySpacer.y4(),
                    Text(
                      selectedLanguage?.name ?? 'English',
                      style: const TextStyle(
                        fontSize: 14,
                        color: FroggyColors.froggyGrey1,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: FroggyColors.froggyGrey2,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the informational note about language settings.
  Widget _buildLanguageNote(AppLocalizations l10n) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: FroggyColors.froggyCream.withAlpha((255.0 * 0.3).round()),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: FroggyColors.froggyCream),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.info_outline,
            color: FroggyColors.primary,
          ),
          FroggySpacer.x16(),
          const Expanded(
            child: Text(
              "Changing the language will update the app's text and content "
              'to match your preference.',
              style: TextStyle(
                fontSize: 14,
                color: FroggyColors.froggyGrey1,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
