import 'package:countries/countries.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:formz/formz.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:navigation/navigation.dart';

class SendOtpPage extends StatefulHookWidget {
  const SendOtpPage({super.key});

  static Route<Object?> route() {
    return MaterialPageRoute(
      builder: (context) => const SendOtpPage(),
    );
  }

  static String routeName = '/authentication';

  @override
  State<SendOtpPage> createState() => _SendOtpPageState();
}

class _SendOtpPageState extends State<SendOtpPage> {
  SendOtpFormBloc? bloc;

  @override
  void initState() {
    super.initState();
    bloc = context.read<SendOtpFormBloc>();
    // Country initialization is now handled by the BLoC automatically
  }

  @override
  Widget build(BuildContext context) {
    final l10n = useLocale();
    
    // Initialize controllers
    final phoneNumberController = useState<String>('');
    final countryController = useState<CountryModel?>(null);
    final referralFieldController = useState<String>('');
    final toggleReferralField = useState(false);

    // Watch BLoC state for country availability
    final currentBlocState = context.watch<SendOtpFormBloc>().state;
    
    // Check if country is still loading
    final isCountryLoading = currentBlocState.selectedCountry == null;

    const horizontal = 20.0;
    const vertical = 5.0;

    final setPhoneNumberOnInit = useCallback(
      () {
        final phoneNumber = context.select(
          (SendOtpFormBloc bloc) => bloc.state.phoneNumber.value,
        );
        if (phoneNumber.isNotEmpty) {
          phoneNumberController.value = phoneNumber;
        }
      },
      [],
    );

    final onSubmit = useCallback(
      () {
        bloc?.add(const SendOtpFormEvent.submited());
      },
      [],
    );

    final redirectToOtpVerificationPage = useCallback(
      () {
        final currentState = bloc?.state;
        final phoneData = currentState?.phoneNumberData;
        FroggyRouter.push(
          BlocProvider(
            create: (context) {
              final verifyBloc = VerifyOtpFormBloc();
              if (phoneData != null && phoneData.isValid) {
                verifyBloc.add(
                  VerifyOtpFormEvent.startedWithPhoneData(
                    phoneNumberData: phoneData,
                  ),
                );
              } else {
                final phoneNumberForVerification =
                    (currentState?.selectedCountry?.dialingCode ?? '') +
                        phoneNumberController.value;
                verifyBloc
                  ..add(
                    VerifyOtpFormEvent.started(
                      phoneNumber: phoneNumberForVerification,
                    ),
                  )
                  ..add(
                    VerifyOtpFormEvent.updateCountryModel(
                      country: currentState?.selectedCountry,
                    ),
                  );
              }
              return verifyBloc;
            },
            child: const VerifyOtpPage(),
          ),
        );
      },
      [],
    );

    final showErrorDialog = useCallback(
      ({String? message}) {
        AuthenticationBottomSheet.showErrorAlertDialog(
          context: context,
          message: message ?? l10n.loginPageErrorMessage,
        );
      },
      [],
    );

    useEffect(
      () {
        setPhoneNumberOnInit();

        void updatedPhoneNumber() {
          bloc?.add(
            SendOtpFormEvent.phoneNumberChanged(
              phoneNumber: phoneNumberController.value,
            ),
          );
        }

        void updatedReferralField() {
          bloc?.add(
            SendOtpFormEvent.referralCodeChanged(
              referralCode: referralFieldController.value,
            ),
          );
        }

        void updatedReferralCheckbox() {
          if (toggleReferralField.value) {
            bloc?.add(
              SendOtpFormEvent.referralCodeChanged(
                referralCode: referralFieldController.value,
              ),
            );
          } else {
            bloc?.add(
              const SendOtpFormEvent.referralCodeChanged(),
            );
          }
        }

        phoneNumberController.addListener(updatedPhoneNumber);
        referralFieldController.addListener(updatedReferralField);
        toggleReferralField.addListener(updatedReferralCheckbox);

        final removeBlocListener = bloc?.stream.listen((state) {
          // Always sync countryController with bloc state changes
          if (state.selectedCountry != countryController.value) {
            countryController.value = state.selectedCountry;
          }
        });

        return () {
          phoneNumberController.removeListener(updatedPhoneNumber);
          referralFieldController.removeListener(updatedReferralField);
          toggleReferralField.removeListener(updatedReferralCheckbox);
          removeBlocListener?.cancel();
        };
      },
      [
        referralFieldController.value,
        toggleReferralField.value,
      ],
    );

    return BlocListener<ConnectivityBloc, ConnectivityState>(
      listener: (context, state) {
        if (state.status.hasNoInternetConnection) {
          FroggyToast.showErrorToast(
            context,
            l10n.internetConnectionAlertTextError,
          );
        }
      },
      child: BlocConsumer<SendOtpFormBloc, SendOtpFormState>(
        listener: (context, state) {
          if (state.status.isSuccess) {
            bloc?.add(const SendOtpFormEvent.formErrorsResetted());
            redirectToOtpVerificationPage();
          }
          if (state.status.isFailure) {
            showErrorDialog(message: state.message);
          }
          if (state.status.isCanceled) {
            showErrorDialog(message: l10n.internetConnectionAlertTextError);
          }
        },
        builder: (context, state) {
          return Scaffold(
            appBar: AuthenticationAppBar(
              title: l10n.loginPageAppBarTitle,
              subtitle: l10n.loginPageAppBarSubtitle,
            ),
            body: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Expanded(
                  child: ListView(
                    children: [
                      AnimatedOpacity(
                        opacity: isCountryLoading ? 0.7 : 1.0,
                        duration: const Duration(milliseconds: 300),
                        child: AuthenticationDisplayHeader(
                          title: l10n.loginPagePhoneLabel,
                          subtitle: isCountryLoading
                              ? 'Detecting your location...'
                              : l10n.loginPagePhoneLabelDescription,
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          vertical: vertical,
                          horizontal: horizontal,
                        ),
                        child: _LoadingShimmer(
                          isLoading: isCountryLoading,
                          child: AnimatedOpacity(
                            opacity: isCountryLoading ? 0.6 : 1.0,
                            duration: const Duration(milliseconds: 300),
                            child: AnimatedScale(
                              scale: isCountryLoading ? 0.98 : 1.0,
                              duration: const Duration(milliseconds: 300),
                              child: AuthPhoneNumberField(
                                enabled: !state.isSubmissionInProgress && 
                                         !isCountryLoading,
                                phoneNumberErrorMessage: 
                                    state.phoneNumberErrorMessage,
                                countryErrorMessage: 
                                    state.countryCodeErrorMessage,
                                defaultCountryCode: state.selectedCountry,
                                onCountryValueChanged: (value) {
                                  // Only update BLoC - local controller 
                                  // will be synced via useEffect
                                  bloc?.add(
                                    SendOtpFormEvent.countryCodeChanged(
                                      countryCode: value,
                                    ),
                                  );
                                },
                                onPhoneNumberChanged: (String value) =>
                                    phoneNumberController.value = value,
                              ),
                            ),
                          ),
                        ),
                      ),
                      if (state.showReferralField)
                        Padding(
                          padding: const EdgeInsets.symmetric(
                            vertical: vertical,
                            horizontal: horizontal - 5,
                          ),
                          child: AuthenticationReferralCheckboxWidget(
                            value: toggleReferralField.value,
                            onReferralCheckboxChanged: (checked) {
                              toggleReferralField.value =
                                  !toggleReferralField.value;
                            },
                          ),
                        ),
                      Builder(
                        builder: (context) {
                          if (!toggleReferralField.value) {
                            return const SizedBox.shrink();
                          }
                          if (!state.showReferralField) {
                            return const SizedBox.shrink();
                          }
                          return Padding(
                            padding: const EdgeInsets.symmetric(
                              vertical: vertical,
                              horizontal: horizontal,
                            ),
                            child: AuthenticationReferralFormField(
                              onReferralCodeChanged: (String value) =>
                                  referralFieldController.value = value,
                              enabled: !state.isSubmissionInProgress,
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 20,
                  ),
                  child: AuthenticationButton(
                    text: l10n.loginPageSubmitButton,
                    inProgress: state.isSubmissionInProgress || 
                                isCountryLoading,
                    onPressed: (state.isValid && !isCountryLoading) 
                        ? onSubmit.call 
                        : null,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

class _LoadingShimmer extends HookWidget {
  const _LoadingShimmer({
    required this.child,
    this.isLoading = false,
  });

  final Widget child;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    final animationController = useAnimationController(
      duration: const Duration(milliseconds: 1500),
    );

    useEffect(
      () {
        if (isLoading) {
          animationController.repeat();
        } else {
          animationController.stop();
        }
        return null;
      },
      [isLoading],
    );

    if (!isLoading) {
      return child;
    }

    return AnimatedBuilder(
      animation: animationController,
      builder: (context, _) {
        return Stack(
          children: [
            child,
            Positioned.fill(
              child: DecoratedBox(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(25),
                  gradient: LinearGradient(
                    begin: Alignment(-1 - animationController.value, 0),
                    end: Alignment(1 - animationController.value, 0),
                    colors: [
                      Colors.transparent,
                      Colors.white.withValues(alpha: 0.3),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
