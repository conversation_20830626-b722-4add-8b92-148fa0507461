import 'package:data/data.dart';
import 'package:either_dart/either.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:http/http.dart';
import 'package:utils/utils.dart';

class SendOtpRepository {
  Future<Either<FormValidatedErrorResponse, PaginatedResponse<SendOtpResponse>>>
      execute({
    required String phoneNumber,
    String countryCode = 'nl',
    String? deviceIdentifier,
    String? referralCode,
  }) async {
    try {
      final iDeviceIdentifier = await getDeviceIdentifier();

      final body = SendOtpRequest(
        phoneNumber: phoneNumber,
        deviceIdentifier: deviceIdentifier ?? iDeviceIdentifier,
        countryCode: countryCode.toLowerCase(),
        referralCode: referralCode ?? '',
      );

      FroggyLogger.info('SendOTP Body Request: ${body.toJson()}');

      final request = ApiRequest(
        route: '/v1/mobile/auth/send-otp-verification',
        requestType: RequestType.post,
        params: PaginatedRequest<SendOtpRequest>(body: body)
            .toJson((p0) => p0.toJson()),
      );

      final response =
          await ApiClient.getInstance().executeRequest(request: request);

          FroggyLogger.info('SendOTP Body Response: ${response.fromJson}');

      if (response.isSuccess) {
        final fromJson = SendOtpResponse.fromJson(response.data);
        final paginatedResponse = PaginatedResponse<SendOtpResponse>(
          status: response.statusToString,
          message: response.successOrErrorMessage,
          data: fromJson,
        );

        FroggyLogger.info('Response: $paginatedResponse');

        return Right(paginatedResponse);
      } else if (response.hasFormErrors) {
        final formErrors =
            FormValidatedErrorResponse.fromJson(response.fromJson);

        final formattedErrors = formErrors.copyWith(
          formValidationErrors: {
            ...formErrors.setErrorAttribute(key: 'telephone'),
            ...formErrors.setErrorAttribute(key: 'device_id'),
            ...formErrors.setErrorAttribute(key: 'country_code'),
            ...formErrors.setErrorAttribute(key: 'referral_code'),
          },
        );

        FroggyLogger.warning('Form errors: $formattedErrors');

        return Left(formattedErrors);
      } else {
        FroggyLogger.error('Error Occurred: ${response.successOrErrorMessage}');

        return Left(
          FormValidatedErrorResponse(
            message: response.successOrErrorMessage ?? 'Unknown error',
          ),
        );
      }
    } on ClientException catch (_) {
      return const Left(
        FormValidatedErrorResponse(
          code: 'internet-out',
          message: 'No internet connection',
        ),
      );
    } catch (e) {
      FroggyLogger.error('Failure Occurred: $e');

      return Left(
        FormValidatedErrorResponse(
          code: 'error-occurred',
          message: e.toString(),
        ),
      );
    }
  }
}
