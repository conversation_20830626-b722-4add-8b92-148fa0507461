import 'package:data/data.dart';
import 'package:either_dart/either.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:http/http.dart';
import 'package:utils/utils.dart';

class VerifyOtpRepository {
  Future<
      Either<FormValidatedErrorResponse,
          PaginatedResponse<VerifyOtpResponse>>> execute({
    required String phoneNumber,
    required String otp,
    String? countryCode,
  }) async {
    try {
      final body = VerifyOtpRequest(
        phoneNumber: phoneNumber,
        otp: otp,
        countryCode: countryCode?.toLowerCase(),
      );

      FroggyLogger.info('VerifyOTP Body Request: ${body.toJson()}');

      final request = ApiRequest(
        route: '/v1/mobile/auth/verify-otp',
        requestType: RequestType.post,
        params: PaginatedRequest<VerifyOtpRequest>(body: body).toJson(
          (json) => body.toJson(),
        ),
      );

      final response =
          await ApiClient.getInstance().executeRequest(request: request);

      if (response.isSuccess) {
        final fromJson = VerifyOtpResponse.fromJson(response.data);
        final paginatedResponse = PaginatedResponse<VerifyOtpResponse>(
          status: response.statusToString,
          message: response.successOrErrorMessage,
          data: fromJson,
        );

        FroggyLogger.info('Response: $paginatedResponse');

        return Right(paginatedResponse);
      } else if (response.hasFormErrors) {
        final formErrors = FormValidatedErrorResponse.fromJson(response.data);

        final formattedErrors = {
          ...formErrors.setErrorAttribute(key: 'telephone'),
          ...formErrors.setErrorAttribute(key: 'otp'),
          ...formErrors.setErrorAttribute(key: 'country_code'),
        };

        FroggyLogger.warning('Form errors: $formattedErrors');

        return Left(
          formErrors.copyWith(
            formValidationErrors: formattedErrors,
          ),
        );
      } else {
        FroggyLogger.error('Error Occurred: ${response.body}');
        FroggyLogger.error('Error Occurred: ${response.successOrErrorMessage}');

        return Left(
          FormValidatedErrorResponse(
            message: response.successOrErrorMessage ?? 'Unknown error',
          ),
        );
      }
    } on ClientException catch (_) {
      return const Left(
        FormValidatedErrorResponse(
          code: 'internet-out',
        ),
      );
    } catch (e) {
      FroggyLogger.error('Failure Occurred: $e');

      return Left(FormValidatedErrorResponse(message: e.toString()));
    }
  }
}
