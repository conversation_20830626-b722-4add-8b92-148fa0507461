import 'package:data/data.dart';
import 'package:either_dart/either.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:http/http.dart';
import 'package:utils/utils.dart';

class CheckExistingUserRepository {
  Future<
          Either<FormValidatedErrorResponse,
              PaginatedResponse<GenericResponse<CheckExistingUserResponse>>>>
      execute({
    required String phoneNumber,
  }) async {
    try {
      final body = CheckExistingUserRequest(
        phoneNumber: phoneNumber,
      );

      final request = ApiRequest(
        route: '/v1/mobile/auth/check-user',
        requestType: RequestType.post,
        params: PaginatedRequest<CheckExistingUserRequest>(body: body)
            .toJson((p0) => p0.toJson()),
      );

      final response =
          await ApiClient.getInstance().executeRequest(request: request);

      if (response.isSuccess) {
        final data = response.data;
        final paginatedResponse =
            PaginatedResponse<GenericResponse<CheckExistingUserResponse>>(
          status: response.statusToString,
          message: response.successOrErrorMessage,
          data: GenericResponse<CheckExistingUserResponse>.fromJson(
            data,
            (p01) {
              final attributes = p01! as Map<String, dynamic>;
              final isExistingUser = attributes['exists'] as String;
              return CheckExistingUserResponse(isExistingUser: isExistingUser);
            },
          ),
        );

        FroggyLogger.info('Response: $paginatedResponse');

        return Right(paginatedResponse);
      } else if (response.hasFormErrors) {
        final formErrors =
            FormValidatedErrorResponse.fromJson(response.fromJson);

        final formattedErrors = formErrors.copyWith(
          formValidationErrors: {
            ...formErrors.setErrorAttribute(key: 'telephone'),
            ...formErrors.setErrorAttribute(key: 'device_id'),
            ...formErrors.setErrorAttribute(key: 'country_code'),
            ...formErrors.setErrorAttribute(key: 'referral_code'),
          },
        );

        FroggyLogger.warning('Form errors: $formattedErrors');

        return Left(formattedErrors);
      } else {
        FroggyLogger.error('Error Occurred: ${response.successOrErrorMessage}');

        return Left(
          FormValidatedErrorResponse(
            message: response.successOrErrorMessage ?? 'Unknown error',
          ),
        );
      }
    } on ClientException catch (_) {
      return const Left(
        FormValidatedErrorResponse(
          message: 'internet-out',
        ),
      );
    } catch (e) {
      FroggyLogger.error('Failure Occurred: $e');

      return Left(FormValidatedErrorResponse(message: e.toString()));
    }
  }
}
