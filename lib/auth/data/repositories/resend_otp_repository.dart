import 'package:data/data.dart';
import 'package:either_dart/either.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:utils/utils.dart';

class ResendOtpRepository {
  Future<
      Either<FormValidatedErrorResponse,
          PaginatedResponse<ResendOtpResponse>>> execute({
    required String phoneNumber,
    String countryCode = 'nl',
  }) async {
    try {
      final body = ResendOtpRequest(
        phoneNumber: phoneNumber,
        countryCode: countryCode.toLowerCase(),
      );

      final request = ApiRequest(
        route: '/v1/mobile/auth/send-otp-verification',
        requestType: RequestType.post,
        params: PaginatedRequest<ResendOtpRequest>(body: body).toJson(
          (json) => body.toJson(),
        ),
      );

      final response =
          await ApiClient.getInstance().executeRequest(request: request);

      if (response.isSuccess) {
        final fromJson = ResendOtpResponse.fromJson(response.data);
        final paginatedResponse = PaginatedResponse<ResendOtpResponse>(
          status: response.statusToString,
          message: response.successOrErrorMessage,
          data: fromJson,
        );

        FroggyLogger.info('Response: InJSON $paginatedResponse');

        return Right(paginatedResponse);
      } else if (response.hasFormErrors) {
        final formErrors = FormValidatedErrorResponse.fromJson(response.data);

        final formattedErrors = {
          ...formErrors.setErrorAttribute(key: 'telephone'),
          ...formErrors.setErrorAttribute(key: 'country_code'),
        };

        FroggyLogger.warning('Form errors: $formattedErrors');

        return Left(
          formErrors.copyWith(
            formValidationErrors: formattedErrors,
          ),
        );
      } else {
        FroggyLogger.error('Error Occurred: ${response.successOrErrorMessage}');

        return Left(
          FormValidatedErrorResponse(
            message: response.successOrErrorMessage ?? 'Unknown error',
          ),
        );
      }
    } catch (e) {
      FroggyLogger.error('Failure Occurred: $e');

      return Left(FormValidatedErrorResponse(message: e.toString()));
    }
  }
}
