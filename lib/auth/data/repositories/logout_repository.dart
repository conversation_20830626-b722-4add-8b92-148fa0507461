import 'package:data/data.dart';
import 'package:either_dart/either.dart';
import 'package:utils/utils.dart';

class LogoutRepository {
  Future<Either<GenericErrorResponse, PaginatedResponse<GenericResponse<void>>>>
      execute() async {
    try {
      final request = ApiRequest(
        route: '/v1/mobile/logout',
        requestType: RequestType.post,
        params: {},
      );

      final response =
          await ApiClient.getInstance().executeRequest(request: request);

      if (response.isSuccess) {
        // final fromJson = SendOtpResponse.fromJson(response.data);
        final paginatedResponse = PaginatedResponse<GenericResponse<void>>(
          status: response.statusToString,
          message: response.successOrErrorMessage,
          // data: fromJson,
        );

        FroggyLogger.info('Response: $paginatedResponse');

        return Right(paginatedResponse);
      } else {
        FroggyLogger.error('Error Occurred: ${response.successOrErrorMessage}');

        return Left(
          GenericErrorResponse(
            message: response.successOrErrorMessage ?? 'Unknown error',
          ),
        );
      }
    } catch (e) {
      FroggyLogger.error('Failure Occurred: $e');

      return Left(GenericErrorResponse(message: e.toString()));
    }
  }
}
