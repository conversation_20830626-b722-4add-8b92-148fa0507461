import 'package:froggytalk/auth/auth.dart';
import 'package:froggytalk/auth/data/models/welcome_back_mode.dart';
import 'package:utils/utils.dart';

/// A service that manages the welcome back mode for the Froggy application.
///
/// It provides methods to check, set, and get the welcome back mode.
class WelcomeBackModeManager {
  /// The key name used to store the welcome back mode.
  static const _keyName = '__froggy_welcome_back_mode';

  /// The storage mechanism used to store and retrieve the mode.
  final FroggyLocalStorage _storage = FroggyLocalStorage.getInstance();

  /// Sets the welcome back mode.
  ///
  /// Takes a [WelcomeBackMode] as a parameter
  /// and saves it using the [FroggyLocalStorage].
  void set(WelcomeBackMode mode) {
    _storage.set<String>(_keyName, mode.value);
  }

  /// Retrieves the welcome back mode.
  ///
  /// Returns the current [WelcomeBackMode].
  /// Defaults to [WelcomeBackMode.proceedToHome]
  /// if no mode is stored.
  WelcomeBackMode get() {
    final value = _storage.get<String>(_keyName);
    switch (value) {
      case 'biometrics':
      case 'needsBiometrics':
        return WelcomeBackMode.needsBiometrics;
      case 'pincode':
      case 'needsPincode':
        return WelcomeBackMode.needsPincode;
      case 'password':
      case 'needsPassword':
        return WelcomeBackMode.needsPassword;
      case 'unknown':
        return WelcomeBackMode.unknown;
      default:
        return WelcomeBackMode.proceedToHome;
    }
  }

  /// Checks if a welcome back mode is set.
  ///
  /// Returns `true` if a mode is stored, `false` otherwise.
  bool has() {
    return _storage.has(_keyName);
  }

  /// Removes the stored welcome back mode.
  void clear() {
    _storage.remove(_keyName);
  }
}
