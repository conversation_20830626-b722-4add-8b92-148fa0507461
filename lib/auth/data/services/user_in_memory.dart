// ignore_for_file: unnecessary_getters_setters

import 'package:utils/utils.dart';

/// A singleton class that represents a user stored in memory.
///
/// This class can only be instantiated once. If an attempt is made to
/// instantiate it more than once, an exception will be thrown.
///
/// Example usage:
/// ```dart
/// final user = UserInMemory.initialize(
///   id: '123',
///   firstName: 'John',
///   lastName: 'Doe',
///   email: '<EMAIL>',
///   phoneNumber: '************',
///   avatar: 'avatar.png',
/// );
/// ```
class FroggyUserInMemory {
  /// Factory method to return the singleton instance.
  ///
  /// If the instance has already been initialized, this method will throw
  /// an exception.
  ///
  /// Example:
  /// ```dart
  /// final user = UserInMemory.initialize(
  ///   id: '123',
  ///   firstName: '<PERSON>',
  ///   lastName: 'Doe',
  ///   email: '<EMAIL>',
  ///   phoneNumber: '************',
  ///   avatar: 'avatar.png',
  /// );
  /// ```
  factory FroggyUserInMemory.ensureInitialized({
    String? id,
    String? firstName,
    String? lastName,
    String? email,
    String? phoneNumber,
    String? avatar,
  }) {
    if (_instance != null) {
      throw Exception('UserInMemory has already been initialized.');
    }

    _instance = FroggyUserInMemory._internal(
      id: id,
      firstName: firstName,
      lastName: lastName,
      email: email,
      phoneNumber: phoneNumber,
      avatarUrl: avatar,
    );

    FroggyLogger.debug('UserInMemory initialized successfully');

    return _instance!;
  }

  // Private constructor
  FroggyUserInMemory._internal({
    String? id,
    String? firstName,
    String? lastName,
    String? email,
    String? phoneNumber,
    String? avatarUrl,
  })  : _id = id,
        _firstName = firstName,
        _lastName = lastName,
        _email = email,
        _phoneNumber = phoneNumber,
        _avatarUrl = avatarUrl;

  // Singleton instance
  static FroggyUserInMemory? _instance;

  // Fields
  String? _id;
  String? _firstName;
  String? _lastName;
  String? _email;
  String? _phoneNumber;
  String? _avatarUrl;

  /// Getter for the user ID.
  String? get id => _id;

  /// Setter for the user ID.
  set id(String? id) => _id = id;

  /// Getter for the user's first name.
  String? get firstName => _firstName;

  /// Setter for the user's first name.
  set firstName(String? firstName) => _firstName = firstName;

  /// Getter for the user's last name.
  String? get lastName => _lastName;

  /// Setter for the user's last name.
  set lastName(String? lastName) => _lastName = lastName;

  /// Getter for the user's email.
  String? get email => _email;

  /// Setter for the user's email.
  set email(String? email) => _email = email;

  /// Getter for the user's phone number.
  String? get phoneNumber => _phoneNumber;

  /// Setter for the user's phone number.
  set phoneNumber(String? phoneNumber) => _phoneNumber = phoneNumber;

  /// Getter for the user's avatar URL.
  String? get avatarUrl => _avatarUrl;

  /// Setter for the user's avatar URL.
  set avatarUrl(String? avatarUrl) => _avatarUrl = avatarUrl;

  /// Creates a copy of this user with the given fields replaced.
  ///
  /// Example:
  /// ```dart
  /// final updatedUser = user.copyWith(
  ///   firstName: 'Jane',
  ///   email: '<EMAIL>',
  /// );
  /// ```
  FroggyUserInMemory copyWith({
    String? id,
    String? firstName,
    String? lastName,
    String? email,
    String? phoneNumber,
    String? avatarUrl,
  }) {
    return FroggyUserInMemory._internal(
      id: id ?? _id,
      firstName: firstName ?? _firstName,
      lastName: lastName ?? _lastName,
      email: email ?? _email,
      phoneNumber: phoneNumber ?? _phoneNumber,
      avatarUrl: avatarUrl ?? _avatarUrl,
    );
  }
}
