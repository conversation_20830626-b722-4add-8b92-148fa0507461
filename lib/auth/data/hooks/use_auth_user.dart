import 'package:countries/countries.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:froggytalk/profile/profile.dart';

/// A custom hook that provides the authenticated user's attributes.
///
/// This hook uses the [AuthenticationBloc] to listen for changes in the
/// authentication state and returns the current user's attributes.
///
/// Returns:
///   - [UserAttributesModel?]: The current authenticated user's attributes,
///     or `null` if no user is authenticated.
///
/// Example usage:
/// ```dart
/// final user = useAuthUser();
/// if (user != null) {
///   print('User is authenticated: ${user.name}');
/// } else {
///   print('No user is authenticated.');
/// }
/// ```
UserAttributesModel? useAuthUser() {
  // Get the current BuildContext
  final context = useContext();

  // Access the AuthenticationBloc from the context
  final authenticationBloc = BlocProvider.of<AuthenticationBloc>(context);

  // Use the useStream hook to listen to the AuthenticationBloc's stream
  // and get the current user from the authentication state
  final user = useStream(
    authenticationBloc.stream.map((state) => state.user),
    initialData: authenticationBloc.state.user,
  );

  // Return the current authenticated user's attributes
  return user.data;
}

String? useAuthUserCurrencyCode() {
  final user = useAuthUser();
  return user?.country?.attributes?.currency?.code;
}

String? useAuthUserDialingCode() {
  final user = useAuthUser();
  return user?.country?.attributes?.dialingCode;
}

CountryModel? useAuthUserCountry() {
  final user = useAuthUser();
  return user?.country?.attributes;
}

String? useAuthUserCountryCode() {
  final user = useAuthUser();
  return user?.country?.attributes?.code;
}
