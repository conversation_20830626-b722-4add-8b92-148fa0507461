import 'package:countries/countries.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:froggytalk/contacts/data/services/phone_number_utils.dart';

part 'phone_number_data.freezed.dart';

/// Standardized phone number data structure for consistent handling
/// across authentication flow components.
/// 
/// This replaces the inconsistent data types between SendOtpFormBloc
/// (PhoneNumberInput) and VerifyOtpFormBloc (String?) to ensure
/// proper state synchronization and data transfer.
@freezed
class PhoneNumberData with _$PhoneNumberData {
  const factory PhoneNumberData({
    /// The raw phone number input from the user
    @Default('') String rawInput,
    
    /// The national number (without country prefix)
    @Default('') String nationalNumber,
    
    /// ISO 3166-1 alpha-2 country code (e.g., 'nl', 'us')
    @Default('') String countryCode,
    
    /// Country model with additional metadata
    CountryModel? country,
    
    /// Formatted phone number for display purposes
    String? formattedNumber,
    
    /// Whether the phone number is valid according to libphonenumber
    @Default(false) bool isValid,
    
    /// Error message if validation fails
    String? errorMessage,
    
    /// Detailed parsing result for advanced error handling
    PhoneNumberParseResult? parseResult,
  }) = _PhoneNumberData;

  /// Create phone number data from raw input using PhoneNumberUtils
  /// 
  /// This factory method centralizes phone number processing and ensures
  /// consistent validation across all authentication components.
  /// 
  /// Example:
  /// ```dart
  /// final phoneData = PhoneNumberData.fromInput(
  ///   rawInput: '0612345678',
  ///   countryCode: 'nl',
  /// );
  /// ```
  factory PhoneNumberData.fromInput({
    required String rawInput,
    required String countryCode,
  }) {
    if (rawInput.trim().isEmpty || countryCode.trim().isEmpty) {
      return PhoneNumberData(
        rawInput: rawInput,
        countryCode: countryCode,
        errorMessage: rawInput.trim().isEmpty 
          ? 'Phone number cannot be empty'
          : 'Country code cannot be empty',
      );
    }

    final phoneUtils = PhoneNumberUtils();
    final parseResult = phoneUtils.parsePhoneNumber(rawInput, countryCode);
    
    return parseResult.when(
      success: (
        nationalNumber, 
        countryCode, 
        formattedNumber, 
        isValid, 
        country,
      ) {
        return PhoneNumberData(
          rawInput: rawInput,
          nationalNumber: nationalNumber,
          countryCode: countryCode,
          country: country,
          formattedNumber: formattedNumber,
          isValid: isValid,
          parseResult: parseResult,
        );
      },
      error: (
        originalInput, 
        errorMessage, 
        errorType, 
        fallbackValue,
      ) {
        return PhoneNumberData(
          rawInput: rawInput,
          nationalNumber: fallbackValue ?? rawInput,
          countryCode: countryCode,
          errorMessage: errorMessage,
          parseResult: parseResult,
        );
      },
    );
  }

  /// Create empty phone number data
  factory PhoneNumberData.empty() => const PhoneNumberData();

  const PhoneNumberData._();

  /// Convert to VerifyOtpFormState initial data
  Map<String, dynamic> toVerifyOtpInitialData() {
    return {
      'phoneNumber': nationalNumber,
      'country': country,
    };
  }

  /// Get the national number for API calls
  /// 
  /// Returns the extracted national number or falls back to raw input
  /// if parsing failed. This ensures API calls always have some value.
  String get apiValue => nationalNumber.isNotEmpty ? nationalNumber : rawInput;

  /// Get the display value for UI components
  /// 
  /// Returns the formatted number if available, otherwise the raw input.
  String get displayValue => formattedNumber ?? rawInput;

  /// Check if the phone number has any content
  bool get isEmpty => rawInput.trim().isEmpty;

  /// Check if the phone number is not empty
  bool get isNotEmpty => !isEmpty;

  /// Check if there are validation errors
  bool get hasError => errorMessage != null;

  /// Check if the phone number is valid and ready for submission
  bool get isReadyForSubmission => isNotEmpty && isValid && !hasError;

  /// Update the phone number with new input while preserving country
  PhoneNumberData updateInput(String newInput) {
    return PhoneNumberData.fromInput(
      rawInput: newInput,
      countryCode: countryCode,
    );
  }

  /// Update the country code and revalidate the phone number
  PhoneNumberData updateCountry(
    String newCountryCode, [
    CountryModel? countryModel,
  ]) {
    final updatedData = PhoneNumberData.fromInput(
      rawInput: rawInput,
      countryCode: newCountryCode,
    );
    
    // Preserve the country model if provided
    if (countryModel != null) {
      return updatedData.copyWith(country: countryModel);
    }
    
    return updatedData;
  }

  /// Create a copy with error message cleared
  PhoneNumberData clearError() {
    return copyWith(errorMessage: null);
  }

  /// Validate the current phone number data
  /// 
  /// Returns updated data with validation results.
  /// Useful for revalidating without changing the input.
  PhoneNumberData validate() {
    return PhoneNumberData.fromInput(
      rawInput: rawInput,
      countryCode: countryCode,
    );
  }
}

/// Transfer data class for passing phone number information between BLoCs
/// 
/// This ensures clean data transfer from SendOtp to VerifyOtp components
/// without coupling the BLoCs directly to each other's state structures.
@freezed
class PhoneNumberTransferData with _$PhoneNumberTransferData {
  const factory PhoneNumberTransferData({
    required String phoneNumber,
    required String nationalNumber,
    required String countryCode,
    required CountryModel country,
    String? formattedNumber,
    @Default(true) bool isValid,
  }) = _PhoneNumberTransferData;

  /// Create transfer data from PhoneNumberData
  factory PhoneNumberTransferData.fromPhoneData(
    PhoneNumberData phoneData,
  ) {
    return PhoneNumberTransferData(
      phoneNumber: phoneData.rawInput,
      nationalNumber: phoneData.nationalNumber,
      countryCode: phoneData.countryCode,
      country: phoneData.country ?? 
        FroggyCountries.getInstance().getDefaultCountry(),
      formattedNumber: phoneData.formattedNumber,
      isValid: phoneData.isValid,
    );
  }
}
