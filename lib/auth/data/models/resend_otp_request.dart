import 'package:freezed_annotation/freezed_annotation.dart';

part 'resend_otp_request.freezed.dart';
part 'resend_otp_request.g.dart';

@freezed
class ResendOtpRequest with _$ResendOtpRequest {
  factory ResendOtpRequest({
    @JsonKey(name: 'telephone') required String phoneNumber,
    @Default('nl') @JsonKey(name: 'country_code') String countryCode,
    @Default('telephone-verification-token') String type,
  }) = _ResendOtpRequest;

  factory ResendOtpRequest.fromJson(Map<String, Object?> json) =>
      _$ResendOtpRequestFromJson(json);
}
