import 'package:freezed_annotation/freezed_annotation.dart';

part 'resend_otp_response.freezed.dart';
part 'resend_otp_response.g.dart';

@freezed
class ResendOtpResponse with _$ResendOtpResponse {
  factory ResendOtpResponse({
    String? token,
    @JsonKey(name: 'expires_at') String? expiresAt,
    @Default('2') @<PERSON>sonKey(name: 'no_of_tries_left') String noOfTriesLeft,
  }) = _ResendOtpResponse;

  factory ResendOtpResponse.fromJson(Map<String, Object?> json) =>
      _$ResendOtpResponseFromJson(json);
}
