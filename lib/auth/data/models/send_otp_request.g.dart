// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'send_otp_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SendOtpRequestImpl _$$SendOtpRequestImplFromJson(Map<String, dynamic> json) =>
    _$SendOtpRequestImpl(
      phoneNumber: json['telephone'] as String,
      deviceIdentifier: json['device_id'] as String,
      countryCode: json['country_code'] as String? ?? 'nl',
      referralCode: json['referral_code'] as String?,
    );

const _$$SendOtpRequestImplFieldMap = <String, String>{
  'phoneNumber': 'telephone',
  'deviceIdentifier': 'device_id',
  'countryCode': 'country_code',
  'referralCode': 'referral_code',
};

Map<String, dynamic> _$$SendOtpRequestImplToJson(
        _$SendOtpRequestImpl instance) =>
    <String, dynamic>{
      'telephone': instance.phoneNumber,
      'device_id': instance.deviceIdentifier,
      'country_code': instance.countryCode,
      if (instance.referralCode case final value?) 'referral_code': value,
    };
