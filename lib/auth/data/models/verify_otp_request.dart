import 'package:freezed_annotation/freezed_annotation.dart';

part 'verify_otp_request.freezed.dart';
part 'verify_otp_request.g.dart';

@freezed
class VerifyOtpRequest with _$VerifyOtpRequest {
  factory VerifyOtpRequest({
    @J<PERSON><PERSON>ey(name: 'telephone') required String phoneNumber,
    required String otp,
    @JsonKey(name: 'country_code') String? countryCode,
  }) = _VerifyOtpRequest;

  factory VerifyOtpRequest.fromJson(Map<String, Object?> json) =>
      _$VerifyOtpRequestFromJson(json);
}
