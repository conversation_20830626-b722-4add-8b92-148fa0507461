enum AuthenticationRequestStatus {
  initial,
  loading,
  success,
  error,
}

extension AuthenticationRequestStatusX on AuthenticationRequestStatus {
  bool get isLoading => this == AuthenticationRequestStatus.loading;
  bool get isSuccess => this == AuthenticationRequestStatus.success;
  bool get isError => this == AuthenticationRequestStatus.error;
  bool get isInitial => this == AuthenticationRequestStatus.initial;
}
