import 'package:freezed_annotation/freezed_annotation.dart';

part 'send_otp_response.freezed.dart';
part 'send_otp_response.g.dart';

@freezed
class SendOtpResponse with _$SendOtpResponse {
  factory SendOtpResponse({
    @Json<PERSON>ey(name: 'expires_at') String? expiresAt,
    @Default('2') @JsonKey(name: 'no_of_tries_left') String noOfTriesLeft,
  }) = _SendOtpResponse;

  factory SendOtpResponse.fromJson(Map<String, Object?> json) =>
      _$SendOtpResponseFromJson(json);
}
