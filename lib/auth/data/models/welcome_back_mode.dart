enum WelcomeBackMode {
  unknown,
  proceedToHome,
  needsBiometrics,
  needsPassword,
  needsPincode,
}

extension WelcomeBackModeX on WelcomeBackMode {
  bool get isUnknown => this == WelcomeBackMode.unknown;
  bool get isProceedToHome => this == WelcomeBackMode.proceedToHome;
  bool get isNeedsBiometrics => this == WelcomeBackMode.needsBiometrics;
  bool get isNeedsPassword => this == WelcomeBackMode.needsPassword;
  bool get isNeedsPincode => this == WelcomeBackMode.needsPincode;

  String get message {
    switch (this) {
      case WelcomeBackMode.unknown:
        return 'Unknown';
      case WelcomeBackMode.proceedToHome:
        return 'Proceed to home';
      case WelcomeBackMode.needsBiometrics:
        return 'Needs biometrics';
      case WelcomeBackMode.needsPassword:
        return 'Needs password';
      case WelcomeBackMode.needsPincode:
        return 'Needs pincode';
    }
  }

  int get index {
    switch (this) {
      case WelcomeBackMode.unknown:
        return 0;
      case WelcomeBackMode.proceedToHome:
        return 1;
      case WelcomeBackMode.needsBiometrics:
        return 2;
      case WelcomeBackMode.needsPassword:
        return 3;
      case WelcomeBackMode.needsPincode:
        return 4;
    }
  }

  String get value {
    switch (this) {
      case WelcomeBackMode.unknown:
        return 'unknown';
      case WelcomeBackMode.proceedToHome:
        return 'proceedToHome';
      case WelcomeBackMode.needsBiometrics:
        return 'needsBiometrics';
      case WelcomeBackMode.needsPassword:
        return 'needsPassword';
      case WelcomeBackMode.needsPincode:
        return 'needsPincode';
    }
  }
}
