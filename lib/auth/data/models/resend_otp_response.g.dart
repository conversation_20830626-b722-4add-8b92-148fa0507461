// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'resend_otp_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ResendOtpResponseImpl _$$ResendOtpResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$ResendOtpResponseImpl(
      token: json['token'] as String?,
      expiresAt: json['expires_at'] as String?,
      noOfTriesLeft: json['no_of_tries_left'] as String? ?? '2',
    );

const _$$ResendOtpResponseImplFieldMap = <String, String>{
  'token': 'token',
  'expiresAt': 'expires_at',
  'noOfTriesLeft': 'no_of_tries_left',
};

Map<String, dynamic> _$$ResendOtpResponseImplToJson(
        _$ResendOtpResponseImpl instance) =>
    <String, dynamic>{
      if (instance.token case final value?) 'token': value,
      if (instance.expiresAt case final value?) 'expires_at': value,
      'no_of_tries_left': instance.noOfTriesLeft,
    };
