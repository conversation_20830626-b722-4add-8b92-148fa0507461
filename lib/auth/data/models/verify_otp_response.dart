import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:froggytalk/profile/profile.dart';

part 'verify_otp_response.freezed.dart';
part 'verify_otp_response.g.dart';

@freezed
class VerifyOtpResponse with _$VerifyOtpResponse {
  factory VerifyOtpResponse({
    required String token,
    UserResponse? user,
  }) = _VerifyOtpResponse;

  factory VerifyOtpResponse.fromJson(Map<String, Object?> json) =>
      _$VerifyOtpResponseFromJson(json);
}
