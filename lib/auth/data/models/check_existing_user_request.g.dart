// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'check_existing_user_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CheckExistingUserRequestImpl _$$CheckExistingUserRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$CheckExistingUserRequestImpl(
      phoneNumber: json['telephone'] as String,
    );

const _$$CheckExistingUserRequestImplFieldMap = <String, String>{
  'phoneNumber': 'telephone',
};

Map<String, dynamic> _$$CheckExistingUserRequestImplToJson(
        _$CheckExistingUserRequestImpl instance) =>
    <String, dynamic>{
      'telephone': instance.phoneNumber,
    };
