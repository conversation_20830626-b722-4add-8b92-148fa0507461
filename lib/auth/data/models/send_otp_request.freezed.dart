// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'send_otp_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SendOtpRequest _$SendOtpRequestFromJson(Map<String, dynamic> json) {
  return _SendOtpRequest.fromJson(json);
}

/// @nodoc
mixin _$SendOtpRequest {
  @JsonKey(name: 'telephone')
  String get phoneNumber => throw _privateConstructorUsedError;
  @JsonKey(name: 'device_id')
  String get deviceIdentifier => throw _privateConstructorUsedError;
  @JsonKey(name: 'country_code')
  String get countryCode => throw _privateConstructorUsedError;
  @JsonKey(name: 'referral_code')
  String? get referralCode => throw _privateConstructorUsedError;

  /// Serializes this SendOtpRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SendOtpRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SendOtpRequestCopyWith<SendOtpRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SendOtpRequestCopyWith<$Res> {
  factory $SendOtpRequestCopyWith(
          SendOtpRequest value, $Res Function(SendOtpRequest) then) =
      _$SendOtpRequestCopyWithImpl<$Res, SendOtpRequest>;
  @useResult
  $Res call(
      {@JsonKey(name: 'telephone') String phoneNumber,
      @JsonKey(name: 'device_id') String deviceIdentifier,
      @JsonKey(name: 'country_code') String countryCode,
      @JsonKey(name: 'referral_code') String? referralCode});
}

/// @nodoc
class _$SendOtpRequestCopyWithImpl<$Res, $Val extends SendOtpRequest>
    implements $SendOtpRequestCopyWith<$Res> {
  _$SendOtpRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SendOtpRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phoneNumber = null,
    Object? deviceIdentifier = null,
    Object? countryCode = null,
    Object? referralCode = freezed,
  }) {
    return _then(_value.copyWith(
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      deviceIdentifier: null == deviceIdentifier
          ? _value.deviceIdentifier
          : deviceIdentifier // ignore: cast_nullable_to_non_nullable
              as String,
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String,
      referralCode: freezed == referralCode
          ? _value.referralCode
          : referralCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SendOtpRequestImplCopyWith<$Res>
    implements $SendOtpRequestCopyWith<$Res> {
  factory _$$SendOtpRequestImplCopyWith(_$SendOtpRequestImpl value,
          $Res Function(_$SendOtpRequestImpl) then) =
      __$$SendOtpRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'telephone') String phoneNumber,
      @JsonKey(name: 'device_id') String deviceIdentifier,
      @JsonKey(name: 'country_code') String countryCode,
      @JsonKey(name: 'referral_code') String? referralCode});
}

/// @nodoc
class __$$SendOtpRequestImplCopyWithImpl<$Res>
    extends _$SendOtpRequestCopyWithImpl<$Res, _$SendOtpRequestImpl>
    implements _$$SendOtpRequestImplCopyWith<$Res> {
  __$$SendOtpRequestImplCopyWithImpl(
      _$SendOtpRequestImpl _value, $Res Function(_$SendOtpRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of SendOtpRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phoneNumber = null,
    Object? deviceIdentifier = null,
    Object? countryCode = null,
    Object? referralCode = freezed,
  }) {
    return _then(_$SendOtpRequestImpl(
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      deviceIdentifier: null == deviceIdentifier
          ? _value.deviceIdentifier
          : deviceIdentifier // ignore: cast_nullable_to_non_nullable
              as String,
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String,
      referralCode: freezed == referralCode
          ? _value.referralCode
          : referralCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SendOtpRequestImpl implements _SendOtpRequest {
  _$SendOtpRequestImpl(
      {@JsonKey(name: 'telephone') required this.phoneNumber,
      @JsonKey(name: 'device_id') required this.deviceIdentifier,
      @JsonKey(name: 'country_code') this.countryCode = 'nl',
      @JsonKey(name: 'referral_code') this.referralCode});

  factory _$SendOtpRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$SendOtpRequestImplFromJson(json);

  @override
  @JsonKey(name: 'telephone')
  final String phoneNumber;
  @override
  @JsonKey(name: 'device_id')
  final String deviceIdentifier;
  @override
  @JsonKey(name: 'country_code')
  final String countryCode;
  @override
  @JsonKey(name: 'referral_code')
  final String? referralCode;

  @override
  String toString() {
    return 'SendOtpRequest(phoneNumber: $phoneNumber, deviceIdentifier: $deviceIdentifier, countryCode: $countryCode, referralCode: $referralCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SendOtpRequestImpl &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.deviceIdentifier, deviceIdentifier) ||
                other.deviceIdentifier == deviceIdentifier) &&
            (identical(other.countryCode, countryCode) ||
                other.countryCode == countryCode) &&
            (identical(other.referralCode, referralCode) ||
                other.referralCode == referralCode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, phoneNumber, deviceIdentifier, countryCode, referralCode);

  /// Create a copy of SendOtpRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SendOtpRequestImplCopyWith<_$SendOtpRequestImpl> get copyWith =>
      __$$SendOtpRequestImplCopyWithImpl<_$SendOtpRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SendOtpRequestImplToJson(
      this,
    );
  }
}

abstract class _SendOtpRequest implements SendOtpRequest {
  factory _SendOtpRequest(
          {@JsonKey(name: 'telephone') required final String phoneNumber,
          @JsonKey(name: 'device_id') required final String deviceIdentifier,
          @JsonKey(name: 'country_code') final String countryCode,
          @JsonKey(name: 'referral_code') final String? referralCode}) =
      _$SendOtpRequestImpl;

  factory _SendOtpRequest.fromJson(Map<String, dynamic> json) =
      _$SendOtpRequestImpl.fromJson;

  @override
  @JsonKey(name: 'telephone')
  String get phoneNumber;
  @override
  @JsonKey(name: 'device_id')
  String get deviceIdentifier;
  @override
  @JsonKey(name: 'country_code')
  String get countryCode;
  @override
  @JsonKey(name: 'referral_code')
  String? get referralCode;

  /// Create a copy of SendOtpRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SendOtpRequestImplCopyWith<_$SendOtpRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
