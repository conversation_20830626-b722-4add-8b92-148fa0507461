import 'package:freezed_annotation/freezed_annotation.dart';

part 'send_otp_request.freezed.dart';
part 'send_otp_request.g.dart';

@freezed
class SendOtpRequest with _$SendOtpRequest {
  factory SendOtpRequest({
    @J<PERSON><PERSON>ey(name: 'telephone') required String phoneNumber,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'device_id') required String deviceIdentifier,
    @Default('nl') @<PERSON>sonKey(name: 'country_code') String countryCode,
    @J<PERSON><PERSON>ey(name: 'referral_code') String? referralCode,
  }) = _SendOtpRequest;

  factory SendOtpRequest.fromJson(Map<String, Object?> json) =>
      _$SendOtpRequestFromJson(json);
}
