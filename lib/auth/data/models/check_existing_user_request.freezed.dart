// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'check_existing_user_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CheckExistingUserRequest _$CheckExistingUserRequestFromJson(
    Map<String, dynamic> json) {
  return _CheckExistingUserRequest.fromJson(json);
}

/// @nodoc
mixin _$CheckExistingUserRequest {
  @JsonKey(name: 'telephone')
  String get phoneNumber => throw _privateConstructorUsedError;

  /// Serializes this CheckExistingUserRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CheckExistingUserRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CheckExistingUserRequestCopyWith<CheckExistingUserRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CheckExistingUserRequestCopyWith<$Res> {
  factory $CheckExistingUserRequestCopyWith(CheckExistingUserRequest value,
          $Res Function(CheckExistingUserRequest) then) =
      _$CheckExistingUserRequestCopyWithImpl<$Res, CheckExistingUserRequest>;
  @useResult
  $Res call({@JsonKey(name: 'telephone') String phoneNumber});
}

/// @nodoc
class _$CheckExistingUserRequestCopyWithImpl<$Res,
        $Val extends CheckExistingUserRequest>
    implements $CheckExistingUserRequestCopyWith<$Res> {
  _$CheckExistingUserRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CheckExistingUserRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phoneNumber = null,
  }) {
    return _then(_value.copyWith(
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CheckExistingUserRequestImplCopyWith<$Res>
    implements $CheckExistingUserRequestCopyWith<$Res> {
  factory _$$CheckExistingUserRequestImplCopyWith(
          _$CheckExistingUserRequestImpl value,
          $Res Function(_$CheckExistingUserRequestImpl) then) =
      __$$CheckExistingUserRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({@JsonKey(name: 'telephone') String phoneNumber});
}

/// @nodoc
class __$$CheckExistingUserRequestImplCopyWithImpl<$Res>
    extends _$CheckExistingUserRequestCopyWithImpl<$Res,
        _$CheckExistingUserRequestImpl>
    implements _$$CheckExistingUserRequestImplCopyWith<$Res> {
  __$$CheckExistingUserRequestImplCopyWithImpl(
      _$CheckExistingUserRequestImpl _value,
      $Res Function(_$CheckExistingUserRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of CheckExistingUserRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phoneNumber = null,
  }) {
    return _then(_$CheckExistingUserRequestImpl(
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CheckExistingUserRequestImpl implements _CheckExistingUserRequest {
  _$CheckExistingUserRequestImpl(
      {@JsonKey(name: 'telephone') required this.phoneNumber});

  factory _$CheckExistingUserRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$CheckExistingUserRequestImplFromJson(json);

  @override
  @JsonKey(name: 'telephone')
  final String phoneNumber;

  @override
  String toString() {
    return 'CheckExistingUserRequest(phoneNumber: $phoneNumber)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CheckExistingUserRequestImpl &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, phoneNumber);

  /// Create a copy of CheckExistingUserRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CheckExistingUserRequestImplCopyWith<_$CheckExistingUserRequestImpl>
      get copyWith => __$$CheckExistingUserRequestImplCopyWithImpl<
          _$CheckExistingUserRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CheckExistingUserRequestImplToJson(
      this,
    );
  }
}

abstract class _CheckExistingUserRequest implements CheckExistingUserRequest {
  factory _CheckExistingUserRequest(
          {@JsonKey(name: 'telephone') required final String phoneNumber}) =
      _$CheckExistingUserRequestImpl;

  factory _CheckExistingUserRequest.fromJson(Map<String, dynamic> json) =
      _$CheckExistingUserRequestImpl.fromJson;

  @override
  @JsonKey(name: 'telephone')
  String get phoneNumber;

  /// Create a copy of CheckExistingUserRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CheckExistingUserRequestImplCopyWith<_$CheckExistingUserRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
