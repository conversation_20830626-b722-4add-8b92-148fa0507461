// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'send_otp_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SendOtpResponseImpl _$$SendOtpResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$SendOtpResponseImpl(
      expiresAt: json['expires_at'] as String?,
      noOfTriesLeft: json['no_of_tries_left'] as String? ?? '2',
    );

const _$$SendOtpResponseImplFieldMap = <String, String>{
  'expiresAt': 'expires_at',
  'noOfTriesLeft': 'no_of_tries_left',
};

Map<String, dynamic> _$$SendOtpResponseImplToJson(
        _$SendOtpResponseImpl instance) =>
    <String, dynamic>{
      if (instance.expiresAt case final value?) 'expires_at': value,
      'no_of_tries_left': instance.noOfTriesLeft,
    };
