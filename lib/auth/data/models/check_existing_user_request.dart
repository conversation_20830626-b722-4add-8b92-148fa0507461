import 'package:freezed_annotation/freezed_annotation.dart';

part 'check_existing_user_request.freezed.dart';
part 'check_existing_user_request.g.dart';

@freezed
class CheckExistingUserRequest with _$CheckExistingUserRequest {
  factory CheckExistingUserRequest({
    @J<PERSON><PERSON>ey(name: 'telephone') required String phoneNumber,
  }) = _CheckExistingUserRequest;

  factory CheckExistingUserRequest.fromJson(Map<String, Object?> json) =>
      _$CheckExistingUserRequestFromJson(json);
}
