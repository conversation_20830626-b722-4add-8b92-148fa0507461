// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'phone_number_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$PhoneNumberData {
  /// The raw phone number input from the user
  String get rawInput => throw _privateConstructorUsedError;

  /// The national number (without country prefix)
  String get nationalNumber => throw _privateConstructorUsedError;

  /// ISO 3166-1 alpha-2 country code (e.g., 'nl', 'us')
  String get countryCode => throw _privateConstructorUsedError;

  /// Country model with additional metadata
  CountryModel? get country => throw _privateConstructorUsedError;

  /// Formatted phone number for display purposes
  String? get formattedNumber => throw _privateConstructorUsedError;

  /// Whether the phone number is valid according to libphonenumber
  bool get isValid => throw _privateConstructorUsedError;

  /// Error message if validation fails
  String? get errorMessage => throw _privateConstructorUsedError;

  /// Detailed parsing result for advanced error handling
  PhoneNumberParseResult? get parseResult => throw _privateConstructorUsedError;

  /// Create a copy of PhoneNumberData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PhoneNumberDataCopyWith<PhoneNumberData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PhoneNumberDataCopyWith<$Res> {
  factory $PhoneNumberDataCopyWith(
          PhoneNumberData value, $Res Function(PhoneNumberData) then) =
      _$PhoneNumberDataCopyWithImpl<$Res, PhoneNumberData>;
  @useResult
  $Res call(
      {String rawInput,
      String nationalNumber,
      String countryCode,
      CountryModel? country,
      String? formattedNumber,
      bool isValid,
      String? errorMessage,
      PhoneNumberParseResult? parseResult});

  $CountryModelCopyWith<$Res>? get country;
  $PhoneNumberParseResultCopyWith<$Res>? get parseResult;
}

/// @nodoc
class _$PhoneNumberDataCopyWithImpl<$Res, $Val extends PhoneNumberData>
    implements $PhoneNumberDataCopyWith<$Res> {
  _$PhoneNumberDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PhoneNumberData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? rawInput = null,
    Object? nationalNumber = null,
    Object? countryCode = null,
    Object? country = freezed,
    Object? formattedNumber = freezed,
    Object? isValid = null,
    Object? errorMessage = freezed,
    Object? parseResult = freezed,
  }) {
    return _then(_value.copyWith(
      rawInput: null == rawInput
          ? _value.rawInput
          : rawInput // ignore: cast_nullable_to_non_nullable
              as String,
      nationalNumber: null == nationalNumber
          ? _value.nationalNumber
          : nationalNumber // ignore: cast_nullable_to_non_nullable
              as String,
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as CountryModel?,
      formattedNumber: freezed == formattedNumber
          ? _value.formattedNumber
          : formattedNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      isValid: null == isValid
          ? _value.isValid
          : isValid // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      parseResult: freezed == parseResult
          ? _value.parseResult
          : parseResult // ignore: cast_nullable_to_non_nullable
              as PhoneNumberParseResult?,
    ) as $Val);
  }

  /// Create a copy of PhoneNumberData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CountryModelCopyWith<$Res>? get country {
    if (_value.country == null) {
      return null;
    }

    return $CountryModelCopyWith<$Res>(_value.country!, (value) {
      return _then(_value.copyWith(country: value) as $Val);
    });
  }

  /// Create a copy of PhoneNumberData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PhoneNumberParseResultCopyWith<$Res>? get parseResult {
    if (_value.parseResult == null) {
      return null;
    }

    return $PhoneNumberParseResultCopyWith<$Res>(_value.parseResult!, (value) {
      return _then(_value.copyWith(parseResult: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$PhoneNumberDataImplCopyWith<$Res>
    implements $PhoneNumberDataCopyWith<$Res> {
  factory _$$PhoneNumberDataImplCopyWith(_$PhoneNumberDataImpl value,
          $Res Function(_$PhoneNumberDataImpl) then) =
      __$$PhoneNumberDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String rawInput,
      String nationalNumber,
      String countryCode,
      CountryModel? country,
      String? formattedNumber,
      bool isValid,
      String? errorMessage,
      PhoneNumberParseResult? parseResult});

  @override
  $CountryModelCopyWith<$Res>? get country;
  @override
  $PhoneNumberParseResultCopyWith<$Res>? get parseResult;
}

/// @nodoc
class __$$PhoneNumberDataImplCopyWithImpl<$Res>
    extends _$PhoneNumberDataCopyWithImpl<$Res, _$PhoneNumberDataImpl>
    implements _$$PhoneNumberDataImplCopyWith<$Res> {
  __$$PhoneNumberDataImplCopyWithImpl(
      _$PhoneNumberDataImpl _value, $Res Function(_$PhoneNumberDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of PhoneNumberData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? rawInput = null,
    Object? nationalNumber = null,
    Object? countryCode = null,
    Object? country = freezed,
    Object? formattedNumber = freezed,
    Object? isValid = null,
    Object? errorMessage = freezed,
    Object? parseResult = freezed,
  }) {
    return _then(_$PhoneNumberDataImpl(
      rawInput: null == rawInput
          ? _value.rawInput
          : rawInput // ignore: cast_nullable_to_non_nullable
              as String,
      nationalNumber: null == nationalNumber
          ? _value.nationalNumber
          : nationalNumber // ignore: cast_nullable_to_non_nullable
              as String,
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as CountryModel?,
      formattedNumber: freezed == formattedNumber
          ? _value.formattedNumber
          : formattedNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      isValid: null == isValid
          ? _value.isValid
          : isValid // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      parseResult: freezed == parseResult
          ? _value.parseResult
          : parseResult // ignore: cast_nullable_to_non_nullable
              as PhoneNumberParseResult?,
    ));
  }
}

/// @nodoc

class _$PhoneNumberDataImpl extends _PhoneNumberData {
  const _$PhoneNumberDataImpl(
      {this.rawInput = '',
      this.nationalNumber = '',
      this.countryCode = '',
      this.country,
      this.formattedNumber,
      this.isValid = false,
      this.errorMessage,
      this.parseResult})
      : super._();

  /// The raw phone number input from the user
  @override
  @JsonKey()
  final String rawInput;

  /// The national number (without country prefix)
  @override
  @JsonKey()
  final String nationalNumber;

  /// ISO 3166-1 alpha-2 country code (e.g., 'nl', 'us')
  @override
  @JsonKey()
  final String countryCode;

  /// Country model with additional metadata
  @override
  final CountryModel? country;

  /// Formatted phone number for display purposes
  @override
  final String? formattedNumber;

  /// Whether the phone number is valid according to libphonenumber
  @override
  @JsonKey()
  final bool isValid;

  /// Error message if validation fails
  @override
  final String? errorMessage;

  /// Detailed parsing result for advanced error handling
  @override
  final PhoneNumberParseResult? parseResult;

  @override
  String toString() {
    return 'PhoneNumberData(rawInput: $rawInput, nationalNumber: $nationalNumber, countryCode: $countryCode, country: $country, formattedNumber: $formattedNumber, isValid: $isValid, errorMessage: $errorMessage, parseResult: $parseResult)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PhoneNumberDataImpl &&
            (identical(other.rawInput, rawInput) ||
                other.rawInput == rawInput) &&
            (identical(other.nationalNumber, nationalNumber) ||
                other.nationalNumber == nationalNumber) &&
            (identical(other.countryCode, countryCode) ||
                other.countryCode == countryCode) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.formattedNumber, formattedNumber) ||
                other.formattedNumber == formattedNumber) &&
            (identical(other.isValid, isValid) || other.isValid == isValid) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.parseResult, parseResult) ||
                other.parseResult == parseResult));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      rawInput,
      nationalNumber,
      countryCode,
      country,
      formattedNumber,
      isValid,
      errorMessage,
      parseResult);

  /// Create a copy of PhoneNumberData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PhoneNumberDataImplCopyWith<_$PhoneNumberDataImpl> get copyWith =>
      __$$PhoneNumberDataImplCopyWithImpl<_$PhoneNumberDataImpl>(
          this, _$identity);
}

abstract class _PhoneNumberData extends PhoneNumberData {
  const factory _PhoneNumberData(
      {final String rawInput,
      final String nationalNumber,
      final String countryCode,
      final CountryModel? country,
      final String? formattedNumber,
      final bool isValid,
      final String? errorMessage,
      final PhoneNumberParseResult? parseResult}) = _$PhoneNumberDataImpl;
  const _PhoneNumberData._() : super._();

  /// The raw phone number input from the user
  @override
  String get rawInput;

  /// The national number (without country prefix)
  @override
  String get nationalNumber;

  /// ISO 3166-1 alpha-2 country code (e.g., 'nl', 'us')
  @override
  String get countryCode;

  /// Country model with additional metadata
  @override
  CountryModel? get country;

  /// Formatted phone number for display purposes
  @override
  String? get formattedNumber;

  /// Whether the phone number is valid according to libphonenumber
  @override
  bool get isValid;

  /// Error message if validation fails
  @override
  String? get errorMessage;

  /// Detailed parsing result for advanced error handling
  @override
  PhoneNumberParseResult? get parseResult;

  /// Create a copy of PhoneNumberData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PhoneNumberDataImplCopyWith<_$PhoneNumberDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$PhoneNumberTransferData {
  String get phoneNumber => throw _privateConstructorUsedError;
  String get nationalNumber => throw _privateConstructorUsedError;
  String get countryCode => throw _privateConstructorUsedError;
  CountryModel get country => throw _privateConstructorUsedError;
  String? get formattedNumber => throw _privateConstructorUsedError;
  bool get isValid => throw _privateConstructorUsedError;

  /// Create a copy of PhoneNumberTransferData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PhoneNumberTransferDataCopyWith<PhoneNumberTransferData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PhoneNumberTransferDataCopyWith<$Res> {
  factory $PhoneNumberTransferDataCopyWith(PhoneNumberTransferData value,
          $Res Function(PhoneNumberTransferData) then) =
      _$PhoneNumberTransferDataCopyWithImpl<$Res, PhoneNumberTransferData>;
  @useResult
  $Res call(
      {String phoneNumber,
      String nationalNumber,
      String countryCode,
      CountryModel country,
      String? formattedNumber,
      bool isValid});

  $CountryModelCopyWith<$Res> get country;
}

/// @nodoc
class _$PhoneNumberTransferDataCopyWithImpl<$Res,
        $Val extends PhoneNumberTransferData>
    implements $PhoneNumberTransferDataCopyWith<$Res> {
  _$PhoneNumberTransferDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PhoneNumberTransferData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phoneNumber = null,
    Object? nationalNumber = null,
    Object? countryCode = null,
    Object? country = null,
    Object? formattedNumber = freezed,
    Object? isValid = null,
  }) {
    return _then(_value.copyWith(
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      nationalNumber: null == nationalNumber
          ? _value.nationalNumber
          : nationalNumber // ignore: cast_nullable_to_non_nullable
              as String,
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String,
      country: null == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as CountryModel,
      formattedNumber: freezed == formattedNumber
          ? _value.formattedNumber
          : formattedNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      isValid: null == isValid
          ? _value.isValid
          : isValid // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  /// Create a copy of PhoneNumberTransferData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CountryModelCopyWith<$Res> get country {
    return $CountryModelCopyWith<$Res>(_value.country, (value) {
      return _then(_value.copyWith(country: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$PhoneNumberTransferDataImplCopyWith<$Res>
    implements $PhoneNumberTransferDataCopyWith<$Res> {
  factory _$$PhoneNumberTransferDataImplCopyWith(
          _$PhoneNumberTransferDataImpl value,
          $Res Function(_$PhoneNumberTransferDataImpl) then) =
      __$$PhoneNumberTransferDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String phoneNumber,
      String nationalNumber,
      String countryCode,
      CountryModel country,
      String? formattedNumber,
      bool isValid});

  @override
  $CountryModelCopyWith<$Res> get country;
}

/// @nodoc
class __$$PhoneNumberTransferDataImplCopyWithImpl<$Res>
    extends _$PhoneNumberTransferDataCopyWithImpl<$Res,
        _$PhoneNumberTransferDataImpl>
    implements _$$PhoneNumberTransferDataImplCopyWith<$Res> {
  __$$PhoneNumberTransferDataImplCopyWithImpl(
      _$PhoneNumberTransferDataImpl _value,
      $Res Function(_$PhoneNumberTransferDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of PhoneNumberTransferData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? phoneNumber = null,
    Object? nationalNumber = null,
    Object? countryCode = null,
    Object? country = null,
    Object? formattedNumber = freezed,
    Object? isValid = null,
  }) {
    return _then(_$PhoneNumberTransferDataImpl(
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      nationalNumber: null == nationalNumber
          ? _value.nationalNumber
          : nationalNumber // ignore: cast_nullable_to_non_nullable
              as String,
      countryCode: null == countryCode
          ? _value.countryCode
          : countryCode // ignore: cast_nullable_to_non_nullable
              as String,
      country: null == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as CountryModel,
      formattedNumber: freezed == formattedNumber
          ? _value.formattedNumber
          : formattedNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      isValid: null == isValid
          ? _value.isValid
          : isValid // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$PhoneNumberTransferDataImpl implements _PhoneNumberTransferData {
  const _$PhoneNumberTransferDataImpl(
      {required this.phoneNumber,
      required this.nationalNumber,
      required this.countryCode,
      required this.country,
      this.formattedNumber,
      this.isValid = true});

  @override
  final String phoneNumber;
  @override
  final String nationalNumber;
  @override
  final String countryCode;
  @override
  final CountryModel country;
  @override
  final String? formattedNumber;
  @override
  @JsonKey()
  final bool isValid;

  @override
  String toString() {
    return 'PhoneNumberTransferData(phoneNumber: $phoneNumber, nationalNumber: $nationalNumber, countryCode: $countryCode, country: $country, formattedNumber: $formattedNumber, isValid: $isValid)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PhoneNumberTransferDataImpl &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.nationalNumber, nationalNumber) ||
                other.nationalNumber == nationalNumber) &&
            (identical(other.countryCode, countryCode) ||
                other.countryCode == countryCode) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.formattedNumber, formattedNumber) ||
                other.formattedNumber == formattedNumber) &&
            (identical(other.isValid, isValid) || other.isValid == isValid));
  }

  @override
  int get hashCode => Object.hash(runtimeType, phoneNumber, nationalNumber,
      countryCode, country, formattedNumber, isValid);

  /// Create a copy of PhoneNumberTransferData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PhoneNumberTransferDataImplCopyWith<_$PhoneNumberTransferDataImpl>
      get copyWith => __$$PhoneNumberTransferDataImplCopyWithImpl<
          _$PhoneNumberTransferDataImpl>(this, _$identity);
}

abstract class _PhoneNumberTransferData implements PhoneNumberTransferData {
  const factory _PhoneNumberTransferData(
      {required final String phoneNumber,
      required final String nationalNumber,
      required final String countryCode,
      required final CountryModel country,
      final String? formattedNumber,
      final bool isValid}) = _$PhoneNumberTransferDataImpl;

  @override
  String get phoneNumber;
  @override
  String get nationalNumber;
  @override
  String get countryCode;
  @override
  CountryModel get country;
  @override
  String? get formattedNumber;
  @override
  bool get isValid;

  /// Create a copy of PhoneNumberTransferData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PhoneNumberTransferDataImplCopyWith<_$PhoneNumberTransferDataImpl>
      get copyWith => throw _privateConstructorUsedError;
}
