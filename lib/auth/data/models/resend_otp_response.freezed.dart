// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'resend_otp_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ResendOtpResponse _$ResendOtpResponseFromJson(Map<String, dynamic> json) {
  return _ResendOtpResponse.fromJson(json);
}

/// @nodoc
mixin _$ResendOtpResponse {
  String? get token => throw _privateConstructorUsedError;
  @JsonKey(name: 'expires_at')
  String? get expiresAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'no_of_tries_left')
  String get noOfTriesLeft => throw _privateConstructorUsedError;

  /// Serializes this ResendOtpResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ResendOtpResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ResendOtpResponseCopyWith<ResendOtpResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ResendOtpResponseCopyWith<$Res> {
  factory $ResendOtpResponseCopyWith(
          ResendOtpResponse value, $Res Function(ResendOtpResponse) then) =
      _$ResendOtpResponseCopyWithImpl<$Res, ResendOtpResponse>;
  @useResult
  $Res call(
      {String? token,
      @JsonKey(name: 'expires_at') String? expiresAt,
      @JsonKey(name: 'no_of_tries_left') String noOfTriesLeft});
}

/// @nodoc
class _$ResendOtpResponseCopyWithImpl<$Res, $Val extends ResendOtpResponse>
    implements $ResendOtpResponseCopyWith<$Res> {
  _$ResendOtpResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ResendOtpResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? token = freezed,
    Object? expiresAt = freezed,
    Object? noOfTriesLeft = null,
  }) {
    return _then(_value.copyWith(
      token: freezed == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String?,
      expiresAt: freezed == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as String?,
      noOfTriesLeft: null == noOfTriesLeft
          ? _value.noOfTriesLeft
          : noOfTriesLeft // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ResendOtpResponseImplCopyWith<$Res>
    implements $ResendOtpResponseCopyWith<$Res> {
  factory _$$ResendOtpResponseImplCopyWith(_$ResendOtpResponseImpl value,
          $Res Function(_$ResendOtpResponseImpl) then) =
      __$$ResendOtpResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? token,
      @JsonKey(name: 'expires_at') String? expiresAt,
      @JsonKey(name: 'no_of_tries_left') String noOfTriesLeft});
}

/// @nodoc
class __$$ResendOtpResponseImplCopyWithImpl<$Res>
    extends _$ResendOtpResponseCopyWithImpl<$Res, _$ResendOtpResponseImpl>
    implements _$$ResendOtpResponseImplCopyWith<$Res> {
  __$$ResendOtpResponseImplCopyWithImpl(_$ResendOtpResponseImpl _value,
      $Res Function(_$ResendOtpResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of ResendOtpResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? token = freezed,
    Object? expiresAt = freezed,
    Object? noOfTriesLeft = null,
  }) {
    return _then(_$ResendOtpResponseImpl(
      token: freezed == token
          ? _value.token
          : token // ignore: cast_nullable_to_non_nullable
              as String?,
      expiresAt: freezed == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as String?,
      noOfTriesLeft: null == noOfTriesLeft
          ? _value.noOfTriesLeft
          : noOfTriesLeft // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ResendOtpResponseImpl implements _ResendOtpResponse {
  _$ResendOtpResponseImpl(
      {this.token,
      @JsonKey(name: 'expires_at') this.expiresAt,
      @JsonKey(name: 'no_of_tries_left') this.noOfTriesLeft = '2'});

  factory _$ResendOtpResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$ResendOtpResponseImplFromJson(json);

  @override
  final String? token;
  @override
  @JsonKey(name: 'expires_at')
  final String? expiresAt;
  @override
  @JsonKey(name: 'no_of_tries_left')
  final String noOfTriesLeft;

  @override
  String toString() {
    return 'ResendOtpResponse(token: $token, expiresAt: $expiresAt, noOfTriesLeft: $noOfTriesLeft)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ResendOtpResponseImpl &&
            (identical(other.token, token) || other.token == token) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt) &&
            (identical(other.noOfTriesLeft, noOfTriesLeft) ||
                other.noOfTriesLeft == noOfTriesLeft));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, token, expiresAt, noOfTriesLeft);

  /// Create a copy of ResendOtpResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ResendOtpResponseImplCopyWith<_$ResendOtpResponseImpl> get copyWith =>
      __$$ResendOtpResponseImplCopyWithImpl<_$ResendOtpResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ResendOtpResponseImplToJson(
      this,
    );
  }
}

abstract class _ResendOtpResponse implements ResendOtpResponse {
  factory _ResendOtpResponse(
          {final String? token,
          @JsonKey(name: 'expires_at') final String? expiresAt,
          @JsonKey(name: 'no_of_tries_left') final String noOfTriesLeft}) =
      _$ResendOtpResponseImpl;

  factory _ResendOtpResponse.fromJson(Map<String, dynamic> json) =
      _$ResendOtpResponseImpl.fromJson;

  @override
  String? get token;
  @override
  @JsonKey(name: 'expires_at')
  String? get expiresAt;
  @override
  @JsonKey(name: 'no_of_tries_left')
  String get noOfTriesLeft;

  /// Create a copy of ResendOtpResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ResendOtpResponseImplCopyWith<_$ResendOtpResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
