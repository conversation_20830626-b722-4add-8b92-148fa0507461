// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'check_existing_user_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CheckExistingUserResponseImpl _$$CheckExistingUserResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$CheckExistingUserResponseImpl(
      isExistingUser: json['exists'] as String,
    );

const _$$CheckExistingUserResponseImplFieldMap = <String, String>{
  'isExistingUser': 'exists',
};

Map<String, dynamic> _$$CheckExistingUserResponseImplToJson(
        _$CheckExistingUserResponseImpl instance) =>
    <String, dynamic>{
      'exists': instance.isExistingUser,
    };
