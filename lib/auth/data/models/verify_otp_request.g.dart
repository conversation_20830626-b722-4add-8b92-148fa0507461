// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'verify_otp_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$VerifyOtpRequestImpl _$$VerifyOtpRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$VerifyOtpRequestImpl(
      phoneNumber: json['telephone'] as String,
      otp: json['otp'] as String,
      countryCode: json['country_code'] as String?,
    );

const _$$VerifyOtpRequestImplFieldMap = <String, String>{
  'phoneNumber': 'telephone',
  'otp': 'otp',
  'countryCode': 'country_code',
};

Map<String, dynamic> _$$VerifyOtpRequestImplToJson(
        _$VerifyOtpRequestImpl instance) =>
    <String, dynamic>{
      'telephone': instance.phoneNumber,
      'otp': instance.otp,
      if (instance.countryCode case final value?) 'country_code': value,
    };
