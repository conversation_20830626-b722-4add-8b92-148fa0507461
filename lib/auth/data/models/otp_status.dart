enum AuthenticationOtpStatus {
  initial,
  loading,
  requested,
  verified,
}

extension AuthenticationOtpStatusX on AuthenticationOtpStatus {
  bool get isInitial => this == AuthenticationOtpStatus.initial;
  bool get isLoading => this == AuthenticationOtpStatus.loading;
  bool get isRequested => this == AuthenticationOtpStatus.requested;
  bool get isVerified => this == AuthenticationOtpStatus.verified;
}
