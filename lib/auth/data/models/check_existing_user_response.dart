import 'package:freezed_annotation/freezed_annotation.dart';

part 'check_existing_user_response.freezed.dart';
part 'check_existing_user_response.g.dart';

@freezed
class CheckExistingUserResponse with _$CheckExistingUserResponse {
  factory CheckExistingUserResponse({
    @JsonKey(name: 'exists') required String isExistingUser,
  }) = _CheckExistingUserResponse;

  factory CheckExistingUserResponse.fromJson(Map<String, Object?> json) =>
      _$CheckExistingUserResponseFromJson(json);
}
