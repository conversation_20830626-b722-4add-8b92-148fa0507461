// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'check_existing_user_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CheckExistingUserResponse _$CheckExistingUserResponseFromJson(
    Map<String, dynamic> json) {
  return _CheckExistingUserResponse.fromJson(json);
}

/// @nodoc
mixin _$CheckExistingUserResponse {
  @JsonKey(name: 'exists')
  String get isExistingUser => throw _privateConstructorUsedError;

  /// Serializes this CheckExistingUserResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CheckExistingUserResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CheckExistingUserResponseCopyWith<CheckExistingUserResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CheckExistingUserResponseCopyWith<$Res> {
  factory $CheckExistingUserResponseCopyWith(CheckExistingUserResponse value,
          $Res Function(CheckExistingUserResponse) then) =
      _$CheckExistingUserResponseCopyWithImpl<$Res, CheckExistingUserResponse>;
  @useResult
  $Res call({@JsonKey(name: 'exists') String isExistingUser});
}

/// @nodoc
class _$CheckExistingUserResponseCopyWithImpl<$Res,
        $Val extends CheckExistingUserResponse>
    implements $CheckExistingUserResponseCopyWith<$Res> {
  _$CheckExistingUserResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CheckExistingUserResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isExistingUser = null,
  }) {
    return _then(_value.copyWith(
      isExistingUser: null == isExistingUser
          ? _value.isExistingUser
          : isExistingUser // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CheckExistingUserResponseImplCopyWith<$Res>
    implements $CheckExistingUserResponseCopyWith<$Res> {
  factory _$$CheckExistingUserResponseImplCopyWith(
          _$CheckExistingUserResponseImpl value,
          $Res Function(_$CheckExistingUserResponseImpl) then) =
      __$$CheckExistingUserResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({@JsonKey(name: 'exists') String isExistingUser});
}

/// @nodoc
class __$$CheckExistingUserResponseImplCopyWithImpl<$Res>
    extends _$CheckExistingUserResponseCopyWithImpl<$Res,
        _$CheckExistingUserResponseImpl>
    implements _$$CheckExistingUserResponseImplCopyWith<$Res> {
  __$$CheckExistingUserResponseImplCopyWithImpl(
      _$CheckExistingUserResponseImpl _value,
      $Res Function(_$CheckExistingUserResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of CheckExistingUserResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isExistingUser = null,
  }) {
    return _then(_$CheckExistingUserResponseImpl(
      isExistingUser: null == isExistingUser
          ? _value.isExistingUser
          : isExistingUser // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CheckExistingUserResponseImpl implements _CheckExistingUserResponse {
  _$CheckExistingUserResponseImpl(
      {@JsonKey(name: 'exists') required this.isExistingUser});

  factory _$CheckExistingUserResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$CheckExistingUserResponseImplFromJson(json);

  @override
  @JsonKey(name: 'exists')
  final String isExistingUser;

  @override
  String toString() {
    return 'CheckExistingUserResponse(isExistingUser: $isExistingUser)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CheckExistingUserResponseImpl &&
            (identical(other.isExistingUser, isExistingUser) ||
                other.isExistingUser == isExistingUser));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, isExistingUser);

  /// Create a copy of CheckExistingUserResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CheckExistingUserResponseImplCopyWith<_$CheckExistingUserResponseImpl>
      get copyWith => __$$CheckExistingUserResponseImplCopyWithImpl<
          _$CheckExistingUserResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CheckExistingUserResponseImplToJson(
      this,
    );
  }
}

abstract class _CheckExistingUserResponse implements CheckExistingUserResponse {
  factory _CheckExistingUserResponse(
          {@JsonKey(name: 'exists') required final String isExistingUser}) =
      _$CheckExistingUserResponseImpl;

  factory _CheckExistingUserResponse.fromJson(Map<String, dynamic> json) =
      _$CheckExistingUserResponseImpl.fromJson;

  @override
  @JsonKey(name: 'exists')
  String get isExistingUser;

  /// Create a copy of CheckExistingUserResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CheckExistingUserResponseImplCopyWith<_$CheckExistingUserResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
