import 'package:json_annotation/json_annotation.dart';
import 'package:utils/utils.dart';

@JsonEnum()
enum AuthenticationStatus {
  unknown,
  authenticated,
  unauthenticated,
  checking,
  needsBiometrics,
}

/// Custom JsonConverter for AuthenticationStatus
class AuthenticationStatusConverter
    implements JsonConverter<AuthenticationStatus, String> {
  const AuthenticationStatusConverter();

  @override
  AuthenticationStatus fromJson(String json) {
    return json.toAuthenticationStatus ?? AuthenticationStatus.unknown;
  }

  @override
  String toJson(AuthenticationStatus status) {
    return status.toAuthenticationStatusString ?? 'unknown';
  }
}

extension AuthenticationStatusX on AuthenticationStatus {
  bool get isAuthenticated => this == AuthenticationStatus.authenticated;

  bool get isUnauthenticated => this == AuthenticationStatus.unauthenticated;

  bool get isChecking => this == AuthenticationStatus.checking;

  bool get needsBiometrics => this == AuthenticationStatus.needsBiometrics;

  AuthenticationStatus fromAuthenticationStatusString(String status) {
    switch (status) {
      case 'unknown':
        return AuthenticationStatus.unknown;

      case 'checking':
        return AuthenticationStatus.checking;

      default:
        return AuthenticationStatus.unknown;
    }
  }
}

extension AuthenticationStatusFromStringX on String {
  AuthenticationStatus? get toAuthenticationStatus => mapToEnum(
        {
          'unknown': AuthenticationStatus.unknown,
          'authenticated': AuthenticationStatus.authenticated,
          'unauthenticated': AuthenticationStatus.unauthenticated,
          'checking': AuthenticationStatus.checking,
          'needsBiometrics': AuthenticationStatus.needsBiometrics,
        },
        'unknown',
      );
}

extension AuthenticationStatusToStringX on AuthenticationStatus {
  String? get toAuthenticationStatusString =>
      mapToString<AuthenticationStatus, String>(
        {
          AuthenticationStatus.unknown: 'unknown',
          AuthenticationStatus.authenticated: 'authenticated',
          AuthenticationStatus.unauthenticated: 'unauthenticated',
          AuthenticationStatus.checking: 'checking',
          AuthenticationStatus.needsBiometrics: 'needsBiometrics',
        },
        AuthenticationStatus.unknown,
      );
}
