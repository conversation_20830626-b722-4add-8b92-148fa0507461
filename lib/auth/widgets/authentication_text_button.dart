import 'package:constants/constants.dart';
import 'package:flutter/material.dart';

class AuthenticationTextButton extends StatelessWidget {
  const AuthenticationTextButton({
    required this.linkText,
    this.onPressed,
    this.linkSuffixText,
    this.linkSuffixWidget,
    this.text,
    super.key,
  });

  // the text to display
  final String linkText;

  // suffix text
  final String? linkSuffixText;

  // suffix text
  final Widget? linkSuffixWidget;

  // text to display if the link text is not to be displayed
  final String? text;

  // when the button is pressed
  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    return Container(
      // padding: const EdgeInsets.all(8),
      padding: const EdgeInsets.symmetric(
        vertical: 5,
        horizontal: 20,
      ),
      margin: const EdgeInsets.symmetric(vertical: 2),
      child: Text.rich(
        TextSpan(
          children: [
            if (text == null)
              WidgetSpan(
                child: InkWell(
                  onTap: onPressed,
                  child: Text(
                    linkText,
                    style: Theme.of(context).textTheme.labelMedium?.copyWith(
                          color: Theme.of(context).primaryColor,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                ),
              ),
            if (linkSuffixWidget != null)
              WidgetSpan(
                child: linkSuffixWidget ?? const SizedBox.shrink(),
              ),
            if (linkSuffixText != null)
              TextSpan(
                text: ' - ',
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                      color: FroggyColors.black,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
              ),
            if (linkSuffixText != null)
              TextSpan(
                text: linkSuffixText,
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                      color: FroggyColors.black,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
              ),
            if (text != null)
              TextSpan(
                text: text,
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                      color: FroggyColors.black,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
              ),
          ],
        ),
      ),
    );
  }
}
