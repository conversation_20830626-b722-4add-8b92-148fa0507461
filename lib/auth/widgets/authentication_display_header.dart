import 'package:common/common.dart';
import 'package:flutter/material.dart';

class AuthenticationDisplayHeader extends StatelessWidget {
  const AuthenticationDisplayHeader({
    required this.title,
    this.subtitle,
    this.vertical = 5.0,
    this.horizontal = 20.0,
    this.subtitleWidget,
    super.key,
  });

  final double vertical;

  final double horizontal;
  final String title;
  final String? subtitle;
  final Widget? subtitleWidget;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        FroggySpacer.custom(
          height: 15,
        ),
        Padding(
          padding: EdgeInsets.symmetric(
            vertical: vertical,
            horizontal: horizontal,
          ),
          child: Text(
            title,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(
            vertical: vertical,
            horizontal: horizontal,
          ),
          child: subtitleWidget ??
              Text(
                subtitle ?? '',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                ),
              ),
        ),
      ],
    );
  }
}
