import 'dart:async';

import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:utils/utils.dart';

class AuthenticationRetrialTimerWidget extends StatefulWidget {
  const AuthenticationRetrialTimerWidget({
    required this.onTimerRestart,
    super.key,
    this.initialDuration = const Duration(minutes: 5),
    this.retriesLeft = '5',
    this.timerColor = FroggyColors.primary,
    this.timerFontSize = 14.0,
  });

  final Duration initialDuration;
  final VoidCallback onTimerRestart;
  final String retriesLeft;
  final Color timerColor;
  final double timerFontSize;

  @override
  State<AuthenticationRetrialTimerWidget> createState() =>
      _AuthenticationRetrialTimerWidgetState();
}

class _AuthenticationRetrialTimerWidgetState
    extends State<AuthenticationRetrialTimerWidget> {
  late Timer _timer;
  late int _remainingTime;
  late AppLocalizations l10n;
  bool _isCountdownEnded = false;
  bool _isCountdownRunning = false;

  @override
  void initState() {
    super.initState();
    _remainingTime = widget.initialDuration.inSeconds;
    _startTimer();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    l10n = context.l10n;
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  void _startTimer() {
    _isCountdownRunning = true;

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_remainingTime > 0) {
          _remainingTime--;
        } else {
          _timer.cancel();
          _isCountdownEnded = true;
          _isCountdownRunning = false;
        }
      });
    });
  }

  void _restartTimer() {
    _timer.cancel();
    setState(() {
      _remainingTime = widget.initialDuration.inSeconds;
      _isCountdownEnded = false;
    });
    _startTimer();
    widget.onTimerRestart();
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return seconds >= 60
        ? '${l10n.minsOrMin(minutes)} '
            '${l10n.secsOrSec(remainingSeconds)}'
        : l10n.secsOrSec(remainingSeconds);
  }

  @override
  Widget build(BuildContext context) {
    final remainingRetriesCount = widget.retriesLeft.toInt;
    final message = l10n.enterOtpPageResendOtpRetries(remainingRetriesCount);

    return AuthenticationTextButton(
      onPressed: remainingRetriesCount > 0
          ? (_isCountdownRunning ? null : _restartTimer)
          : widget.onTimerRestart,
      linkText: '${l10n.enterOtpPageResendOtpButton} ',
      linkSuffixText: message,
      linkSuffixWidget: remainingRetriesCount > 0
          ? (_isCountdownEnded
              ? null
              : Text(
                  '( ${_formatTime(_remainingTime)} )',
                  style: TextStyle(
                    fontSize: widget.timerFontSize,
                    color: widget.timerColor,
                    fontWeight: FontWeight.w600,
                  ),
                ))
          : null,
    );
  }
}
