import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:utils/utils.dart';

class AuthenticationReferralFormField extends StatelessWidget {
  const AuthenticationReferralFormField({
    required this.onReferralCodeChanged,
    this.enabled = true,
    this.errorMessage,
    super.key,
  });

  final bool enabled;
  final void Function(String value) onReferralCodeChanged;
  final String? errorMessage;

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 3),
          decoration: BoxDecoration(
            color: FroggyColors.froggyGrey5,
            borderRadius: BorderRadius.circular(25),
          ),
          child: TextField(
            keyboardType: TextInputType.text,
            onChanged: onReferralCodeChanged,
            textCapitalization: TextCapitalization.characters,
            style: const TextStyle(
              color: FroggyColors.black,
            ),
            inputFormatters: [
              FroggyTextInputUpperCaseFormatter(),
              LengthLimitingTextInputFormatter(6),
              FilteringTextInputFormatter.allow(RegExp('[a-zA-Z0-9]')),
            ],
            decoration: InputDecoration(
              enabled: enabled,
              border: InputBorder.none,
              hintText: l10n.loginPageReferralLabel,
              hintStyle: const TextStyle(
                color: Colors.black38,
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
              focusedBorder: InputBorder.none,
            ),
          ),
        ),
        if (errorMessage != null)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 3),
            child: Text(
              errorMessage ?? '',
              style: const TextStyle(
                color: FroggyColors.error,
                fontSize: 12,
              ),
            ),
          ),
      ],
    );
  }
}
