// import 'package:common/common.dart';
import 'package:constants/constants.dart';
import 'package:countries/countries.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggy_icons/froggy_icons.dart';
import 'package:froggytalk/auth/auth.dart';

class AuthPhoneNumberField extends HookWidget {
  const AuthPhoneNumberField({
    required this.onPhoneNumberChanged,
    required this.onCountryValueChanged,
    this.onShowCountries,
    this.defaultCountryCode,
    this.enabled = true,
    this.phoneNumberErrorMessage,
    this.countryErrorMessage,
    super.key,
  });

  final void Function(String value) onPhoneNumberChanged;
  final void Function(CountryModel value) onCountryValueChanged;
  final VoidCallback? onShowCountries;
  final String? phoneNumberErrorMessage;
  final String? countryErrorMessage;
  final CountryModel? defaultCountryCode;
  final bool enabled;

  @override
  Widget build(BuildContext context) {
    final phoneNumberController = useTextEditingController();
    final selectedCountryController =
        useState<CountryModel?>(defaultCountryCode);

    final showCountrySelector = useCallback(
      () {
        FroggyCountries.showPicker(
          context: context,
          onCountrySelected: (CountryModel country) {
            selectedCountryController.value = country;
          },
          forceReload: true,
        );
      },
      [],
    );

    final formattedPhoneNumberOutput = useCallback(
      () {
        return '${selectedCountryController.value?.dialingCode ?? ''} '
            '${phoneNumberController.text}';
      },
      [selectedCountryController.value, phoneNumberController.text],
    );

    useEffect(
      () {
        void updatePhoneNumber() {
          // onPhoneNumberChanged(formattedPhoneNumberOutput());
          onPhoneNumberChanged(phoneNumberController.text);
        }

        void updateCountryValue() {
          onCountryValueChanged(
            selectedCountryController.value ?? 
                (FroggyCountries.getInstance().findCountryByCountryCode('nl') ??
                 FroggyCountries.getInstance().getDefaultCountry()),
          );
        }

        // Initialize country on first load if we have one
        if (selectedCountryController.value != null) {
          updateCountryValue();
        }

        phoneNumberController.addListener(updatePhoneNumber);
        selectedCountryController.addListener(updateCountryValue);

        return () {
          phoneNumberController.removeListener(updatePhoneNumber);
          selectedCountryController.removeListener(updateCountryValue);
        };
      },
      [
        phoneNumberController.text,
        selectedCountryController.value,
        formattedPhoneNumberOutput,
        defaultCountryCode,
      ],
    );

    // Update selected country when defaultCountryCode prop changes
    useEffect(
      () {
        if (defaultCountryCode != null && 
            selectedCountryController.value != defaultCountryCode) {
          selectedCountryController.value = defaultCountryCode;
        }
        return null;
      },
      [defaultCountryCode],
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // _buildPhoneNumberInput(
        //   showCountrySelector,
        //   selectedCountryController,
        //   phoneNumberController,
        // ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 3),
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(25),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // _buildDropdown(showCountrySelector, selectedCountryController),
              _BuildChooseCountryDropdown(
                showCountrySelector: onShowCountries ?? showCountrySelector,
              ),
              const AuthenticationDivider(),
              Expanded(
                child: TextField(
                  controller: phoneNumberController,
                  keyboardType: TextInputType.phone,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(11),
                  ],
                  style: const TextStyle(
                    color: FroggyColors.black,
                    fontSize: 15,
                  ),
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    hintText: '000 000 00',
                    hintStyle: const TextStyle(color: Colors.black38),
                    focusedBorder: InputBorder.none,
                    enabled: enabled,
                  ),
                ),
              ),
            ],
          ),
        ),
        if (phoneNumberErrorMessage != null)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 3),
            child: Text(
              phoneNumberErrorMessage ?? 'Error Phone',
              style: const TextStyle(
                color: FroggyColors.error,
                fontSize: 12,
              ),
            ),
          ),
        if (countryErrorMessage != null)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 3),
            child: Text(
              countryErrorMessage ?? 'Error Country',
              style: const TextStyle(
                color: FroggyColors.error,
                fontSize: 12,
              ),
            ),
          ),
      ],
    );
  }
}

class _BuildChooseCountryDropdown extends StatelessWidget {
  const _BuildChooseCountryDropdown({
    this.showCountrySelector,
  });

  final VoidCallback? showCountrySelector;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SendOtpFormBloc, SendOtpFormState>(
      builder: (context, state) {
        return InkWell(
          onTap: showCountrySelector,
          child: SizedBox(
            width: 85,
            height: 25,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Spacer(),
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 2),
                  child: FroggyIconsList.arrowDownOutline.toWidget(
                    width: 7,
                    height: 7,
                    color: FroggyColors.froggyGrey2,
                  ),
                ),
                const Spacer(),
                FroggyCountries.showCountryFlagByCountryCode(
                  countryCode: state.selectedCountry?.code ?? 'nl',
                  width: 30,
                  margin: 0,
                ),
                const Spacer(),
                Text(
                  state.selectedCountry?.dialingCode ?? '+31',
                  style: const TextStyle(
                    color: Colors.black,
                    fontSize: 15,
                  ),
                ),
                const Spacer(),
              ],
            ),
          ),
        );
      },
    );
  }
}
