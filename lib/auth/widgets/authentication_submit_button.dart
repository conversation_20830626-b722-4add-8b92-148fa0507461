import 'package:constants/constants.dart';
import 'package:flutter/material.dart';

class AuthenticationButton extends StatelessWidget {
  const AuthenticationButton({
    this.onPressed,
    this.inProgress = false,
    this.text = 'Submit',
    super.key,
  });

  final VoidCallback? onPressed;
  final bool inProgress;
  final String text;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 30),
      padding: const EdgeInsets.symmetric(horizontal: 20),
      width: double.infinity,
      height: 48,
      child: ElevatedButton(
        onPressed: inProgress ? null : onPressed,
        style: ElevatedButton.styleFrom(
          side: BorderSide.none,
          fixedSize: const Size.fromHeight(48),
        ),
        child: Visibility(
          visible: !inProgress,
          replacement: const SizedBox(
            height: 25,
            width: 25,
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(FroggyColors.white),
            ),
          ),
          child: Text(
            text,
            style: const TextStyle(
              color: FroggyColors.white,
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }
}
