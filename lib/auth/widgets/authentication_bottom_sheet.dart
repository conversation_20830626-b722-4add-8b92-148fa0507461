import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggy_icons/froggy_icons.dart';
import 'package:froggytalk/l10n/l10n.dart';

class AuthenticationBottomSheet extends HookWidget {
  const AuthenticationBottomSheet({super.key, this.message, this.icon});

  final String? message;
  final Widget? icon;

  static void showErrorAlertDialog({
    String? message,
    BuildContext? context,
  }) {
    final iContext = context!;

    showGeneralDialog(
      context: iContext,
      barrierDismissible: true,
      barrierLabel: MaterialLocalizations.of(iContext).modalBarrierDismissLabel,
      barrierColor: Colors.black54,
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (
        BuildContext buildContext,
        Animation<double> animation,
        Animation<double> secondaryAnimation,
      ) {
        return Center(
          child: AuthenticationBottomSheet(
            icon: FroggyIconsList.authErrorOutline.toWidget(),
            message: message,
          ),
        );
      },
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return ScaleTransition(
          scale: CurvedAnimation(
            parent: animation,
            curve: Curves.easeInOut,
          ),
          child: child,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;

    return Dialog(
      backgroundColor: FroggyColors.froggyCream,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      child: Padding(
        padding: const EdgeInsets.all(25),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            InkWell(
              onTap: () {
                Navigator.of(context).pop();
              },
              child: icon ?? const SizedBox.shrink(),
            ),
            const SizedBox(height: 10),
            Text(
              message ?? l10n.loginPageErrorMessage,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
