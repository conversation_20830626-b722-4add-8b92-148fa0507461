import 'package:common/common.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/l10n/l10n.dart';

class AuthenticationReferralCheckboxWidget extends HookWidget {
  const AuthenticationReferralCheckboxWidget({
    required this.onReferralCheckboxChanged,
    required this.value,
    super.key,
  });

  final void Function(bool?) onReferralCheckboxChanged;
  final bool value;

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return FroggyCheckbox(
      isChecked: value,
      onCheckboxChanged: onReferralCheckboxChanged,
      label: l10n.loginPageCheckboxLabel,
    );
  }
}
