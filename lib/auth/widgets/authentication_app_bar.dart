import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:navigation/navigation.dart';

class AuthenticationAppBar extends HookWidget implements PreferredSizeWidget {
  const AuthenticationAppBar({
    this.title = 'Getting Started',
    this.subtitle = 'Step 1 of 3',
    super.key,
  });

  final String title;
  final String subtitle;

  @override
  Widget build(BuildContext context) {
    final canNavigateBack = useMemoized(
      FroggyRouter.canPop,
    );

    return AppBar(
      title: Text(title),
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(0),
        child: Text(
          subtitle,
        ),
      ),
      centerTitle: true,
      leading: canNavigateBack != null && canNavigateBack
          ? const BackButton(
              style: ButtonStyle(
                splashFactory: NoSplash.splashFactory,
              ),
            )
          : null,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(60);
}
