import 'package:flutter/material.dart';
import 'package:froggy_icons/froggy_icons.dart';

class AuthenticationDivider extends StatelessWidget {
  const AuthenticationDivider({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(4),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      child: FroggyIconsList.divider.toWidget(
        color: Colors.black,
        width: 25,
        height: 25,
      ),
    );
  }
}
