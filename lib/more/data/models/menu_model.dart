import 'package:constants/constants.dart';
import 'package:flutter/material.dart';

class MorePageMenu {
  MorePageMenu({
    required this.icon,
    required this.label,
    this.suffix,
    this.onTap,
    this.isEnabled = true,
    this.backgroundColor,
    this.iconColor = FroggyColors.froggyGrey2,
    this.labelFontColor = FroggyColors.black,
    this.isIconImage = false,
    this.suffixWidget,
    this.iconSize,
    this.labelFontSize = 16.0,
    this.visible = true,
  });

  final String icon;
  final Color iconColor;
  final bool isIconImage;
  final Size? iconSize;
  final String label;
  final Color labelFontColor;
  final double labelFontSize;
  final VoidCallback? onTap;
  final bool isEnabled;
  final Decoration? backgroundColor;
  final String? suffix;
  final Widget? suffixWidget;
  final bool visible;
}
