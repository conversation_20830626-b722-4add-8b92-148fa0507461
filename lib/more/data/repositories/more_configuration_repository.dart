import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:froggytalk/call_rates/call_rates.dart';
import 'package:froggytalk/docs/views/privacy_policy_view.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:froggytalk/language/language.dart';
import 'package:froggytalk/more/more.dart';
import 'package:froggytalk/profile/profile.dart';
import 'package:froggytalk/radio_stations/views/radio_stations.dart';
import 'package:froggytalk/referral/referral.dart';
import 'package:froggytalk/voucher/voucher.dart';
import 'package:navigation/navigation.dart';

List<MorePageMenu> useMoreMenuItems() {
  final context = useContext();
  final l10n = useLocale();
  final user = useAuthUser();
  final languageCode = useLanguage()?.code;
  final referralAmount =
      useSettings().getSetting<int>('referral_amount') ?? 250;
  final toggleLeaderboard =
      useSettings().getSetting<bool>('hide_leaderboard_promo') ?? true;

  final menu = [
    {
      'icon': 'profile',
      'label': l10n.morePageProfileMenuText,
      'callback': () => Navigator.of(context).push(EditProfilePage.route()),
      'enabled': true,
    },
    {
      'icon': 'load_voucher',
      'label': l10n.morePageLoadVoucherMenuText,
      'callback': () {
        showDialog<void>(
          context: FroggyRouter.context!,
          builder: (BuildContext context) {
            return const LoadVoucherCodeModal();
          },
        );
      },
      'enabled': true,
    },
    {
      'icon': 'rates_outline',
      'label': l10n.morePageCallRatesMenuText,
      'callback': () => FroggyRouter.push(
            const Scaffold(
              appBar: CallRatesPageAppBar(),
              body: CallRatesPage(),
            ),
          ),
      'enabled': true,
    },
    {
      'icon': 'assets/images/3d-gift-box-wrapped-golden-ribbon 1.png',
      'isIconImage': true,
      'iconColor': FroggyColors.froggyBlack,
      'visible': !toggleLeaderboard,
      'iconSize': const Size(30, 30),
      'label': l10n.cashPrizeAmount(
        user?.country?.attributes?.currency?.symbol ?? r'$',
        referralAmount.toString(),
      ),
      'labelFontSize': 15.0,
      'callback': () => FroggyRouter.push(
            LeaderboardPage(
              userId: user?.id ?? '',
              phoneNumber: user?.phoneNumber ?? '',
              languageCode: languageCode ?? 'en',
              environment: AppInstance.getInstance().environmentType,
            ),
          ),
      'enabled': true,
      'textColor': FroggyColors.froggyBlack,
      'trailingWidget': FilledButton(
        style: FilledButton.styleFrom(
          backgroundColor: FroggyColors.froggyCream,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(30),
          ),
        ),
        onPressed: () => FroggyRouter.push(
          LeaderboardPage(
            userId: user?.id ?? '',
            phoneNumber: user?.phoneNumber ?? '',
            languageCode: languageCode ?? 'en',
            environment: AppInstance.getInstance().environmentType,
          ),
        ),
        child: Text(
          l10n.getNow,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: FroggyColors.black,
          ),
        ),
      ),
      'backgroundColor': const BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/images/image-26.png'),
          fit: BoxFit.cover,
        ),
      ),
    },
    {
      'icon': 'radio',
      'label': l10n.morePageRadioMenuText,
      'callback': () => FroggyRouter.push(
            // const RadioPage(
            //     // showComingSoon: true,
            //     ),
            // const RadioPlayerView(),
            // const RadioPlayerListView(),
            const RadioStationsPage(),
          ),
      'enabled': true,
    },
    {
      'icon': 'language',
      'label': l10n.morePageLanguageMenuText,
      'callback': () => FroggyRouter.push(const LanguagePage()),
      'enabled': true,
    },
    {
      'icon': 'help_center',
      'label': l10n.morePageHelpCenterMenuText,
      'callback': () => FroggyRouter.push(const HelpCenterPage()),
      'enabled': true,
    },
    {
      'icon': 'privacy_policy',
      'label': l10n.onboardingPageFooterPrivacy,
      // 'callback': () => PrivacyPolicyView.showModal(context),
      'callback': () => FroggyRouter.push(
            const PrivacyPolicyView(
              isDialog: false,
            ),
          ),
      'enabled': true,
    },
    {
      'icon': 'app_version',
      'label': l10n.morePageAppVersionMenuText,
      'suffix': '${AppInstance.getInstance().version}+'
          '${AppInstance.getInstance().buildNumber}',
      'callback': () {},
      'enabled': false,
    },
  ];

  // final envType = AppInstance.getInstance().environmentType;
  // if (envType.isNotProduction) {
  //   final devPrefix = envType.isDevelopment ? '[DEV]' : '[STG]';
  //   // const devPrefix = '[PRD]';

  //   menu.add(
  //     {
  //       'icon': 'app_version',
  //       'label': '$devPrefix Switch To Sad App Icon',
  //       'callback': () {
  //         // NativeIconChanger.changeIcon('')
  //         FroggyDynamicIcons().changeToSadIcon();
  //       },
  //       'enabled': true,
  //     },
  //   );
  // }

  return menu
      .map(
        (e) => MorePageMenu(
          icon: e['icon']! as String,
          isIconImage: e['isIconImage'] as bool? ?? false,
          suffixWidget: e['trailingWidget'] as Widget?,
          label: e['label']! as String,
          onTap: e['callback']! as VoidCallback,
          suffix: e['suffix'] as String?,
          isEnabled: e['enabled'] as bool? ?? true,
          backgroundColor: e['backgroundColor'] as Decoration?,
          iconColor: e['iconColor'] as Color? ?? FroggyColors.froggyGrey2,
          labelFontColor: e['textColor'] as Color? ?? FroggyColors.black,
          iconSize: e['iconSize'] as Size?,
          labelFontSize: (e['labelFontSize'] as double?) ?? 16.0,
          visible: e['visible'] as bool? ?? true,
        ),
      )
      .toList();
}
