import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggy_icons/froggy_icons.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/app/data/services/event_tracker.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:froggytalk/call_rates/call_rates.dart';
import 'package:froggytalk/contacts/contacts.dart';
import 'package:froggytalk/dialer/dialer.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:froggytalk/language/language.dart';
import 'package:froggytalk/notifications/notifications.dart';
import 'package:froggytalk/profile/profile.dart';
import 'package:froggytalk/referral/referral.dart';
import 'package:froggytalk/voucher/voucher.dart';
import 'package:mixpanel_flutter/mixpanel_flutter.dart';
import 'package:navigation/navigation.dart';
import 'package:skeletonizer/skeletonizer.dart';

class LogoutButton extends HookWidget {
  const LogoutButton({super.key});

  @override
  Widget build(BuildContext context) {
    final authenticationBloc = context.read<AuthenticationBloc>();
    final favContactsBloc = context.read<FavouriteContactsBloc>();
    final recentCallsBloc = context.read<RecentCallsBloc>();
    final dialerBloc = context.read<DialerBloc>();
    final referralBloc = context.read<ReferralBloc>();
    final contactBloc = context.read<ContactListBloc>();
    final keypadBloc = context.read<KeypadBloc>();
    final notificationBloc = context.read<NotificationsBloc>();
    final callRatesBloc = context.read<RatesBloc>();
    final languageBloc = context.read<LanguageBloc>();
    final profileBloc = context.read<EditProfileBloc>();
    final deleteProfileBloc = context.read<DeleteProfileBloc>();
    final voucherBloc = context.read<LoadVoucherBloc>();
    final l10n = context.l10n;

    final onLogoutButtonPressed = useCallback(() {
      // authenticationBloc.add(const AuthenticationEvent.signedOut());
      context.read<Mixpanel>()
        ..track('Logout')
        ..reset();

        context.read<EventTrackerService>().onUserLogsOut();
      authenticationBloc.add(const AuthenticationEvent.destroyedToken());
    });

    return BlocConsumer<AuthenticationBloc, AuthenticationState>(
      listener: (BuildContext context, state) {
        if (state.isUnauthenticated) {
          dialerBloc.add(const DialerEvent.reset());
          authenticationBloc.add(const AuthenticationEvent.reset());
          favContactsBloc.add(const FavouriteContactsEvent.reset());
          recentCallsBloc.add(const RecentCallsEvent.reset());
          referralBloc.add(const ReferralEvent.reset());
          contactBloc.add(const ContactListEvent.reset());
          keypadBloc.add(const KeypadEvent.reset());
          notificationBloc.add(const NotificationsEvent.reset());
          callRatesBloc.add(const RatesEvent.reset());
          languageBloc.add(const LanguageEvent.reset());
          profileBloc.add(const EditProfileEvent.reset());
          deleteProfileBloc.add(const DeleteProfileEvent.reset());
          voucherBloc.add(const LoadVoucherEvent.reset());

          FroggyToast.showSuccessToast(
            context,
            state.message ?? 'Logout successful',
          );

          FroggyRouter.pushAndRemoveUntil(const SendOtpPage());
        }
      },
      builder: (context, state) {
        final isSigningOut = state.isLoading;

        return Skeletonizer(
          enabled: isSigningOut,
          child: Container(
            margin: const EdgeInsets.only(top: 30, bottom: 20),
            width: double.infinity,
            height: 48,
            child: OutlinedButton.icon(
              onPressed: !isSigningOut ? onLogoutButtonPressed : null,
              icon: isSigningOut ? null : FroggyIconsList.logout.toWidget(),
              label: Text(
                l10n.morePageLogoutMenuText,
                style: const TextStyle(
                  color: FroggyColors.primary,
                ),
              ),
              style: OutlinedButton.styleFrom(
                side: BorderSide.none,
              ),
            ),
          ),
        );
      },
    );
  }
}
