import 'package:common/common.dart';
import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggy_icons/froggy_icons.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/auth/auth.dart';
import 'package:froggytalk/buy_credit/buy_credit.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:froggytalk/more/more.dart';
import 'package:froggytalk/referral/referral.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:utils/utils.dart';

class MorePage extends HookWidget {
  MorePage({super.key});

  static Route<Object?> route() {
    return MaterialPageRoute<Object?>(
      builder: (_) => MorePage(),
    );
  }

  static String routeName = '/more';

  final GlobalKey<SliverAnimatedListState> _listKey =
      GlobalKey<SliverAnimatedListState>();

  @override
  Widget build(BuildContext context) {
    final l10n = useLocale();
    final authUser = useAuthUser();
    final currencySymbol =
        authUser?.country?.attributes?.currency?.code ?? 'USD';
    final hasInternet = useHasConnectivity();
    final moreMenuItems = useMoreMenuItems();

    final onFreeCreditButtonPressed =
        useCallback(() => Navigator.push(context, FreeCreditPage.route()));

    final onBuyCreditButtonPressed = useCallback(() {
      BuyCreditEvents.clickedOnBuyCreditButton();

      return Navigator.push(
        context,
        BuyCreditPage.route(bloc: context.read<CheckoutBloc>()),
      );
    });

    final isIOSEnabled =
        useSettings().getSetting<bool>('ios_hide_buy_credit_button') ?? true;

    return Container(
      key: const Key('more_page'),
      child: CustomScrollView(
        slivers: [
          SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 6),
              child: BlocBuilder<AuthenticationBloc, AuthenticationState>(
                builder: (context, state) {
                  return Skeletonizer(
                    enabled: state.isLoading,
                    enableSwitchAnimation: true,
                    child: FroggyAccountBalance(
                      balanceTitleText: l10n.accountBalanceCardTitle,
                      buyCreditText: l10n.buyCreditAppBarTitle,
                      balance: authUser?.accountBalance ?? '0',
                      onBuyCredit:
                          hasInternet ? onBuyCreditButtonPressed : null,
                      currencyCode: currencySymbol,
                      hideBuyCreditButton: isIOS() && isIOSEnabled,
                    ),
                  );
                },
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 6),
              child: ReferralForFreeCredit(
                onFreeCreditButtonPressed: onFreeCreditButtonPressed,
              ),
            ),
          ),
          SliverToBoxAdapter(child: FroggySpacer.y16()),
          SliverAnimatedList(
            key: _listKey,
            initialItemCount: moreMenuItems.length,
            itemBuilder: (context, index, animation) {
              final label = moreMenuItems[index].label;
              final icon = moreMenuItems[index].icon.toLowerCase();
              final onTap = moreMenuItems[index].onTap;
              final suffix = moreMenuItems[index].suffix;
              final isEnabled = moreMenuItems[index].isEnabled;
              final backgroundDecoration = moreMenuItems[index].backgroundColor;
              final iconColor = moreMenuItems[index].iconColor;
              final textColor = moreMenuItems[index].labelFontColor;
              final isIconImage = moreMenuItems[index].isIconImage;
              final trailingWidget = moreMenuItems[index].suffixWidget;
              final iconSize = moreMenuItems[index].iconSize;
              final labelFontSize = moreMenuItems[index].labelFontSize;
              final isVisible = moreMenuItems[index].visible;

              if (!isVisible) {
                return const SizedBox.shrink();
              }

              return SizeTransition(
                sizeFactor: animation,
                child: Column(
                  children: [
                    Container(
                      padding: !isIconImage
                          ? null
                          : const EdgeInsets.symmetric(
                              horizontal: 15,
                            ),
                      margin: isIconImage
                          ? null
                          : const EdgeInsets.symmetric(
                              horizontal: 15,
                            ),
                      decoration: backgroundDecoration,
                      child: ListTile(
                        title: Text(
                          label,
                          style: TextStyle(
                            color: textColor,
                            fontSize: labelFontSize,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        trailing: trailingWidget ??
                            Text(
                              suffix ?? '',
                              style: const TextStyle(
                                color: FroggyColors.black,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                        leading: isIconImage
                            ? Image.asset(
                                icon,
                                width: iconSize?.width ?? 20,
                                height: iconSize?.height ?? 20,
                              )
                            : FroggyIcon(
                                'ic_$icon',
                                width: iconSize?.width ?? 20,
                                height: iconSize?.height ?? 20,
                                color: iconColor,
                              ),
                        onTap: onTap,
                        enabled: isEnabled,
                      ),
                    ),
                    FroggyIconsList.dividerHorizontal.toWidget(),
                  ],
                ),
              );
            },
          ),
          SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 6),
              child: const LogoutButton(),
            ),
          ),
        ],
      ),
    );
  }
}
