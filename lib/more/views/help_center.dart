import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggy_icons/froggy_icons.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/app/data/services/event_tracker.dart';
import 'package:froggytalk/docs/docs.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:froggytalk/live_agent/live_agent.dart';
import 'package:url_launcher/url_launcher.dart';

class HelpCenterPage extends HookWidget {
  const HelpCenterPage({super.key});

  static Route<Object?> route() {
    return MaterialPageRoute<Object?>(
      builder: (_) => const HelpCenterPage(),
    );
  }

  static String routeName = '/help_center';

  Future<void> _launchWhatsApp(
    BuildContext context, {
    required String phoneNumber,
  }) async {
    final url = 'https://wa.me/$phoneNumber';
    final uri = Uri.parse(url);
    try {
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      } else {
        if (!context.mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(context.l10n.unknownErrorText),
          ),
        );
      }
    } catch (e) {
      if (!context.mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${context.l10n.unknownErrorText}: $e'),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final items = useMemoized(
      () => [
        // {
        //   'label': 'Give a complain or Suggestion',
        //   'onPressed': () {},
        // },
        {
          'label': l10n.helpCenterPageMenu_2,
          'icon': 'live_chat',
          'onPressed': () {
            Navigator.of(context).push(ChatWithLiveAgentPage.route());
            context.read<EventTrackerService>().logEvent(
                  schema: 'Contact_Support',
                  description: 'user chats with support agent',
                );
          },
        },
        {
          'label': l10n.helpCenterPageMenu_4,
          'icon': 'whatsapp',
          'onPressed': () {
            _launchWhatsApp(
              context,
              phoneNumber: '31657848469',
            );

            context.read<EventTrackerService>().logEvent(
                  schema: 'Contact_Support',
                  description: 'user chats with support agent via whatsapp',
                );
          },
        },
        {
          'label': l10n.helpCenterPageMenu_3,
          'icon': 'faq',
          'onPressed': () {
            Navigator.of(context).push(FaqsView.route());
            context.read<EventTrackerService>().logEvent(
                  schema: 'FAQ_viewed',
                  description: 'user viewed faq page',
                );
          },
        },
      ],
    );

    return Scaffold(
      appBar: FroggyAppBar(
        label: l10n.helpCenterAppbarTitle,
        labelFontSize: 20,
      ),
      body: ListView(
        children: [
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            separatorBuilder: (_, __) => const Divider(),
            itemCount: items.length,
            itemBuilder: (context, index) {
              final item = items[index] as Map<String, dynamic>;
              final label = item['label'] as String;
              final onPressed = item['onPressed'] as VoidCallback?;
              final icon = item['icon'] as String;

              return ListTile(
                title: Text(
                  label,
                  style: const TextStyle(
                    fontSize: 16,
                    color: FroggyColors.black,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                leading: FroggyIcon(
                  'ic_$icon',
                  width: 20,
                  height: 20,
                  color: FroggyColors.froggyGrey2,
                ),
                onTap: onPressed,
              );
            },
          ),
          const Divider(),
        ],
      ),
    );
  }
}
