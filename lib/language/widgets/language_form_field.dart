import 'package:constants/constants.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/language/language.dart';

class LanguageFormField extends HookWidget {
  const LanguageFormField({
    required this.onLanguageSelected,
    this.defaultLanguage,
    this.message,
    super.key,
  });

  final void Function(Language) onLanguageSelected;
  final Language? defaultLanguage;
  final String? message;

  // Widget _buildDropDownList({
  //   required void Function(Language) onLanguageSelected,
  //   Language? selectedLanguage,
  // }) {
  //   final languages = LanguageRepository.getSupportedLanguages();
  //   return ListView.separated(
  //     itemCount: languages.length,
  //     itemBuilder: (context, index) {
  //       final language = languages[index];

  //       return ListTile(
  //         title: Padding(
  //           padding: const EdgeInsets.all(5),
  //           child: Text(
  //             language.name,
  //             style: const TextStyle(
  //               fontSize: 14,
  //               fontWeight: FontWeight.w600,
  //               color: FroggyColors.black,
  //             ),
  //           ),
  //         ),
  //         onTap: () => onLanguageSelected(language),
  //       );
  //     },
  //     separatorBuilder: (BuildContext context, int index) {
  //       return const Divider();
  //     },
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    // final countryController = useTextEditingController();
    // final selectedLanguage = useState<Language?>(null);
    final items = useMemoized<List<Language>>(
      LanguageRepository.getLanguages,
      [],
    );

    final selectedValue = useState<Language>(
      defaultLanguage ?? LanguageRepository.getLanguages()[0],
    );

    // final showDropdown = useCallback(
    //   () {
    //     showModalBottomSheet<void>(
    //       context: context,
    //       builder: (context) {
    //         return SizedBox(
    //           height: 130,
    //           child: _buildDropDownList(
    //             onLanguageSelected: (language) {
    //               countryController.text = language.name;
    //               selectedLanguage.value = language;
    //               onLanguageSelected.call(language);
    //               Navigator.pop(context);
    //             },
    //             selectedLanguage: selectedLanguage.value,
    //           ),
    //         );
    //       },
    //     );
    //   },
    //   [],
    // );

    return Container(
      height: 50,
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: DropdownButtonHideUnderline(
        child: DropdownButton2<Language>(
          isExpanded: true,
          hint: const Text(
            'English',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: FroggyColors.froggyGrey2,
            ),
            overflow: TextOverflow.ellipsis,
          ),
          items: items
              .map(
                (Language item) => DropdownMenuItem<Language>(
                  value: item,
                  child: Padding(
                    padding: const EdgeInsets.all(10),
                    child: Text(
                      item.shortName ?? '',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: FroggyColors.black,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              )
              .toList(),
          value: selectedValue.value,
          onChanged: (value) {
            if (value != null) {
              onLanguageSelected.call(value);
              selectedValue.value = value;
            }
          },
          buttonStyleData: ButtonStyleData(
            height: 150,
            // width: 100,
            padding: const EdgeInsets.only(left: 6, right: 2),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(250),
              border: Border.all(
                color: FroggyColors.froggyGrey5,
              ),
              color: FroggyColors.froggyGrey5,
            ),
            elevation: 0,
          ),
          iconStyleData: const IconStyleData(
            icon: Padding(
              padding: EdgeInsets.only(right: 10),
              child: Icon(
                Icons.keyboard_arrow_down_outlined,
              ),
            ),
            openMenuIcon: Padding(
              padding: EdgeInsets.only(right: 10),
              child: Icon(
                Icons.close,
                size: 18,
              ),
            ),
            iconEnabledColor: FroggyColors.froggyGrey2,
            iconDisabledColor: Colors.grey,
          ),
          dropdownStyleData: DropdownStyleData(
            maxHeight: 300,
            width: 380,
            useRootNavigator: true,
            elevation: 0,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              color: FroggyColors.froggyCream,
            ),
            // offset: const Offset(-20, 0),
            // scrollbarTheme: ScrollbarThemeData(
            //   radius: Radius.zero,
            //   thickness: WidgetStateProperty.all(6),
            //   thumbVisibility: WidgetStateProperty.all(true),
            // ),
          ),
          menuItemStyleData: MenuItemStyleData(
            height: 40,
            padding: const EdgeInsets.only(left: 14, right: 14),
            selectedMenuItemBuilder: (context, child) {
              return Container(
                decoration: BoxDecoration(
                  color: FroggyColors.froggyGrey5,
                  borderRadius: BorderRadius.circular(5),
                ),
                child: child,
              );
            },
          ),
        ),
      ),
    );

    //   return Container(
    //     margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
    //     padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 3),
    //     decoration: BoxDecoration(
    //       color: FroggyColors.froggyGrey5,
    //       borderRadius: BorderRadius.circular(25),
    //     ),
    //     child: TextField(
    //       controller: countryController,
    //       keyboardType: TextInputType.phone,
    //       enabled: true,
    //       readOnly: true,
    //       onTap: showDropdown,
    //       style: const TextStyle(
    //         color: FroggyColors.black,
    //       ),
    //       decoration: InputDecoration(
    //         border: InputBorder.none,
    //         hintText: 'English',
    //         hintStyle: const TextStyle(color: Colors.black38),
    //         focusedBorder: InputBorder.none,
    //         suffixIconConstraints: const BoxConstraints(
    //           maxHeight: 20,
    //           maxWidth: 20,
    //         ),
    //         suffixIcon: FroggyIconsList.arrowDown.toWidget(),
    //       ),
    //     ),
    //   );
    // }
  }
}
