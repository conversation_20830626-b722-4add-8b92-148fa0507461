import 'package:common/common.dart';
import 'package:constants/constants.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:froggytalk/language/language.dart';
import 'package:navigation/navigation.dart';

/// A modal bottom sheet that allows users to select their preferred language.
///
/// This widget displays a list of available languages and allows the user to
/// search and select their preferred language. It's designed to work with
/// a language BLoC for state management.
class SelectPreferredLanguageModal extends HookWidget {
  /// Creates a modal for selecting the preferred language.
  ///
  /// The [onLanguageSelected] callback is triggered
  /// when a language is selected.
  const SelectPreferredLanguageModal({
    super.key,
    this.onLanguageSelected,
  });

  /// Callback function triggered when a language is selected.
  final void Function(String languageCode)? onLanguageSelected;

  /// Shows the language selection modal as a bottom sheet.
  static Future<String?> show(BuildContext context) async {
    return showDialog<String>(
      context: context,
      // isScrollControlled: true,
      // backgroundColor: Colors.transparent,
      barrierDismissible: false,
      builder: (context) => const SelectPreferredLanguageModal(),
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final bloc = context.read<LanguageBloc>();
    final savedLanguage = context.select<LanguageBloc, Language?>(
      (blocI) => blocI.state.savedLanguage,
    );
    final selectedLanguage = context.select<LanguageBloc, Language?>(
      (blocI) => blocI.state.selectedLanguage,
    );
    final searchController = useTextEditingController(
      text: savedLanguage?.shortName ?? 'English',
    );

    final onLanguageSelected = useCallback<void Function(Language)>(
      (p0) {
        bloc.add(
          LanguageEvent.updatedLanguage(
            value: p0,
          ),
        );
      },
      [],
    );

    final onLanguageSaved = useCallback<void Function()>(
      () {
        bloc.add(
          const LanguageEvent.saved(),
        );

        LanguageRepository.clearOldSavedLang();
      },
      [],
    );

    const radius = 35.0;

    return Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(radius),
      ),
      child: BlocBuilder<LanguageBloc, LanguageState>(
        builder: (context, state) {
          final mediaHeight = MediaQuery.of(context).size.height;
          final responsiveHeight = mediaHeight <= 800 ? 0.82 : 0.72;

          return ClipRRect(
            borderRadius: BorderRadius.circular(radius),
            child: Container(
              height: mediaHeight * responsiveHeight,
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(radius)),
              ),
              child: Center(
                child: SingleChildScrollView(
                  child: Column(
                    // crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Modal handle bar
                      Center(
                        child: Container(
                          margin: const EdgeInsets.only(top: 8),
                          width: 40,
                          height: 4,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade300,
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                      ),

                      // Header
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: Text(
                          // Assumes you have this in your l10n
                          l10n.selectPreferredLanguage,
                          style: Theme.of(context)
                              .textTheme
                              .headlineSmall
                              ?.copyWith(
                                fontSize: 15,
                                fontWeight: FontWeight.w600,
                              ),
                          textAlign: TextAlign.center,
                        ),
                      ),

                      // Search bar
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 24),
                        child: TextField(
                          controller: searchController,
                          readOnly: true,
                          decoration: InputDecoration(
                            // Assumes you have this in your l10n
                            hintText: 'l10n.searchLanguage',
                            prefix: FroggySpacer.x16(),
                            suffixIcon: Transform.rotate(
                              angle: 3.14159 / 2, // 90 degrees clockwise
                              child: const Icon(Icons.chevron_right),
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                            contentPadding:
                                const EdgeInsets.symmetric(vertical: 12),
                          ),
                        ),
                      ),

                      const SizedBox(height: 5),

                      // Language list
                      Card(
                        elevation: 5,
                        margin: const EdgeInsets.symmetric(horizontal: 24),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(25),
                        ),
                        color: Colors.white,
                        child: state.isLoading ?? false
                            ? const Center(child: CircularProgressIndicator())
                            : ListView.separated(
                                shrinkWrap: true,
                                physics: const ClampingScrollPhysics(),
                                padding: const EdgeInsets.only(top: 8),
                                separatorBuilder: (context, index) => Divider(
                                  height: 0.4,
                                  color: FroggyColors.grey.withAlpha(100),
                                ),
                                itemCount: state.languages.length,
                                itemBuilder: (context, index) {
                                  final language = state.languages[index];
                                  final isSelected = language.code ==
                                      (selectedLanguage?.code ??
                                          savedLanguage?.code);

                                  return ListTile(
                                    title: Text(language.shortName ?? ''),
                                    dense: true,
                                    trailing: isSelected
                                        ? Icon(
                                            Icons.check_circle,
                                            color: Theme.of(context)
                                                .colorScheme
                                                .primary,
                                          )
                                        : null,
                                    onTap: () => onLanguageSelected(language),
                                  );
                                },
                              ),
                      ),

                      const SizedBox(
                        height: 15,
                      ),

                      // Confirm button
                      Builder(
                        builder: (context) {
                          final width =
                              MediaQuery.of(context).size.width * 0.35;
                          final borderRadius = BorderRadius.circular(100);

                          return Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                width: width,
                                child: ElevatedButton(
                                  onPressed: () {
                                    onLanguageSaved();
                                    Navigator.of(context).pop();
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor:
                                        Theme.of(context).colorScheme.secondary,
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 16,
                                      horizontal: 20,
                                    ),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: borderRadius,
                                    ),
                                  ),
                                  child: Text(
                                    l10n.permissionButtonSkip,
                                    style: const TextStyle(
                                      fontSize: 15,
                                      fontWeight: FontWeight.w500,
                                      color: FroggyColors.black,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 10),
                              SizedBox(
                                width: width,
                                child: ElevatedButton(
                                  onPressed: (selectedLanguage == null &&
                                          savedLanguage == null)
                                      ? null
                                      : () {
                                          // If selectedLanguage is null, it
                                          // implies the user is confirming the
                                          // savedLanguage. The onLanguageSaved
                                          // method, when called, will trigger
                                          // the BLoC. The BLoC should handle
                                          // saving state.selectedLanguage if
                                          // not null, or state.savedLanguage if
                                          // state.selectedLanguage is null.
                                          LanguageRepository
                                              .clearOldSavedLang();
                                          onLanguageSaved();
                                          Navigator.of(context).pop();
                                        },
                                  style: ElevatedButton.styleFrom(
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 16,
                                      horizontal: 20,
                                    ),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: borderRadius,
                                    ),
                                  ),
                                  child: Text(l10n.confirm),
                                ),
                              ),
                            ],
                          );
                        },
                      ),

                      const SizedBox(height: 15),

                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 24),
                        child: Text.rich(
                          TextSpan(
                            text: l10n.languageSelectionDisclaimer,
                            style: const TextStyle(
                              fontSize: 12,
                              color: FroggyColors.grey,
                            ),
                            children: [
                              TextSpan(
                                text: ' ${l10n.moreNavigationBarText}',
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: FroggyColors.primary,
                                  fontWeight: FontWeight.w600,
                                ),
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () {
                                    // Handle link tap
                                    FroggyRouter.pop();
                                    FroggyRouter.push(
                                      const AppDashboardPage(
                                        initialNavbarDestination: 3,
                                      ),
                                    );
                                  },
                              ),
                            ],
                          ),
                          style: Theme.of(context).textTheme.bodyMedium,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
