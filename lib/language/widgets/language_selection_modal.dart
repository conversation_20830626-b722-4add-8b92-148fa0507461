import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:froggytalk/language/language.dart';
import 'package:utils/utils.dart';

/// A modal dialog that allows users to select their preferred language
///
/// This widget displays available languages and handles selection through
/// the [LanguageBloc].
class LanguageSelectionModal extends HookWidget {
  /// Creates a language selection modal
  const LanguageSelectionModal({super.key});

  /// Shows the language selection modal as a dialog
  static Future<void> show(BuildContext context) {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) => const LanguageSelectionModal(),
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;

    // Use cache to avoid rebuilding list unnecessarily
    final cachedLanguagesList = useMemoized(
      LanguageRepository.getLanguages,
      const [],
    );

    // final bloc = context.read<LanguageBloc>();
    // final languageSelected = useState<Language?>(null);
    // final isSaved = useState<bool?>(null);

    // final onLanguageSelected = useCallback<void Function(Language)>(
    //   (p0) {
    //     languageSelected.value = p0;
    //   },
    //   [],
    // );

    return BlocConsumer<LanguageBloc, LanguageState>(
      listener: (context, state) {
        // // Only close modal if language was saved successfully
        // if (state.savedLanguage != null && state.isLoading == false) {
        //   Navigator.of(context).pop();
        // }
      },
      builder: (context, state) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  // Use string directly as fallback for missing translation
                  // l10n.chooseLanguage,
                  'Select your preferred language',
                  style: Theme.of(context).textTheme.titleLarge,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 5),
                ConstrainedBox(
                  constraints: const BoxConstraints(maxHeight: 500),
                  child: state.isLoading ?? false
                      ? const Center(child: CircularProgressIndicator())
                      : ListView.separated(
                          shrinkWrap: true,
                          itemCount: cachedLanguagesList.length,
                          separatorBuilder: (context, index) => const Divider(),
                          itemBuilder: (context, index) {
                            final language = cachedLanguagesList[index];
                            final isSelected =
                                state.selectedLanguage?.code == language.code;

                            return ListTile(
                              title: Text(language.shortName ?? ''),
                              // titleTextStyle: const TextStyle(
                              //   fontSize: 5,
                              // ),
                              // subtitleTextStyle: const TextStyle(
                              //   fontSize: 5,
                              // ),
                              tileColor: isSelected
                                  ? Theme.of(context).colorScheme.secondary
                                  : null,
                              textColor: isSelected
                                  ? Theme.of(context).colorScheme.primary
                                  : null,
                              minVerticalPadding: 0,
                              trailing: isSelected
                                  ? const Icon(
                                      Icons.check_circle,
                                      color: Colors.green,
                                    )
                                  : null,
                              onTap: () {
                                context.read<LanguageBloc>().add(
                                      LanguageEvent.updatedLanguage(
                                        value: language,
                                      ),
                                    );
                              },
                            );
                          },
                        ),
                ),
                const SizedBox(height: 16),
                Column(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  verticalDirection: VerticalDirection.up,
                  children: [
                    TextButton(
                      onPressed: () {
                        // Mark language selection as completed in repository
                        try {
                          LanguageRepository.clearOldSavedLang();
                          // // Use default language when skipped
                          // final defaultLang =
                          //     LanguageRepository.getLanguageByCode('en');
                          // if (defaultLang != null) {
                          //   // Save the default language quietly
                          //   LanguageRepository.save(defaultLang.code);
                          // }

                          // // Reset the language bloc state
                          // context.read<LanguageBloc>().add(
                          //       const LanguageEvent.reset(),
                          //     );
                        } catch (e) {
                          FroggyLogger.error(
                            'Error skipping language selection: $e',
                          );
                        }
                        Navigator.of(context).pop();
                      },
                      child: Text(
                        l10n.permissionButtonSkip,
                      ),
                    ),
                    ElevatedButton(
                      onPressed: state.selectedLanguage != null
                          ? () {
                              context.read<LanguageBloc>().add(
                                    const LanguageEvent.saved(),
                                  );

                              LanguageRepository.clearOldSavedLang();
                              Navigator.of(context).pop();
                            }
                          : null,
                      child: Text(
                        l10n.saveChangesButtonText,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
