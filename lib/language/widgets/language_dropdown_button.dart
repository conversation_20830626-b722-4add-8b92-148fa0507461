import 'package:constants/constants.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/language/language.dart';

class SelectLanguageDropdown extends HookWidget {
  const SelectLanguageDropdown({
    required this.onLanguageSelected,
    this.defaultLanguage,
    super.key,
  });

  final void Function(Language) onLanguageSelected;
  final Language? defaultLanguage;

  @override
  Widget build(BuildContext context) {
    final items = useMemoized<List<Language>>(
      LanguageRepository.getLanguages,
      [],
    );

    final selectedValue = useState<Language>(
      defaultLanguage ?? LanguageRepository.getLanguages()[0],
    );

    return Container(
      height: 50,
      width: 150,
      padding: const EdgeInsets.only(right: 20),
      child: DropdownButtonHideUnderline(
        child: DropdownButton2<Language>(
          isExpanded: true,
          hint: const Row(
            children: [
              Icon(
                Icons.language,
                size: 16,
                color: FroggyColors.froggyGrey2,
              ),
              SizedBox(
                width: 4,
              ),
              Expanded(
                child: Text(
                  'Eng',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: FroggyColors.froggyGrey2,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          items: items
              // .where(
              //   (language) =>
              //       language.shortName != selectedValue.value.shortName,
              // )
              .map(
                (Language item) => DropdownMenuItem<Language>(
                  value: item,
                  child: Row(
                    children: [
                      const Icon(
                        Icons.language,
                        size: 16,
                        color: FroggyColors.froggyGrey2,
                      ),
                      const SizedBox(
                        width: 4,
                      ),
                      Text(
                        item.shortName ?? '',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: FroggyColors.black,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              )
              .toList(),
          value: selectedValue.value,
          onChanged: (value) {
            if (value != null) {
              onLanguageSelected.call(value);
              selectedValue.value = value;
            }
          },
          buttonStyleData: ButtonStyleData(
            height: 80,
            // width: 100,
            padding: const EdgeInsets.only(left: 6, right: 2),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              border: Border.all(
                color: FroggyColors.froggyGrey5,
              ),
              color: Colors.transparent,
            ),
            elevation: 0,
          ),
          iconStyleData: const IconStyleData(
            icon: Icon(
              Icons.keyboard_arrow_down_outlined,
            ),
            openMenuIcon: Icon(
              Icons.close,
              size: 18,
            ),
            iconEnabledColor: FroggyColors.froggyGrey2,
            iconDisabledColor: Colors.grey,
          ),
          dropdownStyleData: DropdownStyleData(
            maxHeight: 200,
            width: 110,
            elevation: 0,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5),
              color: FroggyColors.froggyCream,
            ),
            // offset: const Offset(-20, 0),
            // scrollbarTheme: ScrollbarThemeData(
            //   radius: Radius.zero,
            //   thickness: WidgetStateProperty.all(6),
            //   thumbVisibility: WidgetStateProperty.all(true),
            // ),
          ),
          menuItemStyleData: const MenuItemStyleData(
            height: 40,
            padding: EdgeInsets.only(left: 14, right: 14),
          ),
        ),
      ),
    );
  }
}
