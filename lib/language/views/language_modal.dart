import 'dart:async';

import 'package:common/common.dart';
import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/app/app.dart' show AppDashboardPage;
import 'package:froggytalk/app/data/services/event_tracker.dart'
    show EventTrackerService;
import 'package:froggytalk/l10n/l10n.dart';
import 'package:froggytalk/language/language.dart';
import 'package:navigation/navigation.dart' show FroggyRouter;

/// A modal bottom sheet that allows users to select their preferred language.
///
/// This widget displays a list of available languages and allows the user
/// to select one. It uses the [LanguageBloc] to manage the state of the
/// language selection process.
class ChooseLanguageModal extends StatefulHookWidget {
  /// Creates a new instance of [ChooseLanguageModal].
  const ChooseLanguageModal({super.key});

  /// Shows the language selection modal as a bottom sheet.
  ///
  /// Returns the selected language code, or null if no selection was made.
  static Future<String?> show(BuildContext context) {
    return showModalBottomSheet<String>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => BlocProvider(
        create: (context) => LanguageBloc()..add(const LanguageEvent.started()),
        child: const ChooseLanguageModal(),
      ),
    );
  }

  @override
  State<ChooseLanguageModal> createState() => _ChooseLanguageModalState();
}

class _ChooseLanguageModalState extends State<ChooseLanguageModal> {
  @override
  Widget build(BuildContext context) {
    final l10n = useLocale();

    // Calculate the available height for the modal
    final availableHeight = MediaQuery.of(context).size.height * 0.8;

    return Container(
      height: availableHeight,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(24),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(l10n),
          Expanded(
            child: BlocConsumer<LanguageBloc, LanguageState>(
              listener: (context, state) {
                // Handle language change completion
                if (state.selectedLanguage == state.savedLanguage) {
                  // Navigator.of(context).pop(state.selectedLanguage?.code);
                }

                // // Show error if language change failed
                // if (state.status.isFailure) {
                //   FroggyToast.showErrorToast(
                //     context,
                //     l10n.languageChangeError,
                //   );
                // }
              },
              builder: (context, state) {
                // if ((state.isLoading) == null) {
                //   return const Center(child: CircularProgressIndicator());
                // }

                if (state.languages.isEmpty) {
                  return Center(
                    child: Text(l10n.noLanguagesAvailable),
                  );
                }

                return _buildLanguageList(context, state, l10n);
              },
            ),
          ),
          _buildActionButtons(context, l10n),
        ],
      ),
    );
  }

  /// Builds the header section of the modal.
  Widget _buildHeader(AppLocalizations l10n) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: FroggyColors.froggyGrey5),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                l10n.chooseLanguage,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
          FroggySpacer.y8(),
          Text(
            l10n.selectPreferredLanguage,
            style: const TextStyle(
              fontSize: 14,
              color: FroggyColors.froggyGrey1,
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the list of available languages.
  Widget _buildLanguageList(
    BuildContext context,
    LanguageState state,
    AppLocalizations l10n,
  ) {
    return ListView.separated(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: state.languages.length,
      separatorBuilder: (context, index) => const Divider(height: 1),
      itemBuilder: (context, index) {
        final language = state.languages[index];
        final isSelected = state.selectedLanguage?.code == language.code;

        return LanguageListItem(
          language: language,
          isSelected: isSelected,
          onTap: () {
            context.read<LanguageBloc>().add(
                  LanguageEvent.updatedLanguage(value: language),
                );
          },
        );
      },
    );
  }

  /// Builds the action buttons at the bottom of the modal.
  Widget _buildActionButtons(BuildContext context, AppLocalizations l10n) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: BlocSelector<LanguageBloc, LanguageState, Language?>(
        selector: (state) => state.selectedLanguage,
        builder: (context, selectedLanguage) {
          final canApply = selectedLanguage != null;

          return Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () => Navigator.pop(context),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    side: const BorderSide(color: FroggyColors.primary),
                  ),
                  child: Text(l10n.cancel),
                ),
              ),
              FroggySpacer.x16(),
              Expanded(
                child: ElevatedButton(
                  onPressed: canApply
                      ? () async {
                          // Dispatch the save event to the bloc
                          context.read<LanguageBloc>().add(
                                const LanguageEvent.saved(),
                              );

                          // Track the event
                          unawaited(
                            context.read<EventTrackerService>().logEvent(
                                  schema: 'Change_Language',
                                  description: 'User changed language to '
                                      '${selectedLanguage.toLocale()}',
                                ),
                          );

                          // Add short delay to ensure storage operations
                          // complete
                          await Future<void>.delayed(
                            const Duration(milliseconds: 300),
                          );

                          // Now navigate when the operation has had time to
                          // complete
                          if (context.mounted) {
                            Navigator.of(context).pop();
                            FroggyRouter.pushAndRemoveUntil(
                              const AppDashboardPage(),
                            );
                          }
                        }
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: FroggyColors.primary,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: Text(l10n.apply),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

/// A list item representing a language option.
class LanguageListItem extends StatelessWidget {
  /// Creates a new instance of [LanguageListItem].
  ///
  /// The [language] parameter is required and represents the language to
  /// display.
  /// The [isSelected] parameter indicates whether this language is currently
  /// selected.
  /// The [onTap] parameter is a callback that will be called when the item is
  /// tapped.
  const LanguageListItem({
    required this.language,
    required this.isSelected,
    required this.onTap,
    super.key,
  });

  /// The language to display.
  final Language language;

  /// Whether this language is currently selected.
  final bool isSelected;

  /// Callback to be invoked when the item is tapped.
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 20,
          vertical: 16,
        ),
        child: Row(
          children: [
            // CircleAvatar(
            //   radius: 12,
            //   backgroundImage: AssetImage(language.shortName),
            // ),
            FroggySpacer.x16(),
            Text(
              language.name,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const Spacer(),
            if (isSelected)
              const Icon(
                Icons.check_circle,
                color: FroggyColors.primary,
                size: 24,
              ),
          ],
        ),
      ),
    );
  }
}
