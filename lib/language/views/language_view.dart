import 'package:constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/app/app.dart';
import 'package:froggytalk/app/data/services/event_tracker.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:froggytalk/language/language.dart';
// import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:utils/utils.dart';

class LanguagePage extends HookWidget {
  const LanguagePage({super.key});

  static Route<Object?> route() {
    return MaterialPageRoute<Object?>(
      builder: (_) => const LanguagePage(),
    );
  }

  static String routeName = '/language';

  @override
  Widget build(BuildContext context) {
    final l10n = useLocale();
    final languageSelected = useState<Language?>(null);
    // final savedLanguage = useLanguage();
    final bloc = context.read<LanguageBloc>();
    final isSaved = useState<bool?>(null);
    // auth bloc
    // final authBloc = context.read<AuthenticationBloc>();
    // final radioBloc = context.read<AffliateRadioStationBloc>();

    // final showSuccess = useCallback(
    //   (String message) {
    //     FroggyToast.showSuccessToast(
    //       context,
    //       message,
    //       locale: savedLanguage!.toLocale(),
    //     );
    //   },
    //   [],
    // );

    final onLanguageSelected = useCallback<void Function(Language)>(
      (p0) {
        languageSelected.value = p0;
      },
      [],
    );

    final onSavePressed = useCallback(() {
      isSaved.value = false;
      final selectedLanguage = languageSelected.value;
      if (selectedLanguage == null) return;
      bloc
        ..add(LanguageEvent.updatedLanguage(value: selectedLanguage))
        ..add(const LanguageEvent.saved());

      isSaved.value = true;
      setTimeout(
        computation: () {
          isSaved.value = null;
          languageSelected.value = null;

          context.read<EventTrackerService>().logEvent(
                schema: 'Change_Language',
                description:
                    'User changed language to ${selectedLanguage.toLocale()}',
              );
        },
      );

      // setTimeout(
      //   computation: () async {
      //     await OneSignal.User.removeTag('user_language');
      //     await OneSignal.User.addTagWithKey(
      //       'user_language',
      //       LanguageRepository.getSavedLanguage()?.code,
      //     );
      //   },
      // );

      // showSuccess(l10n.savedSuccessfullyMessage);
    });

    return Scaffold(
      appBar: FroggyAppBar(
        label: l10n.appLanguageAppbarTitle,
        labelFontSize: 20,
      ),
      body: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          BlocSelector<LanguageBloc, LanguageState, Language>(
            selector: (state) {
              return state.savedLanguage ??
                  LanguageRepository.getLanguages().first;
            },
            builder: (context, defaultLanguage) {
              return LanguageFormField(
                defaultLanguage: defaultLanguage,
                onLanguageSelected: onLanguageSelected,
                message: isSaved.value != null && isSaved.value! == true
                    ? l10n.savedSuccessfullyMessage
                    : null,
              );
            },
          ),
          const Spacer(),
          Container(
            margin: const EdgeInsets.only(top: 30, bottom: 30),
            padding: const EdgeInsets.symmetric(horizontal: 20),
            width: double.infinity,
            height: 48,
            child: ElevatedButton(
              onPressed: languageSelected.value == null ? null : onSavePressed,
              style: OutlinedButton.styleFrom(
                side: BorderSide.none,
              ),
              child: Text(
                l10n.saveChangesButtonText,
                style: const TextStyle(
                  color: FroggyColors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
