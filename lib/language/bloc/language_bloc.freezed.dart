// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'language_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$LanguageEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() saved,
    required TResult Function(Language value) updatedLanguage,
    required TResult Function() reset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? saved,
    TResult? Function(Language value)? updatedLanguage,
    TResult? Function()? reset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? saved,
    TResult Function(Language value)? updatedLanguage,
    TResult Function()? reset,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Saved value) saved,
    required TResult Function(_UpdatedLanguage value) updatedLanguage,
    required TResult Function(_Initial value) reset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Saved value)? saved,
    TResult? Function(_UpdatedLanguage value)? updatedLanguage,
    TResult? Function(_Initial value)? reset,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Saved value)? saved,
    TResult Function(_UpdatedLanguage value)? updatedLanguage,
    TResult Function(_Initial value)? reset,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LanguageEventCopyWith<$Res> {
  factory $LanguageEventCopyWith(
          LanguageEvent value, $Res Function(LanguageEvent) then) =
      _$LanguageEventCopyWithImpl<$Res, LanguageEvent>;
}

/// @nodoc
class _$LanguageEventCopyWithImpl<$Res, $Val extends LanguageEvent>
    implements $LanguageEventCopyWith<$Res> {
  _$LanguageEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LanguageEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$StartedImplCopyWith<$Res> {
  factory _$$StartedImplCopyWith(
          _$StartedImpl value, $Res Function(_$StartedImpl) then) =
      __$$StartedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$StartedImplCopyWithImpl<$Res>
    extends _$LanguageEventCopyWithImpl<$Res, _$StartedImpl>
    implements _$$StartedImplCopyWith<$Res> {
  __$$StartedImplCopyWithImpl(
      _$StartedImpl _value, $Res Function(_$StartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of LanguageEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$StartedImpl implements _Started {
  const _$StartedImpl();

  @override
  String toString() {
    return 'LanguageEvent.started()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$StartedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() saved,
    required TResult Function(Language value) updatedLanguage,
    required TResult Function() reset,
  }) {
    return started();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? saved,
    TResult? Function(Language value)? updatedLanguage,
    TResult? Function()? reset,
  }) {
    return started?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? saved,
    TResult Function(Language value)? updatedLanguage,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Saved value) saved,
    required TResult Function(_UpdatedLanguage value) updatedLanguage,
    required TResult Function(_Initial value) reset,
  }) {
    return started(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Saved value)? saved,
    TResult? Function(_UpdatedLanguage value)? updatedLanguage,
    TResult? Function(_Initial value)? reset,
  }) {
    return started?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Saved value)? saved,
    TResult Function(_UpdatedLanguage value)? updatedLanguage,
    TResult Function(_Initial value)? reset,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started(this);
    }
    return orElse();
  }
}

abstract class _Started implements LanguageEvent {
  const factory _Started() = _$StartedImpl;
}

/// @nodoc
abstract class _$$SavedImplCopyWith<$Res> {
  factory _$$SavedImplCopyWith(
          _$SavedImpl value, $Res Function(_$SavedImpl) then) =
      __$$SavedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SavedImplCopyWithImpl<$Res>
    extends _$LanguageEventCopyWithImpl<$Res, _$SavedImpl>
    implements _$$SavedImplCopyWith<$Res> {
  __$$SavedImplCopyWithImpl(
      _$SavedImpl _value, $Res Function(_$SavedImpl) _then)
      : super(_value, _then);

  /// Create a copy of LanguageEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SavedImpl implements _Saved {
  const _$SavedImpl();

  @override
  String toString() {
    return 'LanguageEvent.saved()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SavedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() saved,
    required TResult Function(Language value) updatedLanguage,
    required TResult Function() reset,
  }) {
    return saved();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? saved,
    TResult? Function(Language value)? updatedLanguage,
    TResult? Function()? reset,
  }) {
    return saved?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? saved,
    TResult Function(Language value)? updatedLanguage,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (saved != null) {
      return saved();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Saved value) saved,
    required TResult Function(_UpdatedLanguage value) updatedLanguage,
    required TResult Function(_Initial value) reset,
  }) {
    return saved(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Saved value)? saved,
    TResult? Function(_UpdatedLanguage value)? updatedLanguage,
    TResult? Function(_Initial value)? reset,
  }) {
    return saved?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Saved value)? saved,
    TResult Function(_UpdatedLanguage value)? updatedLanguage,
    TResult Function(_Initial value)? reset,
    required TResult orElse(),
  }) {
    if (saved != null) {
      return saved(this);
    }
    return orElse();
  }
}

abstract class _Saved implements LanguageEvent {
  const factory _Saved() = _$SavedImpl;
}

/// @nodoc
abstract class _$$UpdatedLanguageImplCopyWith<$Res> {
  factory _$$UpdatedLanguageImplCopyWith(_$UpdatedLanguageImpl value,
          $Res Function(_$UpdatedLanguageImpl) then) =
      __$$UpdatedLanguageImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Language value});

  $LanguageCopyWith<$Res> get value;
}

/// @nodoc
class __$$UpdatedLanguageImplCopyWithImpl<$Res>
    extends _$LanguageEventCopyWithImpl<$Res, _$UpdatedLanguageImpl>
    implements _$$UpdatedLanguageImplCopyWith<$Res> {
  __$$UpdatedLanguageImplCopyWithImpl(
      _$UpdatedLanguageImpl _value, $Res Function(_$UpdatedLanguageImpl) _then)
      : super(_value, _then);

  /// Create a copy of LanguageEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$UpdatedLanguageImpl(
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as Language,
    ));
  }

  /// Create a copy of LanguageEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LanguageCopyWith<$Res> get value {
    return $LanguageCopyWith<$Res>(_value.value, (value) {
      return _then(_value.copyWith(value: value));
    });
  }
}

/// @nodoc

class _$UpdatedLanguageImpl implements _UpdatedLanguage {
  const _$UpdatedLanguageImpl({required this.value});

  @override
  final Language value;

  @override
  String toString() {
    return 'LanguageEvent.updatedLanguage(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdatedLanguageImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  /// Create a copy of LanguageEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdatedLanguageImplCopyWith<_$UpdatedLanguageImpl> get copyWith =>
      __$$UpdatedLanguageImplCopyWithImpl<_$UpdatedLanguageImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() saved,
    required TResult Function(Language value) updatedLanguage,
    required TResult Function() reset,
  }) {
    return updatedLanguage(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? saved,
    TResult? Function(Language value)? updatedLanguage,
    TResult? Function()? reset,
  }) {
    return updatedLanguage?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? saved,
    TResult Function(Language value)? updatedLanguage,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (updatedLanguage != null) {
      return updatedLanguage(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Saved value) saved,
    required TResult Function(_UpdatedLanguage value) updatedLanguage,
    required TResult Function(_Initial value) reset,
  }) {
    return updatedLanguage(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Saved value)? saved,
    TResult? Function(_UpdatedLanguage value)? updatedLanguage,
    TResult? Function(_Initial value)? reset,
  }) {
    return updatedLanguage?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Saved value)? saved,
    TResult Function(_UpdatedLanguage value)? updatedLanguage,
    TResult Function(_Initial value)? reset,
    required TResult orElse(),
  }) {
    if (updatedLanguage != null) {
      return updatedLanguage(this);
    }
    return orElse();
  }
}

abstract class _UpdatedLanguage implements LanguageEvent {
  const factory _UpdatedLanguage({required final Language value}) =
      _$UpdatedLanguageImpl;

  Language get value;

  /// Create a copy of LanguageEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdatedLanguageImplCopyWith<_$UpdatedLanguageImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$LanguageEventCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of LanguageEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'LanguageEvent.reset()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function() saved,
    required TResult Function(Language value) updatedLanguage,
    required TResult Function() reset,
  }) {
    return reset();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function()? saved,
    TResult? Function(Language value)? updatedLanguage,
    TResult? Function()? reset,
  }) {
    return reset?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function()? saved,
    TResult Function(Language value)? updatedLanguage,
    TResult Function()? reset,
    required TResult orElse(),
  }) {
    if (reset != null) {
      return reset();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_Saved value) saved,
    required TResult Function(_UpdatedLanguage value) updatedLanguage,
    required TResult Function(_Initial value) reset,
  }) {
    return reset(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_Saved value)? saved,
    TResult? Function(_UpdatedLanguage value)? updatedLanguage,
    TResult? Function(_Initial value)? reset,
  }) {
    return reset?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_Saved value)? saved,
    TResult Function(_UpdatedLanguage value)? updatedLanguage,
    TResult Function(_Initial value)? reset,
    required TResult orElse(),
  }) {
    if (reset != null) {
      return reset(this);
    }
    return orElse();
  }
}

abstract class _Initial implements LanguageEvent {
  const factory _Initial() = _$InitialImpl;
}

LanguageState _$LanguageStateFromJson(Map<String, dynamic> json) {
  return _LanguageState.fromJson(json);
}

/// @nodoc
mixin _$LanguageState {
  List<Language> get languages => throw _privateConstructorUsedError;
  Language? get savedLanguage => throw _privateConstructorUsedError;
  Language? get selectedLanguage => throw _privateConstructorUsedError;
  bool? get isLoading => throw _privateConstructorUsedError;

  /// Serializes this LanguageState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LanguageState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LanguageStateCopyWith<LanguageState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LanguageStateCopyWith<$Res> {
  factory $LanguageStateCopyWith(
          LanguageState value, $Res Function(LanguageState) then) =
      _$LanguageStateCopyWithImpl<$Res, LanguageState>;
  @useResult
  $Res call(
      {List<Language> languages,
      Language? savedLanguage,
      Language? selectedLanguage,
      bool? isLoading});

  $LanguageCopyWith<$Res>? get savedLanguage;
  $LanguageCopyWith<$Res>? get selectedLanguage;
}

/// @nodoc
class _$LanguageStateCopyWithImpl<$Res, $Val extends LanguageState>
    implements $LanguageStateCopyWith<$Res> {
  _$LanguageStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LanguageState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? languages = null,
    Object? savedLanguage = freezed,
    Object? selectedLanguage = freezed,
    Object? isLoading = freezed,
  }) {
    return _then(_value.copyWith(
      languages: null == languages
          ? _value.languages
          : languages // ignore: cast_nullable_to_non_nullable
              as List<Language>,
      savedLanguage: freezed == savedLanguage
          ? _value.savedLanguage
          : savedLanguage // ignore: cast_nullable_to_non_nullable
              as Language?,
      selectedLanguage: freezed == selectedLanguage
          ? _value.selectedLanguage
          : selectedLanguage // ignore: cast_nullable_to_non_nullable
              as Language?,
      isLoading: freezed == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }

  /// Create a copy of LanguageState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LanguageCopyWith<$Res>? get savedLanguage {
    if (_value.savedLanguage == null) {
      return null;
    }

    return $LanguageCopyWith<$Res>(_value.savedLanguage!, (value) {
      return _then(_value.copyWith(savedLanguage: value) as $Val);
    });
  }

  /// Create a copy of LanguageState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LanguageCopyWith<$Res>? get selectedLanguage {
    if (_value.selectedLanguage == null) {
      return null;
    }

    return $LanguageCopyWith<$Res>(_value.selectedLanguage!, (value) {
      return _then(_value.copyWith(selectedLanguage: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$LanguageStateImplCopyWith<$Res>
    implements $LanguageStateCopyWith<$Res> {
  factory _$$LanguageStateImplCopyWith(
          _$LanguageStateImpl value, $Res Function(_$LanguageStateImpl) then) =
      __$$LanguageStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<Language> languages,
      Language? savedLanguage,
      Language? selectedLanguage,
      bool? isLoading});

  @override
  $LanguageCopyWith<$Res>? get savedLanguage;
  @override
  $LanguageCopyWith<$Res>? get selectedLanguage;
}

/// @nodoc
class __$$LanguageStateImplCopyWithImpl<$Res>
    extends _$LanguageStateCopyWithImpl<$Res, _$LanguageStateImpl>
    implements _$$LanguageStateImplCopyWith<$Res> {
  __$$LanguageStateImplCopyWithImpl(
      _$LanguageStateImpl _value, $Res Function(_$LanguageStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of LanguageState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? languages = null,
    Object? savedLanguage = freezed,
    Object? selectedLanguage = freezed,
    Object? isLoading = freezed,
  }) {
    return _then(_$LanguageStateImpl(
      languages: null == languages
          ? _value._languages
          : languages // ignore: cast_nullable_to_non_nullable
              as List<Language>,
      savedLanguage: freezed == savedLanguage
          ? _value.savedLanguage
          : savedLanguage // ignore: cast_nullable_to_non_nullable
              as Language?,
      selectedLanguage: freezed == selectedLanguage
          ? _value.selectedLanguage
          : selectedLanguage // ignore: cast_nullable_to_non_nullable
              as Language?,
      isLoading: freezed == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LanguageStateImpl extends _LanguageState {
  _$LanguageStateImpl(
      {final List<Language> languages = const [],
      this.savedLanguage,
      this.selectedLanguage,
      this.isLoading})
      : _languages = languages,
        super._();

  factory _$LanguageStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$LanguageStateImplFromJson(json);

  final List<Language> _languages;
  @override
  @JsonKey()
  List<Language> get languages {
    if (_languages is EqualUnmodifiableListView) return _languages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_languages);
  }

  @override
  final Language? savedLanguage;
  @override
  final Language? selectedLanguage;
  @override
  final bool? isLoading;

  @override
  String toString() {
    return 'LanguageState(languages: $languages, savedLanguage: $savedLanguage, selectedLanguage: $selectedLanguage, isLoading: $isLoading)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LanguageStateImpl &&
            const DeepCollectionEquality()
                .equals(other._languages, _languages) &&
            (identical(other.savedLanguage, savedLanguage) ||
                other.savedLanguage == savedLanguage) &&
            (identical(other.selectedLanguage, selectedLanguage) ||
                other.selectedLanguage == selectedLanguage) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_languages),
      savedLanguage,
      selectedLanguage,
      isLoading);

  /// Create a copy of LanguageState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LanguageStateImplCopyWith<_$LanguageStateImpl> get copyWith =>
      __$$LanguageStateImplCopyWithImpl<_$LanguageStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LanguageStateImplToJson(
      this,
    );
  }
}

abstract class _LanguageState extends LanguageState {
  factory _LanguageState(
      {final List<Language> languages,
      final Language? savedLanguage,
      final Language? selectedLanguage,
      final bool? isLoading}) = _$LanguageStateImpl;
  _LanguageState._() : super._();

  factory _LanguageState.fromJson(Map<String, dynamic> json) =
      _$LanguageStateImpl.fromJson;

  @override
  List<Language> get languages;
  @override
  Language? get savedLanguage;
  @override
  Language? get selectedLanguage;
  @override
  bool? get isLoading;

  /// Create a copy of LanguageState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LanguageStateImplCopyWith<_$LanguageStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
