import 'dart:async';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:froggytalk/language/language.dart';
import 'package:froggytalk/profile/profile.dart';
import 'package:froggytalk/radio_stations/radio_stations.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';

part 'language_bloc.freezed.dart';
part 'language_bloc.g.dart';
part 'language_event.dart';
part 'language_state.dart';

class LanguageBloc extends HydratedBloc<LanguageEvent, LanguageState> {
  LanguageBloc({RadioStationsBloc? radioStationsBloc})
      : _radioStationsBloc = radioStationsBloc,
        super(LanguageState.initial()) {
    on<_Started>(_onStarted);
    on<_UpdatedLanguage>(_onUpdatedLanguage);
    on<_Saved>(_onSaved);
    on<_Initial>(_onInitial);
  }

  final AuthProfileRepository _authRepo = AuthProfileRepository();
  final RadioStationsBloc? _radioStationsBloc;

  @override
  LanguageState? fromJson(Map<String, dynamic> json) {
    try {
      return LanguageState.fromJson(json);
    } catch (_) {
      return LanguageState.initial();
    }
  }

  @override
  Map<String, dynamic>? toJson(LanguageState state) {
    try {
      return {
        'savedLanguage': state.savedLanguage,
      };
    } catch (_) {
      return null;
    }
  }

  FutureOr<void> _onInitial(
    _Initial event,
    Emitter<LanguageState> emit,
  ) {
    emit(LanguageState.initial());
  }

  FutureOr<void> _onStarted(_Started event, Emitter<LanguageState> emit) {
    final languages = LanguageRepository.getLanguages();
    final currentLanguage = LanguageRepository.getSavedLanguage();

    final defaultLanguage = LanguageRepository.getLanguageByCode('en');
    emit(
      state.copyWith(
        isLoading: false,
        languages: languages,
        selectedLanguage: currentLanguage,
        savedLanguage: currentLanguage ?? defaultLanguage,
      ),
    );
  }

  FutureOr<void> _onUpdatedLanguage(
    _UpdatedLanguage event,
    Emitter<LanguageState> emit,
  ) {
    emit(
      state.copyWith(selectedLanguage: event.value),
    );
  }

  FutureOr<void> _onSaved(_Saved event, Emitter<LanguageState> emit) {
    emit(
      state.copyWith(isLoading: true),
    );

    final selectedLanguage = state.selectedLanguage;
    if (selectedLanguage == null) return Future.value();

    // Save the new language
    LanguageRepository.save(selectedLanguage.code);

    // Update the user profile with the new language
    unawaited(_authRepo.execute(language: selectedLanguage.code));

    // Clear old language setting if it exists
    if (LanguageRepository.hasSavedOldLanguage()) {
      LanguageRepository.clearOldSavedLang();
    }

    // Refresh radio stations for the new language
    _radioStationsBloc?.add(
      const RadioStationsEvent.started(),
    );

    emit(
      state.copyWith(
        isLoading: false,
        savedLanguage: selectedLanguage,
      ),
    );
  }
}
