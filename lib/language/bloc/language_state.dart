part of 'language_bloc.dart';

@freezed
class LanguageState with _$LanguageState {
  factory LanguageState({
    @Default([]) List<Language> languages,
    Language? savedLanguage,
    Language? selectedLanguage,
    bool? isLoading,
  }) = _LanguageState;

  factory LanguageState.initial() => LanguageState();

  LanguageState._();

  factory LanguageState.fromJson(Map<String, dynamic> json) =>
      _$LanguageStateFromJson(json);
}
