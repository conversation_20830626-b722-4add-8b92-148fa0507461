// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'language_bloc.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$LanguageStateImpl _$$LanguageStateImplFromJson(Map<String, dynamic> json) =>
    _$LanguageStateImpl(
      languages: (json['languages'] as List<dynamic>?)
              ?.map((e) => Language.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      savedLanguage: json['savedLanguage'] == null
          ? null
          : Language.fromJson(json['savedLanguage'] as Map<String, dynamic>),
      selectedLanguage: json['selectedLanguage'] == null
          ? null
          : Language.fromJson(json['selectedLanguage'] as Map<String, dynamic>),
      isLoading: json['isLoading'] as bool?,
    );

const _$$LanguageStateImplFieldMap = <String, String>{
  'languages': 'languages',
  'savedLanguage': 'savedLanguage',
  'selectedLanguage': 'selectedLanguage',
  'isLoading': 'isLoading',
};

Map<String, dynamic> _$$LanguageStateImplToJson(_$LanguageStateImpl instance) =>
    <String, dynamic>{
      'languages': instance.languages.map((e) => e.toJson()).toList(),
      if (instance.savedLanguage?.toJson() case final value?)
        'savedLanguage': value,
      if (instance.selectedLanguage?.toJson() case final value?)
        'selectedLanguage': value,
      if (instance.isLoading case final value?) 'isLoading': value,
    };
