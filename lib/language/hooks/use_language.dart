// create a hook to get the language state from the language bloc
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:froggytalk/language/language.dart';

Language? useLanguage() {
  final context = useContext();
  final languageBloc = BlocProvider.of<LanguageBloc>(context);
  final language = useStream(
    languageBloc.stream.map((state) => state.selectedLanguage),
    initialData: languageBloc.state.selectedLanguage,
  );
  return language.data;
}
