import 'package:flutter/material.dart';
import 'package:froggytalk/l10n/l10n.dart';
import 'package:froggytalk/language/data/models/language_model.dart';
import 'package:utils/utils.dart';

/// Repository for handling language-related operations
class LanguageRepository {
  /// Key used to store the selected language in local storage
  static const languageKey = '_froggytalk_preferred_language';

  /// Key used to store the legacy language selection in local storage
  static const isLanguageUpdatedKey = '__isLanguageUpdated0303';

  static final _localStorage = FroggyLocalStorage.getInstance();

  /// Checks if the old language setting exists
  static bool hasSavedOldLanguage() {
    final s = _localStorage.get<String>(isLanguageUpdatedKey);
    return s != null && s.isNotEmpty;
  }

  /// Clears the old language setting
  static void clearOldSavedLang() {
    _localStorage.set(isLanguageUpdatedKey, 'yes');
  }

  /// Gets the list of supported languages
  static List<Language> getLanguages() {
    return [
      const Language(
        name: 'Amharic',
        shortName: 'አማርኛ',
        code: 'am',
      ),
      const Language(
        name: 'Arabic',
        shortName: 'العربية',
        code: 'ar',
      ),
      const Language(
        name: 'Dinka',
        shortName: 'Dinka',
        code: 'din',
      ),
      const Language(
        name: 'English',
        shortName: 'English',
        code: 'en',
      ),
      const Language(
        name: 'French',
        shortName: 'Français',
        code: 'fr',
      ),
      const Language(
        name: 'Hausa',
        shortName: 'Hausa',
        code: 'ha',
      ),
      const Language(
        name: 'Tigrinya',
        shortName: 'ትግርኛ',
        code: 'ti',
      ),
    ];
  }

  /// Gets a language by its code
  static Language? getLanguageByCode(String code) {
    try {
      return getLanguages().firstWhere(
        (element) => element.code == code,
      );
    } catch (e) {
      FroggyLogger.error('Language code not found: $code');
      return null;
    }
  }

  /// Gets the saved language or null if not set
  static Language? getSavedLanguage() {
    final langKey = _localStorage.get<String>(languageKey);

    if (langKey != null) {
      return getLanguageByCode(langKey);
    }

    return null;
  }

  /// Saves the selected language code
  static void save(String code) {
    _localStorage.set(languageKey, code);
  }

  /// Gets the current locale based on saved language
  static Locale? getCurrentLocale() {
    final savedLanguage = getSavedLanguage();
    if (savedLanguage != null) {
      return Locale(savedLanguage.code);
    }
    return null;
  }

  static List<Language> getSupportedLanguages() {
    return AppLocalizations.supportedLocales
        .map(
          (locale) => Language(
            name: getLanguageByCode(locale.toLanguageTag())?.name ?? '',
            shortName:
                getLanguageByCode(locale.toLanguageTag())?.shortName ?? '',
            code: locale.languageCode,
          ),
        )
        .toList();
  }

  static Language getLanguageByName(String name) {
    return getLanguages().firstWhere((language) => language.name == name);
  }

  static String getLanguageDisplayName(String code) {
    return getLanguageByCode(code)?.name ?? '';
  }
}
