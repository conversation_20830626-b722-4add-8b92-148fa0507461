import 'dart:ui';

import 'package:freezed_annotation/freezed_annotation.dart';

part 'language_model.freezed.dart';
part 'language_model.g.dart';

@freezed
class Language with _$Language {
  const factory Language({
    required String name,
    required String code,
    String? shortName,
  }) = _Language;

  // Factory constructor for creating a Language instance from JSON
  factory Language.fromJson(Map<String, dynamic> json) =>
      _$LanguageFromJson(json);

  static Language defaultLanguage = const Language(
    name: 'English',
    code: 'en',
  );
}

extension LanguageX on Language {
  Locale toLocale() {
    return Locale(code);
  }
}

enum LanguageEnum {
  english(
    Locale('en', 'US'),
    'English',
  ),
  tigrinya(
    Locale('ti'),
    'ትግርኛ',
  ),
  amharic(
    Locale('am'),
    'አማርኛ',
  ),
  hausa(
    Locale('ha'),
    'Hausa',
  ),
  ;

  const LanguageEnum(
    this.value,
    this.text,
  );

  final Locale value;
  final String text;
}
