// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'language_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$LanguageImpl _$$LanguageImplFromJson(Map<String, dynamic> json) =>
    _$LanguageImpl(
      name: json['name'] as String,
      code: json['code'] as String,
      shortName: json['shortName'] as String?,
    );

const _$$LanguageImplFieldMap = <String, String>{
  'name': 'name',
  'code': 'code',
  'shortName': 'shortName',
};

Map<String, dynamic> _$$LanguageImplToJson(_$LanguageImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'code': instance.code,
      if (instance.shortName case final value?) 'shortName': value,
    };
