//
//  CallKitService.swift
//  Runner
//
//  Created for FroggyTalk iOS Call Handling
//

import UIKit
import CallKit
import AVFoundation
import Flutter

/// CallKit service for handling VoIP calls on iOS
/// This provides native iOS call interface and proper system integration
class CallKitService: NSObject {
    
    static let shared = CallKitService()
    
    private let callController = CXCallController()
    private var provider: CXProvider?
    private var currentCallUUID: UUID?
    private var flutterChannel: FlutterMethodChannel?
    
    private override init() {
        super.init()
        setupCallKit()
    }
    
    /// Initialize CallKit provider with proper configuration
    private func setupCallKit() {
        let configuration = CXProviderConfiguration(localizedName: "FroggyTalk")
        
        // Configure provider capabilities
        configuration.supportsVideo = false // Voice calls only initially
        configuration.maximumCallGroups = 1
        configuration.maximumCallsPerCallGroup = 1
        configuration.supportedHandleTypes = [.phoneNumber, .generic]
        
        // Configure audio session
        configuration.includesCallsInRecents = true
        configuration.supportsHolding = true
        configuration.supportsGrouping = false
        configuration.supportsUngrouping = false
        
        // Set ringtone and icon
        if let bundlePath = Bundle.main.path(forResource: "ringtone", ofType: "m4a") {
            configuration.ringtoneSound = bundlePath
        }
        
        // Create provider
        provider = CXProvider(configuration: configuration)
        provider?.setDelegate(self, queue: nil)
    }
    
    /// Set Flutter method channel for communication
    func setFlutterChannel(_ channel: FlutterMethodChannel) {
        self.flutterChannel = channel
    }
    
    /// Report incoming call to CallKit
    func reportIncomingCall(uuid: UUID, phoneNumber: String, hasVideo: Bool = false) {
        guard let provider = provider else { return }
        
        let update = CXCallUpdate()
        update.remoteHandle = CXHandle(type: .phoneNumber, value: phoneNumber)
        update.hasVideo = hasVideo
        update.localizedCallerName = phoneNumber
        update.supportsHolding = true
        update.supportsGrouping = false
        update.supportsUngrouping = false
        update.supportsDTMF = true
        
        provider.reportNewIncomingCall(with: uuid, update: update) { error in
            if let error = error {
                print("[CallKitService] Error reporting incoming call: \(error)")
                self.notifyFlutter("call_failed", data: ["error": error.localizedDescription])
            } else {
                print("[CallKitService] Successfully reported incoming call")
                self.currentCallUUID = uuid
                self.notifyFlutter("call_reported", data: ["uuid": uuid.uuidString])
            }
        }
    }
    
    /// Start outgoing call through CallKit
    func startOutgoingCall(uuid: UUID, phoneNumber: String, hasVideo: Bool = false) {
        let handle = CXHandle(type: .phoneNumber, value: phoneNumber)
        let startCallAction = CXStartCallAction(call: uuid, handle: handle)
        startCallAction.isVideo = hasVideo
        
        let transaction = CXTransaction(action: startCallAction)
        
        callController.request(transaction) { error in
            if let error = error {
                print("[CallKitService] Error starting outgoing call: \(error)")
                self.notifyFlutter("call_failed", data: ["error": error.localizedDescription])
            } else {
                print("[CallKitService] Successfully started outgoing call")
                self.currentCallUUID = uuid
                self.notifyFlutter("call_started", data: ["uuid": uuid.uuidString])
            }
        }
    }
    
    /// Answer incoming call
    func answerCall(uuid: UUID) {
        let answerAction = CXAnswerCallAction(call: uuid)
        let transaction = CXTransaction(action: answerAction)
        
        callController.request(transaction) { error in
            if let error = error {
                print("[CallKitService] Error answering call: \(error)")
                self.notifyFlutter("call_answer_failed", data: ["error": error.localizedDescription])
            } else {
                print("[CallKitService] Successfully answered call")
                self.notifyFlutter("call_answered", data: ["uuid": uuid.uuidString])
            }
        }
    }
    
    /// End call
    func endCall(uuid: UUID) {
        let endCallAction = CXEndCallAction(call: uuid)
        let transaction = CXTransaction(action: endCallAction)
        
        callController.request(transaction) { error in
            if let error = error {
                print("[CallKitService] Error ending call: \(error)")
                self.notifyFlutter("call_end_failed", data: ["error": error.localizedDescription])
            } else {
                print("[CallKitService] Successfully ended call")
                self.currentCallUUID = nil
                self.notifyFlutter("call_ended", data: ["uuid": uuid.uuidString])
            }
        }
    }
    
    /// Set call on hold
    func setCallOnHold(uuid: UUID, onHold: Bool) {
        let setHeldAction = CXSetHeldCallAction(call: uuid, onHold: onHold)
        let transaction = CXTransaction(action: setHeldAction)
        
        callController.request(transaction) { error in
            if let error = error {
                print("[CallKitService] Error setting call hold state: \(error)")
            } else {
                print("[CallKitService] Successfully set call hold state to \(onHold)")
                self.notifyFlutter("call_hold_changed", data: [
                    "uuid": uuid.uuidString,
                    "onHold": onHold
                ])
            }
        }
    }
    
    /// Set call muted state
    func setCallMuted(uuid: UUID, muted: Bool) {
        let setMutedAction = CXSetMutedCallAction(call: uuid, muted: muted)
        let transaction = CXTransaction(action: setMutedAction)
        
        callController.request(transaction) { error in
            if let error = error {
                print("[CallKitService] Error setting call mute state: \(error)")
            } else {
                print("[CallKitService] Successfully set call mute state to \(muted)")
                self.notifyFlutter("call_mute_changed", data: [
                    "uuid": uuid.uuidString,
                    "muted": muted
                ])
            }
        }
    }
    
    /// Report call connection state change
    func reportCallConnected(uuid: UUID) {
        guard let provider = provider else { return }
        provider.reportOutgoingCall(with: uuid, connectedAt: Date())
        notifyFlutter("call_connected", data: ["uuid": uuid.uuidString])
    }
    
    /// Report call quality update
    func reportCallQuality(uuid: UUID, quality: Int) {
        // Update call quality metrics if needed
        notifyFlutter("call_quality_updated", data: [
            "uuid": uuid.uuidString,
            "quality": quality
        ])
    }
    
    /// Send notification to Flutter
    private func notifyFlutter(_ method: String, data: [String: Any] = [:]) {
        DispatchQueue.main.async {
            self.flutterChannel?.invokeMethod(method, arguments: data)
        }
    }
}

// MARK: - CXProviderDelegate
extension CallKitService: CXProviderDelegate {
    
    func providerDidReset(_ provider: CXProvider) {
        print("[CallKitService] Provider did reset")
        currentCallUUID = nil
        notifyFlutter("provider_reset")
    }
    
    func provider(_ provider: CXProvider, perform action: CXStartCallAction) -> Bool {
        print("[CallKitService] Provider perform start call action")
        
        // Configure audio session for call
        configureAudioSession()
        
        // Notify Flutter that call should start
        notifyFlutter("should_start_call", data: [
            "uuid": action.callUUID.uuidString,
            "handle": action.handle.value
        ])
        
        // Report call as connecting
        provider.reportOutgoingCall(with: action.callUUID, startedConnectingAt: Date())
        
        action.fulfill()
        return true
    }
    
    func provider(_ provider: CXProvider, perform action: CXAnswerCallAction) -> Bool {
        print("[CallKitService] Provider perform answer call action")
        
        // Configure audio session for call
        configureAudioSession()
        
        // Notify Flutter that call should be answered
        notifyFlutter("should_answer_call", data: [
            "uuid": action.callUUID.uuidString
        ])
        
        action.fulfill()
        return true
    }
    
    func provider(_ provider: CXProvider, perform action: CXEndCallAction) -> Bool {
        print("[CallKitService] Provider perform end call action")
        
        // Notify Flutter that call should end
        notifyFlutter("should_end_call", data: [
            "uuid": action.callUUID.uuidString
        ])
        
        // Clean up audio session
        deactivateAudioSession()
        
        currentCallUUID = nil
        action.fulfill()
        return true
    }
    
    func provider(_ provider: CXProvider, perform action: CXSetHeldCallAction) -> Bool {
        print("[CallKitService] Provider perform set held call action")
        
        // Notify Flutter about hold state change
        notifyFlutter("should_set_call_hold", data: [
            "uuid": action.callUUID.uuidString,
            "onHold": action.isOnHold
        ])
        
        action.fulfill()
        return true
    }
    
    func provider(_ provider: CXProvider, perform action: CXSetMutedCallAction) -> Bool {
        print("[CallKitService] Provider perform set muted call action")
        
        // Notify Flutter about mute state change
        notifyFlutter("should_set_call_mute", data: [
            "uuid": action.callUUID.uuidString,
            "muted": action.isMuted
        ])
        
        action.fulfill()
        return true
    }
    
    func provider(_ provider: CXProvider, timedOutPerforming action: CXAction) {
        print("[CallKitService] Provider timed out performing action: \(action)")
        notifyFlutter("action_timeout", data: [
            "action": String(describing: type(of: action))
        ])
    }
    
    func provider(_ provider: CXProvider, didActivate audioSession: AVAudioSession) {
        print("[CallKitService] Provider did activate audio session")
        notifyFlutter("audio_session_activated")
    }
    
    func provider(_ provider: CXProvider, didDeactivate audioSession: AVAudioSession) {
        print("[CallKitService] Provider did deactivate audio session")
        notifyFlutter("audio_session_deactivated")
    }
    
    /// Configure audio session for VoIP calls
    private func configureAudioSession() {
        let audioSession = AVAudioSession.sharedInstance()
        do {
            try audioSession.setCategory(.playAndRecord, mode: .voiceChat, options: [.allowBluetooth])
            try audioSession.setActive(true)
            print("[CallKitService] Audio session configured successfully")
        } catch {
            print("[CallKitService] Error configuring audio session: \(error)")
        }
    }
    
    /// Deactivate audio session
    private func deactivateAudioSession() {
        let audioSession = AVAudioSession.sharedInstance()
        do {
            try audioSession.setActive(false, options: .notifyOthersOnDeactivation)
            print("[CallKitService] Audio session deactivated successfully")
        } catch {
            print("[CallKitService] Error deactivating audio session: \(error)")
        }
    }
}
