import CioMessagingPushFCM
import FirebaseCore
import FirebaseMessaging
import Flutter
import UIKit
import flutter_background_service_ios
import flutter_local_notifications
import singular_flutter_sdk
// import CallKit
// import AVFoundation

@main
class AppDelegateWithCioIntegration: CioAppDelegateWrapper<AppDelegate> {}

@objc class AppDelegate: FlutterAppDelegate {
  
  // private var callKitChannel: FlutterMethodChannel?
  
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GeneratedPluginRegistrant.register(with: self)

    // Initialize Customer.io messaging push
    MessagingPushFCM.initialize(
      withConfig: MessagingPushConfigBuilder()
        .showPushAppInForeground(true)  // `true` will display the push when app in foreground
        .build()
    )

    // This is required to make any communication available in the action isolate.
    FlutterLocalNotificationsPlugin.setPluginRegistrantCallback { (registry) in
      GeneratedPluginRegistrant.register(with: registry)
    }

    // Register the SwiftFlutterForegroundTaskPlugin to handle foreground task callbacks
    SwiftFlutterForegroundTaskPlugin.setPluginRegistrantCallback { registry in
      GeneratedPluginRegistrant.register(with: registry)
    }

    SwiftFlutterBackgroundServicePlugin.taskIdentifier = "com.app.Froggy.autoDynamicIconsSwitcher"

    if #available(iOS 10.0, *) {
      UNUserNotificationCenter.current().delegate = self as UNUserNotificationCenterDelegate
    }

    // TODO: Re-enable CallKit integration after properly adding CallKitService.swift to Xcode project
    // setupCallKitIntegration()

    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
  
  // TODO: Re-enable CallKit integration after properly adding CallKitService.swift to Xcode project
  /*
  /// Setup CallKit integration for iOS VoIP calls
  private func setupCallKitIntegration() {
    guard let controller = window?.rootViewController as? FlutterViewController else {
      print("[AppDelegate] ERROR: Could not get FlutterViewController")
      return
    }
    
    // Create method channel for CallKit communication
    callKitChannel = FlutterMethodChannel(
      name: "com.froggytalk.callkit",
      binaryMessenger: controller.binaryMessenger
    )
    
    // Set up CallKit service
    CallKitService.shared.setFlutterChannel(callKitChannel!)
    
    // Handle method calls from Flutter
    callKitChannel?.setMethodCallHandler { [weak self] (call, result) in
      self?.handleCallKitMethodCall(call: call, result: result)
    }
    
    print("[AppDelegate] CallKit integration initialized successfully")
  }
  
  /// Handle CallKit method calls from Flutter
  private func handleCallKitMethodCall(call: FlutterMethodCall, result: @escaping FlutterResult) {
    guard let args = call.arguments as? [String: Any] else {
      result(FlutterError(code: "INVALID_ARGUMENTS", message: "Invalid arguments", details: nil))
      return
    }
    
    switch call.method {
    case "reportIncomingCall":
      guard let uuidString = args["uuid"] as? String,
            let uuid = UUID(uuidString: uuidString),
            let phoneNumber = args["phoneNumber"] as? String else {
        result(FlutterError(code: "INVALID_ARGUMENTS", message: "Missing required arguments", details: nil))
        return
      }
      
      let hasVideo = args["hasVideo"] as? Bool ?? false
      CallKitService.shared.reportIncomingCall(uuid: uuid, phoneNumber: phoneNumber, hasVideo: hasVideo)
      result(nil)
      
    case "startOutgoingCall":
      guard let uuidString = args["uuid"] as? String,
            let uuid = UUID(uuidString: uuidString),
            let phoneNumber = args["phoneNumber"] as? String else {
        result(FlutterError(code: "INVALID_ARGUMENTS", message: "Missing required arguments", details: nil))
        return
      }
      
      let hasVideo = args["hasVideo"] as? Bool ?? false
      CallKitService.shared.startOutgoingCall(uuid: uuid, phoneNumber: phoneNumber, hasVideo: hasVideo)
      result(nil)
      
    case "answerCall":
      guard let uuidString = args["uuid"] as? String,
            let uuid = UUID(uuidString: uuidString) else {
        result(FlutterError(code: "INVALID_ARGUMENTS", message: "Missing UUID", details: nil))
        return
      }
      
      CallKitService.shared.answerCall(uuid: uuid)
      result(nil)
      
    case "endCall":
      guard let uuidString = args["uuid"] as? String,
            let uuid = UUID(uuidString: uuidString) else {
        result(FlutterError(code: "INVALID_ARGUMENTS", message: "Missing UUID", details: nil))
        return
      }
      
      CallKitService.shared.endCall(uuid: uuid)
      result(nil)
      
    case "setCallOnHold":
      guard let uuidString = args["uuid"] as? String,
            let uuid = UUID(uuidString: uuidString),
            let onHold = args["onHold"] as? Bool else {
        result(FlutterError(code: "INVALID_ARGUMENTS", message: "Missing required arguments", details: nil))
        return
      }
      
      CallKitService.shared.setCallOnHold(uuid: uuid, onHold: onHold)
      result(nil)
      
    case "setCallMuted":
      guard let uuidString = args["uuid"] as? String,
            let uuid = UUID(uuidString: uuidString),
            let muted = args["muted"] as? Bool else {
        result(FlutterError(code: "INVALID_ARGUMENTS", message: "Missing required arguments", details: nil))
        return
      }
      
      CallKitService.shared.setCallMuted(uuid: uuid, muted: muted)
      result(nil)
      
    case "reportCallConnected":
      guard let uuidString = args["uuid"] as? String,
            let uuid = UUID(uuidString: uuidString) else {
        result(FlutterError(code: "INVALID_ARGUMENTS", message: "Missing UUID", details: nil))
        return
      }
      
      CallKitService.shared.reportCallConnected(uuid: uuid)
      result(nil)
      
    default:
      result(FlutterMethodNotImplemented)
    }
  }
  */

  override func application(
    _ application: UIApplication,
    didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data
  ) {
    // The super call is now safe to make as the conflicting SDK has been removed.
    super.application(application, didRegisterForRemoteNotificationsWithDeviceToken: deviceToken)

    // Pass the device token to Firebase Messaging.
    Messaging.messaging().apnsToken = deviceToken
  }
}
