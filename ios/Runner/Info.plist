<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>The app requires access to your photo library to allow you to select and upload
			photos, enhancing your ability to personalize your experience within the app.</string>
		<key>NSFaceIDUsageDescription</key>
		<string>We use Face ID to quickly and securely authenticate your identity for accessing
			sensitive information within the app</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>The app collects microphone data to record audio for voice messages and calls,
			enhancing communication features and user experience.</string>
		<key>NSContactsUsageDescription</key>
		<string>The app collects contact data to help you easily search for friends and family,
			check call rates, and make phone calls.</string>
		<key>NSCameraUsageDescription</key>
		<string>The app collects camera data to take photos and videos, enabling features that
			enhance user interaction and personalization.</string>
		<key>NSBluetoothAlwaysUsageDescription</key>
		<string>The app collects Bluetooth data to connect to Bluetooth speakers, headsets, and
			microphones, enhancing your audio experience by enabling seamless device interactions.</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>The app collects location data to provide location-based services and improve your
			experience.</string>
		<key>NSAppleMusicUsageDescription</key>
		<string>The app collects Apple Music data to provide personalized radio stations, enhancing
			your music listening experience.</string>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true />
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>$(FLAVOR_APP_NAME)</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIcons</key>
		<dict>
			<key>CFBundleAlternateIcons</key>
			<dict>
				<key>Froggy_Happy</key>
				<dict>
					<key>CFBundleIconFiles</key>
					<array>
						<string>FroggyTalk_Happy</string>
					</array>
					<key>UIPrerenderedIcon</key>
					<false />
				</dict>
				<key>Froggy_Sad</key>
				<dict>
					<key>CFBundleIconFiles</key>
					<array>
						<string>Froggy_Sad</string>
					</array>
					<key>UIPrerenderedIcon</key>
					<false />
				</dict>
			</dict>
		</dict>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleLocalizations</key>
		<array>
			<string>am</string>
			<string>en</string>
			<string>ha</string>
			<string>ti</string>
		</array>
		<key>CFBundleName</key>
		<string>$(FLAVOR_APP_NAME)</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>LSRequiresIPhoneOS</key>
		<true />
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true />
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false />
		<key>BGTaskSchedulerPermittedIdentifiers</key>
		<array>
			<string>dev.flutter.background.refresh</string>
			<string>com.pravera.flutter_foreground_task.refresh</string>
		</array>
		<key>UIBackgroundModes</key>
		<array>
			<string>remote-notification</string>
			<string>processing</string>
			<string>audio</string>
			<string>voip</string>
			<string>fetch</string>
		</array>
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>sms</string>
			<string>tel</string>
			<string>https</string>
			<string>mailto</string>
		</array>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLName</key>
				<string>Facebook</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>fb126146777159819</string>
				</array>
			</dict>
		</array>
		<key>FacebookAppID</key>
		<string>126146777159819</string>
		<key>FacebookClientToken</key>
		<string>********************************</string>
		<key>FacebookDisplayName</key>
		<string>$(FLAVOR_APP_NAME)</string>
		<key>FacebookAutoLogAppEventsEnabled</key>
		<true />
		<key>FacebookAdvertiserIDCollectionEnabled</key>
		<true />
		<key>FirebaseCrashlyticsCollectionEnabled</key>
		<false />
		<key>NSAppTransportSecurity</key>
		<dict>
			<key>NSAllowsArbitraryLoads</key>
			<true />
		</dict>
		<key>NSUserTrackingUsageDescription</key>
		<string>This identifier will be used to deliver personalized ads to you and help us improve
			our services.</string>
		<key>FirebaseAppDelegateProxyEnabled</key>
		<false />
		<key>SKAdNetworkItems</key>
		<array>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>v9wttpbfk9.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>n38lu8286q.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>cstr6suwn9.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>4fzdc2evr5.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>4pfyvq9l8r.skadnetwork</string>
			</dict>
		</array>
	</dict>
</plist>