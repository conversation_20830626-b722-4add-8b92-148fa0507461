//
//  NotificationService.swift
//  CustomerIONotificationSericeExtension
//
//  Created by m1 on 18/06/2025.
//

import CioMessagingPushFCM
import UserNotifications

class NotificationService: UNNotificationServiceExtension {

  override func didReceive(
    _ request: UNNotificationRequest,
    withContentHandler contentHandler: @escaping (UNNotificationContent) -> Void
  ) {

    // Initialize Customer.io messaging for the extension
    MessagingPushFCM.initializeForExtension(
      withConfig: MessagingPushConfigBuilder(cdpApiKey: "d151be430458fbea3984")
        .build()
    )

    // Let Customer.io handle the notification first (for rich push features)
    MessagingPush.shared.didReceive(request, withContentHandler: contentHandler)
  }

  override func serviceExtensionTimeWillExpire() {
    // Ensure the notification is delivered before the extension is terminated
    MessagingPush.shared.serviceExtensionTimeWillExpire()
  }
}
