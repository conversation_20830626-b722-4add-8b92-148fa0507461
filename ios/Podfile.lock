PODS:
  - "AnalyticsSwiftCIO (1.7.3+cio.1)":
    - JSONSafeEncoding (= 2.0.0)
    - <PERSON><PERSON><PERSON> (= 1.1.2)
  - audio_service (0.0.1):
    - Flutter
    - FlutterMacOS
  - audio_session (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
  - customer_io (2.2.0):
    - customer_io/nopush (= 2.2.0)
    - CustomerIO/DataPipelines (= 3.10.4)
    - CustomerIO/MessagingInApp (= 3.10.4)
    - Flutter
  - customer_io/fcm (2.2.0):
    - CustomerIO/DataPipelines (= 3.10.4)
    - CustomerIO/MessagingInApp (= 3.10.4)
    - CustomerIO/MessagingPushFCM (= 3.10.4)
    - Flutter
  - customer_io/nopush (2.2.0):
    - CustomerIO/DataPipelines (= 3.10.4)
    - CustomerIO/MessagingInApp (= 3.10.4)
    - CustomerIO/MessagingPush (= 3.10.4)
    - Flutter
  - customer_io_richpush/fcm (2.2.0):
    - CustomerIO/MessagingPushFCM (= 3.10.4)
    - Flutter
  - CustomerIO/DataPipelines (3.10.4):
    - CustomerIODataPipelines (= 3.10.4)
  - CustomerIO/MessagingInApp (3.10.4):
    - CustomerIOMessagingInApp (= 3.10.4)
  - CustomerIO/MessagingPush (3.10.4):
    - CustomerIOMessagingPush (= 3.10.4)
  - CustomerIO/MessagingPushFCM (3.10.4):
    - CustomerIOMessagingPushFCM (= 3.10.4)
  - CustomerIOCommon (3.10.4)
  - CustomerIODataPipelines (3.10.4):
    - "AnalyticsSwiftCIO (= 1.7.3+cio.1)"
    - CustomerIOCommon (= 3.10.4)
    - CustomerIOTrackingMigration (= 3.10.4)
  - CustomerIOMessagingInApp (3.10.4):
    - CustomerIOCommon (= 3.10.4)
  - CustomerIOMessagingPush (3.10.4):
    - CustomerIOCommon (= 3.10.4)
  - CustomerIOMessagingPushFCM (3.10.4):
    - CustomerIOMessagingPush (= 3.10.4)
    - FirebaseMessaging (< 12.0.0, >= 8.7.0)
  - CustomerIOTrackingMigration (3.10.4):
    - CustomerIOCommon (= 3.10.4)
  - device_info_plus (0.0.1):
    - Flutter
  - facebook_app_events (0.0.1):
    - FBAudienceNetwork (= 6.16)
    - FBSDKCoreKit (~> 17.0)
    - Flutter
  - fast_contacts (0.0.1):
    - Flutter
  - FBAEMKit (17.4.0):
    - FBSDKCoreKit_Basics (= 17.4.0)
  - FBAudienceNetwork (6.16.0)
  - FBSDKCoreKit (17.4.0):
    - FBAEMKit (= 17.4.0)
    - FBSDKCoreKit_Basics (= 17.4.0)
  - FBSDKCoreKit_Basics (17.4.0)
  - Firebase/Analytics (11.13.0):
    - Firebase/Core
  - Firebase/Core (11.13.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.13.0)
  - Firebase/CoreOnly (11.13.0):
    - FirebaseCore (~> 11.13.0)
  - Firebase/Crashlytics (11.13.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 11.13.0)
  - Firebase/Messaging (11.13.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.13.0)
  - Firebase/Performance (11.13.0):
    - Firebase/CoreOnly
    - FirebasePerformance (~> 11.13.0)
  - Firebase/RemoteConfig (11.13.0):
    - Firebase/CoreOnly
    - FirebaseRemoteConfig (~> 11.13.0)
  - firebase_analytics (11.5.0):
    - Firebase/Analytics (= 11.13.0)
    - firebase_core
    - Flutter
  - firebase_core (3.14.0):
    - Firebase/CoreOnly (= 11.13.0)
    - Flutter
  - firebase_crashlytics (4.3.7):
    - Firebase/Crashlytics (= 11.13.0)
    - firebase_core
    - Flutter
  - firebase_messaging (15.2.7):
    - Firebase/Messaging (= 11.13.0)
    - firebase_core
    - Flutter
  - firebase_performance (0.10.1-7):
    - Firebase/Performance (= 11.13.0)
    - firebase_core
    - Flutter
  - firebase_remote_config (5.4.5):
    - Firebase/RemoteConfig (= 11.13.0)
    - firebase_core
    - Flutter
  - FirebaseABTesting (11.13.0):
    - FirebaseCore (~> 11.13.0)
  - FirebaseAnalytics (11.13.0):
    - FirebaseAnalytics/AdIdSupport (= 11.13.0)
    - FirebaseCore (~> 11.13.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.13.0):
    - FirebaseCore (~> 11.13.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.13.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - FirebaseCore (11.13.0):
    - FirebaseCoreInternal (~> 11.13.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreExtension (11.13.0):
    - FirebaseCore (~> 11.13.0)
  - FirebaseCoreInternal (11.13.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseCrashlytics (11.13.0):
    - FirebaseCore (~> 11.13.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.1)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseInstallations (11.13.0):
    - FirebaseCore (~> 11.13.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.13.0):
    - FirebaseCore (~> 11.13.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Reachability (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
  - FirebasePerformance (11.13.0):
    - FirebaseCore (~> 11.13.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfig (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
  - FirebaseRemoteConfig (11.13.0):
    - FirebaseABTesting (~> 11.0)
    - FirebaseCore (~> 11.13.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSharedSwift (~> 11.0)
    - GoogleUtilities/Environment (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseRemoteConfigInterop (11.14.0)
  - FirebaseSessions (11.13.0):
    - FirebaseCore (~> 11.13.0)
    - FirebaseCoreExtension (~> 11.13.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - FirebaseSharedSwift (11.14.0)
  - Flutter (1.0.0)
  - flutter_background_service_ios (0.0.3):
    - Flutter
  - flutter_dtmf (1.0.1):
    - Flutter
  - flutter_dynamic_icon_plus (1.0.0):
    - Flutter
    - SwiftTryCatch
  - flutter_foreground_task (0.0.1):
    - Flutter
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0.3)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0.3)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_upgrade_version (1.1.8):
    - Flutter
  - flutter_webrtc (0.12.6):
    - Flutter
    - WebRTC-SDK (= 125.6422.06)
  - GoogleAppMeasurement (11.13.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.13.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.13.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.13.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.13.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUtilities (8.1.0):
    - GoogleUtilities/AppDelegateSwizzler (= 8.1.0)
    - GoogleUtilities/Environment (= 8.1.0)
    - GoogleUtilities/Logger (= 8.1.0)
    - GoogleUtilities/MethodSwizzler (= 8.1.0)
    - GoogleUtilities/Network (= 8.1.0)
    - "GoogleUtilities/NSData+zlib (= 8.1.0)"
    - GoogleUtilities/Privacy (= 8.1.0)
    - GoogleUtilities/Reachability (= 8.1.0)
    - GoogleUtilities/SwizzlerTestHelpers (= 8.1.0)
    - GoogleUtilities/UserDefaults (= 8.1.0)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/SwizzlerTestHelpers (8.1.0):
    - GoogleUtilities/MethodSwizzler
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - image_picker_ios (0.0.1):
    - Flutter
  - in_app_purchase_storekit (0.0.1):
    - Flutter
    - FlutterMacOS
  - in_app_review (2.0.0):
    - Flutter
  - JSONSafeEncoding (2.0.0)
  - just_audio (0.0.1):
    - Flutter
    - FlutterMacOS
  - Mixpanel-swift (5.1.0):
    - Mixpanel-swift/Complete (= 5.1.0)
  - Mixpanel-swift/Complete (5.1.0)
  - mixpanel_flutter (2.4.4):
    - Flutter
    - Mixpanel-swift (= 5.1.0)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - OrderedSet (6.0.3)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - Singular-SDK (12.8.0):
    - Singular-SDK/Main (= 12.8.0)
  - Singular-SDK/Main (12.8.0)
  - singular_flutter_sdk (1.7.0):
    - Flutter
    - Singular-SDK (= 12.8.0)
  - Sovran (1.1.2)
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - Stripe (24.7.0):
    - StripeApplePay (= 24.7.0)
    - StripeCore (= 24.7.0)
    - StripePayments (= 24.7.0)
    - StripePaymentsUI (= 24.7.0)
    - StripeUICore (= 24.7.0)
  - stripe_ios (0.0.1):
    - Flutter
    - Stripe (~> 24.7.0)
    - stripe_ios/stripe_ios (= 0.0.1)
    - stripe_ios/stripe_objc (= 0.0.1)
    - StripeApplePay (~> 24.7.0)
    - StripeFinancialConnections (~> 24.7.0)
    - StripePayments (~> 24.7.0)
    - StripePaymentSheet (~> 24.7.0)
    - StripePaymentsUI (~> 24.7.0)
  - stripe_ios/stripe_ios (0.0.1):
    - Flutter
    - Stripe (~> 24.7.0)
    - stripe_ios/stripe_objc
    - StripeApplePay (~> 24.7.0)
    - StripeFinancialConnections (~> 24.7.0)
    - StripePayments (~> 24.7.0)
    - StripePaymentSheet (~> 24.7.0)
    - StripePaymentsUI (~> 24.7.0)
  - stripe_ios/stripe_objc (0.0.1):
    - Flutter
    - Stripe (~> 24.7.0)
    - StripeApplePay (~> 24.7.0)
    - StripeFinancialConnections (~> 24.7.0)
    - StripePayments (~> 24.7.0)
    - StripePaymentSheet (~> 24.7.0)
    - StripePaymentsUI (~> 24.7.0)
  - StripeApplePay (24.7.0):
    - StripeCore (= 24.7.0)
  - StripeCore (24.7.0)
  - StripeFinancialConnections (24.7.0):
    - StripeCore (= 24.7.0)
    - StripeUICore (= 24.7.0)
  - StripePayments (24.7.0):
    - StripeCore (= 24.7.0)
    - StripePayments/Stripe3DS2 (= 24.7.0)
  - StripePayments/Stripe3DS2 (24.7.0):
    - StripeCore (= 24.7.0)
  - StripePaymentSheet (24.7.0):
    - StripeApplePay (= 24.7.0)
    - StripeCore (= 24.7.0)
    - StripePayments (= 24.7.0)
    - StripePaymentsUI (= 24.7.0)
  - StripePaymentsUI (24.7.0):
    - StripeCore (= 24.7.0)
    - StripePayments (= 24.7.0)
    - StripeUICore (= 24.7.0)
  - StripeUICore (24.7.0):
    - StripeCore (= 24.7.0)
  - SwiftTryCatch (0.0.1)
  - url_launcher_ios (0.0.1):
    - Flutter
  - wakelock_plus (0.0.1):
    - Flutter
  - WebRTC-SDK (125.6422.06)
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - audio_service (from `.symlinks/plugins/audio_service/darwin`)
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - customer_io (from `.symlinks/plugins/customer_io/ios`)
  - customer_io/fcm (from `.symlinks/plugins/customer_io/ios`)
  - customer_io_richpush/fcm (from `.symlinks/plugins/customer_io/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - facebook_app_events (from `.symlinks/plugins/facebook_app_events/ios`)
  - fast_contacts (from `.symlinks/plugins/fast_contacts/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - firebase_performance (from `.symlinks/plugins/firebase_performance/ios`)
  - firebase_remote_config (from `.symlinks/plugins/firebase_remote_config/ios`)
  - Flutter (from `Flutter`)
  - flutter_background_service_ios (from `.symlinks/plugins/flutter_background_service_ios/ios`)
  - flutter_dtmf (from `.symlinks/plugins/flutter_dtmf/ios`)
  - flutter_dynamic_icon_plus (from `.symlinks/plugins/flutter_dynamic_icon_plus/ios`)
  - flutter_foreground_task (from `.symlinks/plugins/flutter_foreground_task/ios`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_upgrade_version (from `.symlinks/plugins/flutter_upgrade_version/ios`)
  - flutter_webrtc (from `.symlinks/plugins/flutter_webrtc/ios`)
  - GoogleUtilities (~> 8.1.0)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - in_app_purchase_storekit (from `.symlinks/plugins/in_app_purchase_storekit/darwin`)
  - in_app_review (from `.symlinks/plugins/in_app_review/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/darwin`)
  - mixpanel_flutter (from `.symlinks/plugins/mixpanel_flutter/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - singular_flutter_sdk (from `.symlinks/plugins/singular_flutter_sdk/ios`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - stripe_ios (from `.symlinks/plugins/stripe_ios/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - AnalyticsSwiftCIO
    - CustomerIO
    - CustomerIOCommon
    - CustomerIODataPipelines
    - CustomerIOMessagingInApp
    - CustomerIOMessagingPush
    - CustomerIOMessagingPushFCM
    - CustomerIOTrackingMigration
    - FBAEMKit
    - FBAudienceNetwork
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - Firebase
    - FirebaseABTesting
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebasePerformance
    - FirebaseRemoteConfig
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - FirebaseSharedSwift
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - JSONSafeEncoding
    - Mixpanel-swift
    - nanopb
    - OrderedSet
    - PromisesObjC
    - PromisesSwift
    - Singular-SDK
    - Sovran
    - Stripe
    - StripeApplePay
    - StripeCore
    - StripeFinancialConnections
    - StripePayments
    - StripePaymentSheet
    - StripePaymentsUI
    - StripeUICore
    - SwiftTryCatch
    - WebRTC-SDK

EXTERNAL SOURCES:
  audio_service:
    :path: ".symlinks/plugins/audio_service/darwin"
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  customer_io:
    :path: ".symlinks/plugins/customer_io/ios"
  customer_io_richpush:
    :path: ".symlinks/plugins/customer_io/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  facebook_app_events:
    :path: ".symlinks/plugins/facebook_app_events/ios"
  fast_contacts:
    :path: ".symlinks/plugins/fast_contacts/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  firebase_performance:
    :path: ".symlinks/plugins/firebase_performance/ios"
  firebase_remote_config:
    :path: ".symlinks/plugins/firebase_remote_config/ios"
  Flutter:
    :path: Flutter
  flutter_background_service_ios:
    :path: ".symlinks/plugins/flutter_background_service_ios/ios"
  flutter_dtmf:
    :path: ".symlinks/plugins/flutter_dtmf/ios"
  flutter_dynamic_icon_plus:
    :path: ".symlinks/plugins/flutter_dynamic_icon_plus/ios"
  flutter_foreground_task:
    :path: ".symlinks/plugins/flutter_foreground_task/ios"
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_upgrade_version:
    :path: ".symlinks/plugins/flutter_upgrade_version/ios"
  flutter_webrtc:
    :path: ".symlinks/plugins/flutter_webrtc/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  in_app_purchase_storekit:
    :path: ".symlinks/plugins/in_app_purchase_storekit/darwin"
  in_app_review:
    :path: ".symlinks/plugins/in_app_review/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/darwin"
  mixpanel_flutter:
    :path: ".symlinks/plugins/mixpanel_flutter/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  singular_flutter_sdk:
    :path: ".symlinks/plugins/singular_flutter_sdk/ios"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  stripe_ios:
    :path: ".symlinks/plugins/stripe_ios/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  AnalyticsSwiftCIO: cbded90bbfe53072cb1fa1bc091d249e99ef7c56
  audio_service: cab6c1a0eaf01b5a35b567e11fa67d3cc1956910
  audio_session: 19e9480dbdd4e5f6c4543826b2e8b0e4ab6145fe
  connectivity_plus: 2a701ffec2c0ae28a48cf7540e279787e77c447d
  customer_io: ff3d4b29dc82125b1ea14f4886363030a3780cb6
  customer_io_richpush: 461cdf4b2adad6dfc8fe81b133121baf67a95162
  CustomerIO: 47b09737f3cd1d42998ac9cb50e7088cd1610ba8
  CustomerIOCommon: d614d094309def0031a8b270cba37426042d4413
  CustomerIODataPipelines: f8aa2fad1d11c3f193d8cdcc54faef9bab776d33
  CustomerIOMessagingInApp: 7b03243264bfb82b767745dce8c181069e00bdab
  CustomerIOMessagingPush: 7a4baf045b2686748f2099c461cfcfcc5b3ab1c3
  CustomerIOMessagingPushFCM: f0ef4f90d4d2ba7bc483d1fe21abaa99a1334e25
  CustomerIOTrackingMigration: 213d422ec5abe829d7be9a6254dd76f4dfe53e03
  device_info_plus: bf2e3232933866d73fe290f2942f2156cdd10342
  facebook_app_events: d791f99d78f74cde976edd378678566cb7bb4ab0
  fast_contacts: d5f0da976a40f8fd07a9ff730c2e9bee8c3898dd
  FBAEMKit: 58cb5f302cdd715a56d4c1d0dfdd2e423ac1421a
  FBAudienceNetwork: d1670939884e3a2e0ad98dca98d7e0c841417228
  FBSDKCoreKit: 94d7461d0cecf441b1ba7c41acfff41daa8ccd41
  FBSDKCoreKit_Basics: 151b43db8b834d3f0e02f95d36a44ffd36265e45
  Firebase: 3435bc66b4d494c2f22c79fd3aae4c1db6662327
  firebase_analytics: 9bc12534b28e9955a6d02fac0478e92e027aed26
  firebase_core: a861be150c0e7c6aecedde077968eb92cbf790b9
  firebase_crashlytics: aa26a226ac9a7047f729701fd8a60028a21f84ac
  firebase_messaging: 462da0f5171a325bf7834f9e49ac3d044bda9563
  firebase_performance: c8f2f3be6733e8724c20294c488f4b6f1451faf4
  firebase_remote_config: 471c4e29cae20616582a9b805a671542eb720fac
  FirebaseABTesting: 4048f61cc10d2fad064d3089ace6bd5fb910169b
  FirebaseAnalytics: 630349facf4a114a0977e5d7570e104261973287
  FirebaseCore: c692c7f1c75305ab6aff2b367f25e11d73aa8bd0
  FirebaseCoreExtension: c048485c347616dba6165358dbef765c5197597b
  FirebaseCoreInternal: 29d7b3af4aaf0b8f3ed20b568c13df399b06f68c
  FirebaseCrashlytics: 8281e577b6f85a08ea7aeb8b66f95e1ae430c943
  FirebaseInstallations: 0ee9074f2c1e86561ace168ee1470dc67aabaf02
  FirebaseMessaging: 195bbdb73e6ca1dbc76cd46e73f3552c084ef6e4
  FirebasePerformance: d8d51127a7d1fe977866813c75836f4911849a09
  FirebaseRemoteConfig: 518ca257cdb2ccbc2b781ef2f2104f1104c7488f
  FirebaseRemoteConfigInterop: 7b74ceaa54e28863ed17fa39da8951692725eced
  FirebaseSessions: eaa8ec037e7793769defe4201c20bd4d976f9677
  FirebaseSharedSwift: bdd5c8674c4712a98e70287c936bc5cca5d640f6
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_background_service_ios: e30e0d3ee69e4cee66272d0c78eacd48c2e94aac
  flutter_dtmf: d86aaec3002a172abba756dc179aaf786bb82215
  flutter_dynamic_icon_plus: 97d2592f571db88345a05183e080369289ec9c94
  flutter_foreground_task: 21ef182ab0a29a3005cc72cd70e5f45cb7f7f817
  flutter_inappwebview_ios: 6f63631e2c62a7c350263b13fa5427aedefe81d4
  flutter_local_notifications: df98d66e515e1ca797af436137b4459b160ad8c9
  flutter_upgrade_version: 6a60d0cae546c21e6ab8ebb20053f01f29d17ee9
  flutter_webrtc: 90260f83024b1b96d239a575ea4e3708e79344d1
  GoogleAppMeasurement: 0dfca1a4b534d123de3945e28f77869d10d0d600
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  in_app_purchase_storekit: a1ce04056e23eecc666b086040239da7619cd783
  in_app_review: a31b5257259646ea78e0e35fc914979b0031d011
  JSONSafeEncoding: 54722ebc4fe1482e3e60a8450e1287481e32dd8b
  just_audio: a42c63806f16995daf5b219ae1d679deb76e6a79
  Mixpanel-swift: 7b26468fc0e2e521104e51d65c4bbf7cab8162f8
  mixpanel_flutter: c2bb8345c90bef15512a1b812ec800b52f8614b6
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  share_plus: 8b6f8b3447e494cca5317c8c3073de39b3600d1f
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  Singular-SDK: 4b13069d46b48020034560cb3e6bbde39d2bb34f
  singular_flutter_sdk: c53ce227182eff1857f7413958e954bcd3543853
  Sovran: 117d186f9e9cd47a231ee48dace5cd064a4b8ec8
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  Stripe: 8a03a78bfa16b197f9fac51e42670ac563b34388
  stripe_ios: 0f0884084e2e8b49df838f8fed2129b42c3516b9
  StripeApplePay: 3c1b43d9b5130f6b714863bf8c9482c24168ab27
  StripeCore: 4955c2af14446db04818ad043d19d8f97b73c5fa
  StripeFinancialConnections: 8cf97b04c2f354879a2a5473126efac38f11f406
  StripePayments: 91820845bece6117809bcfdcaef39c84c2b4cae5
  StripePaymentSheet: 1810187cbdbc73410b8fb86cecafaaa41c1481fc
  StripePaymentsUI: 326376e23caa369d1f58041bdb858c89c2b17ed4
  StripeUICore: 17a4f3adb81ae05ab885e1b353022a430176eab1
  SwiftTryCatch: fb6d2b34abe48efd69578dac919293a44f95b481
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  wakelock_plus: 76957ab028e12bfa4e66813c99e46637f367fc7e
  WebRTC-SDK: 79942c006ea64f6fb48d7da8a4786dfc820bc1db
  webview_flutter_wkwebview: a4af96a051138e28e29f60101d094683b9f82188

PODFILE CHECKSUM: fe70822e8cfb74994b64a018c4a36783c9c4871d

COCOAPODS: 1.16.2
