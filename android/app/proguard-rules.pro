-keep class kotlin.Metadata { *; }
-dontwarn kotlin.**
-dontwarn kotlinx.**

-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }

-keep class com.cloudwebrtc.webrtc.** {*;}
-keep class org.webrtc.** {*;}

-dontwarn com.stripe.android.pushProvisioning.PushProvisioningActivity$g
-dontwarn com.stripe.android.pushProvisioning.PushProvisioningActivityStarter$Args
-dontwarn com.stripe.android.pushProvisioning.PushProvisioningActivityStarter$Error
-dontwarn com.stripe.android.pushProvisioning.PushProvisioningActivityStarter
-dontwarn com.stripe.android.pushProvisioning.PushProvisioningEphemeralKeyProvider

-dontwarn com.google.android.play.core.splitcompat.SplitCompatApplication
-dontwarn com.google.android.play.core.splitinstall.SplitInstallException
-dontwarn com.google.android.play.core.splitinstall.SplitInstallManager
-dontwarn com.google.android.play.core.splitinstall.SplitInstallManagerFactory
-dontwarn com.google.android.play.core.splitinstall.SplitInstallRequest$Builder
-dontwarn com.google.android.play.core.splitinstall.SplitInstallRequest
-dontwarn com.google.android.play.core.splitinstall.SplitInstallSessionState
-dontwarn com.google.android.play.core.splitinstall.SplitInstallStateUpdatedListener
-dontwarn com.google.android.play.core.tasks.OnFailureListener
-dontwarn com.google.android.play.core.tasks.OnSuccessListener
-dontwarn com.google.android.play.core.tasks.Task

-keepclassmembers class j$.util.concurrent.ConcurrentHashMap$TreeBin {
  int lockState;
}
-keepclassmembers class j$.util.concurrent.ConcurrentHashMap {
  int sizeCtl;
  int transferIndex;
  long baseCount;
  int cellsBusy;
}
-keepclassmembers class j$.util.concurrent.ConcurrentHashMap$CounterCell {
  long value;
}
-keepclassmembers enum * {
  public static **[] values();
  public static ** valueOf(java.lang.String);
  public static final synthetic <fields>;
}
-keepclassmembers class j$.util.IntSummaryStatistics {
  long count;
  long sum;
  int min;
  int max;
}
-keepclassmembers class j$.util.LongSummaryStatistics {
  long count;
  long sum;
  long min;
  long max;
}
-keepclassmembers class j$.util.DoubleSummaryStatistics {
  long count;
  double sum;
  double min;
  double max;
}
-keep class com.singular.** { *; }
-keep public class com.android.installreferrer.** { *; }
-dontwarn com.android.installreferrer.**
-keep public class com.samsung.android.sdk.sinstallreferrer.** { *; }
-dontwarn com.samsung.android.sdk.sinstallreferrer.**