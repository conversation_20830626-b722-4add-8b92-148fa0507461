plugins {
    id 'com.android.application'
    // START: FlutterFire Configuration
    id 'com.google.gms.google-services'
    id 'com.google.firebase.firebase-perf'
    id 'com.google.firebase.crashlytics'
    // END: FlutterFire Configuration
    id 'kotlin-android'
    id 'dev.flutter.flutter-gradle-plugin'
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace 'com.FroggyTalk'
    compileSdkVersion 35
    // compileSdkVersion flutter.compileSdkVersion
//    ndkVersion flutter.ndkVersion
    ndkVersion "29.0.13113456"

    compileOptions {
        // Flag to enable support for the new language APIs
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // (https://developer.android.com/studio/build/application-id.html).
        applicationId 'com.FroggyTalk'
        // You can update the following values to match your application needs.
        // For more information, see:
        // https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdkVersion 28
        // compileSdkVersion 34
        // targetSdkVersion 34
        // minSdkVersion flutter.minSdkVersion
        targetSdkVersion flutter.targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName

        // Required when setting minSdkVersion to 20 or lower
        multiDexEnabled true
    }

    signingConfigs {
        if (System.getenv('ANDROID_KEYSTORE_PATH')) {
            release {
                storeFile file(System.getenv('ANDROID_KEYSTORE_PATH'))
                storePassword System.getenv('ANDROID_KEYSTORE_PASSWORD')
                keyAlias System.getenv('ANDROID_KEYSTORE_ALIAS')
                keyPassword System.getenv('ANDROID_KEYSTORE_PRIVATE_KEY_PASSWORD')
            }
        } else if (System.getenv()['CI']) {
            release {
                storeFile file(System.getenv('CM_KEYSTORE_PATH'))
                storePassword System.getenv('CM_KEYSTORE_PASSWORD')
                keyAlias System.getenv('CM_KEY_ALIAS')
                keyPassword System.getenv('CM_KEY_PASSWORD')
            }
        } else {
            release {
                storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
                storePassword keystoreProperties['storePassword']
                keyAlias keystoreProperties['keyAlias']
                keyPassword keystoreProperties['keyPassword']
            }
        }
    }

    flavorDimensions 'default'
    productFlavors {
        production {
            dimension 'default'
            applicationIdSuffix ''
            manifestPlaceholders = [appName: 'Froggytalk']
        }
        staging {
            dimension 'default'
            applicationIdSuffix '.stg'
            manifestPlaceholders = [appName: '[STG] Froggytalk']
        }
        development {
            dimension 'default'
            applicationIdSuffix '.dev'
            manifestPlaceholders = [appName: '[DEV] Froggytalk']
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
            signingConfig signingConfigs.debug
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    // For AGP 7.4+
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.3'
    // implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'androidx.multidex:multidex:2.0.1'
    // implementation 'com.stripe:stripe-android:20.53.0'

    /**
     * Specifies the version of the Google Play Billing Library to be used in the project.
     * The `billing_version` variable holds the version number, which is then used to include
     * the corresponding dependency in the project.
     *
     * @property billing_version The version of the Google Play Billing Library.
     * @dependency com.android.billingclient:billing:$billing_version The dependency for the Google Play Billing Library.
     */
    def billing_version = "7.1.1"
    implementation "com.android.billingclient:billing-ktx:$billing_version"

    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'com.android.support:appcompat-v7:28.0.0'
    implementation 'com.google.android.gms:play-services-ads-identifier:18.0.1'
    // implementation 'com.google.android.gms:play-services:6.5.87'

    // singular
    implementation 'com.singular.sdk:singular_sdk:12.5.6'

    //  firebase
    implementation 'com.google.firebase:firebase-analytics'
    implementation platform('com.google.firebase:firebase-bom:32.7.0') // BOM for version management
    
    // Firebase Performance Monitoring
    implementation 'com.google.firebase:firebase-perf'

    // Dependencies for Google Play In-App Review API
    // - 'com.google.android.play:review:2.0.2': Provides the core functionality for in-app reviews.
    // - 'com.google.android.play:review-ktx:2.0.2': Provides Kotlin extensions for the in-app review API, making it easier to use with Kotlin.
    implementation 'com.google.android.play:review:2.0.2'
    implementation 'com.google.android.play:review-ktx:2.0.2'

    implementation "io.customer.android:datapipelines:4.6.1"
    // Required for push notifications only
    implementation "io.customer.android:messaging-push-fcm:4.6.1"
    // Required for in-app messages only
    implementation "io.customer.android:messaging-in-app:4.6.1"
    implementation 'com.android.installreferrer:installreferrer:2.2'
}
